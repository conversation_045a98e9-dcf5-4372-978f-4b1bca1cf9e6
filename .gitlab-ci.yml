# This file is a template, and might need editing before it works on your project.
image: "harbor.shopeemobile.com/shopee/sls-cicd-base:go1.17.12"

before_script:
  - echo "before_script"
  - export GO111MODULE="on"
  - go version
  - go env



golangci-lint:
  stage: scan
  script:
    - go mod tidy
    - curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh  | sh -s -- -b /usr/bin/golangci-lint v1.47.3
    - /usr/bin/golangci-lint/golangci-lint --version
    - /usr/bin/golangci-lint/golangci-lint run -v --timeout=10m --disable unused --skip-files "_test\.go" --exclude unused,sa1012,asmdecl,S1004,SA5008,SA1019  ./...



.tst_tmpl: &tst_def
  only:
    refs:
      - test
    changes:
      - protobuf/*


    ####                                            ####
    ### Start modify your CI configuration from here ###
    ###
    ####
#variables:
#  MY_VERSION: $CI_BUILD_REF_NAME
before_script:
  - export OK=${CI_BUILD_REF_NAME##**/}
  - echo $OK

.scan_tmpl: &scan_def
  script:
    - echo "check"


.test_tmpl: &test_def
  script:
    - echo run your unittest here.                  #这里写您的单元测试shell




.push_tmpl: &push_proto
  script:
    - echo "push test"
    - echo $CI_PROJECT_NAME
    - |
      userMail=$GITLAB_USER_EMAIL
      git config --global user.email "$GITLAB_USER_EMAIL"
      userName=${userMail%@*}
      echo "$userMail"
      git config --global user.name "$userName"
      git clone -v https://oauth2:$<EMAIL>/shopee/bg-logistics/logistics/proto-center.git
      cd proto-center
      if [ `grep -c $CI_PROJECT_NAME README.md` -eq '0' ];then
        echo "- $CI_PROJECT_NAME-->v$(sed -n '$p' README.md | awk -F "-->v" '{print $2+1}' | head)" >>README.md
        export branchName=$(sed -n '$p' README.md | awk -F "-->" '{print $2}')
        echo $branchName
        git add . && git commit -m "add $CI_PROJECT_NAME "
        git push  https://oauth2:$<EMAIL>/shopee/bg-logistics/logistics/proto-center.git master
        git checkout -b $branchName
        mkdir -p $CI_PROJECT_NAME && cp -r ../protobuf/* $CI_PROJECT_NAME/
        go mod init git.garena.com/shopee/bg-logistics/logistics/proto-center/$branchName
        go mod tidy
        git add .
        git commit -m "add  $CI_PROJECT_NAME proto"
        git push --set-upstream https://oauth2:$<EMAIL>/shopee/bg-logistics/logistics/proto-center.git $branchName
        git checkout v2
        mkdir -p $CI_PROJECT_NAME && cp -r ../protobuf/* $CI_PROJECT_NAME/
        git add .
        git commit -m "add  $CI_PROJECT_NAME proto"
        git push  https://oauth2:$<EMAIL>/shopee/bg-logistics/logistics/proto-center.git v2
      else
        export branchName=$(grep $CI_PROJECT_NAME README.md  | awk -F "-->" '{print $2}' | head)
        git checkout $branchName
        rm -rf $CI_PROJECT_NAME/
        mkdir -p $CI_PROJECT_NAME
        cp -r ../protobuf/* $CI_PROJECT_NAME/ && git add .
        git commit -m "update  $CI_PROJECT_NAME proto"
        git push https://oauth2:$<EMAIL>/shopee/bg-logistics/logistics/proto-center.git $branchName;
        git checkout v2
        rm -rf $CI_PROJECT_NAME/
        mkdir -p $CI_PROJECT_NAME
        cp -r ../protobuf/* $CI_PROJECT_NAME/ && git add .
        git commit -m "update  $CI_PROJECT_NAME proto"
        git push https://oauth2:$<EMAIL>/shopee/bg-logistics/logistics/proto-center.git v2;
      fi

####                                            ####
### End modify your CI configuration from here   ###
###                                             ####

include:
  - project: 'shopee/sz-devops/dev-infra/model-x'
    ref: master
    file: '/templates/main.yml'

  # 源项目已有配置

check:
  # allow_failure: true  # 如果期望任何检测项不通过也不影响MR合入，可以打开此配置项
  variables:
    SEA_TALK_ROBOT: https://openapi.seatalk.io/webhook/group/uA6WpTHOSc6hocyhf3i1yw        # seatalk群组机器人的 webhook url， 机器人配置详见https://static.cdn.haiserve.com/seatalk/client/shared/notice_bot/guidelines.html
    SEA_TALK_NOTIFY_CATEGORYS: "error,warning"  # seatalk 通知的消息分类列表，逗号分隔，默认所有类型，可选项：error、warning、success
    SONAR_OPTS: -Dsonar.java.source=11  # 用户自定义代码规范扫描的参数信息
    SONAR_INCREMENTAL_SCAN: "true"      # 对应MergeRequest，默认采用全量扫描，如需增量扫描配置为 true
    SONAR_INCLUDE: "**/*.go"            # 用户自定义代码规范需要扫描的文件
    SONAR_EXCLUDE: "**/*_test.go,**/vendor/**,**.pb.**,*.md" # 用户自定义代码规范不需要扫描的文件
    CHECK_NO_ERROR: "true"  # 即使代码规范检测不通过也不期望job error，等效于NO_ERROR_ITEMS设置为"check:mr-branch,check:mr-content,check:jira,check:sonarqube"
    SEC_NO_ERROR: "true"    # 即使安全检测不通过也不期望job error,等效于NO_ERROR_ITEMS设置为"check:fortify"
    NO_ERROR_ITEMS: ""      # 不想导致job error的检测项，用逗号区分，例如"check:fortify,check:mr-branch", 可选项有check:fortify(安全监测),"check:mr-branch"(分支规范监测),"check:mr-content"(mr 信息包含jira单号),"check:sonarqube"(代码规范监测)
    SKIP_CHECK_ITEMS: ""    # 不希望执行某些检查项，例如"check:fortify,check:mr-branch", 可选项有check:fortify(安全监测),"check:mr-branch"(分支规范监测),"check:mr-content"(mr 信息包含jira单号),"check:sonarqube"(代码规范监测)
    GITLAB_TOKEN: $PUSH_PROTO_TOKEN # 如果check工具提示没有权限，可以配置项目Owner的Token
  extends: .model-x-check
  stage: scan #这里替换为源项目对应的stage名称


stages:
  - scan
  - push
  - notify

notifyFailSeatalk:
  stage: notify
  script:
    - |
      noticeMail=$GITLAB_USER_EMAIL
      message=`echo ${CI_COMMIT_MESSAGE} | tr -d "\r"`
      content="\n 项目名称: $CI_PROJECT_NAME \n 扫描结果: 失败 ❌ \n 提交号: $CI_COMMIT_SHA\n 提交日志: ${message}\n 构建分支: $CI_COMMIT_BRANCH\n 流水线地址: $CI_PIPELINE_URL\n"
      echo "$content"
      curl --location --request POST 'https://openapi.seatalk.io/webhook/group/uA6WpTHOSc6hocyhf3i1yw' \
      --header 'Content-Type: application/json' \
      --data-raw '{
          "text": {
              "content":"'"${content}"'",
              "at_all": false,
              "mentioned_email_list": [ "'"${noticeMail}"'"]
          },
          "tag": "text",
          "at_all": false
      }'

  when: on_failure



notifySuccessSeatalk:
  stage: notify
  script:
    - |
      noticeMail=$GITLAB_USER_EMAIL
      message=`echo ${CI_COMMIT_MESSAGE} | tr -d "\r"`
      content="\n 项目名称: $CI_PROJECT_NAME \n 扫描结果: 成功 ✅ \n 提交号: $CI_COMMIT_SHA\n 提交日志: ${message}\n 构建分支: $CI_COMMIT_BRANCH\n 流水线地址: $CI_PIPELINE_URL\n"
      echo "$content"
      curl --location --request POST 'https://openapi.seatalk.io/webhook/group/uA6WpTHOSc6hocyhf3i1yw' \
      --header 'Content-Type: application/json' \
      --data-raw '{
          "text": {
              "content":"'"${content}"'",
              "at_all": false,
              "mentioned_email_list": [ "'"${noticeMail}"'"]
          },
          "tag": "text",
          "at_all": false
      }'
#   only:
#     - test
#     - uat
#     - master
#     - release
#     - merge_requests
  when: on_success


push_proto:
  stage: push
  <<:
    - *push_proto
    - *tst_def