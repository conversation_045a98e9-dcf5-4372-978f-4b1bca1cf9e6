```mermaid
graph TD
    subgraph gRPC Controller Layer
        A["BatchCheckProductServiceableForItemScene"] --> B["...func1 (goroutine per item)<br/><b>(cum: 42.82%)</b>"];
        B --> C{"getLaneAreaServiceableByRule<br/><b>(cum: 40.35%)</b>"};
        B --> D{"getLinesAreaServiceableByRule<br/><b>(cum: 31.28%)</b>"};
    end

    subgraph Core Service Logic Layer
        C --> E["<font color=red>ServiceableCheckerService.BatchGetServiceable<br/><b>(cum: 26.18%)</b></font>"];
        D --> F["<font color=red>ServiceableCheckerService.GetServiceableWithCheckFlag<br/><b>(cum: 25.29%)</b></font>"];
        E --> G["getOriginServiceableByRequest<br/>(cum: 12.53%)"];
        F --> H["getDestServiceableByRequest<br/>(cum: 3.28%)"];
        E --> I["checkLocationServiceableRouteV2<br/>(cum: 3.80%)"];
    end

    subgraph Low-level & Utility Functions
        G --> J["...SearchLineOperationServiceableLocationDetailUseCacheV2<br/>(cum: 9.25%)"];
        H --> K["...SearchBasicServiceableLocationDetailUseCache<br/>(cum: 5.09%)"];
        I --> L["...CheckLineServiceableRouteExistUseCache<br/>(cum: 2.63%)"];
        C --> M["common.genNormalKey<br/>(cum: 8.96%)"];
        D --> M;
        G --> M;
    end

    subgraph Go Runtime & GC [Garbage Collection is a major factor]
        style E fill:#ffcccc
        style F fill:#ffcccc
        X["runtime.mallocgc<br/>(cum: 18.73%)"];
        Y["runtime.scanobject<br/>(cum: 24.86%)"];
        Z["runtime.findObject<br/>(cum: 9.09%)"];
    end

    E --> X;
    F --> Y;
    G --> Z;


    linkStyle 0 stroke-width:2px,fill:none,stroke:red;
    linkStyle 1 stroke-width:2px,fill:none,stroke:red;
    linkStyle 2 stroke-width:2px,fill:none,stroke:orange;
    linkStyle 3 stroke-width:2px,fill:none,stroke:orange;
```
