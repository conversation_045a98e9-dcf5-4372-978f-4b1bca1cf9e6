# OSX trash
.DS_Store

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
api_server

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Files generated by JetBrains IDEs
.idea/
*.iml

# Vscode files
.vscode

# Emacs save files
*~
\#*\#
.\#*

# Vim-related files
[._]*.s[a-w][a-z]
[._]s[a-w][a-z]
*.un~
Session.vim
.netrwhist

# make-related metadata
/.make/

# go project structure
go.sum
# vendor
bin
/log/
**/log/
setting/dev.json
play/
bak/
scratch*
main
forecast
application.apollo.json
/conf/local.toml.bak
/conf/local.toml

# others
*.pid
*.bak
profile.cov
conf_immutable_*.json
conf_mutable_*.json

# chassis
lcos-conf.json.json
conf/*.json
conf/chassis_conf/**/schema/*.yaml
branch.bleve/*
