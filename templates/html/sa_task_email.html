<html lang="en">
<head>
    <title>{{ .EmailTitle }}</title>
    <style>
        table {
            border-collapse: collapse;
        }

        table, table tr th, table tr td {
            border: 1px solid #000000;
        }
    </style>
</head>
<body>
<h2>{{ .EmailTitle }}</h2>

<p>
    {{ .EmailContent }}


</p>

<h3>Summary of this update:</h3>

<p>Time：{{ .NowTime }}</p>
<p>Auto Update Name：{{ .AutoUpdateName }}</p>
<p>Module: {{ .TaskModule }} - {{ .TaskType }}</p>
<p>LineID+GroupID：{{ .LineExtraInfoString }}</p>

{{ if .OverThreshold }}
<p>Total Number：{{ .TotalNumber }}</p>
<p>Number of Deletion：{{ .NumberOfDeletion }}</p>
{{ end }}


{{ if .ErrorMsg }}
<p>Error Info：{{ .ErrorMsg }}</p>
{{ end }}

{{ if .OverThreshold }}    <!--当前结果对比失败-->
<h3>Current Safety threshold setting:</h3>

<p>Total Number：should be >= {{ .TotalNumberCriteria }}</p>
<p>Number of Deletion：should be <= {{ .NumberOfDeletionCriteria }}</p>
{{ end }}
</body>
</html>