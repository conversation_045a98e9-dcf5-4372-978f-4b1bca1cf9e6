## 安装golangci-lint
```shell
brew install golangci/tap/golangci-lint
brew upgrade golangci/tap/golangci-lint
```
## 代码检测
```shell
make lint
```
## 生成protobuf
需要本地安装docker
```shell
make pb
```

## 部署架构
目前LCOS的部署架构可以分为三部分LCOS-API，LCOS-GRPC，LCOS-TASK。其各自的作用如下
### LCOS API
负责对外提供admin服务
### LCOS GRPC
提供高性能的GRPC接口给LFS/LPS/SLS调用
### LCOS TASK
提供定时任务服务


## 如何运行
### api
```shell
GOSUMDB=off ENV=test GO111MODULE=on go run cmd/api/main.go
```
### grpc
```shell
GOSUMDB=off ENV=test LOCAL_UNIQ_NAME=-lei PORT=8081 CHASSIS_HOME=/path/to/your/project go run cmd/grpc/main.go
```
    参数解析
    ENV: 运行环境，可选项为test/uat/staging
    LOCAL_UNIQ_NAME: 注册到服务发现的服务名。注意本地运行的话，此项必填，否则test环境的其他服务请求会转到本地，可以填为自己的名字，防止冲突
    PORT: 服务运行端口号，不填默认为8080
    CHASSIS_HOME: 项目根目录的绝对路径
