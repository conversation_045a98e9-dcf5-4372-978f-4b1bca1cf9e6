package main

import (
	"log"

	"golang.org/x/net/context"

	cache2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/limiter"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/chassis/handler"
	_ "git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/task" //自定义task，避免task中产生unsafe conext

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/grpc_api"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/graymachine"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/traffic"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	basic_location "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/fastpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
)

func main() {
	ctx := context.Background()
	grpcService := grpc_api.InitGRPCService()
	grpcService.Register()
	handler.RegisterRateLimitHandler()
	handler.RegisterLogHandler(handler.WithLogProviderRequestBodyParser(grpc_api.ReqRespParser))
	handler.RegisterLogHandler(handler.WithLogProviderResponseBodyParser(grpc_api.ReqRespParser))

	startup.InitSSCEnv()
	startup.SetChassisConfDir(startup.ServiceGrpc)
	//err := os.Setenv("CHASSIS_CONF_DIR", "conf/chassis_conf/grpc")
	//if err != nil {
	//	log.Fatalf("write chassis env error: %v", err)
	//}

	// 1. 注册prometheus handler
	handler.RegisterPrometheusMetricHandler()
	handler.RegisterReplayerHandler()
	handler.RegisterRecorderHandler(handler.RecorderHandlerOptions{})
	if err := chassis.Init(
		chassis.WithChassisConfigPrefix(cf.GetChassisConfigPrefix()),
		chassis.WithGRPCUnaryServerInterceptor(
			grpc_api.Recovery,
			grpc_api.LogRequest,
			grpc_api.MonitorReport,
			grpc_api.SecretValidate,
			grpc_api.Validate,
			grpc_api.BranchSwitch,
			traffic.Compare,
			grpc_api.SetContextKey,
		), chassis.WithDefaultProviderHandlerChain(
			handler.LogProviderHandlerName,
			handler.NameOfRateLimitHandlerOfProvider,
			handler.NameOfPrometheusMetricProvider,
			handler.RepalyerProviderName,
			handler.RecorderProviderName,
		), chassis.WithDefaultConsumerHandlerChain(
			handler.NameOfPrometheusMetricConsumer,
		)); err != nil {
		log.Fatal(err)
	}

	chassis.RegisterRespParser(grpc_api.ApiResponseParser)
	startup.InitMonitorMetrics()
	traffic.Init() //初始化流量比对
	_, err := cf.InitMutableConfig(ctx)
	if err != nil {
		log.Fatal(err)
	}
	_, err = cf.InitPublicConfig()
	if err != nil {
		log.Fatal(err)
	}
	cfg, err := cf.InitConfig(ctx)
	if err != nil {
		log.Fatal(err)
	}
	cf.InitChassisConfig()
	cf.InitLruConfig()

	err = startup.InitLibs(cfg)

	if err != nil {
		log.Fatalf("InitLibs Error: %v", err)
	}

	if err := startup.InitBranchGrpcLibs(cfg); err != nil {
		log.Fatalf("InitBranchLibs Error: %v", err)
	}

	if err = startup.InitLayeredCache(cfg); err != nil {
		log.Fatalf("Init layered cache error:%v", err)
	}
	if err = startup.InitRedisCacheForGrpc(cfg); err != nil {
		log.Fatalf("Init redis cache error: %v", err)
	}
	if startup.IsTimeProject() {
		// 仅time-grpc需要初始化EDD AB测试
		if err = startup.InitAbtest(cfg); err != nil {
			log.Fatalf("init abtest error:%v", err)
		}
	}

	err = config.RegisterListener(&cf.ApplicationConfigListener{})
	if err != nil {
		log.Fatalf("RegisterListener %v", err)
	}
	err = config.RegisterListener(&cf.MutableApplicationConfigListener{})
	if err != nil {
		log.Fatalf("RegisterListener %v", err)
	}
	err = config.RegisterListener(&cf.PublicApplicationConfigListener{})
	if err != nil {
		log.Fatalf("RegisterListener %v", err)
	}
	err = config.RegisterListener(&cf.LRUConfigListener{})
	if err != nil {
		log.Fatalf("Register LRUConfigListener %v", err)
	}
	err = cache2.StartUp(cfg)
	if err != nil {
		log.Fatalf("cache init error: %v", err)
	}

	if !cf.CheckConfig(ctx) {
		log.Fatalf("get lfs service error")
	}

	// 注册限流器
	limiter.InitLimiterRegistrar()

	// 内部定时任务，使用了redis，使用的context会产生unsafe context，因此改造为自定义task
	chassis.RegisterSchema("task", &graymachine.CampaignForGrayMachineTask{})

	// 启动Bitmap能力管理器（异步，不阻塞主服务）
	go func() {
		if err := startBitmapCapabilityManager(ctx); err != nil {
			log.Printf("Warning: BitmapCapabilityManager failed to start: %v", err)
		}
	}()

	log.Printf("[%s] start listen addr: %s\n", config.GetConfigValue("service_description.name"), config.GetConfigValue("cse.protocols.grpc.listenAddress"))
	if err := chassis.Run(); err != nil {
		log.Fatal(err)
	}
}

// startBitmapCapabilityManager 启动bitmap能力管理器
func startBitmapCapabilityManager(ctx context.Context) error {
	// 创建依赖
	lineLocationDAO := basic_location.NewLineBasicServiceableLocationDAO()

	// 创建BitmapCapabilityManager
	bitmapCapabilityManager := fastpath.NewBitmapCapabilityManager(lineLocationDAO)

	// 初始化并启动（完成后输出聚合统计）
	config := fastpath.DefaultBitmapCapabilityManagerConfig()
	if err := bitmapCapabilityManager.Initialize(ctx, config); err != nil {
		log.Printf("Failed to initialize BitmapCapabilityManager: %v", err)
		return err
	}

	// 设置为全局实例供其他地方使用
	fastpath.SetGlobalBitmapCapabilityManager(bitmapCapabilityManager)

	// 聚合统计日志：总keys与总序列化字节数，分地区
	agg := bitmapCapabilityManager.GetAggregateStats()
	log.Printf("BitmapCapabilityManager started successfully | total_keys=%v total_serialized_bytes=%v product_bitmaps=%v",
		agg["total_keys"], agg["total_serialized_bytes"], agg["product_bitmap_count"])
	regionAgg := bitmapCapabilityManager.GetRegionAggregateStats(nil)
	for region, s := range regionAgg {
		log.Printf("Bitmap region stats on start | region=%s product_bitmaps=%v total_keys=%v total_bytes=%v",
			region, s["product_bitmap_count"], s["total_keys"], s["total_serialized_bytes"])
	}
	return nil
}
