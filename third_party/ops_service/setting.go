package ops_service

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"time"
)

var (
	account = "lcs"
	hosts   = map[string]string{
		"LOCAL":   "https://ops.ssc.test.shopeemobile.com",
		"TEST":    "https://ops.ssc.test.shopeemobile.com",
		"UAT":     "https://ops.ssc.uat.shopeemobile.com",
		"STAGING": "https://ops.ssc.staging.shopeemobile.com",
		"LIVEISH": "https://ops.ssc.shopeemobile.com",
		"LIVE":    "https://ops.ssc.shopeemobile.com",
	}

	regionMapper = map[string]string{
		"BR": "Brazil",
		"ID": "Indonesia",
		"MY": "Malaysia",
		"PH": "Philippines",
		"SG": "Singapore",
		"TH": "Thailand",
		"TW": "Taiwan",
		"VN": "Vietnam",
		"CO": "Colombia",
		"CL": "Chile",
		"MX": "Mexico",
	}

	defaultTimeout = 100 * time.Second
	httpClient     *chassis.RestInvoker
)

const (
	locationSchemaId      = "protobuf.LocationService"
	GetFullLocationById   = "GetFullLocationByLocationId"
	GetLocationByName     = "GetLocationByName"
	GetLocationTree       = "GetLocationTree"
	GetLocationById       = "GetLocationByLocationId"
	GetAllLocation        = "GetLocationsByPage"
	GetFullLocationByName = "GetFullLocationByName"
)

func init() {
	httpClient = chassis.NewRestInvoker()
}
