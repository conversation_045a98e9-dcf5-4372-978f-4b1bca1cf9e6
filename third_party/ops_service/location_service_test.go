package ops_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
)

func TestGetUpLocationInfo(t *testing.T) {
	t.Skip()
	param := &GetLocationInfoByIdRequest{
		Country:    "PH",
		LocationId: 5046866,
	}

	os.Setenv("ENV", "TEST")
	result, err := GetUpLocationInfo(context.Background(), param)
	if err == nil {
		data, _ := jsoniter.Marshal(result)
		t.Log(string(data))
	}
	//assert.Nil(t, err)
}

func TestGetLocationInfoByLocationName(t *testing.T) {
	param := &GetLocationInfoByNameRequest{
		Country:      "MX",
		LocationName: []string{"Veracruz", "San Andrés Tuxtla"},
	}

	os.Setenv("ENV", "LIVE")
	result, err := GetLocationInfoByLocationName(context.Background(), param)
	assert.Nil(t, err)
	t.Log(result)
}

func TestSyncLocation(t *testing.T) {
	t.Skip()
	param := &GetAllLocationDataRequest{
		Country: "MX",
	}

	os.Setenv("ENV", "LIVE")
	result, err := GetAllLocations(context.Background(), param)
	assert.Nil(t, err)
	t.Log(result)
}

func TestGetUpLocationInfoByName(t *testing.T) {
	//t.Skip()
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	_, err = cf.InitMutableConfig()
	if err != nil {
		t.Fatalf("InitMutableConfig Error: %v", err)
	}
	type args struct {
		ctx   context.Context
		param *GetLocationInfoByNameRequest
	}
	tests := []struct {
		name  string
		args  args
		want  []*SingleLocationInfo
		want1 *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "get location info by name",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				param: &GetLocationInfoByNameRequest{
					Country:      "ID",
					LocationName: []string{"DKI JAKARTA", "KAB. KEPULAUAN SERIBU", "KEPULAUAN SERIBU SELATAN"},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := GetUpLocationInfoByName(tt.args.ctx, tt.args.param)
			if got != nil {
				for _, loc := range got {
					fmt.Println(fmt.Sprintf("%v-%v", loc.LocationId, loc.Name))
				}

			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetUpLocationInfoByName() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_slsLocationService_GetUpLocationInfo(t *testing.T) {
	//t.Skip()
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	type fields struct {
		serviceName string
	}
	type args struct {
		ctx   context.Context
		param *GetLocationInfoByIdRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *LocationResponse
		want1  *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "GetUpLocationInfo",
			fields: fields{
				serviceName: "sls-locationgrpc-test-id",
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				param: &GetLocationInfoByIdRequest{
					Country:    "ID",
					LocationId: 9598,
				},
			},
		},
		{
			name: "GetUpLocationInfo",
			fields: fields{
				serviceName: "sls-locationgrpc-test-id",
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				param: &GetLocationInfoByIdRequest{
					Country:    "ID",
					LocationId: 8315,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := &slsLocationService{
				serviceName: tt.fields.serviceName,
			}
			got, got1 := l.GetUpLocationInfo(tt.args.ctx, tt.args.param)
			if got != nil {
				if got.State != nil {
					fmt.Println(*got.State)
				}
				if got.City != nil {
					fmt.Println(*got.City)
				}
				if got.District != nil {
					fmt.Println(*got.District)
				}
				if got.Street != nil {
					fmt.Println(*got.Street)
				}
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("slsLocationService.GetUpLocationInfo() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestGetLocationInfoBySubLevel(t *testing.T) {
	_ = os.Setenv("ENV", "TEST")
	ctx := context.Background()
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	type args struct {
		ctx   context.Context
		param *GetLocationInfoBySubLevelRequest
	}
	tests := []struct {
		name  string
		args  args
		want  []*SingleLocationInfo
		want1 *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "GetLocationInfoBySubLevel",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				param: &GetLocationInfoBySubLevelRequest{
					Country:    "ID",
					LocationId: 9598,
					SubLevel:   4,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := GetLocationInfoBySubLevel(tt.args.ctx, tt.args.param)
			if len(got) > 0 {
				for _, loc := range got {
					fmt.Println(fmt.Sprintf("%v-%v-%v-%v-%v-%v", loc.LocationId, loc.Name, loc.Postcode, loc.Level, loc.Longitude, loc.Latitude))
				}
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("slsLocationService.GetLocationInfoBySubLevel() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestGetAllLocations(t *testing.T) {
	_ = os.Setenv("ENV", "TEST")
	ctx := context.Background()
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	_, err = cf.InitMutableConfig()
	if err != nil {
		t.Fatalf("InitMutableConfig Error: %v", err)
	}
	type args struct {
		ctx   context.Context
		param *GetAllLocationDataRequest
	}
	tests := []struct {
		name  string
		args  args
		want  []*LocationInfoItem
		want1 *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "getAllLocation",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				param: &GetAllLocationDataRequest{
					Country: "ID",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := GetAllLocations(tt.args.ctx, tt.args.param)
			if got != nil {
				fmt.Println(fmt.Sprintf("num of locations:%d, first one:%v", len(got), got[0]))
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetAllLocations() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
