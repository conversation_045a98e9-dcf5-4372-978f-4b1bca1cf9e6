package ops_service

import (
	"context"
	"encoding/json"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/env"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	locationPb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v21/sls-location/go"
	"strings"
	"sync"
)

type GetLocationInfoByIdRequest struct {
	Country    string `json:"country"`
	LocationId uint64 `json:"location_id"`
}

type GetLocationInfoByNameRequest struct {
	Country      string   `json:"country"`
	LocationName []string `json:"location_name"`
}

type GetLocationInfoBySubLevelRequest struct {
	Country    string `json:"country"`
	LocationId int    `json:"location_id"`
	SubLevel   int    `json:"sub_level"`
}

type GetLocationInfoBySubLevelByNameRequest struct {
	Country      string   `json:"country"`
	LocationName []string `json:"location_name"`
	SubLevel     int      `json:"sub_level"`
}

type GetAllLocationDataRequest struct {
	Country string `json:"country"`
}

type GetLocationInfoByIdResponse struct {
	Retcode int32                 `json:"retcode"`
	Message string                `json:"message"`
	Detail  string                `json:"detail"`
	Data    []*SingleLocationInfo `json:"data"`
}

type GetLocationInfoByNameResponse struct {
	Retcode int32               `json:"retcode"`
	Message string              `json:"message"`
	Detail  string              `json:"detail"`
	Data    *SingleLocationInfo `json:"data"`
}

type GetLocationInfoBySubLevelResponse struct {
	Retcode int32           `json:"retcode"`
	Message string          `json:"message"`
	Detail  string          `json:"detail"`
	Data    *SubLevelStruct `json:"data"`
}

// for sync all location
type LocationInfoItem struct {
	LocationId int     `json:"location_id"`
	ParentId   int     `json:"parent_id"`
	Name       string  `json:"name"`
	Level      int     `json:"level"`
	Postcode   string  `json:"postcode"`
	Longitude  float64 `json:"longitude"`
	Latitude   float64 `json:"latitude"`
}

type SingleLocationItemResponse struct {
	Locations []*LocationInfoItem `json:"locations"`
	Version   int                 `json:"version"`
}

type GetAllLocationsResponse struct {
	Retcode int32                       `json:"retcode"`
	Message string                      `json:"message"`
	Detail  string                      `json:"detail"`
	Data    *SingleLocationItemResponse `json:"data"`
}

type SubLevelStruct struct {
	LocationInfo    *SingleLocationInfo   `json:"location_info"`
	SubLocationInfo []*SingleLocationInfo `json:"sub_location_info"`
}

type BaseResponse struct {
	Retcode int32  `json:"retcode"`
	Message string `json:"message"`
	Detail  string `json:"detail"`
}

type SingleLocationInfo struct {
	LocationId int     `json:"location_id"`
	Name       string  `json:"name"`
	Level      int     `json:"level"`
	Postcode   string  `json:"postcode"`
	Longitude  float64 `json:"longitude"`
	Latitude   float64 `json:"latitude"`
}

type LocationResponse struct {
	State    *string `json:"state"`
	City     *string `json:"city"`
	District *string `json:"district"`
	Street   *string `json:"street"`
}

const (
	STATE    = 0
	CITY     = 1
	DISTRICT = 2
	STREET   = 3
)

var LocationCodeMap = map[int32]int32{
	-10103: lcos_error.LocationServerErrorCode,
	-2005:  lcos_error.NotFoundLocationErrorCode,
	-2006:  lcos_error.NotFoundLocationErrorCode,
	-2007:  lcos_error.NotFoundLocationErrorCode,
}

func GetUpLocationInfo(ctx context.Context, param *GetLocationInfoByIdRequest) (*LocationResponse, *lcos_error.LCOSError) {
	//全部切为grpc服务了
	return NewLocationService(ctx, strings.ToLower(param.Country)).GetUpLocationInfo(ctx, param)
}

func GetLocationInfoByLocationName(ctx context.Context, param *GetLocationInfoByNameRequest) (*SingleLocationInfo, *lcos_error.LCOSError) {
	return NewLocationService(ctx, strings.ToLower(param.Country)).GetLocationInfoByLocationName(ctx, param)
}

// 用于获取摸个location id下某个sub level的location 信息
func GetLocationInfoBySubLevel(ctx context.Context, param *GetLocationInfoBySubLevelRequest) ([]*SingleLocationInfo, *lcos_error.LCOSError) {
	return NewLocationService(ctx, strings.ToLower(param.Country)).GetLocationInfoBySubLevel(ctx, param)
}

/*
通过location id获取location信息
*/
func GetLocationInfo(ctx context.Context, param *GetLocationInfoByIdRequest) (*SingleLocationInfo, *lcos_error.LCOSError) {
	return NewLocationService(ctx, strings.ToLower(param.Country)).GetLocationInfo(ctx, param)
}

// GetLocationInfoBySubLevelByName 用于获取摸个location name下某个sub level的location 信息
func GetLocationInfoBySubLevelByName(ctx context.Context, param *GetLocationInfoBySubLevelByNameRequest) ([]*SingleLocationInfo, *lcos_error.LCOSError) {
	url := hosts[strings.ToUpper(utils.GetEnv(ctx))] + "/location_server/get_sub_location_layer_info_by_name/"
	paramByte, err := json.Marshal(param)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	responseByte, err := http.SendJwtGet(ctx, httpClient, url, paramByte, defaultTimeout, account, cf.GetThirdServiceSecretConfig(ctx).Ops)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.LocationServiceIOErrorCode, err.Error())
	}

	response := &GetLocationInfoBySubLevelResponse{}
	if err := json.Unmarshal(responseByte, response); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if response.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(LocationCodeMap[response.Retcode], response.Message+", "+response.Detail+", param: "+string(paramByte))
	}

	return response.Data.SubLocationInfo, nil
}

// GetAllLocations 用于获取摸个location name下某个sub level的location 信息
func GetAllLocations(ctx context.Context, param *GetAllLocationDataRequest) ([]*LocationInfoItem, *lcos_error.LCOSError) {
	return NewLocationService(ctx, param.Country).GetAllLocations(ctx, param)
}

func GetMostDetailLocationNames(state, city, district, street *string) []string {
	result := []string{}
	if state != nil {
		result = append(result, *state)
	}
	if city != nil {
		result = append(result, *city)
	}
	if district != nil {
		result = append(result, *district)
	}
	if street != nil {
		result = append(result, *street)
	}
	return result
}

func GetUpLocationInfoByName(ctx context.Context, param *GetLocationInfoByNameRequest) ([]*SingleLocationInfo, *lcos_error.LCOSError) {
	return NewLocationService(ctx, param.Country).GetUpLocationInfoByName(ctx, param)
}

/*
sls-location's GRPC call implementation as bellow,
write new function in case of no effect on the old HTTP method
*/

var invoker = chassis.NewRPCInvoker()

type slsLocationService struct {
	serviceName string
}

var (
	serviceNameLocationApi          = "sls-locationapi"
	serviceNameLocationGrpc         = "sls-locationgrpc"
	serviceNameLocationGrpcLiveTest = "sls-locationgrpclivetest"
)

func NewLocationService(ctx context.Context, region string) *slsLocationService {
	locationServiceName := serviceNameLocationApi
	if env.IsGrpcService() || config.NeedLatamConfig("") {
		locationServiceName = serviceNameLocationGrpc
	}

	if config.GetEnv(ctx) == config.LIVE &&
		(strings.ToUpper("grpclivetest") == config.GetModuleName() || strings.ToUpper("apilivetest") == config.GetModuleName()) {
		//locationapi无livetest环境,lcos-task也无livetest环境
		locationServiceName = serviceNameLocationGrpcLiveTest
	}
	if region == "" {
		region = utils.GetCID()
	}
	// 判定region是否是在sls-location中配置的region，如果是CN/KR国际地址则使用SG
	_, ok := regionMapper[strings.ToUpper(region)]
	if !ok {
		region = "sg"
	}
	serviceName := utils.GenKey("-", locationServiceName, strings.ToLower(utils.GetEnv(ctx)), strings.ToLower(region))
	return &slsLocationService{serviceName: serviceName}
}

// get Up Location, seems grpc will return location list
func (l *slsLocationService) GetUpLocationInfo(ctx context.Context, param *GetLocationInfoByIdRequest) (*LocationResponse, *lcos_error.LCOSError) {
	req := &locationPb.LocationReq{
		Country:    param.Country,
		LocationId: uint32(param.LocationId),
	}
	rsp := &locationPb.FullLocationRsp{}
	if err := invoker.Invoke(ctx, l.serviceName, locationSchemaId, GetFullLocationById, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_id=[%d]|error=%v", "GetFullLocationById", param.LocationId, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_id=[%d]|error=%v", "GetFullLocationById", param.LocationId, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_id=[%d]|error_code=%d|error=%v", "GetFullLocationById", param.LocationId, rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetHeader().GetMessage())
	}
	logger.CtxLogInfof(ctx, "requesting sls location|api=%s|req=%v, grpc rsp=%v", "GetFullLocationById", req, rsp)
	resp := &LocationResponse{}
	for _, locationInfo := range rsp.GetFullLocation().GetLocationList() {
		switch locationInfo.Level {
		case STATE:
			resp.State = &locationInfo.Name
		case CITY:
			resp.City = &locationInfo.Name
		case DISTRICT:
			resp.District = &locationInfo.Name
		case STREET:
			resp.Street = &locationInfo.Name
		}
	}
	return resp, nil
}

/*
get location info by name
*/
func (l *slsLocationService) GetLocationInfoByLocationName(ctx context.Context, param *GetLocationInfoByNameRequest) (*SingleLocationInfo, *lcos_error.LCOSError) {
	req := &locationPb.LocationReq{
		Country:    param.Country,
		SplitNames: param.LocationName,
	}
	rsp := &locationPb.LocationRsp{}
	if err := invoker.Invoke(ctx, l.serviceName, locationSchemaId, GetLocationByName, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_name=[%s]|error=%v", "GetLocationByName", strings.Join(param.LocationName, ","), err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_name=[%s]|error=%v", "GetLocationByName", strings.Join(param.LocationName, ","), "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_name=[%s]|error_code=%d|error=%v", "GetLocationByName", strings.Join(param.LocationName, ","), rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetHeader().GetMessage())
	}
	logger.CtxLogInfof(ctx, "requesting sls location|api=%s|req=%v, grpc rsp=%v", "GetLocationByName", req, rsp)
	resp := &SingleLocationInfo{
		LocationId: int(rsp.GetLocationInfo().GetLocationId()),
		Name:       rsp.GetLocationInfo().GetName(),
		Level:      int(rsp.GetLocationInfo().GetLevel()),
		Postcode:   rsp.GetLocationInfo().GetPostcode(),
		Latitude:   rsp.GetLocationInfo().GetLatitude(),
		Longitude:  rsp.GetLocationInfo().GetLongitude(),
	}
	return resp, nil
}

/*
for sub level of a certain location
*/
func (l *slsLocationService) GetLocationInfoBySubLevel(ctx context.Context, param *GetLocationInfoBySubLevelRequest) ([]*SingleLocationInfo, *lcos_error.LCOSError) {
	req := &locationPb.LocationTreeReq{
		Country:    param.Country,
		LocationId: uint32(param.LocationId),
		SubLevel:   int32(param.SubLevel),
	}
	rsp := &locationPb.LocationTreeRsp{}
	if err := invoker.Invoke(ctx, l.serviceName, locationSchemaId, GetLocationTree, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_id=[%d]|error=%v", "GetLocationTree", param.LocationId, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_id=[%d]|error=%v", "GetLocationTree", param.LocationId, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_id=[%d]|error_code=%d|error=%v", "GetLocationTree", param.LocationId, rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetHeader().GetMessage())
	}
	logger.CtxLogInfof(ctx, "requesting sls location|api=%s|req=%v, grpc rsp=%v", "GetLocationTree", req, rsp)
	var resp []*SingleLocationInfo
	for _, node := range rsp.GetLocationTree().ChildLocations {
		resp = append(resp, &SingleLocationInfo{
			LocationId: int(node.GetLocationId()),
			Name:       node.GetName(),
			Level:      int(node.GetLevel()),
			Postcode:   node.GetPostcode(),
			Longitude:  node.GetLongitude(),
			Latitude:   node.GetLatitude(),
		})
	}
	return resp, nil
}

/*
get location by id
*/
func (l *slsLocationService) GetLocationInfo(ctx context.Context, param *GetLocationInfoByIdRequest) (*SingleLocationInfo, *lcos_error.LCOSError) {
	req := &locationPb.LocationReq{
		Country:    param.Country,
		LocationId: uint32(param.LocationId),
	}
	rsp := &locationPb.LocationRsp{}
	if err := invoker.Invoke(ctx, l.serviceName, locationSchemaId, GetLocationById, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_id=[%d]|error=%v", "GetLocationByLocationId", param.LocationId, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_id=[%d]|error=%v", "GetLocationByLocationId", param.LocationId, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_id=[%d]|error_code=%d|error=%v", "GetLocationByLocationId", param.LocationId, rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetHeader().GetMessage())
	}
	logger.CtxLogInfof(ctx, "requesting sls location|api=%s|req=%v, grpc rsp=%v", "GetLocationByLocationId", req, rsp)
	resp := &SingleLocationInfo{
		LocationId: int(rsp.GetLocationInfo().GetLocationId()),
		Name:       rsp.GetLocationInfo().GetName(),
		Level:      int(rsp.GetLocationInfo().GetLevel()),
		Postcode:   rsp.GetLocationInfo().GetPostcode(),
		Longitude:  rsp.GetLocationInfo().GetLongitude(),
		Latitude:   rsp.GetLocationInfo().GetLatitude(),
	}
	return resp, nil
}

/*
get all sub-level locations under a certain location
*/
func (l *slsLocationService) GetAllLocations(ctx context.Context, param *GetAllLocationDataRequest) ([]*LocationInfoItem, *lcos_error.LCOSError) {
	// TODO concurrency use of new grpc
	pageResp := []*locationPb.LocationPageRsp{}
	var retry bool
	for k := 0; k <= MaxRetries; k++ {
		pageResp = pageResp[:0]
		rsp, err := l.getLocationByPage(ctx, param.Country, 1000, 1)
		if err != nil {
			return nil, err
		}
		total := int(rsp.GetPageInfo().GetTotalNum())
		version := rsp.GetPageInfo().GetVersion()
		//batchCount := total / BatchNum
		country := param.Country

		g := NewG(10)
		wg := &sync.WaitGroup{}
		var lock sync.Mutex
		for i := 0; i <= total/BatchNum; i++ {
			wg.Add(1)
			pageno := i + 1

			g.Run(func() {
				ret, err := l.getLocationByPage(ctx, country, uint32(BatchNum), uint32(pageno))
				if err != nil {
					retry = true
				} else {
					lock.Lock()
					pageResp = append(pageResp, ret)
					lock.Unlock()
				}
				wg.Done()
			})
		}
		wg.Wait()
		for _, page := range pageResp {
			if page.GetPageInfo().GetVersion() != version {
				retry = true
				break
			}
		}
		if retry {
			continue
		} else {
			break
		}
	}
	var resp []*LocationInfoItem
	for _, page := range pageResp {
		for _, info := range page.GetPageInfo().LocationList {
			resp = append(resp, &LocationInfoItem{
				LocationId: int(info.GetLocationId()),
				ParentId:   int(info.GetParentId()),
				Name:       info.GetName(),
				Level:      int(info.GetLevel()),
				Postcode:   info.GetPostcode(),
				Longitude:  info.GetLongitude(),
				Latitude:   info.GetLatitude(),
			})
		}
	}
	logger.CtxLogInfof(ctx, "requesting sls location|api=%s|country=%s, num of locations=%d", "GetLocationsByPage", param.Country, len(resp))
	return resp, nil
}

func (l *slsLocationService) getLocationByPage(ctx context.Context, country string, limit, pageno uint32) (*locationPb.LocationPageRsp, *lcos_error.LCOSError) {
	req := &locationPb.LocationPageReq{
		Country: country,
		PageNo:  pageno,
		Limit:   limit,
	}
	rsp := &locationPb.LocationPageRsp{}
	if err := invoker.Invoke(ctx, l.serviceName, locationSchemaId, GetAllLocation, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|country=[%s]|error=%v", "GetAllLocations", country, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|country=[%s]|error=%v", "GetAllLocations", country, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|country=[%s]|error_code=%d|error=%v", "GetAllLocations", country, rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetHeader().GetMessage())
	}
	return rsp, nil
}

/*
get location info by location name
*/
func (l *slsLocationService) GetUpLocationInfoByName(ctx context.Context, param *GetLocationInfoByNameRequest) ([]*SingleLocationInfo, *lcos_error.LCOSError) {
	req := &locationPb.LocationReq{
		Country:    param.Country,
		SplitNames: param.LocationName,
	}
	rsp := &locationPb.FullLocationRsp{}
	if err := invoker.Invoke(ctx, l.serviceName, locationSchemaId, GetFullLocationByName, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_name=[%s]|error=%v", "GetFullLocationByName", strings.Join(param.LocationName, ","), err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_name=[%s]|error=%v", "GetFullLocationByName", strings.Join(param.LocationName, ","), "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting sls location error|api=%s|location_name=[%s]|error_code=%d|error=%v", "GetFullLocationByName", strings.Join(param.LocationName, ","), rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetHeader().GetMessage())
	}
	logger.CtxLogInfof(ctx, "requesting sls location|api=%s|req=%v, grpc rsp=%v", "GetFullLocationByName", req, rsp)
	var resp []*SingleLocationInfo
	for _, locationInfo := range rsp.GetFullLocation().GetLocationList() {
		resp = append(resp, &SingleLocationInfo{
			LocationId: int(locationInfo.GetLocationId()),
			Name:       locationInfo.GetName(),
			Level:      int(locationInfo.GetLevel()),
			Postcode:   locationInfo.GetPostcode(),
			Longitude:  locationInfo.GetLongitude(),
			Latitude:   locationInfo.GetLatitude(),
		})
	}
	return resp, nil
}
