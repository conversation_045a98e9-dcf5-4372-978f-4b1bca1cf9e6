package sls_location_sdk

import (
	"context"
	slslocationplugin "git.garena.com/shopee/bg-logistics/logistics/sls-location-plugin"
	"git.garena.com/shopee/bg-logistics/logistics/sls-location-plugin/geo_distance"
)

var client SlsLocationSdkClient

// Init SLS-Location SDK初始化。有chassis环境依赖，应当在chassis.Init之后初始化，并且在服务启动之前初始化完成
func Init() {
	client = SlsLocationSdkClient{
		client: slslocationplugin.NewSlsLocationSDKImplWithOptions(),
	}
}

// Client 获取SLS-Location SDK的客户端实例
func Client() SlsLocationSdkClient {
	return client
}

type SlsLocationSdkClient struct {
	client slslocationplugin.SlsLocationSDK
}

func (s SlsLocationSdkClient) GetGeoDistance(ctx context.Context, req geo_distance.GetGeoDistanceRequest, opts ...geo_distance.Option) (*geo_distance.GetGeoDistanceResponse, error) {
	if s.client == nil {
		return nil, ErrSdkNotInitialized
	}
	return s.client.GetGeoDistance(ctx, req, opts...)
}

func (s SlsLocationSdkClient) BatchGetGeoDistance(ctx context.Context, req geo_distance.BatchGetGeoDistanceRequest, opts ...geo_distance.Option) (*geo_distance.BatchGetGeoDistanceResponse, error) {
	if s.client == nil {
		return nil, ErrSdkNotInitialized
	}
	return s.client.BatchGetGeoDistance(ctx, req, opts...)
}
