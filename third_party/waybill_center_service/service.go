package waybill_center_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/env"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/mock_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	"github.com/gogo/protobuf/proto"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/jwt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/region_tools"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	wbc_protocol "git.garena.com/shopee/bg-logistics/logistics/proto-center/v19/logistics-waybill-center/go"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/grpc/metadata"
)

type WaybillCenterService struct {
	Region  string
	Account string
	Secret  string
	Timeout int
	Host    string
}

// wbc api service
func NewWaybillCenterService(ctx context.Context, region string) *WaybillCenterService {
	waybillCenterConfig := config.GetWaybillCenterServiceConfig(ctx)
	return &WaybillCenterService{
		Region:  strings.ToUpper(region),
		Account: waybillCenterConfig.Account,
		Secret:  waybillCenterConfig.Secret,
		Timeout: waybillCenterConfig.Timeout,
		Host:    waybillCenterConfig.Host,
	}
}

func (w *WaybillCenterService) getUrl(ctx context.Context) string {
	// SPLN-33587 支持设置 waybill service host 用于动态 mock
	if len(w.Host) > 0 {
		return w.Host
	}
	env := utils.GetEnv(ctx)
	if env == config.LIVE {
		env = ""
	} else {
		env = "." + env
	}
	regionSuffix := region_tools.GetCountrySuffix(w.Region)
	return fmt.Sprintf(host, strings.ToLower(env), regionSuffix)
}

func (w *WaybillCenterService) generateJwtToken(ctx context.Context) (string, error) {

	nowTime := utils.GetTimestamp(ctx)
	payLoad := map[string]interface{}{
		"timestamp": nowTime,
		"info": map[string]interface{}{
			"entity": map[string]string{
				"country": w.Region,
			},
		},
	}

	header := map[string]interface{}{
		"alg":  "HS256",
		"typ":  "JWT",
		"optr": w.Account,
	}

	return jwt.GetJwtDataV2(w.Secret, header, payLoad)
}

func (w *WaybillCenterService) doRequestWaybillCenter(ctx utils.LCOSContext, req, resp interface{}, urlPath string) *lcos_error.LCOSError {
	var requestID string
	if c, ok := ctx.(*utils.HttpContext); ok {
		requestID = c.GetRequestId()
	}

	logger.CtxLogInfof(ctx, "RequestID of in method doRequestWaybillCenter is %s", requestID)

	tokenString, err := w.generateJwtToken(ctx)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.JwtDecodeErrorCode, err.Error())
	}
	url := w.getUrl(ctx) + urlPath
	return http.SendPostHttpRequest(ctx, httpClient, time.Duration(int64(w.Timeout))*time.Second, url, req, resp, map[string]string{
		"request-id": requestID,
		"jwt-token":  tokenString,
	})
}

func (w *WaybillCenterService) PushEDD(ctx utils.LCOSContext, edd *PushEDDInfo) *lcos_error.LCOSError {
	resp := &PushEDDResponse{}
	lcosErr := w.doRequestWaybillCenter(ctx, edd, resp, PUSHEDDAPI)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "requesting waybill center error|sls_tn=%s|edd=%d|api=%s|error=%s", edd.SlsTN, edd.EDD, PUSHEDDAPI, lcosErr.Msg)
		return lcosErr
	}
	if resp.Retcode != lcos_error.SuccessCode {
		logger.CtxLogErrorf(ctx, "requesting waybill center error|sls_tn=%s|edd=%d|api=%s|retcode=%d|error_message=%s", edd.SlsTN, edd.EDD, PUSHEDDAPI, resp.Retcode, resp.Message)
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.Message)
	}
	logger.CtxLogInfof(ctx, "requesting waybill center success|sls_tn=%s|edd=%d|api=%s", edd.SlsTN, edd.EDD, PUSHEDDAPI)
	return nil
}

func (w *WaybillCenterService) BatchGetOrderInfoWithConcurrency(ctx utils.LCOSContext, slsTNList []string, batchSize, maxCurrency int) (map[string]*LaneCodeInfo, *lcos_error.LCOSError) {

	logger.CtxLogInfof(ctx, "ready to call waybillbill center with batch size:[%d], max concurrency:[%d]", batchSize, maxCurrency)

	// split sls tn list into batches
	var chunks [][]string
	for i := 0; i < len(slsTNList); i += batchSize {
		end := i + batchSize
		if end > len(slsTNList) {
			end = len(slsTNList)
		}
		chunks = append(chunks, slsTNList[i:end])
	}

	resultMap := make(map[string]*LaneCodeInfo)

	maxCurrencyChan := make(chan struct{}, maxCurrency)
	var waitGroup sync.WaitGroup
	var lock sync.Mutex

	for _, singleChunk := range chunks {
		maxCurrencyChan <- struct{}{}
		waitGroup.Add(1)
		go func(singleBatches []string) {
			response, lcosErr := w.BatchGetLaneCodeInfo(ctx, singleBatches)
			if lcosErr == nil {
				lock.Lock()
				for key, value := range response {
					resultMap[key] = value
				}
				lock.Unlock()
			} else {
				// put error to every single sls tn
				lock.Lock()
				for _, singleSlsTn := range singleBatches {
					resultMap[singleSlsTn] = &LaneCodeInfo{
						Message: lcosErr.Msg,
						Retcode: int(lcosErr.RetCode),
					}
				}
				lock.Unlock()
			}
			<-maxCurrencyChan
			waitGroup.Done()
		}(singleChunk)
	}
	waitGroup.Wait()
	return resultMap, nil
}

func (w *WaybillCenterService) BatchGetLaneCodeInfo(ctx utils.LCOSContext, slsTNList []string) (map[string]*LaneCodeInfo, *lcos_error.LCOSError) {
	resp := &LaneInfoResponse{}
	req := &WaybillInfoRequest{SlsTNList: slsTNList}
	lcosErr := w.doRequestWaybillCenter(ctx, req, resp, GetLaneCodeAPI)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "requesting waybill center error|sls_tn_list=%s|api=%s|error=%s", strings.Join(slsTNList, ","), GetLaneCodeAPI, lcosErr.Msg)
		return nil, lcosErr
	}
	if resp.Retcode != lcos_error.SuccessCode {
		logger.CtxLogErrorf(ctx, "requesting waybill center error|sls_tn_list=%s|api=%s|retcode=%d|error_message=%s", strings.Join(slsTNList, ","), GetLaneCodeAPI, resp.Retcode, resp.Message)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.Message)
	}

	respStr, _ := jsoniter.MarshalToString(resp.Data)
	logger.CtxLogInfof(ctx, "requesting waybill center success|sls_tn=%s|api=%s|response=%s", strings.Join(slsTNList, ","), GetLaneCodeAPI, respStr)

	resultMap := make(map[string]*LaneCodeInfo)
	for _, singleData := range resp.Data.List {
		sort.SliceStable(singleData.WaybillSequenceList, func(i, j int) bool {
			return singleData.WaybillSequenceList[i].Sequence < singleData.WaybillSequenceList[j].Sequence
		})
		resultMap[singleData.SlsTN] = singleData
	}
	return resultMap, nil
}

func (w *WaybillCenterService) GetSloInfoWithAction(ctx utils.LCOSContext, reqList []*GetSloInfoWithActionRequest) ([]*SloInfo, *lcos_error.LCOSError) {
	req := &BatchGetSloInfoWithActionRequest{
		List: reqList,
	}
	var resp GetSloInfoWithActionResponse
	if err := w.doRequestWaybillCenter(ctx, req, &resp, GetSloInfoWithActionAPI); err != nil {
		logger.CtxLogErrorf(ctx, "requesting waybill center error|req=%s|api=%s|error=%s", utils.MarshToStringWithoutError(req), GetSloInfoWithActionAPI, err.Msg)
		return nil, err
	}
	if resp.Retcode != lcos_error.SuccessCode {
		logger.CtxLogErrorf(ctx, "requesting waybill center error|req=%s|api=%s|retcode=%d|error_message=%s", utils.MarshToStringWithoutError(req), GetSloInfoWithActionAPI, resp.Retcode, resp.Message)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.Message)
	}
	logger.CtxLogInfof(ctx, "requesting waybill center success|sls_tn=%s|api=%s|response=%s", utils.MarshToStringWithoutError(req), GetSloInfoWithActionAPI, utils.MarshToStringWithoutError(resp.Data))
	return resp.Data.List, nil
}

// wbc static service
type WayBillCenterStaticService struct {
	cid           string
	serviceName   string
	maxRetryTimes int
}

var invoker = chassis.NewRPCInvoker()

func NewWayBillCenterStaticService(region string, maxRetryTimes int) *WayBillCenterStaticService {
	serviceName := "wbc-static-sg" // wbc-static为global服务
	if env.IsUSIdc() {
		// SBM后在us机房的服务分市场部署，请求对应市场
		serviceName = "wbc-static-" + strings.ToLower(region)
	}

	return &WayBillCenterStaticService{
		cid:           region,
		serviceName:   serviceName,
		maxRetryTimes: maxRetryTimes,
	}
}

func (w *WayBillCenterStaticService) genHeader(ctx utils.LCOSContext) *wbc_protocol.WbcReqHeader {
	return &wbc_protocol.WbcReqHeader{
		RequestId: utils.NewString(ctx.GetRequestId()),
		Account:   utils.NewString(config.GetConf(ctx).WBCStaticService.Account),
		Token:     utils.NewString(config.GetConf(ctx).WBCStaticService.Token),
		Timestamp: utils.NewUint32(utils.GetTimestamp(ctx)),
		CallerIp:  utils.GetLocalIp(),
	}
}

func (w *WayBillCenterStaticService) doGrpcRequestWithRetries(ctx utils.LCOSContext, schema string, operation string, req interface{}, resp interface{}) *lcos_error.LCOSError {
	var err error
	for i := 0; i < w.maxRetryTimes+1; i++ {
		if i != 0 {
			logger.CtxLogInfof(ctx, "retry %d time(s), call grpc service[%s] %s.%s|req=%+v", i, w.serviceName, schema, operation, req)
		}
		if err = invoker.Invoke(metadata.AppendToOutgoingContext(ctx, "ssc-biz-cid", w.cid), w.serviceName, schema, operation, req, resp, chassis.WithProtocol("grpc")); err == nil {
			return nil
		}
		logger.CtxLogErrorf(ctx, "call grpc service[%s] %s.%s error: %s|req=%+v", w.serviceName, schema, operation, err.Error(), req)
	}
	msg := fmt.Sprintf("call grpc service[%s] %s.%s %d time(s) still error: %s", w.serviceName, schema, operation, w.maxRetryTimes+1, err.Error())
	logger.CtxLogErrorf(ctx, msg)
	return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, msg)
}

type SnapShotData struct {
	DeliveryEstimateTransitTimeBusinessDays   float64 `json:"delivery_estimate_transit_time_business_days"`
	DeliveryAdditionalTransitTimeBusinessDays float64 `json:"delivery_additional_transit_time_business_days"`
}

func (w *WayBillCenterStaticService) GetCdtByForderId(ctx utils.LCOSContext, forderId uint64) (float64, *lcos_error.LCOSError) {
	// 1. build pb request and reply
	req := wbc_protocol.QueryStaticDataRequest{
		ReqHeader:    w.genHeader(ctx),
		ClientName:   utils.NewString("PIS"), // fixed
		SnapshotFlag: utils.NewBool(true),    // fixed
		ForderId:     utils.NewString(strconv.FormatUint(forderId, 10)),
	}
	resp := wbc_protocol.QueryStaticDataResponse{}
	// 2. invoke rpc service with retries
	if err := w.doGrpcRequestWithRetries(ctx, StaticDataServiceSchemaID, QueryStaticDataOperationID, &req, &resp); err != nil {
		logger.CtxLogErrorf(ctx, "requesting wbc error|schema_id=%s|api=%s|forder_id=[%d]|error=[%s]", StaticDataServiceSchemaID, QueryStaticDataOperationID, forderId, err.Msg)
		return 0, err
	}
	// 3. parse rpc response
	if resp.GetRespHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting wbc error|schema_id=%s|api=%s|forder_id=[%d]|error=[%s]", StaticDataServiceSchemaID, QueryStaticDataOperationID, forderId, resp.GetRespHeader().GetMessage())
		return 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, resp.GetRespHeader().GetMessage())
	}
	if len(resp.GetSnapshotInfo()) == 0 {
		errorMsg := "get cdt snapshot info is null"
		logger.CtxLogErrorf(ctx, "requesting wbc error|schema_id=%s|api=%s|forder_id=[%d]|error=[%s]", StaticDataServiceSchemaID, QueryStaticDataOperationID, forderId, errorMsg)
		return 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errorMsg)
	}
	logger.CtxLogInfof(ctx, "requesting wbc success|schema_id=%s|api=%s|forder_id=[%d]|resp=[%s]", StaticDataServiceSchemaID, QueryStaticDataOperationID, forderId, resp.GetSnapshotInfo()[0].GetSnapshotData())
	data := SnapShotData{}
	_ = jsoniter.UnmarshalFromString(resp.GetSnapshotInfo()[0].GetSnapshotData(), &data) // return 0 if unmarshal error
	return data.DeliveryEstimateTransitTimeBusinessDays + data.DeliveryAdditionalTransitTimeBusinessDays, nil
}

func (w *WayBillCenterStaticService) BatchQueryStaticData(ctx utils.LCOSContext, forderId uint64) (*CreateOrderReq, *lcos_error.LCOSError) {
	// 1. build pb request and reply
	req := wbc_protocol.BatchQueryStaticDataRequest{
		ReqHeader:    w.genHeader(ctx),
		ClientName:   utils.NewString("LCOS"), // fixed
		ForderIdList: []string{strconv.FormatUint(forderId, 10)},
	}
	resp := wbc_protocol.BatchQueryStaticDataResponse{}
	// 2. invoke rpc service with retries
	if err := w.doGrpcRequestWithRetries(ctx, StaticDataServiceSchemaID, BatchQueryStaticDataID, &req, &resp); err != nil {
		logger.CtxLogErrorf(ctx, "requesting wbc error|schema_id=%s|api=%s|forder_id=[%d]|error=[%s]", StaticDataServiceSchemaID, BatchQueryStaticDataID, forderId, err.Msg)
		return nil, err
	}
	// 3. parse rpc response
	if resp.GetRespHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting wbc error|schema_id=%s|api=%s|forder_id=[%d]|error=[%s]", StaticDataServiceSchemaID, BatchQueryStaticDataID, forderId, resp.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, resp.GetRespHeader().GetMessage())
	}
	if len(resp.GetResultList()) == 0 {
		errorMsg := "get empty result list"
		logger.CtxLogErrorf(ctx, "requesting wbc error|schema_id=%s|api=%s|forder_id=[%d]|error=[%s]", StaticDataServiceSchemaID, BatchQueryStaticDataID, forderId, errorMsg)
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errorMsg)
	}

	logger.CtxLogInfof(ctx, "requesting wbc success|schema_id=%s|api=%s|forder_id=[%d]|resp=[%s]", StaticDataServiceSchemaID, BatchQueryStaticDataID, forderId, resp.GetResultList())
	data := &CreateOrderReq{}
	_ = jsoniter.UnmarshalFromString(resp.GetResultList()[0].GetBusinessData(), data) // return 0 if unmarshal error

	return data, nil
}

func (w *WayBillCenterStaticService) QueryParcelDataByTime(ctx utils.LCOSContext, skusCombinationId uint64, timestamp uint32) ([]*parcel_library.LogisticParcelLibraryData, bool, *lcos_error.LCOSError) {
	var (
		req = &wbc_protocol.QueryParcelDataByTimeRequest{
			ReqHeader:         w.genHeader(ctx),
			SkusCombinationId: proto.Uint64(skusCombinationId),
			Timestamp:         proto.Uint32(timestamp),
		}
		resp wbc_protocol.QueryParcelDataByTimeResponse
	)

	// 1. 执行grpc请求，支持mock
	if target, useMock := mock_utils.GrpcMockTarget(ctx, mock_utils.MockWbcStatic, req.GetReqHeader().GetRequestId()); useMock {
		if err := mock_utils.MockGrpcInvoke(ctx, invoker, target, ParcelDataServiceSchemaID, QueryParcelDataByTimeOperationID, req, &resp); err != nil {
			return nil, false, err
		}
	} else {
		if err := w.doGrpcRequestWithRetries(ctx, ParcelDataServiceSchemaID, QueryParcelDataByTimeOperationID, req, &resp); err != nil {
			logger.CtxLogErrorf(ctx, "requesting wbc error|schema_id=%s|api=%s|combination_id=[%d],timestamp=[%d]|error=[%s]", ParcelDataServiceSchemaID, QueryParcelDataByTimeOperationID, skusCombinationId, timestamp, err.Msg)
			return nil, false, err
		}
	}
	if resp.GetRespHeader().GetRetcode() != lcos_error.SuccessCode {
		logger.CtxLogErrorf(ctx, "requesting wbc error|schema_id=%s|api=%s|combination_id=[%d],timestamp=[%d]|error=[%s]", ParcelDataServiceSchemaID, QueryParcelDataByTimeOperationID, skusCombinationId, timestamp, resp.GetRespHeader().GetMessage())
		return nil, false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.GetRespHeader().GetMessage())
	}
	logger.CtxLogInfof(ctx, "requesting wbc success|schema_id=%s|api=%s|combination_id=[%d],timestamp=[%d]|listing_usable=[%v],parcel_data=[%+v]", ParcelDataServiceSchemaID, QueryParcelDataByTimeOperationID, skusCombinationId, timestamp, resp.GetListingUsable(), resp.GetParcelDataList())

	// 2. 解析wbc响应接口，需要转换尺寸和重量并基于生效时间排序
	dataList := make([]*parcel_library.LogisticParcelLibraryData, 0, len(resp.GetParcelDataList()))
	for _, data := range resp.GetParcelDataList() {
		parsedData := &parcel_library.LogisticParcelLibraryData{
			Version: data.GetVersion(),
			Ctime:   data.GetCtime(),
			Mtime:   data.GetMtime(),
		}
		if length, err := strconv.ParseFloat(data.GetAccurateLength(), 64); err == nil {
			parsedData.AccurateLength = length
		}
		if width, err := strconv.ParseFloat(data.GetAccurateWidth(), 64); err == nil {
			parsedData.AccurateWidth = width
		}
		if height, err := strconv.ParseFloat(data.GetAccurateHeight(), 64); err == nil {
			parsedData.AccurateHeight = height
		}
		if weight, err := strconv.ParseFloat(data.GetAccurateWeight(), 64); err == nil {
			parsedData.AccurateWeight = weight
		}

		dataList = append(dataList, parsedData)
	}
	sort.SliceStable(dataList, func(i, j int) bool {
		return dataList[i].Version > dataList[j].Version // 基于版本时间从新到旧排序
	})
	return dataList, resp.GetListingUsable(), nil
}
