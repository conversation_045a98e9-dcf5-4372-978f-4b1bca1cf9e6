package waybill_center_service

import protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"

type (
	DgType             int32
	IsDg               int32
	LocationCheckLevel int32

	CreateOrderReq struct {
		BaseInfo         *CreateOrderBaseInfo         `json:"base_info,omitempty" validate:"required,dive"`
		ForderInfo       *CreateOrderForderInfo       `json:"forder_info,omitempty" validate:"required,dive"`
		PickupInfo       *CreateOrderPickupInfo       `json:"pickup_info,omitempty" validate:"required,dive"`
		DeliverInfo      *CreateOrderDeliverInfo      `json:"deliver_info,omitempty" validate:"required,dive"`
		Skus             []*CreateOrderSKU            `json:"skus,omitempty" validate:"required,min=1,dive"`
		ReturnInfo       *CreateOrderReturnInfo       `json:"return_info,omitempty"`
		CheckoutInfo     *CreateOrderCheckOutInfo     `json:"checkout_info,omitempty"`
		OrderInfo        *CreateOrderInfo             `json:"order_info,omitempty"`
		LaneInfo         *CreateOrderLaneInfo         `json:"lane_info,omitempty"`
		LanesInfo        *CreateOrderLanesInfo        `json:"lanes_info,omitempty"`
		LaneCodeList     []string                     `json:"lane_code_list,omitempty"`
		RegionPolicyInfo *CreateOrderRegionPolicyInfo `json:"region_policy_info,omitempty"`
		SubPackageInfos  []CreateOrderSubPackageInfo  `json:"sub_package_info,omitempty"` //SPLPS-5834: if SubPackageInfo is not empty, then lfs && lcos will use SubPackageInfo to replace Skus when calling CheckProductRule
	}

	CreateOrderBaseInfo struct {
		ShippingChannelId       *string             `json:"shipping_channel_id,omitempty" validate:"required,min=1,max=32"`
		ShippingChannelName     *string             `json:"shipping_channel_name,omitempty"`
		MaskChannelId           *string             `json:"mask_channel_id,omitempty" validate:"omitempty,min=1,max=32"`
		Forderid                *string             `json:"forderid,omitempty" validate:"required,forderid"`
		Ordersn                 *string             `json:"ordersn,omitempty" validate:"required,min=1,max=32"`
		MultiOrdersn            *string             `json:"multi_ordersn,omitempty"`
		WhsId                   *string             `json:"whs_id,omitempty" validate:"required,min=0,max=16"`
		PickupTime              *uint32             `json:"pickup_time,omitempty" validate:"required"`
		Acl2                    *int32              `json:"acl2"`
		CbFlag                  *int32              `json:"cb_flag,omitempty" validate:"required,bool"`
		IsDropship              *uint8              `json:"is_dropship,omitempty"`
		IsReturn                *uint8              `json:"is_return,omitempty"`
		PickupTimeRangeId       *uint32             `json:"pickup_time_range_id,omitempty"`
		OrderCreateTime         *uint32             `json:"order_create_time,omitempty"`
		PickupFlag              *int32              `json:"pickup_flag,omitempty" validate:"omitempty,bool"`
		OrdersnList             []string            `json:"ordersn_list,omitempty" validate:"omitempty,min=1"`
		CollectionForderIds     []string            `json:"collection_forder_ids,omitempty" validate:"omitempty,min=1"`
		CheckSender             *uint32             `json:"check_sender,omitempty"`
		CheckReceiver           *uint32             `json:"check_receiver,omitempty"`
		SkipPostalCode          *uint32             `json:"skip_postal_code,omitempty"`
		SenderCheckLevel        *LocationCheckLevel `json:"sender_check_level,omitempty"`
		ReceiverCheckLevel      *LocationCheckLevel `json:"receiver_check_level,omitempty"`
		PickupClientGroupIdList []string            `json:"pickup_client_group_id_list,omitempty"`
		IsConsolidation         bool                `json:"is_consolidation,omitempty"`
		MplRosEligible          *int32              `json:"mpl_ros_eligible,omitempty"` // https://jira.shopee.io/browse/SPLPS-9822 透传给LFS
		RosOptin                *int32              `json:"ros_optin,omitempty"`
		ClientId                *string             `json:"client_id,omitempty"`

		BatchInfo *CreateOrderBatchInfo `json:"batch_info,omitempty"`
	}

	CreateOrderForderInfo struct {
		CurrencyRate            *string     `json:"currency_rate,omitempty" validate:"required,min=1,max=64"`
		PaymentMethod           *string     `json:"payment_method,omitempty" validate:"required,max=128"`
		IsSodBool               *bool       `json:"is_sod_bool"`
		CodAmount               *float64    `json:"cod_amount,omitempty" validate:"required,nonNegativeFloat"`
		ActualPaid              *float32    `json:"actual_paid,omitempty" validate:"required,nonNegativeFloat"`
		PurchaseTime            *uint32     `json:"purchase_time,omitempty" validate:"required"`
		ShopId                  *string     `json:"shop_id,omitempty" validate:"required,min=1,max=32"`
		ShopInfos               []*ShopInfo `json:"shop_infos,omitempty" validate:"omitempty,min=1,dive"`
		SubPaymentMethod        *string     `json:"sub_payment_method,omitempty" validate:"omitempty,max=128"`
		ActualPrice             *float32    `json:"actual_price,omitempty" validate:"omitempty,nonNegativeFloat"`
		TotalPrice              *float32    `json:"total_price,omitempty" validate:"omitempty,nonNegativeFloat"`
		ShippingPrice           *float32    `json:"shipping_price,omitempty" validate:"omitempty,nonNegativeFloat"`
		OriginShippingFee       *float32    `json:"origin_shipping_fee,omitempty"`
		PriceBeforeDiscount     *float32    `json:"price_before_discount,omitempty"`
		BuyerPaidShippingFee    *float32    `json:"buyer_paid_shipping_fee,omitempty"`
		Cogs                    *float32    `json:"cogs,omitempty"`
		ShopName                *string     `json:"shop_name,omitempty"`
		SellerRemark            *string     `json:"seller_remark,omitempty"`
		BuyerRemark             *string     `json:"buyer_remark,omitempty"`
		SellerRealName          *string     `json:"seller_real_name,omitempty"`
		SenderAddressId         *uint64     `json:"sender_address_id,omitempty"`
		PickupCvsAddressId      *uint64     `json:"pickup_cvs_address_id,omitempty"`
		DeliverCvsAddressId     *uint64     `json:"deliver_cvs_address_id,omitempty"`
		BranchId                *uint64     `json:"branch_id,omitempty"`
		StoreId                 *string     `json:"store_id,omitempty"`
		ShippingProof           *string     `json:"shipping_proof,omitempty"`
		ShippingTraceno         *string     `json:"shipping_traceno,omitempty" validate:"omitempty,max=40"`
		ShippingCarrier         *string     `json:"shipping_carrier,omitempty"`
		ShipByDate              *uint64     `json:"ship_by_date,omitempty"`
		PreferredDeliveryOption *int32      `json:"preferred_delivery_option,omitempty" validate:"omitempty,bool"`
		DropshipPhone           *string     `json:"dropship_phone,omitempty"`
		DropshipName            *string     `json:"dropship_name,omitempty"`
		TaxNumber               *string     `json:"tax_number,omitempty" validate:"omitempty,min=0,max=64"`
		//集成渠道和非集成渠道公用slug字段，各自有不同的含义
		Slug                *string     `json:"slug,omitempty"`
		IsShopSbs           *int32      `json:"is_shop_sbs,omitempty" validate:"omitempty,bool"`
		IsLocalWhs          *bool       `json:"is_local_whs,omitempty"`
		IsCustomsAddress    *bool       `json:"is_customs_address,omitempty"`
		SellerTaxNumber     *string     `json:"seller_tax_number,omitempty" validate:"omitempty,min=0,max=128"`
		EncryptShopName     *string     `json:"encrypt_shop_name,omitempty"`
		BuyerUploadPhoto    *string     `json:"buyer_upload_photo,omitempty"`
		ShippingConfirmTime *uint64     `json:"shipping_confirm_time,omitempty"`
		IsTaxableShop       *int32      `json:"is_taxable_shop,omitempty"`
		SellerInfo          *SellerInfo `json:"seller_info,omitempty"`
		EshopId             *string     `json:"eshop_id,omitempty"`
		IsB2C               *int32      `json:"is_b2c,omitempty"`
		IsStore             *bool       `json:"is_store,omitempty"`
		// global fulfillment indicate order is from warehouse and is a CB order
		IsGlobalFulfill      *bool                 `json:"is_global_fulfill,omitempty"`
		SellerWhsInvoiceInfo *SellerWhsInvoiceInfo `json:"seller_whs_invoice_info,omitempty"`
		HeadquarterInfo      *HeadquarterInfo      `json:"headquarter_info,omitempty"`
		HubId                int                   `json:"hub_id"`
		DeliverMode          int                   `json:"deliver_mode"`
	}

	SellerWhsInvoiceInfo struct {
		SellerWhsCnpjId                  *string `json:"seller_whs_cnpj_id,omitempty"`
		SellerWhsStateRegistrationNumber *string `json:"seller_whs_state_registration_number,omitempty"`
		SellerWhsCnpj                    *string `json:"seller_whs_cnpj,omitempty"`
		SellerWhsId                      *string `json:"seller_whs_id,omitempty"`
		IsFreightPayer                   *bool   `json:"is_freight_payer,omitempty"`
	}

	HeadquarterInfo struct {
		LocalSellerRegistrationNumber *string                       `json:"local_seller_registration_number,omitempty"`
		LocalSellerRegisteredName     *string                       `json:"local_seller_registered_name,omitempty"`
		LocalSellerTaxId              *string                       `json:"local_seller_tax_id,omitempty"`
		LocalSellerRegisteredAddress  *LocalSellerRegisteredAddress `json:"local_seller_registered_address,omitempty"`
	}

	LocalSellerRegisteredAddress struct {
		SellerRegisteredAddressName         *string `json:"seller_registered_address_name,omitempty"`
		SellerRegisteredAddressPhone        *string `json:"seller_registered_address_phone,omitempty"`
		SellerRegisteredAddressAddress      *string `json:"seller_registered_address_address,omitempty"`
		SellerRegisteredAddressZipcode      *string `json:"seller_registered_address_zipcode,omitempty"`
		SellerRegisteredAddressState        *string `json:"seller_registered_address_state,omitempty"`
		SellerRegisteredAddressCity         *string `json:"seller_registered_address_city,omitempty"`
		SellerRegisteredAddressNeighborhood *string `json:"seller_registered_address_neighborhood,omitempty"`
	}

	SellerInfo struct {
		SellerUsername          *string `json:"seller_username,omitempty" validate:"omitempty,min=0,max=1024"`
		SellerRegisteredName    *string `json:"seller_registered_name,omitempty" validate:"omitempty,min=0,max=1024"`
		SellerRegisteredPhone   *string `json:"seller_registered_phone,omitempty" validate:"omitempty,min=0,max=24"`
		SellerRegisteredAddress *string `json:"seller_registered_address,omitempty" validate:"omitempty,min=0,max=1024"`
		MaskingSellerPhone      *string `json:"masking_seller_phone,omitempty"`
		MaskingSellerAddress    *string `json:"masking_seller_address,omitempty"`
		ShippingConfirmTime     *uint64 `json:"shipping_confirm_time,omitempty"`
		IsTaxableShop           *int32  `json:"is_taxable_shop,omitempty"`
	}

	ShopInfo struct {
		ShopId     *string `json:"shop_id,omitempty" validate:"required,min=1,max=32"`
		ShopTypeId *int64  `json:"shop_type_id,omitempty"`
	}

	CreateOrderPickupInfo struct {
		PickupName         *string `json:"pickup_name,omitempty" validate:"required,min=1,max=1024"`
		PickupPhone        *string `json:"pickup_phone,omitempty" validate:"required,phone"`
		PickupAddress      *string `json:"pickup_address,omitempty" validate:"required,min=0,max=1024"`
		PickupPostalCode   *string `json:"pickup_postal_code,omitempty" validate:"required,min=0,max=16"`
		PickupCountry      *string `json:"pickup_country,omitempty" validate:"required,country"`
		PickupLongitude    *string `json:"pickup_longitude,omitempty" validate:"omitempty,latitudeLongitude"`
		PickupLatitude     *string `json:"pickup_latitude,omitempty" validate:"omitempty,latitudeLongitude"`
		PickupState        *string `json:"pickup_state,omitempty" validate:"required,min=0,max=128"`
		PickupCity         *string `json:"pickup_city,omitempty" validate:"required,min=0,max=128"`
		PickupDistrict     *string `json:"pickup_district,omitempty" validate:"required,min=0,max=128"`
		PickupStreet       *string `json:"pickup_street,omitempty" validate:"required,min=0,max=128"`
		PickupEmail        *string `json:"pickup_email,omitempty" validate:"omitempty,max=254"`
		PickupDistrictId   *int32  `json:"pickup_district_id,omitempty"`
		PickupCepGroupName *string `json:"pickup_cep_group_name,omitempty"`
	}

	CreateOrderDeliverInfo struct {
		DeliverName          *string `json:"deliver_name,omitempty" validate:"required,min=1,max=1024"`
		DeliverPhone         *string `json:"deliver_phone,omitempty" validate:"required,phone"`
		DeliverAddress       *string `json:"deliver_address,omitempty" validate:"required,min=0,max=1024"`
		DeliverPostalCode    *string `json:"deliver_postal_code,omitempty" validate:"required,min=0,max=16"`
		DeliverCountry       *string `json:"deliver_country,omitempty" validate:"required,country"`
		DeliverState         *string `json:"deliver_state,omitempty" validate:"required,min=0,max=128"`
		DeliverCity          *string `json:"deliver_city,omitempty" validate:"required,min=0,max=128"`
		DeliverDistrict      *string `json:"deliver_district,omitempty" validate:"required,min=0,max=128"`
		DeliverStreet        *string `json:"deliver_street,omitempty" validate:"required,min=0,max=128"`
		DeliverEmail         *string `json:"deliver_email,omitempty" validate:"omitempty,max=254"`
		DeliverLongitude     *string `json:"deliver_longitude,omitempty" validate:"omitempty,latitudeLongitude"`
		DeliverLatitude      *string `json:"deliver_latitude,omitempty" validate:"omitempty,latitudeLongitude"`
		DeliverDistrictId    *int32  `json:"deliver_district_id,omitempty"`
		KycName              *string `json:"kyc_name,omitempty" validate:"omitempty,min=0,max=1024"`
		KycPhone             *string `json:"kyc_phone,omitempty" validate:"omitempty,phone"`
		DeliverType          *int    `json:"deliver_type,omitempty"`
		DeliverUserId        *uint64 `json:"deliver_user_id,omitempty"`
		DeliverAddressId     *uint64 `json:"deliver_address_id,omitempty"`
		BranchName           *string `json:"branch_name,omitempty"`
		BranchRef            *string `json:"branch_ref,omitempty"`             //3pl实际branch_id，LFS面单上需要
		DeliveryAddressLabel *int32  `json:"delivery_address_label,omitempty"` //新增buyer address纬度delivery preference
		DeliveryInstructions []int   `json:"delivery_instructions,omitempty"`
		DeliverCepGroupName  *string `json:"deliver_cep_group_name,omitempty"`
	}

	CreateOrderSKU struct {
		ItemId                  *uint64 `json:"item_id,omitempty" validate:"required"`
		CategoryId              *uint32 `json:"category_id,omitempty"`
		Category                *string `json:"category,omitempty"`
		SubCategory             *string `json:"sub_category,omitempty"`
		SubSubCategory          *string `json:"sub_sub_category,omitempty"`
		CategoryDisplay         *string `json:"category_display,omitempty"`
		SubCategoryDisplay      *string `json:"sub_category_display,omitempty"`
		SubSubCategoryDisplay   *string `json:"sub_sub_category_display,omitempty"`
		GlobalCategoryId        *uint32 `json:"global_category_id,omitempty"`
		GlobalCategoryL1        *string `json:"global_category_L1,omitempty"`
		GlobalCategoryL2        *string `json:"global_category_L2,omitempty"`
		GlobalCategoryL3        *string `json:"global_category_L3,omitempty"`
		GlobalCategoryL4        *string `json:"global_category_L4,omitempty"`
		GlobalCategoryL5        *string `json:"global_category_L5,omitempty"`
		GlobalCategoryIdL1      *int64  `json:"global_category_id_L1,omitempty"`
		GlobalCategoryIdL2      *int64  `json:"global_category_id_L2,omitempty"`
		GlobalCategoryIdL3      *int64  `json:"global_category_id_L3,omitempty"`
		GlobalCategoryIdL4      *int64  `json:"global_category_id_L4,omitempty"`
		GlobalCategoryIdL5      *int64  `json:"global_category_id_L5,omitempty"`
		GlobalCategoryDisplayL1 *string `json:"global_category_display_L1,omitempty"`
		GlobalCategoryDisplayL2 *string `json:"global_category_display_L2,omitempty"`
		GlobalCategoryDisplayL3 *string `json:"global_category_display_L3,omitempty"`
		GlobalCategoryDisplayL4 *string `json:"global_category_display_L4,omitempty"`
		GlobalCategoryDisplayL5 *string `json:"global_category_display_L5,omitempty"`
		CheckedByGlobalCategory *uint32 `json:"checked_by_global_category,omitempty"`

		Sku                    *string    `json:"sku,omitempty" validate:"required,max=150"`
		Weight                 *float32   `json:"weight,omitempty" validate:"required,nonNegativeFloat"`
		Quantity               *uint32    `json:"quantity,omitempty" validate:"required"`
		SingleItemPrice        *float32   `json:"single_item_price,omitempty" validate:"required,nonNegativeFloat"`
		OrderItem              *OrderItem `json:"order_item,omitempty" validate:"required,dive"`
		ModelId                *uint64    `json:"model_id,omitempty"`
		ModelName              *string    `json:"model_name,omitempty"`
		ItemName               *string    `json:"item_name,omitempty"`
		CurrentCategoryDisplay *string    `json:"current_category_display,omitempty" validate:"omitempty,max=128"`
		WeightUnit             *string    `json:"weight_unit,omitempty"`
		ProductName            *string    `json:"product_name,omitempty" validate:"omitempty,max=1024"`
		Length                 *float32   `json:"length,omitempty" validate:"omitempty,nonNegativeFloat"`
		Width                  *float32   `json:"width,omitempty" validate:"omitempty,nonNegativeFloat"`
		Height                 *float32   `json:"height,omitempty" validate:"omitempty,nonNegativeFloat"`
		CogsNonTax             *float32   `json:"cogs_non_tax,omitempty" validate:"omitempty,nonNegativeFloat"`
		ItemSizeId             *uint8     `json:"item_size_id,omitempty"`
		OrderPrice             *float32   `json:"order_price,omitempty"`
		DeclareValueUsd        *float32   `json:"declare_value_usd,omitempty"`
		DeclareValue           *float32   `json:"declare_value,omitempty"`
		DeclareNameLocal       *string    `json:"declare_name_local,omitempty"`
		DeclareNameEn          *string    `json:"declare_name_en,omitempty"`
		DeclareNameCn          *string    `json:"declare_name_cn,omitempty"`
		HsCode                 *string    `json:"hs_code,omitempty"`
		CurrentCategory        *string    `json:"current_category,omitempty"`
		TaxInfo                []*TaxInfo `json:"tax_info,omitempty"`
		ShopName               *string    `json:"shop_name,omitempty"`
		OrderSn                *string    `json:"order_sn,omitempty"`
		OrderAmount            *float64   `json:"order_amount,omitempty"`
		OrderQuantity          *uint32    `json:"order_quantity,omitempty"`
		DgType                 *DgType    `json:"dg_type,omitempty"`
		IsDg                   *IsDg      `json:"is_dg"`
		SerialNumber           []string   `json:"serial_number,omitempty"`
		SellerListingPhoto     *string    `json:"seller_listing_photo,omitempty"`
		Brand                  *string    `json:"brand,omitempty"`
		ProductDesc            *string    `json:"product_desc,omitempty"`
		NetAmount              *float32   `json:"net_amount,omitempty"`
	}

	OrderItem struct {
		ItemSnapshot *ItemSnapshot `json:"item_snapshot,omitempty" validate:"required,dive"`
	}

	ItemSnapshot struct {
		Item  *ItemSnapshotItem  `json:"item,omitempty" validate:"required,dive"`
		Model *ItemSnapshotModel `json:"model,omitempty"`
	}

	ItemSnapshotItem struct {
		ExtInfo    *ItemSnapshotExtInfo `json:"ext_info,omitempty" validate:"required,dive"`
		Sku        *string              `json:"sku,omitempty"`
		Country    *string              `json:"country,omitempty"`
		CategoryId *int64               `json:"category_id,omitempty"`
		Name       *string              `json:"name,omitempty"`
	}

	ItemSnapshotExtInfo struct {
		LogisticsInfo *string `json:"logistics_info,omitempty" validate:"required"`
	}

	ItemSnapshotModel struct {
		Sku  *string `json:"sku,omitempty"`
		Name *string `json:"name,omitempty"`
	}

	TaxInfo struct {
		TaxType   *string  `json:"tax_type,omitempty"`
		TaxRate   *int32   `json:"tax_rate,omitempty"`
		TaxAmount *float32 `json:"tax_amount,omitempty"`
		LvgTag    *int     `json:"lvg_tag"`
	}

	CreateOrderReturnInfo struct {
		ReturnName         *string `json:"return_name,omitempty" validate:"omitempty,min=1,max=1024"`
		ReturnPhone        *string `json:"return_phone,omitempty" validate:"omitempty,phone"`
		ReturnEmail        *string `json:"return_email,omitempty" validate:"omitempty,min=0,max=254"`
		ReturnAddress      *string `json:"return_address,omitempty" validate:"omitempty,min=0,max=1024"`
		ReturnPostalCode   *string `json:"return_postal_code,omitempty" validate:"omitempty,min=0,max=16"`
		ReturnCountry      *string `json:"return_country,omitempty" validate:"omitempty,country"`
		ReturnLongitude    *string `json:"return_longitude,omitempty" validate:"omitempty,latitudeLongitude"`
		ReturnLatitude     *string `json:"return_latitude,omitempty" validate:"omitempty,latitudeLongitude"`
		ReturnState        *string `json:"return_state,omitempty" validate:"omitempty,min=0,max=128"`
		ReturnCity         *string `json:"return_city,omitempty" validate:"omitempty,min=0,max=128"`
		ReturnDistrict     *string `json:"return_district,omitempty" validate:"omitempty,min=0,max=128"`
		ReturnStreet       *string `json:"return_street,omitempty" validate:"omitempty,min=0,max=128"`
		BuyerRealPhone     *string `json:"buyer_real_phone,omitempty" validate:"omitempty,phone"`
		BuyerRealName      *string `json:"buyer_real_name,omitempty" validate:"omitempty,min=1,max=1024"`
		ReturnReason       *int32  `json:"return_reason,omitempty"`
		ForwardOrderType   *string `json:"forward_order_type,omitempty"` //SPLPS-8850 CB/WH/MP
		ForwardIsB2cSeller *string `json:"forward_is_b2c_seller,omitempty"`
	}

	CreateOrderCheckOutInfo struct {
		CheckoutSellerAddress         *string `json:"checkout_seller_address,omitempty" validate:"required,min=0,max=1024"`
		CheckoutSellerAddressState    *string `json:"checkout_seller_address_state,omitempty" validate:"required,min=0,max=128"`
		CheckoutSellerAddressCity     *string `json:"checkout_seller_address_city,omitempty" validate:"required,min=0,max=128"`
		CheckoutSellerAddressDistrict *string `json:"checkout_seller_address_district,omitempty" validate:"required,min=0,max=128"`
		CheckoutSellerAddressStreet   *string `json:"checkout_seller_address_street,omitempty" validate:"required,min=0,max=128"`
	}

	CreateOrderInfo struct {
		OrderId        *uint64       `json:"order_id,omitempty" validate:"required"`
		Seller         *Seller       `json:"seller,omitempty" validate:"required"`
		ExtInfo        *OrderExtInfo `json:"ext_info,omitempty" validate:"required,dive"`
		ForwardOrderSn *string       `json:"forward_order_sn,omitempty"`
		EdtInfo        *EdtInfo      `json:"edt_info"`
	}

	EdtInfo struct {
		AveragePreparationDate          float32 `json:"apt"`
		PreparationTimeHolidayExtension int     `json:"he_pt"`
		CdtMin                          float32 `json:"cdt_min"`
		CdtMax                          float32 `json:"cdt_max"`
		CdtHolidayExtension             int     `json:"he_cdt"`
		EdtMinDate                      string  `json:"edt_min_dt"`
		EdtMaxDate                      string  `json:"edt_max_dt"`
	}

	Seller struct {
		Username    *string `json:"username,omitempty"`
		SellerPhone *string `json:"seller_phone,omitempty" validate:"omitempty,phone"`
	}

	OrderExtInfo struct {
		ShopSnapshot  *OrderExtInfoShopSnapshot `json:"shop_snapshot,omitempty" validate:"required,dive"`
		LogisticsInfo *OrderLogisticsInfo       `json:"logistics_info,omitempty" validate:"required,dive"`
	}

	OrderLogisticsInfo struct {
		LogisticsFlag *uint64 `json:"logistics_flag,omitempty" validate:"required"`
	}

	OrderExtInfoShopSnapshot struct {
		Shop *Shop `json:"shop,omitempty"`
	}

	Shop struct {
		ExtInfo *ShopShopExtInfo `json:"ext_info,omitempty"`
		ShopId  *int32           `json:"shop_id,omitempty"`
	}

	ShopShopExtInfo struct {
		LogisticsInfo *string `json:"logistics_info,omitempty"`
	}

	CreateOrderLaneInfo struct {
		LaneCode        *string                 `json:"lane_code,omitempty" validate:"required"`
		DgFlag          *int32                  `json:"dg_flag,omitempty" validate:"required"`
		ActualPointList []*protocol.ActualPoint `json:"actual_point_list,omitempty"`
		LaneCodeGroup   []string                `json:"lane_code_group,omitempty"`
	}
	// CreateOrderLanesInfo For multi product only
	CreateOrderLanesInfo struct {
		DgFlag *int32                     `json:"dg_flag,omitempty" validate:"required"`
		Items  []CreateOrderLanesInfoItem `json:"items,omitempty"`
	}
	ServiceableLaneInfo struct {
		LaneCode        *string                 `json:"lane_code,omitempty" validate:"required"`
		LaneCodeGroup   []string                `json:"lane_code_group,omitempty"`
		ActualPointList []*protocol.ActualPoint `json:"actual_point_list,omitempty"`
	}
	CreateOrderLanesInfoItem struct {
		LaneCode        *string                 `json:"lane_code,omitempty" validate:"required"`
		OrderSequence   int                     `json:"order_sequence,omitempty" validate:"required"` // 下单顺序，非必传，最小值为1
		Sequence        int                     `json:"sequence,omitempty" validate:"required"`
		ProductId       string                  `json:"product_id,omitempty" validate:"required"`
		DgGroup         string                  `json:"dg_group,omitempty" validate:"required"` // product 为ILH 时必传
		IsCritical      int                     `json:"is_critical,omitempty"`                  // 是否是关键节点 0: false, 1: true
		ActualPointList []*protocol.ActualPoint `json:"actual_point_list,omitempty"`
	}

	ActualPoint struct {
		SiteId      string `json:"site_id"`       // 虚拟点 ID
		SiteSubType int    `json:"site_sub_type"` // 虚拟点sub_type
		PointId     string `json:"point_id"`      // 实际点ID
		PointType   int    `json:"point_type"`    // in:1 out:2, all:3
	}

	CreateOrderRegionPolicyInfo struct {
		BrInvoiceNumber                       *uint32  `json:"br_invoice_number,omitempty"`                       //deprecate by SPLFS-5132
		BrInvoiceSerialNumber                 *uint16  `json:"br_invoice_serial_number"`                          //deprecate by SPLFS-5132
		BrInvoiceTaxCodeOperationsServices    *uint16  `json:"br_invoice_tax_code_operations_services,omitempty"` //deprecate by SPLFS-5132
		BrInvoiceAccessKey                    *string  `json:"br_invoice_access_key,omitempty" validate:"omitempty,min=0,max=64"`
		BrInvoiceIssueTime                    *uint32  `json:"br_invoice_issue_time,omitempty"`
		BrInvoiceTotalValue                   *float64 `json:"br_invoice_total_value,omitempty"`
		BrInvoiceProductsTotalValue           *float64 `json:"br_invoice_products_total_value,omitempty"`
		BrStateRegisteredNo                   *string  `json:"br_state_registered_no,omitempty" validate:"omitempty,min=0,max=128"`
		BrLegalEntityName                     *string  `json:"br_legal_entity_name,omitempty"`
		BrInvoiceNumberStr                    *string  `json:"br_invoice_number_str,omitempty"`
		BrInvoiceSerialNumberStr              *string  `json:"br_invoice_serial_number_str,omitempty"`
		BrInvoiceTaxCodeOperationsServicesStr *string  `json:"br_invoice_tax_code_operations_services_str,omitempty"`
		BrImportTax                           *string  `json:"br_import_tax,omitempty"`
		BrIcmsTax                             *string  `json:"br_icms_tax,omitempty"`
		BrTaxMethod                           *uint8   `json:"br_tax_method,omitempty"`

		InInvoiceNumber    *string  `json:"in_invoice_number,omitempty"`
		InInvoiceIssueTime *uint32  `json:"in_invoice_issue_time,omitempty"`
		InInvoiceAmount    *float64 `json:"in_invoice_amount,omitempty"`
		InIgstTaxAmount    *float64 `json:"in_igst_tax_amount,omitempty"`
		InCgstTaxAmount    *float64 `json:"in_cgst_tax_amount,omitempty"`
		InSgstTaxAmount    *float64 `json:"in_sgst_tax_amount,omitempty"`
	}

	CreateOrderBatchInfo struct {
		BatchId   *string `json:"batch_id"`
		BatchSize *int    `json:"batch_size"`
	}

	//SPLPS-5834: add new struct for sub packages info
	CreateOrderSubPackageInfo struct {
		Length float64 `json:"length"` //unit:cm
		Width  float64 `json:"width"`  //unit:cm
		Height float64 `json:"height"` //unit:cm
		Weight float64 `json:"weight"` //unit:g
	}
)

func (c *CreateOrderReq) GetCreateOrderInfo() *CreateOrderInfo {
	if c != nil && c.OrderInfo != nil {
		return c.OrderInfo
	}
	return nil
}

func (c *CreateOrderInfo) GetOrderExtInfo() *EdtInfo {
	if c != nil && c.EdtInfo != nil {
		return c.EdtInfo
	}
	return nil
}

func (e *EdtInfo) GetEdtMaxDate() string {
	if e != nil {
		return e.EdtMaxDate
	}
	return ""
}
