package waybill_center_service

import (
	"fmt"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
)

type PushEDDInfo struct {
	SlsTN string `json:"sls_tn"`
	EDD   int64  `json:"edd"`
}

type PushEDDResponse struct {
	Retcode int32  `json:"retcode"`
	Message string `json:"message"`
}

type WaybillSequence struct {
	LineSiteID      string `json:"line_site_id"`
	ResourceSubType int    `json:"resource_sub_type"`
	Sequence        int    `json:"sequence"`
	ThreePlTn       string `json:"three_pl_tn"`
}

type WaybillInfoRequest struct {
	SlsTNList []string `json:"sls_tn_list"`
}

// SPLN-24104 for lane code info
type LaneCodeInfo struct {
	SlsTN               string             `json:"sls_tn"`
	Retcode             int                `json:"retcode"`
	Message             string             `json:"message"`
	LmTN                string             `json:"lm_tn"`
	ProductID           string             `json:"product_id"`
	SellerAddress       string             `json:"seller_address"`
	BuyerAddress        string             `json:"buyer_address"`
	LaneCode            string             `json:"lane_code"`
	CurrentSequence     int                `json:"current_sequence"`
	WaybillSequenceList []*WaybillSequence `json:"waybill_sequence_list"`
	FCode               string             `json:"f_code"`
	IsReturn            uint8              `json:"is_return"`

	// SPLN-30795
	DeliverUserID uint64 `json:"deliver_user_id"`
	PickupType    uint32 `json:"pickup_type"`
}

func (w *LaneCodeInfo) IsSuccess() bool {
	return w != nil && int32(w.Retcode) == lcos_error.SuccessCode
}

func (w *LaneCodeInfo) IsReturnOrder() bool {
	return w != nil && w.IsReturn == constant.TRUE
}

func (w *LaneCodeInfo) CheckValid(firstDeliverAttempt, rts, terminal []string) (string, bool) {
	if utils.InStringSlice(w.FCode, rts) {
		return "SLSTN is RTS order", false
	}
	if utils.InStringSlice(w.FCode, terminal) {
		return "SLSTN is at terminal status", false
	}
	if utils.InStringSlice(w.FCode, firstDeliverAttempt) {
		return "SLSTN has already made first delivery attempt", false
	}
	if w.IsReturn == constant.TRUE {
		return "sls tn is return order", false
	}
	return "", true
}

func (w *LaneCodeInfo) GetResourceInfo() string {
	for _, singleSequence := range w.WaybillSequenceList {
		if singleSequence.Sequence == w.CurrentSequence {
			if strings.HasPrefix(singleSequence.LineSiteID, "S") {
				return fmt.Sprintf("%s(%s)", singleSequence.LineSiteID, "site")
			}
			return singleSequence.LineSiteID
		}
	}
	return ""
}

// GetFollowingLineIDList
// get all lines that are going or will go
func (w *LaneCodeInfo) GetFollowingLineIDList() []*cdt_calculation.LineInfo {
	var results []*cdt_calculation.LineInfo
	for _, singleSequence := range w.WaybillSequenceList {
		if singleSequence.Sequence >= w.CurrentSequence && (strings.HasPrefix(singleSequence.LineSiteID, "L") || strings.HasPrefix(singleSequence.LineSiteID, "FM")) { // SPLN-29225 Need to add line FM01/FM02
			results = append(results, &cdt_calculation.LineInfo{
				LineID:  singleSequence.LineSiteID,
				SubType: uint32(singleSequence.ResourceSubType),
			})
		}
	}
	return results
}

func (w *LaneCodeInfo) CheckLineWillGoingOnLines(lineIDList []string) bool {
	for _, sequence := range w.WaybillSequenceList {
		if sequence.Sequence < w.CurrentSequence {
			continue
		}

		if utils.InStringSlice(sequence.LineSiteID, lineIDList) {
			return true
		}
	}
	return false
}

type LaneInfoResponse struct {
	Retcode int32  `json:"retcode"`
	Message string `json:"message"`
	Data    struct {
		List []*LaneCodeInfo `json:"list"`
	} `json:"data"`
}

type ApiLevel uint8

const (
	DBAndHbase ApiLevel = 1
	HbaseOnly  ApiLevel = 2
	DBOnly     ApiLevel = 3
)

type (
	BatchGetSloInfoWithActionRequest struct {
		List []*GetSloInfoWithActionRequest `json:"list"`
	}

	GetSloInfoWithActionRequest struct {
		SloTn      string   `json:"slo_tn"`
		ResourceId string   `json:"resource_id"`
		ActionType string   `json:"action_type"`
		SloId      uint64   `json:"slo_id"`
		ThreePlTn  string   `json:"three_pl_tn"`
		ApiLevel   ApiLevel `json:"api_level"`
		ForderId   string   `json:"forder_id"`
	}

	GetSloInfoWithActionResponse struct {
		Retcode int32  `json:"retcode"`
		Message string `json:"message"`
		Data    struct {
			List []*SloInfo `json:"list"`
		} `json:"data"`
	}

	SloInfo struct {
		Retcode   int32      `json:"retcode"`
		Message   string     `json:"message"`
		OrderInfo *OrderInfo `json:"order_info"`
		OrderData *OrderData `json:"order_data"`
	}

	OrderInfo struct {
		OriginForderId    string `json:"origin_forder_id"`
		ActualFulfillTime int64  `json:"actual_fulfill_time"`
		ClientId          int64  `json:"client_id"`
		ForderId          string `json:"forder_id"`
		SloId             int64  `json:"slo_id"`
		SloTn             string `json:"slo_tn"`
	}

	OrderData struct {
		PurchaseTime int64  `json:"purchase_time"`
		DeliverState string `json:"deliver_state"`
		DeliverCity  string `json:"deliver_city"`
		PickupCity   string `json:"pickup_city"`
	}

	ActionInfo struct {
		ResourceType     int32  `json:"resource_type"`
		ResourceSubType  int32  `json:"resource_sub_type"`
		ResourceMainType int32  `json:"resource_main_type"`
		ResourceTn       string `json:"resource_tn"`
		ResourceId       string `json:"resource_id"`
		ThreePlTn        string `json:"three_pl_tn"`
	}

	ParcelLibraryQuery struct {
		UniqueId          string `json:"unique_id"`
		SkusCombinationId uint64 `json:"skus_combination_id"`
		Timestamp         uint32 `json:"timestamp"`
	}
)
