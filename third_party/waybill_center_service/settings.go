package waybill_center_service

import "git.garena.com/shopee/bg-logistics/go/chassis"

const (
	host           = "https://waybillcenter.ssc%s.shopee.%s"
	PUSHEDDAPI     = "/api/edd/save"
	GetLaneCodeAPI = "/api/slo/batch_get_lane_info"

	GetSloInfoWithActionAPI = "/api/standard_api/get_slo_info_with_action"
)

const (
	// WBC service
	StaticDataServiceSchemaID = "wbc_protocol.StaticDataService"
	ParcelDataServiceSchemaID = "wbc_protocol.ParcelDataService"
	// WBC endpoint
	QueryStaticDataOperationID       = "QueryStaticData"
	BatchQueryStaticDataID           = "BatchQueryStaticData"
	QueryParcelDataByTimeOperationID = "QueryParcelDataByTime"
)

var httpClient *chassis.RestInvoker

func init() {
	httpClient = chassis.NewRestInvoker()
}
