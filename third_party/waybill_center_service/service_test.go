package waybill_center_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
	"git.garena.com/shopee/platform/service-governance/viewercontext/attr"
	jsoniter "github.com/json-iterator/go"
	"os"
	"path"
	"reflect"
	"testing"
)

func TestWaybillCenterService_PushEDD(t *testing.T) {
	t.Skip()

	_ = os.Setenv("env", "test")

	type fields struct {
		Region  string
		Account string
		Secret  string
		Timeout int
	}
	type args struct {
		ctx utils.LCOSContext
		edd *PushEDDInfo
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		{
			name: "test waybill center service",
			fields: fields{
				Region:  "VN",
				Account: "LCOS",
				Secret:  "faedwhAe322423$%#~Aw#4s",
				Timeout: 5,
			},
			args: args{
				ctx: utils.NewCommonCtx(context.WithValue(context.Background(), "logid", "**********")),
				edd: &PushEDDInfo{
					SlsTN: "VN12121212",
					EDD:   **********,
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WaybillCenterService{
				Region:  tt.fields.Region,
				Account: tt.fields.Account,
				Secret:  tt.fields.Secret,
				Timeout: tt.fields.Timeout,
			}
			if got := w.PushEDD(tt.args.ctx, tt.args.edd); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PushEDD() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWaybillCenterService_BatchGetLaneCodeInfo(t *testing.T) {
	//t.Skip()

	_ = os.Setenv("env", "test")

	_ = os.Setenv("PFB_NAME", "PFB_NAME=pfb-dms-dev-spln-26162-public")
	ctxWithPfb, _ := viewercontext.Start(context.Background(), attr.WithPFB("pfb-dms-dev-spln-26162-public"))
	ctxWithPfb = context.WithValue(ctxWithPfb, "logid", "test1111111")
	ctx := utils.NewCommonCtx(ctxWithPfb)

	type fields struct {
		Region  string
		Account string
		Secret  string
		Timeout int
	}
	type args struct {
		ctx       utils.LCOSContext
		slsTNList []string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[string]*LaneCodeInfo
		want1  *lcos_error.LCOSError
	}{
		{
			name: "test get lane code",
			fields: fields{
				Region:  "BR",
				Account: "LCOS",
				Secret:  "faedwhAe322423$%#~Aw#4s",
				Timeout: 5,
			},
			args: args{
				ctx:       ctx,
				slsTNList: []string{"****************"},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WaybillCenterService{
				Region:  tt.fields.Region,
				Account: tt.fields.Account,
				Secret:  tt.fields.Secret,
				Timeout: tt.fields.Timeout,
			}
			got, got1 := w.BatchGetLaneCodeInfo(tt.args.ctx, tt.args.slsTNList)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("BatchGetLaneCodeInfo() got1 = %v, want %v", got1, tt.want1)
			}
			gotStr, _ := jsoniter.MarshalToString(got)
			t.Logf(gotStr)
		})
	}
}

func TestWayBillCenterStaticService_GetCdtByForderId(t *testing.T) {
	ctx, _ := viewercontext.Start(context.Background(), attr.WithPFB("pfb-spln-26145"))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	_ = os.Setenv("ENV", "test")

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}
	_, _ = config.InitConfig(ctx)

	svc := NewWayBillCenterStaticService("sg", 3)
	res, err := svc.GetCdtByForderId(utils.NewCommonCtx(ctx), 4955471786374146276) // 4955471786374146276, 8955471786374146276
	if err != nil {
		fmt.Println(err)
	} else {
		fmt.Println(res)
	}
}

func TestWaybillCenterService_GetSloInfoWithAction(t *testing.T) {
	ctx := utils.NewCommonCtx(context.Background())

	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	_ = os.Setenv("ENV", "test")

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}
	_, _ = config.InitConfig(ctx)

	type args struct {
		ctx utils.LCOSContext
		req []*GetSloInfoWithActionRequest
	}
	tests := []struct {
		name  string
		args  args
		want  []*SloInfo
		want1 *lcos_error.LCOSError
	}{
		{"case1", args{ctx: ctx, req: []*GetSloInfoWithActionRequest{{ForderId: "5552949015453312279", ApiLevel: DBAndHbase}}}, nil, nil},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WaybillCenterService{
				Region:  "ID",
				Account: "LCOS",
				Secret:  "faedwhAe322423$%#~Aw#4s",
				Timeout: 3,
			}
			got, got1 := w.GetSloInfoWithAction(tt.args.ctx, tt.args.req)
			if got1 != nil {
				fmt.Println("err:", got1.Msg)
				return
			}
			fmt.Println(utils.MarshToStringWithoutError(got))
		})
	}
}
