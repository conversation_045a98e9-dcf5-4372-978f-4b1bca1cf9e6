package lts_service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	jsoniter "github.com/json-iterator/go"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
)

func Test_ltsService_RetrieveTracking(t *testing.T) {
	//t.Skip()
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	startup.InitSSCEnv()

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	type args struct {
		ctx     utils.LCOSContext
		slsTn   string
		country string
	}
	tests := []struct {
		name  string
		args  args
		want1 *lcos_error.LCOSError
	}{
		{
			name: "test retrieve history tracking",
			args: args{
				ctx:     utils.NewCommonCtx(context.Background()),
				slsTn:   "ID2256574583400T",
				country: "ID",
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l, got3 := NewLTSService(tt.args.country)
			if !reflect.DeepEqual(got3, tt.want1) {
				t.Errorf("NewLtsService() got1 = %v, want %v", got3, tt.want1)
			}
			got, got1 := l.RetrieveTracking(tt.args.ctx, tt.args.slsTn, tt.args.country)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("RetrieveTracking() got1 = %v, want %v", got1, tt.want1)
			}
			infoStr, _ := jsoniter.MarshalToString(got)
			t.Logf("%s", infoStr)
		})
	}
}
