package lts_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/common"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	pb2 "git.garena.com/shopee/bg-logistics/logistics/proto-center/v10/logistics-tracking-system/go"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/net/context"
	"strings"
	"sync"
)

var invoker = chassis.NewRPCInvoker()
var agoConfig *config.AgoClient
var once sync.Once

type ltsService struct {
	serviceName string
}

// for lts service, need to get access to apollo to get region map
func initAgoConfig() {
	once.Do(func() {
		ctx := context.Background()
		agoConfig, _ = config.InitApollo(ctx, LtsAppID, LtsCluster, LtsNamespace, config.GetLtsApolloAccessSecret(ctx))
	})
}

func NewLTSService(region string) (*ltsService, *lcos_error.LCOSError) {
	ltsServiceName := LTSServiceName
	// TODO this needs to be added when lts deploys livetest env
	//if cf.GetEnv() == cf.LIVE && strings.ToUpper("grpclivetest") == cf.GetModuleName() {
	//	ltsServiceName = "lcos-grpclivetest"
	//}
	initAgoConfig()

	// get region map
	if agoConfig == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "cannot init ago client for lts service")
	}
	key := fmt.Sprintf("lts_router.LTSTrackingGrpc.%s.TargetCID", strings.ToUpper(region)) // cid in upper
	mappedRegion := agoConfig.Client.GetConfig(LtsNamespace).GetValue(key)
	serviceName := utils.GenKey("-", ltsServiceName, strings.ToLower(mappedRegion))
	return &ltsService{serviceName: serviceName}, nil
}

func (l *ltsService) RetrieveTracking(ctx utils.LCOSContext, slsTn string, country string) ([]*TrackingData, *lcos_error.LCOSError) {

	reqCtx := common.NewOutgoingContextWithHeader(ctx, utils.MapToStringSlice(map[string]string{constant.RequestMetadataRegionKey: country})...)

	req := &pb2.RetrieveTrackingReq{
		Header: genLtsGrpcReqHeader(ctx),
		SlsTn:  utils.NewString(slsTn),
	}
	resp := &pb2.RetrieveTrackingResp{}
	if err := invoker.Invoke(reqCtx, l.serviceName, RetrieveTrackingSchemaID, RetrieveTrackingOperationID, req, resp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(reqCtx, "requesting lts error|schema_id=%s|api=%s|req=[sls_tn=%s, region=%s]|error=%v", RetrieveTrackingSchemaID, RetrieveTrackingOperationID, slsTn, country, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if resp.GetHeader() == nil {
		logger.CtxLogErrorf(reqCtx, "requesting lts error|schema_id=%s|api=%s|req=[sls_tn=%s, region=%s]|error=%v", RetrieveTrackingSchemaID, RetrieveTrackingOperationID, slsTn, country, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if resp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(reqCtx, "requesting lts error|schema_id=%s|api=%s|req=[sls_tn=%s, region=%s]|retcode=%d|error=%v", RetrieveTrackingSchemaID, RetrieveTrackingOperationID, slsTn, country, resp.GetHeader().GetRetcode(), resp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.GetHeader().GetMessage())
	}

	rspStr, _ := jsoniter.MarshalToString(resp)
	logger.CtxLogInfof(reqCtx, "requesting lts success|schema_id=%s|api=%s|req=[sls_tn=%s, region=%s]|req=[%s]", RetrieveTrackingSchemaID, RetrieveTrackingOperationID, slsTn, country, rspStr)

	var results []*TrackingData
	for _, singleResult := range resp.GetData().GetRecords() {
		results = append(results, &TrackingData{
			TrackingCode:  singleResult.GetTrackingCode(),
			TrackingName:  singleResult.GetTrackingName(),
			Description:   singleResult.GetDescription(),
			DisplayFlag:   singleResult.GetDisplayFlag(),
			ActualTime:    singleResult.GetActualTime(),
			Operator:      singleResult.GetOperator(),
			OperatorPhone: singleResult.GetOperatorPhone(),
			ReasonCode:    singleResult.GetReasonCode(),
			ReasonDesc:    singleResult.GetReasonDesc(),
			Epod:          singleResult.GetEpod(),
			PinCode:       singleResult.GetPinCode(),
		})
	}
	return results, nil
}
