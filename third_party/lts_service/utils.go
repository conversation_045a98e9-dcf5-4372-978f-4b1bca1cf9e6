package lts_service

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	pb2 "git.garena.com/shopee/bg-logistics/logistics/proto-center/v10/logistics-tracking-system/go"
	uuid2 "github.com/satori/go.uuid"
)

func genLtsGrpcReqHeader(ctx utils.LCOSContext) *pb2.ReqHeader {
	var requestID string
	if value := ctx.Value(constant.RequestIdKey); value != nil {
		if id, ok := value.(string); ok {
			requestID = id
		}
	}
	if requestID == "" {
		requestID = uuid2.NewV4().String()
	}

	callerIp := utils.GetLocalIp()
	timeStamp := utils.GetTimestamp(ctx)
	return &pb2.ReqHeader{
		RequestId: &requestID,
		Account:   utils.NewString(""),
		Token:     utils.NewString(""),
		Timestamp: &timeStamp,
		CallerIp:  callerIp,
	}
}
