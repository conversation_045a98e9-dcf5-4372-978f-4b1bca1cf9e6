package pis_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/region_tools"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	pisPb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v11/partner-integration-system/go"
	jsoniter "github.com/json-iterator/go"
	uuid "github.com/satori/go.uuid"
	"golang.org/x/net/context"
	"strings"
	"time"
)

var (
	rpcInvoker  *chassis.RPCInvoker
	restInvoker *chassis.RestInvoker
)

type PISService struct {
	rpcInvoker  *chassis.RPCInvoker
	restInvoker *chassis.RestInvoker
	serviceName string
	region      string
	httpHost    string
}

func NewPISService(ctx context.Context, region string) *PISService {
	if rpcInvoker == nil {
		rpcInvoker = chassis.NewRPCInvoker()
	}
	if restInvoker == nil {
		restInvoker = chassis.NewRestInvoker()
	}

	// 生成service name，由于现在pis分大区部署，不同的region有可能请求到相同的站点上
	serviceRegion := region
	if _, ok := config.GetConf(ctx).PISService.RegionMap[strings.ToUpper(region)]; ok {
		serviceRegion = strings.ToUpper(config.GetConf(ctx).PISService.RegionMap[strings.ToUpper(region)])
	}

	return &PISService{
		rpcInvoker:  rpcInvoker,
		restInvoker: restInvoker,
		serviceName: fmt.Sprintf("sls-partnerintegrationservice-%s", strings.ToLower(serviceRegion)),
		region:      region,
		httpHost:    getPisHttpHost(ctx, serviceRegion),
	}
}

func getPisHttpHost(ctx context.Context, region string) string {
	env := utils.GetEnv(ctx)
	if env == config.LIVE {
		return fmt.Sprintf("https://partner-integration-service.ssc.shopee.%s", region_tools.GetCountrySuffix(region))
	}

	return fmt.Sprintf("https://partner-integration-service.ssc.%s.shopee.%s", strings.ToLower(env), region_tools.GetCountrySuffix(region))
}

//
// SyncBranch
//  @Description: 调用pis接口获取并且同步branch
//  @param ctx
//  @param requestID        上下文request id
//  @param resourceID       resource id列表
//  @param region           region
//  @return *lcos_error.LCOSError   只关注返回值是否为0，非0即报错
//
func (p *PISService) SyncBranch(ctx utils.LCOSContext, requestID string, resourceID int) *lcos_error.LCOSError {
	// 生成header
	requestHeader := genReqHeader(ctx, requestID)

	businessData := []struct {
		ResourceID int    `json:"resource_id"`
		Region     string `json:"region"`
	}{{
		ResourceID: resourceID,
		Region:     p.region,
	}}

	businessDataString, _ := jsoniter.MarshalToString(businessData)

	distributionProcessId := config.GetConf(ctx).PISService.DistributionProcessID
	distributionId := config.GetConf(ctx).PISService.DistributionID
	asyncMsgType := config.GetConf(ctx).PISService.AsyncMsgType

	syncBranchRequest := &pisPb.DistributionRequest{
		ReqHeader:             requestHeader,
		DistributionProcessId: utils.NewString(distributionProcessId),
		DistributionId:        utils.NewString(distributionId),
		ResourceId:            utils.NewUint32(uint32(resourceID)),
		AsyncMsgType:          utils.NewUint32(uint32(asyncMsgType)),
		BusinessData:          &businessDataString,
	}

	syncBranchResponse := &pisPb.DistributionResponse{}

	pisContext := NewDistributionCtx(ctx, map[string]string{
		constant.RequestMetadataRegionKey: strings.ToUpper(p.region),
	})

	if config.GetMutableConf(ctx).PISBranchConfig.BranchUseHttpCallPis {
		var (
			timeout = config.GetMutableConf(ctx).PISBranchConfig.GetSyncBranchTimeout()
			url     = fmt.Sprintf("%s/%s/%s", p.httpHost, SchemaID, SyncBranchApi)
			headers = map[string]string{constant.HttpRequestMetadataRegionKey: strings.ToUpper(p.region)}
		)
		if err := http.SendPostHttpRequest(ctx, p.restInvoker, timeout, url, syncBranchRequest, syncBranchResponse, headers); err != nil {
			return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Msg)
		}
	} else {
		if err := p.rpcInvoker.Invoke(pisContext, p.serviceName, SchemaID, SyncBranchApi, syncBranchRequest, syncBranchResponse, chassis.WithProtocol("grpc")); err != nil {
			logger.CtxLogErrorf(ctx, "request pis error|schema_id=%s|api=%s|resource_id=%d|error=%s", SchemaID, SyncBranchApi, resourceID, err.Error())
			return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
		}
	}

	if syncBranchResponse.GetRespHeader().GetRetcode() != lcos_error.SuccessCode {
		logger.CtxLogErrorf(ctx, "request pis error|schema_id=%s|api=%s|resource_id=%d|error_code=%d|error=%s", SchemaID, SyncBranchApi, resourceID, syncBranchResponse.GetRespHeader().GetRetcode(), syncBranchResponse.GetRespHeader().GetMessage())
		return lcos_error.NewLCOSError(syncBranchResponse.GetRespHeader().GetRetcode(), syncBranchResponse.GetRespHeader().GetMessage())
	}
	logger.CtxLogInfof(ctx, "successfully call sync branch api from pis side|schema_id=%s|api=%s|resource_id=%d|region=%s", SchemaID, SyncBranchApi, resourceID, p.region)
	return nil
}

func (p *PISService) SendPisSyncCommand(ctx utils.LCOSContext, supplyTypes []uint32) *lcos_error.LCOSError {
	// transfer supply type to resource id
	for _, supplyType := range supplyTypes {
		resourceID, err := config.GetResourceIDBySupplyType(ctx, int(supplyType), p.region)
		if err != nil {
			logger.CtxLogErrorf(ctx, err.Error())
		}
		lcosErr := p.SyncBranch(ctx, ctx.GetRequestId(), int(resourceID))
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, lcosErr.Msg)
		}
	}
	return nil
}

//
// GetQuotation
// @Description: 调用pis接口获取CDT信息(BR市场调用)
// @other: 目前只有一个渠道调用这个接口,因此这个接口没有传递resourceID参数，而是直接将这个resource写成了一个常量
func (p *PISService) GetQuotation(ctx utils.LCOSContext, requestID string, req *GetQuotationReq) (*GetQuotationReply, *lcos_error.LCOSError) {
	if len(requestID) == 0 {
		requestID = uuid.NewV4().String()
	}
	requestHeader := genReqHeader(ctx, requestID)

	//set timeout
	timeout := cf.GetConf(ctx).PISService.GetGetQuotationTimeout()
	pisContext, cancel := NewDistributionCtxWithTimeout(ctx, time.Millisecond*time.Duration(timeout), map[string]string{
		GrpcMetaKeyRegion: strings.ToUpper(p.region),
	})
	defer cancel()

	businessDataString, _ := jsoniter.MarshalToString(req)
	GetQuotationRequest := &pisPb.DistributionRequest{
		ReqHeader:             requestHeader,
		ResourceId:            utils.NewUint32(GetQuotationDistributionResourceID), //先使用常量，最好做一个product和resource的映射
		BusinessData:          utils.NewString(businessDataString),
		DistributionId:        utils.NewString(GetQuotationDistributionID),
		DistributionProcessId: utils.NewString(GetQuotationDistributionProcessID),
	}
	getQuotationResponse := &pisPb.DistributionResponse{}
	err := p.rpcInvoker.Invoke(pisContext, p.serviceName, GetQuotationSchemaID, GetQuotationApi, GetQuotationRequest, getQuotationResponse, chassis.WithProtocol("grpc"))
	if err != nil {
		logger.CtxLogErrorf(ctx, "requesting pis error|schema_id=%s|api=%s|req=[%s]|error=%s", GetQuotationSchemaID, GetQuotationApi, businessDataString, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if getQuotationResponse.GetRespHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting pis error|schema_id=%s|api=%s|req=[%s]|error=%s", GetQuotationSchemaID, GetQuotationApi, businessDataString, getQuotationResponse.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(getQuotationResponse.GetRespHeader().GetRetcode(), getQuotationResponse.GetRespHeader().GetMessage())
	}

	logger.CtxLogInfof(ctx, "requesting pis success|schema_id=%s|api=%s|req=[%s]|resp=[%s]", GetQuotationSchemaID, GetQuotationApi, businessDataString, getQuotationResponse.GetData())

	var resp GetQuotationReply
	if err := jsoniter.Unmarshal([]byte(getQuotationResponse.GetData()), &resp); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return &resp, nil
}
