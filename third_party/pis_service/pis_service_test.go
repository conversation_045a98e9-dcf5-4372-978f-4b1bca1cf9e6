package pis_service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
)

func TestPISService_SyncBranch(t *testing.T) {
	t.Skip()
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/task"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}

	invoker := chassis.NewRPCInvoker()

	type fields struct {
		invoker     *chassis.RPCInvoker
		serviceName string
		region      string
	}
	type args struct {
		ctx        utils.LCOSContext
		requestID  string
		resourceID int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		{
			name: "test sync branch",
			fields: fields{
				invoker:     invoker,
				serviceName: "sls-partnerintegrationservice-sg",
				region:      "SG",
			},
			args: args{
				ctx:        utils.NewCommonCtx(context.Background()),
				requestID:  "123455",
				resourceID: 2033,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PISService{
				rpcInvoker:  tt.fields.invoker,
				serviceName: tt.fields.serviceName,
				region:      tt.fields.region,
			}
			if got := p.SyncBranch(tt.args.ctx, tt.args.requestID, tt.args.resourceID); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SyncBranch() = %v, want %v", got, tt.want)
			}
		})
	}
}
