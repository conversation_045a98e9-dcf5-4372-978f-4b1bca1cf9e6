package pis_service

// 1. GetQuotation

type Sku struct {
	ItemId    uint64  `json:"item_id"`
	Category  string  `json:"category"`
	Length    float64 `json:"length"`
	Width     float64 `json:"width"`
	Height    float64 `json:"height"`
	Weight    float64 `json:"weight"`
	Quantity  uint32  `json:"quantity"`
	ItemPrice float64 `json:"item_price"`
}

type RateLocation struct {
	PickupPostcode  string `json:"pickup_postcode"`
	DeliverPostcode string `json:"deliver_postcode"`
}

type GetQuotationReq struct {
	Skus   []*Sku `json:"skus"`
	ShopId string `json:"shop_id"`
	//ChannelId    string        `json:"channel_id"`
	SourceType   uint8         `json:"source_type"`
	RateLocation *RateLocation `json:"rate_location"`
}

type GetQuotationReply struct {
	QuotationId                               uint64  `json:"quotation_id"`
	DeliveryMethodId                          uint64  `json:"delivery_method_id"`
	DeliveryEstimateTransitTimeBusinessDays   int32   `json:"delivery_estimate_transit_time_business_days"`
	DeliveryAdditionalTransitTimeBusinessDays int32   `json:"delivery_additional_transit_time_business_days"`
	ShippingFee                               float64 `json:"shipping_fee"`
}
