package pis_service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/common"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	pisPb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v11/partner-integration-system/go"
	"time"
)

func genReqHeader(ctx context.Context, requestID string) *pisPb.ReqHeader {
	clientIP := utils.GetLocalIp()
	return &pisPb.ReqHeader{
		RequestId: utils.NewString(requestID),
		RequestIp: clientIP,
		Timestamp: utils.NewUint32(utils.GetTimestamp(ctx)),
		ClientId:  utils.NewString(config.GetConf(ctx).PISService.ClientID),
	}
}

func NewDistributionCtx(ctx context.Context, m map[string]string) context.Context {
	return common.NewOutgoingContextWithHeader(ctx, utils.MapToStringSlice(m)...)
}

func NewDistributionCtxWithTimeout(ctx context.Context, timeout time.Duration, m map[string]string) (context.Context, context.CancelFunc) {
	tCtx, cancel := context.WithTimeout(ctx, timeout)
	newCtx := common.NewOutgoingContextWithHeader(tCtx, utils.MapToStringSlice(m)...)
	return newCtx, cancel
}
