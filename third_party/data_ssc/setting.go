package data_ssc

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"time"
)

var (
	account = "lcs"
	hosts   = map[string]string{
		"LOCAL":   "https://data.ssc.test.shopeemobile.com",
		"TEST":    "https://data.ssc.test.shopeemobile.com",
		"UAT":     "https://data.ssc.uat.shopeemobile.com",
		"STAGING": "https://data.ssc.staging.shopeemobile.com",
		//"LIVEISH": "https://ops.ssc.shopeemobile.com",
		"LIVE": "https://data.ssc.shopeemobile.com",
	}

	defaultTimeout   = 5 * time.Second
	httpClient       *chassis.RestInvoker
	triggerApi       = "/data_api/data_sls/cdt_api/trigger_run_job/"
	querySlsTNApi    = "/data_api/data_sls/cdt_api/sls_tn_query/"
	forecastApi      = "/data_api/data_sls/cdt_api/trigger_run_job_forecast/"
	triggerVolumeApi = "/data_api/data_sls/cdt_api/volume_trigger_run_job/"
)

func init() {
	httpClient = chassis.NewRestInvoker()
}
