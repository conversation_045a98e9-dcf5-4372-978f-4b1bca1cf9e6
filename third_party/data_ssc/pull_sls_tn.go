package data_ssc

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"strconv"
)

const BatchSize = 1000

type SlsTNStruct struct {
	SlsTN    string `json:"sls_tn"`
	Postcode string `json:"postcode"`
}

type PreviewOrderInfoRequest struct {
	FulfillmentProductID          []string `json:"fulfillment_product_id"`
	Region                        string   `json:"region"`
	OriginalStateLocationID       []string `json:"original_state_location_id,omitempty"`
	OriginalCityLocationID        []string `json:"original_city_location_id,omitempty"`
	OriginalDistrictLocationID    []string `json:"original_district_location_id,omitempty"`
	OriginalStreetLocationID      []string `json:"original_street_location_id,omitempty"`
	DestinationStateLocationID    []string `json:"destination_state_location_id,omitempty"`
	DestinationCityLocationID     []string `json:"destination_city_location_id,omitempty"`
	DestinationDistrictLocationID []string `json:"destination_district_location_id,omitempty"`
	DestinationStreetLocationID   []string `json:"destination_street_location_id,omitempty"`
	EDDStartTime                  int64    `json:"edd_start_time,omitempty"`
	EDDEndTime                    int64    `json:"edd_end_time,omitempty"`
	Postcode                      []string `json:"postcode,omitempty"`
	CepRange                      string   `json:"cep_range,omitempty"`
	EDDPickupTimestampStartTime   int64    `json:"edd_pickup_timestamp_start_time"`
	EDDPickupTimestampEndTime     int64    `json:"edd_pickup_timestamp_end_time"`
	Pageno                        int      `json:"pageno"`
	Count                         int      `json:"count"`
}

type PreviewOrderInfoResponse struct {
	Retcode int    `json:"retcode"`
	Message string `json:"message"`
	Data    struct {
		TotalSize string         `json:"total_size"`
		PageSize  int            `json:"page_size"`
		PageNo    int            `json:"page_no"`
		List      []*SlsTNStruct `json:"list"`
	} `json:"data"`
}

func GetSlsTNListFromDataPaging(ctx utils.LCOSContext, request *PreviewOrderInfoRequest, page, count int) ([]*SlsTNStruct, int, *lcos_error.LCOSError) {
	request.Pageno = page
	request.Count = count
	response := &PreviewOrderInfoResponse{}
	url := hosts[utils.GetEnv(ctx)] + querySlsTNApi
	lcosErr := http.SendPostHttpRequest(ctx, httpClient, defaultTimeout, url, request, response, nil)
	if lcosErr != nil {
		return nil, 0, lcosErr
	}
	if response.Retcode != 0 {
		logger.CtxLogErrorf(ctx, "requesting data error|url=[%s]|req=[%s]|resp=[%s]", url, utils.MarshToStringWithoutError(request), utils.MarshToStringWithoutError(response))
		return nil, 0, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, response.Message)
	}
	logger.CtxLogInfof(ctx, "requesting data success|url=[%s]|req=[%s]|resp=[%s]", url, utils.MarshToStringWithoutError(request), utils.MarshToStringWithoutError(response))
	totalSize, err := strconv.Atoi(response.Data.TotalSize)
	if err != nil {
		return nil, 0, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return response.Data.List, totalSize, nil
}
