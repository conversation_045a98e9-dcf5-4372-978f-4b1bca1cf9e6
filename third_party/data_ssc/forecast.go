package data_ssc

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
)

// TriggerForecastJobTask
func TriggerForecastJobTask(ctx utils.LCOSContext, triggerParams *ForecastRunJob) (*DataResponse, *lcos_error.LCOSError) {
	result := &DataResponse{}
	url := hosts[utils.GetEnv(ctx)] + forecastApi
	lcosErr := http.SendPostHttpRequest(ctx, httpClient, defaultTimeout, url, triggerParams, result, nil)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if result.Retcode != 0 {
		logger.CtxLogErrorf(ctx, "requesting data error|url=[%s]|req=[%s]|resp=[%s]", url, utils.MarshToStringWithoutError(triggerParams), utils.MarshToStringWithoutError(result))
		return nil, lcos_error.NewLCOSError(int32(result.Retcode), result.Message)
	}
	logger.CtxLogInfof(ctx, "requesting data success|url=[%s]|req=[%s]|resp=[%s]", url, utils.MarshToStringWithoutError(triggerParams), utils.MarshToStringWithoutError(result))
	return result, nil
}
