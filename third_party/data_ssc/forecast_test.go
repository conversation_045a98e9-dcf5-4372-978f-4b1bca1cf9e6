package data_ssc

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"os"
	"reflect"
	"testing"
)

func TestTriggerForecastJobTask(t *testing.T) {

	startup.InitSSCEnv()
	startup.SetChassisConfDir(startup.ServiceAPI)
	_ = os.Setenv("CHASSIS_CONF_DIR", "/Users/<USER>/Documents/golang/src/logistics-core-service/conf/chassis_conf/api")

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	type args struct {
		ctx           utils.LCOSContext
		triggerParams *ForecastRunJob
	}
	tests := []struct {
		name  string
		args  args
		want1 *lcos_error.LCOSError
	}{
		{
			name: "test forecast task rule",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				triggerParams: &ForecastRunJob{
					CdtType:                  1,
					ProductID:                "18054",
					ForecastTaskID:           23123323,
					IsCB:                     1,
					MaskingType:              0,
					OriginLocationLevel:      -1,
					DestinationLocationLevel: -1,
					Frequency:                7,
					CepRange:                 "",
					PostcodeList:             "",
					MinPercentile:            0.2,
					MaxPercentile:            0.8,
					LMMaxPercentile:          0,
					ThresholdNum:             10,
					ExcludedDays:             "",
					Region:                   "SG",
					TimePeriod:               30,
					PostcodeFlag:             1,
					RemoveFlag:               0,
					RemoveThresholdNum:       0,
					TestSetTimePeriodStart:   1677600000,
					TestSetTimePeriodEnd:     1680278400,
					UpdateEventRuleList:      "[{\"event\":3,\"allow_preempt_update\":1,\"deadline_method\":2,\"confidence_level\":0.8,\"update_condition\":2,\"min_value\":1,\"update_method\":2,\"min_allow_extend\":1,\"min_allow_deduct\":1,\"max_allow_extend\":1,\"max_allow_deduct\":1,\"rule_id\":\"1031\",\"max_times\":5,\"edd_min_threshold_min\":50,\"edd_min_threshold_max\":50,\"edd_max_threshold_min\":50,\"edd_max_threshold_max\":50,\"checkpoint_frequency\":12},{\"event\":4,\"allow_preempt_update\":1,\"deadline_method\":2,\"confidence_level\":0.8,\"update_condition\":2,\"min_value\":1,\"update_method\":2,\"min_allow_extend\":1,\"min_allow_deduct\":1,\"max_allow_extend\":1,\"max_allow_deduct\":1,\"rule_id\":\"1031\",\"max_times\":5,\"edd_min_threshold_min\":50,\"edd_min_threshold_max\":50,\"edd_max_threshold_min\":50,\"edd_max_threshold_max\":50,\"checkpoint_frequency\":12},{\"event\":5,\"allow_preempt_update\":1,\"deadline_method\":2,\"confidence_level\":0.8,\"update_condition\":2,\"min_value\":1,\"update_method\":2,\"min_allow_extend\":1,\"min_allow_deduct\":1,\"max_allow_extend\":1,\"max_allow_deduct\":1,\"rule_id\":\"1031\",\"max_times\":5,\"edd_min_threshold_min\":50,\"edd_min_threshold_max\":50,\"edd_max_threshold_min\":50,\"edd_max_threshold_max\":50,\"checkpoint_frequency\":12},{\"event\":6,\"allow_preempt_update\":1,\"deadline_method\":2,\"confidence_level\":0.8,\"update_condition\":2,\"min_value\":1,\"update_method\":2,\"min_allow_extend\":1,\"min_allow_deduct\":1,\"max_allow_extend\":1,\"max_allow_deduct\":1,\"rule_id\":\"1031\",\"max_times\":5,\"edd_min_threshold_min\":50,\"edd_min_threshold_max\":50,\"edd_max_threshold_min\":50,\"edd_max_threshold_max\":50,\"checkpoint_frequency\":12},{\"event\":7,\"allow_preempt_update\":1,\"deadline_method\":2,\"confidence_level\":0.8,\"update_condition\":2,\"min_value\":1,\"update_method\":2,\"min_allow_extend\":1,\"min_allow_deduct\":1,\"max_allow_extend\":1,\"max_allow_deduct\":1,\"rule_id\":\"1031\",\"max_times\":5,\"edd_min_threshold_min\":50,\"edd_min_threshold_max\":50,\"edd_max_threshold_min\":50,\"edd_max_threshold_max\":50,\"checkpoint_frequency\":12}]",
				},
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, got1 := TriggerForecastJobTask(tt.args.ctx, tt.args.triggerParams)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("TriggerForecastJobTask() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
