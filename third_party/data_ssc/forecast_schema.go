package data_ssc

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	jsoniter "github.com/json-iterator/go"
)

type SingleUpdateEvent struct {
	Event               uint8   `json:"event"`
	AllowPreemptUpdate  uint8   `json:"allow_preempt_update"`
	DeadlineMethod      uint8   `json:"deadline_method"`
	ConfidenceLevel     float64 `json:"confidence_level"`
	UpdateCondition     uint8   `json:"update_condition"`
	MinValue            uint32  `json:"min_value"`
	UpdateMethod        uint8   `json:"update_method"`
	MinAllowExtend      uint8   `json:"min_allow_extend"`
	MinAllowDeduct      uint8   `json:"min_allow_deduct"`
	MaxAllowExtend      uint8   `json:"max_allow_extend"`
	MaxAllowDeduct      uint8   `json:"max_allow_deduct"`
	RuleID              string  `json:"rule_id"`
	MaxTimes            uint32  `json:"max_times"`
	EDDMinThresholdMin  uint32  `json:"edd_min_threshold_min"`
	EDDMinThresholdMax  uint32  `json:"edd_min_threshold_max"`
	EDDMaxThresholdMin  uint32  `json:"edd_max_threshold_min"`
	EDDMaxThresholdMax  uint32  `json:"edd_max_threshold_max"`
	CheckpointFrequency uint32  `json:"checkpoint_frequency"`
}

func getTrending(maxExtend, minDeduct uint8) uint8 {
	if maxExtend == constant.TRUE && minDeduct != constant.TRUE {
		return edd_constant.ExtendTrending
	} else if maxExtend != constant.TRUE && minDeduct == constant.TRUE {
		return edd_constant.DeductTrending
	}
	return edd_constant.FreeTrending
}

func (s *SingleUpdateEvent) GetEDDMinTrending() uint8 {
	return getTrending(s.MinAllowExtend, s.MinAllowDeduct)
}

func (s *SingleUpdateEvent) GetEDDMaxTrending() uint8 {
	return getTrending(s.MaxAllowExtend, s.MaxAllowDeduct)
}

func (s *SingleUpdateEvent) GetConfidenceLevel() uint32 {
	return uint32(s.ConfidenceLevel * 100)
}

type ForecastTargetMetrics struct {
	Metrics   float64 `json:"metrics"`
	Priority  uint32  `json:"priority"`
	RangeDays uint32  `json:"range_days,omitempty"`
}

type ForecastTargets struct {
	InitialAccuracy        ForecastTargetMetrics `json:"initial_accuracy" validate:"dive"`
	InitialPrecision       ForecastTargetMetrics `json:"initial_precision" validate:"dive"`
	FinalAccuracy          ForecastTargetMetrics `json:"final_accuracy" validate:"dive"`
	FinalPrecision         ForecastTargetMetrics `json:"final_precision" validate:"dive"`
	AverageInitialEDDRange ForecastTargetMetrics `json:"average_initial_edd_range" validate:"dive"`
	AverageFinalEDDRange   ForecastTargetMetrics `json:"average_final_edd_range" validate:"dive"`
}

type ForecastEdtTargets struct {
	EDTAccuracy     ForecastTargetMetrics `json:"edt_accuracy" validate:"dive"`
	EDTPrecision    ForecastTargetMetrics `json:"edt_precision" validate:"dive"`
	AverageEdtRange ForecastTargetMetrics `json:"average_edt_range" validate:"dive"`
}

type ForecastRunJob struct {
	ProductID                string  `json:"product_id"`
	ForecastTaskID           uint64  `json:"forecast_task_id"`
	IsCB                     uint8   `json:"is_cb"`
	MaskingType              uint8   `json:"masking_type"`
	OriginLocationLevel      int8    `json:"origin_location_level"`
	DestinationLocationLevel int8    `json:"destination_location_level"`
	Frequency                uint32  `json:"frequency"`
	CepRange                 string  `json:"cep_range"` // used to store cep range or postcode data to data, cep range format:1-2#3-4#5-6, postcode format: 1-1#2-2#3-3
	PostcodeList             string  `json:"postcode_list"`
	MinPercentile            float64 `json:"min_percentile"`
	MaxPercentile            float64 `json:"max_percentile"`
	LMMaxPercentile          float64 `json:"lm_max_percentile"`
	ThresholdNum             uint32  `json:"threshold_num"`
	ExcludedDays             string  `json:"excluded_days"`
	Region                   string  `json:"region"`
	TimePeriod               uint32  `json:"time_period"`
	PostcodeFlag             uint8   `json:"postcode_flag"` // 1-location，2-cep range，3-postcode
	RemoveFlag               uint8   `json:"remove_flag"`
	RemoveThresholdNum       uint32  `json:"remove_threshold_num"`
	TestSetTimePeriodStart   uint32  `json:"test_set_time_period_start"`
	TestSetTimePeriodEnd     uint32  `json:"test_set_time_period_end"`
	CdtType                  uint8   `json:"cdt_type"` // SPLN-24104, cdt类型，0-product，1-product&lane
	UpdateEventRuleList      string  `json:"update_event_rule_list"`
	Method                   *uint8  `json:"method,omitempty"`           // SPLN-30795 0-User Defines Rule   1-System Recommends
	Targets                  *string `json:"targets,omitempty"`          // SPLN-30795 targets
	EventTimeLevel           *uint8  `json:"event_time_level,omitempty"` // 0-None；1-Day of Week，按照day group对数据进行聚合；2-time of day，按照time bucket对数据进行聚合计算，3-day of week+time of day，需要Day Group+Time Bucket对数据进行聚合计算
	DayGroup                 *string `json:"day_group,omitempty"`        // 日期组，包含相关的星期分组 json字符串[ [ 1, 2, 3 ], [ 4, 5, 6 ], [ 7 ] ]
	EdtTargets               *string `json:"edt_targets,omitempty"`
}

func (f *ForecastRunJob) Scan(value interface{}) error {
	valueBytes, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("Failed to unmarshal ForecastRunJob value:", valueBytes))
	}
	if len(valueBytes) == 0 {
		valueBytes = []byte("{}")
	}

	return jsoniter.Unmarshal(valueBytes, f)
}

func (f ForecastRunJob) Value() (driver.Value, error) {
	return jsoniter.Marshal(f)
}
