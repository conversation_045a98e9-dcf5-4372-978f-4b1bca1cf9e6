package data_ssc

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
)

type SinglePreemptiveEvent struct {
	Event              uint8   `json:"event"`
	DeadlineMethod     uint8   `json:"deadline_method"`
	AllowPreemptUpdate uint8   `json:"allow_preempt_update"`
	ConfidenceLevel    float64 `json:"confidence_level"`
}

type TriggerRunJob struct {
	CdtType                  uint8   `json:"cdt_type"` // SPLN-24104, cdt类型，0-product，1-product&lane
	ProductID                string  `json:"product_id"`
	AutoUpdateRuleID         uint64  `json:"auto_update_rule_id"`
	<PERSON><PERSON>                     uint8   `json:"is_lm"`
	MaskingType              uint8   `json:"masking_type"`
	OriginLocationLevel      int8    `json:"origin_location_level"`
	DestinationLocationLevel int8    `json:"destination_location_level"`
	Frequency                int     `json:"frequency"`
	CepRange                 string  `json:"cep_range"` // used to store cep range or postcode data to data, cep range format:1-2#3-4#5-6, postcode format: 1-1#2-2#3-3
	PostcodeList             string  `json:"postcode_list"`
	MinPercentile            float64 `json:"min_percentile"`
	MaxPercentile            float64 `json:"max_percentile"`
	LMMaxPercentile          float64 `json:"lm_max_percentile"`
	ThresholdNum             int     `json:"threshold_num"`
	ExcludedDays             string  `json:"excluded_days"`
	Region                   string  `json:"region"`
	TimePeriod               int     `json:"time_period"`
	PostcodeFlag             uint8   `json:"postcode_flag"` // 1-location，2-cep range，3-postcode
	RemoveFlag               uint8   `json:"remove_flag"`
	RemoveThresholdNum       uint32  `json:"remove_threshold_num"`
	UpdateEventRuleList      string  `json:"update_event_rule_list"`

	// SPLN-30795
	SystemRecommendation uint8  `json:"system_recommendation"`
	DataVersion          uint32 `json:"data_version"`
	UUID                 string `json:"uuid"`
	EventTimeLevel       uint8  `json:"event_time_level"`  // 0-None；1-Day of Week，按照day group对数据进行聚合；2-time of day，按照time bucket对数据进行聚合计算，3-day of week+time of day，需要Day Group+Time Bucket对数据进行聚合计算
	DayGroup             string `json:"day_group"`         // 日期组，包含相关的星期分组 json字符串[ [ 1, 2, 3 ], [ 4, 5, 6 ], [ 7 ] ]
	StartEventFCode      uint8  `json:"start_event_fcode"` // 0-default pickup done, 1-F000
}

type DataResponse struct {
	Retcode int    `json:"retcode"`
	Message string `json:"message"`
}

// 触发自动计算任务
func TriggerRunJobTask(ctx utils.LCOSContext, triggerParams *TriggerRunJob) (*DataResponse, *lcos_error.LCOSError) {
	result := &DataResponse{}
	url := hosts[utils.GetEnv(ctx)] + triggerApi
	lcosErr := http.SendPostHttpRequest(ctx, httpClient, defaultTimeout, url, triggerParams, result, nil)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if result.Retcode != 0 {
		logger.CtxLogErrorf(ctx, "requesting data error|url=[%s]|req=[%s]|resp=[%s]", url, utils.MarshToStringWithoutError(triggerParams), utils.MarshToStringWithoutError(result))
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, result.Message)
	}
	logger.CtxLogInfof(ctx, "requesting data success|url=[%s]|req=[%s]|resp=[%s]", url, utils.MarshToStringWithoutError(triggerParams), utils.MarshToStringWithoutError(result))
	return result, nil
}
