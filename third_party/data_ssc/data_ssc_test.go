package data_ssc

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"os"
	"strings"
	"testing"
)

// 触发自动计算任务
func TestTriggerRunJobTask(t *testing.T) {

	_ = os.Setenv("ENV", "TEST")

	req := &TriggerRunJob{
		ProductID:                "2222",
		IsLM:                     1,
		MaskingType:              1,
		OriginLocationLevel:      1,
		DestinationLocationLevel: constant.CDTPostcode,
		Frequency:                7,
		CepRange:                 "",
		MinPercentile:            10,
		MaxPercentile:            99,
		LMMaxPercentile:          0,
		ThresholdNum:             100,
		ExcludedDays:             "",
		Region:                   "VN",
		PostcodeList:             strings.Join([]string{"1", "2", "3"}, ","),
		PostcodeFlag:             3,
	}
	result, lcosErr := TriggerRunJobTask(utils.NewCommonCtx(context.Background()), req, true)
	if lcosErr != nil {
		t.Error(lcosErr.Msg)
	} else {
		t.Logf("%v", result)
	}
}
