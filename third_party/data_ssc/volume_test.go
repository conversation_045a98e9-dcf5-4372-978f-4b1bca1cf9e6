package data_ssc

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"reflect"
	"testing"
)

func TestTriggerVolumeRunJobTask(t *testing.T) {
	//初始化资源

	type args struct {
		ctx           utils.LCOSContext
		triggerParams *TriggerVolumeRunJob
	}
	tests := []struct {
		name string
		args args
		//want  *DataResponse
		want1 *lcos_error.LCOSError
	}{
		{
			name: "test volume task",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				triggerParams: &TriggerVolumeRunJob{
					ProductID:                "91003",
					AutomatedVolumeRuleID:    1,
					OriginLocationLevel:      int8(constant.DISTRICT),
					DestinationLocationLevel: int8(constant.DISTRICT),
					TimePeriod:               1,
					Frequency:                20,
					ExcludedDays:             "",
					Region:                   "BR",
					GroupBy:                  edd_constant.GroupByFChannel,
				},
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, got1 := TriggerVolumeRunJobTask(tt.args.ctx, tt.args.triggerParams)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("TriggerVolumeRunJobTask() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
