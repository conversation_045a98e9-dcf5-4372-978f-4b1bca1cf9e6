package data_ssc

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"reflect"
	"testing"
)

func TestGetSlsTNListFromDataPaging(t *testing.T) {
	type args struct {
		ctx     utils.LCOSContext
		request *PreviewOrderInfoRequest
		page    int
		count   int
	}
	tests := []struct {
		name  string
		args  args
		want  []*SlsTNStruct
		want1 int
		want2 *lcos_error.LCOSError
	}{
		{
			name: "test pull sls tn from BR",
			args: args{
				ctx: utils.NewCommonCtx(logger.NewLogContext(context.TODO(), "test111")),
				request: &PreviewOrderInfoRequest{
					FulfillmentProductID:        []string{"90008", ""},
					Region:                      "",
					EDDStartTime:                0,
					EDDEndTime:                  0,
					Postcode:                    nil,
					CepRange:                    "",
					EDDPickupTimestampStartTime: 0,
					EDDPickupTimestampEndTime:   0,
					Pageno:                      0,
					Count:                       0,
				},
				page:  0,
				count: 0,
			},
			want:  nil,
			want1: 0,
			want2: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := GetSlsTNListFromDataPaging(tt.args.ctx, tt.args.request, tt.args.page, tt.args.count)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSlsTNListFromDataPaging() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetSlsTNListFromDataPaging() got1 = %v, want %v", got1, tt.want1)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("GetSlsTNListFromDataPaging() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}
