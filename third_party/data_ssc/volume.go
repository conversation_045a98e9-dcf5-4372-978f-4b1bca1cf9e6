package data_ssc

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
)

type TriggerVolumeRunJob struct {
	ProductID                string `json:"product_id"`
	AutomatedVolumeRuleID    uint64 `json:"automated_volume_rule_id"`
	OriginLocationLevel      int8   `json:"origin_location_level"`
	DestinationLocationLevel int8   `json:"destination_location_level"`
	TimePeriod               uint32 `json:"time_period"`
	Frequency                uint32 `json:"frequency"`
	ExcludedDays             string `json:"excluded_days"`
	Region                   string `json:"region"`
	GroupBy                  int8   `json:"group_by"` //1：F-channel, 2：Lane code
}

// 触发单量统计计算任务
func TriggerVolumeRunJobTask(ctx utils.LCOSContext, triggerParams *TriggerVolumeRunJob) (*DataResponse, *lcos_error.LCOSError) {
	result := &DataResponse{}
	//mock
	url := config.GetMockDataApiConfig(ctx).Url
	if len(url) == 0 {
		url = hosts[utils.GetEnv(ctx)] + triggerVolumeApi
	}
	lcosErr := http.SendPostHttpRequest(ctx, httpClient, defaultTimeout, url, triggerParams, result, nil)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if result.Retcode != 0 {
		logger.CtxLogErrorf(ctx, "requesting data for volume error|url=[%s]|req=[%s]|resp=[%s]|url=[%s]", url, utils.MarshToStringWithoutError(triggerParams), utils.MarshToStringWithoutError(result), url)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, result.Message)
	}
	logger.CtxLogInfof(ctx, "requesting data for volume success|url=[%s]|req=[%s]|resp=[%s]]|url=[%s]", url, utils.MarshToStringWithoutError(triggerParams), utils.MarshToStringWithoutError(result), url)
	return result, nil
}
