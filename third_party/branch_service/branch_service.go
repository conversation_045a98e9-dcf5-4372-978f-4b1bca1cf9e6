package branch_service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/env"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/grpc"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"strings"
)

type branchServiceImpl struct {
	serviceName string
}

type CommonInterface interface {
	BranchService
	StoreService
}

type BranchService interface {
	GetBranchInfo(ctx context.Context, req *pb.GetBranchInfoRequest) (rsp *pb.GetBranchInfoResponse, lcosError *lcos_error.LCOSError)
	GetBranchListByBranchGroupID(ctx context.Context, req *pb.GetBranchListByBranchGroupIDRequest) (rsp *pb.GetBranchListByBranchGroupIDResponse, lcosError *lcos_error.LCOSError)
}

type StoreService interface {
	BatchGetStoreByAddressID(ctx context.Context, req *pb.BatchGetStoreByAddressIDRequest) (rsp *pb.BatchGetStoreByStoreIDResponse, lcosError *lcos_error.LCOSError)
}

func NewBranchService() CommonInterface {
	branchServiceName := "sls-branchservice-sg"
	if env.IsUSIdc() {
		branchServiceName = "sls-branchservice-" + strings.ToLower(env.GetCID()) // us机房单独请求br/mx/co/cl市场的服务获取对应市场数据
	}
	return &branchServiceImpl{serviceName: branchServiceName}
}

func (l *branchServiceImpl) GetBranchInfo(ctx context.Context, req *pb.GetBranchInfoRequest) (rsp *pb.GetBranchInfoResponse, lcosError *lcos_error.LCOSError) {
	rsp = &pb.GetBranchInfoResponse{}
	err := grpc.Invoke(ctx, l.serviceName, BranchSchemaID, GetBranchInfo, req, rsp)
	if err != nil {
		rsp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.ServerErrorCode, err.Error())
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, BranchSchemaID, GetBranchInfo, utils.MarshToStringWithoutError(req), err.Error())
		return rsp, nil
	}
	if rsp.GetRespHeader() == nil {
		rsp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.ServerErrorCode, "response header is nil")
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, BranchSchemaID, GetBranchInfo, utils.MarshToStringWithoutError(req), "response header is nil")
		return rsp, nil
	}

	return rsp, nil
}

func (l *branchServiceImpl) GetBranchListByBranchGroupID(ctx context.Context, req *pb.GetBranchListByBranchGroupIDRequest) (rsp *pb.GetBranchListByBranchGroupIDResponse, lcosError *lcos_error.LCOSError) {
	rsp = &pb.GetBranchListByBranchGroupIDResponse{}
	err := grpc.Invoke(ctx, l.serviceName, BranchSchemaID, GetBranchListByBranchGroupID, req, rsp)
	if err != nil {
		rsp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.ServerErrorCode, err.Error())
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, BranchSchemaID, GetBranchListByBranchGroupID, utils.MarshToStringWithoutError(req), err.Error())
		return rsp, nil
	}
	if rsp.GetRespHeader() == nil {
		rsp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.ServerErrorCode, "response header is nil")
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, BranchSchemaID, GetBranchListByBranchGroupID, utils.MarshToStringWithoutError(req), "response header is nil")
		return rsp, nil
	}

	return rsp, nil
}

func (l *branchServiceImpl) BatchGetStoreByAddressID(ctx context.Context, req *pb.BatchGetStoreByAddressIDRequest) (rsp *pb.BatchGetStoreByStoreIDResponse, lcosError *lcos_error.LCOSError) {
	rsp = &pb.BatchGetStoreByStoreIDResponse{}
	err := grpc.Invoke(ctx, l.serviceName, StoreSchemaId, BatchGetStoreByAddressID, req, rsp)
	if err != nil {
		rsp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.ServerErrorCode, err.Error())
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, StoreSchemaId, BatchGetStoreByAddressID, utils.MarshToStringWithoutError(req), err.Error())
		return rsp, nil
	}
	if rsp.GetRespHeader() == nil {
		rsp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.ServerErrorCode, "response header is nil")
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, BranchSchemaID, BatchGetStoreByAddressID, utils.MarshToStringWithoutError(req), "response header is nil")
		return rsp, nil
	}

	return rsp, nil
}
