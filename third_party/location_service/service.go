package location_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache_with_ttl"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	protocol "git.garena.com/shopee/bg-logistics/logistics/proto-center/v21/sls-location/go"
	"golang.org/x/net/context"
	"strconv"
	"strings"
)

var invoker = chassis.NewRPCInvoker()

var locationInfoCache *cache.LruCache

func init() {
	locationInfoCache, _ = cache.NewLruCache(cache.LocationServiceLruName, &protocol.LocationInfo{})
}

type LocationService interface {
	GetLocationByLocationId(ctx utils.LCOSContext, locationId uint64) (*protocol.LocationInfo, *lcos_error.LCOSError)
}

func NewLocationService(ctx context.Context) LocationService {
	serviceName := fmt.Sprintf("%s-%s-%s", LocationServiceName, strings.ToLower(utils.GetEnv(ctx)), strings.ToLower(utils.GetCID()))
	return &locationService{
		serviceName: serviceName,
	}
}

type locationService struct {
	serviceName string
}

func (l *locationService) GetLocationByLocationId(ctx utils.LCOSContext, locationId uint64) (*protocol.LocationInfo, *lcos_error.LCOSError) {
	cacheKey := utils.GenKey(":", "location_id", strconv.FormatUint(locationId, 10))
	if obj, ok := locationInfoCache.Get(ctx, cacheKey); ok {
		// 防止缓存穿透
		if obj == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "location not found")
		}
		if ret, ok := obj.(*protocol.LocationInfo); ok {
			return ret, nil
		}
	}

	req := &protocol.LocationReq{
		Country:    "", // 无法判断locationId的region，可以不指定
		LocationId: uint32(locationId),
	}
	resp := &protocol.LocationRsp{}
	if err := invoker.Invoke(ctx, l.serviceName, LocationServiceSchema, GetLocationByLocationIdOperation, req, resp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "request %s/%s/%s error: %s", l.serviceName, LocationServiceSchema, GetLocationByLocationIdOperation, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if resp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "sls-location return header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "sls-location return header is nil")
	}
	if resp.GetHeader().GetRetcode() != 0 {
		// 如果报错location not found，则缓存一个空值，防止缓存大量穿透
		if resp.GetHeader().GetRetcode() == ErrLocationNotFound {
			locationInfoCache.Add(ctx, cacheKey, nil)
		}
		logger.CtxLogErrorf(ctx, "sls-location return error, req=%+v, retcode=%d, message=%s", req, resp.GetHeader().GetRetcode(), resp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.GetHeader().GetMessage())
	}
	locationInfoCache.Add(ctx, cacheKey, resp.LocationInfo)
	return resp.LocationInfo, nil
}
