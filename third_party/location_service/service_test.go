package location_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"os"
	"path"
	"strings"
	"testing"
	"time"
)

func TestGetLocationByLocationId(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "tw")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	svc := NewLocationService(ctx)
	start := recorder.Now(ctx)
	location, err := svc.GetLocationByLocationId(utils.NewCommonCtx(context.Background()), 3000295)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(location)
	fmt.Printf("time usage: %dms\n", time.Since(start).Milliseconds())

	start = recorder.Now(ctx)
	location, err = svc.GetLocationByLocationId(utils.NewCommonCtx(context.Background()), 3000295)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(location)
	fmt.Printf("time usage: %dms\n", time.Since(start).Milliseconds())

}
