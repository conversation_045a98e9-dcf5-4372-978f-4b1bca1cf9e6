package emailhelper

import (
	"os"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"gopkg.in/gomail.v2"
)

const (
	FromAddr             = "<EMAIL>"
	EMAIL_ADDRESS        = "<EMAIL>"
	EMAIL_FROMNAME       = "LCOS System Email"
	EMAIL_HOST           = "smtp.shopeemobile.com"
	EMAIL_PORT           = 587
	EMAIL_USER           = "<EMAIL>"
	DEFAULT_CONTENT_TYPE = "text/html"
)

func SendEmail(mailTo []string, subject string, body string) *lcos_error.LCOSError {
	m := gomail.NewMessage()
	m.SetHeader("From", FromAddr)
	m.SetHeader("To", mailTo...)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body)
	d := &gomail.Dialer{Host: "smtp.shopeemobile.com", Port: 587}
	//err := smtp.SendMail("smtp.shopeemobile.com:587", nil, FromAddr, mailTo, []byte(body))
	//err := m.Send(msg)
	err := d.DialAndSend(m)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.CdtCannotSendEmailErrorCode, err.Error())
	}
	return nil
}

// SPLN-27978
// add attachment to email. Notice this func will remove attachment
func SendEmailV2(content, subject, contentType string, attachment string, toPeople []string, ccPeople []string, bccPeople []string) error {
	mailMsg := gomail.NewMessage()
	mailMsg.SetAddressHeader("From", EMAIL_ADDRESS, EMAIL_FROMNAME)
	mailMsg.SetHeader("To", toPeople...)
	if len(ccPeople) > 0 {
		mailMsg.SetHeader("Cc", ccPeople...)
	}
	if len(bccPeople) > 0 {
		mailMsg.SetHeader("Bcc", bccPeople...)
	}
	if len(attachment) > 0 {
		mailMsg.Attach(attachment)
		defer os.Remove(attachment)
	}

	mailMsg.SetHeader("Subject", subject)
	mailMsg.SetBody(contentType, content)
	d := gomail.NewDialer(EMAIL_HOST, EMAIL_PORT, EMAIL_USER, "")
	err := d.DialAndSend(mailMsg)
	return err
}
