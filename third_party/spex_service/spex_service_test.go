package spex_service

import (
	"context"
	"testing"

	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"github.com/stretchr/testify/assert"
)

func TestMain(m *testing.M) {
	InitSpex(&config.SpexConfig{
		ServiceName: "supply_chain.logistics.http_api",
		ConfigKey:   "5dbc1d4184f249d1aafdc80d7067c530",
	}, false)
	config.Conf = &config.Config{
		SpexConfig: config.SpexConfig{
			GeoRoutingUser:    "sls",
			GeoRoutingProject: "self_collection_point",
			GeoRoutingKey:     "wVDdgtMv6pVqlIqys0JdTDbK2ABRtjtRpTDWJGVy",
		},
	}
	m.Run()
}

func Test_Create(t *testing.T) {
	s := &spexService{}
	resp, err := s.CreateBranchInfo(utils.NewCommonCtx(context.Background()), &branch_info.LogisticBranchInfoTab{
		SupplyType:            66666,
		BranchRef:             "test_3",
		BranchName:            "test_name",
		BranchStatus:          1,
		LocationDivisionID:    10006,
		MaxParcelStayDuration: 0,
		SpexExtraData:         "{}",
		DetailAddress:         "adress",
		Longitude:             "80.0",
		Latitude:              "23.1",
	})
	if assert.Empty(t, err) {
		t.Log(*resp.BranchInfo.Id)
	}
}

func Test_List(t *testing.T) {
	s := &spexService{}
	ctx := utils.NewCommonCtx(context.Background())
	resp, err := s.GetBranchListByType(ctx, "sg", 4001)
	if assert.Empty(t, err) {
		t.Log(resp)
	}
}

func Test_Search_Location_ID(t *testing.T) {
	s := &spexService{}
	ctx := utils.NewCommonCtx(context.Background())
	result := []uint64{}
	for _, v := range [][]string{
		//{"PH", "Metro Manila", "Metro Manila", ""},
		//{"PH", "Metro Manila", "Metro Manila", ""},
		//{"PH", "Metro Manila", "Metro Manila", ""},
		//{"PH", "Metro Manila", "Metro Manila", ""},
		{"ID", "SUMATERA SELATAN", "KOTA PALEMBANG", "GANDUS"},
		{"ID", "SUMATERA UTARA", "KOTA MEDAN", "MEDAN AMPLAS"},
	} {
		resp, err := s.SearchLocationDivisionID(ctx, v[0], v[1], v[2], v[3], "")
		if assert.Empty(t, err) {
			t.Log(resp)
			result = append(result, resp)
		}
	}
	t.Log(result)
}

func Test_GetBranch(t *testing.T) {
	s := &spexService{}
	ctx := utils.NewCommonCtx(context.Background())
	resp, err := s.GetBranchInfo(ctx, "sg", 1000, "711_903")
	if assert.Empty(t, err) {
		t.Log(resp)
	}
}

func Test_Matrix(t *testing.T) {
	s := &spexService{}
	ctx := utils.NewCommonCtx(context.Background())
	resp, err := s.RoutingMatrix(ctx, 1.***********, 103.*********, "SG", []*branch_info.LogisticBranchInfoTab{
		{
			Latitude:  "1.302",
			Longitude: "103.850",
		},
	}, false)
	t.Log(resp, err)
}
