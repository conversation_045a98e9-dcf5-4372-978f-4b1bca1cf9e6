package spex_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/scsps"
	"os"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
)

var initOnce sync.Once

func InitSpex(cfg *config.SpexConfig, isRegisterProcess bool) {

	spexAddress := scsps.WithSpexAddress("unix", "/run/spex/spex.sock")
	ctx := context.Background()
	env := strings.ToLower(utils.GetEnv(ctx)) // 深坑，一定要转env小写

	spexDebug := os.Getenv("spex_debug")
	if spexDebug == "true" {
		spexAddress = scsps.WithSpexAddress("tcp", "spex.lls-lcs.tech:8088")
	}
	instanceId, err := scsps.GenerateInstanceID(cfg.ServiceName, utils.GetCID(), env, "", "", "")
	if err != nil {
		panic(fmt.Sprintf("generate instance id :%s", err.Error()))
	}
	initOnce.Do(func() {
		err = scsps.Init(scsps.WithInstanceID(instanceId), scsps.WithConfigKey(cfg.ConfigKey), spexAddress)
		if err != nil {
			panic(fmt.Sprintf("Init sps :%s", err.Error()))
		}
		startSpex(isRegisterProcess)
	})

}

func startSpex(isRegisterProcess bool) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	if err := scsps.SubscribeConfig(ctx); err != nil {
		logger.LogErrorf("subscribe config :%s", err.Error())
	}

	// RegisterProcessor for server
	//if isRegisterProcess {
	//registerProcessors()
	//}
	// service and client  start up after call Register
	if err := scsps.Register(ctx); err != nil {
		logger.LogErrorf("sps register  :%s", err.Error())
	}
}
