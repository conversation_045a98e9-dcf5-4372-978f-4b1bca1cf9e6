package spex_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/monitoring"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/spex_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/spcli/gen/go/map_admindivision.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"
	geo_routing "git.garena.com/shopee/shopee_services_protocol/golang/geo_routing.pb"
	location_branch "git.garena.com/shopee/shopee_services_protocol/golang/location_branch.pb"
)

var useCaseOfMapApi = "be.sls_logistics.admin" //mapAdminDivision接口的useCase值,由map团队PM定义

type SpexService interface {
	//GetBranchInfoList(ctx utils.LCOSContext, req *location_branch.GetBranchInfoListRequest) ([]*location_branch.LocationBranchInfo, *lcos_error.LCOSError)
	GetBranchInfo(ctx utils.LCOSContext, region string, branchType uint32, branchRef string) (*location_branch.GetBranchInfoResponse, *lcos_error.LCOSError)
	CreateBranchInfo(ctx utils.LCOSContext, branchInfo *branch_info.LogisticBranchInfoTab) (*location_branch.CreateBranchInfoResponse, *lcos_error.LCOSError)
	UpdateBranchInfo(ctx utils.LCOSContext, branchInfo *branch_info.LogisticBranchInfoTab) (*location_branch.UpdateBranchInfoResponse, *lcos_error.LCOSError)
	SearchLocationDivisionID(ctx utils.LCOSContext, country, state, city, district, street string) (uint64, *lcos_error.LCOSError)
	GetBranchListByType(ctx utils.LCOSContext, region string, branchType int) ([]*location_branch.LocationBranchInfo, *lcos_error.LCOSError)
	UpdateBranchByRemoteBranch(ctx utils.LCOSContext, region string, branchInfo *location_branch.LocationBranchInfo) (*location_branch.UpdateBranchInfoResponse, *lcos_error.LCOSError)
	RoutingMatrix(ctx utils.LCOSContext, lat, lon float64, region string, branches []*branch_info.LogisticBranchInfoTab, avoid bool) (*geo_routing.MatrixResponse, *lcos_error.LCOSError)
	GetOrderList(ctx utils.LCOSContext, req *order_order_info.GetOrderListByIdListRequest, region string) (*order_order_info.GetOrderListByIdListResponse, *lcos_error.LCOSError)
	GetOrderListByOrderSN(ctx utils.LCOSContext, req *order_order_info.GetOrderListBySnListRequest, region string) (*order_order_info.GetOrderListBySnListResponse, *lcos_error.LCOSError)
}

type spexService struct{}

func NewSpexService() *spexService {
	return &spexService{}
}

func (s *spexService) CreateBranchInfo(ctx utils.LCOSContext, branchInfo *branch_info.LogisticBranchInfoTab) (*location_branch.CreateBranchInfoResponse, *lcos_error.LCOSError) {
	resp := new(location_branch.CreateBranchInfoResponse)
	status := uint32(branchInfo.BranchStatus)
	request := &location_branch.CreateBranchInfoRequest{
		BranchType:         &branchInfo.SupplyType,
		BranchRef:          &branchInfo.BranchRef,
		BranchName:         &branchInfo.BranchName,
		BranchAddress:      &branchInfo.DetailAddress,
		Status:             &status,
		ExtraData:          &branchInfo.SpexExtraData,
		PostalCode:         &branchInfo.Postalcode,
		Latitude:           &branchInfo.Latitude,
		Longitude:          &branchInfo.Longitude,
		LocationDivisionId: &branchInfo.LocationDivisionID,
	}
	err := RequestSpex(ctx, branchInfo.Region, spex_constant.CreateBranchInfoCommand, request, resp)
	return resp, err
}

func (s *spexService) UpdateBranchInfo(ctx utils.LCOSContext, branchInfo *branch_info.LogisticBranchInfoTab) (*location_branch.UpdateBranchInfoResponse, *lcos_error.LCOSError) {
	resp := new(location_branch.UpdateBranchInfoResponse)
	status := uint32(branchInfo.BranchStatus)
	request := &location_branch.UpdateBranchInfoRequest{
		Id:                 &branchInfo.BranchID,
		BranchType:         &branchInfo.SupplyType,
		BranchRef:          &branchInfo.BranchRef,
		BranchName:         &branchInfo.BranchName,
		BranchAddress:      &branchInfo.DetailAddress,
		Status:             &status,
		ExtraData:          &branchInfo.SpexExtraData,
		PostalCode:         &branchInfo.Postalcode,
		Latitude:           &branchInfo.Latitude,
		Longitude:          &branchInfo.Longitude,
		LocationDivisionId: &branchInfo.LocationDivisionID,
	}
	err := RequestSpex(ctx, branchInfo.Region, spex_constant.UpdateBranchInfoCommand, request, resp)
	return resp, err
}

//
//func (s *spexService) searchLocationDivisionID(ctx utils.LCOSContext, country, state, city, district, street string) (uint64, *lcos_error.LCOSError) {
//	division := config.GetConf(ctx).DivisionConfig.GET_DIVISION_ID_COUNTRY
//	searchAddress := []*location_address.SearchedAddress{{LevelName: []string{division[country]}}}
//	for _, v := range []string{state, city, district, street} {
//		if len(v) == 0 {
//			break
//		}
//		searchAddress[0].LevelName = append(searchAddress[0].LevelName, v)
//	}
//	req := location_address.SearchDivisionListRequest{Addresses: searchAddress}
//	resp := new(location_address.SearchDivisionListResponse)
//
//	err := RequestSpex(ctx, country, spex_constant.SearchDivisionListCommand, &req, resp)
//	if err != nil {
//		return 0, err
//	}
//
//	if len(resp.Addresses) == 0 {
//		msg := fmt.Sprintf("get spex success, but seasrch result is empty")
//		logger.CtxLogErrorf(ctx, msg)
//		ReportSPEXBusinessError(ctx, "SpexService", spex_constant.SearchDivisionListCommand, spex_constant.ErrorInternal, msg)
//		return 0, lcos_error.NewLCOSError(lcos_error.SpexCommonError, msg)
//	}
//
//	addr := resp.Addresses[0]
//	if addr.ErrCode != nil && *addr.ErrCode != 0 {
//		msg := fmt.Sprintf("get location division id of %v failed, %v",req.Addresses[0].LevelName, *addr.ErrCode)
//		logger.CtxLogErrorf(ctx, msg)
//		//错误上报(业务错误)
//		ReportSPEXBusinessError(ctx, "SpexService", spex_constant.SearchDivisionListCommand, spex_constant.ErrorInternal, msg)
//		return 0, lcos_error.NewLCOSError(lcos_error.SpexCommonError, msg)
//	}
//	return *addr.Id, nil
//}

func (s *spexService) searchLocationDivisionIDByNewAPI(ctx utils.LCOSContext, country, state, city, district, street string) (uint64, *lcos_error.LCOSError) {
	division := config.GetConf(ctx).DivisionConfig.GET_DIVISION_ID_COUNTRY
	searchAddress := []*admindivision.SearchedAddress{{LevelName: []string{division[country]}}}
	for _, v := range []string{state, city, district, street} {
		if len(v) == 0 {
			break
		}
		searchAddress[0].LevelName = append(searchAddress[0].LevelName, v)
	}

	req := admindivision.SearchDivisionListRequest{Addresses: searchAddress, UseCase: &useCaseOfMapApi}
	resp := new(admindivision.SearchDivisionListResponse)
	err := RequestSpex(ctx, country, spex_constant.NewSearchDivisionListCommand, &req, resp)
	if err != nil {
		return 0, err
	}

	if len(resp.Addresses) == 0 {
		msg := fmt.Sprintf("get spex success, but seasrch result is empty")
		logger.CtxLogErrorf(ctx, msg)
		ReportSPEXBusinessError(ctx, "SpexService", spex_constant.NewSearchDivisionListCommand, spex_constant.ErrorInternal, msg)
		return 0, lcos_error.NewLCOSError(lcos_error.SpexCommonError, msg)
	}

	addr := resp.Addresses[0]
	if addr.ErrCode != nil && *addr.ErrCode != 0 {
		msg := fmt.Sprintf("get location division id of %v failed, %v", req.Addresses[0].LevelName, *addr.ErrCode)
		logger.CtxLogErrorf(ctx, msg)
		//错误上报(业务错误)
		ReportSPEXBusinessError(ctx, "SpexService", spex_constant.NewSearchDivisionListCommand, spex_constant.ErrorInternal, msg)
		return 0, lcos_error.NewLCOSError(lcos_error.SpexCommonError, msg)
	}
	return *addr.Id, nil
}

func (s *spexService) SearchLocationDivisionID(ctx utils.LCOSContext, country, state, city, district, street string) (uint64, *lcos_error.LCOSError) {
	//SPLN-28639去掉老接口，以及老的开关配置
	return s.searchLocationDivisionIDByNewAPI(ctx, country, state, city, district, street)
}

func (s *spexService) GetBranchInfo(ctx utils.LCOSContext, region string, branchType uint32, branchRef string) (*location_branch.GetBranchInfoResponse, *lcos_error.LCOSError) {
	request := &location_branch.GetBranchInfoRequest{
		BranchType: &branchType,
		BranchRef:  &branchRef,
	}
	resp := new(location_branch.GetBranchInfoResponse)
	err := RequestSpex(ctx, region, spex_constant.GetBranchInfoCommand, request, resp)
	return resp, err
}

func (s *spexService) GetBranchListByType(ctx utils.LCOSContext, region string, branchType int) ([]*location_branch.LocationBranchInfo, *lcos_error.LCOSError) {
	resp := new(location_branch.GetBranchListByTypeResponse)
	brType := uint32(branchType)
	err := RequestSpex(ctx, region, spex_constant.GetBrancListByTypeCommand, &location_branch.GetBranchListByTypeRequest{
		BranchType: &brType,
	}, resp)
	if err != nil {
		return nil, err
	}
	return resp.BranchInfos, nil
}

func (s *spexService) UpdateBranchByRemoteBranch(ctx utils.LCOSContext, region string, branchInfo *location_branch.LocationBranchInfo) (*location_branch.UpdateBranchInfoResponse, *lcos_error.LCOSError) {
	resp := new(location_branch.UpdateBranchInfoResponse)
	//status := uint32(branchInfo.BranchStatus)
	request := &location_branch.UpdateBranchInfoRequest{
		Id:                 branchInfo.Id,
		BranchType:         branchInfo.BranchType,
		BranchRef:          branchInfo.BranchRef,
		BranchName:         branchInfo.BranchName,
		BranchAddress:      branchInfo.BranchAddress,
		Status:             branchInfo.Status,
		ExtraData:          branchInfo.ExtraData,
		PostalCode:         branchInfo.PostalCode,
		Latitude:           branchInfo.Latitude,
		Longitude:          branchInfo.Longitude,
		LocationDivisionId: branchInfo.LocationDivisionId,
	}
	err := RequestSpex(ctx, region, spex_constant.UpdateBranchInfoCommand, request, resp)
	return resp, err
}

func (s *spexService) RoutingMatrix(ctx utils.LCOSContext, lat, lon float64, region string, branches []*branch_info.LogisticBranchInfoTab, avoid bool) (*geo_routing.MatrixResponse, *lcos_error.LCOSError) {
	source := "geo"
	mode := "car"
	defaultAvoid := []string{
		"toll", "highway", "ferry",
	}

	cfg := &config.GetConf(ctx).SpexConfig
	request := &geo_routing.MatrixRequest{
		Origins: []*geo_routing.Location{{Lat: &lat, Lon: &lon}},
		//Destinations:  []*geo_routing.Location{},
		Source:    &source,
		Mode:      &mode,
		Region:    &region,
		User:      &cfg.GeoRoutingUser,
		Project:   &cfg.GeoRoutingProject,
		ClientKey: &cfg.GeoRoutingKey,
	}
	if avoid {
		request.Avoid = defaultAvoid
	}

	for _, v := range branches {
		bLat, _ := strconv.ParseFloat(v.Latitude, 64)
		bLon, _ := strconv.ParseFloat(v.Longitude, 64)
		request.Destinations = append(request.Destinations, &geo_routing.Location{Lat: &bLat, Lon: &bLon})
	}

	resp := &geo_routing.MatrixResponse{}
	err := RequestSpex(ctx, region, spex_constant.RoutingMatricCommand, request, resp)
	return resp, err
}

// ReportSPEXBusinessError 上报业务错误
func ReportSPEXBusinessError(ctx utils.LCOSContext, moduleName, command string, errCode uint32, errMessage string) {
	reportCtx := monitoring.WithTransactionMsg(ctx)
	ok := monitor.AwesomeReportTransactionEnd(reportCtx, moduleName, command, strconv.FormatUint(uint64(errCode), 10), fmt.Sprintf("command=%s, err=%+v", spex_constant.NewSearchDivisionListCommand, errMessage))
	if !ok {
		logger.CtxLogErrorf(ctx, "report spex business error failed")
	}
}

func (s *spexService) GetOrderList(ctx utils.LCOSContext, req *order_order_info.GetOrderListByIdListRequest, region string) (*order_order_info.GetOrderListByIdListResponse, *lcos_error.LCOSError) {
	resp := new(order_order_info.GetOrderListByIdListResponse)
	err := RequestSpex(ctx, region, spex_constant.OrderListCommand, req, resp)
	return resp, err
}

func (s *spexService) GetOrderListByOrderSN(ctx utils.LCOSContext, req *order_order_info.GetOrderListBySnListRequest, region string) (*order_order_info.GetOrderListBySnListResponse, *lcos_error.LCOSError) {
	resp := new(order_order_info.GetOrderListBySnListResponse)
	err := RequestSpex(ctx, region, spex_constant.GetOrderListBySnListCommand, req, resp)
	return resp, err
}
