package spex_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/scsps"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/spex_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
)

// const defaultTimeout = 5 * time.Second

func RequestSpex(ctx context.Context, cid, command string, request, response interface{}) *lcos_error.LCOSError {
	reportCtx := monitoring.WithTransactionMsg(ctx)
	timeout := config.GetSpexTimeout(ctx, command)
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	reqCtx, err := viewercontext.WithCID(timeoutCtx, strings.ToLower(cid))
	if err != nil {
		cancel()
		errMsg := fmt.Sprintf("Generate CID context failed, error:%v", err)
		monitor.AwesomeReportTransactionEnd(
			reportCtx, "SpexService", string(command),
			fmt.Sprintf("%d", lcos_error.SchemaParamsErrorCode),
			fmt.Sprintf("command=%s, req=%+v, err=%v", command, request, errMsg))
		return &lcos_error.LCOSError{RetCode: lcos_error.SchemaParamsErrorCode, Msg: fmt.Sprintf("viewercontext.WithCID error: %s", err.Error())}
	}
	respCode := scsps.RPCRequest(reqCtx, command, request, response)
	cancel()
	if respCode == 0 {
		logger.CtxLogInfof(reqCtx, "spex command=%s, req=%+v, response=%+v, timeout=%v", command, request, response, timeout)
		monitor.AwesomeReportTransactionEnd(reportCtx, constant.CatModuleSpex, command, "0", fmt.Sprintf("command=%s, req=%+v", command, request))
		return nil
	}

	msg, ok := spex_constant.SpexErrorCodeMap[respCode]

	if !ok {
		msg = "spex request unknown err"
	}
	logger.CtxLogErrorf(ctx, "spex command=%s, req=%+v, response=%+v, code=%v, timeout=%v", command, request, response, respCode, timeout)
	monitor.AwesomeReportTransactionEnd(
		reportCtx, constant.CatModuleSpex, command,
		strconv.FormatUint(uint64(respCode), 10), fmt.Sprintf("command=%s, req=%+v, err=%v", command, request, msg))

	return lcos_error.NewLCOSError(lcos_error.SpexCommonError, msg)
}
