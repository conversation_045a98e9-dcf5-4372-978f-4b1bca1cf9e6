package oms_service

import (
	"fmt"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/jwt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"golang.org/x/net/context"
	"strings"
)

func getEndPointFullUrl(ctx context.Context, country string) string {
	country = strings.ToLower(country)
	env := config.GetEnv(ctx)
	//cid := strings.ToLower(envvar.GetCID())
	host := ""
	if !(env == config.LIVE || env == config.LIVEISH) {
		if env == config.LOCAL {
			env = config.TEST
		}
		if env == config.STABLE {
			env = "test-stable"
		}
		embedEnv := "." + strings.ToLower(env)
		host = fmt.Sprintf(EnvHosts[country], embedEnv)
	} else {
		host = fmt.Sprintf(EnvHosts[country], "")
	}
	return host
}

func genJwtToken(ctx context.Context) (string, error) {
	currentTime := utils.GetTimestamp(ctx)
	optr := strings.ToLower(OmsCallerName)
	header := map[string]interface{}{
		"alg":  JtwAlg,
		"optr": optr,
	}
	payload := map[string]interface{}{
		"exp": currentTime + 60,
		"typ": currentTime,
	}
	secret := config.GetThirdServiceSecretConfig(ctx).Oms
	jwtToken, err := jwt.GetJwtDataV2(secret, header, payload)
	return jwtToken, err
}
