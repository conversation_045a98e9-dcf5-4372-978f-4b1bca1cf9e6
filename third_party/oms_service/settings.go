package oms_service

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
)

var (
	EnvHosts = map[string]string{
		"id":       "https://oms%s.shopee.co.id/",
		"my":       "https://oms%s.shopee.com.my/",
		"ph":       "https://oms%s.shopee.ph/",
		"sg":       "https://oms%s.shopee.sg/",
		"th":       "https://oms%s.shopee.co.th/",
		"tw":       "https://oms%s.shopee.tw/",
		"vn":       "https://oms%s.shopee.vn/",
		"mx":       "https://oms%s.shopee.com.mx/",
		"br":       "https://oms%s.shopee.com.br/",
		"co":       "https://oms%s.shopee.com.co/",
		"cl":       "https://oms%s.shopee.cl/",
		"mock":     "https://http-mock.ssc.test.shopeemobile.com/",
		"oms-base": "https://oms-base%s.shopeemobile.com/",
		"ar":       "https://oms%s.shopee.com.ar/",
		"pl":       "https://oms%s.shopee.pl/",
		"es":       "https://oms%s.shopee.es/",
		"fr":       "https://oms%s.shopee.fr/",
		"in":       "https://oms%s.shopee.in/",
	}
	httpClient *chassis.RestInvoker

	ApiUpdateTrackingNumber = "api/sls/update_forder_logistic_info"

	OmsCallerName = "SLS"
	JtwAlg        = "HS256"

	OMSIgnoreErrorCode int32 = 14001006
)

func init() {
	httpClient = chassis.NewRestInvoker()
}
