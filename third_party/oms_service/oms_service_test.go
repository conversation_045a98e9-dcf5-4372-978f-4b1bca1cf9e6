package oms_service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
)

func Test_omsService_UpdateCdtInfo(t *testing.T) {

	t.Skip()
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	type fields struct {
		url string
	}
	type args struct {
		ctx utils.LCOSContext
		req *UpdateCdtInfoReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		{
			name:   "test push oms cdt info",
			fields: fields{},
			args: args{
				ctx: utils.NewCommonCtx(context.WithValue(context.Background(), "logid", "11111111111111111111111111111111")),
				req: &UpdateCdtInfoReq{
					ForderId:              11111111111111111,
					Country:               "MY",
					LastMileTn:            "dddddddddddddddddd",
					LastMileInboundDate:   1111111,
					LastMileCdtMax:        10,
					LastMileCdtHolidayExt: 10,
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := NewOmsService(ctx, "MY")
			if got := o.UpdateCdtInfo(tt.args.ctx, tt.args.req); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateCdtInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}
