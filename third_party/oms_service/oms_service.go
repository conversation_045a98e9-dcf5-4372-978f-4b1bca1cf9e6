package oms_service

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/net/context"
	"strconv"
	"time"
)

type omsService struct {
	Url        string
	Timeout    int
	RetryTimes int
}

func NewOmsService(ctx context.Context, region string) *omsService {
	// check if configged
	url := config.GetOmsServiceConfig(ctx).Url
	if len(url) == 0 {
		url = getEndPointFullUrl(ctx, region)
	}
	return &omsService{
		Url:        url,
		Timeout:    config.GetOmsServiceConfig(ctx).Timeout,
		RetryTimes: config.GetOmsServiceConfig(ctx).RetryTimes,
	}
}

func (o *omsService) UpdateCdtInfo(ctx utils.LCOSContext, req *UpdateCdtInfoReq) *lcos_error.LCOSError {

	reqStr, _ := jsoniter.MarshalToString(req)

	resp := new(UpdateCdtInfoResp)

	jwtToken, err := genJwtToken(ctx)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "cannot gen jwt token")
	}

	headerMap := map[string]string{
		"request-id": ctx.GetRequestId(),
		"jwt-token":  jwtToken,
	}

	lcosErr := http.SendPostHttpRequest(ctx, httpClient, time.Duration(o.Timeout)*time.Second, o.Url+ApiUpdateTrackingNumber, req, resp, headerMap)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "requesting oms error|req=[%s]|error=[%s]", reqStr, lcosErr.Msg)
		return lcosErr
	}
	if resp.Header == nil {
		logger.CtxLogErrorf(ctx, "requesting oms error|req=[%s]|error=[%s]", reqStr, "resp header is nil from oms")
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "resp header is nil from oms")
	}
	if resp.Header.Retcode != lcos_error.SuccessCode {
		logger.CtxLogErrorf(ctx, "requesting oms error|req=[%s]|error_code=[%s]|error_message=[%s]", reqStr, resp.Header.Retcode, resp.Header.Message)
		return lcos_error.NewLCOSError(resp.Header.Retcode, resp.Header.Message)
	}

	respStr, _ := jsoniter.MarshalToString(resp)
	logger.CtxLogInfof(ctx, "requesting oms success|req=[%s]|req=[%s]", reqStr, respStr)
	return nil
}

func (o *omsService) UpdateEDDInfo(ctx utils.LCOSContext, req *UpdateEddInfoReq) *lcos_error.LCOSError {

	reqStr, _ := jsoniter.MarshalToString(req)

	resp := new(UpdateCdtInfoResp)

	jwtToken, err := genJwtToken(ctx)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "cannot gen jwt token")
	}

	headerMap := map[string]string{
		"request-id": ctx.GetRequestId(),
		"jwt-token":  jwtToken,
	}

	// retry to ensure success
	_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)
	lcosErr := http.SendPostHttpRequest(ctx, httpClient, time.Duration(o.Timeout)*time.Second, o.Url+ApiUpdateTrackingNumber, req, resp, headerMap)
	if lcosErr == nil && resp.Header != nil {
		respStr, _ := jsoniter.MarshalToString(resp)
		logger.CtxLogInfof(ctx, "requesting oms success|req=[%s]|req=[%s]", reqStr, respStr)
		if resp.Header.Retcode == lcos_error.SuccessCode {
			endFunc(constant.CatOMS, ApiUpdateTrackingNumber, constant.StatusSuccess, "")
			return nil
		} else {
			endFunc(constant.CatOMS, ApiUpdateTrackingNumber, strconv.Itoa(int(resp.Header.Retcode)), resp.Header.Message)
			if resp.Header.Retcode == OMSIgnoreErrorCode { // will not consider as error
				return nil
			} else {
				return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.Header.Message)
			}
		}
	} else {
		var errMsg string
		if lcosErr != nil {
			errMsg = lcosErr.Msg
			logger.CtxLogErrorf(ctx, "requesting oms error|req=[%s]|error=[%s]", reqStr, errMsg)
		} else {
			errMsg = "resp header is nil"
			logger.CtxLogErrorf(ctx, "requesting oms error|req=[%s]|error=[%s]", reqStr, errMsg)
		}
		endFunc(constant.CatOMS, ApiUpdateTrackingNumber, constant.StatusError, errMsg)
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
}
