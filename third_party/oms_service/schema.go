package oms_service

type RespHeader struct {
	SeqId string `json:"seq_id,omitempty"`
	// TODO: pointer is not neccessary
	Retcode int32  `json:"retcode,omitempty"`
	Message string `json:"message,omitempty"`
	Retty<PERSON> string `json:"rettype,omitempty"`
}

type UpdateCdtInfoReq struct {
	ForderId              uint64  `json:"forder_id"`
	Country               string  `json:"country"`
	ConsignmentNo         string  `json:"consignment_no"`
	LastMileTn            string  `json:"last_mile_tn"`
	LastMileInboundDate   uint32  `json:"last_mile_inbound_date"`
	LastMileCdtMax        float32 `json:"last_mile_cdt_max"`
	LastMileCdtHolidayExt int     `json:"last_mile_cdt_holiday_ext"`
}

type UpdateCdtInfoResp struct {
	Header *RespHeader `json:"header,omitempty"`
}

type UpdateEddInfoReq struct {
	ForderId        uint64 `json:"forder_id"`
	Country         string `json:"country"`
	ConsignmentNo   string `json:"consignment_no"`
	EddMin          int64  `json:"edd_min"`
	EddMax          int64  `json:"edd"`
	EDDUpdateReason int    `json:"edd_update_reason"`
	EddType         int    `json:"edd_type"`
}
