package product_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/channel_service"
	jsoniter "github.com/json-iterator/go"
	"os"
	"reflect"
	"testing"
)

func TestGetAllProducts(t *testing.T) {

	_ = os.Setenv("env", "test")

	result, lcosErr := GetAllProducts(context.Background(), "SG")
	if lcosErr != nil {
		t.Error(lcosErr.Msg)
	}
	fmt.Printf("%v", result)
}

func TestGetAllProductLines(t *testing.T) {

	_ = os.Setenv("env", "test")

	type args struct {
		ctx       context.Context
		productID int
		region    string
	}
	tests := []struct {
		name  string
		args  args
		want  map[string]*channel_service.Channel
		want1 *lcos_error.LCOSError
	}{
		{
			name: "test get all lines of product",
			args: args{
				ctx:       context.Background(),
				productID: 90001,
				region:    "BR",
			},
			want:  nil,
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := GetAllProductLines(tt.args.ctx, tt.args.productID, tt.args.region)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetAllProductLines() got1 = %v, want %v", got1, tt.want1)
			}
			gotStr, _ := jsoniter.MarshalToString(got)
			t.Logf("%v", gotStr)
		})
	}
}
