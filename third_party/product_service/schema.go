package product_service

import llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"

type Product struct {
	ProductID        int    `json:"product_id"`
	ProductName      string `json:"buyer_display_name"`
	Entity           uint8  `json:"entity"`
	IntegratedType   uint8  `json:"integrated_type"`
	FromRegion       string `json:"from_region"`
	Region           string `json:"to_region"`
	IsMaskingProduct bool   `json:"is_masking_product"`
	ProductFlowType  int    `json:"product_flow_type"`
}

type AllProductResponse struct {
	Retcode int32           `json:"retcode"`
	Data    *AllProductInfo `json:"data"`
	Message string          `json:"message"`
	Detail  string          `json:"detail"`
}

type AllProductInfo struct {
	List []*Product `json:"list"`
}

type AllProductLinesResponse struct {
	Retcode int32            `json:"retcode"`
	Data    *AllProductLines `json:"data"`
	Message string           `json:"message"`
	Detail  string           `json:"detail"`
}

type LineInfo struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}
type AllProductLines struct {
	LineList []*LineInfo `json:"line_list"`
}

type GetLaneCodeByProductResponse struct {
	Retcode int32                `json:"retcode"`
	Message string               `json:"message"`
	Detail  string               `json:"detail"`
	Data    *ProductLaneCodeData `json:"data"`
}

type ProductLaneCodeData struct {
	ProductList []*ProductLaneCode `json:"product_list"`
}

type ProductLaneCode struct {
	ProductId           int                `json:"product_id"`
	ProductType         string             `json:"product_type"` // external-product：单产品, multi-product：多产品
	InternalProductList []*InternalProduct `json:"internal_product_list"`
}

type InternalProduct struct {
	ProductId    string   `json:"internal_product_id"`
	ProductType  string   `json:"internal_product_type"` // FM, ILH, LM，如果是单产品则为external
	LaneCodeList []string `json:"lane_code_list"`
}

type LaneCodeInfo struct {
	IsMultiProduct bool     `json:"is_multi_product" form:"is_multi_product"`
	FM             []string `json:"fm" form:"fm"`
	LM             []string `json:"lm" form:"lm"`
	ALL            []string `json:"all" form:"all"`
}

type AllProductByLineIdResponse struct {
	Retcode int32                 `json:"retcode"`
	Data    []*AllProductByLineId `json:"data"`
	Message string                `json:"message"`
	Detail  string                `json:"detail"`
}

type AllProductByLineId struct {
	ProductBaseInfo *LogisticProductTab `json:"product_base_info"`
	LaneCodeList    []string            `json:"lane_code_list"`
	ProductStatus   int                 `json:"product_status"`
}

type LogisticProductTab struct {
	ProductId int `gorm:"column:product_id" json:"product_id"`
	//BuyerDisplayName       string  `gorm:"column:buyer_display_name" json:"buyer_display_name"`
}

type AllScenarioByProductResponse struct {
	Retcode int32     `json:"retcode"`
	Data    *ConfPage `json:"data"`
	Message string    `json:"message"`
	Detail  string    `json:"detail"`
}

type ConfPage struct {
	Offset int64                  `json:"offset"`
	Size   int64                  `json:"size"`
	Total  int64                  `json:"total"`
	List   []*GetScenarioInfoResp `json:"list"`
}

type GetScenarioInfoResp struct {
	ProductId int        `json:"product_id" struct2map:"product_id,omitempty"`
	InfoLists []InfoList `json:"details"`
}

type InfoList struct {
	Id                 int    `json:"id" validate:"omitempty"`
	Scenario           *uint8 `json:"scenario" validate:"gte=1,lte=5" struct2map:"scenario,omitempty"`
	CheckSender        *uint8 `json:"check_sender" validate:"gte=0,lte=1" struct2map:"check_sender,omitempty"`
	CheckReceiver      *uint8 `json:"check_receiver" validate:"gte=0,lte=1" struct2map:"check_receiver,omitempty"`
	SenderCheckLevel   *uint8 `json:"sender_check_level" validate:"gte=1,lte=4" struct2map:"sender_check_level,omitempty"`
	ReceiverCheckLevel *uint8 `json:"receiver_check_level" validate:"gte=1,lte=4" struct2map:"receiver_check_level,omitempty"`
	DropoffSetting     bool   `json:"dropoff_setting"`
}

type (
	GetProductSiteLineInfoResponse struct {
		Retcode int32                   `json:"retcode"`
		Data    *GetProductSiteLineInfo `json:"data"`
		Message string                  `json:"message"`
		Detail  string                  `json:"detail"`
	}

	GetProductSiteLineInfo struct {
		ProductId int                    `json:"product_id"`
		LaneList  []LaneWithSiteLineInfo `json:"lane_list"`
	}

	LaneWithSiteLineInfo struct {
		LaneCode string                     `json:"lane_code"`
		LineList []*llspb.LinesResponseData `json:"line_list"`
	}

	GetShopGroupInfoListResp struct {
		Retcode int32                   `json:"retcode"`
		Message string                  `json:"message"`
		Data    *DisplayClientGroupResp `json:"data"`
	}

	DisplayClientGroupResp struct {
		List []ClientGroupDisplay `json:"list"`
	}

	ClientGroupDisplay struct {
		ID              uint64 `json:"id"`
		ClientTagId     uint64 `json:"client_tag_id"`
		ClientTagName   string `json:"client_tag_name"`
		ClientGroupID   string `json:"client_group_id"`
		ClientGroupName string `json:"client_group_name"`
		Operator        string `json:"operator"`
		LastUpdateTime  uint   `json:"last_update_time"`
		ProductIds      []int  `json:"product_ids"`
	}
)

type (
	GetLaneCodesRequest struct {
		Token string `json:"token" form:"token"`
	}

	GetLaneCodesResponse struct {
		Retcode int64                 `json:"retcode"`
		Message string                `json:"message"`
		Detail  string                `json:"detail"`
		Data    []ProductLaneCodeList `json:"data"`
	}

	ProductLaneCodeList struct {
		ProductId   int      `json:"product_id"`
		ProductName string   `json:"product_name"`
		LaneCodes   []string `json:"lane_codes"`
	}
)

type (
	ChannelsGetRequest struct {
		Token string `json:"token" form:"token"`
	}

	ChannelsGetResponse struct {
		Retcode int32  `json:"retcode"`
		Message string `json:"message"`
		Detail  string `json:"detail"`
		Data    struct {
			Channels []*ChannelInfo `json:"channels"`
		} `json:"data"`
	}

	// ChannelInfo 渠道信息。实际返回的包体很大，可根据需要增加字段。可参考：https://apidoc.i.ssc.shopeemobile.com/project/667/interface/api/107143
	ChannelInfo struct {
		ChannelId   int    `json:"channelid"`
		Name        string `json:"name"`
		FromCountry string `json:"from_country"` // 渠道的起始国家
		Country     string `json:"country"`      // 渠道的目的国家
	}

	// ProductInfo 用于本地缓存的结构体，跟http接口返回的结构区分，只缓存需要的字段
	ProductInfo struct {
		ProductId   int    `json:"product_id"`
		Name        string `json:"name"`
		FromCountry string `json:"from_country"`
		Country     string `json:"country"`
	}
)
