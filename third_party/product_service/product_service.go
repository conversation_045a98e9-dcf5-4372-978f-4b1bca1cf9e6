package product_service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	http_utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/channel_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	jsoniter "github.com/json-iterator/go"
)

const (
	SingleProductType = "external-product"
	MultiProductType  = "multi-product"
	FM                = "CB_FM"
	LM                = "CB_DOMESTIC"
	FL                = "CB_LH"
)

var invoker = chassis.NewRestInvoker()

func GetAllProducts(ctx context.Context, region string) (map[string]*channel_service.Channel, *lcos_error.LCOSError) {
	env := utils.GetEnv(ctx)
	url := GetUrl(env, region)
	data := map[string]interface{}{}
	paramByte, err := jsoniter.Marshal(channel_service.GenerateJwtInfo(ctx, data, region))
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	responseByte, lcosErr := channel_service.SendHeaderJwt(ctx, data, url+LISTALLPRODUCTAPI, region, "GET", account, cf.GetThirdServiceSecretConfig(ctx).Product)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|error=%v", LISTALLPRODUCTAPI, lcosErr.Msg)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, lcosErr.Msg)
	}

	response := &AllProductResponse{}
	if err := jsoniter.Unmarshal(responseByte, response); err != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|error=%v", LISTALLPRODUCTAPI, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if response.Retcode != lcos_error.SuccessCode {
		errMsg := response.Message + ", " + response.Detail + ", param: " + string(paramByte)
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|error=%v", LISTALLPRODUCTAPI, errMsg)
		return nil, lcos_error.NewLCOSError(ops_service.LocationCodeMap[response.Retcode], errMsg)
	}

	resultMap := map[string]*channel_service.Channel{}
	if response.Data == nil {
		return nil, nil
	}
	productList := response.Data.List
	for _, product := range productList {
		var maskingType uint8
		var cbType uint8
		if product.IsMaskingProduct {
			maskingType = 1
		} else {
			maskingType = 0
		}
		if product.Entity == 1 {
			cbType = 0
		} else {
			cbType = 1
		}
		resultMap[strconv.Itoa(product.ProductID)] = &channel_service.Channel{
			ChannelID:      product.ProductID,
			ChannelName:    product.ProductName,
			MaskingType:    maskingType,
			IntegratedType: 1,
			FromCountry:    product.FromRegion,
			Country:        product.Region,
			CBType:         cbType,
			FlowType:       product.ProductFlowType,
		}
	}

	resultMapStr, _ := jsoniter.MarshalToString(resultMap)
	logger.CtxLogInfof(ctx, "requesting lps admin success|api=%s|resp=[%v]", LISTALLPRODUCTAPI, resultMapStr)
	return resultMap, nil
}

// GetAllProductLines
// get all product lines belong to current product
func GetAllProductLines(ctx context.Context, productID int, region string) (*AllProductLines, *lcos_error.LCOSError) {
	env := utils.GetEnv(ctx)
	url := GetUrl(env, region)
	data := map[string]interface{}{
		"product_id":     productID,
		"product_status": ActiveStatusProduct,
	}

	dataStr, _ := jsoniter.MarshalToString(data)

	paramByte, err := jsoniter.Marshal(channel_service.GenerateJwtInfo(ctx, data, region))
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	responseByte, lcosErr := channel_service.SendHeaderJwt(ctx, data, url+GETLINELISTAPI, region, "GET", account, cf.GetThirdServiceSecretConfig(ctx).Product)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETLINELISTAPI, dataStr, lcosErr.Msg)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, lcosErr.Msg)
	}

	response := &AllProductLinesResponse{}
	if err := jsoniter.Unmarshal(responseByte, response); err != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETLINELISTAPI, dataStr, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if response.Retcode != lcos_error.SuccessCode {
		errMsg := response.Message + ", " + response.Detail + ", param: " + string(paramByte)
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETLINELISTAPI, dataStr, errMsg)
		return nil, lcos_error.NewLCOSError(ops_service.LocationCodeMap[response.Retcode], errMsg)
	}
	return response.Data, nil
}

func GetLaneCodeInfoByProductId(ctx context.Context, region string, productId int) (*LaneCodeInfo, *lcos_error.LCOSError) {
	env := utils.GetEnv(ctx)
	url := GetApiUrl(env, region)
	data := map[string]interface{}{
		"product_id": productId,
	}
	responseByte, lcosErr := channel_service.SendHeaderJwt(ctx, data, url+"/api/v3/logistics/product/list_lane_code", region, "POST", account, cf.GetThirdServiceSecretConfig(ctx).Product)
	if lcosErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, lcosErr.Msg)
	}

	response := &GetLaneCodeByProductResponse{}
	if err := json.Unmarshal(responseByte, response); err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "get lane code from lps error:%s", err.Error())
	}

	if response.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, response.Message)
	}
	if len(response.Data.ProductList) == 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "product %d not exists", productId)
	}
	res := &LaneCodeInfo{}
	productInfo := response.Data.ProductList[0]
	if len(productInfo.InternalProductList) == 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "get lane code from lps error:internal product list is empty")
	}
	if productInfo.ProductType == MultiProductType {
		res.IsMultiProduct = true
		for _, internalProduct := range productInfo.InternalProductList {
			res.ALL = append(res.ALL, internalProduct.LaneCodeList...)
			switch internalProduct.ProductType {
			case FM:
				res.FM = append(res.FM, internalProduct.LaneCodeList...)
			case LM:
				res.LM = append(res.LM, internalProduct.LaneCodeList...)
			}
		}
	} else {
		res.IsMultiProduct = false
		res.ALL = productInfo.InternalProductList[0].LaneCodeList
		multiLayerLaneCodeGroup, err := lfs_service.NewLFSService(ctx, region).GetMultiLayerLaneCodeGroup(utils.NewCommonCtx(ctx), res.ALL)
		if err != nil {
			return nil, err
		}
		if multiLayerLaneCodeGroup.IsMultiLayer {
			res.IsMultiProduct = true
			res.FM = multiLayerLaneCodeGroup.FM
			res.LM = multiLayerLaneCodeGroup.LM
		}
	}
	return res, nil
}

func GetScenarioByProduct(ctx context.Context, ProductID int, region string) (*ConfPage, *lcos_error.LCOSError) {
	env := utils.GetEnv(ctx)
	url := GetUrl(env, region)
	data := map[string]interface{}{
		"product_id": ProductID,
		"offset":     0,
		"size":       10,
	}

	dataStr, _ := jsoniter.MarshalToString(data)

	paramByte, err := jsoniter.Marshal(channel_service.GenerateJwtInfo(ctx, data, region))
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	responseByte, lcosErr := channel_service.SendHeaderJwt(ctx, data, url+GETSCENARIO, region, "GET", account, cf.GetThirdServiceSecretConfig(ctx).Product)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETSCENARIO, dataStr, lcosErr.Msg)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, lcosErr.Msg)
	}

	response := &AllScenarioByProductResponse{}
	if err := jsoniter.Unmarshal(responseByte, response); err != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETSCENARIO, dataStr, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if response.Retcode != lcos_error.SuccessCode {
		errMsg := response.Message + ", " + response.Detail + ", param: " + string(paramByte)
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETSCENARIO, dataStr, errMsg)
		return nil, lcos_error.NewLCOSError(ops_service.LocationCodeMap[response.Retcode], errMsg)
	}
	return response.Data, nil
}

func GetLaneAndLineInfoByProduct(ctx context.Context, ProductID int, region string) (*GetProductSiteLineInfo, *lcos_error.LCOSError) {
	env := utils.GetEnv(ctx)
	url := GetUrl(env, region)
	data := map[string]interface{}{
		"product_id": ProductID,
	}

	dataStr, _ := jsoniter.MarshalToString(data)

	paramByte, err := jsoniter.Marshal(channel_service.GenerateJwtInfo(ctx, data, region))
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	responseByte, lcosErr := channel_service.SendHeaderJwt(ctx, data, url+GETSITELINEINFO, region, "POST", account, cf.GetThirdServiceSecretConfig(ctx).Product)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETSITELINEINFO, dataStr, lcosErr.Msg)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, lcosErr.Msg)
	}

	response := &GetProductSiteLineInfoResponse{}
	if err := jsoniter.Unmarshal(responseByte, response); err != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETSITELINEINFO, dataStr, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if response.Retcode != lcos_error.SuccessCode {
		errMsg := response.Message + ", " + response.Detail + ", param: " + string(paramByte)
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETSITELINEINFO, dataStr, errMsg)
		return nil, lcos_error.NewLCOSError(ops_service.LocationCodeMap[response.Retcode], errMsg)
	}
	return response.Data, nil
}

func ListLaneCodesByRegion(ctx utils.LCOSContext, region string) ([]ProductLaneCodeList, *lcos_error.LCOSError) {
	env := utils.GetEnv(ctx)
	url := GetApiUrl(env, region)

	conf := cf.GetMutableConf(ctx).LpsApiServiceConfig
	req := &GetLaneCodesRequest{
		Token: conf.Token,
	}
	var resp GetLaneCodesResponse
	if err := http_utils.SendPostHttpRequest(ctx, invoker, conf.GetTimeout(), url+GETLANECODES, req, &resp, nil); err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "send http request error|api=%s|error=%s", GETLANECODES, err.Msg)
	}
	if resp.Retcode != 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "lps api returns error|api=%s|error=%s", GETLANECODES, resp.Message)
	}
	return resp.Data, nil
}

func GetAllShopGroupsByTag(ctx context.Context, region string, clientTagId ClientTag) (*DisplayClientGroupResp, *lcos_error.LCOSError) {
	env := utils.GetEnv(ctx)
	url := GetUrl(env, region)
	data := map[string]interface{}{
		"client_tag_id": clientTagId,
	}

	dataStr, _ := jsoniter.MarshalToString(data)

	paramByte, err := jsoniter.Marshal(channel_service.GenerateJwtInfo(ctx, data, region))
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	responseByte, lcosErr := channel_service.SendHeaderJwt(ctx, data, url+GetClientGroupByClientTag, region, "GET", account, cf.GetThirdServiceSecretConfig(ctx).Product)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETSITELINEINFO, dataStr, lcosErr.Msg)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, lcosErr.Msg)
	}

	response := &GetShopGroupInfoListResp{}
	if err := jsoniter.Unmarshal(responseByte, response); err != nil {
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETSITELINEINFO, dataStr, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if response.Retcode != lcos_error.SuccessCode {
		errMsg := response.Message + ", param: " + string(paramByte)
		logger.CtxLogErrorf(ctx, "requesting lps admin error|api=%s|req=[%s]|error=%v", GETSITELINEINFO, dataStr, errMsg)
		return nil, lcos_error.NewLCOSError(ops_service.LocationCodeMap[response.Retcode], errMsg)
	}
	return response.Data, nil
}

func ConvertToShopGroupMap(resp *DisplayClientGroupResp) map[string]struct{} {
	result := make(map[string]struct{}, 0)
	if resp == nil || len(resp.List) == 0 {
		return result
	}

	for _, tab := range resp.List {
		for _, productId := range tab.ProductIds {
			result[FormatShopGroupKey(tab.ClientGroupID, strconv.FormatInt(int64(productId), 10))] = struct{}{}
		}
	}

	return result
}

func FormatShopGroupKey(clientGroupId, productId string) string {
	return fmt.Sprintf("%s:%s", clientGroupId, productId)
}

// GetAllChannelsInfoByRegion 调用lps-api获取市场下的所有渠道信息
func GetAllChannelsInfoByRegion(ctx utils.LCOSContext, region string) ([]*ChannelInfo, *lcos_error.LCOSError) {
	env := utils.GetEnv(ctx)
	url := GetApiUrl(env, region)

	conf := cf.GetMutableConf(ctx).LpsApiServiceConfig
	req := &ChannelsGetRequest{
		Token: conf.Token,
	}
	var resp ChannelsGetResponse
	if err := http_utils.SendPostHttpRequest(ctx, invoker, conf.GetTimeout(), url+CHANNELSGETAPI, req, &resp, nil); err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "send http request error|api=%s|error=%s", CHANNELSGETAPI, err.Msg)
	}
	if resp.Retcode != 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "lps api returns error|api=%s|error=%s", CHANNELSGETAPI, resp.Message)
	}
	return resp.Data.Channels, nil
}

func GetProductInfoByProductIdWithLocalCache(ctx context.Context, productId string) (*ProductInfo, *lcos_error.LCOSError) {
	executor := localcache.NewLocalCacheQueryExecutor()
	obj, err := executor.Find(ctx, constant.LogisticProductInfoNamespace, productId)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, err.Error())
	}
	if obj == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, "product not found")
	}
	channel, ok := obj.(*ProductInfo)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, "product not found")
	}
	return channel, nil
}
