package spx_service

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	http_utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/region_tools"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"golang.org/x/net/context"
	"time"
)

var restInvoker = chassis.NewRestInvoker()

type SpxFleetOrderService struct {
	Host string
}

func NewSpxFleetOrderService(ctx context.Context, cid string) *SpxFleetOrderService {
	var host string

	cfg := config.GetMutableConf(ctx).SpxFleetOrderServiceConfig
	if cfg.UseMock {
		host = cfg.MockHost // use mock sever
	} else {
		suffix := region_tools.GetCountrySuffix(cid)
		if len(suffix) == 0 {
			suffix = region_tools.GetCountrySuffix("SG")
		}
		host = cfg.Host + suffix
	}

	return &SpxFleetOrderService{
		Host: host,
	}
}

func (s *SpxFleetOrderService) getTimeout(ctx context.Context) time.Duration {
	timeout := config.GetMutableConf(ctx).SpxFleetOrderServiceConfig.Timeout
	if timeout <= 0 {
		return time.Second
	}
	return time.Duration(timeout) * time.Millisecond
}

func (s *SpxFleetOrderService) GetAllOrderAccount(ctx utils.LCOSContext) ([]*OrderAccountEnum, *lcos_error.LCOSError) {
	var resp GetOrderAccountResponse
	if err := http_utils.SendGetHttpRequest(ctx, restInvoker, s.Host+apiGetOrderAccount, nil, &resp, nil); err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.HTTPRequestErrorCode, "invoke spx fleet order error: %s", err.Msg)
	}

	if resp.Retcode != 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "spx fleet order return error: %s", resp.Message)
	}
	return resp.Data.OrderAccountList, nil
}

func (s *SpxFleetOrderService) GetOrderAccountById(ctx utils.LCOSContext, orderAccountId int) (*OrderAccountEnum, *lcos_error.LCOSError) {
	orderAccountList, err := s.GetAllOrderAccount(ctx)
	if err != nil {
		return nil, err
	}
	for _, enum := range orderAccountList {
		if enum.OrderAccountId == orderAccountId {
			return enum, nil
		}
	}
	return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "order account not found")
}

type SpxServiceIntegrationService struct {
	Host string
}

func NewSpxServiceIntegrationService(ctx context.Context, cid string) *SpxServiceIntegrationService {
	var host string

	cfg := config.GetMutableConf(ctx).SpxServiceIntegrationServiceConfig
	if cfg.UseMock {
		host = cfg.MockHost // use mock sever
	} else {
		suffix := region_tools.GetCountrySuffix(cid)
		if len(suffix) == 0 {
			suffix = region_tools.GetCountrySuffix("SG")
		}
		host = cfg.Host + suffix
	}

	return &SpxServiceIntegrationService{
		Host: host,
	}
}

func (s *SpxServiceIntegrationService) getTimeout(ctx context.Context) time.Duration {
	timeout := config.GetMutableConf(ctx).SpxServiceIntegrationServiceConfig.Timeout
	if timeout <= 0 {
		return time.Second
	}
	return time.Duration(timeout) * time.Millisecond
}

func (s *SpxServiceIntegrationService) GetServiceableAreaScheduledPlanning(ctx utils.LCOSContext) ([]ServiceableAreaScheduledPlanning, *lcos_error.LCOSError) {
	var resp GetServiceableAreaScheduledPlanningResponse
	if err := http_utils.SendPostHttpRequest(ctx, restInvoker, s.getTimeout(ctx), s.Host+apiGetSchedulePlanning, nil, &resp, nil); err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.HTTPRequestErrorCode, "invoke spx service integration error: %s", err.Msg)
	}
	return resp.Data, nil
}

func (s *SpxServiceIntegrationService) GetServiceableAreaByPaging(ctx utils.LCOSContext, planningId uint64, pageNo, pageSize uint32) ([]*ServiceableArea, int, *lcos_error.LCOSError) {
	req := &GetServiceableAreaRequest{
		PlanningId: planningId,
		PageNo:     pageNo,
		Count:      pageSize,
	}

	var resp GetServiceableAreaResponse
	var err *lcos_error.LCOSError
	for i := 0; i < int(config.GetRequestMaxRetryTimes(ctx))+1; i++ {
		if err = http_utils.SendPostHttpRequest(ctx, restInvoker, s.getTimeout(ctx), s.Host+apiGetServiceableArea, req, &resp, nil); err == nil {
			break
		}
	}
	if err != nil {
		return nil, 0, lcos_error.NewLCOSErrorf(lcos_error.HTTPRequestErrorCode, "invoke spx service integration error: %s", err.Msg)
	}

	if resp.Retcode != 0 {
		return nil, 0, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "spx service integration return error: %s", resp.Data.Message)
	}
	return resp.Data.Data.List, resp.Data.Data.Total, nil
}
