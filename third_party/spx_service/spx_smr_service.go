package spx_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	spxPb "git.garena.com/shopee/bg-logistics/spx/sorting/smart-sorting-protobuf/zone_facade_protobuf"
	"strings"
)

var invoker = chassis.NewRPCInvoker()

const SpxSmrServiceNamePattern = "smart-sorting-%s-%s"

type SpxSmrService struct {
	serviceName string
}

func NewSpxSmrService(ctx utils.LCOSContext, region string) *SpxSmrService {
	env := utils.GetEnv(ctx)
	serviceName := fmt.Sprintf(SpxSmrServiceNamePattern, strings.ToLower(env), strings.ToLower(region))
	return &SpxSmrService{serviceName: serviceName}
}

func (s *SpxSmrService) GetCustomizedZoneDetail(ctx utils.LCOSContext, zoneIdList []*spxPb.ZoneDetailParamItem) ([]*spxPb.CustomizedZoneDetail, *lcos_error.LCOSError) {
	req := &spxPb.GetCustomizedZoneDetailReq{
		ZoneIdList: zoneIdList,
	}
	resp := &spxPb.GetCustomizedZoneDetailRsp{}

	if err := invoker.Invoke(ctx, s.serviceName, ZoneDetailSchemaID, ZoneDetailOperationID, req, resp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting spx error|schema_id=%s|api=%s|req=[%+v]|error=%v", ZoneDetailSchemaID, ZoneDetailOperationID, zoneIdList, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if resp.GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting spx fail|schema_id=%s|api=%s|req=[%+v]|retcode=%d", ZoneDetailSchemaID, ZoneDetailOperationID, zoneIdList, resp.GetRetcode())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.GetMessage())
	}

	return resp.GetZoneList(), nil
}
