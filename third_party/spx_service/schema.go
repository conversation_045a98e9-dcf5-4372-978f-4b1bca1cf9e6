package spx_service

type GetOrderAccountResponse struct {
	Retcode int    `json:"retcode"`
	Message string `json:"message"`
	Data    struct {
		OrderAccountList []*OrderAccountEnum `json:"order_account_list"`
	} `json:"data"`
}

type OrderAccountEnum struct {
	OrderAccountId   int    `json:"order_account_id"`
	OrderAccountName string `json:"order_account_name"`
}

type GetServiceableAreaScheduledPlanningResponse struct {
	Retcode int                                `json:"retcode"`
	Message string                             `json:"message"`
	Data    []ServiceableAreaScheduledPlanning `json:"data"`
}

type ServiceableAreaScheduledPlanning struct {
	Region             string `json:"region"`
	PlanningId         uint64 `json:"planning_id"`
	PlanningName       string `json:"planning_name"`
	ActiveTime         uint32 `json:"active_time"`
	CopyFrom           uint64 `json:"copy_from"`
	CopyFromActiveFlag bool   `json:"copy_from_active_flag"`
	ModifyFlag         bool   `json:"modify_flag"`
}

type GetServiceableAreaRequest struct {
	PlanningId uint64 `json:"planning_id"`
	PageNo     uint32 `json:"pageno"`
	Count      uint32 `json:"count"`
}

type GetServiceableAreaResponse struct {
	Retcode int    `json:"retcode"`
	Message string `json:"message"`
	Data    struct {
		Message string `json:"message"`
		Data    struct {
			Total int                `json:"total"`
			List  []*ServiceableArea `json:"list"`
		} `json:"data"`
	} `json:"data"`
}

type ServiceableArea struct {
	OrderAccount string `json:"order_account"`
	State        string `json:"state"`
	City         string `json:"city"`
	District     string `json:"district"`
	Uptown       string `json:"uptown"`
	Postcode     string `json:"postcode"`
	Pickup       uint8  `json:"pickup"`
	CodPickup    uint8  `json:"cod_pickup"`
	Delivery     uint8  `json:"delivery"`
	CodDelivery  uint8  `json:"cod_delivery"`
	Md5          string `json:"md5"`
}
