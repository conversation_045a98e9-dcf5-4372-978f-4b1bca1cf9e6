package spx_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
	"git.garena.com/shopee/platform/service-governance/viewercontext/attr"
	"os"
	"path"
	"strings"
	"testing"
)

func TestGetServiceableAreaScheduledPlanning(t *testing.T) {
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "my")
	_ = os.Setenv("SSC_ENV", strings.ToLower(config.GetEnv()))

	if err := chassis.Init(); err != nil {
		t.Fatal("init chassis error:", err.Error())
	}

	if _, err := config.InitConfig(); err != nil {
		t.Fatal("init config error:", err.Error())
	}
	if _, err := config.InitMutableConfig(); err != nil {
		t.Fatal("init mutable config error:", err.Error())
	}

	ctxWithPfb, _ := viewercontext.Start(context.Background(), attr.WithPFB("pfb-dms-dev-36004-dev-joint-test"))

	service := NewSpxServiceIntegrationService("VN")
	ret, err := service.GetServiceableAreaScheduledPlanning(utils.NewCommonCtx(ctxWithPfb))
	if err != nil {
		t.Fatal("get serviceable area planning error:", err.Msg)
	}

	fmt.Println(ret)
}
