package lfs_service

import (
	"context"
	"fmt"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
	"git.garena.com/shopee/platform/service-governance/viewercontext/attr"
	jsoniter "github.com/json-iterator/go"
)

func Test_lfsService_BatchQueryLaneInfo(t *testing.T) {
	t.Skip()
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	_, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatal(err)
	}

	type fields struct {
		serviceName string
	}
	type args struct {
		ctx       utils.LCOSContext
		laneCodes []string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[string]*LaneCodeStruct
		want1  *lcos_error.LCOSError
	}{
		{
			name:   "test lfs BatchQueryLaneInfo",
			fields: fields{serviceName: "lfs-grpc-test-ph"},
			args: args{
				ctx:       utils.NewCommonCtx(context.Background()),
				laneCodes: []string{"L-PH-4/L-PH-5"},
			},
			want:  nil,
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := &lfsService{
				serviceName: tt.fields.serviceName,
			}
			_, got1 := l.GetLaneCodeMapUsingCache(tt.args.ctx, tt.args.laneCodes)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("BatchQueryLaneInfo() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_lfsService_GetLogisticOrderDataBySloTn(t *testing.T) {
	t.Skip()
	ctx := context.Background()
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "my")
	startup.InitSSCEnv()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/cdttask"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")

	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	_, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	type args struct {
		ctx   utils.LCOSContext
		sloTn string
	}
	tests := []struct {
		name  string
		args  args
		want  *LogisticOrderData
		want1 *lcos_error.LCOSError
	}{
		{
			name: "test get order info",
			args: args{
				ctx:   utils.NewCommonCtx(context.Background()),
				sloTn: "MY227359071026MT",
			},
			want:  nil,
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := NewLFSService("my")
			got, got1 := l.GetLogisticOrderDataBySloTn(tt.args.ctx, tt.args.sloTn)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetLogisticOrderDataBySloTn() got1 = %v, want %v", got1, tt.want1)
			} else {
				info, _ := jsoniter.MarshalToString(got)
				t.Logf(info)
			}
		})
	}
}

func Test_lfsService_GetMultiLayerLaneCodeGroup(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "vn")
	startup.InitSSCEnv()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/cdttask"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-haobing")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}
	_, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}
	_, err = cf.InitMutableConfig()
	if err != nil {
		t.Fatal(err)
	}

	laneCodes := []string{
		"C-CN-VN-81",
		"CL-VN-81",
		"I-CN-VN-91",
		"C-CN-VN-82",
		"C-CN-VN-83",
		"C-CN-VN-84",
		"I-CN-VN-92",
		"CL-VN-83",
		"CL-VN-84",
		"CL-VN-85",
		"I-CN-VN-2423",
		"I-CN-VN-2424",
		"CL-VN-381681",
		"CL-VN-381682",
		"CL-VN-381683",
	}
	l := NewLFSService("vn")
	ret, lcosErr := l.GetMultiLayerLaneCodeGroup(utils.NewCommonCtx(context.Background()), laneCodes)
	if lcosErr != nil {
		t.Fatal(lcosErr)
	}
	fmt.Printf("%+v", ret)
}

func Test_lfsService_GetLogisticOrderDataBySloTnForEDD(t *testing.T) {
	//t.Skip()
	// add GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn to env

	region := "th"

	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", region)
	startup.InitSSCEnv()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/cdttask"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("PFB_NAME", "PFB_NAME=pfb-dms-dev-edd")

	ctxWithPfb, _ := viewercontext.Start(context.WithValue(context.Background(), constant2.LogIDStr, "111111dawdawd111111111111111"), attr.WithPFB("pfb-dms-dev-edd"))
	ctx := utils.NewCommonCtx(ctxWithPfb)

	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	_, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}
	_, err = cf.InitMutableConfig()
	if err != nil {
		t.Fatalf("getMutableConfig %v", err)
	}

	type args struct {
		ctx   utils.LCOSContext
		sloTn string
	}
	tests := []struct {
		name  string
		args  args
		want1 *lcos_error.LCOSError
	}{
		{
			name: "test get order info",
			args: args{
				ctx:   ctx,
				sloTn: "TH233312599460ST",
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := NewLFSService(region)
			got, got1 := l.GetLogisticOrderDataBySloTnForEDD(tt.args.ctx, tt.args.sloTn)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetLogisticOrderDataBySloTnForEDD() got1 = %v, want %v", got1, tt.want1)
			} else {
				info, _ := jsoniter.MarshalToString(got)
				t.Logf(info)
			}
		})
	}
}
