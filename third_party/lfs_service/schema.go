package lfs_service

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

type SingleModelRule struct {
	MainType                   int   `json:"main_type"`
	SubType                    int   `json:"sub_type"`
	ResourceType               int   `json:"resource_type"`
	SortingAsPreDeliverFlag    int   `json:"sorting_as_pre_deliver_flag"`
	SortingAsNextPickupFlag    int   `json:"sorting_as_next_pickup_flag"`
	SiteSortingPickupAddrFlag  int   `json:"site_sorting_pickup_address_flag"`
	SiteSortingDeliverAddrFlag int   `json:"site_sorting_deliver_address_flag"`
	PreResourceSACheckFlag     int   `json:"pre_resource_sa_check_flag"`
	NextResourceSACheckFlag    int   `json:"next_resource_sa_check_flag"`
	SortingFlag                int   `json:"sorting_flag"`
	Sequence                   int   `json:"sequence"`
	CheckSender                int32 `json:"check_sender"`
	CheckReceiver              int32 `json:"check_receiver"`

	CheckSenderAddrType   int32 `json:"-"`
	CheckReceiverAddrType int32 `json:"-"`
}

func (l *SingleModelRule) IsSite() bool {
	if l.ResourceType == constant.LaneComposeSite {
		return true
	}
	return false
}

func (l *SingleModelRule) IsSiteSorting() bool {
	if l.SortingFlag == 1 {
		return true
	}
	return false
}

func (l *SingleModelRule) IsSortingAsPreDeliver() bool {
	if l.SortingAsPreDeliverFlag == 1 {
		return true
	}
	return false
}

func (l *SingleModelRule) IsSortingAsNextPickup() bool {
	if l.SortingAsNextPickupFlag == 1 {
		return true
	}
	return false
}

func (l *SingleModelRule) IsPreResourceSACheck() bool {
	if l.PreResourceSACheckFlag == 1 {
		return true
	}
	return false
}

func (l *SingleModelRule) IsNextResourceSACheck() bool {
	if l.NextResourceSACheckFlag == 1 {
		return true
	}
	return false
}

type SingleCompose struct {
	ResourceID   string `json:"resource_id"`
	ResourceName string `json:"resource_name"`
	ResourceType int    `json:"resource_type"`
	MainType     int    `json:"main_type"`
	SubType      int    `json:"sub_type"`
	Sequence     int    `json:"sequence"`
}

func (l *SingleCompose) IsFirstLeg() bool {
	if l.ResourceType != constant.LaneComposeLine {
		return false
	}
	return utils.ContainsUint32(constant.FirstLegFlagList, uint32(l.SubType))
}

// check can do line check
func (l *SingleCompose) IsLineRuleCheck() bool {
	if l.ResourceType != constant.LaneComposeLine {
		return false
	}
	return uint32(l.SubType) != constant.C_FM
}

func (l *SingleCompose) IsFirstMile() bool {
	if l.ResourceType != constant.LaneComposeLine {
		return false
	}
	return utils.ContainsUint32(constant.FirstMileFlagList, uint32(l.SubType))
}

func (l *SingleCompose) IsLastMile() bool {
	if l.ResourceType != constant.LaneComposeLine {
		return false
	}
	return utils.ContainsUint32(constant.LastMileFlagList, uint32(l.SubType))
}

func (l *SingleCompose) IsSite() bool {
	if l.ResourceType == constant.LaneComposeSite {
		return true
	}
	return false
}

func (l *SingleCompose) IsLine() bool {
	if l.ResourceType == constant.LaneComposeLine {
		return true
	}
	return false
}

func (l *SingleCompose) IsNeedSiteSorting(ruleList []*SingleModelRule) bool {
	if !l.IsSite() {
		return false
	}
	for _, rule := range ruleList {
		if !rule.IsSite() || rule.Sequence != l.Sequence {
			continue
		}
		if rule.IsSiteSorting() && (rule.IsSortingAsPreDeliver() || rule.IsSortingAsNextPickup()) {
			// sorting flag == 1, and at least one of (IsSortingAsPreDeliver,IsSortingAsNextPickup) is true, then check site SA
			if l.MainType == rule.MainType && l.SubType == rule.SubType {
				return true
			}
		}
	}
	return false
}

func (l *SingleCompose) GetRule(ruleList []*SingleModelRule) *SingleModelRule {
	for _, rule := range ruleList {
		if rule.Sequence != l.Sequence || (l.MainType != rule.MainType && l.SubType != rule.SubType) {
			continue
		}
		return rule
	}
	return nil
}

func (l *SingleCompose) GetPreLineAndNextLine(composeList []*SingleCompose, rules []*SingleModelRule) (*SingleCompose, *SingleCompose) {
	var preLine, nextLine *SingleCompose
	rule := l.GetRule(rules)
	for _, compose := range composeList {
		if compose.Sequence == l.Sequence+1 && rule.IsNextResourceSACheck() {
			nextLine = compose
		}
		if compose.Sequence == l.Sequence-1 && rule.IsPreResourceSACheck() {
			preLine = compose
		}
	}
	return preLine, nextLine
}

type LaneCodeStruct struct {
	LaneCode       string             `json:"lane_code"`
	LaneFormatType string             `json:"lane_format_type"`
	LaneCbFlag     uint32             `json:"lane_cb_flag"`
	ErrCode        int                `json:"errcode"`
	Message        string             `json:"message"`
	Composes       []*SingleCompose   `json:"composes"`
	ModelRules     []*SingleModelRule `json:"model_rules"`
}

func (l *LaneCodeStruct) IsCb() bool {
	if l != nil && l.LaneCbFlag == 1 {
		return true
	}

	return false
}

func (l *LaneCodeStruct) IsLocal() bool {
	if l != nil && l.LaneCbFlag == 2 {
		return true
	}

	return false
}

func (l *LaneCodeStruct) GetFmAndLm() (*SingleCompose, *SingleCompose, *lcos_error.LCOSError) {
	var fm, lm *SingleCompose
	for i := 0; i < len(l.Composes); i++ {
		resource := l.Composes[i]
		if resource.ResourceType != constant.LaneComposeLine {
			continue
		}
		if resource.IsFirstMile() && fm == nil {
			fm = resource
		}
		if resource.IsLastMile() && lm == nil {
			lm = resource
		}
	}

	// feat: SPLN-23592, for cb multi-product, do NOT check lastmile necessity any more.
	//if lm == nil {
	//	errMsg := fmt.Sprintf("lane: %s no last mile", l.LaneCode)
	//	return fm, lm, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	//}

	return fm, lm, nil
}

func (l *LaneCodeStruct) GetFlAndLm() (*SingleCompose, *SingleCompose, *lcos_error.LCOSError) {
	var fl, lm *SingleCompose
	for i := 0; i < len(l.Composes); i++ {
		if l.Composes[i].ResourceType != constant.LaneComposeLine {
			continue
		}
		if l.Composes[i].IsFirstLeg() {
			fl = l.Composes[i]
		}
		if l.Composes[i].IsLastMile() {
			lm = l.Composes[i]
		}
	}

	// feat: SPLN-23592, for cb multi-product, do NOT check lastmile necessity any more.
	// feat: SPLN-25229, need to fit this scene: lm not exist but get first leg
	if lm == nil && fl != nil && fl.SubType == int(constant.C_FL) {
		errMsg := fmt.Sprintf("lane: %s no last mile but have first leg:%s", l.LaneCode, fl.ResourceName)
		return fl, lm, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}

	return fl, lm, nil
}

// 为了兼容履约时传入双层链路
func (l *LaneCodeStruct) GetFlAndLmFulfillment() (*SingleCompose, *SingleCompose, *lcos_error.LCOSError) {
	var fl, lm *SingleCompose
	for i := 0; i < len(l.Composes); i++ {
		if l.Composes[i].ResourceType != constant.LaneComposeLine {
			continue
		}
		if l.Composes[i].IsFirstLeg() {
			fl = l.Composes[i]
		}
		if l.Composes[i].IsLastMile() {
			lm = l.Composes[i]
		}
	}

	// feat: SPLN-23592, for cb multi-product, do NOT check lastmile necessity any more.
	// feat: SPLN-25229, need to fit this scene: lm not exist but get first leg
	//if lm == nil && fl != nil && fl.SubType == int(constant.C_FL) {
	//	errMsg := fmt.Sprintf("lane: %s no last mile but have first leg:%s", l.LaneCode, fl.ResourceName)
	//	return fl, lm, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	//}

	return fl, lm, nil
}

// using for cb multi-product new API
// feat: SPLN-23592, for cb multi-product, do NOT check lastmile necessity any more, can fit local product as well
func (l *LaneCodeStruct) GetNeedCheckLines() (*SingleCompose, *SingleCompose, *SingleCompose) {
	var fm, lm, fl *SingleCompose
	for i := 0; i < len(l.Composes); i++ {
		resource := l.Composes[i]
		if resource.ResourceType != constant.LaneComposeLine {
			continue
		}
		if resource.IsFirstMile() && fm == nil {
			fm = resource
		}
		if resource.IsLastMile() && lm == nil {
			lm = resource
		}
		if l.Composes[i].IsFirstLeg() {
			fl = l.Composes[i]
		}
	}

	return fm, lm, fl
}

func (l *LaneCodeStruct) GetSite() []*SingleCompose {
	// may have multiple sites in a group of Compose, but only one site need to check SA
	var sites []*SingleCompose
	for i := 0; i < len(l.Composes); i++ {
		if l.Composes[i].ResourceType != constant.LaneComposeSite {
			continue
		}
		if l.Composes[i].IsNeedSiteSorting(l.ModelRules) {
			sites = append(sites, l.Composes[i])
		}
	}
	return sites
}

func (l *LaneCodeStruct) GetSequenceNo(resourceId string) uint32 {
	var sequence int
	for i := 0; i < len(l.Composes); i++ {
		if l.Composes[i].ResourceID == resourceId {
			sequence = l.Composes[i].Sequence
			break
		}
	}
	return uint32(sequence)
}

type CacheValue struct {
	Timestamp int64
	Value     *LaneCodeStruct
}

func (c *CacheValue) Expired(ctx context.Context) bool {
	return c.Timestamp < recorder.Now(ctx).Unix()
}

type (
	BatchLaneFormatReq struct {
		FormatIdList []uint64 `json:"format_id_list"`
	}
	BatchLaneFormatResp struct {
		Retcode int32  `json:"retcode"`
		Message string `json:"message"`
		Data    struct {
			List []*LaneInfo `json:"list"`
		} `json:"data"`
	}
	LaneInfo struct {
		FormatId  uint64 `json:"format_id"`
		ModelName string `json:"lane_format"`
		ModelLink string `json:"model_link"`
	}

	BatchListLaneInfoReq struct {
		LaneCodeList []string `json:"lane_code_list"`
	}

	BatchListLaneInfoResp struct {
		Retcode int32  `json:"retcode"`
		Message string `json:"message"`
		Data    struct {
			List []ListLaneInfo `json:"list"`
		} `json:"data"`
	}

	ListLaneInfo struct {
		Id           int32  `json:"id"`
		LaneFormat   string `json:"lane_format"`
		LaneCode     string `json:"lane_code"`
		FormatId     uint64 `json:"format_id"`
		FormatRemark string `json:"format_remark"`
	}
)

type LogisticOrderAddressInfo struct {
	StateLocationId    int32
	CityLocationId     int32
	DistrictLocationId int32
	PostCode           string
}

type LogisticOrderData struct {
	ForderId          uint64
	CbFlag            int32
	OrderSN           string
	LmTrackingNumber  string
	LogisticProductId string
	ResourceId        string
	DeliverCountry    string
	ClientId          uint32
	SellerAddress     *LogisticOrderAddressInfo
	BuyerAddress      *LogisticOrderAddressInfo
}

func (o *LogisticOrderData) IsCBOrder() bool {
	return o != nil && o.CbFlag == int32(constant.TRUE)
}

type MultiLayerLaneCodeGroup struct {
	IsMultiLayer bool
	FM           []string
	LM           []string
}

type EDDLogisticOrderData struct {
	ForderId          uint64
	OrderSN           string
	CbFlag            int32
	LmTrackingNumber  string
	LogisticProductId string
	ResourceId        string
	DeliverCountry    string
	ClientId          uint32
	SellerAddress     *LogisticOrderAddressInfo
	BuyerAddress      *LogisticOrderAddressInfo
	LmPickupAddress   *LogisticOrderAddressInfo
	LmDeliverAddress  *LogisticOrderAddressInfo
}
