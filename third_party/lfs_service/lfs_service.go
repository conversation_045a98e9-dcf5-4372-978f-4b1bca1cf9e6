package lfs_service

import (
	"context"
	"encoding/gob"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/jwt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/switch_utils"
	"google.golang.org/protobuf/proto"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache_with_ttl"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	http_util "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
	jsoniter "github.com/json-iterator/go"
)

var invoker = chassis.NewRPCInvoker()

type lfsService struct {
	serviceName string
}

var (
	lruCache     *cache.LruCache
	laneLruCache *cache.LruCache
)

func init() {
	lruCache, _ = cache.NewLruCache(cache.LfsLruName, &LaneInfo{})
	laneLruCache, _ = cache.NewLruCache(cache.LfsLaneName, &CacheValue{})
	//流量回放增加gob注册
	gob.Register(&CacheValue{})
	gob.Register(&LaneInfo{})
	gob.Register(&map[string]*LaneCodeStruct{})
}

func NewLFSService(ctx context.Context, region string) *lfsService {
	lfsPrefix := cf.GetLfsMeshConfig(ctx).NamePrefix

	serviceName := utils.GenKey("-", lfsPrefix, strings.ToLower(region))
	return &lfsService{serviceName: serviceName}
}

func (l *lfsService) BatchQueryLaneInfo(ctx utils.LCOSContext, laneCodes []string) (map[string]*LaneCodeStruct, *lcos_error.LCOSError) {
	timeoutCtx, cancel := context.WithTimeout(ctx, getLFSServiceTimeOut(ctx))
	defer cancel()

	req := &protocol.BatchLaneInfoReq{
		Header: genLfsReqHeader(ctx),
		Data: &protocol.BatchLaneInfoReqData{
			LaneCodes:           laneCodes,
			IsRemoveSellerBuyer: proto.Bool(true),
		},
	}
	rsp := &protocol.BatchLaneInfoRsp{}
	if err := invoker.Invoke(timeoutCtx, l.serviceName, LfsLaneSchemaID, BatchQueryLaneInfo, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|lane_codes=[%s]|error=%v", BatchQueryLaneInfo, strings.Join(laneCodes, ","), err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|lane_codes=[%s]|error=%v", BatchQueryLaneInfo, strings.Join(laneCodes, ","), "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|lane_codes=[%s]|error_code=%d|error=%v", BatchQueryLaneInfo, strings.Join(laneCodes, ","), rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetHeader().GetMessage())
	}
	// 请求成功打印日志
	items := rsp.GetData().GetItems()
	itemString, _ := jsoniter.MarshalToString(items)
	logger.CtxLogInfof(ctx, "requesting lfs success|api=%s|lane_codes=[%s]|rsp=%s", BatchQueryLaneInfo, strings.Join(laneCodes, ","), itemString)

	// 将结果作为map返回
	resultMap := map[string]*LaneCodeStruct{}
	for _, item := range items {
		var composes []*SingleCompose
		var modelRules []*SingleModelRule
		for _, singleCompose := range item.GetComposes() {
			composes = append(composes, &SingleCompose{
				ResourceID:   singleCompose.GetResourceId(),
				ResourceName: singleCompose.GetResourceName(),
				ResourceType: int(singleCompose.GetResourceType()),
				MainType:     int(singleCompose.GetMainType()),
				SubType:      int(singleCompose.GetSubType()),
				Sequence:     int(singleCompose.GetSequence()),
			})
		}
		// sa check flag, for supporting cb multi-product
		for _, singleModelRule := range item.GetModelRules() {
			modelRules = append(modelRules, &SingleModelRule{
				MainType:                   int(singleModelRule.GetMainType()),
				SubType:                    int(singleModelRule.GetSubType()),
				ResourceType:               int(singleModelRule.GetResourceType()),
				SortingAsPreDeliverFlag:    int(singleModelRule.GetSortingAsPreDeliverFlag()),
				SortingAsNextPickupFlag:    int(singleModelRule.GetSortingAsNextPickupFlag()),
				SiteSortingPickupAddrFlag:  int(singleModelRule.GetSiteSortingPickupAddressFlag()),
				SiteSortingDeliverAddrFlag: int(singleModelRule.GetSiteSortingDeliverAddressFlag()),
				PreResourceSACheckFlag:     int(singleModelRule.GetPreResourceSaCheckFlag()),
				NextResourceSACheckFlag:    int(singleModelRule.GetNextResourceSaCheckFlag()),
				SortingFlag:                int(singleModelRule.GetSortingFlag()),
				Sequence:                   int(singleModelRule.GetSequence()),
			})
		}
		sort.Slice(composes, func(i, j int) bool {
			return composes[i].Sequence < composes[j].Sequence
		})
		sort.Slice(modelRules, func(i, j int) bool {
			return modelRules[i].Sequence < modelRules[j].Sequence
		})
		resultMap[item.GetLaneCode()] = &LaneCodeStruct{
			LaneCode:       item.GetLaneCode(),
			LaneFormatType: item.GetLaneFormatType(),
			LaneCbFlag:     item.GetLaneCbFlag(),
			ErrCode:        int(item.GetErrcode()),
			Message:        item.GetMessage(),
			Composes:       composes,
			ModelRules:     modelRules,
		}
	}
	return resultMap, nil
}

func (l *lfsService) BatchQueryLaneInfoWithPaging(ctx utils.LCOSContext, laneCodes []string, batchSize int) (map[string]*LaneCodeStruct, *lcos_error.LCOSError) {
	if batchSize == 0 {
		batchSize = 100
	}

	laneInfoMap := make(map[string]*LaneCodeStruct, len(laneCodes))
	start, end := 0, batchSize
	for start < len(laneCodes) {
		if end > len(laneCodes) {
			end = len(laneCodes)
		}

		// 分批请求LFS获取链路信息
		subLaneInfoMap, err := l.BatchQueryLaneInfo(ctx, laneCodes[start:end])
		if err != nil {
			return nil, err
		}
		for laneCode, laneInfo := range subLaneInfoMap {
			laneInfoMap[laneCode] = laneInfo
		}

		start = end
		end += batchSize
	}

	return laneInfoMap, nil
}

// will use lru cache to store lane info
func (l *lfsService) GetLaneCodeMapUsingCache(ctx utils.LCOSContext, laneCodes []string) (map[string]*LaneCodeStruct, *lcos_error.LCOSError) {
	resultMap := map[string]*LaneCodeStruct{}

	//read from cache
	remoteReq := []string{}
	missing := false
	for _, laneCode := range laneCodes {
		result, ok := laneLruCache.Get(ctx, laneCode)
		if ok {
			resultModel, _ := result.(*CacheValue)
			resultMap[laneCode] = resultModel.Value
			if resultModel.Expired(ctx) {
				remoteReq = append(remoteReq, laneCode)
			}
			continue
		}
		remoteReq = append(remoteReq, laneCode)
		missing = true
	}

	if len(remoteReq) == 0 {
		logger.LogInfof("cache hit for all lanecodes: %v", laneCodes)
		return resultMap, nil
	}

	logger.LogInfof("some cache miss or expired, try to get lane info from lfs: %v", remoteReq)
	laneCodeInfoMap, lcosErr := l.BatchQueryLaneInfo(ctx, remoteReq)
	if lcosErr != nil {
		if missing {
			return nil, lcosErr
		} else {
			logger.CtxLogInfof(ctx, "reuse the expired cache for lanecodes: %v", laneCodes)
			laneCodeInfoMap = resultMap
		}
		logger.CtxLogErrorf(ctx, "lfs api failed for get lane info, err: %v, missing:%v", lcosErr, missing)
	}

	// set cache
	for laneCode, laneCodeInfo := range laneCodeInfoMap {
		Timestamp := recorder.Now(ctx).Add(DEFAULT_LOGIC_CACHE_TIMEOUT).Unix()
		cacheValue := &CacheValue{
			Timestamp: Timestamp,
			Value:     laneCodeInfo,
		}
		ok, _ := laneLruCache.Add(ctx, laneCode, cacheValue)
		if !ok {
			logger.CtxLogErrorf(ctx, "cannot set key:[%s] to lru cache", laneCode)
		}
		resultMap[laneCode] = laneCodeInfo
	}

	return resultMap, nil

}

func (l *lfsService) GetLaneCodeMapUsingLocalCache(ctx utils.LCOSContext, laneCodes []string) (map[string]*LaneCodeStruct, *lcos_error.LCOSError) {
	laneInfoMap := make(map[string]*LaneCodeStruct, len(laneCodes))

	var missingLaneCodes []string
	executor := localcache.NewLocalCacheQueryExecutor()
	for _, laneCode := range laneCodes {
		obj, err := executor.Find(ctx, constant.LogisticLaneInfoNamespace, laneCode)
		if err != nil || obj == nil {
			// 异常场景：缓存key缺失。lcos加载了lps可能传下来的所有lane code数据，正常情况不应存在缓存key缺失
			missingLaneCodes = append(missingLaneCodes, laneCode)
			continue
		}
		laneInfo, ok := obj.(*LaneCodeStruct)
		if !ok {
			// 异常场景：缓存value不合法。不会走到这个分支
			missingLaneCodes = append(missingLaneCodes, laneCode)
			continue
		}
		laneInfoMap[laneCode] = laneInfo
	}

	if len(missingLaneCodes) > 0 {
		// 上报并触发告警
		_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleThirdPartyLocalCache, constant.LogisticLaneInfoNamespace, constant.StatusMiss, strings.Join(missingLaneCodes, ","))

		// 兜底使用LRU+remote请求获取lane code数据
		missingLaneInfoMap, err := l.GetLaneCodeMapUsingCache(ctx, laneCodes)
		if err != nil {
			return nil, err
		}
		for laneCode, laneInfo := range missingLaneInfoMap {
			laneInfoMap[laneCode] = laneInfo
		}
	}
	return laneInfoMap, nil
}

func (l *lfsService) GetLaneCodeMapWithGreySwitch(ctx utils.LCOSContext, laneCodes []string) (map[string]*LaneCodeStruct, *lcos_error.LCOSError) {
	obj, err := switch_utils.WithGreySwitch(ctx, switch_utils.BusinessKeyLogisticLaneInfoCache,
		func(ctx utils.LCOSContext) (interface{}, *lcos_error.LCOSError) {
			return l.GetLaneCodeMapUsingCache(ctx, laneCodes)
		},
		func(ctx utils.LCOSContext) (interface{}, *lcos_error.LCOSError) {
			return l.GetLaneCodeMapUsingLocalCache(ctx, laneCodes)
		},
		func(got1, got2 interface{}, err1, err2 *lcos_error.LCOSError) bool {
			if !lcos_error.IsSameError(err1, err2) {
				return true
			}
			ret1, ret2 := got1.(map[string]*LaneCodeStruct), got2.(map[string]*LaneCodeStruct)
			if len(ret1) != len(ret2) {
				return true
			}
			if ret1 == nil || ret2 == nil {
				return false
			}
			for laneCode, laneInfoV1 := range ret1 {
				laneInfoV2, ok := ret2[laneCode]
				if !ok {
					return true
				}

				// lane info内的slice字段是排序过的，因此可以直接序列化判断结构体是否一致
				if utils.MarshToStringWithoutError(laneInfoV1) != utils.MarshToStringWithoutError(laneInfoV2) {
					return true
				}
			}
			return false
		},
	)
	if err != nil {
		return nil, err
	}
	return obj.(map[string]*LaneCodeStruct), nil
}

func getLFSServiceTimeOut(ctx context.Context) time.Duration {
	timeout := config.GetMutableConf(ctx).LFSConfig.ServiceTimeout
	if timeout > 0 {
		return time.Duration(timeout) * time.Millisecond
	}
	return DEFAULT_LFS_SERVICE_TIMEOUT
}

func BatchGetModelInfoMap(ctx utils.LCOSContext, formatIdList []uint64) (map[uint64]*LaneInfo, *lcos_error.LCOSError) {
	rsp := make(map[uint64]*LaneInfo)
	remoteReq := []uint64{}

	// get from cache
	for _, formatId := range formatIdList {
		cacheKey := utils.GenKey(":", "BatchGetModelInfo", strconv.Itoa(int(formatId)))
		cacheVal, ok := lruCache.Get(ctx, cacheKey)
		if ok {
			modelInfo, ok := cacheVal.(*LaneInfo)
			if ok {
				rsp[formatId] = modelInfo
				continue
			}
		}
		remoteReq = append(remoteReq, formatId)
	}
	if len(remoteReq) == 0 {
		logger.CtxLogInfof(ctx, "BatchGetModelInfo cache hit, formatIds:[%v]", formatIdList)
		return rsp, nil
	}

	logger.LogInfof("BatchGetModelInfo cache mis, try to get from lfs, mis formatIds:[%v]", remoteReq)

	//get from remote
	remoteResp, lcos_err := BatchGetModelInfo(ctx, remoteReq)
	if lcos_err != nil {
		return rsp, lcos_err
	}

	// set cache
	for _, modelInfo := range remoteResp {
		formatId := modelInfo.FormatId
		cacheKey := utils.GenKey(":", "BatchGetModelInfo", strconv.Itoa(int(formatId)))
		ok, _ := lruCache.Add(ctx, cacheKey, modelInfo)
		if !ok {
			logger.CtxLogErrorf(ctx, "cannot set key:[%s] to lru cache", cacheKey)
		}
		rsp[formatId] = modelInfo
	}

	return rsp, nil
}

func BatchListLaneInfo(ctx utils.LCOSContext, laneCodeList []string) ([]ListLaneInfo, *lcos_error.LCOSError) {
	req := BatchListLaneInfoReq{
		LaneCodeList: laneCodeList,
	}
	resp, err := sendLFSJwt(ctx, req, "", "/admin/api/list_lane_info", ctx.GetCountry(), "POST")
	if err != nil {
		return nil, err
	}

	rsp := &BatchListLaneInfoResp{}
	if err := jsoniter.Unmarshal(resp, rsp); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, err.Error())
	}

	if rsp.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(rsp.Retcode, "Call lfs failed:"+rsp.Message)
	}

	return rsp.Data.List, nil
}

func BatchGetModelInfo(ctx utils.LCOSContext, formatIdList []uint64) ([]*LaneInfo, *lcos_error.LCOSError) {
	req := BatchLaneFormatReq{
		FormatIdList: formatIdList,
	}
	resp, err := sendLFSJwt(ctx, req, "", "/admin/api/batch_get_lane_format", ctx.GetCountry(), "POST")
	if err != nil {
		return nil, err
	}

	rsp := &BatchLaneFormatResp{}
	if err := jsoniter.Unmarshal(resp, rsp); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, err.Error())
	}

	if rsp.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(rsp.Retcode, "Call lfs failed:"+rsp.Message)
	}

	return rsp.Data.List, nil
}

func sendLFSJwt(ctx utils.LCOSContext, data interface{}, params, api, cid, method string) ([]byte, *lcos_error.LCOSError) {
	if cid == "XX" {
		cid = "SG"
	}
	env := utils.GetEnv(ctx)
	url := hosts[env][cid] + api

	c, ok := ctx.(*utils.HttpContext)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, "invalid context")
	}

	jwtInfo := c.GetJwtInfo()
	newJwtInfo := map[string]interface{}{}
	// put data to context
	for k, v := range jwtInfo {
		newJwtInfo[k] = v
	}

	payload := map[string]interface{}{
		//"data": data,
		"exp":  recorder.Now(ctx).Unix() + 10000,
		"info": newJwtInfo,
	}
	headers := map[string]interface{}{
		"typ":     "JWT",
		"alg":     "HS256",
		"account": operatorAccount,
	}
	enc, err := jwt.GetJwtDataV2(config.GetThirdServiceSecretConfig(ctx).Lfs, headers, payload)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	var req *http.Request
	if method == "POST" {
		d, _ := jsoniter.Marshal(data)
		req, err = http.NewRequest(http.MethodPost, url, strings.NewReader(string(d)))
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, err.Error())
		}
		req.Header.Add("Content-Type", "application/json")
	} else {
		if len(params) > 0 {
			url = url + "?" + params
		}
		req, err = http.NewRequest(http.MethodGet, url, nil)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, err.Error())
		}
	}
	req.Header.Add("request-id", ctx.GetRequestId())
	req.Header.Add("X-Request-Id", ctx.GetRequestId())
	req.Header.Add("jwt-token", enc)
	req.Header.Add("X-Token", "LPS")
	req.Header.Add("X-Account", "test_token")

	httpCtx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	resp, err := http_util.HttpRequest(httpCtx, httpClient, req)
	//resp, err := client.CacheEntry
	if err != nil {
		logger.LogErrorf("send http jwt request failed,err[%v]", err)
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, err.Error())
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	logger.LogInfof("send jwt http request,method[%s],url[%s],req[%v],resp[%s], jwt[%s]", method, url, data, body, enc)
	if err != nil {
		logger.LogErrorf("send http jwt request failed")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	return body, nil
}

func (l *lfsService) GetLogisticOrderDataBySloTn(ctx utils.LCOSContext, sloTn string) (*LogisticOrderData, *lcos_error.LCOSError) {
	req := &protocol.GetLogisticOrderDataBySloTnReq{
		Header: genLfsReqHeader(ctx),
		Data: &protocol.GetLogisticOrderDataBySloTnReqData{
			SloTn: utils.NewString(sloTn),
		},
	}

	rsp := &protocol.GetLogisticOrderDataBySloTnRsp{}

	if err := invoker.Invoke(ctx, l.serviceName, LfsOrderQuerySchemaID, GetLogisticOrderDataBySloTn, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|sls_tn=%s|error=%v", GetLogisticOrderDataBySloTn, sloTn, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|sls_tn=%s|error=%v", GetLogisticOrderDataBySloTn, sloTn, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|sls_tn=%s|error_code=%d|error=%v", GetLogisticOrderDataBySloTn, sloTn, rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetHeader().GetMessage())
	}

	orderStr, _ := jsoniter.MarshalToString(rsp.GetData())

	logger.CtxLogInfof(ctx, "requesting lfs success|api=%s|sls_tn=%s|order_info=[%s]", GetLogisticOrderDataBySloTn, sloTn, orderStr)

	return &LogisticOrderData{
		ForderId:          rsp.GetData().GetForderId(),
		CbFlag:            rsp.GetData().GetCbFlag(),
		LmTrackingNumber:  rsp.GetData().GetLmTrackingNumber(),
		LogisticProductId: rsp.GetData().GetLogisticProductId(),
		ResourceId:        rsp.GetData().GetResourceId(),
		DeliverCountry:    rsp.GetData().GetDeliverCountry(),
		SellerAddress: &LogisticOrderAddressInfo{
			StateLocationId:    rsp.GetData().GetSellerAddress().GetStateLocationId(),
			CityLocationId:     rsp.GetData().GetSellerAddress().GetCityLocationId(),
			DistrictLocationId: rsp.GetData().GetSellerAddress().GetDistrictLocationId(),
			PostCode:           rsp.GetData().GetSellerAddress().GetPostCode(),
		},
		BuyerAddress: &LogisticOrderAddressInfo{
			StateLocationId:    rsp.GetData().GetBuyerAddress().GetStateLocationId(),
			CityLocationId:     rsp.GetData().GetBuyerAddress().GetCityLocationId(),
			DistrictLocationId: rsp.GetData().GetBuyerAddress().GetDistrictLocationId(),
			PostCode:           rsp.GetData().GetBuyerAddress().GetPostCode(),
		},
	}, nil
}

func (l *lfsService) GetMultiLayerLaneCodeGroup(ctx utils.LCOSContext, laneCodes []string) (*MultiLayerLaneCodeGroup, *lcos_error.LCOSError) {
	timeoutCtx, cancel := context.WithTimeout(ctx, getLFSServiceTimeOut(ctx))
	defer cancel()
	req := &protocol.BatchLaneInfoReq{
		Header: genLfsReqHeader(ctx),
		Data:   &protocol.BatchLaneInfoReqData{LaneCodes: laneCodes},
	}
	resp := &protocol.BatchLaneGroupInfoRsp{}
	if err := invoker.Invoke(timeoutCtx, l.serviceName, LfsLaneSchemaID, BatchQueryLaneGroupInfo, req, resp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|lane_codes=[%s]|error=%v", BatchQueryLaneGroupInfo, strings.Join(laneCodes, ","), err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if resp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|lane_codes=[%s]|error=%v", BatchQueryLaneGroupInfo, strings.Join(laneCodes, ","), "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if resp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|lane_codes=[%s]|error_code=%d|error=%v", BatchQueryLaneGroupInfo, strings.Join(laneCodes, ","), resp.GetHeader().GetRetcode(), resp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.GetHeader().GetMessage())
	}
	// 请求成功打印日志
	items := resp.GetData().GetItems()
	itemString, _ := jsoniter.MarshalToString(items)
	logger.CtxLogInfof(ctx, "requesting lfs success|api=%s|lane_codes=[%s]|rsp=%s", BatchQueryLaneGroupInfo, strings.Join(laneCodes, ","), itemString)

	fmMap := make(map[string]bool, len(laneCodes)) // 去重
	lmMap := make(map[string]bool, len(laneCodes))
	ret := &MultiLayerLaneCodeGroup{
		IsMultiLayer: false,
		FM:           make([]string, 0, len(laneCodes)),
		LM:           make([]string, 0, len(laneCodes)),
	}
	for _, item := range items {
		if *item.Errcode == 0 && len(item.LaneCodeGroup) == 2 {
			ret.IsMultiLayer = true
			fm := item.LaneCodeGroup[0]
			lm := item.LaneCodeGroup[1]
			if _, ok := fmMap[fm]; !ok {
				fmMap[fm] = true
				ret.FM = append(ret.FM, fm)
			}
			if _, ok := lmMap[lm]; !ok {
				lmMap[lm] = true
				ret.LM = append(ret.LM, lm)
			}
		}
	}
	return ret, nil
}

func (l *lfsService) GetLogisticOrderDataBySloTnForEDD(ctx utils.LCOSContext, sloTn string) (*EDDLogisticOrderData, *lcos_error.LCOSError) {
	req := &protocol.GetLogisticOrderDataBySloTnReq{
		Header: genLfsReqHeader(ctx),
		Data: &protocol.GetLogisticOrderDataBySloTnReqData{
			SloTn: utils.NewString(sloTn),
		},
	}

	rsp := &protocol.GetLogisticOrderDataBySloTnForEDDRsp{}

	if err := invoker.Invoke(ctx, l.serviceName, LfsOrderQuerySchemaID, GetLogisticOrderDataBySloTnForEDD, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|sls_tn=%s|error=%v", GetLogisticOrderDataBySloTnForEDD, sloTn, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|sls_tn=%s|error=%v", GetLogisticOrderDataBySloTnForEDD, sloTn, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting lfs error|api=%s|sls_tn=%s|error_code=%d|error=%v", GetLogisticOrderDataBySloTnForEDD, sloTn, rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetHeader().GetMessage())
	}

	orderStr, _ := jsoniter.MarshalToString(rsp.GetData())

	logger.CtxLogInfof(ctx, "requesting lfs success|api=%s|sls_tn=%s|order_info=[%s]", GetLogisticOrderDataBySloTnForEDD, sloTn, orderStr)

	return &EDDLogisticOrderData{
		ForderId:          rsp.GetData().GetForderId(),
		OrderSN:           rsp.GetData().GetOrderSn(),
		CbFlag:            rsp.GetData().GetCbFlag(),
		LmTrackingNumber:  rsp.GetData().GetLmTrackingNumber(),
		LogisticProductId: rsp.GetData().GetLogisticProductId(),
		ResourceId:        rsp.GetData().GetResourceId(),
		DeliverCountry:    rsp.GetData().GetDeliverCountry(),
		ClientId:          rsp.GetData().GetClientId(),
		SellerAddress: &LogisticOrderAddressInfo{
			StateLocationId:    rsp.GetData().GetSellerAddress().GetStateLocationId(),
			CityLocationId:     rsp.GetData().GetSellerAddress().GetCityLocationId(),
			DistrictLocationId: rsp.GetData().GetSellerAddress().GetDistrictLocationId(),
			PostCode:           rsp.GetData().GetSellerAddress().GetPostCode(),
		},
		BuyerAddress: &LogisticOrderAddressInfo{
			StateLocationId:    rsp.GetData().GetBuyerAddress().GetStateLocationId(),
			CityLocationId:     rsp.GetData().GetBuyerAddress().GetCityLocationId(),
			DistrictLocationId: rsp.GetData().GetBuyerAddress().GetDistrictLocationId(),
			PostCode:           rsp.GetData().GetBuyerAddress().GetPostCode(),
		},
		LmPickupAddress: &LogisticOrderAddressInfo{
			StateLocationId:    rsp.GetData().GetLmPickupAddress().GetStateLocationId(),
			CityLocationId:     rsp.GetData().GetLmPickupAddress().GetCityLocationId(),
			DistrictLocationId: rsp.GetData().GetLmPickupAddress().GetDistrictLocationId(),
			PostCode:           rsp.GetData().GetLmPickupAddress().GetPostCode(),
		},
		LmDeliverAddress: &LogisticOrderAddressInfo{
			StateLocationId:    rsp.GetData().GetLmDeliverAddress().GetStateLocationId(),
			CityLocationId:     rsp.GetData().GetLmDeliverAddress().GetCityLocationId(),
			DistrictLocationId: rsp.GetData().GetLmDeliverAddress().GetDistrictLocationId(),
			PostCode:           rsp.GetData().GetLmDeliverAddress().GetPostCode(),
		},
	}, nil
}

func TransferEDDOrderInfoToOrderInfo(orderInfo *EDDLogisticOrderData) *LogisticOrderData {
	returnOrderInfo := &LogisticOrderData{
		ForderId:          orderInfo.ForderId,
		OrderSN:           orderInfo.OrderSN,
		CbFlag:            orderInfo.CbFlag,
		LmTrackingNumber:  orderInfo.LmTrackingNumber,
		LogisticProductId: orderInfo.LogisticProductId,
		ResourceId:        orderInfo.ResourceId,
		DeliverCountry:    orderInfo.DeliverCountry,
		SellerAddress:     orderInfo.SellerAddress,
		BuyerAddress:      orderInfo.BuyerAddress,
		ClientId:          orderInfo.ClientId,
	}

	// for cb order, need to replace seller address with lm pickup address
	if returnOrderInfo.CbFlag == int32(constant.CBType) {
		returnOrderInfo.SellerAddress = &LogisticOrderAddressInfo{
			StateLocationId:    orderInfo.LmPickupAddress.StateLocationId,
			CityLocationId:     orderInfo.LmPickupAddress.CityLocationId,
			DistrictLocationId: orderInfo.LmPickupAddress.DistrictLocationId,
			PostCode:           orderInfo.LmPickupAddress.PostCode,
		}
	}
	return returnOrderInfo
}

func (l *lfsService) BatchGetLogisticOrderDataBySloTnForEDDWithConcurrency(ctx utils.LCOSContext, slsTNList []string, maxCurrency int) (map[string]*LogisticOrderData, *lcos_error.LCOSError) {

	logger.CtxLogInfof(ctx, "ready to call lfs service with batch size:[%d]", maxCurrency)

	resultMap := make(map[string]*LogisticOrderData)

	maxCurrencyChan := make(chan struct{}, maxCurrency)
	var waitGroup sync.WaitGroup
	var lock sync.Mutex

	for _, tmpSLSTN := range slsTNList {
		maxCurrencyChan <- struct{}{}
		waitGroup.Add(1)
		go func(slsTN string) {
			response, lcosErr := l.GetLogisticOrderDataBySloTnForEDD(ctx, slsTN)
			if lcosErr == nil {
				lock.Lock()
				resultMap[slsTN] = TransferEDDOrderInfoToOrderInfo(response)
				lock.Unlock()
			}
			<-maxCurrencyChan
			waitGroup.Done()
		}(tmpSLSTN)
	}
	waitGroup.Wait()
	return resultMap, nil
}
