package lfs_service

import (
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
)

func genLfsReqHeader(ctx utils.LCOSContext) *protocol.ReqHeader {
	currentTime := utils.GetTimestamp(ctx)
	ip := utils.GetLocalIp()
	reqId := ctx.GetRequestId()
	lfsAccount := config.GetLFSServiceConfig(ctx).Account
	lfsToken := config.GetLFSServiceConfig(ctx).Token
	return &protocol.ReqHeader{
		RequestId: &reqId,
		Account:   &lfsAccount,
		Token:     &lfsToken,
		Timestamp: &currentTime,
		CallerIp:  ip,
	}
}
