package lfs_service

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"time"
)

const (
	// lfs api
	LfsLaneSchemaID         = "protocol.Lane"
	BatchQueryLaneInfo      = "BatchQueryLaneInfo"
	BatchQueryLaneGroupInfo = "BatchQueryLaneGroupInfo"

	// lfs api
	LfsOrderQuerySchemaID       = "protocol.OrderQuery"
	GetLogisticOrderDataBySloTn = "GetLogisticOrderDataBySloTn"

	GetLogisticOrderDataBySloTnForEDD = "GetLogisticOrderDataBySloTnForEDD"
)

const (
	DEFAULT_LOGIC_CACHE_TIMEOUT = 5 * 60 * time.Second
	DEFAULT_LFS_SERVICE_TIMEOUT = 1 * time.Second
)

var (
	account = "lcos"

	hosts = map[string]map[string]string{
		"FTE": {
			"BR": "https://api.lfs.test.shopee.com.br",
			"SG": "https://api.lfs.test.shopee.sg",
			"TW": "https://api.lfs.test.shopee.tw",
			"TH": "https://api.lfs.test.shopee.co.th",
			"ID": "https://api.lfs.test.shopee.co.id",
			"MY": "https://api.lfs.test.shopee.com.my",
			"PH": "https://api.lfs.test.shopee.ph",
			"VN": "https://api.lfs.test.shopee.vn",
			"MX": "https://api.lfs.test.shopee.com.mx",
			"IN": "https://api.lfs.test.shopee.in",
			"FR": "https://api.lfs.test.shopee.fr",
			"ES": "https://api.lfs.test.shopee.es",
			"PL": "https://api.lfs.test.shopee.pl",
			"AR": "https://api.lfs.test.shopee.com.ar",
			"CL": "https://api.lfs.test.shopee.cl",
			"CO": "https://api.lfs.test.shopee.com.co",
		},
		"TEST": {
			"BR": "https://api.lfs.test.shopee.com.br",
			"SG": "https://api.lfs.test.shopee.sg",
			"TW": "https://api.lfs.test.shopee.tw",
			"TH": "https://api.lfs.test.shopee.co.th",
			"ID": "https://api.lfs.test.shopee.co.id",
			"MY": "https://api.lfs.test.shopee.com.my",
			"PH": "https://api.lfs.test.shopee.ph",
			"VN": "https://api.lfs.test.shopee.vn",
			"MX": "https://api.lfs.test.shopee.com.mx",
			"IN": "https://api.lfs.test.shopee.in",
			"FR": "https://api.lfs.test.shopee.fr",
			"ES": "https://api.lfs.test.shopee.es",
			"PL": "https://api.lfs.test.shopee.pl",
			"AR": "https://api.lfs.test.shopee.com.ar",
			"CL": "https://api.lfs.test.shopee.cl",
			"CO": "https://api.lfs.test.shopee.com.co",
		},
		"UAT": {
			"BR": "https://api.lfs.uat.shopee.com.br",
			"SG": "https://api.lfs.uat.shopee.sg",
			"TW": "https://api.lfs.uat.shopee.tw",
			"TH": "https://api.lfs.uat.shopee.co.th",
			"ID": "https://api.lfs.uat.shopee.co.id",
			"MY": "https://api.lfs.uat.shopee.com.my",
			"PH": "https://api.lfs.uat.shopee.ph",
			"VN": "https://api.lfs.uat.shopee.vn",
			"MX": "https://api.lfs.uat.shopee.com.mx",
			"IN": "https://api.lfs.uat.shopee.in",
			"FR": "https://api.lfs.uat.shopee.fr",
			"ES": "https://api.lfs.uat.shopee.es",
			"PL": "https://api.lfs.uat.shopee.pl",
			"AR": "https://api.lfs.uat.shopee.com.ar",
			"CL": "https://api.lfs.uat.shopee.cl",
			"CO": "https://api.lfs.uat.shopee.com.co",
		},
		"STABLE": {
			"BR": "https://api.lfs.test-stable.shopee.com.br",
			"SG": "https://api.lfs.test-stable.shopee.sg",
			"TW": "https://api.lfs.test-stable.shopee.tw",
			"TH": "https://api.lfs.test-stable.shopee.co.th",
			"ID": "https://api.lfs.test-stable.shopee.co.id",
			"MY": "https://api.lfs.test-stable.shopee.com.my",
			"PH": "https://api.lfs.test-stable.shopee.ph",
			"VN": "https://api.lfs.test-stable.shopee.vn",
			"MX": "https://api.lfs.test-stable.shopee.com.mx",
			"IN": "https://api.lfs.test-stable.shopee.in",
			"FR": "https://api.lfs.test-stable.shopee.fr",
			"ES": "https://api.lfs.test-stable.shopee.es",
			"PL": "https://api.lfs.test-stable.shopee.pl",
			"AR": "https://api.lfs.test-stable.shopee.com.ar",
			"CL": "https://api.lfs.test-stable.shopee.cl",
			"CO": "https://api.lfs.test-stable.shopee.com.co",
		},
		"STAGING": {
			"BR": "https://api.lfs.staging.shopee.com.br",
			"SG": "https://api.lfs.staging.shopee.sg",
			"TW": "https://api.lfs.staging.shopee.tw",
			"TH": "https://api.lfs.staging.shopee.co.th",
			"ID": "https://api.lfs.staging.shopee.co.id",
			"MY": "https://api.lfs.staging.shopee.com.my",
			"PH": "https://api.lfs.staging.shopee.ph",
			"VN": "https://api.lfs.staging.shopee.vn",
			"MX": "https://api.lfs.staging.shopee.com.mx",
			"IN": "https://api.lfs.staging.shopee.in",
			"FR": "https://api.lfs.staging.shopee.fr",
			"ES": "https://api.lfs.staging.shopee.es",
			"PL": "https://api.lfs.staging.shopee.pl",
			"AR": "https://api.lfs.staging.shopee.com.ar",
			"CL": "https://api.lfs.staging.shopee.cl",
			"CO": "https://api.lfs.staging.shopee.com.co",
		},
		"LIVE": {
			"BR": "https://api.lfs.shopee.com.br",
			"SG": "https://api.lfs.shopee.sg",
			"TW": "https://api.lfs.shopee.tw",
			"TH": "https://api.lfs.shopee.co.th",
			"ID": "https://api.lfs.shopee.co.id",
			"MY": "https://api.lfs.shopee.com.my",
			"PH": "https://api.lfs.shopee.ph",
			"VN": "https://api.lfs.shopee.vn",
			"MX": "https://api.lfs.shopee.com.mx",
			"IN": "https://api.lfs.shopee.in",
			"FR": "https://api.lfs.shopee.fr",
			"ES": "https://api.lfs.shopee.es",
			"PL": "https://api.lfs.shopee.pl",
			"AR": "https://api.lfs.shopee.com.ar",
			"CL": "https://api.lfs.shopee.cl",
			"CO": "https://api.lfs.shopee.com.co",
		},
	}

	httpClient      *chassis.RestInvoker
	defaultTimeout  = time.Second * 5
	operatorAccount = "lfs-api"
)

func init() {
	httpClient = chassis.NewRestInvoker()
}
