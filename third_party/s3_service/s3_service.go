package s3_service

import (
	"bufio"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
)

var (
	s3ClinetMap             = map[string]*minio.Client{} // 适配多个S3客户端
	s3ClientWithEndpointMap = map[string]*minio.Client{}
)

func generateS3UniqueKey(accessKey, bucketKey string) string {
	return utils.GenKey(":", accessKey, bucketKey)
}

func generateS3WithEndpointUniqueKey(endpoint, accessKey, bucketKey string) string {
	return utils.GenKey(":", endpoint, generateS3UniqueKey(accessKey, bucketKey))
}

func InitS3Client(c *config.S3Config) error {
	s3Clinet, err := minio.New(c.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(c.AccessKeyID, c.SecretAccessKey, ""),
		Secure: false,
	})

	s3ClinetMap[generateS3UniqueKey(c.AccessKeyID, c.BucketKey)] = s3Clinet

	return err
}

func InitS3ClientWithEndpoint(c *config.S3Config) error {
	s3Client, err := minio.New(c.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(c.AccessKeyID, c.SecretAccessKey, ""),
		Secure: false,
	})

	s3ClientWithEndpointMap[generateS3WithEndpointUniqueKey(c.Endpoint, c.AccessKeyID, c.BucketKey)] = s3Client

	return err
}

type S3Service interface {
	Download(ctx utils.LCOSContext, AccessKeyID, bucketName, remoteFilePath string, timeout time.Duration) (io.ReadCloser, *lcos_error.LCOSError)
	DownloadWithEndpoint(ctx utils.LCOSContext, endpoint, accessKeyID, bucketName, remoteFilePath string, timeout time.Duration) (io.ReadCloser, *lcos_error.LCOSError)
	Upload(ctx utils.LCOSContext, AccessKeyID, bucketName, remoteFilePath string, reader io.Reader, size int64, timeout time.Duration) (*minio.UploadInfo, *lcos_error.LCOSError)
	GetFileUrlByFilePath(ctx utils.LCOSContext, accessKeyID, bucketName, remoteFilePath, fileName string, expireDays int) (string, *lcos_error.LCOSError)
	GetFileUrlByFilePathForUss2(ctx utils.LCOSContext, accessKeyID, bucketName, remoteFilePath string, expireDays int) (string, *lcos_error.LCOSError)
	UploadFileToS3(ctx utils.LCOSContext, AccessKeyID, bucketName, filePath string, timeout, expiredDays int, businessName string) (string, string, string, *lcos_error.LCOSError)
	UploadFileWithFileName(ctx utils.LCOSContext, AccessKeyID, bucketName, filePath string, timeout, expiredDays int, businessName, fileName string) (string, string, string, *lcos_error.LCOSError)
	ListObjects(ctx context.Context, accessKeyId, bucketName, prefix string) ([]string, *lcos_error.LCOSError) // todo SPLPS-13459 delete
}

type s3Service struct {
	getObjectOption *minio.GetObjectOptions
	putObjectOption *minio.PutObjectOptions
}

func NewS3Service() *s3Service {
	userMetaData := map[string]string{"x-amz-acl": "public-read"} // 设置权限为共有读私有写
	return &s3Service{
		getObjectOption: &minio.GetObjectOptions{},
		putObjectOption: &minio.PutObjectOptions{UserMetadata: userMetaData, PartSize: 5 * 1024 * 1024},
	}
}

func (s *s3Service) Download(ctx utils.LCOSContext, accessKeyID, bucketName, remoteFilePath string, timeout time.Duration) (io.ReadCloser, *lcos_error.LCOSError) {
	var c context.Context = ctx
	if timeout > 0 {
		var cancel context.CancelFunc
		c, cancel = context.WithTimeout(c, timeout)
		defer cancel()
	}

	if _, ok := s3ClinetMap[generateS3UniqueKey(accessKeyID, bucketName)]; !ok {
		errMsg := fmt.Sprintf("s3 client not found|access_key_id=%s", accessKeyID)
		monitor.AwesomeReportEvent(ctx, constant.CatModuleS3, constant.S3EventNameClientNotFound, monitor.StatusError, errMsg)
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, errMsg)
	}

	obj, err := s3ClinetMap[generateS3UniqueKey(accessKeyID, bucketName)].GetObject(ctx, bucketName, remoteFilePath, *s.getObjectOption)
	if err != nil {
		//if err := s3Clinet.FGetObject(ctx, bucketName, remoteFilePath, localFilePath, *s.getObjectOption); err != nil {
		monitor.AwesomeReportEvent(ctx, constant.CatModuleS3, constant.S3EventNameDownloadFail, monitor.StatusError, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, err.Error())
	}

	return obj, nil
}

func (s *s3Service) DownloadWithEndpoint(
	ctx utils.LCOSContext, endpoint, accessKeyID, bucketName, remoteFilePath string, timeout time.Duration,
) (io.ReadCloser, *lcos_error.LCOSError) {
	var c context.Context = ctx
	if timeout > 0 {
		var cancel context.CancelFunc
		c, cancel = context.WithTimeout(c, timeout)
		defer cancel()
	}

	s3Client, ok := s3ClientWithEndpointMap[generateS3WithEndpointUniqueKey(endpoint, accessKeyID, bucketName)]
	if !ok {
		errMsg := fmt.Sprintf("s3 client not found|access_key_id=%s", accessKeyID)
		_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleS3, constant.S3EventNameClientNotFound, monitor.StatusError, errMsg)
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, errMsg)
	}

	obj, err := s3Client.GetObject(ctx, bucketName, remoteFilePath, *s.getObjectOption)
	if err != nil {
		//if err := s3Clinet.FGetObject(ctx, bucketName, remoteFilePath, localFilePath, *s.getObjectOption); err != nil {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleS3, constant.S3EventNameDownloadFail, monitor.StatusError, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, err.Error())
	}

	return obj, nil
}

func (s *s3Service) Upload(ctx utils.LCOSContext, accessKeyID, bucketName, remoteFilePath string, reader io.Reader, size int64, timeout time.Duration) (*minio.UploadInfo, *lcos_error.LCOSError) {
	var c context.Context = ctx
	if timeout > 0 {
		var cancel context.CancelFunc
		c, cancel = context.WithTimeout(c, timeout)
		defer cancel()
	}

	if _, ok := s3ClinetMap[generateS3UniqueKey(accessKeyID, bucketName)]; !ok {
		errMsg := fmt.Sprintf("s3 client not found|access_key_id=%s", accessKeyID)
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, errMsg)
	}

	info, err := s3ClinetMap[generateS3UniqueKey(accessKeyID, bucketName)].PutObject(ctx, bucketName, remoteFilePath, reader, size, *s.putObjectOption)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, err.Error())
	}

	return &info, nil
}

func (s *s3Service) GetFileUrlByFilePath(ctx utils.LCOSContext, accessKeyID, bucketName, remoteFilePath, fileName string, expireDays int) (string, *lcos_error.LCOSError) {
	//if _, ok := s3ClinetMap[generateS3UniqueKey(accessKeyID, bucketName)]; !ok {
	//	errMsg := fmt.Sprintf("s3 client not found|access_key_id=%s", accessKeyID)
	//	return "", lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, errMsg)
	//}
	//
	//key, err := crypto.CryptoToken(fileName, uint32(expireDays*24*3600))
	//if err != nil {
	//	return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	//}
	//return fmt.Sprintf("%s%s/%s?key=%s", config.GetConf(ctx).S3.BaseUrl, bucketName, remoteFilePath, key), nil

	// SPLN-26221 change url to uss
	return s.GetFileUrlByFilePathForUss(ctx, accessKeyID, bucketName, remoteFilePath, fileName, expireDays)
}

// GetFileUrlByFilePathForUss
// use uss to export file to user. will no longer use external2
func (s *s3Service) GetFileUrlByFilePathForUss(ctx utils.LCOSContext, accessKeyID, bucketName, remoteFilePath, fileName string, expireDays int) (string, *lcos_error.LCOSError) {
	if _, ok := s3ClinetMap[generateS3UniqueKey(accessKeyID, bucketName)]; !ok {
		errMsg := fmt.Sprintf("s3 client not found|access_key_id=%s", accessKeyID)
		return "", lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, errMsg)
	}
	object, err := s3ClinetMap[generateS3UniqueKey(accessKeyID, bucketName)].PresignedGetObject(ctx, bucketName, remoteFilePath, time.Duration(expireDays)*24*time.Hour, nil)
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, err.Error())
	}
	return fmt.Sprintf("https://%s%s?%s", object.Host, object.Path, object.RawQuery), nil
}

// GetFileUrlByFilePathForUss2 基于文件在uss服务器的路径获取下载URL。与GetFileUrlByFilePathForUss基本一致，区别是使用URL.String()生成URL，同时去除了无用的fileName字段
func (s *s3Service) GetFileUrlByFilePathForUss2(ctx utils.LCOSContext, accessKeyID, bucketName, remoteFilePath string, expireDays int) (string, *lcos_error.LCOSError) {
	if _, ok := s3ClinetMap[generateS3UniqueKey(accessKeyID, bucketName)]; !ok {
		errMsg := fmt.Sprintf("s3 client not found|access_key_id=%s", accessKeyID)
		return "", lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, errMsg)
	}
	object, err := s3ClinetMap[generateS3UniqueKey(accessKeyID, bucketName)].PresignedGetObject(ctx, bucketName, remoteFilePath, time.Duration(expireDays)*24*time.Hour, nil)
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, err.Error())
	}
	object.Scheme = "https" // 替换使用https协议
	return object.String(), nil
}

func (s *s3Service) UploadFile(ctx utils.LCOSContext, AccessKeyID, bucketName, filePath string, timeout int, businessName string) (string, *lcos_error.LCOSError) {
	fileName := filepath.Base(filePath)
	var remoteFilePath string
	if businessName == "" {
		remoteFilePath = fmt.Sprintf("%s/%s/%s", strings.ToLower(config.GetConf(ctx).EnvConfig.Env), recorder.Now(ctx).Format("20060102"), fileName)
	} else {
		remoteFilePath = fmt.Sprintf("%s/%s/%s/%s", strings.ToLower(config.GetConf(ctx).EnvConfig.Env), businessName, recorder.Now(ctx).Format("20060102"), fileName)
	}

	file, err := os.Open(filePath)
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	fileSize, err := file.Stat()
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	ioReader := bufio.NewReader(file)
	if _, lcosErr := s.Upload(ctx, AccessKeyID, bucketName, remoteFilePath, ioReader, fileSize.Size(), time.Duration(timeout)*time.Second); lcosErr != nil {
		return "", lcosErr
	}
	return remoteFilePath, nil
}

// UploadFileToS3
//
//	@Description: 将本地文件上传到S3服务器，并且生成可以被下载的https链接
//	@receiver s
//	@param ctx
//	@param AccessKeyID     所上传的S3服务器的access key
//	@param bucketName      所上传的S3服务器的bucket name
//	@param filePath        本地文件的绝对路径名
//	@param timeout         上传文件的timeout
//	@param expiredDays     文件失效时间
//	@return string         文件下载链接，https
//	@return string         文件名
//	@return string         文件大小的字符串，eg: "10.2KB"
//	@return *lcos_error.LCOSError
func (s *s3Service) UploadFileToS3(ctx utils.LCOSContext, AccessKeyID, bucketName, filePath string, timeout, expiredDays int, businessName string) (string, string, string, *lcos_error.LCOSError) {

	// 切分filePath得到fileName
	fileName := filepath.Base(filePath)

	file, err := os.Open(filePath)
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.CtxLogErrorf(ctx, "cannot close file [%s], err=%s", filePath, err.Error())
		}
	}(file)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file [%s], err=%v", filePath, err)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	fileSize, err := file.Stat()
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file [%s], err=%v", filePath, err)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	ioReader := bufio.NewReader(file)

	// for business name, need to check whether is empty
	var remoteFilePath string
	if businessName == "" {
		remoteFilePath = fmt.Sprintf("%s/%s/%s", strings.ToLower(config.GetConf(ctx).EnvConfig.Env), recorder.Now(ctx).Format("20060102"), fileName)
	} else {
		remoteFilePath = fmt.Sprintf("%s/%s/%s/%s", strings.ToLower(config.GetConf(ctx).EnvConfig.Env), businessName, recorder.Now(ctx).Format("20060102"), fileName)
	}

	_, lcosErr := s.Upload(ctx, AccessKeyID, bucketName, remoteFilePath, ioReader, fileSize.Size(), time.Duration(timeout)*time.Second)
	if lcosErr != nil {
		return "", "", "", lcosErr
	}

	// 将上传的文件转为file Url
	fileUrl, lcosErr := s.GetFileUrlByFilePath(ctx, AccessKeyID, bucketName, remoteFilePath, fileName, expiredDays)
	if lcosErr != nil {
		return "", "", "", lcosErr
	}

	// 将文件大小改为类似于2.3M的格式，保留两位小数
	var fileSizeStr string
	if fileSize.Size() > 1024*1024 { // 结果为MB
		fileSizeStr = fmt.Sprintf("%.2fMB", float64(fileSize.Size())/(1024*1024))
	} else if fileSize.Size() > 1024 {
		fileSizeStr = fmt.Sprintf("%.2fKB", float64(fileSize.Size())/1024)
	} else {
		fileSizeStr = fmt.Sprintf("%.2fB", float64(fileSize.Size()))
	}
	return fileUrl, fileName, fileSizeStr, nil
}

func (s *s3Service) UploadFileWithFileName(ctx utils.LCOSContext, AccessKeyID, bucketName, filePath string, timeout, expiredDays int, businessName, fileName string) (string, string, string, *lcos_error.LCOSError) {
	if len(fileName) == 0 {
		fileName = filepath.Base(filePath)
	}

	file, err := os.Open(filePath)
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.CtxLogErrorf(ctx, "cannot close file [%s], err=%s", filePath, err.Error())
		}
	}(file)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file [%s], err=%v", filePath, err)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	fileSize, err := file.Stat()
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file [%s], err=%v", filePath, err)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	ioReader := bufio.NewReader(file)

	// for business name, need to check whether is empty
	var remoteFilePath string
	if businessName == "" {
		remoteFilePath = fmt.Sprintf("%s/%s/%s", strings.ToLower(config.GetConf(ctx).EnvConfig.Env), time.Now().Format("20060102"), fileName)
	} else {
		remoteFilePath = fmt.Sprintf("%s/%s/%s/%s", strings.ToLower(config.GetConf(ctx).EnvConfig.Env), businessName, time.Now().Format("20060102"), fileName)
	}

	_, lcosErr := s.Upload(ctx, AccessKeyID, bucketName, remoteFilePath, ioReader, fileSize.Size(), time.Duration(timeout)*time.Second)
	if lcosErr != nil {
		return "", "", "", lcosErr
	}

	// 将上传的文件转为file Url
	fileUrl, lcosErr := s.GetFileUrlByFilePathForUss2(ctx, AccessKeyID, bucketName, remoteFilePath, expiredDays)
	if lcosErr != nil {
		return "", "", "", lcosErr
	}

	// 将文件大小改为类似于2.3M的格式，保留两位小数
	var fileSizeStr string
	if fileSize.Size() > 1024*1024 { // 结果为MB
		fileSizeStr = fmt.Sprintf("%.2fMB", float64(fileSize.Size())/(1024*1024))
	} else if fileSize.Size() > 1024 {
		fileSizeStr = fmt.Sprintf("%.2fKB", float64(fileSize.Size())/1024)
	} else {
		fileSizeStr = fmt.Sprintf("%.2fB", float64(fileSize.Size()))
	}
	return fileUrl, fileName, fileSizeStr, nil
}

func (s *s3Service) ListObjects(ctx context.Context, accessKeyId, bucketName, prefix string) ([]string, *lcos_error.LCOSError) {
	logger.CtxLogInfof(ctx, "ListObjects Start, accessKeyId: %s, bucketName: %s, prefix: %s", accessKeyId, bucketName, prefix)
	if _, ok := s3ClinetMap[generateS3UniqueKey(accessKeyId, bucketName)]; !ok {
		errMsg := fmt.Sprintf("s3 client not found|access_key_id=%s", accessKeyId)
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPS3DownloadFileErrorCode, errMsg)
	}
	objkeys := s3ClinetMap[generateS3UniqueKey(accessKeyId, bucketName)].ListObjects(ctx, bucketName, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	})
	var pathList []string
	for object := range objkeys {
		// 第一个key是目录名，需要过滤
		if object.Key != prefix {
			pathList = append(pathList, object.Key)
		}
	}
	return pathList, nil
}
