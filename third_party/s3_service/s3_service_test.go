package s3_service

import (
	"bufio"
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"io/ioutil"
	"os"
	"testing"
	"time"

	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
)

func Test_Init_Client(t *testing.T) {

	t.Skip()

	err := InitS3Client(&config.S3Config{
		AccessKeyID:     "0Y9UD3FKQ45OAZ41K0OB",
		SecretAccessKey: "FABXcJkf8xOg8PvwM0ORTbINnq51TPMjCUDSuspc",
		Endpoint:        "s3.i.test.sz.shopee.io",
	})
	if err != nil {
		t.Fatalf("InitS3Client failed, err[%v]", err)
	}
}

func Test_Download(t *testing.T) {

	t.Skip()

	Test_Init_Client(t)
	s3Service := NewS3Service()
	obj, err := s3Service.Download(utils.NewCommonCtx(context.Background()), "0Y9UD3FKQ45OAZ41K0OB", "shopee.lcs.test", "/id/branch/lcs_branch_info_66666_8026_1621926123.json", time.Second*10)
	if err != nil {
		t.Fatalf("Download failed, err[%v]", err.Msg)
	}

	str, e := ioutil.ReadAll(obj)
	if e != nil {
		t.Fatal(e)
	}

	t.Logf("%s", str)
}

func Test_s3Service_GetFileUrlByFilePathForUss(t *testing.T) {

	t.Skip()

	type args struct {
		ctx            utils.LCOSContext
		accessKeyID    string
		bucketName     string
		remoteFilePath string
		fileName       string
		expireDays     int
	}
	tests := []struct {
		name string
		args args
		want *lcos_error.LCOSError
	}{
		{
			name: "test uss download",
			args: args{
				ctx:            utils.NewCommonCtx(context.Background()),
				accessKeyID:    "60055733",
				bucketName:     "basic_service_report",
				remoteFilePath: "/test/edd_ongoing/20221008/preview-order-20221008170529.xlsx",
				fileName:       "preview-order-20221008170529.xlsx",
				expireDays:     7,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := InitS3Client(&config.S3Config{
				AccessKeyID:     "60055733",
				SecretAccessKey: "LbyuNkhGqVVsloKkgeJhHjklDqAHvVkw",
				Endpoint:        "proxy.uss.s3.test.shopee.io",
				BucketKey:       "basic_service_report",
				TimeOut:         10,
				ExpirationDays:  7,
			})
			if err != nil {
				t.Fatal(err.Error())
			}
			s := NewS3Service()
			got, got1 := s.GetFileUrlByFilePathForUss(tt.args.ctx, tt.args.accessKeyID, tt.args.bucketName, tt.args.remoteFilePath, tt.args.fileName, tt.args.expireDays)
			if got1 != tt.want {
				t.Errorf("GetFileUrlByFilePathForUss() got = %v, want %v", got, tt.want)
			}
			t.Log(got)
		})
	}
}

func Test_upload(t *testing.T) {

	t.Skip()

	err := InitS3Client(&config.S3Config{
		AccessKeyID:     "60055733",
		SecretAccessKey: "LbyuNkhGqVVsloKkgeJhHjklDqAHvVkw",
		Endpoint:        "proxy.uss.s3.test.shopee.io",
		BucketKey:       "basic_service_report",
	})
	if err != nil {
		t.Fatalf("InitS3Client failed, err[%v]", err)
	}

	fileName := "/Users/<USER>/Desktop/test111111.txt"
	file, err := os.Open(fileName)

	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			t.Logf("cannot close file [%s], err=%s", fileName, err.Error())
		}
	}(file)

	if err != nil {
		t.Fatalf("fail to open file[%s], err=[%s]", fileName, err.Error())
	}
	fileSize, err := file.Stat()
	if err != nil {
		t.Fatalf("fail to open file[%s], err=[%s]", fileName, err.Error())
	}
	ioReader := bufio.NewReader(file)
	remoteFilePath := "test/edd_ongoing/20220906/test111111.txt"

	testS3Service := NewS3Service()
	fileInfo, lcosErr := testS3Service.Upload(utils.NewCommonCtx(context.Background()), "60055733", "basic_service_report", remoteFilePath, ioReader, fileSize.Size(), 1*time.Hour)
	if lcosErr != nil {
		t.Fatalf("upload to s3 error:[%s]", lcosErr.Msg)
	}
	t.Logf("successfully upload file:[%d]", fileInfo.Size)
}
