package channel_service

type Channel struct {
	ChannelID      int    `json:"channel_id"`
	Channel<PERSON>ame    string `json:"channel_name"`
	MaskingType    uint8  `json:"masking_type"`
	IntegratedType uint8  `json:"integrated_type"`
	FromCountry    string `json:"from_country"`
	Country        string `json:"country"`
	CBType         uint8  `json:"cb_type"`
	FlowType       int    `json:"flow_type"`
}

type AllChannelsResponse struct {
	Retcode int32      `json:"retcode"`
	Data    []*Channel `json:"data"`
	Message string     `json:"message"`
	Detail  string     `json:"detail"`
}

type JwtInfo struct {
	Exp  uint32                 `json:"exp"`
	Data map[string]interface{} `json:"data"`
	Info struct {
		Entity struct {
			Country string `json:"country"`
		} `json:"entity"`
	} `json:"info"`
}

type ThreePLInfo struct {
	ProductID    string `json:"product_id"`
	TplUniqueKey string `json:"3pl_unique_key"`
	TplName      string `json:"3pl_name"`
}

type ListThreePLInfoList struct {
	Retcode int32          `json:"retcode"`
	Data    []*ThreePLInfo `json:"data"`
}
