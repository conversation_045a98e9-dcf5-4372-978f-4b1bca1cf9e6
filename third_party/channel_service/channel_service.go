package channel_service

import (
	"context"
	"encoding/json"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	"strconv"
	"strings"
)

// GetAllChannels 用于获取某个region的所有channel
func GetAllChannels(ctx context.Context, region string) (map[string]*Channel, *lcos_error.LCOSError) {
	data := map[string]interface{}{}
	paramByte, err := json.Marshal(GenerateJwtInfo(ctx, data, region))
	env := utils.GetEnv(ctx)
	url := hosts[env]
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	responseByte, lcosErr := SendHeaderJwt(ctx, data, url+"/api/sls-ops/sls/channelConfig/all_channel_list", region, "GET", account, cf.GetThirdServiceSecretConfig(ctx).Channel)
	if lcosErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, lcosErr.Msg)
	}

	response := &AllChannelsResponse{}
	if err := json.Unmarshal(responseByte, response); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if response.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(ops_service.LocationCodeMap[response.Retcode], response.Message+", "+response.Detail+", param: "+string(paramByte))
	}

	resultMap := map[string]*Channel{}
	channelList := response.Data
	for _, channel := range channelList {
		resultMap[strconv.Itoa(channel.ChannelID)] = channel
	}

	return resultMap, nil
}

// GetThreePLInfoList 用于3pl信息列表
func GetThreePLInfoList(ctx context.Context, region string, channelIDList []string) ([]*ThreePLInfo, *lcos_error.LCOSError) {
	data := map[string]interface{}{}
	data["country"] = region
	data["channel_id_list"] = strings.Join(channelIDList, ",")
	paramByte, err := json.Marshal(GenerateJwtInfo(ctx, data, region))
	env := utils.GetEnv(ctx)
	url := hosts[env]
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	responseByte, lcosErr := SendHeaderJwt(ctx, data, url+"/api/sls-ops/sls/channelConfig/list_3pl_info", region, "GET", account, cf.GetThirdServiceSecretConfig(ctx).Channel)
	if lcosErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, lcosErr.Msg)
	}

	response := &ListThreePLInfoList{}
	if err := json.Unmarshal(responseByte, response); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if response.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(ops_service.LocationCodeMap[response.Retcode], ", param: "+string(paramByte))
	}

	return response.Data, nil
}
