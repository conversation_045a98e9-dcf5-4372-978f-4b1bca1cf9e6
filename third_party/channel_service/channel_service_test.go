package channel_service

import (
	"context"
	"fmt"
	"testing"
)

func TestGetAllChannels(t *testing.T) {
	result, lcosErr := GetAllChannels(context.Background(), "SG")
	if lcosErr != nil {
		t.Error(lcosErr.Msg)
	}
	fmt.Printf("%v", result)
}

func TestGetAllThreePlList(t *testing.T) {
	results, lcosErr := GetThreePLInfoList(context.Background(), "ID", []string{"88001", "88021"})
	if lcosErr != nil {
		t.Error(lcosErr.Msg)
	}
	for _, result := range results {
		t.Log(result)
	}
}
