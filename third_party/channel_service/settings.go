package channel_service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"time"
)

var (
	account = "sls-ops"
	hosts   = map[string]string{
		"LOCAL":   "https://sls.test.shopeemobile.com",
		"TEST":    "https://sls.test.shopeemobile.com",
		"UAT":     "https://sls.uat.shopeemobile.com",
		"STAGING": "https://sls.staging.shopeemobile.com",
		"LIVEISH": "https://sls.shopeemobile.com",
		"LIVE":    "https://sls.shopeemobile.com",
	}

	defaultTimeout = 100 * time.Second
	httpClient     *chassis.RestInvoker
)

func init() {
	httpClient = chassis.NewRestInvoker()
}

func GenerateJwtInfo(ctx context.Context, data map[string]interface{}, region string) *JwtInfo {
	jwtInfo := &JwtInfo{}
	timestamp := uint32(recorder.Now(ctx).Unix()) + 1000
	jwtInfo.Exp = timestamp
	jwtInfo.Data = data
	jwtInfo.Info.Entity.Country = region
	return jwtInfo
}
