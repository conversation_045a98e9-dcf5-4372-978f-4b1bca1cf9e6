package channel_service

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	http_util "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/jwt"
	"io/ioutil"
	"net/http"
	"strings"
)

// SendHeaderJwt 通用的jwt请求
func SendHeaderJwt(ctx context.Context, data map[string]interface{}, url, region, method, account, secret string) ([]byte, *lcos_error.LCOSError) {

	jwtInfo := GenerateJwtInfo(ctx, data, region)

	payload := map[string]interface{}{
		"data":      data,
		"timestamp": recorder.Now(ctx).Unix(),
		"exp":       recorder.Now(ctx).Unix() + 1000,
		"info":      jwtInfo.Info,
	}
	headers := map[string]interface{}{
		"typ":  "JWT",
		"alg":  "HS256",
		"optr": account,
	}
	enc, err := jwt.GetJwtDataV2(secret, headers, payload)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	var req *http.Request
	if method == "POST" {
		d, _ := json.Marshal(data)
		req, err = chassis.NewRestRequest(http.MethodPost, url, d)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, err.Error())
		}
		req.Header.Add("Content-Type", "application/json")
	} else {
		var queryStringList []string
		for key, value := range data {
			queryStringList = append(queryStringList, fmt.Sprintf("%v=%v", key, value))
		}

		queryString := strings.Join(queryStringList, "&")
		if queryString != "" {
			url = url + "?" + queryString
		}
		req, err = chassis.NewRestRequest(http.MethodGet, url, nil)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, err.Error())
		}
	}
	req.Header.Add("jwt-token", enc)

	httpCtx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	resp, err := http_util.HttpRequest(httpCtx, httpClient, req)
	//resp, err := client.CacheEntry
	if err != nil {
		logger.LogErrorf("send http jwt request failed,err[%v]", err)
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, err.Error())
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	logger.LogInfof("send jwt http request,method[%s],url[%s],req[%v],resp[%s]", method, url, data, body)
	if err != nil {
		logger.LogErrorf("send http jwt request failed")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	return body, nil
}
