package seatalk

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"reflect"
	"testing"
)

func TestNotifyWithTextMessage(t *testing.T) {
	type args struct {
		ctx                utils.LCOSContext
		webhook            string
		message            string
		mentionedEmailList []string
		atAll              bool
	}
	tests := []struct {
		name string
		args args
		want *lcos_error.LCOSError
	}{
		{
			name: "TestNotifyWithTextMessage",
			args: args{
				ctx:     utils.NewCommonCtx(context.Background()),
				webhook: "https://openapi.seatalk.io/webhook/group/XgAb0UJUSo6ZQAecv9Qznw", // TODO 指定为自己测试用的webhook，检查seatalk消息是否符合预期
				message: "https://sls-lhs.sp-cdn.susercontent.com/shopee-slsopsrecon-sg-live/ssc-ops/20240926/689275daf34c5695de43c48104edd3b9/edd_add3_-_special_for_NH.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=50050920%2F20240926%2Fdefault%2Fs3%2Faws4_request&X-Amz-Date=20240926T102619Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=5285f5e1803837d73714df276dfca5d6efcc3121222ef580556c744454abc867",
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NotifyWithTextMessage(tt.args.ctx, tt.args.webhook, tt.args.message, tt.args.mentionedEmailList, tt.args.atAll); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NotifyWithTextMessage() = %v, want %v", got, tt.want)
			}
		})
	}
}
