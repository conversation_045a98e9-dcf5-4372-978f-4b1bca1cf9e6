package seatalk

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net/http"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	jsoniter "github.com/json-iterator/go"
)

const (
	// message type
	TextMessage     = "text"
	ImageMessage    = "image"
	MarkdownMessage = "markdown"
)

type MessageFormat int64

const (
	PlainTextFormat MessageFormat = 2
	MarkdownFormat  MessageFormat = 1
)

type SeatalkNotifyText struct {
	Content            string        `json:"content" form:"content"`
	MentionedEmailList []string      `json:"mentioned_email_list" form:"mentioned_email_list"`
	AtAll              bool          `json:"at_all" form:"at_all"`
	Format             MessageFormat `json:"format" form:"format"` // 不传默认使用Markdown格式，如果发送消息包含URL，最好使用PlainTextFormat
}

type SeatalkNotifyRequest struct {
	Tag  string            `json:"tag" form:"tag"`
	Text SeatalkNotifyText `json:"text" form:"text"`
}

type SeatalkNotifyResponse struct {
	Code    int    `json:"code" form:"code"`
	Message string `json:"message" form:"message"`
}

func NotifyWithTextMessage(ctx utils.LCOSContext, webhook, message string, mentionedEmailList []string, atAll bool) *lcos_error.LCOSError {
	req := SeatalkNotifyRequest{
		Tag: TextMessage,
		Text: SeatalkNotifyText{
			Content:            message,
			MentionedEmailList: mentionedEmailList,
			AtAll:              atAll,
			Format:             PlainTextFormat,
		},
	}
	return Notify(ctx, webhook, req)
}

// Notify SeaTalk通知，接入文档：https://open.seatalk.io/docs/system-account
func Notify(ctx utils.LCOSContext, webhook string, req SeatalkNotifyRequest) *lcos_error.LCOSError {
	reqJSON, err := jsoniter.Marshal(req)
	if err != nil {
		msg := fmt.Sprintf("marshal request error|req=%+v, cause=%s", req, err.Error())
		logger.CtxLogErrorf(ctx, "seatalk notify error|%s", msg)
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, msg)
	}
	resp, err := http.Post(webhook, "application/json", bytes.NewBuffer(reqJSON))
	if err != nil {
		msg := fmt.Sprintf("send post request error|req=%+v, cause=%s", req, err.Error())
		logger.CtxLogErrorf(ctx, "seatalk notify error|%s", msg)
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, msg)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		msg := fmt.Sprintf("status code not 200|req=%+v, code=%d", req, resp.StatusCode)
		logger.CtxLogErrorf(ctx, "seatalk notify error|%s", msg)
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, msg)
	}
	bodyJSON, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		msg := fmt.Sprintf("read response body error|req=%+v, cause=%s", req, err.Error())
		logger.CtxLogErrorf(ctx, "seatalk notify error|%s", msg)
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, msg)
	}
	body := &SeatalkNotifyResponse{}
	if err = jsoniter.Unmarshal(bodyJSON, body); err != nil {
		msg := fmt.Sprintf("unmarshal response body error|req=%+v, cause=%s", req, err.Error())
		logger.CtxLogErrorf(ctx, "seatalk notify error|%s", msg)
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, msg)
	}
	if body.Code != 0 {
		msg := fmt.Sprintf("send seatalk message error|req=%+v, code=%d, message=%s", req, body.Code, body.Message)
		logger.CtxLogErrorf(ctx, "seatalk notify error|%s", msg)
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, msg)
	}
	return nil
}
