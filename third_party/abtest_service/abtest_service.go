package abtest_service

import (
	"context"
	"fmt"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/monitor"

	abtest_edd "git.garena.com/shopee/bg-logistics/logistics/logistics-abtest/api/edd"
	abtest_eddschema "git.garena.com/shopee/bg-logistics/logistics/logistics-abtest/api/edd/schema"
	abtest_api "git.garena.com/shopee/bg-logistics/logistics/logistics-abtest/api/schema"
)

func InitAbtest(c *config.AbtestConfig) error {
	if c.Disabled {
		logger.LogInfo("[abtest] disabled")
		return nil
	}
	abConfig := &abtest_api.AbtestConfig{
		Env:         utils.GetEnv(context.Background()),
		Interval:    c.Interval,
		ProjectKeys: c.ProjectKeys,
		SceneKeys:   c.SceneKeys,
	}
	if err := abtest_edd.InitEddAbTest(abConfig); err != nil {
		logger.LogErrorf("[abtest] init error, error=%s", err.Error())
		return err
	}
	logger.LogInfo("[abtest] init ok")
	return nil
}

func GetEDDAbtestGroup(ctx context.Context, region string, buyerId string, productId string, slsTn string) (*abtest_eddschema.EddExpData, *lcos_error.LCOSError) {
	/*
		获取 EDD Abtest 分组结果
	*/
	abParam := abtest_eddschema.NewEddExpParam(region, buyerId, slsTn)
	abParam.SetLogisticProductId(&productId)

	res := abtest_edd.GetEddExpGroup(ctx, abParam)
	if len(res) == 0 {
		logger.CtxLogInfof(ctx, "abtest result empty, abParam=%s", utils.MarshToStringWithoutError(abParam))
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDAbTest, "GetEddExpGroup", "empty", slsTn)
		return nil, lcos_error.NewLCOSError(lcos_error.AlgoAbtestGroupError, "abtest result empty")
	}
	groupRes := res[0] // edd 只关注第一层结果
	if groupRes.ErrCode == 0 {
		_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDAbTest, "GetEddExpGroup", constant.StatusSuccess, "")
		logger.CtxLogInfof(ctx, "abtest result ok: %s", utils.MarshToStringWithoutError(groupRes))
		return &groupRes.EddExpData, nil
	}

	_ = monitor.AwesomeReportEvent(ctx, constant.CatEDDAbTest, "GetEddExpGroup", strconv.Itoa(int(groupRes.ErrCode)), groupRes.ErrMsg)
	logger.CtxLogErrorf(ctx, "abtest result error: %s", utils.MarshToStringWithoutError(groupRes))
	return nil, lcos_error.NewLCOSError(lcos_error.AlgoAbtestGroupError, fmt.Sprintf("abtest result error, code=%v, msg=%s", groupRes.ErrCode, groupRes.ErrMsg))
}
