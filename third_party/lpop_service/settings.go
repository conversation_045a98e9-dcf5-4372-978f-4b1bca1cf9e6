package lpop_service

var (
	account = "lcos"
	// TODO: token暂时随机定义
	token = "test"

	// lpop service config
	LPOPServiceName = "lpop-grpc"

	SlaConfigServiceSchemaID    = "lpop_protobuf.SlaConfService"
	SupplierInfoServiceSchemaID = "lpop_protobuf.SupplierInfoService"

	SearchHolidayOperationID           = "SearchHoliday"
	SearchLogisticsEventOperationID    = "SearchLogisticsEvent"
	GetSupplierInfoByLineIdOperationID = "GetSupplierInfoByLineId"
)
