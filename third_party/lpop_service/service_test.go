package lpop_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	jsoniter "github.com/json-iterator/go"
	"os"
	"path"
	"strings"
	"testing"
	"time"
)

func TestGetThreePLHolidays(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "my")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	lpopService := NewLpopService()
	holidays, lcosErr := lpopService.GetThreePLHolidaysWithLruCache(utils.NewCommonCtx(context.WithValue(context.Background(), "logid", "1231huhhuhhuhu3123")), "LMY11", 1, 111, "MY", false, false)

	if lcosErr != nil {
		t.Errorf("error=%v", lcosErr.Msg)
	} else {
		t.Log(holidays)
	}
}

func TestGetEventCodeList(t *testing.T) {
	os.Setenv("CHASSIS_CONF_DIR", "/Users/<USER>/Documents/golang/src/logistics-core-service/conf/chassis_conf/grpc")
	os.Setenv("CHASSIS_HOME", "/Users/<USER>/Documents/golang/src/logistics-core-service")
	os.Setenv("SSC_ENV", "test")
	os.Setenv("ENV", "TEST")
	if err := chassis.Init(); err != nil {
		t.Error("Init failed." + err.Error())
		return
	}
	lpopService := NewLpopService()
	slsEventCodeList, lfsEventCodeList, lcosErr := lpopService.GetSlaLMInboundStatusWithLruCache(utils.NewCommonCtx(context.Background()))
	if lcosErr != nil {
		t.Errorf("error=%v", lcosErr.Msg)
	} else {
		t.Log(slsEventCodeList)
		t.Log(lfsEventCodeList)
	}
}

func TestGetEventCodeListWithLru(t *testing.T) {
	os.Setenv("CHASSIS_CONF_DIR", "/Users/<USER>/Documents/golang/src/logistics-core-service/conf/chassis_conf/grpc")
	os.Setenv("CHASSIS_HOME", "/Users/<USER>/Documents/golang/src/logistics-core-service")
	os.Setenv("SSC_ENV", "test")
	os.Setenv("ENV", "TEST")
	if err := chassis.Init(); err != nil {
		t.Error("Init failed." + err.Error())
		return
	}
	lpopService := NewLpopService()
	slsEventCodeList, lfsEventCodeList, lcosErr := lpopService.GetSlaLMInboundStatusWithLruCache(utils.NewCommonCtx(context.Background()))
	if lcosErr != nil {
		t.Errorf("error=%v", lcosErr.Msg)
	} else {
		t.Log(slsEventCodeList)
		t.Log(lfsEventCodeList)
	}

	slsEventCodeList, lfsEventCodeList, lcosErr = lpopService.GetSlaLMInboundStatusWithLruCache(utils.NewCommonCtx(context.Background()))
	if lcosErr != nil {
		t.Errorf("error=%v", lcosErr.Msg)
	} else {
		t.Log(slsEventCodeList)
		t.Log(lfsEventCodeList)
	}
}

func TestOmniempty(t *testing.T) {
	type TestStruct struct {
		Age  int    `json:"age,omitempty"`
		Name string `json:"name"`
	}

	a := &TestStruct{Age: 0, Name: "test"}
	result, _ := jsoniter.Marshal(a)
	t.Log(string(result))
}

func TestGetSupplierInfoByLineId(t *testing.T) {
	ctx := utils.NewCommonCtx(context.WithValue(context.Background(), "logid", "muhaobing-test-0428-1"))
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-muhaobing")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "tw")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("init chassis error: %s", err.Error())
		return
	}

	lpopService := NewLpopService()

	// 多次调用验证缓存有效性
	for i := 0; i < 5; i++ {
		startTime := recorder.Now(ctx)
		supplierInfoMap, lcosErr := lpopService.GetSupplierInfoByLineId(ctx, []string{"LMY11", "LTW5"})
		if lcosErr != nil {
			t.Errorf("GetSupplierInfoByLineId error: %s", lcosErr.Msg)
		} else {
			t.Log(supplierInfoMap)
		}
		t.Log(fmt.Sprintf(">>> latency: %dms", time.Since(startTime).Milliseconds()))
	}

	// map[LMY11:line_id:"LMY11" supplier_id:10030 supplier_name:"ABX" supplier_description:"" ]
	// >>> latency: 3154ms
	// map[LMY11:line_id:"LMY11" supplier_id:10030 supplier_name:"ABX" supplier_description:"" ]
	// >>> latency: 0ms
	// map[LMY11:line_id:"LMY11" supplier_id:10030 supplier_name:"ABX" supplier_description:"" ]
	// >>> latency: 0ms
	// map[LMY11:line_id:"LMY11" supplier_id:10030 supplier_name:"ABX" supplier_description:"" ]
	// >>> latency: 0ms
	// map[LMY11:line_id:"LMY11" supplier_id:10030 supplier_name:"ABX" supplier_description:"" ]
	// >>> latency: 0ms
}
