package lpop_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache_with_ttl"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	lpop_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v7/logistics-partner-open-platform/go"
	"strings"
	"time"
)

var invoker = chassis.NewRPCInvoker()

var lmInboundStatuslruCache *cache.LruCache
var lmHolidaylruCache *cache.LruCache
var supplierInfoCache *cache.LruCache

func init() {
	lmInboundStatuslruCache, _ = cache.NewLruCache(cache.LMInboundStatusLruName, &lpop_protobuf.SearchEventResponse{})
	lmHolidaylruCache, _ = cache.NewLruCache(cache.LMHolidayLruName, &lpop_protobuf.SearchHolidayResponse{})
	supplierInfoCache, _ = cache.NewLruCache(cache.SupplierInfoLruName, &lpop_protobuf.SupplierInfoWithLine{})
}

const lmInboundEvent = "L021"
const lfsActionType = 3
const slsActionType = 1

type lpopService struct {
	serviceName string
}

func NewLpopService() *lpopService {
	return &lpopService{serviceName: LPOPServiceName}
}

func getAllExtendedNonWorkingDays(ctx context.Context, lpopReply *lpop_protobuf.SearchHolidayResponse, stateLocationID uint32, region string, disableHolidays, disableWeekends bool) []*LpopNonWorkingDay {

	// make a holiday map
	holidayMap := make(map[string]bool)
	for _, singleHoliday := range lpopReply.GetHolidayInfoList() {
		if disableHolidays {
			break
		}

		if singleHoliday.GetStateId() == uint64(stateLocationID) || singleHoliday.GetStateId() == 0 {
			holidayMap[singleHoliday.GetHoliday()] = true
		}
	}

	// make a weekend list
	weekendMap := make(map[int]bool)
	for _, singleWeekend := range lpopReply.GetWeekDayInfoList() {
		if disableWeekends {
			break
		}

		if singleWeekend.GetStateId() == uint64(stateLocationID) || singleWeekend.GetStateId() == 0 {
			if singleWeekend.GetFriDay() {
				weekendMap[5] = true
			}
			if singleWeekend.GetSatDay() {
				weekendMap[6] = true
			}
			if singleWeekend.GetSunDay() {
				weekendMap[0] = true // time.weekday return 0 as sunday
			}
		}
	}

	// get now date
	today := pickup.TransferTimeStampToTime(utils.GetTimestamp(ctx), region)

	results := make([]*LpopNonWorkingDay, 0)

	for i := constant.ExtendDaysMin; i <= constant.ExtendDaysMax; i++ {
		currentDate := time.Date(today.Year(), today.Month(), today.Day()+i, today.Hour(), today.Minute(), today.Second(), today.Nanosecond(), today.Location())
		dayString := currentDate.Format("2006-01-02")
		if holidayMap[dayString] || weekendMap[int(currentDate.Weekday())] {
			results = append(results, &LpopNonWorkingDay{
				Region:  region,
				Holiday: dayString,
				StateID: uint64(stateLocationID),
			})
		}
	}

	return results
}

func (l *lpopService) GetThreePLHolidaysWithLruCache(ctx utils.LCOSContext, lineID string, isSiteLine uint8, stateLocationID int32, region string, disableHolidays, disableWeekends bool) ([]*LpopNonWorkingDay, *lcos_error.LCOSError) {
	request := &lpop_protobuf.SearchHolidaysRequest{
		ReqHeader:  genLpopReqHeader(ctx),
		ResourceId: utils.NewString(lineID),
	}
	var resourceType uint32 = 2 // 3pl id
	if isSiteLine == constant.TRUE {
		resourceType = 1
	}
	request.ResourceType = &resourceType
	//declare reply struct
	reply := &lpop_protobuf.SearchHolidayResponse{}

	// check if can get from cache
	cacheKey := utils.GenKey(":", "tpl_holiday", lineID)
	cacheFlag := false
	if cacheVal, ok1 := lmHolidaylruCache.Get(ctx, cacheKey); ok1 {
		if cacheResult, ok2 := cacheVal.(*lpop_protobuf.SearchHolidayResponse); ok2 {
			logger.CtxLogInfof(ctx, "successfully get holiday:[%s] for lru cache key:[%s]", utils.MarshToStringWithoutError(cacheResult.HolidayInfoList), cacheKey)
			reply = cacheResult
			cacheFlag = true
		}
	}

	if !cacheFlag {
		//Invoke with micro service name, schema ID and operation ID
		if err := invoker.Invoke(ctx, l.serviceName, SlaConfigServiceSchemaID, SearchHolidayOperationID,
			request, reply, chassis.WithProtocol("grpc")); err != nil {
			errMsg := fmt.Sprintf("requesting lpop error|schema_id=%s|operation_id=%s|tpl_unique_key=%s|is_site_line=%d|error=%s", SlaConfigServiceSchemaID, SearchHolidayOperationID, lineID, isSiteLine, err.Error())
			logger.CtxLogErrorf(ctx, errMsg)
			return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
		}

		if reply.RespHeader == nil || reply.RespHeader.Retcode == nil {
			errMsg := fmt.Sprintf("requesting lpop error|schema_id=%s|operation_id=%s|tpl_unique_key=%s|is_site_line=%d|error=%s", SlaConfigServiceSchemaID, SearchHolidayOperationID, lineID, isSiteLine, "response is nil")
			logger.CtxLogErrorf(ctx, errMsg)
			return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
		}

		if *reply.RespHeader.Retcode != 0 {
			errMsg := fmt.Sprintf("requesting lpop error|schema_id=%s|operation_id=%s|tpl_unique_key=%s|is_site_line=%d|retcode=%d|error_msg=%s", SlaConfigServiceSchemaID, SearchHolidayOperationID, lineID, isSiteLine, *reply.RespHeader.Retcode, *reply.RespHeader.Message)
			logger.CtxLogErrorf(ctx, errMsg)
			return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
		}

		holidayStr := utils.MarshToStringWithoutError(reply)

		logger.CtxLogInfof(ctx, "requesting lpop success|schema_id=%s|operation_id=%s|tpl_unique_key=%s|is_site_line=%d|reply=%s", SlaConfigServiceSchemaID, SearchHolidayOperationID, lineID, isSiteLine, holidayStr)
		// set value to lru cache
		lmHolidaylruCache.Add(ctx, cacheKey, reply)
		logger.CtxLogInfof(ctx, "successfully store holiday:[%s] for cache key:[%s]", holidayStr, cacheKey)
	}

	returnHolidayList := getAllExtendedNonWorkingDays(ctx, reply, uint32(stateLocationID), region, disableHolidays, disableWeekends)

	logger.CtxLogInfof(ctx, "get holidays success|schema_id=%s|operation_id=%s|tpl_unique_key=%s|is_site_line=%d|state_location_id=%d|reply=%s", SlaConfigServiceSchemaID, SearchHolidayOperationID, lineID, isSiteLine, stateLocationID, utils.MarshToStringWithoutError(returnHolidayList))
	return returnHolidayList, nil
}

// Get3PLNonWorkingDaysWithCache 返回3PL非工作日信息，holiday和weekend分别返回
func (l *lpopService) Get3PLNonWorkingDaysWithCache(ctx utils.LCOSContext, lineId string, isSiteLine uint8, stateId uint64, disableHolidays, disableWeekends bool) ([]string, []int32, *lcos_error.LCOSError) {
	req := &lpop_protobuf.SearchHolidaysRequest{
		ReqHeader:  genLpopReqHeader(ctx),
		ResourceId: utils.NewString(lineId),
	}
	var resourceType uint32 = 2
	if isSiteLine == constant.TRUE {
		resourceType = 1
	}
	req.ResourceType = &resourceType

	resp := &lpop_protobuf.SearchHolidayResponse{}

	cacheKey := utils.GenKey(":", "tpl_holiday", lineId)
	var cacheHit bool
	if obj, ok1 := lmHolidaylruCache.Get(ctx, cacheKey); ok1 {
		if ret, ok2 := obj.(*lpop_protobuf.SearchHolidayResponse); ok2 {
			resp = ret
			cacheHit = true
		}
	}

	if !cacheHit {
		if err := invoker.Invoke(ctx, l.serviceName, SlaConfigServiceSchemaID, SearchHolidayOperationID, req, resp, chassis.WithProtocol("grpc")); err != nil {
			errMsg := fmt.Sprintf("get holiday from lpop error|line_id=%s, is_site_line=%d, state_id=%d, cause=%s", lineId, isSiteLine, stateId, err.Error())
			logger.CtxLogErrorf(ctx, errMsg)
			return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
		}

		if resp.RespHeader == nil || resp.RespHeader.Retcode == nil {
			errMsg := fmt.Sprintf("get holiday from lpop error|line_id=%s, is_site_line=%d, state_id=%d, cause=%s", lineId, isSiteLine, stateId, "response header or retcode is nil")
			logger.CtxLogErrorf(ctx, errMsg)
			return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
		}

		if resp.GetRespHeader().GetRetcode() != 0 {
			errMsg := fmt.Sprintf("get holiday from lpop error|line_id=%s, is_site_line=%d, state_id=%d, retcode=%d, message=%s", lineId, isSiteLine, stateId, resp.GetRespHeader().GetRetcode(), resp.GetRespHeader().GetMessage())
			logger.CtxLogErrorf(ctx, errMsg)
			return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
		}

		logger.CtxLogInfof(ctx, "get nonworking days from lpop success|line_id=%s, is_site_line=%d, state_id=%d, resp=%s", lineId, isSiteLine, stateId, utils.MarshToStringWithoutError(resp))
		lmHolidaylruCache.Add(ctx, cacheKey, resp)
	}

	var holidayList []string
	for _, holiday := range resp.GetHolidayInfoList() {
		if disableHolidays {
			break
		}

		if holiday.GetStateId() == stateId || holiday.GetStateId() == 0 {
			holidayList = append(holidayList, holiday.GetHoliday())
		}
	}

	var weekendList []int32
	for _, weekend := range resp.GetWeekDayInfoList() {
		if disableWeekends {
			break
		}

		if weekend.GetStateId() == stateId || weekend.GetStateId() == 0 {
			if weekend.GetFriDay() {
				weekendList = append(weekendList, 5)
			}
			if weekend.GetSatDay() {
				weekendList = append(weekendList, 6)
			}
			if weekend.GetSunDay() {
				weekendList = append(weekendList, 0) // time.Weekday return 0 as Sunday
			}
		}
	}

	logger.CtxLogInfof(ctx, "get nonworking days success|line_id=%s, is_site_line=%d, state_id=%d, disable_holidays=%t, holidays=%v, disable_weekends=%t, weekends=%v", lineId, isSiteLine, stateId, disableHolidays, holidayList, disableWeekends, weekendList)
	return holidayList, weekendList, nil
}

func (l *lpopService) GetSlaLMInboundStatusWithLruCache(ctx utils.LCOSContext) ([]string, []string, *lcos_error.LCOSError) {
	eventCode := lmInboundEvent
	request := &lpop_protobuf.SearchEventRequest{
		ReqHeader: genLpopReqHeader(ctx),
		EventCode: utils.NewString(eventCode),
	}
	//declare reply struct
	reply := &lpop_protobuf.SearchEventResponse{}
	//header will transport to target service

	cacheKey := utils.GenKey(":", "event", lmInboundEvent)
	cacheFlag := false

	if cacheVal, ok1 := lmInboundStatuslruCache.Get(ctx, cacheKey); ok1 {
		if cacheResult, ok2 := cacheVal.(*lpop_protobuf.SearchEventResponse); ok2 {
			logger.CtxLogInfof(ctx, "successfully get cached value by cache key:[%s]", cacheKey)
			reply = cacheResult
			cacheFlag = true
		}
	}

	if !cacheFlag {
		//Invoke with micro service name, schema ID and operation ID
		if err := invoker.Invoke(ctx, l.serviceName, SlaConfigServiceSchemaID, SearchLogisticsEventOperationID,
			request, reply, chassis.WithProtocol("grpc")); err != nil {
			logger.CtxLogErrorf(ctx, "error"+err.Error())
			return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
		}

		if reply.RespHeader == nil || reply.RespHeader.Retcode == nil {
			logger.CtxLogErrorf(ctx, "lpop-grpc return nil")
			return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lpop-grpc return nil")
		}

		if *reply.RespHeader.Retcode != 0 {
			logger.CtxLogErrorf(ctx, "lpop-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message)
			return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("lpop-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message))
		}

		// set value to lru cache
		lmInboundStatuslruCache.Add(ctx, cacheKey, reply)
	}
	var slsEventCodeList []string
	var lfsEventCodeList []string
	for _, event := range reply.EventList {
		if event.GetActionType() == lfsActionType {
			lfsEventCodeList = event.GetActionCodeList()
		}
		if event.GetActionType() == slsActionType {
			slsEventCodeList = event.GetActionCodeList()
		}
	}
	logger.CtxLogInfof(ctx, "requesting lpop success|schema_id=%s|api=%s|lfs_action_code_list=[%s]|sls_action_code_list=[%s]", SlaConfigServiceSchemaID, SearchLogisticsEventOperationID, strings.Join(lfsEventCodeList, ","), strings.Join(slsEventCodeList, ","))

	return slsEventCodeList, lfsEventCodeList, nil
}

func (l *lpopService) GetSupplierInfoByLineId(ctx utils.LCOSContext, lineIdList []string) (map[string]*lpop_protobuf.SupplierInfoWithLine, *lcos_error.LCOSError) {
	req := &lpop_protobuf.LineInfoRequest{
		ReqHeader: genLpopReqHeader(ctx),
	}
	ret := make(map[string]*lpop_protobuf.SupplierInfoWithLine, len(lineIdList))

	for _, lineId := range lineIdList {
		if _, ok := ret[lineId]; ok {
			continue
		}
		cacheKey := utils.GenKey(":", "supplier_info", lineId)
		obj, ok := supplierInfoCache.Get(ctx, cacheKey)
		if !ok {
			req.LineIds = append(req.LineIds, lineId)
			continue
		}
		supplierInfo, ok := obj.(*lpop_protobuf.SupplierInfoWithLine)
		if !ok {
			req.LineIds = append(req.LineIds, lineId)
			continue
		}
		ret[lineId] = supplierInfo
	}

	if len(req.LineIds) != 0 {
		resp := &lpop_protobuf.SupplierInfoWithLineIdResponse{}
		if err := invoker.Invoke(ctx, l.serviceName, SupplierInfoServiceSchemaID, GetSupplierInfoByLineIdOperationID, req, resp, chassis.WithProtocol("grpc")); err != nil {
			logger.CtxLogErrorf(ctx, "request %s/%s/%s error: %s", l.serviceName, SupplierInfoServiceSchemaID, GetSupplierInfoByLineIdOperationID, err.Error())
			return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
		}
		if resp.RespHeader == nil || resp.RespHeader.Retcode == nil {
			logger.CtxLogErrorf(ctx, "lpop return nil")
			return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lpop return nil")
		}
		if resp.GetRespHeader().GetRetcode() != 0 {
			logger.CtxLogErrorf(ctx, "lpop return error, req=%+v, retcode=%d, message=%s", req, resp.GetRespHeader().GetRetcode(), resp.GetRespHeader().GetMessage())
			return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, resp.GetRespHeader().GetMessage())
		}

		logger.CtxLogInfof(ctx, "request %s/%s/%s success|req=[%s]|resp=[%s]", l.serviceName, SupplierInfoServiceSchemaID, GetSupplierInfoByLineIdOperationID, strings.Join(req.LineIds, ","), utils.MarshToStringWithoutError(resp))

		for _, supplierInfo := range resp.GetSupplierInfo() {
			ret[supplierInfo.GetLineId()] = supplierInfo

			cacheKey := utils.GenKey(":", "supplier_info", supplierInfo.GetLineId())
			supplierInfoCache.Add(ctx, cacheKey, supplierInfo)
		}
	}

	return ret, nil
}
