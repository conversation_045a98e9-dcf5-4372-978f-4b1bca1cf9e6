package lpop_service

import (
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	lpop_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v7/logistics-partner-open-platform/go"
)

func genLpopReqHeader(ctx utils.LCOSContext) *lpop_protobuf.ReqHeader {
	currentTime := utils.GetTimestamp(ctx)
	ip := utils.GetLocalIp()
	reqId := ctx.GetRequestId()
	return &lpop_protobuf.ReqHeader{
		RequestId: &reqId,
		Account:   &account,
		Token:     &token,
		Timestamp: &currentTime,
		CallerIp:  ip,
	}
}
