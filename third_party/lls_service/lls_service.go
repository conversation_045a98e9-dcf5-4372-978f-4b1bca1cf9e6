package lls_service

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/env"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/switch_utils"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"sort"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache_with_ttl"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	http_util "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/jwt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
)

var invoker = chassis.NewRPCInvoker()
var lruCache, lineLruCache *cache.LruCache

func init() {
	lruCache, _ = cache.NewLruCache(cache.LlsPointName, &llspb.ActualPointInfo{}) //使用使用pb协议作为model值是否合适
	lineLruCache, _ = cache.NewLruCache(cache.LlsLruName, &CacheValue{})
}

func CheckLineExist(ctx utils.LCOSContext, lineId string) (bool, *lcos_error.LCOSError) {
	if len(lineId) < 4 {
		return false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("line id is valid|line_id=[%s]", lineId))
	}
	request := &llspb.GetLineInfoRequest{
		ReqHeader: genLlsReqHeader(ctx),
		LineId:    &lineId,
	}
	//declare reply struct
	reply := &llspb.GetLineInfoResponse{}
	//header will transport to target service
	microServiceName := getLLSServiceName(getRegionFromLineId(lineId))

	if _, ok := config.GetConf(ctx).LLSConfig.ServiceConf[os.Getenv("FTE_NAME")]; ok && (os.Getenv("FTE_NAME")) != "" {
		microServiceName += config.GetConf(ctx).LLSConfig.ServiceConf[os.Getenv("FTE_NAME")].ServiceName
	}
	//Invoke with micro service name, schema ID and operation ID
	if err := invoker.Invoke(ctx, microServiceName, "lls_protobuf.QueryService", "GetLineInfoByLineId",
		request, reply, chassis.WithProtocol("grpc")); err != nil {
		logger.LogErrorf("error" + err.Error())
		return false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if reply.RespHeader == nil || reply.RespHeader.Retcode == nil {
		logger.LogErrorf("lls-grpc return nil")
		return false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lls-grpc return nil")
	}

	if *reply.RespHeader.Retcode != 0 {
		logger.LogInfof("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message)
		return false, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message))
	}
	return true, nil
}

// GetLineBaseInfo Deprecated
func GetLineBaseInfo(ctx utils.LCOSContext, lineId string) (*LineBaseInfo, *lcos_error.LCOSError) {
	params := fmt.Sprintf("line_id=%s", lineId)
	if ctx.GetClientType() > 0 {
		params += fmt.Sprintf("&client_type=%d", ctx.GetClientType())
	}

	lines, err := requestLLSAdminListLine(ctx, params, getRegionFromLineId(lineId))
	if err != nil {
		return nil, err
	}

	if len(lines) == 0 {
		return nil, nil
	}

	return lines[0], nil
}

func GetLineInfoByBranchGroupIDs(ctx utils.LCOSContext, branchGroupIds []uint32) ([]*LineBaseInfo, *lcos_error.LCOSError) {
	urlValues := url.Values{}
	for _, v := range branchGroupIds {
		urlValues.Add("branch_group_ids", fmt.Sprintf("%d", v))
	}
	urlValues.Add("all_region", "true")
	urlValues.Add("no_need_serviceable_conf", "true")
	return requestLLSAdminListLine(ctx, urlValues.Encode(), ctx.GetCountry())
}

// GetAllActualPointId 获取全市场的actual point id列表
func GetAllActualPointId(ctx utils.LCOSContext) ([]string, *lcos_error.LCOSError) {
	req := &llspb.GetAllActualPointIdRequest{
		ReqHeader: genLlsReqHeader(ctx),
	}

	cid := "sg" // sg机房请求lls-grpc-sg获取全市场数据
	if env.IsUSIdc() {
		cid = strings.ToLower(env.GetCID()) // us机房单独请求br/mx/co/cl市场的服务获取对应市场数据
	}
	serviceName := "lls-grpc-" + cid
	var resp llspb.GetAllActualPointIdResponse
	if err := invoker.Invoke(ctx, serviceName, "lls_protobuf.QueryService", "GetAllActualPointId", req, &resp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, err.Error())
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "invoke %s error: %s", serviceName, err.Error())
	}
	if resp.GetRespHeader() == nil {
		logger.CtxLogErrorf(ctx, "%s return nil", serviceName)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lls-grpc return nil")
	}
	if resp.GetRespHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "%s return error: %s", serviceName, resp.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "lls-grpc return error, retcode: %d, msg: %s", resp.GetRespHeader().GetRetcode(), resp.GetRespHeader().GetMessage())
	}
	return resp.GetActualPointIdList(), nil
}

func generateActualPointsKey(actualPointId string) string {
	return utils.GenKey(":", "GetActualPoints", actualPointId)
}

func GetActualPointsMapWithCache(ctx utils.LCOSContext, actualPointIDList []string) (map[string]*llspb.ActualPointInfo, *lcos_error.LCOSError) {
	result := make(map[string]*llspb.ActualPointInfo)
	var remoteReq []string

	// get from cache
	for _, apId := range actualPointIDList {
		cacheKey := generateActualPointsKey(apId)
		cacheVal, ok := lruCache.Get(ctx, cacheKey)
		if ok {
			actualPointInfo, actualPointInfoOk := cacheVal.(*llspb.ActualPointInfo)
			if actualPointInfoOk {
				result[apId] = actualPointInfo
				continue
			}
		}
		remoteReq = append(remoteReq, apId)
	}
	if len(remoteReq) == 0 {
		logger.CtxLogInfof(ctx, "BatchGetLineInfosMap cache hit, actual_point_id:[%v]", actualPointIDList)
		return result, nil
	}

	// request remote
	tmpAp := actualPointIDList[0]
	region := getRegionFromSiteId(tmpAp)
	req := &llspb.GetActualPointRequest{
		ReqHeader:         genLlsReqHeader(ctx),
		ActualPointIdList: remoteReq,
	}
	rsp := &llspb.GetActualPointResponse{}
	microServiceName := getLlsGrpcServiceName(ctx, region)
	if err := invoker.Invoke(ctx, microServiceName, "lls_protobuf.BuildLaneService", "GetActualPoints",
		req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.LogErrorf("error" + err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetRespHeader() == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lls-grpc return nil")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 {
		logger.LogInfof("lls-grpc return error, retcode: %d, msg: %s", rsp.GetRespHeader().GetRetcode(), rsp.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("lls-grpc return error, retcode: %d, msg: %s", rsp.GetRespHeader().GetRetcode(), rsp.GetRespHeader().GetMessage()))
	}

	// set cache
	for _, actualPointInfo := range rsp.GetData().GetList() {
		cacheKey := generateActualPointsKey(actualPointInfo.GetActualPointId())
		ok, _ := lruCache.Add(ctx, cacheKey, actualPointInfo)
		if !ok {
			logger.CtxLogErrorf(ctx, "cannot set key:[%s] to lru cache", cacheKey)
		}
		result[actualPointInfo.GetActualPointId()] = actualPointInfo
	}
	return result, nil
}

func GetActualPointsMapWithLocalCache(ctx utils.LCOSContext, actualPointIDList []string) (map[string]*llspb.ActualPointInfo, *lcos_error.LCOSError) {
	actualPointInfoMap := make(map[string]*llspb.ActualPointInfo, len(actualPointIDList))

	var missingActualPointIdList []string
	executor := localcache.NewLocalCacheQueryExecutor()
	for _, actualPointId := range actualPointIDList {
		obj, err := executor.Find(ctx, constant.ActualPointInfoNamespace, actualPointId)
		if err != nil || obj == nil {
			// 异常场景：缓存key缺失。lcos加载了全市场的actual point数据，正常情况不应存在缓存key缺失
			missingActualPointIdList = append(missingActualPointIdList, actualPointId)
			continue
		}
		actualPointInfo, ok := obj.(*llspb.ActualPointInfo)
		if !ok {
			// 异常场景：缓存value不合法。不会走到这个分支
			missingActualPointIdList = append(missingActualPointIdList, actualPointId)
			continue
		}
		actualPointInfoMap[actualPointId] = actualPointInfo
	}

	if len(missingActualPointIdList) != 0 {
		// 上报并触发告警
		_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleThirdPartyLocalCache, constant.ActualPointInfoNamespace, constant.StatusMiss, strings.Join(missingActualPointIdList, ","))

		// 兜底使用LRU+remote请求获取actual point数据
		missingActualPointInfoMap, err := GetActualPointsMapWithCache(ctx, missingActualPointIdList)
		if err != nil {
			return nil, err
		}
		for actualPointId, actualPointInfo := range missingActualPointInfoMap {
			actualPointInfoMap[actualPointId] = actualPointInfo
		}
	}
	return actualPointInfoMap, nil
}

func GetActualPointsMapWithGreySwitch(ctx utils.LCOSContext, actualPointIDList []string) (map[string]*llspb.ActualPointInfo, *lcos_error.LCOSError) {
	obj, err := switch_utils.WithGreySwitch(ctx, switch_utils.BusinessKeyActualPointInfoCache,
		func(ctx utils.LCOSContext) (interface{}, *lcos_error.LCOSError) {
			return GetActualPointsMapWithCache(ctx, actualPointIDList)
		},
		func(ctx utils.LCOSContext) (interface{}, *lcos_error.LCOSError) {
			return GetActualPointsMapWithLocalCache(ctx, actualPointIDList)
		},
		func(got1, got2 interface{}, err1, err2 *lcos_error.LCOSError) bool {
			if !lcos_error.IsSameError(err1, err2) {
				return true
			}
			ret1, ret2 := got1.(map[string]*llspb.ActualPointInfo), got2.(map[string]*llspb.ActualPointInfo)
			if len(ret1) != len(ret2) {
				return true
			}
			if ret1 == nil || ret2 == nil {
				return false
			}
			for actualPointId, actualPointInfoV1 := range ret1 {
				actualPointInfoV2, ok := ret2[actualPointId]
				if !ok {
					return true
				}

				// actual point info内无slice字段，可以直接序列化判断结构体是否一致
				if utils.MarshToStringWithoutError(actualPointInfoV1) != utils.MarshToStringWithoutError(actualPointInfoV2) {
					return true
				}
			}
			return false
		},
	)
	if err != nil {
		return nil, err
	}
	return obj.(map[string]*llspb.ActualPointInfo), nil
}

func GetActualPointsMap(ctx utils.LCOSContext, actualPointIdList []string) (map[string]*llspb.ActualPointInfo, *lcos_error.LCOSError) {
	req := &llspb.GetActualPointRequest{
		ReqHeader:         genLlsReqHeader(ctx),
		ActualPointIdList: actualPointIdList,
	}

	cid := "sg" // sg机房请求lls-grpc-sg获取全市场数据
	if env.IsUSIdc() {
		cid = strings.ToLower(env.GetCID()) // us机房单独请求br/mx/co/cl市场的服务获取对应市场数据
	}
	serviceName := "lls-grpc-" + cid
	var resp llspb.GetActualPointResponse
	if err := invoker.Invoke(ctx, serviceName, "lls_protobuf.BuildLaneService", "GetActualPoints", req, &resp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "invoke %s error: %s", serviceName, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if resp.GetRespHeader() == nil {
		logger.CtxLogErrorf(ctx, "%s return nil", serviceName)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lls-grpc return nil")
	}
	if resp.GetRespHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "%s return error: %s", serviceName, resp.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "lls-grpc return error, retcode: %d, msg: %s", resp.GetRespHeader().GetRetcode(), resp.GetRespHeader().GetMessage())
	}
	actualPointsMap := make(map[string]*llspb.ActualPointInfo, len(resp.GetData().GetList()))
	for _, actualPointInfo := range resp.GetData().GetList() {
		actualPointsMap[actualPointInfo.GetActualPointId()] = actualPointInfo
	}
	return actualPointsMap, nil
}

func GetActualPointsMapWithPaging(ctx utils.LCOSContext, actualPointIdList []string, batchSize int) (map[string]*llspb.ActualPointInfo, *lcos_error.LCOSError) {
	if batchSize <= 0 {
		batchSize = 1000
	}

	actualPointsMap := make(map[string]*llspb.ActualPointInfo, len(actualPointIdList))
	start, end := 0, batchSize
	for start < len(actualPointIdList) {
		if end > len(actualPointIdList) {
			end = len(actualPointIdList)
		}

		subActualPointsMap, err := GetActualPointsMap(ctx, actualPointIdList[start:end])
		if err != nil {
			return nil, err
		}
		for actualPointId, actualPointInfo := range subActualPointsMap {
			actualPointsMap[actualPointId] = actualPointInfo
		}

		start = end
		end += batchSize
	}
	return actualPointsMap, nil
}

// Noted: only for admin service's usage. cannot offer for high QPS call, low qps LIMITED.
func GetActualPointInfos(ctx utils.LCOSContext, actualPointIdList []string) (map[string]*ActualSiteInfo, *lcos_error.LCOSError) {
	urlValues := url.Values{}
	for _, actualPoint := range actualPointIdList {
		urlValues.Add("actual_point_id_list", actualPoint)
	}
	actualPointIDInfoList, lcosErr := requestLLSAdminListActualPoint(ctx, urlValues.Encode(), ctx.GetCountry())
	if lcosErr != nil {
		return nil, lcosErr
	}
	// 转为map方便查询
	returnMap := map[string]*ActualSiteInfo{}
	for _, actualPoint := range actualPointIDInfoList {
		returnMap[actualPoint.ActualPointID] = actualPoint
	}
	return returnMap, nil
}

func GetSiteInfo(ctx utils.LCOSContext, siteID string) (*SiteBaseInfo, *lcos_error.LCOSError) {
	urlValues := url.Values{}
	urlValues.Add("site_id", siteID)
	siteInfoList, lcosErr := requestLLSAdminListSites(ctx, urlValues.Encode(), ctx.GetCountry())
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(siteInfoList) == 0 {
		return nil, nil
	}
	return siteInfoList[0], nil
}

func GetAllSiteInfo(ctx utils.LCOSContext, region string) (map[string]*SiteBaseInfo, *lcos_error.LCOSError) {
	urlValues := url.Values{}
	urlValues.Add("region", region)
	urlValues.Add("actual_point_type", "1")
	siteInfoList, lcosErr := requestLLSAdminListSites(ctx, urlValues.Encode(), ctx.GetCountry())
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(siteInfoList) == 0 {
		return nil, nil
	}
	returnInfoMap := map[string]*SiteBaseInfo{}
	for _, siteInfo := range siteInfoList {
		returnInfoMap[siteInfo.SiteId] = siteInfo
	}
	return returnInfoMap, nil
}

func requestLLSAdminListLine(ctx utils.LCOSContext, params, region string) ([]*LineBaseInfo, *lcos_error.LCOSError) {
	resp, err := sendLLSJwt(ctx, nil, params, "/admin/logistic_line/list/", region, "GET")
	if err != nil {
		return nil, err
	}

	rsp := &ListLineResp{}
	if err := json.Unmarshal(resp, rsp); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, err.Error())
	}

	if rsp.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(rsp.Retcode, "Call lls failed:"+rsp.Message)
	}

	return rsp.Data.List, nil
}

func requestLLSAdminListSites(ctx utils.LCOSContext, params, region string) ([]*SiteBaseInfo, *lcos_error.LCOSError) {

	resp, err := sendLLSJwt(ctx, nil, params, "/admin/logistic_site/all_list/", region, "GET")
	if err != nil {
		return nil, err
	}

	rsp := &ListSiteResp{}
	if err := json.Unmarshal(resp, rsp); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, err.Error())
	}

	if rsp.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(rsp.Retcode, "Call lls failed:"+rsp.Message)
	}

	return rsp.Data.List, nil
}

func requestLLSAdminListAllSites(ctx utils.LCOSContext, params, region string) ([]*SiteBaseInfo, *lcos_error.LCOSError) {

	resp, err := sendLLSJwt(ctx, nil, params, "/admin/logistic_site/all_list/", region, "GET")
	if err != nil {
		return nil, err
	}

	rsp := &ListSiteResp{}
	if err := json.Unmarshal(resp, rsp); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, err.Error())
	}

	if rsp.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(rsp.Retcode, "Call lls failed:"+rsp.Message)
	}

	return rsp.Data.List, nil
}

func requestLLSAdminListActualPoint(ctx utils.LCOSContext, params, region string) ([]*ActualSiteInfo, *lcos_error.LCOSError) {

	resp, err := sendLLSJwt(ctx, nil, params, "/admin/logistic_actual_point/list/", region, "GET")
	if err != nil {
		return nil, err
	}

	rsp := &ListActualPointResp{}
	if err := json.Unmarshal(resp, rsp); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, err.Error())
	}

	if rsp.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(rsp.Retcode, "Call lls failed:"+rsp.Message)
	}

	return rsp.Data, nil
}

func GetLineIdsByClientType(ctx utils.LCOSContext, clientType uint32) ([]string, *lcos_error.LCOSError) {
	params := fmt.Sprintf("client_type=%d", clientType)
	resp, err := sendLLSJwt(ctx, nil, params, "/admin/logistic_line/search_line_ids/", ctx.GetCountry(), "GET")
	if err != nil {
		return nil, err
	}

	rs := SearchLineIdsResp{}
	if err := json.Unmarshal(resp, &rs); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, err.Error())
	}

	if rs.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(rs.Retcode, "call lls failed:"+rs.Message)
	}

	return rs.Data.LineIds, nil
}

func GetSiteIdsByClientType(ctx utils.LCOSContext, clientType uint32) ([]string, *lcos_error.LCOSError) {
	params := fmt.Sprintf("client_type=%d", clientType)
	resp, err := sendLLSJwt(ctx, nil, params, "/admin/logistic_line/search_site_ids", ctx.GetCountry(), "GET")
	if err != nil {
		return nil, err
	}

	rs := SearchSiteIdsResp{}
	if err := json.Unmarshal(resp, &rs); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.JsonDecodeErrorCode, err.Error())
	}

	if rs.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSError(rs.Retcode, "call lls failed:"+rs.Message)
	}

	return rs.Data.SiteIds, nil
}

func sendLLSJwt(ctx utils.LCOSContext, data interface{}, params, api, cid, method string) ([]byte, *lcos_error.LCOSError) {
	if cid == "XX" {
		cid = "SG"
	}
	env := utils.GetEnv(ctx)
	url := hosts[env][cid] + api

	c, ok := ctx.(*utils.HttpContext)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, "invalid context")
	}
	jwtInfo := c.GetJwtInfo()
	newJwtInfo := map[string]interface{}{}
	// put data to context
	for k, v := range jwtInfo {
		newJwtInfo[k] = v
	}
	if entity, ok := newJwtInfo["entity"].(map[string]interface{}); ok {
		entity["country"] = "XX"
	}

	payload := map[string]interface{}{
		"data":      data,
		"timestamp": recorder.Now(ctx).Unix(),
		"info":      newJwtInfo,
	}
	headers := map[string]interface{}{
		"typ":  "JWT",
		"alg":  "HS256",
		"optr": operatorAccount,
	}
	enc, err := jwt.GetJwtDataV2(cf.GetThirdServiceSecretConfig(ctx).Lls, headers, payload)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	var req *http.Request
	if method == "POST" {
		d, _ := json.Marshal(data)
		req, err = http.NewRequest(http.MethodPost, url, strings.NewReader(string(d)))
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, err.Error())
		}
		req.Header.Add("Content-Type", "application/json")
	} else {
		if len(params) > 0 {
			url = url + "?" + params
		}
		req, err = http.NewRequest(http.MethodGet, url, nil)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, err.Error())
		}
	}
	req.Header.Add("request-id", ctx.GetRequestId())
	req.Header.Add("X-Request-Id", ctx.GetRequestId())
	req.Header.Add("jwt-token", enc)

	//SPLN-27215 请求超时时间也可以配置在invoker的ops中，这里由于invoker独立初始化,且主要用于ops功能，超时目前一致，因此暂时先集中设置
	httpCtx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()
	resp, err := http_util.HttpRequest(httpCtx, httpClient, req)
	if err != nil {
		logger.LogErrorf("send http jwt request failed,err[%v]", err)
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, err.Error())
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	logger.LogInfof("send jwt http request,method[%s],url[%s],req[%v],resp[%s]", method, url, data, body)
	if err != nil {
		logger.LogErrorf("send http jwt request failed")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	return body, nil
}

// genLLSJwtToken 从sendLLSJwt剥离出的admin端LLS JWT生成逻辑。支持指定jwt country字段，sendLLSJwt里被改成了hardcode为XX，原因不明
func genLLSJwtToken(ctx utils.LCOSContext, data interface{}, cid string) (string, *lcos_error.LCOSError) {
	payload := map[string]interface{}{
		"data":      data,
		"timestamp": recorder.Now(ctx).Unix(),
		"info": map[string]interface{}{
			"entity": map[string]interface{}{
				"name":    cid,
				"country": cid,
			},
			"user": map[string]interface{}{
				"name":  "LCOS",
				"email": "LCOS",
			},
		},
	}
	headers := map[string]interface{}{
		"typ":  "JWT",
		"alg":  "HS256",
		"optr": operatorAccount,
	}
	jwtToken, err := jwt.GetJwtDataV2(cf.GetThirdServiceSecretConfig(ctx).Lls, headers, payload)
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return jwtToken, nil
}

func genLLSJwtHeader(ctx utils.LCOSContext, data interface{}, cid string) (map[string]string, *lcos_error.LCOSError) {
	jwtToken, err := genLLSJwtToken(ctx, data, cid)
	if err != nil {
		return nil, err
	}
	return map[string]string{
		"jwt-token": jwtToken,
	}, nil
}

func getLLSAdminHost(ctx utils.LCOSContext, cid string) string {
	if cid == "XX" {
		cid = "SG"
	}
	env := utils.GetEnv(ctx)
	return hosts[env][cid]
}

/*
批量获取line信息，返回以lineId为key，lineInfo为value的字典
*/
func BatchGetLineInfosMap(ctx utils.LCOSContext, lineIds []string) (map[string]*llspb.GetLineInfoResponseData, *lcos_error.LCOSError) {
	rsp := make(map[string]*llspb.GetLineInfoResponseData)
	remoteReq := []string{}

	missing := false
	// get from cache
	for _, lineId := range lineIds {
		cacheKey := utils.GenKey(":", "BatchGetLineMapByLineIds", lineId)
		cacheVal, ok := lineLruCache.Get(ctx, cacheKey)
		if ok {
			ruleResults, _ := cacheVal.(*CacheValue)
			rsp[lineId] = ruleResults.Value
			if ruleResults.Expired(ctx) {
				remoteReq = append(remoteReq, lineId)
			}
			continue
		}
		remoteReq = append(remoteReq, lineId)
		missing = true
	}

	if len(remoteReq) == 0 {
		logger.LogInfof("cache hit for all lineIds: %v", lineIds)
		return rsp, nil
	}

	logger.LogInfof("try to get from lls, some cache miss or expired for lineIDs: %v", remoteReq)

	//get from remote
	remoteResp, lcosErr := batchGetLineInfoRemote(ctx, remoteReq)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "lls api failed for get line info, err: %v", lcosErr)
		if missing {
			return nil, lcosErr
		} else {
			logger.CtxLogInfof(ctx, "reuse the expired cache for line_ids: %v", remoteReq)
			remoteResp = rsp
		}
	}

	// set cache
	for lineId, lineInfo := range remoteResp {
		cacheKey := utils.GenKey(":", "BatchGetLineMapByLineIds", lineId)
		cacheValue := &CacheValue{
			Timestamp: recorder.Now(ctx).Add(DEFAULT_LOGIC_CACHE_TIMEOUT).Unix(),
			Value:     lineInfo,
		}
		ok, _ := lineLruCache.Add(ctx, cacheKey, cacheValue)
		if !ok {
			logger.CtxLogErrorf(ctx, "cannot set key:[%s] to lru cache", cacheKey)
		}
		rsp[lineId] = lineInfo
	}

	return rsp, nil
}

func batchGetLineInfoRemote(ctx utils.LCOSContext, lineIds []string) (map[string]*llspb.GetLineInfoResponseData, *lcos_error.LCOSError) {
	logger.LogInfo("lls timeout:%v", getLLSServiceTimeOut(ctx))
	timeoutCtx, cancel := context.WithTimeout(ctx, getLLSServiceTimeOut(ctx))
	defer cancel()

	request := &llspb.BatchGetLineMapRequest{
		ReqHeader: genLlsReqHeader(ctx),
		LineIds:   lineIds,
	}
	reply := &llspb.BatchGetLineMapResponse{}
	microServiceName := getLLSServiceName("sg") // 通过sg的服务获取所有的line信息，br idc需要请求br的服务
	//Invoke with micro service name, schema ID and operation ID
	if err := invoker.Invoke(timeoutCtx, microServiceName, "lls_protobuf.QueryService", "BatchGetLineMapByLineIds",
		request, reply, chassis.WithProtocol("grpc")); err != nil {
		logger.LogErrorf("error" + err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if reply.RespHeader == nil || reply.RespHeader.Retcode == nil {
		logger.LogErrorf("lls-grpc return nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lls-grpc return nil")
	}

	if *reply.RespHeader.Retcode != 0 {
		logger.LogInfof("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message))
	}
	return reply.Data, nil
}

/*
批量获取lineDraft信息，返回以lineId为key，lineInfo为value的字典
*/
func BatchGetLineDraftsMap(ctx utils.LCOSContext, lineIds []string) (map[string]*llspb.GetLineInfoResponseData, *lcos_error.LCOSError) {
	request := &llspb.BatchGetLineMapRequest{
		ReqHeader: genLlsReqHeader(ctx),
		LineIds:   lineIds,
	}
	reply := &llspb.BatchGetLineMapResponse{}
	microServiceName := getLLSServiceName("sg") // 通过sg的服务获取所有的line信息，br idc需要请求br的服务
	//Invoke with micro service name, schema ID and operation ID
	if err := invoker.Invoke(ctx, microServiceName, "lls_protobuf.QueryService", "BatchGetLineDraftMapByLineIds",
		request, reply, chassis.WithProtocol("grpc")); err != nil {
		logger.LogErrorf("error" + err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if reply.RespHeader == nil || reply.RespHeader.Retcode == nil {
		logger.LogErrorf("lls-grpc return nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lls-grpc return nil")
	}

	if *reply.RespHeader.Retcode != 0 {
		logger.LogInfof("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message))
	}
	return reply.Data, nil
}

/*
通过line id获取单个line
*/
func GetLineInfosByLineId(ctx utils.LCOSContext, lineId string) (*llspb.GetLineInfoResponseData, *lcos_error.LCOSError) {
	// lineId必须要4位以上，否则报错
	if len(lineId) < 4 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("lineId is not valid|line_id=%s", lineId))
	}
	request := &llspb.GetLineInfoRequest{
		ReqHeader: genLlsReqHeader(ctx),
		LineId:    &lineId,
	}
	reply := &llspb.GetLineInfoResponse{}
	microServiceName := getLLSServiceName(getRegionFromLineId(lineId))
	//Invoke with micro service name, schema ID and operation ID
	if err := invoker.Invoke(ctx, microServiceName, "lls_protobuf.QueryService", "GetLineInfoByLineId",
		request, reply, chassis.WithProtocol("grpc")); err != nil {
		logger.LogErrorf("error" + err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if reply.RespHeader == nil || reply.RespHeader.Retcode == nil {
		logger.LogErrorf("lls-grpc return nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lls-grpc return nil")
	}

	if *reply.RespHeader.Retcode != 0 {
		logger.LogInfof("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message))
	}
	return reply.GetLineInfoResponseData, nil
}

/*
通过过滤条件获取相关的line信息
*/
func BatchGetLineInfosByParams(ctx utils.LCOSContext, params map[string]interface{}) ([]*llspb.GetLineInfoResponseData, *lcos_error.LCOSError) {
	request := &llspb.GetAllLinesByParamsRequest{
		ReqHeader: genLlsReqHeader(ctx),
	}
	if lineId, ok := params["line_id"]; ok {
		lineId := lineId.(string)
		request.LineId = &lineId
	}
	if lineSubType, ok := params["line_sub_type"]; ok {
		lineSubType := lineSubType.(int32)
		request.LineSubType = &lineSubType
	}
	if destinationRegion, ok := params["destination_region"]; ok {
		destinationRegion := destinationRegion.(string)
		request.DestinationRegion = &destinationRegion
	}

	if originRegion, ok := params["origin_region"]; ok {
		originRegion := originRegion.(string)
		request.OriginRegion = &originRegion
	}

	if supplierName, ok := params["supplier_name"]; ok {
		supplierName := supplierName.(string)
		request.SupplierName = &supplierName
	}

	if companyName, ok := params["company_name"]; ok {
		companyName := companyName.(string)
		request.CompanyName = &companyName
	}

	if clientType, ok := params["client_type"]; ok {
		clientType := clientType.(uint32)
		request.ClientType = &clientType
	}

	reply := &llspb.GetAllLinesByParamsResponse{}
	microServiceName := getLLSServiceName("sg")
	//Invoke with micro service name, schema ID and operation ID
	if err := invoker.Invoke(ctx, microServiceName, "lls_protobuf.QueryService", "GetAllLinesByParams",
		request, reply, chassis.WithProtocol("grpc")); err != nil {
		logger.LogErrorf("error" + err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if reply.RespHeader == nil || reply.RespHeader.Retcode == nil {
		logger.LogErrorf("lls-grpc return nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lls-grpc return nil")
	}

	if *reply.RespHeader.Retcode != 0 {
		logger.LogInfof("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message))
	}
	// 根据mtime，对lineId进行从大到小排序
	sort.Slice(reply.AllLineDataList, func(i, j int) bool {
		if *reply.AllLineDataList[i].Mtime > *reply.AllLineDataList[j].Mtime {
			return true
		}
		return false
	})
	return reply.AllLineDataList, nil
}

/*
通过过滤条件获取相关的line信息
*/
func BatchGetLineDraftsByParams(ctx utils.LCOSContext, params map[string]interface{}) ([]*llspb.GetLineInfoResponseData, *lcos_error.LCOSError) {
	request := &llspb.GetAllLinesByParamsRequest{
		ReqHeader: genLlsReqHeader(ctx),
	}
	if lineId, ok := params["line_id"]; ok {
		lineId := lineId.(string)
		request.LineId = &lineId
	}
	if lineSubType, ok := params["line_sub_type"]; ok {
		lineSubType := lineSubType.(int32)
		request.LineSubType = &lineSubType
	}
	if destinationRegion, ok := params["destination_region"]; ok {
		destinationRegion := destinationRegion.(string)
		request.DestinationRegion = &destinationRegion
	}

	if originRegion, ok := params["origin_region"]; ok {
		originRegion := originRegion.(string)
		request.OriginRegion = &originRegion
	}

	if supplierName, ok := params["supplier_name"]; ok {
		supplierName := supplierName.(string)
		request.SupplierName = &supplierName
	}

	if companyName, ok := params["company_name"]; ok {
		companyName := companyName.(string)
		request.CompanyName = &companyName
	}

	if clientType, ok := params["client_type"]; ok {
		clientType := clientType.(uint32)
		request.ClientType = &clientType
	}

	reply := &llspb.GetAllLinesByParamsResponse{}
	microServiceName := getLLSServiceName("sg")
	//Invoke with micro service name, schema ID and operation ID
	if err := invoker.Invoke(ctx, microServiceName, "lls_protobuf.QueryService", "GetAllLineDraftsByParams",
		request, reply, chassis.WithProtocol("grpc")); err != nil {
		logger.LogErrorf("error" + err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	if reply.RespHeader == nil || reply.RespHeader.Retcode == nil {
		logger.LogErrorf("lls-grpc return nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "lls-grpc return nil")
	}

	if *reply.RespHeader.Retcode != 0 {
		logger.LogInfof("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("lls-grpc return error, retcode: %d, msg: %s", *reply.RespHeader.Retcode, *reply.RespHeader.Message))
	}
	// 根据mtime，对lineId进行从大到小排序
	sort.Slice(reply.AllLineDataList, func(i, j int) bool {
		if *reply.AllLineDataList[i].Mtime > *reply.AllLineDataList[j].Mtime {
			return true
		}
		return false
	})
	return reply.AllLineDataList, nil
}

func genLlsReqHeader(ctx utils.LCOSContext) *llspb.ReqHeader {
	currentTime := utils.GetTimestamp(ctx)
	ip := utils.GetLocalIp()
	reqId := ctx.GetRequestId()
	return &llspb.ReqHeader{
		RequestId: &reqId,
		Account:   &account,
		Token:     &token,
		Timestamp: &currentTime,
		CallerIp:  ip,
	}
}

func getRegionFromLineId(lineId string) string {
	region := utils.GetRegionFromResourceId(lineId)
	if region == "CN" || region == "KR" {
		region = "SG"
	}
	return region
}

func getRegionFromSiteId(siteId string) string {
	region := utils.GetRegionFromResourceId(siteId)
	if region == "CN" || region == "KR" {
		region = "SG"
	}
	return region
}

func getLlsGrpcServiceName(ctx context.Context, region string) string {
	microServiceName := getLLSServiceName(region)
	if _, ok := config.GetConf(ctx).LLSConfig.ServiceConf[os.Getenv("FTE_NAME")]; ok && (os.Getenv("FTE_NAME")) != "" {
		microServiceName += config.GetConf(ctx).LLSConfig.ServiceConf[os.Getenv("FTE_NAME")].ServiceName
	}
	return microServiceName
}

func getLLSServiceTimeOut(ctx context.Context) time.Duration {
	timeout := config.GetMutableConf(ctx).LLSConfig.ServiceTimeout
	if timeout > 0 {
		return time.Duration(timeout) * time.Millisecond
	}
	return DEFAULT_LLS_SERVICE_TIMEOUT
}

func getLLSServiceName(region string) string {
	if env.IsBrIdc("") || config.NeedLatamConfig("") {
		region = "br"
	}
	if len(region) == 0 {
		region = "sg"
	}
	return "lls-grpc-" + strings.ToLower(region)
}

// BatchCheckLineExists 检查线是否存在。可以通过Option指定是否检查草稿态和安装线，在结果中仅返回存在的线
func BatchCheckLineExists(ctx utils.LCOSContext, region string, lineList []string, opts ...LineInfoQueryOption) (map[string]bool, *lcos_error.LCOSError) {
	var (
		existsMap           = make(map[string]bool, len(lineList))            // 合法的线ID
		query               = new(LineInfoQuery)                              // 线查询选项
		lineInfosMap        = make(map[string]*llspb.GetLineInfoResponseData) // 生效履约线
		lineDraftsMap       = make(map[string]*llspb.GetLineInfoResponseData) // 草稿态的履约线
		installLineInfosMap = make(map[string]*InstallationLineBaseInfo)      // 安装线
		err                 *lcos_error.LCOSError
	)
	for _, opt := range opts {
		opt(query)
	}

	// 1. 请求LLS获取线信息
	// 1.1 获取已经生效的履约线信息
	lineInfosMap, err = BatchGetLineInfosMap(ctx, lineList)
	if err != nil {
		return nil, err
	}
	// 1.2 获取已经草稿态履约线信息
	if query.IncludeDraftLine {
		lineDraftsMap, err = BatchGetLineDraftsMap(ctx, lineList)
		if err != nil {
			return nil, err
		}
	}
	// 1.3 获取安装线信息
	if query.IncludeInstallLine {
		installLineInfosMap, err = BatchGetAllInstallLineInfosMap(ctx, region)
		if err != nil {
			return nil, err
		}
	}

	// 2. 检查请求的线在LLS是否存在
	for _, lineId := range lineList {
		if _, ok := lineInfosMap[lineId]; ok {
			existsMap[lineId] = true
		}
		if _, ok := lineDraftsMap[lineId]; ok {
			existsMap[lineId] = true
		}
		if _, ok := installLineInfosMap[lineId]; ok {
			existsMap[lineId] = true
		}
	}
	return existsMap, nil
}

func CheckLineAndInstallationLineExist(ctx utils.LCOSContext, lineId string) (bool, *lcos_error.LCOSError) {
	return CheckAllLineExist(ctx, lineId)
}

func CheckAllLineExist(ctx utils.LCOSContext, lineId string) (bool, *lcos_error.LCOSError) {
	// 1. 检查履约线是否存在
	lineInfo, e := GetLineBaseInfo(ctx, lineId)
	if e != nil {
		return false, e
	}
	if lineInfo != nil {
		return true, nil
	}
	// 2. 检查安装线是否存在
	installLineInfo, err := GetInstallationLineBaseInfo(ctx, lineId)
	if err != nil {
		return false, err
	}
	if installLineInfo != nil {
		return true, nil
	}
	return false, nil
}

func BatchGetAllInstallLineInfosMap(ctx utils.LCOSContext, region string) (map[string]*InstallationLineBaseInfo, *lcos_error.LCOSError) {
	var (
		host   = getLLSAdminHost(ctx, region)
		resp   ListAllInstallationLineResp
		retMap map[string]*InstallationLineBaseInfo
	)

	header, err := genLLSJwtHeader(ctx, nil, region)
	if err != nil {
		return nil, err
	}
	if err = http_util.SendGetHttpRequest(ctx, chassis.NewRestInvoker(), host+"/admin/installation_line/all_list/", nil, &resp, header); err != nil {
		return nil, err
	}
	if resp.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSErrorf(resp.Retcode, "invoke lls error: %s", resp.Message)
	}

	retMap = make(map[string]*InstallationLineBaseInfo, len(resp.Data.List))
	for _, lineInfo := range resp.Data.List {
		retMap[lineInfo.InstallationLineID] = lineInfo
	}
	return retMap, nil
}

func GetInstallationLineBaseInfo(ctx utils.LCOSContext, lineId string) (*InstallationLineBaseInfo, *lcos_error.LCOSError) {
	var (
		region = strings.ToUpper(getRegionFromLineId(lineId))
		host   = getLLSAdminHost(ctx, region)
		req    = &GetInstallationLineReq{
			InstallationLineId: lineId,
		}
		resp GetInstallationLineResp
	)

	header, err := genLLSJwtHeader(ctx, req, region)
	if err != nil {
		return nil, err
	}
	if err = http_util.SendPostHttpRequest(ctx, chassis.NewRestInvoker(), defaultTimeout, host+"/admin/installation_line/list/", req, &resp, header); err != nil {
		return nil, err
	}
	if resp.Retcode != lcos_error.SuccessCode {
		return nil, lcos_error.NewLCOSErrorf(resp.Retcode, "invoke lls error: %s", resp.Message)
	}
	if len(resp.Data.List) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.LineNotValidErrorCode, "install line not found")
	}
	return resp.Data.List[0], nil
}
