package lls_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"os"
	"path"
	"testing"
)

func TestMain(m *testing.M) {
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	_ = os.Setenv("CID", "br")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("MODULE_NAME", "grpc")
	_ = os.Setenv("IDC", "sg5")

	config.Conf = &config.Config{
		LLSConfig: config.LLSConfig{
			ServiceConf:    make(map[string]config.ServiceConfig),
			ServiceTimeout: 20,
		},
	}
	config.MutableConf = &config.MutableConfig{
		LLSConfig: config.LLSConfig{
			ServiceConf:    make(map[string]config.ServiceConfig),
			ServiceTimeout: 20,
		},
	}

	if err := chassis.Init(); err != nil {
		fmt.Printf("init chassis error: %s\n", err.Error())
		return
	}

	m.Run()
}

func TestGetLineInfo(t *testing.T) {
	if _, err := CheckLineExist(utils.NewCommonCtx(context.Background()), "LVN111"); err != nil {
		t.Logf("error msg: %s", err.Msg)
	}
	fmt.Println("success")
}

func TestBatchGetLineInfosMap(t *testing.T) {
	if _, err := BatchGetLineInfosMap(utils.NewCommonCtx(context.Background()), []string{"LVN111"}); err != nil {
		t.Logf("error msg: %s", err.Msg)
	}
	fmt.Println("success")
}
