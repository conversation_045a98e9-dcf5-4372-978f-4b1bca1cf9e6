package lls_service

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"time"
)

var (
	account = "lcos"
	// TODO: token暂时随机定义
	token = "test"

	hosts = map[string]map[string]string{
		"FTE": {
			"SG": "https://lls-api.test.shopee.sg",
			"TW": "https://lls-api.test.shopee.tw",
			"TH": "https://lls-api.test.shopee.co.th",
			"PH": "https://lls-api.test.shopee.ph",
			"VN": "https://lls-api.test.shopee.vn",
			"MY": "https://lls-api.test.shopee.com.my",
			"ID": "https://lls-api.test.shopee.co.id",
			"BR": "https://lls-api.test.shopee.com.br",
			"MX": "https://lls-api.test.shopee.com.mx",
			"CO": "https://lls-api.test.shopee.com.co",
			"CL": "https://lls-api.test.shopee.cl",
			"AR": "https://lls-api.test.shopee.com.ar",
			"PL": "https://lls-api.test.shopee.pl",
			"ES": "https://lls-api.test.shopee.es",
			"FR": "https://lls-api.test.shopee.fr",
			"IN": "https://lls-api.test.shopee.in",
		},
		"TEST": {
			"SG": "https://lls-api.test.shopee.sg",
			"TW": "https://lls-api.test.shopee.tw",
			"TH": "https://lls-api.test.shopee.co.th",
			"PH": "https://lls-api.test.shopee.ph",
			"VN": "https://lls-api.test.shopee.vn",
			"MY": "https://lls-api.test.shopee.com.my",
			"ID": "https://lls-api.test.shopee.co.id",
			"BR": "https://lls-api.test.shopee.com.br",
			"MX": "https://lls-api.test.shopee.com.mx",
			"CO": "https://lls-api.test.shopee.com.co",
			"CL": "https://lls-api.test.shopee.cl",
			"AR": "https://lls-api.test.shopee.com.ar",
			"PL": "https://lls-api.test.shopee.pl",
			"ES": "https://lls-api.test.shopee.es",
			"FR": "https://lls-api.test.shopee.fr",
			"IN": "https://lls-api.test.shopee.in",
		},
		"UAT": {
			"SG": "https://lls-api.uat.shopee.sg",
			"TW": "https://lls-api.uat.shopee.tw",
			"TH": "https://lls-api.uat.shopee.co.th",
			"PH": "https://lls-api.uat.shopee.ph",
			"VN": "https://lls-api.uat.shopee.vn",
			"MY": "https://lls-api.uat.shopee.com.my",
			"ID": "https://lls-api.uat.shopee.co.id",
			"BR": "https://lls-api.uat.shopee.com.br",
			"MX": "https://lls-api.uat.shopee.com.mx",
			"CO": "https://lls-api.uat.shopee.com.co",
			"CL": "https://lls-api.uat.shopee.cl",
			"AR": "https://lls-api.uat.shopee.com.ar",
			"PL": "https://lls-api.uat.shopee.pl",
			"ES": "https://lls-api.uat.shopee.es",
			"FR": "https://lls-api.uat.shopee.fr",
			"IN": "https://lls-api.uat.shopee.in",
		},
		"STABLE": {
			"SG": "https://lls-api.test-stable.shopee.sg",
			"TW": "https://lls-api.test-stable.shopee.tw",
			"TH": "https://lls-api.test-stable.shopee.co.th",
			"PH": "https://lls-api.test-stable.shopee.ph",
			"VN": "https://lls-api.test-stable.shopee.vn",
			"MY": "https://lls-api.test-stable.shopee.com.my",
			"ID": "https://lls-api.test-stable.shopee.co.id",
			"BR": "https://lls-api.test-stable.shopee.com.br",
			"MX": "https://lls-api.test-stable.shopee.com.mx",
			"CO": "https://lls-api.test-stable.shopee.com.co",
			"CL": "https://lls-api.test-stable.shopee.cl",
			"AR": "https://lls-api.test-stable.shopee.com.ar",
			"PL": "https://lls-api.test-stable.shopee.pl",
			"ES": "https://lls-api.test-stable.shopee.es",
			"FR": "https://lls-api.test-stable.shopee.fr",
			"IN": "https://lls-api.test-stable.shopee.in",
		},
		"STAGING": {
			"SG": "https://lls-api.staging.shopee.sg",
			"TW": "https://lls-api.staging.shopee.tw",
			"TH": "https://lls-api.staging.shopee.co.th",
			"PH": "https://lls-api.staging.shopee.ph",
			"VN": "https://lls-api.staging.shopee.vn",
			"MY": "https://lls-api.staging.shopee.com.my",
			"ID": "https://lls-api.staging.shopee.co.id",
			"BR": "https://lls-api.staging.shopee.com.br",
			"MX": "https://lls-api.staging.shopee.com.mx",
			"CO": "https://lls-api.staging.shopee.com.co",
			"CL": "https://lls-api.staging.shopee.cl",
			"AR": "https://lls-api.staging.shopee.com.ar",
			"PL": "https://lls-api.staging.shopee.pl",
			"ES": "https://lls-api.staging.shopee.es",
			"FR": "https://lls-api.staging.shopee.fr",
			"IN": "https://lls-api.staging.shopee.in",
		},
		"LIVE": {
			"SG": "https://lls-api.shopee.sg",
			"TW": "https://lls-api.shopee.tw",
			"TH": "https://lls-api.shopee.co.th",
			"PH": "https://lls-api.shopee.ph",
			"VN": "https://lls-api.shopee.vn",
			"MY": "https://lls-api.shopee.com.my",
			"ID": "https://lls-api.shopee.co.id",
			"BR": "https://lls-api.shopee.com.br",
			"MX": "https://lls-api.shopee.com.mx",
			"CO": "https://lls-api.shopee.com.co",
			"CL": "https://lls-api.shopee.cl",
			"AR": "https://lls-api.shopee.com.ar",
			"PL": "https://lls-api.shopee.pl",
			"ES": "https://lls-api.shopee.es",
			"FR": "https://lls-api.shopee.fr",
			"IN": "https://lls-api.shopee.in",
		},
	}
	httpClient      *chassis.RestInvoker
	defaultTimeout  = time.Second * 5
	operatorAccount = "operator"
)

const (
	DEFAULT_LOGIC_CACHE_TIMEOUT = 5 * 60 * time.Second
	DEFAULT_LLS_SERVICE_TIMEOUT = 1 * time.Second
)

func init() {
	httpClient = chassis.NewRestInvoker()
}

type (
	LineInfoQuery struct {
		IncludeDraftLine   bool
		IncludeInstallLine bool
	}

	LineInfoQueryOption func(query *LineInfoQuery)
)

func WithDraftLine() LineInfoQueryOption {
	return func(query *LineInfoQuery) {
		query.IncludeDraftLine = true
	}
}

func WithInstallLine() LineInfoQueryOption {
	return func(query *LineInfoQuery) {
		query.IncludeInstallLine = true
	}
}
