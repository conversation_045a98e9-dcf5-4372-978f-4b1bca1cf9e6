package lls_service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
)

type SearchLineIdsResp struct {
	Retcode int32  `json:"retcode"`
	Message string `json:"message"`
	Data    struct {
		LineIds []string `json:"line_ids"`
	}
}

type SearchSiteIdsResp struct {
	Retcode int32  `json:"retcode"`
	Message string `json:"message"`
	Data    struct {
		SiteIds []string `json:"site_ids"`
	}
}

type ListLineResp struct {
	Retcode int32  `json:"retcode"`
	Message string `json:"message"`
	Data    struct {
		List []*LineBaseInfo `json:"list"`
	}
}

type ListSiteResp struct {
	Retcode int32  `json:"retcode"`
	Message string `json:"message"`
	Data    struct {
		List []*SiteBaseInfo `json:"list"`
	}
}

type LineBaseInfo struct {
	LineId        string `json:"line_id"`
	ClientType    uint32 `json:"client_type"`
	BranchGroupID uint32 `json:"branch_group_id"`
}

type SiteBaseInfo struct {
	SiteId   string `json:"site_id"`
	SiteName string `json:"site_name"`
}

type ListActualPointResp struct {
	Retcode int32             `json:"retcode"`
	Message string            `json:"message"`
	Data    []*ActualSiteInfo `json:"data"`
}

type ActualSiteInfo struct {
	ActualPointID   string `json:"actual_point_id"`
	ActualPointName string `json:"actual_point_name"`
	SiteID          string `json:"site_id"`
	SiteName        string `json:"site_name"`
}

type CacheValue struct {
	Timestamp int64
	Value     *llspb.GetLineInfoResponseData
}

type ListAllInstallationLineResp struct {
	Retcode int32                   `json:"retcode"`
	Message string                  `json:"message"`
	Data    GetInstallationLineData `json:"data"`
}

type GetInstallationLineReq struct {
	InstallationLineId string  `json:"installation_line_id"` // 非必填
	MainType           *uint8  `json:"main_type"`            // 非必填，1-Local
	SubType            *uint32 `json:"sub_type"`             // 非必填，1-INST-ISP, 2-INST-LM
	DestinationRegion  string  `json:"destination_region"`   // 非必填
	PageNo             uint32  `json:"page_no"`
	Count              uint32  `json:"count"`
}

type GetInstallationLineResp struct {
	Retcode int32                   `json:"retcode"`
	Message string                  `json:"message"`
	Data    GetInstallationLineData `json:"data"`
}

func (c *CacheValue) Expired(ctx context.Context) bool {
	return c.Timestamp < recorder.Now(ctx).Unix()
}

type InstallationLineBaseInfo struct {
	InstallationLineID                               string `json:"installation_line_id"`                                   // Installation line ID
	InstallationLineName                             string `json:"installation_line_name"`                                 // Installation line name
	MainType                                         int    `json:"main_type"`                                              // Main type
	SubType                                          int    `json:"sub_type"`                                               // Sub type
	ServiceType                                      int    `json:"service_type"`                                           // Service type
	LineInterfaceSysID                               int    `json:"line_interface_sys_id"`                                  // Line interface system ID
	AllowBuyerScheduleInstallationTime               int    `json:"allow_buyer_schedule_installation_time"`                 // Allow buyer to schedule installation time
	AllowBuyerChangeInstallationTime                 int    `json:"allow_buyer_change_installation_time"`                   // Allow buyer to change installation time
	AllowBuyerChangeInstallationTimeCutoffLimitation string `json:"allow_buyer_change_installation_time_cutoff_limitation"` // Allow buyer to change installation time cutoff limitation
}

type GetInstallationLineData struct {
	List   []*InstallationLineBaseInfo `json:"list"`
	PageNo int                         `json:"pageno"`
	Count  int                         `json:"count"`
	Total  int                         `json:"total"`
}
