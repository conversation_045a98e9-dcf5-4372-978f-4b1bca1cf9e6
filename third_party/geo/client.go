package geo

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/goasync"
	http_util "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/geo/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/geo/schema/geo_map"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/geo/schema/matrix"
)

var restInvoker = chassis.NewRestInvoker()

type GeoClient struct {
}

func NewGeoClient() *GeoClient {
	return &GeoClient{}
}

func (g *GeoClient) MatchAddress(ctx context.Context, query geo_map.AddressMatchQueryV1) (geo_map.AddressMatchRsp, error) {
	// 新增开关 如果调用量增加 关闭调用
	if config.GetMutableConf(ctx).GoogleCallShutDown {
		logger.CtxLogErrorf(ctx, "MatchAddress google call shut down")
		return geo_map.AddressMatchRsp{}, errors.New("MatchAddress google call shut down")
	}
	logger.CtxLogInfof(ctx, "start to MatchAddress, query: %+v", query)
	var err error
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("got painic %v", r)
			goasync.PanicHandler(err)
			logger.CtxLogErrorf(ctx, "MatchAddress | an panic occur, panic: %v", err)
		}
	}()
	if query.Components == "" {
		query.Components = fmt.Sprintf("country:%v", utils.GetRegion(ctx))
	}
	// 此处传入url对BR无效，BR使用新接口
	path := getGeoServiceUrl(ctx, constant.GeoCodeUrl)
	if path == "" {
		logger.CtxLogErrorf(ctx, "MatchAddress | path is empty")
		return geo_map.AddressMatchRsp{}, errors.New("path is empty")
	}
	//参数处理
	requestBody := g.MatchAddressParamAssembly(ctx, query)
	//请求map google geo
	var ret geo_map.AddressMatchRsp
	lErr := http_util.SendPostHttpRequestWithOriginalContext(ctx, restInvoker, g.getTimeout(ctx), path, requestBody, &ret, nil)
	if lErr != nil {
		logger.CtxLogErrorf(ctx, "GeoClient | fail to match address from map service, err: %v", err)
		return ret, errors.New(lErr.Msg)
	}
	return ret, err
}

func (g GeoClient) Matrix(ctx context.Context, matrixParam *matrix.MatrixReq) (resp *matrix.MatrixResp, err error) {
	if config.GetMutableConf(ctx).GoogleCallShutDown {
		logger.CtxLogErrorf(ctx, "Matrix google call shut down")
		return nil, errors.New("Matrix google call shut down")
	}
	path := getMatrixApiUrl(ctx)
	matrixResp := &matrix.MatrixResp{}
	if lErr := http_util.SendPostHttpRequestWithOriginalContext(ctx, restInvoker, g.getTimeout(ctx), path, matrixParam, matrixResp, nil); lErr != nil {
		logger.CtxLogErrorf(ctx, "matrix httpMatrix error, error=%v\n", lErr.Msg)
		return matrixResp, err
	}

	if !(matrixResp.Status == "SUCCESS" || matrixResp.Status == "SUCCESS_PARTIAL") {
		errStr := fmt.Sprintf("map.matrix_api err:%s, %s", matrixResp.Status, matrixResp.ErrorMessage)
		logger.CtxLogErrorf(ctx, "fail at map.matrix)api, err:%s", errStr)
		return matrixResp, fmt.Errorf(errStr)
	}
	if matrixResp.Status == constant.TimeoutError {
		logger.CtxLogErrorf(ctx, "matrix timeout error\n")
		return matrixResp, errors.New(constant.TimeoutError)
	}

	return matrixResp, nil
}

func getMatrixApiUrl(ctx context.Context) string {
	url := getMatrixHost(ctx) + constant.GeoMatrixUrl
	logger.LogInfof("getMatrixApiUrl, url=%v\n", url)
	return url
}

func (g *GeoClient) getTimeout(ctx context.Context) time.Duration {
	timeout := config.GetMutableConf(ctx).MapTeamServiceConfig.Timeout
	if timeout <= 0 {
		return 2 * time.Second
	}
	return time.Duration(timeout) * time.Millisecond
}

func (g GeoClient) MatchAddressParamAssembly(ctx context.Context, query geo_map.AddressMatchQueryV1) geo_map.AddressMatchQueryV1 {
	// br市场调新接口 https://jira.shopee.io/browse/SPSSO-2396
	// todo SPLPS-13459 本期暂不考虑BR
	//if strings.ToUpper(utils.GetRegion(ctx)) == "BR" {
	//	query.Meta.UseCase = "BE.SPX.smart_zoning" // todo 产品暂时每个live的 use_case, 临时使用
	//	requestBody = geo_map.AddressMatchQueryV2{
	//		RequestMeta: query.Meta,
	//		AddressList: query.AddressList,
	//	}
	//} else if strings.ToUpper(utils.GetRegion(ctx)) == "TW" {
	requestBody := geo_map.AddressMatchQueryV1{}
	if strings.ToUpper(utils.GetRegion(ctx)) == "TW" {
		for index, _ := range query.AddressList {
			query.AddressList[index].Address = fmt.Sprintf("%v,%v,%v",
				query.AddressList[index].AddressL1,
				query.AddressList[index].AddressL2,
				query.AddressList[index].Address)
			query.AddressList[index].AddressL1 = ""
			query.AddressList[index].AddressL2 = ""
		}
		requestBody = query
	} else {
		requestBody = query
	}
	return requestBody
}
