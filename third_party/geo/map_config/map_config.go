package map_config

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/geo/schema/matrix"
)

// GetMapMatrixDefaultReq 获取Matrix默认值为
// {"user"："SPX","project": "smart_sorting","client_key": "OUlMpWzy7AgTC1V48iq4ooK2lRIeGWOXiIvBfmtN","source":"geo","mode":"car","region":"BR" }
func GetMapMatrixDefaultReq(ctx context.Context) *matrix.MatrixReq {
	// SPX MatrixDefaultParam是空
	//if value, ok := archaius.Get(fmt.Sprintf("%v.%v", ThirdPartyNameSpace, MatrixDefaultParam)).(string); ok {
	//	// 存在配置数据
	//	matrixParam := &matrix.MatrixReq{}
	//	err := json.Unmarshal([]byte(fmt.Sprintf("%v", value)), matrixParam)
	//	if err == nil {
	//		// 直接返回
	//		return matrixParam
	//	}
	//	logger.CtxLogErrorf(ctx, "GetMapMatrixDefaultReq Unmarshal fail， config value=%s, err=%v", value, err)
	//}

	// 默认配置
	return &matrix.MatrixReq{
		Source: "geo",
		Mode:   "car",
		//DepartureTime: recorder.Now(ctx).Unix(),
		User:      "SPX",
		Project:   "smart_sorting",
		ClientKey: config.GetMutableConf(ctx).MapApiClientKey,
		Region:    "BR",
	}
}
