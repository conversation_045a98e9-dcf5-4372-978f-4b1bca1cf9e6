package geo

import (
	"context"
	"fmt"
	"strings"

	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

func getMatrixHost(ctx context.Context) string {
	//http://routing.geo.test.shopee.io
	env := strings.ToLower(utils.GetEnv(ctx))
	if env == "live" || env == "liveish" || env == "livetest" {
		env = ""
	} else {
		env = env + "."
	}
	return fmt.Sprintf("http://routing.geo.%sshopee.io", env)
}

func getGeoServiceUrl(ctx context.Context, url string) string {
	upperCid := strings.ToUpper(utils.GetRegion(ctx))
	getHostFunc := func() string {
		domain := "shopee.co.id"
		switch upperCid {
		case "PH":
			domain = "shopee.ph"
		case "ID":
			domain = "shopee.co.id"
		case "TH":
			domain = "shopee.co.th"
		case "VN":
			domain = "shopee.vn"
		case "MY":
			domain = "shopee.com.my"
		case "SG":
			domain = "shopee.sg"
		case "TW":
			domain = "shopee.tw"
		case "BR":
			return getGeoServiceUrlForBR(ctx)
		default:
			return ""
		}
		env := strings.ToLower(utils.GetEnv(ctx))
		if env == "live" || env == "liveish" || env == "livetest" {
			env = ""
		} else {
			env = env + "."
		}

		return fmt.Sprintf("https://%s%s", env, domain)
	}
	path := getHostFunc()
	if path == "" {
		return ""
	}

	if upperCid == "BR" {
		return path
	}

	path = path + url
	return path
}

func getGeoServiceUrlForBR(ctx context.Context) string {
	// todo 暂无br服务 本期不需要考虑
	return ""
	//configPath := config.GetGeoServiceUrl(ctx)
	//
	//if configPath != "" {
	//	return configPath
	//}
	//
	//env := utils.GetEnv(ctx)
	//switch env {
	//case "UAT":
	//	return "http://gateway.openplatform.uat.map.shopee.gcp.ex.br/api/v1/poi/geocode_v2"
	//case "LIVE":
	//	return "http://gateway.openplatform.map.shopee.gcp.ex.br/api/v1/poi/geocode_v2"
	//case "TEST", "STAGING":
	//	return "http://br.gateway.openplatform.map.test.shopee.io/api/v1/poi/geocode_v2"
	//default:
	//	return ""
	//}
}
