package matrix

// matrix 接口文档：https://confluence.shopee.io/pages/viewpage.action?spaceKey=SGST&title=Matrix+API+v1.1#heading-Location
type Location struct {
	Lat  float64 `json:"lat"`
	Lon  float64 `json:"lon"`
	Name string  `json:"name"`
}

type MatrixReq struct {
	Origins      []Location `json:"origins" validate:"required"`
	Destinations []Location `json:"destinations" validate:"required"`
	Source       string     `json:"source" validate:"required"`
	Mode         string     `json:"mode"`
	Objective    string     `json:"objective"`
	Region       string     `json:"region" validate:"required"`
	//DepartureTime int64      `json:"departure_time"`
	Avoid     []string `json:"avoid"`
	User      string   `json:"user" validate:"required"`
	Project   string   `json:"project" validate:"required"`
	ClientKey string   `json:"client_key" validate:"required"`
}

type SingleMatrixReq struct {
	StartRowIndex int        `json:"start_row_index" validate:"required"`
	EndRowIndex   int        `json:"end_row_index" validate:"required"`
	StartColIndex int        `json:"start_col_index" validate:"required"`
	EndColIndex   int        `json:"end_col_index" validate:"required"`
	MatrixReq     *MatrixReq `json:"matrix_request" validate:"required"`
}

type Waypoint struct {
	Location Location `json:"location"`
}

type LocationError struct {
	Status string `json:"status"`
}

type MatrixErrorInfo struct {
	OriginsErrors      []LocationError `json:"origins_errors"`
	DestinationsErrors []LocationError `json:"destinations_errors"`
}

type MatrixResp struct {
	Status       string          `json:"status"`
	Source       string          `json:"source"`
	Origins      []Waypoint      `json:"origins"`
	Destinations []Waypoint      `json:"destinations"`
	Distances    [][]int         `json:"distances"`
	Durations    [][]int         `json:"durations"`
	ErrorMessage string          `json:"error_message"`
	ErrorInfo    MatrixErrorInfo `json:"error_info"`
}

type SingleMatrixResp struct {
	StartRowIndex int         `json:"start_row_index" validate:"required"`
	EndRowIndex   int         `json:"end_row_index" validate:"required"`
	StartColIndex int         `json:"start_col_index" validate:"required"`
	EndColIndex   int         `json:"end_col_index" validate:"required"`
	MatrixResp    *MatrixResp `json:"matrix_resp"`
}
