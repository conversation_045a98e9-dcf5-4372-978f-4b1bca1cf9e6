package geo_map

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/geo/constant"

type AddressMatchQueryV1 struct {
	Meta
	AddressList []AddressDetail `json:"address_list"`
}

type AddressMatchQueryV2 struct {
	RequestMeta Meta            `json:"request_meta"`
	AddressList []AddressDetail `json:"address_list"`
}

type Meta struct {
	UseCase    string `json:"use_case"`
	Components string `json:"components"`
}

type AddressDetail struct {
	Address   string `json:"address"`
	AddressL1 string `json:"address_level1"`
	AddressL2 string `json:"address_level2"`
	AddressL3 string `json:"address_level3"`
	AddressL4 string `json:"address_level4"`
	Zipcode   string `json:"zipcode"`
}

type AddressMatchRsp struct {
	Error        int       `json:"error"`
	ErrorMessage string    `json:"error_msg"`
	ResultList   []Address `json:"result_list"`
}

type Address struct {
	InputAddress       string                 `json:"input_address"`
	OutputAddress      string                 `json:"output_address"`
	Geometry           Geometry               `json:"geometry"`
	PlaceId            string                 `json:"place_id"`
	LocationConfidence bool                   `json:"location_confidence"`
	Group              string                 `json:"group"`
	MatchLevel         constant.GeoMatchLevel `json:"match_level"`
	ErrorMsg           string                 `json:"error_msg"`
}

type Geometry struct {
	Location     Location `json:"location"`
	LocationType int      `json:"location_type"`
}

type Location struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}
