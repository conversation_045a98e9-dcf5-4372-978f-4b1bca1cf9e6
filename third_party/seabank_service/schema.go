package seabank_service

type SeabankGatewayRequest struct {
	AppId       string `json:"appId"`       // AppId 用于识别调用方, 由 bank 分配
	ChannelCode string `json:"channelCode"` // ChannelCode 通道编码, 由bank分配
	Charset     string `json:"charset"`     // Charset 编码格式, 统一传 utf-8
	Version     string `json:"version"`     // Version 接口版本, 目前统一传 1.0
	Method      string `json:"method"`      // Method 接口名, 不同业务场景不同
	Timestamp   int64  `json:"timestamp"`   // Timestamp 请求时间, UNIX 时间戳
	Nonce       string `json:"nonce"`       // Nonce 流水号, 应保证15分钟内不重复
	SignType    string `json:"signType"`    // SignType 签名类型, 目前仅支持 RSA2
	Sign        string `json:"sign"`        // Sign 签名, 签名算法可参考 https://docs.google.com/document/d/16_V13cfiD33I-yIQJ4GzTDW79kG7aExG8xbrkZhcT2Q/edit
	BizContent  string `json:"bizContent"`  // BizContent 业务请求体, 不同 method 对应不同的 bizContent
}

type SeabankGatewayResponse struct {
	Code       int    `json:"code"`
	Msg        string `json:"msg"`
	Timestamp  int64  `json:"timestamp"`
	Sign       string `json:"sign"`
	BizContent string `json:"bizContent"`
}

type NotifyServiceableAreaChangeRequest struct {
	FileUrl    string `json:"fileUrl"`    // FileUrl 变更文件地址
	UpdateTime int64  `json:"updateTime"` // UpdateTime 请求时间戳
	ReqNo      string `json:"reqNo"`      // ReqNo 请求流水号, 全局唯一（一次地址更新的唯一标识）
}

type NotifyServiceableAreaChangeResponse struct {
	Status    string `json:"status"`    // Status 通知结果, S-succes, F-fail
	ErrorCode string `json:"errorCode"` // ErrorCode 000000-success
	ErrorDesc string `json:"errorDesc"` // ErrorDesc
}
