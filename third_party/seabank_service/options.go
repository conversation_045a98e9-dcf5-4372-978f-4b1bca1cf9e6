package seabank_service

import (
	"crypto"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"net/url"
	"strconv"
)

type Option func(req *SeabankGatewayRequest) *lcos_error.LCOSError

func WithNonce(nonce string) Option {
	return func(req *SeabankGatewayRequest) *lcos_error.LCOSError {
		req.Nonce = nonce
		return nil
	}
}

func WithTimestamp(timestamp int64) Option {
	return func(req *SeabankGatewayRequest) *lcos_error.LCOSError {
		req.Timestamp = timestamp
		return nil
	}
}

func WithRsaSign(privateKey string) Option {
	return func(req *SeabankGatewayRequest) *lcos_error.LCOSError {
		req.SignType = DefaultSignType

		// signContent生成格式: appId=${appId}&bizContent=${bizContent}&channelCode=${channelCode}&charset=${charset}&method=${method}&nonce=${nonce}&signType=${signType}&timestamp=${timestamp}&version=${version}
		value := url.Values{}
		value.Add("appId", req.AppId)
		value.Add("bizContent", req.BizContent)
		value.Add("channelCode", req.ChannelCode)
		value.Add("charset", req.Charset)
		value.Add("method", req.Method)
		value.Add("nonce", req.Nonce)
		value.Add("signType", req.SignType)
		value.Add("timestamp", strconv.FormatInt(req.Timestamp, 10))
		value.Add("version", req.Version)
		signContent, _ := url.QueryUnescape(value.Encode())

		sign, err := RsaSign(signContent, privateKey, crypto.SHA256)
		if err != nil {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "rsa sign error: %s", err.Error())
		}
		req.Sign = sign
		return nil
	}
}
