package seabank_service

import (
	"context"
	"crypto"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"os"
	"path"
	"testing"
)

func TestRsaSign(t *testing.T) {
	privateKey := ``

	publicKey := ``

	req := &SeabankGatewayRequest{
		AppId:       DefaultAppId,
		ChannelCode: DefaultChannelCode,
		Charset:     DefaultCharset,
		Version:     DefaultVersion,

		Method:     MethodCardDeliveryAddressUpdateNotify,
		Timestamp:  **********,
		Nonce:      "66f56637-2910-4d22-a69b-1a431c28fc28",
		BizContent: `{"fileUrl":"/test/**********/LPH81.csv","updateTime":**********,"reqNo":"66f56637-2910-4d22-a69b-1a431c28fc28"}`,
	}
	WithRsaSign(privateKey)(req)
	signContent := `appId=************&bizContent={"fileUrl":"/test/**********/LPH81.csv","updateTime":**********,"reqNo":"66f56637-2910-4d22-a69b-1a431c28fc28"}&channelCode=1001&charset=utf-8&method=card.delivery.region.update.notify.v1&nonce=66f56637-2910-4d22-a69b-1a431c28fc28&signType=RSA2&timestamp=**********&version=1.0`

	fmt.Println(req.Sign)

	if err := RsaVerify(signContent, publicKey, crypto.SHA256, req.Sign); err != nil {
		fmt.Println(err)
	} else {
		fmt.Println("验签成功")
	}

	fmt.Println("seabank 验签")
	sign3 := ``
	signContent3 := `code=0&msg=success&timestamp=**********&bizContent={"errorCode":"000000","errorDesc":"success","status":"S"}`
	publicKey3 := ``
	RsaVerify(signContent3, publicKey3, crypto.SHA256, sign3)
}

func TestGenRsaKey(t *testing.T) {
	privateKey, publicKey := GenRsaKey(2048)
	fmt.Println(string(privateKey))
	fmt.Println(string(publicKey))
}

func TestSeabankGatewayService_NotifyServiceableAreaChange(t *testing.T) {
	ctx := utils.NewCommonCtx(context.Background())

	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	_ = os.Setenv("ENV", "test")

	if err := chassis.Init(); err != nil {
		t.Fatalf("init chassis error: %s", err.Error())
	}
	_, _ = config.InitConfig(ctx)
	_, _ = config.InitMutableConfig(ctx)

	svc := NewSeabankGatewayService(ctx, "PH")
	if err := svc.NotifyCardDeliveryAddressUpdate(ctx, "TEST", "/test/**********/temp.csv"); err != nil {
		t.Fatalf("notify SA change error: %s", err.Msg)
	}
}
