package seabank_service

const (
	DefaultAppId       string = "************"
	DefaultChannelCode string = "1001"
	DefaultCharset     string = "utf-8"
	DefaultVersion     string = "1.0"
	DefaultSignType    string = "RSA2"
)

const (
	// Seabank Gateway request API
	DoGatewayRequestApi = "/v2/gateway.do"

	// method
	MethodCardDeliveryAddressUpdateNotify = "card.delivery.region.update.notify.v1"
)

type SeabankGatewayCode int

const (
	// Seabank 网关错误码
	GatewaySuccess            SeabankGatewayCode = 0
	GatewaySystemError        SeabankGatewayCode = 1200001
	GatewayVerifySignFailed   SeabankGatewayCode = 4200001
	GatewayAuthFailed         SeabankGatewayCode = 4200002
	GatewayAntiRepeatFailed   SeabankGatewayCode = 4200003
	GatewayRouteNotFound      SeabankGatewayCode = 4200004
	GatewaySignFailed         SeabankGatewayCode = 4200005
	GatewaySystemLimit        SeabankGatewayCode = 4200006
	GatewaySystemFuse         SeabankGatewayCode = 4200007
	GatewayParseResponseError SeabankGatewayCode = 4200008
	GatewaySignInvalid        SeabankGatewayCode = 4071101
	GatewaySystemFail         SeabankGatewayCode = 1070001
)

var SeabankGatewayErrorCodeMessage = map[SeabankGatewayCode]string{
	GatewaySystemError:        "SYSTEM_ERROR",
	GatewayVerifySignFailed:   "VERIFY_SIGN_FAILED",
	GatewayAuthFailed:         "AUTH_FAILED",
	GatewayAntiRepeatFailed:   "ANTI_REPEAT_FAILED",
	GatewayRouteNotFound:      "ROUTE_NOT_FOUND",
	GatewaySignFailed:         "SIGN_FAILED",
	GatewaySystemLimit:        "SYSTEM_LIMIT",
	GatewaySystemFuse:         "SYSTEM_FUSE",
	GatewayParseResponseError: "PARSE_RESPONSE_ERROR",
	GatewaySignInvalid:        "SIGN_INVALID",
	GatewaySystemFail:         "SYSTEM_FAIL",
}

const (
	// Seabank 业务错误码
	SeabankSuccess = "000000"
)

func GetErrorCodeMessage(code SeabankGatewayCode) string {
	msg, ok := SeabankGatewayErrorCodeMessage[code]
	if !ok {
		return "UNKNOWN_ERROR"
	}
	return msg
}
