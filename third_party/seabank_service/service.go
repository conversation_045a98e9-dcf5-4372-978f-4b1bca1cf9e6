package seabank_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	jsoniter "github.com/json-iterator/go"
	"sync"
	"time"
)

var (
	invoker *chassis.RestInvoker
	once    sync.Once
)

type SeabankGatewayService interface {
	NotifyCardDeliveryAddressUpdate(ctx utils.LCOSContext, versionName, fileUrl string) *lcos_error.LCOSError
}

func NewSeabankGatewayService(ctx utils.LCOSContext, region string) SeabankGatewayService {
	var host string
	cfg := config.GetMutableConf(ctx).SeabankGatewayServiceConfig
	if cfg.UseMock {
		host = cfg.MockHost
	} else {
		host = cfg.Host + constant.GetHostRegionSuffix(region)
	}

	return &seabankGatewayService{
		region: region,
		host:   host,
	}
}

type seabankGatewayService struct {
	region string
	host   string
}

func (s *seabankGatewayService) getTimeout(ctx utils.LCOSContext) time.Duration {
	seconds := config.GetMutableConf(ctx).SeabankGatewayServiceConfig.Timeout
	if seconds <= 0 {
		seconds = 10
	}
	return time.Duration(seconds) * time.Second
}

func (s *seabankGatewayService) GatewayDo(ctx utils.LCOSContext, method string, reqBizContent interface{}, respBizContent interface{}, opts ...Option) (string, *lcos_error.LCOSError) {
	// 序列化请求体biz content
	reqBizContentJson, err := jsoniter.MarshalToString(reqBizContent)
	if err != nil {
		return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "marshal request biz content error: %s", err.Error())
	}

	// 生成seabank gateway的请求参数
	req := &SeabankGatewayRequest{
		AppId:       DefaultAppId,
		ChannelCode: DefaultChannelCode,
		Charset:     DefaultCharset,
		Version:     DefaultVersion,

		Method:     method,
		Timestamp:  GetCurrentUnixTimestamp(),
		Nonce:      GenerateUUID(),
		BizContent: reqBizContentJson,
	}
	for _, opt := range opts {
		if lcosErr := opt(req); lcosErr != nil {
			return "", lcosErr
		}
	}
	reqJson := utils.MarshToStringWithoutError(req)

	once.Do(func() {
		invoker = chassis.NewRestInvoker()
	})

	// 请求seabank gateway
	url := s.host + DoGatewayRequestApi
	var resp SeabankGatewayResponse
	if lcosErr := http.SendPostHttpRequest(ctx, invoker, s.getTimeout(ctx), url, req, &resp, nil); lcosErr != nil {
		logger.CtxLogErrorf(ctx, "request seabank gateway failed, send http request error|url=%s, req=%s, err=%s", url, reqJson, lcosErr.Msg)
		return "", lcosErr
	}
	retcode := SeabankGatewayCode(resp.Code)
	if retcode != GatewaySuccess {
		logger.CtxLogErrorf(ctx, "request seabank gateway failed, gateway returns error|url=%s, req=%s, code=%s, err=%s", url, reqJson, GetErrorCodeMessage(retcode), resp.Msg)
		return "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "%s: %s", GetErrorCodeMessage(retcode), resp.Msg)
	}
	if err = jsoniter.UnmarshalFromString(resp.BizContent, &respBizContent); err != nil {
		logger.CtxLogErrorf(ctx, "request seabank gateway failed, unmarshal response biz content error|url=%s, req=%s, err=%s", url, reqJson, err.Error())
		return "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "unmarshal response biz content error: %s", err.Error())
	}
	logger.CtxLogInfof(ctx, "request seabank gateway success|url=%s, req=%s", url, reqJson)
	return resp.Sign, nil
}

func (s *seabankGatewayService) NotifyCardDeliveryAddressUpdate(ctx utils.LCOSContext, versionName, fileUrl string) *lcos_error.LCOSError {
	nowTime := GetCurrentUnixTimestamp()                                        // 网关请求的时间戳
	uniqueKey := fmt.Sprintf("%s-%s-%s", s.region, versionName, GenerateUUID()) // 网关请求的流水号，要求全局唯一

	req := NotifyServiceableAreaChangeRequest{
		FileUrl:    fileUrl,
		UpdateTime: recorder.Now(ctx).UnixMilli(),
		ReqNo:      uniqueKey,
	}
	var resp NotifyServiceableAreaChangeResponse
	_, err := s.GatewayDo(ctx, MethodCardDeliveryAddressUpdateNotify, req, &resp, WithTimestamp(nowTime), WithNonce(uniqueKey), WithRsaSign(config.GetMutableConf(ctx).CardDeliveryAddressSyncConfig.PrivateKey))
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "request seabank gateway error|method=%s, file_url=%s, err=%s", MethodCardDeliveryAddressUpdateNotify, fileUrl, err.Msg)
	}
	if resp.ErrorCode != SeabankSuccess {
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "seabank response return error|method=%s, file_url=%s, err_code=%s, err_desc=%s", MethodCardDeliveryAddressUpdateNotify, fileUrl, resp.ErrorCode, resp.ErrorDesc)
	}
	return nil
}
