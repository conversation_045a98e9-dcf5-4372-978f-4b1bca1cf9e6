package seabank_service

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"github.com/google/uuid"
	"time"
)

func GetCurrentUnixTimestamp() int64 {
	return time.Now().Unix()
}

func GenerateUUID() string {
	u1, _ := uuid.NewUUID()
	return u1.String()
}

// RsaSign RSA加签
func RsaSign(signContent string, privateKey string, hash crypto.Hash) (string, error) {
	shaNew := hash.New()
	shaNew.Write([]byte(signContent))
	hashed := shaNew.Sum(nil)

	priKey, err := ParsePrivateKey(privateKey)
	if err != nil {
		return "", err
	}

	var pssOptions = rsa.PSSOptions{SaltLength: rsa.PSSSaltLengthEqualsHash}
	signature, err := rsa.SignPSS(rand.<PERSON>, priKey, crypto.SHA256, hashed[:], &pssOptions)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(signature), nil
}

// RsaVerify RSA验签
func RsaVerify(signContent string, pub string, hash crypto.Hash, sign string) error {
	shaNew := hash.New()
	shaNew.Write([]byte(signContent))
	hashed := shaNew.Sum(nil)

	pke, err := ParsePublicKey(pub)
	if err != nil {
		return err
	}
	signature, err := base64.StdEncoding.DecodeString(sign)
	if err != nil {
		return err
	}
	return rsa.VerifyPSS(pke.(*rsa.PublicKey), crypto.SHA256, hashed[:], signature, nil)
}

func ParsePrivateKey(privateKey string) (*rsa.PrivateKey, error) {
	// privateKey = FormatPrivateKey(privateKey)
	block, _ := pem.Decode([]byte(privateKey))
	if block == nil {
		return nil, errors.New("error decode private key！")
	}
	priKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	return priKey, nil
}

func ParsePublicKey(public string) (interface{}, error) {
	block, _ := pem.Decode([]byte(public))
	if block == nil {
		return nil, errors.New("error decode private key！")
	}
	pubKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	return pubKey, nil
}

func GenRsaKey(bits int) (prvkey, pubkey []byte) {
	// 生成私钥文件
	privateKey, err := rsa.GenerateKey(rand.Reader, bits)
	if err != nil {
		panic(err)
	}
	derStream := x509.MarshalPKCS1PrivateKey(privateKey)
	block := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: derStream,
	}
	prvkey = pem.EncodeToMemory(block)
	publicKey := &privateKey.PublicKey
	derPkix, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		panic(err)
	}
	block = &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: derPkix,
	}
	pubkey = pem.EncodeToMemory(block)
	return
}
