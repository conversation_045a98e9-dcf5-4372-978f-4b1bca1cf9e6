package algo_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

type AlgoResponse interface {
	GetRetcode() int
	GetMessage() string
}

type CommonResponse struct {
	Retcode int    `json:"retcode"`
	Message string `json:"message"`
}

func (resp *CommonResponse) GetRetcode() int {
	return resp.Retcode
}

func (resp *CommonResponse) GetMessage() string {
	return resp.Message
}

type AlgoModelConf struct {
	ProductId   int                                `json:"productId"`
	Region      string                             `json:"region"`
	Status      edd_constant.AlgoModelStatusString `json:"status"`
	RefreshTime uint64                             `json:"refreshTime"`

	// SPLN-34309
	BizType        edd_constant.BusinessTypeString `json:"bizType"`
	ModelVersion   string                          `json:"modelVersion"`
	SlideWinID     string                          `json:"slideWinId"`
	SlideWinParams string                          `json:"slideWinParams"`

	// SPLN-35757
	WithRouteGroup bool `json:"withRouteGroup"`
}

type ListAlgoModelConfRequest struct {
	Region     string                           `json:"region"`
	ModuleType edd_constant.AlgoModelTypeString `json:"moduleType"`
	BizType    edd_constant.BusinessTypeString  `json:"bizType,omitempty"`
}

type ListAlgoModelConfResponse struct {
	CommonResponse
	Data []*AlgoModelConf `json:"data"`
}

type AlgoSimulation struct {
	ConfigId         uint64             `json:"configId"`
	Weight           string             `json:"weight"`
	Bound            string             `json:"bound"`
	SimulationResult map[string]float32 `json:"simulationResult"`
	OperateTime      int64              `json:"operateTime"`
	Operator         string             `json:"operator"`

	Status edd_constant.AlgoModelStatusString `json:"status"`

	ModelVersion      string                          `json:"modelVersion"`
	SlideWinID        uint64                          `json:"slideWinId"`
	SlideWinParams    string                          `json:"slideWinParams"`
	SimulationVersion string                          `json:"simulationVersion"`
	SlideWinFlag      edd_constant.SlideWinFlagType   `json:"slideWinFlag"`
	ProductId         int                             `json:"productId"`
	BizType           edd_constant.BusinessTypeString `json:"bizType"`
	RefreshTime       int64                           `json:"refreshTime"`

	// SPLN-35757 新增，只有route维度划窗才有这两个字段
	ByRouteGroup    bool   `json:"by_route_group"`
	OriginZone      string `json:"originZone"`
	DestinationZone string `json:"destinationZone"`
}

func (a *AlgoSimulation) IsRecommend() bool {
	return a.SlideWinFlag == edd_constant.SlideWinFlagRecommend || a.SlideWinFlag == edd_constant.SlideWinFlagInheritedAndRecommend
}

type ListAlgoSimulationRequest struct {
	Region     string                           `json:"region"`
	ModuleType edd_constant.AlgoModelTypeString `json:"moduleType"`

	ModelVersion string `json:"modelVersion"`
}

type ListAlgoSimulationResponse struct {
	CommonResponse
	Data []*AlgoSimulation `json:"data"`
}

type DeployAlgoSimulationRequest struct {
	Region     string                           `json:"region"`
	ConfigId   uint64                           `json:"configId"`
	ProductId  int                              `json:"productId"`
	Operator   string                           `json:"operator"`
	ModuleType edd_constant.AlgoModelTypeString `json:"moduleType"`
}

type RevokeAlgoSimulationRequest DeployAlgoSimulationRequest

type PredictEDDRequest struct {
	Region    string `json:"region"`
	SlsTn     string `json:"slsTn"`
	Status    string `json:"status"` // tracking code
	EventType string `json:"eventType"`
	EventTime int64  `json:"eventTime"`
	BuyerId   string `json:"buyerId"`
	ProductId int    `json:"productId"`
}

type PredictEDDInfo struct {
	EDDMin    string `json:"EDDMin"` // YYYY-mm-dd
	EDDMax    string `json:"EDDMax"` // YYYY-mm-dd
	SlsTn     string `json:"slsTn"`
	EventType string `json:"eventType"`
	EventTime int64  `json:"eventTime"`
}

func (p *PredictEDDInfo) IsSameEvent(eventType string) bool {
	return p.EventType == eventType
}

func (p *PredictEDDInfo) ParseEddTime(region string) (int64, int64, *lcos_error.LCOSError) {
	/*
		Algo推送的EDD是一个日期，而SLS需要向上游推送一个时间戳。因此，默认推送EDD当日23:59的时间戳
	*/
	eddMin, err := utils.ParseTimeInRegion(fmt.Sprintf("%s 23:59:59", p.EDDMin), constant.DateAndTimeFormat, region)
	if err != nil {
		return 0, 0, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("invalid format edd min: %s", p.EDDMin))
	}
	eddMax, err := utils.ParseTimeInRegion(fmt.Sprintf("%s 23:59:59", p.EDDMax), constant.DateAndTimeFormat, region)
	if err != nil {
		return 0, 0, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("invalid format edd max: %s", p.EDDMax))
	}
	return eddMin, eddMax, nil
}

type PredictEDDResponse struct {
	CommonResponse
	Data *PredictEDDInfo `json:"data"`
}

func (resp *PredictEDDResponse) NeedRetry() bool {
	// EDD预测服务返回错误码在[10000,20000)之间的，间隔几分钟尝试重试
	return resp.Retcode >= retcodeRetryMin && resp.Retcode < retcodeRetryMax
}

type DisableAlgoModelConfRequest struct {
	Region     string                           `json:"region"`
	ProductId  int                              `json:"productId"`
	Operator   string                           `json:"operator"`
	ModuleType edd_constant.AlgoModelTypeString `json:"moduleType"`
}

type QueryEffectiveAlgoSimulationRequest struct {
	Region     string                           `json:"region"`
	ProductId  int                              `json:"productId"`
	ModuleType edd_constant.AlgoModelTypeString `json:"moduleType"`
}

type EffectiveAlgoSimulation struct {
	ConfigId uint64 `json:"configId"`
}

func (e *EffectiveAlgoSimulation) IsEffective() bool {
	return e != nil && e.ConfigId > 0
}

type QueryEffectiveEDDSimulationResponse struct {
	CommonResponse
	Data *EffectiveAlgoSimulation `json:"data"`
}

type QueryMetaDataRequest struct {
	Region     string                           `json:"region"`
	ModuleType edd_constant.AlgoModelTypeString `json:"moduleType"`
}

type MetaData struct {
	BizType              edd_constant.BusinessTypeString `json:"bizType"`
	WeightType           edd_constant.WeightType         `json:"weightType"`
	SimulationResult     map[string]float32              `json:"simulationResult"`
	TrainSetDate         string                          `json:"trainSetDate"`
	TestSetDate          string                          `json:"testSetDate"`
	Status               edd_constant.AlgoModelStatus    `json:"status"`
	ModelVersion         string                          `json:"modelVersion"`
	IsAllProductDeployed int                             `json:"isAllProductDeployed"`
	RefreshTime          uint64                          `json:"refreshTime"`
}

type QueryMetaDataResponse struct {
	CommonResponse
	Data []*MetaData `json:"data"`
}

type DeployAllModelRequest struct {
	Region          string                           `json:"region"`
	ModuleType      edd_constant.AlgoModelTypeString `json:"moduleType"`
	OldModelVersion string                           `json:"oldModelVersion"`
	WeightType      edd_constant.WeightType          `json:"weightType"`
	NewModelVersion string                           `json:"newModelVersion"`
	BizType         edd_constant.BusinessTypeString  `json:"bizType"`
	Operator        string                           `json:"operator"`
}

type DeployAllModelResponse struct {
	CommonResponse
	Data struct {
		ProductList []int `json:"productList"`
	} `json:"data"`
}

type ReplaceRecommendModelRequest struct {
	Region      string `json:"region"`
	OldConfigId uint64 `json:"oldConfigId"`
	NewConfigId uint64 `json:"newConfigId"`
}
