package algo_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/monitor"
)

// getAlgoModelType 根据传入的模型类型返回相应的算法模型类型字符串。
// 参数:
//
//	modelType constant.AlgoModelType - 传入的模型类型，属于常量类型。
//
// 返回值:
//
//	string - 根据模型类型返回的算法模型类型字符串，可能是 "edd" 或 "edt"。
func getAlgoModelType(modelType edd_constant.AlgoModelType) edd_constant.AlgoModelTypeString {
	return modelType.ToString()
}

// ListAlgoModelConf
// model type: edd edt
// biz type : 3pl cb local（注意当不需要此值时填0）
func ListAlgoModelConf(ctx utils.LCOSContext, region string, modelType edd_constant.AlgoModelType, bizType edd_constant.BusinessType) ([]*AlgoModelConf, *lcos_error.LCOSError) {
	/* 获取全量 Algo 仿真模型列表 */
	resp := &ListAlgoModelConfResponse{}
	req := &ListAlgoModelConfRequest{
		Region:     region,
		ModuleType: getAlgoModelType(modelType),
		BizType:    bizType.ToString(),
	}
	lcosErr := sendAlgoRequest(ctx, region, listAlgoModelConfApi, req, resp)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return resp.Data, nil
}

func ListAlgoSimulation(ctx utils.LCOSContext, region string, modelVersion string, modelType edd_constant.AlgoModelType) ([]*AlgoSimulation, *lcos_error.LCOSError) {
	/* 获取 Algo 仿真 weight 配置列表 */
	resp := &ListAlgoSimulationResponse{}
	req := &ListAlgoSimulationRequest{
		Region:       region,
		ModuleType:   getAlgoModelType(modelType),
		ModelVersion: modelVersion,
	}
	lcosErr := sendAlgoRequest(ctx, region, listAlgoSimulationApi, req, resp)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return resp.Data, nil
}

func ListRouteAlgoSimulation(ctx utils.LCOSContext, region string, modelVersion string, modelType edd_constant.AlgoModelType) ([]*AlgoSimulation, *lcos_error.LCOSError) {
	/* 获取 Route维度 Algo 仿真 weight 配置列表 */
	resp := &ListAlgoSimulationResponse{}
	req := &ListAlgoSimulationRequest{
		Region:       region,
		ModuleType:   getAlgoModelType(modelType),
		ModelVersion: modelVersion,
	}
	lcosErr := sendAlgoRequest(ctx, region, listRouteAlgoSimulationAPi, req, resp)
	if lcosErr != nil {
		return nil, lcosErr
	}
	simulationList := resp.Data
	for _, simulation := range simulationList {
		simulation.ByRouteGroup = true // route维度划窗将by route group标记置为true
	}
	return simulationList, nil
}

func RevokeAlgoSimulation(ctx utils.LCOSContext, region string, productId int, modelType edd_constant.AlgoModelType, configId uint64, operator string) *lcos_error.LCOSError {
	/* 撤销 Algo 仿真模型 */
	resp := &CommonResponse{}
	req := &RevokeAlgoSimulationRequest{
		Region:     region,
		ProductId:  productId,
		ConfigId:   configId,
		Operator:   operator,
		ModuleType: getAlgoModelType(modelType),
	}
	return sendAlgoRequest(ctx, region, revokeAlgoSimulationApi, req, resp)
}

func DeployAlgoSimulation(ctx utils.LCOSContext, region string, productId int, modelType edd_constant.AlgoModelType, configId uint64, operator string) *lcos_error.LCOSError {
	/* 启用 Algo 仿真模型 */
	resp := &CommonResponse{}
	req := &DeployAlgoSimulationRequest{
		Region:     region,
		ProductId:  productId,
		ConfigId:   configId,
		Operator:   operator,
		ModuleType: getAlgoModelType(modelType),
	}
	return sendAlgoRequest(ctx, region, deployAlgoSimulationApi, req, resp)
}

func RevokeRouteAlgoSimulation(ctx utils.LCOSContext, region string, productId int, modelType edd_constant.AlgoModelType, configId uint64, operator string) *lcos_error.LCOSError {
	/* 撤销 Route维度 Algo 仿真模型 */
	resp := &CommonResponse{}
	req := &RevokeAlgoSimulationRequest{
		Region:   region,
		ConfigId: configId,
		Operator: operator,
	}
	return sendAlgoRequest(ctx, region, revokeRouteAlgoSimulationApi, req, resp)
}

func DeployRouteAlgoSimulation(ctx utils.LCOSContext, region string, productId int, modelType edd_constant.AlgoModelType, configId uint64, operator string) *lcos_error.LCOSError {
	/* 启用 Route维度 Algo 仿真模型 */
	resp := &CommonResponse{}
	req := &DeployAlgoSimulationRequest{
		Region:   region,
		ConfigId: configId,
		Operator: operator,
	}
	return sendAlgoRequest(ctx, region, deployRouteAlgoSimulationApi, req, resp)
}

func PredictEDD(ctx utils.LCOSContext, region string, productId int, slsTn string, buyerId string, status string, eventType string, eventTime int64) (*PredictEDDInfo, *lcos_error.LCOSError) {
	/* 启用 EDD 仿真模型 */
	resp := &PredictEDDResponse{}
	req := &PredictEDDRequest{
		Region:    region,
		SlsTn:     slsTn,
		Status:    status,
		BuyerId:   buyerId,
		EventType: eventType,
		EventTime: eventTime,
		ProductId: productId,
	}
	lcosErr := sendAlgoRequest(ctx, region, predictEDDApi, req, resp)
	if lcosErr != nil {
		// 区分需要重试的错误码
		if resp.NeedRetry() {
			return nil, lcos_error.NewLCOSError(lcos_error.SaturnNeedToRetryErrorCode, fmt.Sprintf("request algo error: %s, need retry", utils.MarshToStringWithoutError(resp)))
		}
		return nil, lcosErr
	}
	if resp.Data == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("request algo error: %s", utils.MarshToStringWithoutError(resp)))
	}
	return resp.Data, nil
}

func DisableAlgoModelConf(ctx utils.LCOSContext, region string, productId int, modelType edd_constant.AlgoModelType, operator string) *lcos_error.LCOSError {
	/* 撤销 Algo 仿真模型 */
	resp := &CommonResponse{}
	req := &DisableAlgoModelConfRequest{
		Region:     region,
		ProductId:  productId,
		Operator:   operator,
		ModuleType: getAlgoModelType(modelType),
	}
	return sendAlgoRequest(ctx, region, disableAlgoModelConfApi, req, resp)
}

func QueryEffectiveAlgoSimulation(ctx utils.LCOSContext, region string, productId int, modelType edd_constant.AlgoModelType) (*EffectiveAlgoSimulation, *lcos_error.LCOSError) {
	/* 撤销 Algo 仿真模型 */
	resp := &QueryEffectiveEDDSimulationResponse{}
	req := &QueryEffectiveAlgoSimulationRequest{
		Region:     region,
		ProductId:  productId,
		ModuleType: getAlgoModelType(modelType),
	}

	lcosErr := sendAlgoRequest(ctx, region, queryEffectiveAlgoSimulationApi, req, resp)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if resp.Data == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("request algo error: %s", utils.MarshToStringWithoutError(resp)))
	}
	return resp.Data, nil
}

func QueryAllMetaData(ctx utils.LCOSContext, region string, modelType edd_constant.AlgoModelType) ([]*MetaData, *lcos_error.LCOSError) {
	/* 获取 Algo 仿真模型元数据 */
	resp := &QueryMetaDataResponse{}
	req := &QueryMetaDataRequest{
		Region:     region,
		ModuleType: getAlgoModelType(modelType),
	}
	lcosErr := sendAlgoRequest(ctx, region, queryMetadataApi, req, resp)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return resp.Data, nil
}

// SPLN-34657
// 新增deploy all接口，返回所部署的渠道列表
func DeployAllMetaData(ctx utils.LCOSContext, region string, modelType edd_constant.AlgoModelType, newModelVersion string, oldModelVersion string, weightType edd_constant.WeightType, bizType edd_constant.BusinessType, operator string) ([]int, *lcos_error.LCOSError) {
	/* 启用 Algo 仿真模型 */
	resp := &DeployAllModelResponse{}
	req := &DeployAllModelRequest{
		Region:          region,
		ModuleType:      getAlgoModelType(modelType),
		NewModelVersion: newModelVersion,
		OldModelVersion: oldModelVersion,
		WeightType:      weightType,
		BizType:         bizType.ToString(),
		Operator:        operator,
	}
	return resp.Data.ProductList, sendAlgoRequest(ctx, region, deployAllModelApi, req, resp)
}

func ReplaceRecommendModel(ctx utils.LCOSContext, region string, oldConfigId, newConfigId uint64) *lcos_error.LCOSError {
	/* 启用 Algo 仿真模型 */
	resp := &CommonResponse{}
	req := &ReplaceRecommendModelRequest{
		Region:      region,
		OldConfigId: oldConfigId,
		NewConfigId: newConfigId,
	}
	return sendAlgoRequest(ctx, region, replaceRecommendModelApi, req, resp)
}

func ReplaceRouteRecommendModel(ctx utils.LCOSContext, region string, oldConfigId, newConfigId uint64) *lcos_error.LCOSError {
	/* 启用 Algo 仿真模型 */
	resp := &CommonResponse{}
	req := &ReplaceRecommendModelRequest{
		Region:      region,
		OldConfigId: oldConfigId,
		NewConfigId: newConfigId,
	}
	return sendAlgoRequest(ctx, region, replaceRouteRecommendModelApi, req, resp)
}

func sendAlgoRequest(ctx utils.LCOSContext, region string, endpoint string, req interface{}, resp AlgoResponse) *lcos_error.LCOSError {
	/*
		默认使用 POST 请求，统一记录请求日志
	*/
	host := config.GetAlgoHost(ctx, region)
	if len(host) == 0 {
		host = hosts[utils.GetEnv(ctx)]
	}

	url := host + endpoint
	lcosErr := http.SendPostHttpRequest(ctx, httpClient, defaultTimeout, url, req, resp, nil)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "sendAlgoRequest error|url=[%s]|req=[%s]|resp=[%s]|error=[%s]", url, utils.MarshToStringWithoutError(req), utils.MarshToStringWithoutError(resp), lcosErr.Msg)
		return lcosErr
	}
	if resp.GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "sendAlgoRequest error|url=[%s]|req=[%s]|resp=[%s]|retcode=[%d]|message=[%s]", url, utils.MarshToStringWithoutError(req), utils.MarshToStringWithoutError(resp), resp.GetRetcode(), resp.GetMessage())
		_ = monitor.AwesomeReportEvent(ctx, host, endpoint, strconv.Itoa(resp.GetRetcode()), resp.GetMessage())
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("request algo error: %s", utils.MarshToStringWithoutError(resp)))
	}
	_ = monitor.AwesomeReportEvent(ctx, host, endpoint, constant.StatusSuccess, "")
	logger.CtxLogInfof(ctx, "sendAlgoRequest success|url=[%s]|req=[%s]|resp=[%s]", url, utils.MarshToStringWithoutError(req), utils.MarshToStringWithoutError(resp))
	return nil
}
