package algo_service

import (
	"context"
	"testing"

	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

func TestListEDDModelConf(t *testing.T) {
	result, lcosErr := ListAlgoModelConf(utils.NewCommonCtx(context.Background()), "TH")
	if lcosErr != nil {
		t.<PERSON>r(lcosErr.Msg)
	}
	t.Logf("%v", result)
}
func TestListEDDSimulation(t *testing.T) {
	result, lcosErr := ListAlgoSimulation(utils.NewCommonCtx(context.Background()), "TH", 70015)
	if lcosErr != nil {
		t.Error(lcosErr.Msg)
	}
	t.Logf("%v", result)
}

func TestPredictEDD(t *testing.T) {
	result, lcosErr := PredictEDD(
		utils.NewCommonCtx(context.Background()), "ID", 111, "ID240528766411B", "11652134", "F100", "Pickup Done", 1714122218,
	)
	if lcosErr != nil {
		t.Error(lcosErr.Msg)
	}
	t.Logf("%v", result)
}

func TestNeedRetry(t *testing.T) {
	resp := &PredictEDDResponse{}
	resp.Retcode = 10000

	if !resp.NeedRetry() {
		t.Error("retry error")
	}

	resp.Retcode = 20000
	if resp.NeedRetry() {
		t.Error("retry error")
	}
}
