package algo_service

import (
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis"
)

var (
	hosts = map[string]string{
		"LOCAL":   "http://ai.ssc.test.shopee.sg",
		"TEST":    "http://ai.ssc.test.shopee.sg",
		"UAT":     "http://ai.ssc.test.shopee.sg",
		"STAGING": "http://ai.ssc.staging.shopee.sg",
		"LIVE":    "http://ai.ssc.shopee.sg",
	}

	defaultTimeout = 5 * time.Second
	httpClient     *chassis.RestInvoker
)

const (
	// SPLN-33865 ALGO Model相关的admin接口
	listAlgoModelConfApi            = "/api/predict/model_config/list"
	listAlgoSimulationApi           = "/api/predict/simulation/list"
	revokeAlgoSimulationApi         = "/api/predict/model_config/revoke"
	deployAlgoSimulationApi         = "/api/predict/model_config/deploy"
	disableAlgoModelConfApi         = "/api/predict/model_config/product/disable"
	queryEffectiveAlgoSimulationApi = "/api/predict/model_config/effective/query"
	retcodeRetryMin                 = 10000
	retcodeRetryMax                 = 20000

	// 在线计算EDD接口
	predictEDDApi = "/api/predict/edd"

	// SPLN-34309 新增模型自动刷新接口
	queryMetadataApi         = "/api/predict/metadata/model/query"           // 展示region维度的仿真效果
	deployAllModelApi        = "/api/predict/metadata/model/deploy"          // region维度，deploy all接口
	replaceRecommendModelApi = "/api/predict/model_config/recommend/replace" // 替换为推荐配置

	listRouteAlgoSimulationAPi    = "/api/predict/model_config/route/list"
	deployRouteAlgoSimulationApi  = "/api/predict/model_config/route/deploy"
	revokeRouteAlgoSimulationApi  = "/api/predict/model_config/route/revoke"
	replaceRouteRecommendModelApi = "/api/predict/model_config/route/recommend/replace"
)

func init() {
	httpClient = chassis.NewRestInvoker()
}
