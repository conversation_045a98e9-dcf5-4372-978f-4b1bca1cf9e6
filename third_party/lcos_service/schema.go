package lcos_service

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type EddInfo struct {
	EddMin     int64                   `json:"edd_min"`
	EddMax     int64                   `json:"edd_max"`
	EddProcess *edd_history.EddProcess `json:"edd_process"`

	DDL    int64   `json:"ddl"`
	DDLCdt float64 `json:"ddl_cdt"`

	CdtProductInfo     *cdt_calculation.CdtProductInfo
	EddCalculationInfo *lcos_protobuf.GetEddCalculationInfoResponse
}

func (e *EddInfo) GetEddMin() int64 {
	if e == nil {
		return 0
	}
	return e.EddMin
}

func (e *EddInfo) GetEddMax() int64 {
	if e == nil {
		return 0
	}
	return e.EddMax
}

func (e *EddInfo) GetDDL() int64 {
	if e == nil {
		return 0
	}
	return e.DDL
}

func (e *EddInfo) GetDDLCdt() float64 {
	if e == nil {
		return 0
	}
	return e.DDLCdt
}

func (e *EddInfo) GetEddProcess() *edd_history.EddProcess {
	if e == nil {
		return nil
	}
	return e.EddProcess
}

func (e *EddInfo) DDLAvailable() bool {
	if e == nil {
		return false
	}
	return e.EddCalculationInfo != nil
}

type GetExtendedEddInfoResponse struct {
	SlsTN                 string                             `json:"sls_tn"`
	ExtendedEddMin        int64                              `json:"extended_edd_min"`
	ExtendedEddMax        int64                              `json:"extended_edd_max"`
	EddMinExtendedDays    int                                `json:"edd_min_extended_days"`
	EddMaxExtendedDays    int                                `json:"edd_max_extended_days"`
	NonWorkingDaysProcess *edd_history.NonWorkingDaysProcess `json:"non_working_days_process"`
}
