package lcos_service

var account = "lcos-cdt-task"

// TODO: token暂时随机定义
var token = "test"

const (
	// lcos grpc path
	LcosCdtCalculationSchemaID       = "lcos_protobuf.LcosCdtCalculationService"
	GetCdtInfoForTrackingOperationID = "GetCdtInfoForTracking"
	GetEDDInfoOperationID            = "GetEddInfo"
	GetExtendedEDDOperationID        = "GetExtendedEDD"
	GetCdtInfoForProduct             = "BatchGetCdtInfo"
	GetEddCalculationInfo            = "GetEddCalculationInfo"
	GetEddInfoByAlgo                 = "GetEddInfoByAlgo"
	ListMChannelGreyConfigByRegion   = "ListMChannelGreyConfigByRegion"
)

const (
	LcosServiceableServiceSchemaID = "lcos_protobuf.LcosServiceableService"
	RegenerateOperationID          = "RegenerateMesh"
)
