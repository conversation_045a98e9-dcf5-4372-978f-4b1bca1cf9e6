package lcos_service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/golang/protobuf/proto"
	"strings"
	"time"
)

var (
	restInvoker       = chassis.NewRestInvoker()
	defaultTimeoutSec = 5
)

func (l *lcosService) Forward2MeshTabV1(ctx utils.LCOSContext, region, zoneId, version, layerId string, pipMode int32, cacheRefresh bool) error {
	req := &lcos_protobuf.RegenerateMeshRequest{
		ReqHeader:    genLcosReqHeader(ctx),
		Region:       proto.String(strings.ToLower(region)),
		ZoneId:       proto.String(zoneId),
		Version:      proto.String(version),
		PipMode:      proto.Int32(pipMode),
		RefreshCache: proto.Bool(cacheRefresh),
		LayerId:      proto.String(layerId),
	}
	resp := &lcos_protobuf.RegenerateMeshResponse{}
	host := config.GetMutableConf(ctx).EFenceConfig.ForwardToMeshTabV1Host
	url := fmt.Sprintf("%s/%s/%s", host, LcosServiceableServiceSchemaID, RegenerateOperationID)
	err := GrpcHttpPost(ctx, req, resp, url, restInvoker, getTimeout(ctx))
	if err != nil {
		return err
	}
	if resp.GetRespHeader() == nil || resp.GetRespHeader().GetRetcode() != 0 {
		return fmt.Errorf("forward to mesh tab v1 fail, resp[%+v]", resp)
	}

	return nil
}

func getTimeout(ctx utils.LCOSContext) time.Duration {
	timeout := config.GetMutableConf(ctx).EFenceConfig.ForwardToMeshTabV1Timeout
	if timeout == 0 {
		timeout = defaultTimeoutSec
	}

	return time.Duration(timeout) * time.Second
}
