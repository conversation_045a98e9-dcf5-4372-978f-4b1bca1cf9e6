package lcos_service

import (
	"context"
	"os"
	"path"
	"reflect"
	"strconv"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
	"git.garena.com/shopee/platform/service-governance/viewercontext/attr"
	jsoniter "github.com/json-iterator/go"
)

func Test_lcosService_GetEddInfo(t *testing.T) {
	ctx, _ := viewercontext.Start(context.Background(), attr.WithPFB("pfb-spln-26145"))
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	product := cdt_calculation.CdtProductInfo{
		QueryID:    "haobing.mu|get_edd_info",
		ProductID:  "90020",
		IsCB:       0,
		IsSiteLine: 1,
		Region:     "sg",
		SellerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(0),
			CityLocationId:     utils.NewInt(0),
			DistrictLocationId: utils.NewInt(0),
			PostalCode:         utils.NewString(""),
		},
		BuyerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(0),
			CityLocationId:     utils.NewInt(0),
			DistrictLocationId: utils.NewInt(0),
			PostalCode:         utils.NewString(""),
		},
	}
	svc := NewLCOSService("sg")
	eddInfo, err := svc.GetEDDInfo(utils.NewCommonCtx(ctx), &product, 4955471786374146276, 1666076814) // 查不到cdt: 8955471786374146276，查得到: 4955471786374146276
	if err != nil {
		t.Fatal("lcos service get edd info error:", err.Msg)
	}
	t.Log("get edd info process:", eddInfo.GetEddProcess())
	t.Log("edd:", eddInfo.GetEddMax())
}

func Test_lcosService_GetExtensionEDDInfo(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "br")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	type fields struct {
		serviceName string
	}
	type args struct {
		ctx                  utils.LCOSContext
		extensionEDDInfoList []*cdt_calculation.SingleExtendedEDDInfo
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[string]*cdt_calculation.SingleExtendedEDDResponse
		want1  *lcos_error.LCOSError
	}{
		{
			name: "test get extended edd",
			fields: fields{
				serviceName: "lcos-grpc",
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				extensionEDDInfoList: []*cdt_calculation.SingleExtendedEDDInfo{{
					UniqueID:        "1",
					ProductID:       "90008",
					StateLocationID: 0,
					Region:          "BR",
					Adjustment:      10,
					Edd:             1662979637,
					IsLM:            0,
					IsSiteLine:      1,
					LineIDList:      nil,
				}},
			},
			want:  nil,
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := NewLCOSService("BR")
			got, got1 := l.GetExtensionEDDInfo(tt.args.ctx, tt.args.extensionEDDInfoList)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetExtensionEDDInfo() got1 = %v, want %v", got1, tt.want1)
			} else {
				gotStr, _ := jsoniter.MarshalToString(got)
				t.Log(gotStr)
			}
		})
	}
}

func Test_lcosService_GetExtensionEDDInfoWithConcurrency(t *testing.T) {
	t.Skip()
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "br")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	var request []*cdt_calculation.SingleExtendedEDDInfo
	for i := 0; i < 10000; i++ {
		request = append(request, &cdt_calculation.SingleExtendedEDDInfo{
			UniqueID:        strconv.Itoa(i),
			ProductID:       "90008",
			StateLocationID: 0,
			Region:          "BR",
			Adjustment:      10,
			Edd:             1662979637,
			IsLM:            0,
			IsSiteLine:      1,
			LineIDList:      nil,
		})
	}

	type fields struct {
		serviceName string
	}
	type args struct {
		ctx                  utils.LCOSContext
		extensionEDDInfoList []*cdt_calculation.SingleExtendedEDDInfo
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[string]*cdt_calculation.SingleExtendedEDDResponse
		want1  *lcos_error.LCOSError
	}{
		{
			name: "test get extended edd with currency",
			fields: fields{
				serviceName: "lcos-grpc",
			},
			args: args{
				ctx:                  utils.NewCommonCtx(context.Background()),
				extensionEDDInfoList: request,
			},
			want:  nil,
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := NewLCOSService("BR")
			got, got1 := l.GetExtensionEDDInfoWithConcurrency(tt.args.ctx, tt.args.extensionEDDInfoList, 20, 100)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetExtensionEDDInfo() got1 = %v, want %v", got1, tt.want1)
			} else {
				//gotStr, _ := jsoniter.MarshalToString(got)
				t.Log(len(got))
			}
		})
	}
}

func Test_lcosService_GetEDDInfo(t *testing.T) {
	t.Skip()
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "uat")
	_ = os.Setenv("CID", "br")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	type fields struct {
		serviceName string
	}
	type args struct {
		ctx         utils.LCOSContext
		productInfo *cdt_calculation.CdtProductInfo
		updateTime  uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		{
			name:   "test get edd info with address not valid",
			fields: fields{serviceName: "lcos-grpc"},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				productInfo: &cdt_calculation.CdtProductInfo{
					QueryID:    "1",
					ProductID:  "90008",
					IsCB:       0,
					IsSiteLine: 1,
					Region:     "BR",
					SellerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(0),
						CityLocationId:     utils.NewInt(1231),
						DistrictLocationId: utils.NewInt(1232),
						PostalCode:         utils.NewString(""),
					},
					BuyerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(1231),
						CityLocationId:     utils.NewInt(0),
						DistrictLocationId: utils.NewInt(1232),
						PostalCode:         utils.NewString(""),
					},
				},
				updateTime: utils.GetTimestamp(ctx),
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := NewLCOSService("BR")
			eddInfo, err := l.GetEDDInfo(tt.args.ctx, tt.args.productInfo, 0, tt.args.updateTime)
			if err != tt.want {
				t.Errorf("GetEDDInfo() got = %v, want %v", err, tt.want)
			} else {
				t.Logf("edd: %s, process:%s", pickup.TransferTimeStampToTime(uint32(eddInfo.GetEddMax()), "BR"), eddInfo.GetEddProcess())
			}
		})
	}
}
