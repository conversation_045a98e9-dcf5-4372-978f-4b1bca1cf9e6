package lcos_service

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/location_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lpop_service"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/golang/protobuf/jsonpb"
	"github.com/golang/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"
)

func genLcosReqHeader(ctx utils.LCOSContext) *lcos_protobuf.ReqHeader {
	currentTime := utils.GetTimestamp(ctx)
	ip := utils.GetLocalIp()
	reqId := ctx.GetRequestId()
	lcosAccount := account
	lcosToken := token
	return &lcos_protobuf.ReqHeader{
		RequestId: &reqId,
		Account:   &lcosAccount,
		Token:     &lcosToken,
		Timestamp: &currentTime,
		CallerIp:  ip,
	}
}

func ConvertEddProcess(ctx utils.LCOSContext, eddProcess *lcos_protobuf.EDDProcess) *edd_history.EddProcess {
	if eddProcess == nil {
		return nil
	}
	returnEddProcess := &edd_history.EddProcess{}
	if cdtProcess := eddProcess.GetCdtProcess(); cdtProcess != nil {
		locationService := location_service.NewLocationService(ctx) // 每个服务可以查询所有region的地址数据

		// 填充leadtime process信息
		leadTimeProcess := &edd_history.LeadTimeProcess{}
		if cdtInfo := cdtProcess.GetAutoUpdateProcess(); cdtInfo != nil {
			leadTimeProcess.Type = 0
			leadTimeProcess.LeadTimeMin = cdtInfo.GetCdtMin()
			leadTimeProcess.LeadTimeMax = cdtInfo.GetCdtMax()
			leadTimeProcess.TaskId = cdtInfo.GetAutoUpdateRuleId()
			leadTimeProcess.TestGroupTag = cdtProcess.GetGroupTag()
		}
		if cdtInfo := cdtProcess.GetManualUpdateProcess(); cdtInfo != nil {
			leadTimeProcess.Type = 1
			leadTimeProcess.LeadTimeMin = cdtInfo.GetCdtMin()
			leadTimeProcess.LeadTimeMax = cdtInfo.GetCdtMax()
		}
		if cdtQuery := cdtProcess.GetCdtQueryInfo(); cdtQuery != nil {
			leadTimeProcess.OriginLocation = edd_history.LocationInfo{
				Level: int8(cdtQuery.GetOriginLocationLevel()),
			}
			originLocationId := cdtQuery.GetOriginLocationId()
			if originLocationId != 0 {
				location, err := locationService.GetLocationByLocationId(ctx, uint64(originLocationId))
				if err != nil || location == nil {
					leadTimeProcess.OriginLocation.Name = strconv.Itoa(int(originLocationId))
				} else {
					leadTimeProcess.OriginLocation.Name = location.GetName()
				}
			}

			leadTimeProcess.DestLocation = edd_history.LocationInfo{
				Level: int8(cdtQuery.GetDestinationLocationLevel()),
			}
			switch leadTimeProcess.DestLocation.Level {
			case constant.CDTPostcode:
				leadTimeProcess.DestLocation.Name = cdtQuery.GetDestinationPostcode()
			case constant.CepRange:
				leadTimeProcess.DestLocation.Name = strconv.Itoa(int(cdtQuery.GetDestinationCeprangePostcode()))
			default:
				destLocationId := cdtQuery.GetDestinationLocationId()
				if destLocationId != 0 {
					location, err := locationService.GetLocationByLocationId(ctx, uint64(destLocationId))
					if err != nil || location == nil {
						leadTimeProcess.DestLocation.Name = strconv.Itoa(int(destLocationId))
					} else {
						leadTimeProcess.DestLocation.Name = location.GetName()
					}
				}
			}
		}
		returnEddProcess.LeadTimeProcess = leadTimeProcess

		// 填充leadtime delta process信息
		var leadTimeDeltaProcess *edd_history.LeadTimeDeltaProcess
		if deltaInfo := cdtProcess.GetManualManipulationProcess(); deltaInfo != nil {
			leadTimeDeltaProcess = &edd_history.LeadTimeDeltaProcess{}
			leadTimeDeltaProcess.DeltaMin = deltaInfo.GetCdtMinDelta()
			leadTimeDeltaProcess.DeltaMax = deltaInfo.GetCdtMaxDelta()
			leadTimeDeltaProcess.TaskId = uint64(deltaInfo.GetRecordId())
		}
		if deltaQuery := cdtProcess.GetManualManipulationQueryInfo(); deltaQuery != nil && leadTimeDeltaProcess != nil {
			leadTimeDeltaProcess.OriginLocation = edd_history.LocationInfo{
				Level: int8(deltaQuery.GetOriginLocationLevel()),
			}
			originLocationId := deltaQuery.GetOriginLocationId()
			if originLocationId != 0 {
				location, err := locationService.GetLocationByLocationId(ctx, uint64(originLocationId))
				if err != nil {
					logger.CtxLogErrorf(ctx, "get location info error|location_id=%d, message=%s", originLocationId, err.Msg)
					leadTimeDeltaProcess.OriginLocation.Name = strconv.Itoa(int(originLocationId))
				} else {
					leadTimeDeltaProcess.OriginLocation.Name = location.GetName()
				}
			}

			leadTimeDeltaProcess.DestLocation = edd_history.LocationInfo{
				Level: int8(deltaQuery.GetDestinationLocationLevel()),
			}
			switch leadTimeDeltaProcess.DestLocation.Level {
			case constant.CDTPostcode:
				leadTimeDeltaProcess.DestLocation.Name = deltaQuery.GetDestinationPostcode()
			case constant.CepRange:
				leadTimeDeltaProcess.DestLocation.Name = strconv.Itoa(int(deltaQuery.GetDestinationCeprangePostcode()))
			default:
				destLocationId := deltaQuery.GetDestinationLocationId()
				if destLocationId != 0 {
					location, err := locationService.GetLocationByLocationId(ctx, uint64(destLocationId))
					if err != nil {
						logger.CtxLogErrorf(ctx, "get location info error|location_id=%d, message=%s", destLocationId, err.Msg)
						leadTimeDeltaProcess.OriginLocation.Name = strconv.Itoa(int(destLocationId))
					} else {
						leadTimeDeltaProcess.OriginLocation.Name = location.GetName()
					}
				}
			}
		}
		returnEddProcess.LeadTimeDeltaProcess = leadTimeDeltaProcess
	}

	// 填充nonworking days process信息
	if holidayProcess := eddProcess.GetNonWorkingDayProcess(); holidayProcess != nil {
		returnEddProcess.NonWorkingDaysProcess = convertNonWorkingDaysProcess(ctx, holidayProcess)
	}
	return returnEddProcess
}

func CheckEddMinAvailableWithGreyConfig(ctx context.Context, productId, laneCode string, city int) bool {
	greyEnabled, greyBuyerCityList := config.GetEddRangeGreyConfig(ctx, productId, laneCode)
	if !greyEnabled {
		return false
	}
	if len(greyBuyerCityList) == 0 {
		// 开启灰度，但不限制灰度城市
		return true
	}
	return utils.CheckInInt(city, greyBuyerCityList)
}

func convertNonWorkingDaysProcess(ctx utils.LCOSContext, holidayProcess *lcos_protobuf.NonWorkingDayProcess) *edd_history.NonWorkingDaysProcess {
	lineIdList := holidayProcess.GetLineIdList()
	nonWorkingDaysProcess := &edd_history.NonWorkingDaysProcess{
		EddMinNwdList: holidayProcess.GetEddMinNwdList(),
		EddMaxNwdList: holidayProcess.GetEddMaxNwdList(),
		Supplier:      make([]edd_history.SupplierInfo, 0, len(lineIdList)),
	}
	supplierInfoMap, _ := lpop_service.NewLpopService().GetSupplierInfoByLineId(ctx, lineIdList)
	for _, lineId := range lineIdList {
		supplier := edd_history.SupplierInfo{
			LineId: lineId,
		}
		if supplierInfo, ok := supplierInfoMap[lineId]; ok {
			supplier.SupplierId = supplierInfo.GetSupplierId()
			supplier.SupplierName = supplierInfo.GetSupplierName()
		}
		nonWorkingDaysProcess.Supplier = append(nonWorkingDaysProcess.Supplier, supplier)
	}
	return nonWorkingDaysProcess
}

func HandleEDDProcessAndEDDMin(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, process *lcos_protobuf.EDDProcess, eddMax, eddMin, ddl int64, ddlCdt float64) *EddInfo {
	eddProcess := ConvertEddProcess(ctx, process)
	eddInfo := &EddInfo{
		EddMax:     eddMax,
		EddProcess: eddProcess,
		DDL:        ddl,
		DDLCdt:     ddlCdt,
	}
	if CheckEddMinAvailableWithGreyConfig(ctx, productInfo.ProductID, productInfo.LaneCode, productInfo.BuyerAddr.GetCityLocationId()) {
		eddInfo.EddMin = eddMin
	} else {
		logger.CtxLogErrorf(ctx, "edd min unavailable due to grey config|product_id=%s, lane_code=%s, city=%d", productInfo.ProductID, productInfo.LaneCode, productInfo.BuyerAddr.GetCityLocationId())
	}
	return eddInfo
}

func GrpcHttpPost(ctx utils.LCOSContext, req interface{}, resp interface{}, url string, invoker *chassis.RestInvoker, timeout time.Duration) error {
	b, err := jsoniter.Marshal(req)
	if err != nil {
		return err
	}

	httpReq, err := chassis.NewRestRequest(http.MethodPost, url, b)
	if err != nil {
		return err
	}

	httpReq.Header.Add("Content-Type", "application/json")
	httpReq.Header.Add("request-id", ctx.GetRequestId())
	httpReq.Header.Add("X-Request-Id", ctx.GetRequestId())

	httpCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	response, err := HttpRequest(httpCtx, invoker, httpReq)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	var body []byte
	body, err = ioutil.ReadAll(response.Body)
	if err != nil {
		logger.CtxLogErrorf(ctx, "send http jwt request failed")
		return err
	}

	if response.StatusCode != http.StatusOK {
		return fmt.Errorf("status code is not 2xx, but %d, response is %s", response.StatusCode, string(body))
	}

	readerBuff := bytes.NewBuffer(body)

	respMessage, ok := resp.(proto.Message)
	if !ok {
		return fmt.Errorf("convert resp err, %+v", resp)
	}

	unmarshaler := jsonpb.Unmarshaler{}
	unmarshaler.AllowUnknownFields = true // ignore unknown param

	err = unmarshaler.Unmarshal(readerBuff, respMessage)
	if err != nil {
		return err
	}

	return nil
}

func HttpRequest(ctx context.Context, invoker *chassis.RestInvoker, req *http.Request) (*http.Response, error) {
	response, err := invoker.Invoke(ctx, req, chassis.WithoutServiceDiscovery())
	if err != nil {
		return nil, err
	}
	return response, err
}
