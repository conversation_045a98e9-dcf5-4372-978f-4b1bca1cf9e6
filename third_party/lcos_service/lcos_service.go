package lcos_service

import (
	"context"
	"strconv"
	"strings"
	"sync"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/location_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lpop_service"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	jsoniter "github.com/json-iterator/go"
)

var invoker = chassis.NewRPCInvoker()

type lcosService struct {
	serviceName string
}

func NewLCOSService(ctx context.Context, region string) *lcosService {
	lcosServiceName := cf.GetTimeServiceName(ctx)
	serviceName := utils.GenKey("-", lcosServiceName, strings.ToLower(region))
	return &lcosService{serviceName: serviceName}
}

func validCdtAddress(address *lcos_protobuf.AddressInfo) {
	if address.GetDistrictLocationId() != 0 {
		if address.GetStateLocationId() == 0 || address.GetCityLocationId() == 0 {
			address.DistrictLocationId = utils.NewInt32(0)
		}
	}
	if address.GetCityLocationId() != 0 {
		if address.GetStateLocationId() == 0 {
			address.CityLocationId = utils.NewInt32(0)
		}
	}
}

func (l *lcosService) convertNonWorkingDaysProcess(ctx utils.LCOSContext, holidayProcess *lcos_protobuf.NonWorkingDayProcess) *edd_history.NonWorkingDaysProcess {
	lineIdList := holidayProcess.GetLineIdList()
	nonWorkingDaysProcess := &edd_history.NonWorkingDaysProcess{
		EddMinNwdList: holidayProcess.GetEddMinNwdList(),
		EddMaxNwdList: holidayProcess.GetEddMaxNwdList(),
		Supplier:      make([]edd_history.SupplierInfo, 0, len(lineIdList)),
	}
	supplierInfoMap, _ := lpop_service.NewLpopService().GetSupplierInfoByLineId(ctx, lineIdList)
	for _, lineId := range lineIdList {
		supplier := edd_history.SupplierInfo{
			LineId: lineId,
		}
		if supplierInfo, ok := supplierInfoMap[lineId]; ok {
			supplier.SupplierId = supplierInfo.GetSupplierId()
			supplier.SupplierName = supplierInfo.GetSupplierName()
		}
		nonWorkingDaysProcess.Supplier = append(nonWorkingDaysProcess.Supplier, supplier)
	}
	return nonWorkingDaysProcess
}

func (l *lcosService) convertEddProcess(ctx utils.LCOSContext, eddProcess *lcos_protobuf.EDDProcess) *edd_history.EddProcess {
	if eddProcess == nil {
		return nil
	}
	returnEddProcess := &edd_history.EddProcess{}
	if cdtProcess := eddProcess.GetCdtProcess(); cdtProcess != nil {
		locationService := location_service.NewLocationService(ctx) // 每个服务可以查询所有region的地址数据

		// 填充leadtime process信息
		leadTimeProcess := &edd_history.LeadTimeProcess{}
		if cdtInfo := cdtProcess.GetAutoUpdateProcess(); cdtInfo != nil {
			leadTimeProcess.Type = 0
			leadTimeProcess.LeadTimeMin = cdtInfo.GetCdtMin()
			leadTimeProcess.LeadTimeMax = cdtInfo.GetCdtMax()
			leadTimeProcess.TaskId = cdtInfo.GetAutoUpdateRuleId()
		}
		if cdtInfo := cdtProcess.GetManualUpdateProcess(); cdtInfo != nil {
			leadTimeProcess.Type = 1
			leadTimeProcess.LeadTimeMin = cdtInfo.GetCdtMin()
			leadTimeProcess.LeadTimeMax = cdtInfo.GetCdtMax()
		}
		if cdtQuery := cdtProcess.GetCdtQueryInfo(); cdtQuery != nil {
			leadTimeProcess.OriginLocation = edd_history.LocationInfo{
				Level: int8(cdtQuery.GetOriginLocationLevel()),
			}
			originLocationId := cdtQuery.GetOriginLocationId()
			if originLocationId != 0 {
				location, err := locationService.GetLocationByLocationId(ctx, uint64(originLocationId))
				if err != nil || location == nil {
					leadTimeProcess.OriginLocation.Name = strconv.Itoa(int(originLocationId))
				} else {
					leadTimeProcess.OriginLocation.Name = location.GetName()
				}
			}

			leadTimeProcess.DestLocation = edd_history.LocationInfo{
				Level: int8(cdtQuery.GetDestinationLocationLevel()),
			}
			switch leadTimeProcess.DestLocation.Level {
			case constant.CDTPostcode:
				leadTimeProcess.DestLocation.Name = cdtQuery.GetDestinationPostcode()
			case constant.CepRange:
				leadTimeProcess.DestLocation.Name = strconv.Itoa(int(cdtQuery.GetDestinationCeprangePostcode()))
			default:
				destLocationId := cdtQuery.GetDestinationLocationId()
				if destLocationId != 0 {
					location, err := locationService.GetLocationByLocationId(ctx, uint64(destLocationId))
					if err != nil || location == nil {
						leadTimeProcess.DestLocation.Name = strconv.Itoa(int(destLocationId))
					} else {
						leadTimeProcess.DestLocation.Name = location.GetName()
					}
				}
			}
		}
		returnEddProcess.LeadTimeProcess = leadTimeProcess

		// 填充leadtime delta process信息
		var leadTimeDeltaProcess *edd_history.LeadTimeDeltaProcess
		if deltaInfo := cdtProcess.GetManualManipulationProcess(); deltaInfo != nil {
			leadTimeDeltaProcess = &edd_history.LeadTimeDeltaProcess{}
			leadTimeDeltaProcess.DeltaMin = deltaInfo.GetCdtMinDelta()
			leadTimeDeltaProcess.DeltaMax = deltaInfo.GetCdtMaxDelta()
			leadTimeDeltaProcess.TaskId = uint64(deltaInfo.GetRecordId())
		}
		if deltaQuery := cdtProcess.GetManualManipulationQueryInfo(); deltaQuery != nil && leadTimeDeltaProcess != nil {
			leadTimeDeltaProcess.OriginLocation = edd_history.LocationInfo{
				Level: int8(deltaQuery.GetOriginLocationLevel()),
			}
			originLocationId := deltaQuery.GetOriginLocationId()
			if originLocationId != 0 {
				location, err := locationService.GetLocationByLocationId(ctx, uint64(originLocationId))
				if err != nil {
					logger.CtxLogErrorf(ctx, "get location info error|location_id=%d, message=%s", originLocationId, err.Msg)
					leadTimeDeltaProcess.OriginLocation.Name = strconv.Itoa(int(originLocationId))
				} else {
					leadTimeDeltaProcess.OriginLocation.Name = location.GetName()
				}
			}

			leadTimeDeltaProcess.DestLocation = edd_history.LocationInfo{
				Level: int8(deltaQuery.GetDestinationLocationLevel()),
			}
			switch leadTimeDeltaProcess.DestLocation.Level {
			case constant.CDTPostcode:
				leadTimeDeltaProcess.DestLocation.Name = deltaQuery.GetDestinationPostcode()
			case constant.CepRange:
				leadTimeDeltaProcess.DestLocation.Name = strconv.Itoa(int(deltaQuery.GetDestinationCeprangePostcode()))
			default:
				destLocationId := deltaQuery.GetDestinationLocationId()
				if destLocationId != 0 {
					location, err := locationService.GetLocationByLocationId(ctx, uint64(destLocationId))
					if err != nil {
						logger.CtxLogErrorf(ctx, "get location info error|location_id=%d, message=%s", destLocationId, err.Msg)
						leadTimeDeltaProcess.OriginLocation.Name = strconv.Itoa(int(destLocationId))
					} else {
						leadTimeDeltaProcess.OriginLocation.Name = location.GetName()
					}
				}
			}
		}
		returnEddProcess.LeadTimeDeltaProcess = leadTimeDeltaProcess
	}

	// 填充nonworking days process信息
	if holidayProcess := eddProcess.GetNonWorkingDayProcess(); holidayProcess != nil {
		returnEddProcess.NonWorkingDaysProcess = l.convertNonWorkingDaysProcess(ctx, holidayProcess)
	}
	return returnEddProcess
}

func (l *lcosService) GetEDDInfo(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, forderId uint64, updateTime uint32) (*EddInfo, *lcos_error.LCOSError) {
	sellerAddress := &lcos_protobuf.AddressInfo{
		StateLocationId:    utils.NewInt32(int32(*productInfo.SellerAddr.StateLocationId)),
		CityLocationId:     utils.NewInt32(int32(*productInfo.SellerAddr.CityLocationId)),
		DistrictLocationId: utils.NewInt32(int32(*productInfo.SellerAddr.DistrictLocationId)),
		Postcode:           utils.NewString(*productInfo.SellerAddr.PostalCode),
	}
	// for cb order, need to set seller address as 0
	validCdtAddress(sellerAddress)

	buyerAddress := &lcos_protobuf.AddressInfo{
		StateLocationId:    utils.NewInt32(int32(*productInfo.BuyerAddr.StateLocationId)),
		CityLocationId:     utils.NewInt32(int32(*productInfo.BuyerAddr.CityLocationId)),
		DistrictLocationId: utils.NewInt32(int32(*productInfo.BuyerAddr.DistrictLocationId)),
		Postcode:           utils.NewString(*productInfo.BuyerAddr.PostalCode),
	}
	validCdtAddress(buyerAddress)

	// use product info to generate struct
	lineList := make([]*lcos_protobuf.SimpleLineInfo, 0, len(productInfo.LineList))
	for _, singleLine := range productInfo.LineList {
		lineList = append(lineList, &lcos_protobuf.SimpleLineInfo{
			LineId:      utils.NewString(singleLine.LineID),
			LineSubType: utils.NewUint32(singleLine.SubType),
		})
	}
	lcosProductInfo := &lcos_protobuf.ProductInfo{
		QueryId:    utils.NewString(productInfo.QueryID),
		ProductId:  utils.NewString(productInfo.ProductID),
		IsCb:       utils.NewInt32(int32(productInfo.IsCB)),
		IsSiteLine: utils.NewInt32(int32(productInfo.IsSiteLine)),
		IsLm:       utils.NewInt32(0),
		Region:     utils.NewString(productInfo.Region),
		SellerAddr: sellerAddress,
		BuyerAddr:  buyerAddress,
		LaneCode:   utils.NewString(productInfo.LaneCode),
		LineList:   lineList,

		UpdateEvent: utils.NewUint32(uint32(productInfo.UpdateEvent)),
		BuyerId:     utils.NewUint64(productInfo.BuyerId),
	}

	req := &lcos_protobuf.QueryCdtInfoForTrackingRequest{
		ReqHeader:       genLcosReqHeader(ctx),
		ProductInfo:     lcosProductInfo,
		ForderId:        utils.NewUint64(forderId),
		CbLmInboundDate: utils.NewUint32(updateTime),
	}
	rsp := &lcos_protobuf.GetEddInfoResponse{}

	reqStr, _ := jsoniter.MarshalToString(req)

	if err := invoker.Invoke(ctx, l.serviceName, LcosCdtCalculationSchemaID, GetEDDInfoOperationID, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEDDInfoOperationID, reqStr, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetRespHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEDDInfoOperationID, reqStr, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 && !utils.InInt32Slice(rsp.GetRespHeader().GetRetcode(), []int32{lcos_error.NotFoundCdtInfoErrorCode, lcos_error.NotFoundManualUpdateDataErrorCode}) {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|retcode=%d|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEDDInfoOperationID, reqStr, rsp.GetRespHeader().GetRetcode(), rsp.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetRespHeader().GetMessage())
	}

	rspStr, _ := jsoniter.MarshalToString(rsp)
	logger.CtxLogInfof(ctx, "requesting [%s] success|schema_id=%s|api=%s|req=[%s]|req=[%s]", l.serviceName, LcosCdtCalculationSchemaID, GetEDDInfoOperationID, reqStr, rspStr)

	return HandleEDDProcessAndEDDMin(ctx, productInfo, rsp.GetEddProcess(), rsp.GetEdd(), rsp.GetEddMin(), 0, 0), nil
}

func (l *lcosService) GetEDDInfoWithConcurrency(ctx utils.LCOSContext, eddInfoList []*cdt_calculation.SingleEDDRequest, maxCurrency int) (map[string]*EddInfo, *lcos_error.LCOSError) {
	resultMap := make(map[string]*EddInfo)
	logger.CtxLogInfof(ctx, "ready to request edd info with max_currency:[%d]", maxCurrency)

	// request lcos with currency
	maxCurrencyChan := make(chan struct{}, maxCurrency)
	var eddWaitGroup sync.WaitGroup
	var eddLock sync.Mutex
	for _, singleBatch := range eddInfoList {
		maxCurrencyChan <- struct{}{}
		eddWaitGroup.Add(1)
		go func(singleEDD *cdt_calculation.SingleEDDRequest) {
			eddInfo, lcosErr := l.GetEDDInfo(ctx, singleEDD.ProductInfo, singleEDD.ForderID, singleEDD.UpdateTime)
			if lcosErr == nil && eddInfo.GetEddMax() != 0 {
				// append data to result map
				eddLock.Lock()
				resultMap[singleEDD.SlsTN] = eddInfo
				eddLock.Unlock()
			}
			<-maxCurrencyChan
			eddWaitGroup.Done()
		}(singleBatch)
	}
	eddWaitGroup.Wait()
	return resultMap, nil
}

func (l *lcosService) GetExtensionEDDInfo(ctx utils.LCOSContext, extensionEDDInfoList []*cdt_calculation.SingleExtendedEDDInfo) (map[string]*GetExtendedEddInfoResponse, *lcos_error.LCOSError) {

	pbRequest := make([]*lcos_protobuf.SingleEDDExtension, 0, len(extensionEDDInfoList))
	for _, extensionEDDInfo := range extensionEDDInfoList {
		singlePbRequest := &lcos_protobuf.SingleEDDExtension{
			UniqueId:        utils.NewString(extensionEDDInfo.UniqueID),
			ProductId:       utils.NewString(extensionEDDInfo.ProductID),
			StateLocationId: utils.NewInt32(int32(extensionEDDInfo.StateLocationID)),
			Region:          utils.NewString(extensionEDDInfo.Region),
			Adjustment:      utils.NewInt32(int32(extensionEDDInfo.Adjustment)),
			Edd:             utils.NewInt64(extensionEDDInfo.Edd),
			IsLm:            utils.NewInt32(int32(extensionEDDInfo.IsLM)),
			IsSitLine:       utils.NewInt32(int32(extensionEDDInfo.IsSiteLine)),

			EddMinAdjustment: utils.NewInt32(int32(extensionEDDInfo.EddMinAdjustment)),
			EddMin:           utils.NewInt64(extensionEDDInfo.EddMin),
		}
		lineInfoList := make([]*lcos_protobuf.SimpleLineInfo, 0, len(extensionEDDInfo.LineIDList))
		for _, tmpLineInfo := range extensionEDDInfo.LineIDList {
			lineInfoList = append(lineInfoList, &lcos_protobuf.SimpleLineInfo{
				LineId:      utils.NewString(tmpLineInfo.LineID),
				LineSubType: utils.NewUint32(tmpLineInfo.SubType),
			})
		}
		singlePbRequest.LineIdList = lineInfoList
		pbRequest = append(pbRequest, singlePbRequest)
	}

	req := &lcos_protobuf.GetExtendedEDDRequest{
		ReqHeader:        genLcosReqHeader(ctx),
		EddExtensionList: pbRequest,
	}

	rsp := &lcos_protobuf.GetExtendedEDDResponse{}

	reqStr, _ := jsoniter.MarshalToString(req)

	if err := invoker.Invoke(ctx, l.serviceName, LcosCdtCalculationSchemaID, GetExtendedEDDOperationID, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetExtendedEDDOperationID, reqStr, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetRespHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetExtendedEDDOperationID, reqStr, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|retcode=%d|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetExtendedEDDOperationID, reqStr, rsp.GetRespHeader().GetRetcode(), rsp.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetRespHeader().GetMessage())
	}

	rspStr, _ := jsoniter.MarshalToString(rsp)
	logger.CtxLogInfof(ctx, "requesting [%s] success|schema_id=%s|api=%s|req=[%s]|req=[%s]", l.serviceName, LcosCdtCalculationSchemaID, GetExtendedEDDOperationID, reqStr, rspStr)

	returnedResult := make(map[string]*GetExtendedEddInfoResponse, len(rsp.GetExtendedEddResponseMap()))
	for key, value := range rsp.GetExtendedEddResponseMap() {
		returnedResult[key] = &GetExtendedEddInfoResponse{
			SlsTN:                 value.GetSlsTn(),
			ExtendedEddMin:        value.GetExtendedEddMin(),
			ExtendedEddMax:        value.GetExtendedEdd(),
			EddMinExtendedDays:    int(value.GetEddMinExtendedDays()),
			EddMaxExtendedDays:    int(value.GetExtendedDays()),
			NonWorkingDaysProcess: convertNonWorkingDaysProcess(ctx, value.GetNonWorkingDayProcess()),
		}
	}
	return returnedResult, nil
}

func (l *lcosService) GetExtensionEDDInfoWithConcurrency(ctx utils.LCOSContext, extensionEDDInfoList []*cdt_calculation.SingleExtendedEDDInfo, maxCurrency, batchSize int) (map[string]*GetExtendedEddInfoResponse, *lcos_error.LCOSError) {
	resultMap := make(map[string]*GetExtendedEddInfoResponse)
	logger.CtxLogInfof(ctx, "ready to request extended edd info with max_currency:[%d] batch_size:[%d]", maxCurrency, batchSize)

	// split into batch
	var extensionEDDChunks [][]*cdt_calculation.SingleExtendedEDDInfo
	for i := 0; i < len(extensionEDDInfoList); i += batchSize {
		end := i + batchSize
		if end > len(extensionEDDInfoList) {
			end = len(extensionEDDInfoList)
		}
		extensionEDDChunks = append(extensionEDDChunks, extensionEDDInfoList[i:end])
	}

	// request lcos with currency
	maxCurrencyChan := make(chan struct{}, maxCurrency)
	var eddWaitGroup sync.WaitGroup
	var eddLock sync.Mutex
	for _, singleBatch := range extensionEDDChunks {
		maxCurrencyChan <- struct{}{}
		eddWaitGroup.Add(1)
		go func(batches []*cdt_calculation.SingleExtendedEDDInfo) {
			responseMap, lcosErr := l.GetExtensionEDDInfo(ctx, batches)
			if lcosErr == nil {
				// append data to result map
				eddLock.Lock()
				for key, value := range responseMap {
					resultMap[key] = value
				}
				eddLock.Unlock()
			}
			<-maxCurrencyChan
			eddWaitGroup.Done()
		}(singleBatch)
	}
	eddWaitGroup.Wait()
	return resultMap, nil
}

func (l *lcosService) BatchGetCdtInfo(ctx utils.LCOSContext, productInfos []*cdt_calculation.CdtProductInfo, objectType uint8) (map[string]*cdt_calculation.CdtInfoForProduct, *lcos_error.LCOSError) {
	lcosProtobufInfos := make([]*lcos_protobuf.ProductInfo, 0)

	for _, productInfo := range productInfos {
		lcosProductInfo := &lcos_protobuf.ProductInfo{
			QueryId:    utils.NewString(productInfo.QueryID),
			ProductId:  utils.NewString(productInfo.ProductID),
			IsCb:       utils.NewInt32(int32(productInfo.IsCB)),
			IsSiteLine: utils.NewInt32(int32(productInfo.IsSiteLine)),
			IsLm:       utils.NewInt32(0),
			Region:     utils.NewString(productInfo.Region),
			SellerAddr: &lcos_protobuf.AddressInfo{
				StateLocationId:    utils.NewInt32(int32(productInfo.SellerAddr.GetStateLocationId())),
				CityLocationId:     utils.NewInt32(int32(productInfo.SellerAddr.GetCityLocationId())),
				DistrictLocationId: utils.NewInt32(int32(productInfo.SellerAddr.GetDistrictLocationId())),
				Postcode:           productInfo.SellerAddr.PostalCode,
			},
			BuyerAddr: &lcos_protobuf.AddressInfo{
				StateLocationId:    utils.NewInt32(int32(productInfo.BuyerAddr.GetStateLocationId())),
				CityLocationId:     utils.NewInt32(int32(productInfo.BuyerAddr.GetCityLocationId())),
				DistrictLocationId: utils.NewInt32(int32(productInfo.BuyerAddr.GetDistrictLocationId())),
				Postcode:           productInfo.BuyerAddr.PostalCode,
			},
		}
		lcosProtobufInfos = append(lcosProtobufInfos, lcosProductInfo)
	}

	req := &lcos_protobuf.QueryCdtInfoRequest{
		ReqHeader:   genLcosReqHeader(ctx),
		ProductInfo: lcosProtobufInfos,
		ObjectType:  utils.NewUint32(uint32(objectType)),
	}

	rsp := &lcos_protobuf.QueryCdtInfoRequestResponse{}

	reqStr, _ := jsoniter.MarshalToString(req)

	if err := invoker.Invoke(ctx, l.serviceName, LcosCdtCalculationSchemaID, GetCdtInfoForProduct, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetCdtInfoForProduct, reqStr, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetRespHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEDDInfoOperationID, reqStr, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|retcode=%d|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEDDInfoOperationID, reqStr, rsp.GetRespHeader().GetRetcode(), rsp.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetRespHeader().GetMessage())
	}

	rspStr, _ := jsoniter.MarshalToString(rsp)
	logger.CtxLogInfof(ctx, "requesting [%s] success|schema_id=%s|api=%s|req=[%s]|req=[%s]", l.serviceName, LcosCdtCalculationSchemaID, GetEDDInfoOperationID, reqStr, rspStr)

	if len(rsp.CdtReplyList) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response body is nil")
	}
	result := make(map[string]*cdt_calculation.CdtInfoForProduct, 0)
	for _, item := range rsp.CdtReplyList {
		if item.Error == nil || *item.Error.Retcode == 0 {
			val := &cdt_calculation.CdtInfoForProduct{
				QueryId: item.QueryId,
				CdtMax:  item.CdtMax,
				CdtMin:  item.CdtMin,
				CbLmMax: item.CbLmMax,
			}
			result[*item.QueryId] = val
		}

	}
	return result, nil
}

func (l *lcosService) GetEddCalculationInfo(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, forderId uint64, updateTime uint32) (*lcos_protobuf.GetEddCalculationInfoResponse, *lcos_error.LCOSError) {
	sellerAddress := &lcos_protobuf.AddressInfo{
		StateLocationId:    utils.NewInt32(int32(*productInfo.SellerAddr.StateLocationId)),
		CityLocationId:     utils.NewInt32(int32(*productInfo.SellerAddr.CityLocationId)),
		DistrictLocationId: utils.NewInt32(int32(*productInfo.SellerAddr.DistrictLocationId)),
		Postcode:           utils.NewString(*productInfo.SellerAddr.PostalCode),
	}
	// for cb order, need to set seller address as 0
	validCdtAddress(sellerAddress)

	buyerAddress := &lcos_protobuf.AddressInfo{
		StateLocationId:    utils.NewInt32(int32(*productInfo.BuyerAddr.StateLocationId)),
		CityLocationId:     utils.NewInt32(int32(*productInfo.BuyerAddr.CityLocationId)),
		DistrictLocationId: utils.NewInt32(int32(*productInfo.BuyerAddr.DistrictLocationId)),
		Postcode:           utils.NewString(*productInfo.BuyerAddr.PostalCode),
	}
	validCdtAddress(buyerAddress)

	// use product info to generate struct
	lineList := make([]*lcos_protobuf.SimpleLineInfo, 0, len(productInfo.LineList))
	lineStringList := make([]string, 0, len(productInfo.LineList))
	for _, singleLine := range productInfo.LineList {
		lineList = append(lineList, &lcos_protobuf.SimpleLineInfo{
			LineId:      utils.NewString(singleLine.LineID),
			LineSubType: utils.NewUint32(singleLine.SubType),
		})
		lineStringList = append(lineStringList, singleLine.LineID)
	}
	lcosProductInfo := &lcos_protobuf.ProductInfo{
		QueryId:     utils.NewString(productInfo.QueryID),
		ProductId:   utils.NewString(productInfo.ProductID),
		IsCb:        utils.NewInt32(int32(productInfo.IsCB)),
		IsSiteLine:  utils.NewInt32(int32(productInfo.IsSiteLine)),
		IsLm:        utils.NewInt32(0),
		Region:      utils.NewString(productInfo.Region),
		SellerAddr:  sellerAddress,
		BuyerAddr:   buyerAddress,
		LaneCode:    utils.NewString(productInfo.LaneCode),
		LineList:    lineList,
		NextEvent:   utils.NewUint32(uint32(productInfo.NextEvent)),
		UpdateEvent: utils.NewUint32(uint32(productInfo.UpdateEvent)),
		BuyerId:     utils.NewUint64(productInfo.BuyerId),
	}

	req := &lcos_protobuf.GetEddCalculationInfoRequest{
		ReqHeader:   genLcosReqHeader(ctx),
		ProductInfo: lcosProductInfo,
		ForderId:    utils.NewUint64(forderId),
		EventTime:   utils.NewUint32(updateTime),
	}
	rsp := &lcos_protobuf.GetEddCalculationInfoResponse{}

	reqStr, _ := jsoniter.MarshalToString(req)

	if err := invoker.Invoke(ctx, l.serviceName, LcosCdtCalculationSchemaID, GetEddCalculationInfo, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEddCalculationInfo, reqStr, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetRespHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEddCalculationInfo, reqStr, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 && !utils.InInt32Slice(rsp.GetRespHeader().GetRetcode(), []int32{lcos_error.NotFoundCdtInfoErrorCode, lcos_error.NotFoundManualUpdateDataErrorCode}) {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|retcode=%d|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEddCalculationInfo, reqStr, rsp.GetRespHeader().GetRetcode(), rsp.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, rsp.GetRespHeader().GetMessage())
	}

	// fill line list to edd process
	if rsp.EddProcess != nil && rsp.EddProcess.NonWorkingDayProcess != nil {
		rsp.EddProcess.NonWorkingDayProcess.LineIdList = lineStringList
	}

	rspStr, _ := jsoniter.MarshalToString(rsp)
	logger.CtxLogInfof(ctx, "requesting [%s] success|schema_id=%s|api=%s|req=[%s]|req=[%s]", l.serviceName, LcosCdtCalculationSchemaID, GetEddCalculationInfo, reqStr, rspStr)

	return rsp, nil
}

func (l *lcosService) GetEddInfoByAlgo(ctx utils.LCOSContext, productInfo *cdt_calculation.CdtProductInfo, slsTn string, updateTime uint32, trackingCode string) (*lcos_protobuf.GetEddInfoByAlgoResponse, *lcos_error.LCOSError) {
	sellerAddress := &lcos_protobuf.AddressInfo{
		StateLocationId:    utils.NewInt32(int32(*productInfo.SellerAddr.StateLocationId)),
		CityLocationId:     utils.NewInt32(int32(*productInfo.SellerAddr.CityLocationId)),
		DistrictLocationId: utils.NewInt32(int32(*productInfo.SellerAddr.DistrictLocationId)),
		Postcode:           utils.NewString(*productInfo.SellerAddr.PostalCode),
	}
	// for cb order, need to set seller address as 0
	validCdtAddress(sellerAddress)

	buyerAddress := &lcos_protobuf.AddressInfo{
		StateLocationId:    utils.NewInt32(int32(*productInfo.BuyerAddr.StateLocationId)),
		CityLocationId:     utils.NewInt32(int32(*productInfo.BuyerAddr.CityLocationId)),
		DistrictLocationId: utils.NewInt32(int32(*productInfo.BuyerAddr.DistrictLocationId)),
		Postcode:           utils.NewString(*productInfo.BuyerAddr.PostalCode),
	}
	validCdtAddress(buyerAddress)

	// use product info to generate struct
	lineList := make([]*lcos_protobuf.SimpleLineInfo, 0, len(productInfo.LineList))
	lineStringList := make([]string, 0, len(productInfo.LineList))
	for _, singleLine := range productInfo.LineList {
		lineList = append(lineList, &lcos_protobuf.SimpleLineInfo{
			LineId:      utils.NewString(singleLine.LineID),
			LineSubType: utils.NewUint32(singleLine.SubType),
		})
		lineStringList = append(lineStringList, singleLine.LineID)
	}
	lcosProductInfo := &lcos_protobuf.ProductInfo{
		QueryId:     utils.NewString(productInfo.QueryID),
		ProductId:   utils.NewString(productInfo.ProductID),
		IsCb:        utils.NewInt32(int32(productInfo.IsCB)),
		IsSiteLine:  utils.NewInt32(int32(productInfo.IsSiteLine)),
		IsLm:        utils.NewInt32(0),
		Region:      utils.NewString(productInfo.Region),
		SellerAddr:  sellerAddress,
		BuyerAddr:   buyerAddress,
		LaneCode:    utils.NewString(productInfo.LaneCode),
		LineList:    lineList,
		NextEvent:   utils.NewUint32(uint32(productInfo.NextEvent)),
		UpdateEvent: utils.NewUint32(uint32(productInfo.UpdateEvent)),
		BuyerId:     utils.NewUint64(productInfo.BuyerId),
	}

	req := &lcos_protobuf.GetEddInfoByAlgoRequest{
		ReqHeader:         genLcosReqHeader(ctx),
		ProductInfo:       lcosProductInfo,
		SlsTn:             &slsTn,
		EventTime:         utils.NewUint32(updateTime),
		EventTrackingCode: &trackingCode,
	}
	rsp := &lcos_protobuf.GetEddInfoByAlgoResponse{}

	reqStr, _ := jsoniter.MarshalToString(req)

	if err := invoker.Invoke(ctx, l.serviceName, LcosCdtCalculationSchemaID, GetEddInfoByAlgo, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEddInfoByAlgo, reqStr, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetRespHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEddInfoByAlgo, reqStr, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 && !utils.InInt32Slice(rsp.GetRespHeader().GetRetcode(), []int32{lcos_error.NotFoundCdtInfoErrorCode, lcos_error.NotFoundManualUpdateDataErrorCode}) {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|retcode=%d|error=%v", l.serviceName, LcosCdtCalculationSchemaID, GetEddInfoByAlgo, reqStr, rsp.GetRespHeader().GetRetcode(), rsp.GetRespHeader().GetMessage())
		return nil, lcos_error.NewLCOSError(rsp.GetRespHeader().GetRetcode(), rsp.GetRespHeader().GetMessage())
	}

	// fill line list to edd process
	if rsp.EddProcess != nil && rsp.EddProcess.NonWorkingDayProcess != nil {
		rsp.EddProcess.NonWorkingDayProcess.LineIdList = lineStringList
	}

	rspStr, _ := jsoniter.MarshalToString(rsp)
	logger.CtxLogInfof(ctx, "requesting [%s] success|schema_id=%s|api=%s|req=[%s]|req=[%s]", l.serviceName, LcosCdtCalculationSchemaID, GetEddInfoByAlgo, reqStr, rspStr)

	return rsp, nil
}

func (l *lcosService) ListMChannelGreyConfigByRegion(ctx utils.LCOSContext, region string) (*lcos_protobuf.MChannelGreyConfigByRegionResponse, *lcos_error.LCOSError) {
	rsp := &lcos_protobuf.MChannelGreyConfigByRegionResponse{}
	req := &lcos_protobuf.ListMChannelGreyConfigByRegionRequest{
		ReqHeader: genLcosReqHeader(ctx),
		Region:    utils.NewString(region),
	}
	reqStr, _ := jsoniter.MarshalToString(req)

	serviceName := utils.GenKey("-", "sls-timeservice", strings.ToLower(region))
	if err := invoker.Invoke(ctx, serviceName, LcosCdtCalculationSchemaID, ListMChannelGreyConfigByRegion, req, rsp, chassis.WithProtocol("grpc")); err != nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", serviceName, LcosCdtCalculationSchemaID, ListMChannelGreyConfigByRegion, reqStr, err)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	if rsp.GetRespHeader() == nil {
		logger.CtxLogErrorf(ctx, "requesting [%s] error|schema_id=%s|api=%s|req=[%s]|error=%v", serviceName, LcosCdtCalculationSchemaID, ListMChannelGreyConfigByRegion, reqStr, "response header is nil")
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "response header is nil")
	}
	//该region 不存在配置时接口报错 retcode != 0，但业务逻辑正常
	rspStr, _ := jsoniter.MarshalToString(rsp)
	logger.CtxLogInfof(ctx, "requesting [%s] success|schema_id=%s|api=%s|req=[%s]|req=[%s]", serviceName, LcosCdtCalculationSchemaID, GetEddInfoByAlgo, reqStr, rspStr)

	return rsp, nil
}
