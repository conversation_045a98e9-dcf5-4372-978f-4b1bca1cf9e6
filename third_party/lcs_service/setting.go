package lcs_service

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"time"
)

var (
	/*hosts = map[string]map[string]string{
		"LIVE": {
			"ID": "https://lcs-api.shopee.co.id",
			"MY": "https://lcs-api.shopee.com.my",
			"PH": "https://lcs-api.shopee.ph",
			"SG": "https://lcs-api.shopee.sg",
			"TH": "https://lcs-api.shopee.co.th",
			"TW": "https://lcs-api.shopee.tw",
			"VN": "https://lcs-api.shopee.vn",
			"BR": "https://lcs-api.shopee.com.br",
			"MX": "https://lcs-api.shopee.com.mx",
			"AR": "https://lcs-api.shopee.com.ar",
			"PL": "https://lcs-api.shopee.pl",
			"ES": "https://lcs-api.shopee.es",
			"FR": "https://lcs-api.shopee.fr",
			"IN": "https://lcs-api.shopee.in",
			"XX": "https://lcs-api.shopee.sg",
		},
		"STAGING": {
			"ID": "https://lcs-api.staging.shopee.co.id",
			"MY": "https://lcs-api.staging.shopee.com.my",
			"PH": "https://lcs-api.staging.shopee.ph",
			"SG": "https://lcs-api.staging.shopee.sg",
			"TH": "https://lcs-api.staging.shopee.co.th",
			"TW": "https://lcs-api.staging.shopee.tw",
			"VN": "https://lcs-api.staging.shopee.vn",
			"BR": "https://lcs-api.staging.shopee.com.br",
			"MX": "https://lcs-api.staging.shopee.com.mx",
			"AR": "https://lcs-api.staging.shopee.com.ar",
			"PL": "https://lcs-api.staging.shopee.pl",
			"ES": "https://lcs-api.staging.shopee.es",
			"FR": "https://lcs-api.staging.shopee.fr",
			"IN": "https://lcs-api.staging.shopee.in",
			"XX": "https://lcs-api.staging.shopee.sg",
		},
		"UAT": {
			"ID": "https://lcs-api.uat.shopee.co.id",
			"MY": "https://lcs-api.uat.shopee.com.my",
			"PH": "https://lcs-api.uat.shopee.ph",
			"SG": "https://lcs-api.uat.shopee.sg",
			"TH": "https://lcs-api.uat.shopee.co.th",
			"TW": "https://lcs-api.uat.shopee.tw",
			"VN": "https://lcs-api.uat.shopee.vn",
			"BR": "https://lcs-api.uat.shopee.com.br",
			"MX": "https://lcs-api.uat.shopee.com.mx",
			"AR": "https://lcs-api.uat.shopee.com.ar",
			"PL": "https://lcs-api.uat.shopee.pl",
			"ES": "https://lcs-api.uat.shopee.es",
			"FR": "https://lcs-api.uat.shopee.fr",
			"IN": "https://lcs-api.uat.shopee.in",
			"XX": "https://lcs-api.uat.shopee.sg",
		},
		"TEST": {
			"ID": "https://lcs-api.test.shopee.co.id",
			"MY": "https://lcs-api.test.shopee.com.my",
			"PH": "https://lcs-api.test.shopee.ph",
			"SG": "https://lcs-api.test.shopee.sg",
			"TH": "https://lcs-api.test.shopee.co.th",
			"TW": "https://lcs-api.test.shopee.tw",
			"VN": "https://lcs-api.test.shopee.vn",
			"BR": "https://lcs-api.test.shopee.com.br",
			"MX": "https://lcs-api.test.shopee.com.mx",
			"AR": "https://lcs-api.test.shopee.com.ar",
			"PL": "https://lcs-api.test.shopee.pl",
			"ES": "https://lcs-api.test.shopee.es",
			"FR": "https://lcs-api.test.shopee.fr",
			"IN": "https://lcs-api.test.shopee.in",
			"XX": "https://lcs-api.test.shopee.sg",
		},
		"LOCAL": {
			"ID": "http://localhost:8000",
			"MY": "http://localhost:8000",
			"PH": "http://localhost:8000",
			"SG": "http://localhost:8000",
			"TH": "http://localhost:8000",
			"TW": "http://localhost:8000",
			"VN": "http://localhost:8000",
			"BR": "http://localhost:8000",
			"MX": "http://localhost:8000",
			"AR": "http://localhost:8000",
			"PL": "http://localhost:8000",
			"ES": "http://localhost:8000",
			"FR": "http://localhost:8000",
			"IN": "http://localhost:8000",
			"XX": "http://localhost:8000",
		},
	}*/

	defaultTimeout = 5 * time.Second
	httpClient     *chassis.RestInvoker
	account        = "LCOS"
)

func init() {
	httpClient = chassis.NewRestInvoker()
}
