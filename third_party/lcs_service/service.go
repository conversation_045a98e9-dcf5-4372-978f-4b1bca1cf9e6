package lcs_service

import (
	"encoding/json"
	"strings"
	"sync"
	//"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/goroutine"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcs_service/dto"
)

const (
	GetBranchTypeInfo  = "/api/lls/get_all_branch_type_info"
	GetShippingChannel = "/api/lls/get_shipping_channel"
)

type LcsService interface {
	GetBranchTypeInfo(ctx utils.LCOSContext, allRegion bool) ([]*dto.BranchTypeInfo, *lcos_error.LCOSError)
	GetShippingChannels(ctx utils.LCOSContext, branchSupplyType []uint32) ([]*dto.ShippingChannel, *lcos_error.LCOSError)
}

type lcsService struct{}

func NewLcsService() *lcsService {
	return &lcsService{}
}

func (l *lcsService) GetBranchTypeInfo(ctx utils.LCOSContext, allRegion bool) ([]*dto.BranchTypeInfo, *lcos_error.LCOSError) {
	f := func(url string) ([]*dto.BranchTypeInfo, *lcos_error.LCOSError) {
		responseByte, err := http.SendJwtPost(ctx, httpClient, url, []byte("{}"), defaultTimeout, account, cf.GetThirdServiceSecretConfig(ctx).Lcs)
		if err != nil {
			return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.HTTPRequestErrorCode)
		}

		response := &dto.GetBranchTypeInfoResponse{}
		err = json.Unmarshal(responseByte, &response)
		if err != nil {
			return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.JsonDecodeErrorCode)
		}

		if response.Retcode != 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, response.Message)
		}

		return response.Data.BranchTypeList, nil
	}
	hosts := config.GetConf(ctx).HostConfig.Hosts
	if allRegion {
		wg := &sync.WaitGroup{}
		resultCh := make(chan []*dto.BranchTypeInfo, len(hosts[strings.ToUpper(utils.GetEnv(ctx))]))
		for key, host := range hosts[strings.ToUpper(utils.GetEnv(ctx))] {
			if key == "XX" {
				continue
			}
			wg.Add(1)
			tmpHost := host
			goroutine.Go(
				func() {
					defer wg.Done()
					res, _ := f(tmpHost + GetBranchTypeInfo)
					resultCh <- res
				})
		}
		wg.Wait()
		close(resultCh)
		var result []*dto.BranchTypeInfo
		for v := range resultCh {
			result = append(result, v...)
		}
		return result, nil
	}
	url := hosts[strings.ToUpper(utils.GetEnv(ctx))][ctx.GetCountry()] + GetBranchTypeInfo
	return f(url)
}

func (l *lcsService) GetShippingChannels(ctx utils.LCOSContext, branchSupplyType []uint32) ([]*dto.ShippingChannel, *lcos_error.LCOSError) {
	if len(branchSupplyType) == 0 {
		return []*dto.ShippingChannel{}, nil
	}
	hosts := config.GetConf(ctx).HostConfig.Hosts
	url := hosts[strings.ToUpper(utils.GetEnv(ctx))][ctx.GetCountry()] + GetShippingChannel
	requestData := map[string]interface{}{
		"branch_supply_types": branchSupplyType,
	}
	paramByte, err := json.Marshal(requestData)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	responseByte, err := http.SendJwtPost(ctx, httpClient, url, paramByte, defaultTimeout, account, cf.GetThirdServiceSecretConfig(ctx).Lcs)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	response := &dto.GetShippingChannelIdResponse{}
	err = json.Unmarshal(responseByte, &response)
	if err != nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.JsonDecodeErrorCode)
	}

	if response.Retcode != 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.HTTPRequestErrorCode, response.Message)
	}

	return response.Data.ShippingChannels, nil
}
