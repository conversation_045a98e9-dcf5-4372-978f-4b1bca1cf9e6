/*
@Time : 2020/9/21 11:10
<AUTHOR> xiebozeng
@File : channel_tag_request
@Software: GoLand
*/

package dto

type ChannelNamesResponseData struct {
	ChannelId   uint32 `json:"channel_id"`
	ChannelName string `json:"channel_name"`
}

type GetBranchTypeInfoResponse struct {
	Message string `json:"message"`
	Retcode int32  `json:"retcode"`
	Data    struct {
		BranchTypeList []*BranchTypeInfo `json:"branch_type_list"`
	} `json:"data"`
}

type BranchTypeInfo struct {
	BranchSupplyType uint32 `json:"branch_supply_type"`
	BranchTypeName   string `json:"branch_type_name"`
	ChannelID        uint32 `json:"channel_id"`
	Region           string `json:"region"`
}

type GetShippingChannelIdResponse struct {
	Message string `json:"message"`
	Retcode int32  `json:"retcode"`
	Data    struct {
		ShippingChannels []*ShippingChannel `json:"shipping_channels"`
	} `json:"data"`
}

type ShippingChannel struct {
	ShippingChannelID uint32 `json:"shipping_channel_id"`
	BranchSupplyType  uint32 `json:"branch_supply_type"`
}

type SendSyncBranchCommandResponse struct {
	Message string   `json:"message"`
	Retcode int32    `json:"retcode"`
	Data    struct{} `json:"data"`
}
