CRUD_TABLE=""
CRUD_PATH=""
gen:
	set -ue
	cd protobuf && mkdir -p go && protoc -I ./ --go_out=plugins=grpc:./go/ *.proto
test:
	go test -v  -coverprofile=profile.cov ./...
	go tool cover -func profile.cov | grep total | tail -n1
run:
	go run cmd/api/main.go
bench:
	go test -v ./... -test.run="bench*test.go" -test.bench=".*" -test.cpuprofile=cpu.profile
lint:
	golangci-lint run -v --skip-files "_test\.go" --exclude unused,sa1012,asmdecl,S1004,SA5008,SA1019  ./...

doc:
	podman run --rm -v $(PWD)/protobuf/doc:/out -v $(PWD)/protobuf:/protos pseudomuto/protoc-gen-doc --doc_opt=markdown,docs.md

html:
	podman run --rm -v $(PWD)/protobuf/doc:/out -v $(PWD)/protobuf:/protos pseudomuto/protoc-gen-doc
crud:
	echo $(CRUD_TABLE)
	echo $(CRUD_PATH)
	lls_go_scaffold crud  --path $(CRUD_PATH) -t $(CRUD_TABLE) -p model  --project logistics-core-service  --short LCOS -H master.shopee_lcos.mysql.cloud.test.shopee.io --pass E4ORiHc1gmQEk1_x9lPh -d shopee_lcos_db --P 6606 -u shopee_dev_all4

wire:
	make wire_api
	make wire_task
	make wire_grpc

wire_api:
	rm -f apps/admin_api/wire.go
	rm -f apps/admin_api/wire_gen.go
	lls_go_scaffold wire -i apps/admin_api/api_service.go -f InitAdminAPIService

wire_grpc:
	rm -f apps/grpc_api/wire.go
	rm -f apps/grpc_api/wire_gen.go
	lls_go_scaffold wire -i apps/grpc_api/grpc_service.go -f InitGRPCService

wire_task:
	rm -f apps/task_api/wire.go
	rm -f apps/task_api/wire_gen.go
	lls_go_scaffold wire -i apps/task_api/task_service.go -f InitTaskService


yApi:
	podman run --rm -it  -v $(PWD):$(PWD) -w $(PWD) harbor.test.shopeemobile.com/lls/lls-protoc:latest lls_go_scaffold  yApi -k proto --importPath=./protobuf  --protoName=$(PName).proto  -P 1018


.PHONY: run lint bench

proto:
	/bin/bash ./protobuf/build.sh

pb:
	podman run --rm -it  -v $(PWD):$(PWD) -w $(PWD) harbor.test.shopeemobile.com/lls/lls-protoc:latest make proto


pb-docker:
	docker run --rm -it  -v $(PWD):$(PWD) -w $(PWD) harbor.test.shopeemobile.com/lls/lls-protoc:latest make proto

build:
	# clean executable files
	@echo rm -r ./api ./cdttask ./grpc ./mono ./task
	@rm -r ./api ./cdttask ./grpc ./mono ./task || echo "Executable File Not Found. Go build"
	# build
	go build ./cmd/api
	go build ./cmd/cdttask
	go build ./cmd/grpc
	go build ./cmd/mono
	go build ./cmd/task
	# clean executable files
	@echo rm -r ./api ./cdttask ./grpc ./mono ./task
	@rm -r ./api ./cdttask ./grpc ./mono ./task
	# echo success result
	@echo "all build success!!!"