syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service ParcelLibraryService {
  rpc GetParcelSizeDetailBySkuCombination(GetParcelSizeDetailBySkuRequest) returns (GetParcelSizeDetailBySkuResponse) {}
  rpc BatchGetParcelLibraryDataBySkuInfos(BatchGetParcelLibraryDataBySkuInfosRequest) returns (BatchGetParcelLibraryDataBySkuInfosResponse) {}
}

message ParcelLibraryQuery {
  required string unique_id = 1;
  required string region    = 2;
  repeated SkuInfo sku_list = 3;
  optional uint32 buyer_purchase_time = 4;
}

message ParcelLibraryInfo {
  required string unique_id = 1;
  required double accurate_length = 2;
  required double accurate_width = 3;
  required double accurate_height = 4;
  required double accurate_weight = 5;
  required double weight_fin_accuracy = 6;
  required double volumetric_fin_accuracy = 7;
  required int32  is_bulky_parcel = 8;
  optional double weight_enhanced_fin_accuracy = 9;
  optional double volumetric_enhanced_fin_accuracy = 10;
}

message GetParcelSizeDetailBySkuRequest{
  required ReqHeader  req_header = 1;
  repeated SkuInfo    sku_info   = 2;
  required string region = 3;
}

message GetParcelSizeDetailBySkuResponse {
  optional ParcelSizeInfo parcel_size_info = 1;
  required RespHeader resp_header = 2;
}

message ParcelSizeInfo {
  required string length = 1;
  required string width = 2;
  required string height = 3;
  required string weight = 4;
  required string actual_weight_accuracy = 5;
  required string volumetric_weight_accuracy = 6;
  optional int32  is_bulky_parcel = 8;
  optional string weight_enhanced_fin_accuracy = 9;
  optional string volumetric_enhanced_fin_accuracy = 10;
}

message GroupParcelSizeInfo {
  required string accurate_length = 1;
  required string accurate_width = 2;
  required string accurate_height = 3;
  required string accurate_weight = 4;
  required string weight_fin_accuracy = 5;
  required string volumetric_fin_accuracy = 6;
  required string group_id = 7;
  optional int32  is_bulky_parcel = 8;
  optional string weight_enhanced_fin_accuracy = 9;
  optional string volumetric_enhanced_fin_accuracy = 10;
}

message BatchGetParcelLibraryDataBySkuInfosRequest{
  required ReqHeader  req_header = 1;
  repeated CalculateDetail    orders   = 2;
  required string region = 3;
}

message BatchGetParcelLibraryDataBySkuInfosResponse {
  repeated CalculateResult order_result = 1;
  required RespHeader resp_header = 2;
}

message CalculateResult {
  repeated GroupParcelSizeInfo order_result = 1;
  required string calculate_id = 2;
}

message CalculateDetail {
  required string       calculate_id = 1;
  repeated SkuGroupInfo sku_group = 2;
}

message SkuGroupInfo {
  required string   group_id = 1;
  repeated SkuInfo  sku_info = 2;
}
