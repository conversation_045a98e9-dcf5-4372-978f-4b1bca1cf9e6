syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service BranchService {
  rpc GetSyncBranchSupplyType(GetSyncBranchSupplyTypeRequest) returns (GetSyncBranchSupplyTypeResponse) {}
  rpc SyncBranchInfo(SyncBranchInfoRequest) returns (SyncBranchInfoResponse) {}
  rpc GetBranchInfo(GetBranchInfoRequest) returns (GetBranchInfoResponse) {}
  rpc BatchGetBranchInfoByBranchId(BatchGetBranchInfoByBranchIdRequest) returns (BatchGetBranchInfoByBranchIdResponse) {}
  rpc BatchGetBranchInfoByBranchRef(BatchGetBranchInfoByBranchRefRequest) returns (BatchGetBranchInfoByBranchRefResponse) {}
  rpc GetBranchInfoByLocationID(GetBranchInfoByLocationIDRequest) returns (GetBranchInfoByLocationIDResponse) {}
  rpc GetBranchSubLocations(GetBranchSubLocationsRequest) returns (GetBranchSubLocationsResponse) {}
  rpc SearchBranch(SearchBranchRequest) returns (SearchBranchResponse) {}
  rpc SearchBranchForGoogleMaps(SearchBranchForGoogleMapsRequest) returns (SearchBranchForGoogleMapsResponse) {}   // 配合google maps的api接口
  rpc GetBranchListByBranchGroupID(GetBranchListByBranchGroupIDRequest) returns (GetBranchListByBranchGroupIDResponse) {}  // SPLN-19827
  rpc BatchSearchDropoffBranch(BatchSearchDropoffBranchRequest) returns (BatchSearchDropoffBranchResponse) {}
  rpc GetSpxStationIdInfoByOrderId(GetSpxStationIdInfoByOrderIdRequest) returns (GetSpxStationIdInfoByOrderIdResponse) {}  // SPLN-19827
  rpc CheckAddressListHomeDeliveryAbility(CheckHDAbilityRequest) returns (CheckHDAbilityResponse) {}
  rpc GetHomeDeliveryStation(GetHDStationRequest) returns (GetHDStationResponse) {}
  rpc GetCacheVal(QueryCacheValReq) returns (QueryCacheValResp) {}; // SPLPS-13459 缓存值查询接口，方便测试和排查问题
  rpc GetDrivingDistanceWithHDStation(GetDrivingDistanceWithHDStationRequest) returns (GetDrivingDistanceWithHDStationResponse) {}
  rpc GetBuyerAddressCoordinate(GetBuyerAddressCoordinateRequest) returns (GetBuyerAddressCoordinateResponse) {}
  rpc SearchNearbyStation(SearchNearbyStationRequest) returns (SearchNearbyStationResponse) {}
  rpc GetDrivingDistanceFromMatrix(GetDrivingDistanceFromMatrixRequest) returns (GetDrivingDistanceFromMatrixResponse) {}
  rpc GetCoordinateByAddress(GetCoordinateByAddressRequest) returns (GetCoordinateByAddressResponse) {}
}

message GetSyncBranchSupplyTypeRequest {
  required ReqHeader req_header = 1;
  required string region = 2;
}

message GetSyncBranchSupplyTypeResponse {
  required RespHeader resp_header = 1; //公共返回头
  repeated uint32 branch_supply_type = 2;
}

message SyncBranchInfoRequest {
  required ReqHeader req_header = 1;
  required uint32 branch_supply_type = 2;
  required string remote_file_path = 3;
  required string region = 4;
}

message SyncBranchInfoResponse {
  required RespHeader resp_header = 1; //公共返回头
}

message GetBranchInfoRequest {
  required ReqHeader req_header = 1;
  required uint64 branch_id = 2;
  repeated uint32 branch_group_id = 3;
}

message GetBranchInfoResponse {
  required RespHeader resp_header = 1;
  optional BranchInfo branch_info = 2;
}

// req
message BatchGetBranchInfoByBranchIdRequest {
  required ReqHeader req_header = 1;
  repeated getBranchInfoByBranchIdRequest branch_info_list = 2;
}

// rsp
message BatchGetBranchInfoByBranchIdResponse {
  required RespHeader resp_header = 1;
  repeated SingleBranchInfo data = 2;
}

message getBranchInfoByBranchIdRequest {
  required string unique_id = 1;
  required uint64 branch_id = 2;
  optional uint32 branch_group = 3;
}

// req
message BatchGetBranchInfoByBranchRefRequest {
  required ReqHeader req_header = 1;
  repeated getBranchInfoByBranchRefRequest branch_info_list = 2;
}

// rsp
message BatchGetBranchInfoByBranchRefResponse {
  required RespHeader resp_header = 1;
  repeated SingleBranchInfo data = 2;
}

message getBranchInfoByBranchRefRequest {
  required string unique_id = 1;
  required string branch_ref = 2;
  required uint32 branch_group = 3;
}

// single
message SingleBranchInfo {
  required string unique_id = 1;
  optional int32 err_code = 2;
  optional string message = 3;
  optional uint64 id = 4; // sls branch id
  optional uint32 branch_group = 5; // sls branch group
  optional uint32 branch_type = 6; // sls branch ype
  optional string branch_ref = 7; // 3pl branch id
  optional string branch_name = 8;
  optional string branch_address = 9;
  optional uint32 status = 10;
  optional string extra_data = 11;
  optional string postal_code = 12;
  optional string latitude = 13;
  optional string longitude = 14;
  optional uint32 create_time = 15;
  optional uint32 update_time = 16;
  optional string country = 17;
  optional string state = 18;
  optional string city = 19;
  optional string district = 20;
  optional string street = 21;
  optional string branch_phone = 22;
  optional SubStatusEnum sub_status = 23; //SPLN-31422
}

message BranchInfo {
  required uint64 branch_id = 1;
  required string branch_name = 2;
  required uint64 location_id = 3;
  required uint64 location_division_id = 4;
  required uint32 branch_type = 5; // 100: Store  200: Box
  required string postalcode = 6;
  required string longitude = 7;
  required string latitude = 8;
  required string detail_address = 9;
  repeated OpenHour open_hour = 10;
  required string branch_ref = 11;
  optional uint32 branch_status = 12; // 1:valid  2:invalid
  optional BranchInfoExtraData branch_info_extra_data = 13;
  optional string sub_district = 14;
  optional string branch_phone = 15;
  optional int32 max_parcel_stay_duration = 16; // -1 表示不限制
  optional string branch_code = 17; // SPLN-19827
  optional string region = 18;      // SPLN-23437
  repeated string ops_type = 19;                  // SPLN-23324
  optional string location_description = 20;      // SPLN-23324
  optional SubStatusEnum sub_status = 21; //SPLN-31422
}

// 用于给google maps提供信息
message FullBranchInfo {
  required uint64 branch_id = 1;
  required string branch_name = 2;
  required uint64 location_id = 3;
  required uint64 location_division_id = 4;
  required uint32 branch_type = 5; // 100: Store  200: Box
  required string postalcode = 6;
  required string longitude = 7;
  required string latitude = 8;
  required string detail_address = 9;
  repeated OpenHour open_hour = 10;
  required string branch_ref = 11;
  optional uint32 branch_status = 12; // 1:valid  2:invalid
  optional BranchInfoExtraData branch_info_extra_data = 13;
  optional string sub_district = 14;
  optional string branch_phone = 15;
  optional int32 max_parcel_stay_duration = 16; // -1 表示不限制
  optional string branch_code = 17; // SPLN-19827
  optional string state = 18; // 补充地址信息
  optional string city = 19;
  optional string district = 20;
  optional string street = 21;
  optional uint32 branch_group_id = 22;
}

message BranchInfoExtraData {
  optional string DCNAME = 1;
  optional string DCCODE = 2;
}

message OpenHour {
  required string start_time = 1;
  required string end_time = 2;
  repeated uint32 day_of_week = 3; // [1-7] [sunday-starday]
  required string time_zone = 4;
}

message GetBranchInfoByLocationIDRequest {
  required ReqHeader req_header = 1;
  required uint32 branch_group_id = 2;
  required uint64 location_id = 3;
  repeated SubStatusEnum  sub_status = 4;
}

message GetBranchInfoByLocationIDResponse {
  required RespHeader resp_header = 1; //公共返回头
  repeated BranchInfo data = 2;
}

//message BranchInfoForSlsApi {
//required string branch_ref =1;
//required string branch_name =2;
//required string branch_address =3;
//required string work_hour = 4;
//}

message GetBranchSubLocationsRequest {
  required ReqHeader req_header = 1;
  required uint32 branch_group_id = 2;
  required uint64 location_id = 3;
}

message GetBranchSubLocationsResponse {
  required RespHeader resp_header = 1; //公共返回头
  optional LocationsIdMap locations_id_map = 2;
}

message LocationsIdMap {
  repeated uint64 state_locations_id = 1;
  repeated uint64 city_locations_id = 2;
  repeated uint64 district_locations_id = 3;
  repeated uint64 street_locations_id = 4;
}

message OpsType {
  repeated string ops_type = 1;
}

message SearchBranchRequest {
  optional ReqHeader req_header = 1;
  optional uint32 branch_group_id = 2;
  optional string keyword = 3;
  optional uint32 find_type = 4;
  optional string region = 5;
  optional int32 size = 6;
  optional uint32 location_sort_type = 7; // 0(默认值): 直线距离排序 1. 车辆行驶距离排序
  optional string longitude = 8;
  optional string latitude = 9;
  optional string zipcode = 10;
  repeated OpsType ops_type_list = 11;
  optional uint32 distance = 12;         // SPLN-23324 allow distance to get from outside
  repeated SubStatusEnum sub_status = 13;  //0=unavailable,1=available,2=closing soon,3=temp close,4=checkout limit
}

message SearchBranchForGoogleMapsRequest {
  optional ReqHeader req_header = 1;
  repeated uint32 branch_group_id = 2;     // 3pl唯一标识
  optional string keyword = 3;
  optional uint32 find_type = 4;
  optional string region = 5;
  optional int32 size = 6;
  optional uint32 location_sort_type = 7; // 0(默认值): 直线距离排序 1. 车辆行驶距离排序
  optional string longitude = 8;
  optional string latitude = 9;
  optional string zipcode = 10;
  optional uint32 distance = 11;
}

message SearchBranchResponse {
  optional RespHeader resp_header = 1;
  optional uint32 count = 2;
  repeated BranchInfo branch_info = 3;
  map<uint64, double> distance = 4;
}

message SearchBranchForGoogleMapsResponse {
  optional RespHeader resp_header = 1;
  optional uint32 count = 2;
  repeated FullBranchInfo branch_info = 3;
  map<uint64, double> distance = 4;
}


message GetBranchListByBranchGroupIDRequest {
  optional ReqHeader req_header = 1;
  required uint32 branch_group_id = 2;
}

message GetBranchListByBranchGroupIDResponse {
  required RespHeader resp_header = 1; //公共返回头
  repeated BranchInfo branch_info_list = 2;
}

enum SubStatusEnum{
  Unavailable   = 0;
  Available     = 1;
  ClosingSoon   = 2;
  TempClose     = 3;
  CheckoutLimit = 4;
}

message SearchDropoffBranchRequest {
    required string unique_id = 1;
    required SearchBranchRequest search_branch_request = 2;
}

message BatchSearchDropoffBranchRequest {
    optional ReqHeader req_header = 1;
    repeated SearchDropoffBranchRequest search_dropoff_branch_request = 2;
}

message BatchSearchDropoffBranchResponse {
    optional RespHeader resp_header = 1;
    map<string, SearchBranchResponse> branch_result_map = 2;
}

message GetSpxStationIdInfoByOrderIdRequest {
  optional ReqHeader req_header = 1;
  optional uint64 order_id = 2;
  optional string address = 3;
  optional string address_l1 = 4;
  optional string address_l2 = 5;
}

message GetSpxStationIdInfoByOrderIdResponse {
  optional RespHeader resp_header = 1;
  optional uint64 station_id = 2;
  optional int32 deliver_mode = 3;
  repeated string line_list = 4;
}

message CheckHDAbilityRequest {
  required ReqHeader req_header = 1;
  optional string buyer_id = 2;
  repeated CheckHDAbilityAddressItem check_hd_ability_address_items = 3;
}

message CheckHDAbilityAddressItem {
  optional string address = 1;
  optional string address_l1 = 2;
  optional string address_l2 = 3;
  optional string unique_key = 4;
}

message CheckHDAbilityResponse {
  required RespHeader resp_header = 1;
  optional string     buyer_id = 2;
  repeated CheckHDAbilityAddressResItem check_hd_ability_address_res_item = 3;
}

message CheckHDAbilityAddressResItem {
  optional string address = 1;
  optional string address_l1 = 2;
  optional string address_l2 = 3;
  optional string unique_key = 4;
  optional int32 is_support_hd = 5;
  optional string return_type = 6;
}

message GetHDStationRequest {
  required ReqHeader req_header = 1;
  optional GetHDStationAddressItem get_hd_station_address_item = 2;
}

message GetHDStationAddressItem {
  optional string address_l1 = 1;
  optional string address_l2 = 2;
  optional string address = 3;
  optional string shipment_id = 4;
}

message GetHDStationResponse {
  required RespHeader resp_header = 1;
  optional GetHDStationAddressResItem get_hd_station_address_res_item = 2;
}

message GetHDStationAddressResItem {
  optional string address_l1 = 1;
  optional string address_l2 = 2;
  optional string address = 3;
  optional uint64 station_id = 4;
  optional float address_lng = 5;
  optional float address_lat = 6;
}

message QueryCacheValItem {
  required string cache_key = 1;
  required string cache_type = 2;
  required string val_type = 3;
}

message QueryCacheValReq {
  repeated QueryCacheValItem list = 1;
}

message QueryCacheValRespItem {
  optional string cache_key = 1;
  optional string cache_type = 2;
  optional string val_type = 3;
  optional string value = 4;
  optional int32 retcode = 5;
  optional string message = 6;
}

message QueryCacheValResp {
  required RespHeader resp_header = 1;
  repeated QueryCacheValRespItem list = 2;
}

message GetDrivingDistanceWithHDStationRequest {
  required ReqHeader req_header = 1;
  optional string buyer_id = 2;
  repeated CommonAddress address_list = 3;
}

message GetDrivingDistanceWithHDStationResponse {
  required RespHeader resp_header = 1;
  repeated StationToAddressDrivingDistance station_to_address_driving_distance_list = 2;
}

message GetBuyerAddressCoordinateRequest {
  required ReqHeader req_header = 1;
  optional string buyer_id = 2;
  repeated CommonAddress address_list = 3;
}

message GetBuyerAddressCoordinateResponse {
  required RespHeader resp_header = 1;
  repeated AddressCoordinate address_coordinate_list = 2;
}

message SearchNearbyStationRequest {
  required ReqHeader req_header = 1;
  repeated CenterPointWithRadius center_point_with_radius_list = 2;
}

message SearchNearbyStationResponse {
  required RespHeader resp_header = 1;
  repeated NearbyStation nearby_station_list = 2;
}

message GetDrivingDistanceFromMatrixRequest {
  required ReqHeader req_header = 1;
  repeated CommonCoordinate starting_point_list = 2;
  repeated CommonCoordinate terminate_point_list = 3;
}

message GetDrivingDistanceFromMatrixResponse {
  required RespHeader resp_header = 1;
  repeated CommonMatrixDistance common_matrix_distance_list = 2;
}

message GetCoordinateByAddressRequest {
  required ReqHeader req_header = 1;
  optional string address_l1 = 2;
  optional string address_l2 = 3;
  optional string address = 4;
}

message GetCoordinateByAddressResponse {
  required RespHeader resp_header = 1;
  optional string longitude = 2;
  optional string latitude = 3;
}

message StationToAddressDrivingDistance {
  optional CommonAddress address = 1;
  optional CommonCoordinate coordinate = 2;
  optional bool is_address_not_exist = 3;
  repeated StationToAddressDrivingDistanceItem station_to_address_driving_distance_item_list = 4;
}

message CommonAddress {
  optional string address = 1;
  optional string address_l1 = 2;
  optional string address_l2 = 3;
  optional string address_l3 = 4;
  optional string address_l4 = 5;
  optional string address_l5 = 6;
  optional string address_l6 = 7;
  optional string address_l7 = 8;
  optional string address_l8 = 9;
  optional string zipcode = 10;
}

message CommonCoordinate {
  optional float lat = 1;
  optional float lng = 2;
}

message StationToAddressDrivingDistanceItem {
  optional int64 station_id = 1;
  optional float driving_distance = 2;
  optional uint64 version = 3;
}

message AddressCoordinate {
  optional CommonAddress address = 1;
  optional CommonCoordinate coordinate = 2;
  optional string standard_address = 3;
}

message CenterPointWithRadius {
  optional CommonCoordinate center_point = 1;
  optional float radius = 2;
  optional CommonAddress address = 3;
}

message NearbyStation {
  optional CommonCoordinate center_point = 1;
  optional float radius = 2;
  optional CommonAddress address = 3;
  repeated StationCoordinate station_list = 4;
}

message StationCoordinate {
  optional int64 station_id = 1;
  optional CommonCoordinate coordinate = 2;
}

message CommonMatrixDistance {
  optional CommonCoordinate start_point = 1;
  optional CommonCoordinate end_point = 2;
  optional bool is_no_result = 3;
  optional float distance = 4;
}