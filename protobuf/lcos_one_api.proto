syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";
import "lcos_serviceable.proto";
import "lcos_weight_limit.proto";
import "lcos_pickup_window.proto";
import "lcos_branch.proto";
import "lcos_tw_store.proto";
import "lcos_scene_serviceable.proto";
import "lcos_parcel_lib.proto";

service LcosOneApiService {
  rpc GetAndCheckCanCreateWaybillLine(OneApiRequest) returns (OneApiResponse) {}
  rpc GetAndCheckForCheckoutScene(OneApiRequestForCheckoutScene) returns (OneApiResponseForCheckoutScene) {}
  rpc GetAndCheckForCheckoutSceneV2(OneApiRequestForCheckoutScene) returns (OneApiResponseForCheckoutScene) {}
  rpc GetAndCheckForFulfillment(OneApiRequestForFulfillment) returns (OneApiResponseForFulfillment){}
  rpc GetAndCheckCanRerouteLine(OneApiRequestForReroute) returns (OneApiResponseForReroute){}
}

// @core
message LineWaybillCheckAttr {
  required string req_no = 1; // 唯一标识
  required string line_id = 2; // 需要进行check或者get的line
  optional uint32 is_check_serviceable = 3; // 是否检查服务范围的flag, 1-Check, 0-Not Check
  optional uint32 is_check_pickup_time = 4; // 是否校验pickup time的flag, 1-Check, 0-Not Check
  optional uint32 is_check_line_rule = 5; // 是否校验line rule的flag, 1-Check, 0-Not Check
  optional CheckServiceableAreaBaseInfo2 serviceable_info = 6; // 需要检查的服务范围信息isCheckServiceable开启时，不能为空
  optional LocationInfo pickup_info = 7; // 需要检查的服务范围信息isCheckServiceable开启时，不能为空
  optional LocationInfo deliver_info = 8; // 需要检查的服务范围信息isCheckServiceable开启时，不能为空
  optional CheckPickupTimeInfo pickup_time_info = 9; // 需要检查的pickup time info，isCheckPickup开启时不能为空
}

// @core
message GetTimeslotForOneApiRequest {
  required uint32 pickup_time = 1;          // 揽收时间
  required string country = 2;             // 当前所在country
  required uint32 pickup_time_range_id = 3; // timeslot的pickup time range id
  optional uint32 start_time = 4; // 当前不需要
  optional uint32 merchant_type = 5; // merchant_type，当merchant_type和account_group同时传入时，走开放物流逻辑。只要有一个没传入，则走点线逻辑
  optional uint32 account_group = 6; // account_group，不必填
}

// @core
message LineWaybillGetAttr {
  required string req_no = 1; // 唯一标识
  required string line_id = 2; // 传入的first mile line id
  optional GetTimeslotForOneApiRequest get_timeslot_info = 3; // 获取timeslot信息
  optional BranchGetReq dest_branch_get_req = 4;
  optional TwStoreGetReq tw_store_get_req = 5;
}

// @core
message BranchGetReq {
  optional uint64 branch_id = 1;
  optional uint32 branch_group_id = 2;
}

// @core
message TwStoreGetReq {
  repeated string request_list = 1;   // address_id的列表，需要转为string
}

// @core
message OneApiInfo {
  optional OrderInfo order_info = 1;
  repeated SkuInfo sku_info = 2;
  repeated LineWaybillCheckAttr check_line_waybill_attr_list = 3;
  repeated LineWaybillGetAttr get_line_waybill_attr_list = 4;
  repeated SubPackageInfo sub_package_info = 5;
  optional uint32 buyer_purchase_time = 6;
}

// @core
message OneApiRequest {
  required ReqHeader req_header = 1;
  required OneApiInfo one_api_info = 2;
}

// @core
message OneApiCheckInfo {
  optional int32 retcode = 1;
  optional string message = 2;
}

// @core
message OneApiLineRuleInfo {
  optional int32 retcode = 1;
  optional string message = 2;
  repeated SingleCheckLineRuleResult rule_limit_info = 3;
}

// @core
message CheckResults {
  map<string, CheckLineServiceableAreaResponse> serviceable_check_result = 1; // 服务范围的检查结果，key为req_no
  map<string, CheckPickupTimeResponse> pickup_time_check_result = 2; // pickup time的检查结果，key为req_no
  map<string, CheckLineRuleResponse> line_rule_check_result = 3; // line rule的检查结果，key为req_no
}

// @core
message OneApiPickupTimeInfo {
  required uint32     pickup_start_time = 1;
  required uint32     pickup_end_time = 2;
}

// @core
message OneApiGetPickupTimeslotsResponse {
  required int32  retcode = 1;
  required string message = 2;
  optional OneApiPickupTimeInfo pickup_time_info = 3;
}

// @core
message GetResults {
  map<string, OneApiGetPickupTimeslotsResponse> timeslot_results = 1; // key为req_no
  map<string, OneApiBranchInfo> dest_branch_results = 2; // key为req_no
  map<string, BatchGetStoreByStoreIDResponse> tw_store_results = 3; // key为req_no
}

// @core
message OneApiBranchInfo {
  required int32  retcode = 1;
  required string message = 2;
  optional BranchInfo branch_info = 3;
}

// @core
message OneApiResponse {
  required RespHeader resp_header = 1;
  map<string, OneApiCheckInfo> check_results = 2;
  optional GetResults get_results = 3;
}

// @core
message SingleLaneProductRule {
  required string                 unique_id = 1;
  repeated SkuInfo                sku_info = 2;
  optional OrderInfo              order_info = 3;
  repeated string                 product_id = 4;
  optional uint32                 buyer_purchase_time = 5;
}

// @core
message ServiceableItem {
  required uint64 item_id = 1;
  optional uint64 model_id = 2;
  optional uint32 category_id = 3;
  optional uint32 global_category_id = 4;
  optional float item_price = 5;
  optional float item_price_usd = 6;
  required uint32 quantity = 7;
  optional float length = 8;
  optional float width = 9;
  optional float height = 10;
  optional float weight = 11;
}

// @core
message LogisticsServiceableReq {
  required LaneAreaServiceabilityReq lane_serviceability = 1;
  repeated SkuInfo sku_infos = 2;
  repeated SubPackageInfo sub_package_info = 3;
  optional uint32 buyer_purchase_time = 4;
}

// @core
message OneApiCheckInfoForCheckoutScene {
  repeated SingleLaneProductRule product_rules_list = 1; // for product rule check
  repeated LogisticsServiceableReq product_serviceable_list = 2; // for serviceable area check
  optional uint32 is_check_product_rule = 3  [default=1]; // flag to decide whether need check product rule, default check
  repeated ParcelLibraryQuery parcel_library_queries = 4;
}

// @core
message LaneServiceable {
  required string lane_code = 1;
  required int32 code = 2;
  required string message = 3;
  optional AreaServiceable area_serviceable = 6;
  repeated string lane_code_group = 8;
}

// @core
message LogisticsServiceable {
  required string unique_id = 1;
  required int32 errcode = 2;
  required string message = 3;
  repeated LaneServiceable available_lanes = 4;
  repeated LaneServiceable unavailable_lanes = 5;
}

// @core
message OneApiRequestForCheckoutScene {
  required ReqHeader req_header = 1;
  required OneApiCheckInfoForCheckoutScene one_api_check_info = 2; // for check info in one api
}

// @core
message OneApiResponseForCheckoutScene {
  required RespHeader resp_header = 1;
  map<string, CheckProductRuleResponse>   check_product_rule_result_map = 2; // result for check product rule，key is unique id
  map<string, ProductServiceableRsp>      check_serviceable_area_result_map = 3; // result for check serviceable area，key is unique id
  map<string, ParcelLibraryInfo>          parcel_library_info_map = 4;
}

// @core
message OneApiRequestForFulfillment {
    required ReqHeader req_header = 1;
    required OneApiCheckInfoForFulfillment one_api_check_info = 2; // for check info in one api
}

// @core
message OneApiCheckInfoForFulfillment {
    repeated LogisticsServiceableReq product_serviceable_list = 1; // for serviceable area check
}

// @core
message OneApiResponseForFulfillment {
    required RespHeader resp_header = 1;
    map<string, ProductServiceableRsp>   check_serviceable_area_result_map = 2; // result only for check serviceable area，key is unique id
    map<string, CheckLaneRuleResponse>   check_lane_rule_result_map = 3; // result only for lane rule，key is unique id
}

// @core
message OneApiRequestForReroute {
    required ReqHeader req_header = 1;
    required RerouteOneApiInfo one_api_info = 2;
}

// @core
message RerouteOneApiInfo {
    repeated LaneServiceableReq lane_serviceable_list = 1; // serviceable area check
    repeated SkuInfo sku_infos = 2;
    optional uint32 buyer_purchase_time = 3;
}

// @core
message OneApiResponseForReroute {
    required RespHeader resp_header = 1;
    map<string, RerouteCheckRsp>   reroute_check_rsp = 2;  // key is uniq_id
}

// @core
message RerouteCheckRsp {
    repeated LaneCheckResult lane_check_result = 1;
}

// @core
message LaneCheckResult {
    required int32 code = 1;
    required string message = 2;
    required string lane_code = 3;
    repeated string lane_code_group = 4;
    repeated Serviceable serviceable = 5;
    repeated SingleCheckLineRuleResult rule_limit_info = 6;
}

