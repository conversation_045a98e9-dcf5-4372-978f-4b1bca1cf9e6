syntax = "proto2";

package lcos_protobuf;

message ParcelLibraryDataLite {
  required double accurate_length = 1;
  required double accurate_width = 2;
  required double accurate_height = 3;
  required double accurate_weight = 4;
  required uint64 version = 5;
  required uint32 effective_time = 6;
  required double length_accuracy = 7;
  required double width_accuracy = 8;
  required double height_accuracy = 9;
  required double weight_accuracy = 10;
  required double weight_fin_accuracy = 11;
  required double volumetric_fin_accuracy = 12;
  optional int32  is_bulky_parcel = 13 [default=-1]; // 1: bulky, 0: non-bulky, -1: null
  optional double weight_enhanced_fin_accuracy = 14 [default=-1];
  optional double volumetric_enhanced_fin_accuracy = 15 [default=-1];
}