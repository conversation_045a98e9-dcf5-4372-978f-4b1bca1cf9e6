syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service LcosWeightLimitService {
  rpc CalculateWeight(CalculateWeightRequest) returns (CalculateWeightResponse) {}
  rpc BatchCalculateWeight(BatchCalculateWeightRequest) returns (BatchCalculateWeightResponse) {}
  rpc CheckLineRule(CheckLineRuleRequest) returns (CheckLineRuleResponse) {}
  rpc BatchCheckLineRule(BatchCheckLineRuleRequest) returns (BatchCheckLineRuleResponse) {}


  rpc GetLineRule(GetLineRuleRequest) returns (GetLineRuleResponse) {}
  rpc BatchGetLineRule(BatchGetLineRuleRequest) returns (BatchGetLineRuleResponse) {}
  rpc CheckProductRule(CheckProductRuleRequest) returns (CheckProductRuleResponse) {}
  rpc BatchCheckProductRule(BatchCheckProductRuleRequest) returns (BatchCheckProductRuleResponse) {}
  rpc BatchCheckProductRuleForAlgo(BatchCheckProductRuleRequest) returns (BatchCheckProductRuleResponse) {}
  rpc BatchCheckProductRuleForShoppingCart(BatchCheckProductRuleForShoppingCartRequest) returns (BatchCheckProductRuleForShoppingCartResponse) {}
  rpc GetProductRule(GetProductRuleRequest) returns (GetProductRuleResponse) {}
  rpc BatchGetProductRule(BatchGetProductRuleRequest) returns (BatchGetProductRuleResponse) {}
  rpc BatchCalculateProductValidateWeight(BatchCalculateProductValidateWeightRequest) returns (BatchCalculateProductValidateWeightResponse) {}
  rpc GetTWSizeInfos(GetTWSizeInfosRequest) returns (GetTWSizeInfosResponse) {}
  rpc BatchGetProductSideLimits(ProductSideLimitsRequest) returns (ProductSideLimitsResponse) {}

  // SPLN-22250 lane rule, if one line is not valid, the whole lane is not valid
  rpc BatchCheckLaneRule(BatchCheckLaneRuleRequest) returns (BatchCheckLaneRuleResponse) {}
  rpc CalculateFormula(CalculateFormulaRequest) returns (CalculateFormulaResponse) {}
  rpc BatchCheckProductRuleForParcelLib(BatchCheckProductRuleForParcelLibRequest) returns (BatchCheckProductRuleForParcelLibResponse) {}
}

message GetTWSizeInfosRequest {
  required ReqHeader              req_header = 1;
  required string                 product_id = 2;
  optional string                 size_id = 3;
}

message GetTWSizeInfosResponse {
  required RespHeader resp_header = 1;
  repeated TWSizeInfo             size_infos = 2;
}

message TWSizeInfo {
  required uint64     id = 1;
  required string     product_id = 2;
  required string     size_id = 3;
  required string     name = 4;  // 限制类型
  optional string     description = 5;  // 是否限制，0-不限制，1-限制
  optional string     country = 6;
  optional string     unit = 7;
  optional double     max_size = 8;
  optional double     min_size = 9;
  optional double     default_price = 10;
  optional string     extra_data = 11;
}

message CalculateFormulaRequest{
  required ReqHeader          req_header = 1;
  required CalculateBaseInfoMulti  base_info = 2;
  optional OrderInfo          order_info = 3;
  repeated SkuInfo            sku_info = 4;
}

message CalculateFormulaResponse{
  required RespHeader resp_header = 1;
  map<uint32, CalculateFormulaResult>      formula_result = 2;
}



message CalculateFormulaResult {
  required RespHeader resp_header = 1;
  optional double     result = 2;
}

message CalculateWeightRequest {
  required ReqHeader              req_header = 1;
  required CalculateBaseInfo      base_info = 2;
  required OrderInfo              order_info = 3;
  repeated SkuInfo                sku_info = 4;
}

message CalculateWeightResponse {
  required RespHeader resp_header = 1;
  optional double     result_weight = 2;
}

message BatchCalculateWeightRequest {
  required ReqHeader              req_header = 1;
  repeated BatchCalculateInfo     calculate_info = 2;
}

message BatchCalculateWeightResponse {
  required RespHeader               resp_header = 1;
  repeated BatchCalculateResult     calculate_result = 2;
}

message CheckLineRuleRequest {
  required ReqHeader              req_header                  = 1;
  repeated SkuInfo                sku_info                    = 2;
  repeated string                 line_id                     = 3;
  optional OrderInfo              order_info                  = 4;
  repeated SubPackageInfo         sub_package_info            = 5;
  optional uint32                 buyer_purchase_time         = 6;
}

message SingleCheckLineRuleRequest {
  required string                 unique_id                   = 1;     // 唯一标识，必填且不能为空
  repeated SkuInfo                sku_info                    = 2;
  repeated string                 line_id                     = 3;
  optional OrderInfo              order_info                  = 4;
  repeated SubPackageInfo         sub_package_info            = 5;
  optional uint32                 buyer_purchase_time         = 6;
}

message BatchCheckLineRuleRequest {
  required ReqHeader                           req_header = 1;
  repeated SingleCheckLineRuleRequest          check_line_rule_list = 2;
}

message CheckLineRuleResponse {
  required RespHeader                     resp_header = 1;
  repeated SingleCheckLineRuleResult      rule_limit_info = 2;
}

message BatchCheckLineRuleResponse {
  required RespHeader                     resp_header = 1;
  map<string, CheckLineRuleResponse>      check_line_rule_result_map = 2; // 批量检查结果，key为unique id
}

message CheckProductRuleRequest {
  required ReqHeader              req_header                  = 1;
  repeated SkuInfo                sku_info                    = 2;
  repeated string                 product_id                  = 3;
  optional OrderInfo              order_info                  = 4;
  repeated SubPackageInfo         sub_package_info            = 5;
  optional uint32                 buyer_purchase_time         = 6;
}

message SingleProductRule {
  required string                 unique_id                   = 1;
  repeated SkuInfo                sku_info                    = 2;
  repeated string                 product_id                  = 3;
  optional OrderInfo              order_info                  = 4;
  repeated SubPackageInfo         sub_package_info            = 5;
  optional uint32                 buyer_purchase_time         = 6;
}

message BatchCheckProductRuleRequest {
  required ReqHeader              req_header = 1;
  repeated SingleProductRule      product_rules = 2;
}

message BatchCheckProductRuleForShoppingCartRequest {
  required ReqHeader              req_header = 1;
  repeated SingleProductRule      product_rules = 2;
}

message CheckProductRuleResponse {
  required RespHeader                     resp_header = 1;
  repeated SingleCheckProductRuleResult   rule_limit_info = 2;
}

message BatchCheckProductRuleResponse {
  required RespHeader                     resp_header = 1;
  map<string, CheckProductRuleResponse>   check_product_rule_result_map = 2; // 批量检查结果，key为unique id
}

message VolumetricWeightInfo {
  optional uint64 item_id = 1;
  optional uint64 category_id = 2;
  optional double volumetric_weight = 3;
  optional uint64 model_id = 4;
  optional uint32 volumetric_weight_int = 5;
}

message SingleRuleCheckResult {
  required uint32 rule_type = 1;
  required uint32 check_result = 2;
  required string error_message = 3;
  required double calculate_result = 4;
  required double limit_threshold_min = 5;
  required double limit_threshold_max = 6;
  required bool include_volumetric_weight = 7;
  repeated VolumetricWeightInfo volumetric_weight_info = 8;
  required uint32 calculate_result_int = 9;
  required uint32 limit_threshold_min_int = 10;
  required uint32 limit_threshold_max_int = 11;
  optional uint32 formula = 12;
  optional uint32 volumetric_factor = 13;
}

message SingleProductCheckResult {
  required string product_id = 1;
  required uint32 check_result = 2;
  repeated SingleRuleCheckResult rule_check_detail = 3;
}

message CheckProductRuleForShoppingCartResponse {
  required RespHeader resp_header = 1;
  repeated SingleProductCheckResult product_check_result = 2;
}

message BatchCheckProductRuleForShoppingCartResponse {
  required RespHeader resp_header = 1;
  map<string, CheckProductRuleForShoppingCartResponse> check_product_rule_result_map = 2;
}

message GetLineRuleRequest {
  required ReqHeader              req_header = 1;
  required string                 line_id = 2;
}

message GetLineRuleResponse {
  required RespHeader             resp_header = 1;
  repeated RuleInfoDetail         rule_info_detail = 2;
}

message BatchGetLineRuleRequest {
  required ReqHeader              req_header = 1;
  repeated string                 line_id = 2;
}

message BatchGetLineRuleResponse {
  required RespHeader             resp_header = 1;
  repeated LineRuleInfo           line_rule_info = 2;
}

message GetProductRuleRequest {
  required ReqHeader              req_header = 1;
  required string                 product_id = 2;
}

message BatchGetProductRuleRequest {
  required ReqHeader              req_header = 1;
  repeated string                 product_id_list = 2;
}

message GetProductRuleResponse {
  required RespHeader             resp_header = 1;
  repeated RuleInfoDetail         rule_info_detail = 2;
}

message RealRuleInfoDetailList {
  repeated RealRuleInfoDetail   rule_info_detail = 1;
}

message BatchGetProductRuleResponse {
  required RespHeader                     resp_header = 1;
  map<string, RealRuleInfoDetailList>     rule_info_detail_list = 2;    // key为product id，value为其对应的rule列表
}

message CalculateBaseInfo {
  required uint32             formula                                         = 1;  // 公式
  optional uint32             volumetric_factor                               = 2;  // 体积因子
  optional uint32             sort_flag                                       = 3;  // 是否排序，0-no，1-yes
  repeated ConditionParams    condition_params                                = 4;
  optional SizeLimitInfo      parcel_limits                                   = 5;  // 包裹大小限制
  optional uint32             buyer_purchase_time                             = 6;  // 买家下单时间
}

message CalculateBaseInfoMulti {
  repeated uint32             formula                                         = 1;  // 公式
  optional uint32             volumetric_factor                               = 2;  // 体积因子
  optional uint32             sort_flag                                       = 3;  // 是否排序，0-no，1-yes
  optional SizeLimitInfo      parcel_limits                                   = 4;  // 包裹大小限制
}

message ConditionParams {
  optional double   key_param = 1;   // 复杂公式所需要的判断条件比较参数
  optional uint32   key_symbol = 2;   // 复杂公式所需要的判断条件比较符号，1-大于，2-大于等于，3-小于，4-小于等于
}

message SingleCheckLineRuleResult {
  required string                 line_id = 1;
  required uint32                 check_result = 2;    // 检查结果，0-成功，1-失败
  repeated RuleLimitDetail        limit_detail = 3;
}

message SingleCheckProductRuleResult {
  required string                 product_id = 1;
  required uint32                 check_result = 2;    // 检查结果，0-成功，1-失败
  repeated RuleLimitDetail        limit_detail = 3;
}

message RuleLimitDetail {
  required uint32     rule_type = 1;  // 限制类型
  required uint32     single_check_result = 2;  // check结果，0-通过，1-不通过
  required string     reason = 3;  // check详情
  optional double     calculate_result = 4;  // 对应Rule的具体计算结果
}

message LineRuleInfo {
  required string             line_id = 1;
  repeated RuleInfoDetail     rule_info_detail = 2;
}

message RuleInfoDetail {
  required uint32     rule_type = 1;  // 限制类型
  required uint32     limit_flag = 2;  // 是否限制，0-不限制，1-限制
  optional double     max_weight = 3;
  optional double     min_weight = 4;
  optional double     max_size = 5;
  optional double     min_size = 6;
  optional double     max_length = 7;
  optional double     min_length = 8;
  optional double     max_width = 9;
  optional double     min_width = 10;
  optional double     max_height = 11;
  optional double     min_height = 12;
  optional bool       use_parcel_lib = 13;
  optional bool       by_pass = 14;
  optional uint32     by_pass_purchase_time = 15;
}

message RealRuleInfoDetail {
  required uint64     id = 1;
  required string     product_id = 2;
  required string     region = 3;
  required uint32     rule_type = 4;  // 限制类型
  required uint32     limit_flag = 5;  // 是否限制，0-不限制，1-限制
  optional double     max_weight = 6;
  optional double     min_weight = 7;
  optional double     max_size = 8;
  optional double     min_size = 9;
  optional double     max_length = 10;
  optional double     min_length = 11;
  optional double     max_width = 12;
  optional double     min_width = 13;
  optional double     max_height = 14;
  optional double     min_height = 15;
  optional uint32     max_symbol = 16;
  optional uint32     min_symbol = 17;
  optional uint32     volumetric_factor = 18;
  optional uint32     sort_flag = 19;
  optional uint32     formula = 20;
  optional string     condition_formula_params = 21;
  optional bool       use_parcel_library = 22;
}

message BatchCalculateInfo {
  required string                         calculate_id = 1;
  required CalculateBaseInfo              base_info = 2;
  repeated BatchCalculateSkuGroupInfo     sku_group = 3;
}

message BatchCalculateSkuGroupInfo {
  required string                   group_id = 1;
  repeated SkuInfo                  sku_info = 2;
}

message BatchCalculateResult {
  required string                         calculate_id = 1;
  repeated BatchCalculateSkuGroupResult   group_result = 2;
}

message BatchCalculateSkuGroupResult {
  required string                   group_id = 1;
  required double                   result_weight = 2;
  required string                   calculate_message = 3;
}

// 批量获取产品校验重量req对象
message BatchCalculateProductValidateWeightRequest {
  required ReqHeader                                          req_header = 1;
  repeated SingleProductValidateWeightReq                     req_info_list = 2;
}

message ProductValidateWeightBaseInfo {
  required uint32                                             query_id = 1;
  required string                                             product_id = 2;
  repeated ConditionParams                                    condition_params = 3;
  optional uint32                                             sort_flag = 4;
  optional uint32                                             volumetric_factor = 5;
  optional uint32                                             buyer_purchase_time = 6;
}

message SingleProductValidateWeightReq {
  required ProductValidateWeightBaseInfo                      base_info = 1;
  required OrderInfo                                          order_info = 2;
  repeated SkuInfo                                            sku_info = 3;
}

message BatchCalculateProductValidateWeightResponse {
  required RespHeader                                         resp_header = 1;
  repeated SingleProductValidateWeightResult                  result_list = 2;
}

message SingleProductValidateWeightResult {
  required int32                                              item_code = 1;
  required string                                             item_msg = 2;
  required uint32                                             query_id = 3;
  required string                                             product_id = 4;
  required double                                             result_weight = 5;
}

message ProductSideLimitsRequest{
  required ReqHeader              req_header          = 1;
  repeated string                 product_id_list     = 2;
}

message ProductSideLimitsResponse{
  required RespHeader             resp_header            = 1;
  map<string, SizeLimitInfo>      size_limits_info       = 2;
}

message SizeLimitInfo {
  required double max_length = 1;
  required double min_length = 2;
  required double max_width = 3;
  required double min_width = 4;
  required double max_height = 5;
  required double min_height = 6;
}

message SingleCheckLaneRuleResult {
  required string                 lane_code = 1;
  required int32                  code = 2;    // 检查结果，0-成功，1-失败
  optional string                 message = 3;
  optional uint32                 rule_type = 4; // 规则类型
  repeated SingleCheckLineRuleResult  line_rule_res = 5; // 具体线规则，给reroute用
}

message CheckLaneRuleResponse {
  map<string, SingleCheckLaneRuleResult>     rule_limit_info_map = 1; // key is lane code
}

message SingleCheckLaneRuleRequest {
  required string                 unique_id = 1;     // 唯一标识，必填且不能为空
  required string                 region = 2;
  repeated SkuInfo                sku_info = 3;
  repeated string                 lane_code_list = 4;
  optional OrderInfo              order_info = 5;
  repeated SubPackageInfo         sub_package_info = 6;
  optional uint32                 buyer_purchase_time = 7;
}

message BatchCheckLaneRuleRequest {
  required ReqHeader                           req_header = 1;
  repeated SingleCheckLaneRuleRequest          check_lane_rule_list = 2;
}

message BatchCheckLaneRuleResponse {
  required RespHeader                     resp_header = 1;
  map<string, CheckLaneRuleResponse>      check_lane_rule_result_map = 2; // 批量检查结果，key为unique id
}

message BatchCheckProductRuleForParcelLibRequest {
  required ReqHeader                           req_header = 1;
  repeated ParcelItem parcel_detail = 2;
  required string region = 3;
}

message BatchCheckProductRuleForParcelLibResponse {
  required RespHeader                     resp_header = 1;
  repeated CheckResult results = 2;
}

message ParcelItem {
  required string length = 1;
  required string width = 2;
  required string height = 3;
  required string weight = 4;
  required uint64 combination_id = 5;
  required uint32 version = 6;
}

message CheckResult {
  required uint64 combination_id = 1;
  required uint32 version = 2;
  repeated ProductCheckResult check_results = 3;
}

message ProductCheckResult {
  required string product_id = 1;
  required int32 result = 2;
  required string message = 3;
}
