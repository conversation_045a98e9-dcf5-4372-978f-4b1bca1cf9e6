syntax = "proto2";

package lcos_protobuf;

enum Scene {
  UnknownScene = 0;
  PDPScene = 1;
  CheckoutScene = 2;
  ShipmentScene = 3;
  RRScene = 4;
  SearchScene = 5;
  SearchOfflineScene = 6;
}

message ReqHeader {
  required string request_id = 1;
  required string account = 2;
  required string token = 3;
  required uint32 timestamp = 4;
  required string caller_ip = 5;
  optional string deploy_region = 6;
  optional bool   log_hit = 7;
  optional Scene  scene = 8;
}

message RespHeader {
  required string request_id = 1;
  required int32  retcode = 2;
  required string message = 3;
}

message LocationInfo {
  optional uint32     state_location_id = 1;
  optional uint32     city_location_id = 2;
  optional uint32     district_location_id = 3;
  optional uint32     street_location_id = 4;
  optional string     postcode = 5;
  optional string     longitude = 6;
  optional string     latitude = 7;
}

message SkuInfo {
  optional uint64  item_id = 1;
  optional uint64  category_id = 2;
  optional double  weight = 3;
  optional uint32  quantity = 4;
  optional double  length = 5;
  optional double  width = 6;
  optional double  height = 7;
  optional double  item_price_usd = 8;
  optional double  item_price = 9;
  optional uint64  model_id = 10;
}

message SubPackageInfo {
  optional double  weight = 1; // 单位 g
  optional double  length = 2; // 单位 cm
  optional double  width = 3;  // 单位cm
  optional double  height = 4; // 单位cm
}

message OrderInfo {
  // @inject_tag: validate:"omitempty,gte=0"
  optional double  order_actual_weight = 1;
  // @inject_tag: validate:"omitempty,gte=0"
  optional double  order_volumetric_weight = 2;
  // @inject_tag: validate:"omitempty,gte=0"
  optional double  order_length = 3;
  // @inject_tag: validate:"omitempty,gte=0"
  optional double  order_width = 4;
  // @inject_tag: validate:"omitempty,gte=0"
  optional double  order_height = 5;
}

enum LocationCheckLevelEnum {
  STATE = 1;
  CITY = 2;
  DISTRICT = 3;
  STREET = 4;
}

enum PaymentMethodEnum {
  STANDARD = 1;
  COD = 2;
}

enum CollectTypeEnum {
  PICK_UP_COLLECT = 1;
  DROP_OFF_COLLECT = 2;
  B2C_COLLECT = 4;
}

enum DeliveryTypeEnum {
  TO_HOME = 8;
  TO_SITE = 16;
  TO_TWS = 32;
  TO_3PL = 64;
  TO_BRANCH = 128;
}

enum CdtScenarioEnum {
  PDP = 1;
  CheckOut = 2;
  ItemCard = 3;
}

enum MChannelRuleEnum {
  FastestFChannel = 0;
  SlowestFChannel = 1;
  WeightedAverage = 2;
  HighestADO = 3;
  WeightedAverageSPFOnly = 4;
}

enum CdtLocationLevelEnum {
  Country = -1;
  State = 0;
  City = 1;
  District = 2;
}

enum CdtScene {
  Default = 0;
  Simulation = 1;
  Offline = 2;
}