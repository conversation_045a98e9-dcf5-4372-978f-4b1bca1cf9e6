// Code generated by protoc-gen-go. DO NOT EDIT.
// source: parcel_lib_lite_model.proto

package lcos_protobuf

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ParcelLibraryDataLite struct {
	AccurateLength                *float64 `protobuf:"fixed64,1,req,name=accurate_length,json=accurateLength" json:"accurate_length,omitempty"`
	AccurateWidth                 *float64 `protobuf:"fixed64,2,req,name=accurate_width,json=accurateWidth" json:"accurate_width,omitempty"`
	AccurateHeight                *float64 `protobuf:"fixed64,3,req,name=accurate_height,json=accurateHeight" json:"accurate_height,omitempty"`
	AccurateWeight                *float64 `protobuf:"fixed64,4,req,name=accurate_weight,json=accurateWeight" json:"accurate_weight,omitempty"`
	Version                       *uint64  `protobuf:"varint,5,req,name=version" json:"version,omitempty"`
	EffectiveTime                 *uint32  `protobuf:"varint,6,req,name=effective_time,json=effectiveTime" json:"effective_time,omitempty"`
	LengthAccuracy                *float64 `protobuf:"fixed64,7,req,name=length_accuracy,json=lengthAccuracy" json:"length_accuracy,omitempty"`
	WidthAccuracy                 *float64 `protobuf:"fixed64,8,req,name=width_accuracy,json=widthAccuracy" json:"width_accuracy,omitempty"`
	HeightAccuracy                *float64 `protobuf:"fixed64,9,req,name=height_accuracy,json=heightAccuracy" json:"height_accuracy,omitempty"`
	WeightAccuracy                *float64 `protobuf:"fixed64,10,req,name=weight_accuracy,json=weightAccuracy" json:"weight_accuracy,omitempty"`
	WeightFinAccuracy             *float64 `protobuf:"fixed64,11,req,name=weight_fin_accuracy,json=weightFinAccuracy" json:"weight_fin_accuracy,omitempty"`
	VolumetricFinAccuracy         *float64 `protobuf:"fixed64,12,req,name=volumetric_fin_accuracy,json=volumetricFinAccuracy" json:"volumetric_fin_accuracy,omitempty"`
	IsBulkyParcel                 *int32   `protobuf:"varint,13,opt,name=is_bulky_parcel,json=isBulkyParcel,def=-1" json:"is_bulky_parcel,omitempty"`
	WeightEnhancedFinAccuracy     *float64 `protobuf:"fixed64,14,opt,name=weight_enhanced_fin_accuracy,json=weightEnhancedFinAccuracy,def=-1" json:"weight_enhanced_fin_accuracy,omitempty"`
	VolumetricEnhancedFinAccuracy *float64 `protobuf:"fixed64,15,opt,name=volumetric_enhanced_fin_accuracy,json=volumetricEnhancedFinAccuracy,def=-1" json:"volumetric_enhanced_fin_accuracy,omitempty"`
	XXX_NoUnkeyedLiteral          struct{} `json:"-"`
	XXX_unrecognized              []byte   `json:"-"`
	XXX_sizecache                 int32    `json:"-"`
}

func (m *ParcelLibraryDataLite) Reset()         { *m = ParcelLibraryDataLite{} }
func (m *ParcelLibraryDataLite) String() string { return proto.CompactTextString(m) }
func (*ParcelLibraryDataLite) ProtoMessage()    {}
func (*ParcelLibraryDataLite) Descriptor() ([]byte, []int) {
	return fileDescriptor_f2475affee630028, []int{0}
}

func (m *ParcelLibraryDataLite) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ParcelLibraryDataLite.Unmarshal(m, b)
}
func (m *ParcelLibraryDataLite) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ParcelLibraryDataLite.Marshal(b, m, deterministic)
}
func (m *ParcelLibraryDataLite) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ParcelLibraryDataLite.Merge(m, src)
}
func (m *ParcelLibraryDataLite) XXX_Size() int {
	return xxx_messageInfo_ParcelLibraryDataLite.Size(m)
}
func (m *ParcelLibraryDataLite) XXX_DiscardUnknown() {
	xxx_messageInfo_ParcelLibraryDataLite.DiscardUnknown(m)
}

var xxx_messageInfo_ParcelLibraryDataLite proto.InternalMessageInfo

const Default_ParcelLibraryDataLite_IsBulkyParcel int32 = -1
const Default_ParcelLibraryDataLite_WeightEnhancedFinAccuracy float64 = -1
const Default_ParcelLibraryDataLite_VolumetricEnhancedFinAccuracy float64 = -1

func (m *ParcelLibraryDataLite) GetAccurateLength() float64 {
	if m != nil && m.AccurateLength != nil {
		return *m.AccurateLength
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetAccurateWidth() float64 {
	if m != nil && m.AccurateWidth != nil {
		return *m.AccurateWidth
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetAccurateHeight() float64 {
	if m != nil && m.AccurateHeight != nil {
		return *m.AccurateHeight
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetAccurateWeight() float64 {
	if m != nil && m.AccurateWeight != nil {
		return *m.AccurateWeight
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetVersion() uint64 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetEffectiveTime() uint32 {
	if m != nil && m.EffectiveTime != nil {
		return *m.EffectiveTime
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetLengthAccuracy() float64 {
	if m != nil && m.LengthAccuracy != nil {
		return *m.LengthAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetWidthAccuracy() float64 {
	if m != nil && m.WidthAccuracy != nil {
		return *m.WidthAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetHeightAccuracy() float64 {
	if m != nil && m.HeightAccuracy != nil {
		return *m.HeightAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetWeightAccuracy() float64 {
	if m != nil && m.WeightAccuracy != nil {
		return *m.WeightAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetWeightFinAccuracy() float64 {
	if m != nil && m.WeightFinAccuracy != nil {
		return *m.WeightFinAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetVolumetricFinAccuracy() float64 {
	if m != nil && m.VolumetricFinAccuracy != nil {
		return *m.VolumetricFinAccuracy
	}
	return 0
}

func (m *ParcelLibraryDataLite) GetIsBulkyParcel() int32 {
	if m != nil && m.IsBulkyParcel != nil {
		return *m.IsBulkyParcel
	}
	return Default_ParcelLibraryDataLite_IsBulkyParcel
}

func (m *ParcelLibraryDataLite) GetWeightEnhancedFinAccuracy() float64 {
	if m != nil && m.WeightEnhancedFinAccuracy != nil {
		return *m.WeightEnhancedFinAccuracy
	}
	return Default_ParcelLibraryDataLite_WeightEnhancedFinAccuracy
}

func (m *ParcelLibraryDataLite) GetVolumetricEnhancedFinAccuracy() float64 {
	if m != nil && m.VolumetricEnhancedFinAccuracy != nil {
		return *m.VolumetricEnhancedFinAccuracy
	}
	return Default_ParcelLibraryDataLite_VolumetricEnhancedFinAccuracy
}

func init() {
	proto.RegisterType((*ParcelLibraryDataLite)(nil), "lcos_protobuf.ParcelLibraryDataLite")
}

func init() {
	proto.RegisterFile("parcel_lib_lite_model.proto", fileDescriptor_f2475affee630028)
}

var fileDescriptor_f2475affee630028 = []byte{
	// 370 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x92, 0x4d, 0x6b, 0xdb, 0x40,
	0x10, 0x86, 0x91, 0x6a, 0xd7, 0xed, 0xb6, 0xb2, 0xa8, 0x8a, 0xe9, 0x96, 0x36, 0x20, 0x02, 0xc1,
	0x22, 0x10, 0x43, 0x2e, 0x39, 0xe4, 0x96, 0x4f, 0x02, 0xf1, 0x21, 0x88, 0x80, 0x8f, 0x8b, 0xb4,
	0x5e, 0x59, 0x43, 0x56, 0x92, 0x91, 0x56, 0x16, 0xfe, 0x7b, 0xf9, 0x65, 0x41, 0x3b, 0xb2, 0x3e,
	0x82, 0x6f, 0xd2, 0x33, 0xcf, 0xbc, 0x33, 0x03, 0x4b, 0xfe, 0x6d, 0x83, 0x9c, 0x0b, 0xc9, 0x24,
	0x84, 0x4c, 0x82, 0x12, 0x2c, 0xc9, 0xd6, 0x42, 0x2e, 0xb6, 0x79, 0xa6, 0x32, 0xc7, 0x92, 0x3c,
	0x2b, 0x98, 0xfe, 0x0e, 0xcb, 0xe8, 0xf4, 0x7d, 0x4c, 0x66, 0x2f, 0x5a, 0x5f, 0x42, 0x98, 0x07,
	0xf9, 0xfe, 0x3e, 0x50, 0xc1, 0x12, 0x94, 0x70, 0xe6, 0xc4, 0x0e, 0x38, 0x2f, 0xf3, 0x40, 0x09,
	0x26, 0x45, 0xba, 0x51, 0x31, 0x35, 0x5c, 0xd3, 0x33, 0xfc, 0xe9, 0x01, 0x2f, 0x35, 0x75, 0xce,
	0x48, 0x4b, 0x58, 0x05, 0x6b, 0x15, 0x53, 0x53, 0x7b, 0xd6, 0x81, 0xae, 0x6a, 0x38, 0xc8, 0x8b,
	0x05, 0x6c, 0x62, 0x45, 0xbf, 0x0c, 0xf3, 0x9e, 0x34, 0x1d, 0x88, 0x15, 0x8a, 0xa3, 0xa1, 0xb8,
	0x42, 0x91, 0x92, 0xc9, 0x4e, 0xe4, 0x05, 0x64, 0x29, 0x1d, 0xbb, 0xa6, 0x37, 0xf2, 0x0f, 0xbf,
	0xf5, 0x4a, 0x22, 0x8a, 0x04, 0x57, 0xb0, 0x13, 0x4c, 0x41, 0x22, 0xe8, 0x57, 0xd7, 0xf4, 0x2c,
	0xdf, 0x6a, 0xe9, 0x2b, 0x24, 0xfa, 0x44, 0xbc, 0x8c, 0x61, 0x32, 0xdf, 0xd3, 0x09, 0x4e, 0x42,
	0x7c, 0xd3, 0xd0, 0x3a, 0x4f, 0x5f, 0xd6, 0x79, 0xdf, 0xf0, 0x44, 0x4d, 0x5b, 0x6d, 0x4e, 0x6c,
	0xbc, 0xac, 0xf3, 0xbe, 0x63, 0x1e, 0xe2, 0xbe, 0x58, 0x7d, 0x12, 0x09, 0x8a, 0xd5, 0x50, 0x5c,
	0x90, 0xdf, 0x8d, 0x18, 0x41, 0xda, 0xc9, 0x3f, 0xb4, 0xfc, 0x0b, 0x4b, 0x8f, 0x90, 0xb6, 0xfe,
	0x15, 0xf9, 0xb3, 0xcb, 0x64, 0x99, 0x08, 0x95, 0x03, 0x1f, 0xf6, 0xfc, 0xd4, 0x3d, 0xb3, 0xae,
	0xdc, 0xef, 0x3b, 0x27, 0x36, 0x14, 0x2c, 0x2c, 0xe5, 0xdb, 0x9e, 0xe1, 0xeb, 0xa1, 0x96, 0x6b,
	0x78, 0xe3, 0x6b, 0xf3, 0xe2, 0xd2, 0xb7, 0xa0, 0xb8, 0xad, 0x2b, 0xf8, 0x4e, 0x9c, 0x3b, 0xf2,
	0xbf, 0xd9, 0x49, 0xa4, 0x71, 0x90, 0x72, 0xb1, 0x1e, 0x0e, 0x9a, 0xba, 0x86, 0x67, 0xe8, 0xc6,
	0xbf, 0xe8, 0x3d, 0x34, 0x5a, 0x7f, 0xe0, 0x33, 0x71, 0x7b, 0x8b, 0x1e, 0x0f, 0xb2, 0xdb, 0xa0,
	0x93, 0xce, 0x3d, 0x12, 0xf6, 0x11, 0x00, 0x00, 0xff, 0xff, 0x8a, 0x88, 0x9e, 0x2a, 0xf1, 0x02,
	0x00, 0x00,
}
