// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tpl_id_line_id_ref.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type QueryThreePLIDLineIDRefRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineId               *string    `protobuf:"bytes,2,opt,name=line_id,json=lineId" json:"line_id,omitempty"`
	ThreePlId            *uint32    `protobuf:"varint,3,opt,name=three_pl_id,json=threePlId" json:"three_pl_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *QueryThreePLIDLineIDRefRequest) Reset()         { *m = QueryThreePLIDLineIDRefRequest{} }
func (m *QueryThreePLIDLineIDRefRequest) String() string { return proto.CompactTextString(m) }
func (*QueryThreePLIDLineIDRefRequest) ProtoMessage()    {}
func (*QueryThreePLIDLineIDRefRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_851706f8c37ab6b2, []int{0}
}

func (m *QueryThreePLIDLineIDRefRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryThreePLIDLineIDRefRequest.Unmarshal(m, b)
}
func (m *QueryThreePLIDLineIDRefRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryThreePLIDLineIDRefRequest.Marshal(b, m, deterministic)
}
func (m *QueryThreePLIDLineIDRefRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryThreePLIDLineIDRefRequest.Merge(m, src)
}
func (m *QueryThreePLIDLineIDRefRequest) XXX_Size() int {
	return xxx_messageInfo_QueryThreePLIDLineIDRefRequest.Size(m)
}
func (m *QueryThreePLIDLineIDRefRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryThreePLIDLineIDRefRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryThreePLIDLineIDRefRequest proto.InternalMessageInfo

func (m *QueryThreePLIDLineIDRefRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *QueryThreePLIDLineIDRefRequest) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *QueryThreePLIDLineIDRefRequest) GetThreePlId() uint32 {
	if m != nil && m.ThreePlId != nil {
		return *m.ThreePlId
	}
	return 0
}

type ThreePLIDLineIDRef struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	ThreePlId            *uint32  `protobuf:"varint,2,opt,name=three_pl_id,json=threePlId" json:"three_pl_id,omitempty"`
	LcsChannelId         *string  `protobuf:"bytes,3,opt,name=lcs_channel_id,json=lcsChannelId" json:"lcs_channel_id,omitempty"`
	ThreePlName          *string  `protobuf:"bytes,4,opt,name=three_pl_name,json=threePlName" json:"three_pl_name,omitempty"`
	ThreePlType          *uint32  `protobuf:"varint,5,opt,name=three_pl_type,json=threePlType" json:"three_pl_type,omitempty"`
	ThreePlSubType       *uint32  `protobuf:"varint,6,opt,name=three_pl_sub_type,json=threePlSubType" json:"three_pl_sub_type,omitempty"`
	ChannelType          *uint32  `protobuf:"varint,7,opt,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	LineId               *string  `protobuf:"bytes,8,opt,name=line_id,json=lineId" json:"line_id,omitempty"`
	LineName             *string  `protobuf:"bytes,9,opt,name=line_name,json=lineName" json:"line_name,omitempty"`
	LaneCode             *string  `protobuf:"bytes,10,opt,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	CrossBorderType      *uint32  `protobuf:"varint,11,opt,name=cross_border_type,json=crossBorderType" json:"cross_border_type,omitempty"`
	IsVirtual            *uint32  `protobuf:"varint,12,opt,name=is_virtual,json=isVirtual" json:"is_virtual,omitempty"`
	ShippingChannelId    *uint32  `protobuf:"varint,13,opt,name=shipping_channel_id,json=shippingChannelId" json:"shipping_channel_id,omitempty"`
	ShippingChannelName  *string  `protobuf:"bytes,14,opt,name=shipping_channel_name,json=shippingChannelName" json:"shipping_channel_name,omitempty"`
	Progress             *uint32  `protobuf:"varint,15,opt,name=progress" json:"progress,omitempty"`
	Region               *string  `protobuf:"bytes,16,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ThreePLIDLineIDRef) Reset()         { *m = ThreePLIDLineIDRef{} }
func (m *ThreePLIDLineIDRef) String() string { return proto.CompactTextString(m) }
func (*ThreePLIDLineIDRef) ProtoMessage()    {}
func (*ThreePLIDLineIDRef) Descriptor() ([]byte, []int) {
	return fileDescriptor_851706f8c37ab6b2, []int{1}
}

func (m *ThreePLIDLineIDRef) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThreePLIDLineIDRef.Unmarshal(m, b)
}
func (m *ThreePLIDLineIDRef) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThreePLIDLineIDRef.Marshal(b, m, deterministic)
}
func (m *ThreePLIDLineIDRef) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThreePLIDLineIDRef.Merge(m, src)
}
func (m *ThreePLIDLineIDRef) XXX_Size() int {
	return xxx_messageInfo_ThreePLIDLineIDRef.Size(m)
}
func (m *ThreePLIDLineIDRef) XXX_DiscardUnknown() {
	xxx_messageInfo_ThreePLIDLineIDRef.DiscardUnknown(m)
}

var xxx_messageInfo_ThreePLIDLineIDRef proto.InternalMessageInfo

func (m *ThreePLIDLineIDRef) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *ThreePLIDLineIDRef) GetThreePlId() uint32 {
	if m != nil && m.ThreePlId != nil {
		return *m.ThreePlId
	}
	return 0
}

func (m *ThreePLIDLineIDRef) GetLcsChannelId() string {
	if m != nil && m.LcsChannelId != nil {
		return *m.LcsChannelId
	}
	return ""
}

func (m *ThreePLIDLineIDRef) GetThreePlName() string {
	if m != nil && m.ThreePlName != nil {
		return *m.ThreePlName
	}
	return ""
}

func (m *ThreePLIDLineIDRef) GetThreePlType() uint32 {
	if m != nil && m.ThreePlType != nil {
		return *m.ThreePlType
	}
	return 0
}

func (m *ThreePLIDLineIDRef) GetThreePlSubType() uint32 {
	if m != nil && m.ThreePlSubType != nil {
		return *m.ThreePlSubType
	}
	return 0
}

func (m *ThreePLIDLineIDRef) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

func (m *ThreePLIDLineIDRef) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *ThreePLIDLineIDRef) GetLineName() string {
	if m != nil && m.LineName != nil {
		return *m.LineName
	}
	return ""
}

func (m *ThreePLIDLineIDRef) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *ThreePLIDLineIDRef) GetCrossBorderType() uint32 {
	if m != nil && m.CrossBorderType != nil {
		return *m.CrossBorderType
	}
	return 0
}

func (m *ThreePLIDLineIDRef) GetIsVirtual() uint32 {
	if m != nil && m.IsVirtual != nil {
		return *m.IsVirtual
	}
	return 0
}

func (m *ThreePLIDLineIDRef) GetShippingChannelId() uint32 {
	if m != nil && m.ShippingChannelId != nil {
		return *m.ShippingChannelId
	}
	return 0
}

func (m *ThreePLIDLineIDRef) GetShippingChannelName() string {
	if m != nil && m.ShippingChannelName != nil {
		return *m.ShippingChannelName
	}
	return ""
}

func (m *ThreePLIDLineIDRef) GetProgress() uint32 {
	if m != nil && m.Progress != nil {
		return *m.Progress
	}
	return 0
}

func (m *ThreePLIDLineIDRef) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type QueryThreePLIDLineIDRefResponse struct {
	RespHeader           *RespHeader           `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ThreePlIdLineIdRefs  []*ThreePLIDLineIDRef `protobuf:"bytes,2,rep,name=three_pl_id_line_id_refs,json=threePlIdLineIdRefs" json:"three_pl_id_line_id_refs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *QueryThreePLIDLineIDRefResponse) Reset()         { *m = QueryThreePLIDLineIDRefResponse{} }
func (m *QueryThreePLIDLineIDRefResponse) String() string { return proto.CompactTextString(m) }
func (*QueryThreePLIDLineIDRefResponse) ProtoMessage()    {}
func (*QueryThreePLIDLineIDRefResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_851706f8c37ab6b2, []int{2}
}

func (m *QueryThreePLIDLineIDRefResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryThreePLIDLineIDRefResponse.Unmarshal(m, b)
}
func (m *QueryThreePLIDLineIDRefResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryThreePLIDLineIDRefResponse.Marshal(b, m, deterministic)
}
func (m *QueryThreePLIDLineIDRefResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryThreePLIDLineIDRefResponse.Merge(m, src)
}
func (m *QueryThreePLIDLineIDRefResponse) XXX_Size() int {
	return xxx_messageInfo_QueryThreePLIDLineIDRefResponse.Size(m)
}
func (m *QueryThreePLIDLineIDRefResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryThreePLIDLineIDRefResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryThreePLIDLineIDRefResponse proto.InternalMessageInfo

func (m *QueryThreePLIDLineIDRefResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *QueryThreePLIDLineIDRefResponse) GetThreePlIdLineIdRefs() []*ThreePLIDLineIDRef {
	if m != nil {
		return m.ThreePlIdLineIdRefs
	}
	return nil
}

func init() {
	proto.RegisterType((*QueryThreePLIDLineIDRefRequest)(nil), "lcos_protobuf.QueryThreePLIDLineIDRefRequest")
	proto.RegisterType((*ThreePLIDLineIDRef)(nil), "lcos_protobuf.ThreePLIDLineIDRef")
	proto.RegisterType((*QueryThreePLIDLineIDRefResponse)(nil), "lcos_protobuf.QueryThreePLIDLineIDRefResponse")
}

func init() {
	proto.RegisterFile("tpl_id_line_id_ref.proto", fileDescriptor_851706f8c37ab6b2)
}

var fileDescriptor_851706f8c37ab6b2 = []byte{
	// 533 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x53, 0x4d, 0x6f, 0xd3, 0x40,
	0x10, 0xc5, 0x6e, 0x48, 0xe3, 0x71, 0x3e, 0xc8, 0x46, 0xc0, 0x36, 0x88, 0x92, 0x46, 0x1c, 0x02,
	0x12, 0x39, 0xe4, 0x82, 0xc4, 0x91, 0xf6, 0x40, 0xa4, 0x08, 0x15, 0x37, 0x42, 0xdc, 0x2c, 0xc7,
	0x9e, 0x24, 0x2b, 0xb9, 0xb6, 0xb3, 0x63, 0x57, 0x8a, 0xf8, 0x17, 0x9c, 0xf8, 0x25, 0xf0, 0xf7,
	0x50, 0xc6, 0x8e, 0xc9, 0x07, 0x45, 0x3d, 0x79, 0x67, 0xde, 0xf3, 0x9b, 0xb7, 0x4f, 0xb3, 0x20,
	0xd3, 0x24, 0x74, 0x55, 0xe0, 0x86, 0x2a, 0xc2, 0xcd, 0x57, 0xe3, 0x7c, 0x98, 0xe8, 0x38, 0x8d,
	0x45, 0x23, 0xf4, 0x63, 0x72, 0xf9, 0x3c, 0xcb, 0xe6, 0xdd, 0x16, 0x97, 0x33, 0x8f, 0x30, 0xc7,
	0xfb, 0x3f, 0x0c, 0x38, 0xff, 0x92, 0xa1, 0x5e, 0x4f, 0x97, 0x1a, 0xf1, 0x7a, 0x32, 0xbe, 0x9a,
	0xa8, 0x08, 0xc7, 0x57, 0x0e, 0xce, 0x1d, 0x5c, 0x65, 0x48, 0xa9, 0x78, 0x0f, 0xa0, 0x71, 0xe5,
	0x2e, 0xd1, 0x0b, 0x50, 0x4b, 0xa3, 0x67, 0x0e, 0xec, 0x91, 0x1c, 0xee, 0xe9, 0x0e, 0x1d, 0x5c,
	0x7d, 0x62, 0xdc, 0xb1, 0xf4, 0xf6, 0x28, 0x9e, 0xc3, 0x69, 0x61, 0x48, 0x9a, 0x3d, 0x63, 0x60,
	0x39, 0xd5, 0x4d, 0x39, 0x0e, 0xc4, 0x39, 0xd8, 0xe9, 0x66, 0x9c, 0xcb, 0xae, 0xe5, 0x49, 0xcf,
	0x18, 0x34, 0x1c, 0x8b, 0x5b, 0xd7, 0xe1, 0x38, 0xe8, 0xff, 0xaa, 0x80, 0x38, 0xf6, 0x23, 0x9a,
	0x60, 0xaa, 0x40, 0x1a, 0x3d, 0x63, 0x50, 0x71, 0x4c, 0x75, 0x24, 0x63, 0x1e, 0xc8, 0x88, 0xd7,
	0xd0, 0x0c, 0x7d, 0x72, 0xfd, 0xa5, 0x17, 0x45, 0x58, 0x4e, 0xb2, 0x9c, 0x7a, 0xe8, 0xd3, 0x65,
	0xde, 0x1c, 0x07, 0xa2, 0x0f, 0x8d, 0x52, 0x25, 0xf2, 0x6e, 0x51, 0x56, 0x98, 0x64, 0x17, 0x3a,
	0x9f, 0xbd, 0x5b, 0xdc, 0xe3, 0xa4, 0xeb, 0x04, 0xe5, 0x63, 0x9e, 0xb5, 0xe5, 0x4c, 0xd7, 0x09,
	0x8a, 0x37, 0xd0, 0x2e, 0x39, 0x94, 0xcd, 0x72, 0x5e, 0x95, 0x79, 0xcd, 0x82, 0x77, 0x93, 0xcd,
	0x98, 0x7a, 0x01, 0xf5, 0xad, 0x29, 0x66, 0x9d, 0xe6, 0x6a, 0x45, 0x8f, 0x29, 0x3b, 0xd9, 0xd5,
	0xf6, 0xb2, 0x7b, 0x01, 0x16, 0x03, 0x6c, 0xd5, 0x62, 0xa8, 0xb6, 0x69, 0xb0, 0xcf, 0x0d, 0xe8,
	0x45, 0xe8, 0xfa, 0x71, 0x80, 0x12, 0x0a, 0xd0, 0x8b, 0xf0, 0x32, 0x0e, 0x50, 0xbc, 0x85, 0xb6,
	0xaf, 0x63, 0x22, 0x77, 0x16, 0xeb, 0x00, 0x75, 0x3e, 0xda, 0xe6, 0xd1, 0x2d, 0x06, 0x3e, 0x72,
	0x9f, 0xc7, 0xbf, 0x04, 0x50, 0xe4, 0xde, 0x29, 0x9d, 0x66, 0x5e, 0x28, 0xeb, 0x79, 0xb2, 0x8a,
	0xbe, 0xe6, 0x0d, 0x31, 0x84, 0x0e, 0x2d, 0x55, 0x92, 0xa8, 0x68, 0xb1, 0x1b, 0x6f, 0x83, 0x79,
	0xed, 0x2d, 0xf4, 0x37, 0xe3, 0x11, 0x3c, 0x3d, 0xe2, 0xf3, 0x05, 0x9a, 0xec, 0xb1, 0x73, 0xf0,
	0x07, 0xdf, 0xa5, 0x0b, 0xb5, 0x44, 0xc7, 0x0b, 0x8d, 0x44, 0xb2, 0xc5, 0xc2, 0x65, 0x2d, 0x9e,
	0x41, 0x55, 0xe3, 0x42, 0xc5, 0x91, 0x7c, 0x92, 0x87, 0x93, 0x57, 0xfd, 0xdf, 0x06, 0xbc, 0xba,
	0x77, 0x9b, 0x29, 0x89, 0x23, 0x42, 0xf1, 0x01, 0x6c, 0x8d, 0x94, 0xec, 0xef, 0xf3, 0xd9, 0xd1,
	0x3e, 0x53, 0x52, 0x2c, 0x34, 0xe8, 0xf2, 0x2c, 0xbe, 0x81, 0xdc, 0xd9, 0xb8, 0xdd, 0xe7, 0x46,
	0xd2, 0xec, 0x9d, 0x0c, 0xec, 0xd1, 0xc5, 0x81, 0xd0, 0x3f, 0x8c, 0x74, 0xca, 0x0d, 0xe5, 0x5e,
	0xe0, 0xe0, 0x9c, 0x46, 0x3f, 0x0d, 0x38, 0x9b, 0xf8, 0x31, 0x4d, 0xf7, 0xb8, 0x37, 0xa8, 0xef,
	0x94, 0x8f, 0xe2, 0x3b, 0xc8, 0x7b, 0xae, 0x45, 0xe2, 0xdd, 0xc1, 0xc4, 0xff, 0xbf, 0xe6, 0xee,
	0xf0, 0xa1, 0xf4, 0x3c, 0xae, 0xfe, 0xa3, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xe2, 0x43, 0x80,
	0x05, 0x5d, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosTPLIDLineIDRefServiceClient is the client API for LcosTPLIDLineIDRefService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosTPLIDLineIDRefServiceClient interface {
	// 通过three pl id或者line id获取到refs
	QueryThreePLIDLineIDRefs(ctx context.Context, in *QueryThreePLIDLineIDRefRequest, opts ...grpc.CallOption) (*QueryThreePLIDLineIDRefResponse, error)
}

type lcosTPLIDLineIDRefServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosTPLIDLineIDRefServiceClient(cc grpc.ClientConnInterface) LcosTPLIDLineIDRefServiceClient {
	return &lcosTPLIDLineIDRefServiceClient{cc}
}

func (c *lcosTPLIDLineIDRefServiceClient) QueryThreePLIDLineIDRefs(ctx context.Context, in *QueryThreePLIDLineIDRefRequest, opts ...grpc.CallOption) (*QueryThreePLIDLineIDRefResponse, error) {
	out := new(QueryThreePLIDLineIDRefResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosTPLIDLineIDRefService/QueryThreePLIDLineIDRefs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosTPLIDLineIDRefServiceServer is the server API for LcosTPLIDLineIDRefService service.
type LcosTPLIDLineIDRefServiceServer interface {
	// 通过three pl id或者line id获取到refs
	QueryThreePLIDLineIDRefs(context.Context, *QueryThreePLIDLineIDRefRequest) (*QueryThreePLIDLineIDRefResponse, error)
}

// UnimplementedLcosTPLIDLineIDRefServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLcosTPLIDLineIDRefServiceServer struct {
}

func (*UnimplementedLcosTPLIDLineIDRefServiceServer) QueryThreePLIDLineIDRefs(ctx context.Context, req *QueryThreePLIDLineIDRefRequest) (*QueryThreePLIDLineIDRefResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryThreePLIDLineIDRefs not implemented")
}

func RegisterLcosTPLIDLineIDRefServiceServer(s *grpc.Server, srv LcosTPLIDLineIDRefServiceServer) {
	s.RegisterService(&_LcosTPLIDLineIDRefService_serviceDesc, srv)
}

func _LcosTPLIDLineIDRefService_QueryThreePLIDLineIDRefs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryThreePLIDLineIDRefRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosTPLIDLineIDRefServiceServer).QueryThreePLIDLineIDRefs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosTPLIDLineIDRefService/QueryThreePLIDLineIDRefs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosTPLIDLineIDRefServiceServer).QueryThreePLIDLineIDRefs(ctx, req.(*QueryThreePLIDLineIDRefRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosTPLIDLineIDRefService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosTPLIDLineIDRefService",
	HandlerType: (*LcosTPLIDLineIDRefServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryThreePLIDLineIDRefs",
			Handler:    _LcosTPLIDLineIDRefService_QueryThreePLIDLineIDRefs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tpl_id_line_id_ref.proto",
}
