// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_one_api.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// @core
type LineWaybillCheckAttr struct {
	ReqNo                *string                        `protobuf:"bytes,1,req,name=req_no,json=reqNo" json:"req_no,omitempty"`
	LineId               *string                        `protobuf:"bytes,2,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	IsCheckServiceable   *uint32                        `protobuf:"varint,3,opt,name=is_check_serviceable,json=isCheckServiceable" json:"is_check_serviceable,omitempty"`
	IsCheckPickupTime    *uint32                        `protobuf:"varint,4,opt,name=is_check_pickup_time,json=isCheckPickupTime" json:"is_check_pickup_time,omitempty"`
	IsCheckLineRule      *uint32                        `protobuf:"varint,5,opt,name=is_check_line_rule,json=isCheckLineRule" json:"is_check_line_rule,omitempty"`
	ServiceableInfo      *CheckServiceableAreaBaseInfo2 `protobuf:"bytes,6,opt,name=serviceable_info,json=serviceableInfo" json:"serviceable_info,omitempty"`
	PickupInfo           *LocationInfo                  `protobuf:"bytes,7,opt,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	DeliverInfo          *LocationInfo                  `protobuf:"bytes,8,opt,name=deliver_info,json=deliverInfo" json:"deliver_info,omitempty"`
	PickupTimeInfo       *CheckPickupTimeInfo           `protobuf:"bytes,9,opt,name=pickup_time_info,json=pickupTimeInfo" json:"pickup_time_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *LineWaybillCheckAttr) Reset()         { *m = LineWaybillCheckAttr{} }
func (m *LineWaybillCheckAttr) String() string { return proto.CompactTextString(m) }
func (*LineWaybillCheckAttr) ProtoMessage()    {}
func (*LineWaybillCheckAttr) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{0}
}

func (m *LineWaybillCheckAttr) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineWaybillCheckAttr.Unmarshal(m, b)
}
func (m *LineWaybillCheckAttr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineWaybillCheckAttr.Marshal(b, m, deterministic)
}
func (m *LineWaybillCheckAttr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineWaybillCheckAttr.Merge(m, src)
}
func (m *LineWaybillCheckAttr) XXX_Size() int {
	return xxx_messageInfo_LineWaybillCheckAttr.Size(m)
}
func (m *LineWaybillCheckAttr) XXX_DiscardUnknown() {
	xxx_messageInfo_LineWaybillCheckAttr.DiscardUnknown(m)
}

var xxx_messageInfo_LineWaybillCheckAttr proto.InternalMessageInfo

func (m *LineWaybillCheckAttr) GetReqNo() string {
	if m != nil && m.ReqNo != nil {
		return *m.ReqNo
	}
	return ""
}

func (m *LineWaybillCheckAttr) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *LineWaybillCheckAttr) GetIsCheckServiceable() uint32 {
	if m != nil && m.IsCheckServiceable != nil {
		return *m.IsCheckServiceable
	}
	return 0
}

func (m *LineWaybillCheckAttr) GetIsCheckPickupTime() uint32 {
	if m != nil && m.IsCheckPickupTime != nil {
		return *m.IsCheckPickupTime
	}
	return 0
}

func (m *LineWaybillCheckAttr) GetIsCheckLineRule() uint32 {
	if m != nil && m.IsCheckLineRule != nil {
		return *m.IsCheckLineRule
	}
	return 0
}

func (m *LineWaybillCheckAttr) GetServiceableInfo() *CheckServiceableAreaBaseInfo2 {
	if m != nil {
		return m.ServiceableInfo
	}
	return nil
}

func (m *LineWaybillCheckAttr) GetPickupInfo() *LocationInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

func (m *LineWaybillCheckAttr) GetDeliverInfo() *LocationInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

func (m *LineWaybillCheckAttr) GetPickupTimeInfo() *CheckPickupTimeInfo {
	if m != nil {
		return m.PickupTimeInfo
	}
	return nil
}

// @core
type GetTimeslotForOneApiRequest struct {
	PickupTime           *uint32  `protobuf:"varint,1,req,name=pickup_time,json=pickupTime" json:"pickup_time,omitempty"`
	Country              *string  `protobuf:"bytes,2,req,name=country" json:"country,omitempty"`
	PickupTimeRangeId    *uint32  `protobuf:"varint,3,req,name=pickup_time_range_id,json=pickupTimeRangeId" json:"pickup_time_range_id,omitempty"`
	StartTime            *uint32  `protobuf:"varint,4,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	MerchantType         *uint32  `protobuf:"varint,5,opt,name=merchant_type,json=merchantType" json:"merchant_type,omitempty"`
	AccountGroup         *uint32  `protobuf:"varint,6,opt,name=account_group,json=accountGroup" json:"account_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTimeslotForOneApiRequest) Reset()         { *m = GetTimeslotForOneApiRequest{} }
func (m *GetTimeslotForOneApiRequest) String() string { return proto.CompactTextString(m) }
func (*GetTimeslotForOneApiRequest) ProtoMessage()    {}
func (*GetTimeslotForOneApiRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{1}
}

func (m *GetTimeslotForOneApiRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTimeslotForOneApiRequest.Unmarshal(m, b)
}
func (m *GetTimeslotForOneApiRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTimeslotForOneApiRequest.Marshal(b, m, deterministic)
}
func (m *GetTimeslotForOneApiRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTimeslotForOneApiRequest.Merge(m, src)
}
func (m *GetTimeslotForOneApiRequest) XXX_Size() int {
	return xxx_messageInfo_GetTimeslotForOneApiRequest.Size(m)
}
func (m *GetTimeslotForOneApiRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTimeslotForOneApiRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTimeslotForOneApiRequest proto.InternalMessageInfo

func (m *GetTimeslotForOneApiRequest) GetPickupTime() uint32 {
	if m != nil && m.PickupTime != nil {
		return *m.PickupTime
	}
	return 0
}

func (m *GetTimeslotForOneApiRequest) GetCountry() string {
	if m != nil && m.Country != nil {
		return *m.Country
	}
	return ""
}

func (m *GetTimeslotForOneApiRequest) GetPickupTimeRangeId() uint32 {
	if m != nil && m.PickupTimeRangeId != nil {
		return *m.PickupTimeRangeId
	}
	return 0
}

func (m *GetTimeslotForOneApiRequest) GetStartTime() uint32 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *GetTimeslotForOneApiRequest) GetMerchantType() uint32 {
	if m != nil && m.MerchantType != nil {
		return *m.MerchantType
	}
	return 0
}

func (m *GetTimeslotForOneApiRequest) GetAccountGroup() uint32 {
	if m != nil && m.AccountGroup != nil {
		return *m.AccountGroup
	}
	return 0
}

// @core
type LineWaybillGetAttr struct {
	ReqNo                *string                      `protobuf:"bytes,1,req,name=req_no,json=reqNo" json:"req_no,omitempty"`
	LineId               *string                      `protobuf:"bytes,2,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	GetTimeslotInfo      *GetTimeslotForOneApiRequest `protobuf:"bytes,3,opt,name=get_timeslot_info,json=getTimeslotInfo" json:"get_timeslot_info,omitempty"`
	DestBranchGetReq     *BranchGetReq                `protobuf:"bytes,4,opt,name=dest_branch_get_req,json=destBranchGetReq" json:"dest_branch_get_req,omitempty"`
	TwStoreGetReq        *TwStoreGetReq               `protobuf:"bytes,5,opt,name=tw_store_get_req,json=twStoreGetReq" json:"tw_store_get_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *LineWaybillGetAttr) Reset()         { *m = LineWaybillGetAttr{} }
func (m *LineWaybillGetAttr) String() string { return proto.CompactTextString(m) }
func (*LineWaybillGetAttr) ProtoMessage()    {}
func (*LineWaybillGetAttr) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{2}
}

func (m *LineWaybillGetAttr) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineWaybillGetAttr.Unmarshal(m, b)
}
func (m *LineWaybillGetAttr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineWaybillGetAttr.Marshal(b, m, deterministic)
}
func (m *LineWaybillGetAttr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineWaybillGetAttr.Merge(m, src)
}
func (m *LineWaybillGetAttr) XXX_Size() int {
	return xxx_messageInfo_LineWaybillGetAttr.Size(m)
}
func (m *LineWaybillGetAttr) XXX_DiscardUnknown() {
	xxx_messageInfo_LineWaybillGetAttr.DiscardUnknown(m)
}

var xxx_messageInfo_LineWaybillGetAttr proto.InternalMessageInfo

func (m *LineWaybillGetAttr) GetReqNo() string {
	if m != nil && m.ReqNo != nil {
		return *m.ReqNo
	}
	return ""
}

func (m *LineWaybillGetAttr) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *LineWaybillGetAttr) GetGetTimeslotInfo() *GetTimeslotForOneApiRequest {
	if m != nil {
		return m.GetTimeslotInfo
	}
	return nil
}

func (m *LineWaybillGetAttr) GetDestBranchGetReq() *BranchGetReq {
	if m != nil {
		return m.DestBranchGetReq
	}
	return nil
}

func (m *LineWaybillGetAttr) GetTwStoreGetReq() *TwStoreGetReq {
	if m != nil {
		return m.TwStoreGetReq
	}
	return nil
}

// @core
type BranchGetReq struct {
	BranchId             *uint64  `protobuf:"varint,1,opt,name=branch_id,json=branchId" json:"branch_id,omitempty"`
	BranchGroupId        *uint32  `protobuf:"varint,2,opt,name=branch_group_id,json=branchGroupId" json:"branch_group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BranchGetReq) Reset()         { *m = BranchGetReq{} }
func (m *BranchGetReq) String() string { return proto.CompactTextString(m) }
func (*BranchGetReq) ProtoMessage()    {}
func (*BranchGetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{3}
}

func (m *BranchGetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BranchGetReq.Unmarshal(m, b)
}
func (m *BranchGetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BranchGetReq.Marshal(b, m, deterministic)
}
func (m *BranchGetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BranchGetReq.Merge(m, src)
}
func (m *BranchGetReq) XXX_Size() int {
	return xxx_messageInfo_BranchGetReq.Size(m)
}
func (m *BranchGetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BranchGetReq.DiscardUnknown(m)
}

var xxx_messageInfo_BranchGetReq proto.InternalMessageInfo

func (m *BranchGetReq) GetBranchId() uint64 {
	if m != nil && m.BranchId != nil {
		return *m.BranchId
	}
	return 0
}

func (m *BranchGetReq) GetBranchGroupId() uint32 {
	if m != nil && m.BranchGroupId != nil {
		return *m.BranchGroupId
	}
	return 0
}

// @core
type TwStoreGetReq struct {
	RequestList          []string `protobuf:"bytes,1,rep,name=request_list,json=requestList" json:"request_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TwStoreGetReq) Reset()         { *m = TwStoreGetReq{} }
func (m *TwStoreGetReq) String() string { return proto.CompactTextString(m) }
func (*TwStoreGetReq) ProtoMessage()    {}
func (*TwStoreGetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{4}
}

func (m *TwStoreGetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TwStoreGetReq.Unmarshal(m, b)
}
func (m *TwStoreGetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TwStoreGetReq.Marshal(b, m, deterministic)
}
func (m *TwStoreGetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TwStoreGetReq.Merge(m, src)
}
func (m *TwStoreGetReq) XXX_Size() int {
	return xxx_messageInfo_TwStoreGetReq.Size(m)
}
func (m *TwStoreGetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TwStoreGetReq.DiscardUnknown(m)
}

var xxx_messageInfo_TwStoreGetReq proto.InternalMessageInfo

func (m *TwStoreGetReq) GetRequestList() []string {
	if m != nil {
		return m.RequestList
	}
	return nil
}

// @core
type OneApiInfo struct {
	OrderInfo                *OrderInfo              `protobuf:"bytes,1,opt,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	SkuInfo                  []*SkuInfo              `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	CheckLineWaybillAttrList []*LineWaybillCheckAttr `protobuf:"bytes,3,rep,name=check_line_waybill_attr_list,json=checkLineWaybillAttrList" json:"check_line_waybill_attr_list,omitempty"`
	GetLineWaybillAttrList   []*LineWaybillGetAttr   `protobuf:"bytes,4,rep,name=get_line_waybill_attr_list,json=getLineWaybillAttrList" json:"get_line_waybill_attr_list,omitempty"`
	SubPackageInfo           []*SubPackageInfo       `protobuf:"bytes,5,rep,name=sub_package_info,json=subPackageInfo" json:"sub_package_info,omitempty"`
	BuyerPurchaseTime        *uint32                 `protobuf:"varint,6,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                `json:"-"`
	XXX_unrecognized         []byte                  `json:"-"`
	XXX_sizecache            int32                   `json:"-"`
}

func (m *OneApiInfo) Reset()         { *m = OneApiInfo{} }
func (m *OneApiInfo) String() string { return proto.CompactTextString(m) }
func (*OneApiInfo) ProtoMessage()    {}
func (*OneApiInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{5}
}

func (m *OneApiInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiInfo.Unmarshal(m, b)
}
func (m *OneApiInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiInfo.Marshal(b, m, deterministic)
}
func (m *OneApiInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiInfo.Merge(m, src)
}
func (m *OneApiInfo) XXX_Size() int {
	return xxx_messageInfo_OneApiInfo.Size(m)
}
func (m *OneApiInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiInfo proto.InternalMessageInfo

func (m *OneApiInfo) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *OneApiInfo) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func (m *OneApiInfo) GetCheckLineWaybillAttrList() []*LineWaybillCheckAttr {
	if m != nil {
		return m.CheckLineWaybillAttrList
	}
	return nil
}

func (m *OneApiInfo) GetGetLineWaybillAttrList() []*LineWaybillGetAttr {
	if m != nil {
		return m.GetLineWaybillAttrList
	}
	return nil
}

func (m *OneApiInfo) GetSubPackageInfo() []*SubPackageInfo {
	if m != nil {
		return m.SubPackageInfo
	}
	return nil
}

func (m *OneApiInfo) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

// @core
type OneApiRequest struct {
	ReqHeader            *ReqHeader  `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	OneApiInfo           *OneApiInfo `protobuf:"bytes,2,req,name=one_api_info,json=oneApiInfo" json:"one_api_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *OneApiRequest) Reset()         { *m = OneApiRequest{} }
func (m *OneApiRequest) String() string { return proto.CompactTextString(m) }
func (*OneApiRequest) ProtoMessage()    {}
func (*OneApiRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{6}
}

func (m *OneApiRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiRequest.Unmarshal(m, b)
}
func (m *OneApiRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiRequest.Marshal(b, m, deterministic)
}
func (m *OneApiRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiRequest.Merge(m, src)
}
func (m *OneApiRequest) XXX_Size() int {
	return xxx_messageInfo_OneApiRequest.Size(m)
}
func (m *OneApiRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiRequest proto.InternalMessageInfo

func (m *OneApiRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *OneApiRequest) GetOneApiInfo() *OneApiInfo {
	if m != nil {
		return m.OneApiInfo
	}
	return nil
}

// @core
type OneApiCheckInfo struct {
	Retcode              *int32   `protobuf:"varint,1,opt,name=retcode" json:"retcode,omitempty"`
	Message              *string  `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneApiCheckInfo) Reset()         { *m = OneApiCheckInfo{} }
func (m *OneApiCheckInfo) String() string { return proto.CompactTextString(m) }
func (*OneApiCheckInfo) ProtoMessage()    {}
func (*OneApiCheckInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{7}
}

func (m *OneApiCheckInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiCheckInfo.Unmarshal(m, b)
}
func (m *OneApiCheckInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiCheckInfo.Marshal(b, m, deterministic)
}
func (m *OneApiCheckInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiCheckInfo.Merge(m, src)
}
func (m *OneApiCheckInfo) XXX_Size() int {
	return xxx_messageInfo_OneApiCheckInfo.Size(m)
}
func (m *OneApiCheckInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiCheckInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiCheckInfo proto.InternalMessageInfo

func (m *OneApiCheckInfo) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *OneApiCheckInfo) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

// @core
type OneApiLineRuleInfo struct {
	Retcode              *int32                       `protobuf:"varint,1,opt,name=retcode" json:"retcode,omitempty"`
	Message              *string                      `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	RuleLimitInfo        []*SingleCheckLineRuleResult `protobuf:"bytes,3,rep,name=rule_limit_info,json=ruleLimitInfo" json:"rule_limit_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *OneApiLineRuleInfo) Reset()         { *m = OneApiLineRuleInfo{} }
func (m *OneApiLineRuleInfo) String() string { return proto.CompactTextString(m) }
func (*OneApiLineRuleInfo) ProtoMessage()    {}
func (*OneApiLineRuleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{8}
}

func (m *OneApiLineRuleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiLineRuleInfo.Unmarshal(m, b)
}
func (m *OneApiLineRuleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiLineRuleInfo.Marshal(b, m, deterministic)
}
func (m *OneApiLineRuleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiLineRuleInfo.Merge(m, src)
}
func (m *OneApiLineRuleInfo) XXX_Size() int {
	return xxx_messageInfo_OneApiLineRuleInfo.Size(m)
}
func (m *OneApiLineRuleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiLineRuleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiLineRuleInfo proto.InternalMessageInfo

func (m *OneApiLineRuleInfo) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *OneApiLineRuleInfo) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *OneApiLineRuleInfo) GetRuleLimitInfo() []*SingleCheckLineRuleResult {
	if m != nil {
		return m.RuleLimitInfo
	}
	return nil
}

// @core
type CheckResults struct {
	ServiceableCheckResult map[string]*CheckLineServiceableAreaResponse `protobuf:"bytes,1,rep,name=serviceable_check_result,json=serviceableCheckResult" json:"serviceable_check_result,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	PickupTimeCheckResult  map[string]*CheckPickupTimeResponse          `protobuf:"bytes,2,rep,name=pickup_time_check_result,json=pickupTimeCheckResult" json:"pickup_time_check_result,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	LineRuleCheckResult    map[string]*CheckLineRuleResponse            `protobuf:"bytes,3,rep,name=line_rule_check_result,json=lineRuleCheckResult" json:"line_rule_check_result,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral   struct{}                                     `json:"-"`
	XXX_unrecognized       []byte                                       `json:"-"`
	XXX_sizecache          int32                                        `json:"-"`
}

func (m *CheckResults) Reset()         { *m = CheckResults{} }
func (m *CheckResults) String() string { return proto.CompactTextString(m) }
func (*CheckResults) ProtoMessage()    {}
func (*CheckResults) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{9}
}

func (m *CheckResults) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckResults.Unmarshal(m, b)
}
func (m *CheckResults) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckResults.Marshal(b, m, deterministic)
}
func (m *CheckResults) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckResults.Merge(m, src)
}
func (m *CheckResults) XXX_Size() int {
	return xxx_messageInfo_CheckResults.Size(m)
}
func (m *CheckResults) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckResults.DiscardUnknown(m)
}

var xxx_messageInfo_CheckResults proto.InternalMessageInfo

func (m *CheckResults) GetServiceableCheckResult() map[string]*CheckLineServiceableAreaResponse {
	if m != nil {
		return m.ServiceableCheckResult
	}
	return nil
}

func (m *CheckResults) GetPickupTimeCheckResult() map[string]*CheckPickupTimeResponse {
	if m != nil {
		return m.PickupTimeCheckResult
	}
	return nil
}

func (m *CheckResults) GetLineRuleCheckResult() map[string]*CheckLineRuleResponse {
	if m != nil {
		return m.LineRuleCheckResult
	}
	return nil
}

// @core
type OneApiPickupTimeInfo struct {
	PickupStartTime      *uint32  `protobuf:"varint,1,req,name=pickup_start_time,json=pickupStartTime" json:"pickup_start_time,omitempty"`
	PickupEndTime        *uint32  `protobuf:"varint,2,req,name=pickup_end_time,json=pickupEndTime" json:"pickup_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneApiPickupTimeInfo) Reset()         { *m = OneApiPickupTimeInfo{} }
func (m *OneApiPickupTimeInfo) String() string { return proto.CompactTextString(m) }
func (*OneApiPickupTimeInfo) ProtoMessage()    {}
func (*OneApiPickupTimeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{10}
}

func (m *OneApiPickupTimeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiPickupTimeInfo.Unmarshal(m, b)
}
func (m *OneApiPickupTimeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiPickupTimeInfo.Marshal(b, m, deterministic)
}
func (m *OneApiPickupTimeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiPickupTimeInfo.Merge(m, src)
}
func (m *OneApiPickupTimeInfo) XXX_Size() int {
	return xxx_messageInfo_OneApiPickupTimeInfo.Size(m)
}
func (m *OneApiPickupTimeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiPickupTimeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiPickupTimeInfo proto.InternalMessageInfo

func (m *OneApiPickupTimeInfo) GetPickupStartTime() uint32 {
	if m != nil && m.PickupStartTime != nil {
		return *m.PickupStartTime
	}
	return 0
}

func (m *OneApiPickupTimeInfo) GetPickupEndTime() uint32 {
	if m != nil && m.PickupEndTime != nil {
		return *m.PickupEndTime
	}
	return 0
}

// @core
type OneApiGetPickupTimeslotsResponse struct {
	Retcode              *int32                `protobuf:"varint,1,req,name=retcode" json:"retcode,omitempty"`
	Message              *string               `protobuf:"bytes,2,req,name=message" json:"message,omitempty"`
	PickupTimeInfo       *OneApiPickupTimeInfo `protobuf:"bytes,3,opt,name=pickup_time_info,json=pickupTimeInfo" json:"pickup_time_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *OneApiGetPickupTimeslotsResponse) Reset()         { *m = OneApiGetPickupTimeslotsResponse{} }
func (m *OneApiGetPickupTimeslotsResponse) String() string { return proto.CompactTextString(m) }
func (*OneApiGetPickupTimeslotsResponse) ProtoMessage()    {}
func (*OneApiGetPickupTimeslotsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{11}
}

func (m *OneApiGetPickupTimeslotsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiGetPickupTimeslotsResponse.Unmarshal(m, b)
}
func (m *OneApiGetPickupTimeslotsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiGetPickupTimeslotsResponse.Marshal(b, m, deterministic)
}
func (m *OneApiGetPickupTimeslotsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiGetPickupTimeslotsResponse.Merge(m, src)
}
func (m *OneApiGetPickupTimeslotsResponse) XXX_Size() int {
	return xxx_messageInfo_OneApiGetPickupTimeslotsResponse.Size(m)
}
func (m *OneApiGetPickupTimeslotsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiGetPickupTimeslotsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiGetPickupTimeslotsResponse proto.InternalMessageInfo

func (m *OneApiGetPickupTimeslotsResponse) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *OneApiGetPickupTimeslotsResponse) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *OneApiGetPickupTimeslotsResponse) GetPickupTimeInfo() *OneApiPickupTimeInfo {
	if m != nil {
		return m.PickupTimeInfo
	}
	return nil
}

// @core
type GetResults struct {
	TimeslotResults      map[string]*OneApiGetPickupTimeslotsResponse `protobuf:"bytes,1,rep,name=timeslot_results,json=timeslotResults" json:"timeslot_results,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	DestBranchResults    map[string]*OneApiBranchInfo                 `protobuf:"bytes,2,rep,name=dest_branch_results,json=destBranchResults" json:"dest_branch_results,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	TwStoreResults       map[string]*BatchGetStoreByStoreIDResponse   `protobuf:"bytes,3,rep,name=tw_store_results,json=twStoreResults" json:"tw_store_results,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetResults) Reset()         { *m = GetResults{} }
func (m *GetResults) String() string { return proto.CompactTextString(m) }
func (*GetResults) ProtoMessage()    {}
func (*GetResults) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{12}
}

func (m *GetResults) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResults.Unmarshal(m, b)
}
func (m *GetResults) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResults.Marshal(b, m, deterministic)
}
func (m *GetResults) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResults.Merge(m, src)
}
func (m *GetResults) XXX_Size() int {
	return xxx_messageInfo_GetResults.Size(m)
}
func (m *GetResults) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResults.DiscardUnknown(m)
}

var xxx_messageInfo_GetResults proto.InternalMessageInfo

func (m *GetResults) GetTimeslotResults() map[string]*OneApiGetPickupTimeslotsResponse {
	if m != nil {
		return m.TimeslotResults
	}
	return nil
}

func (m *GetResults) GetDestBranchResults() map[string]*OneApiBranchInfo {
	if m != nil {
		return m.DestBranchResults
	}
	return nil
}

func (m *GetResults) GetTwStoreResults() map[string]*BatchGetStoreByStoreIDResponse {
	if m != nil {
		return m.TwStoreResults
	}
	return nil
}

// @core
type OneApiBranchInfo struct {
	Retcode              *int32      `protobuf:"varint,1,req,name=retcode" json:"retcode,omitempty"`
	Message              *string     `protobuf:"bytes,2,req,name=message" json:"message,omitempty"`
	BranchInfo           *BranchInfo `protobuf:"bytes,3,opt,name=branch_info,json=branchInfo" json:"branch_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *OneApiBranchInfo) Reset()         { *m = OneApiBranchInfo{} }
func (m *OneApiBranchInfo) String() string { return proto.CompactTextString(m) }
func (*OneApiBranchInfo) ProtoMessage()    {}
func (*OneApiBranchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{13}
}

func (m *OneApiBranchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiBranchInfo.Unmarshal(m, b)
}
func (m *OneApiBranchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiBranchInfo.Marshal(b, m, deterministic)
}
func (m *OneApiBranchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiBranchInfo.Merge(m, src)
}
func (m *OneApiBranchInfo) XXX_Size() int {
	return xxx_messageInfo_OneApiBranchInfo.Size(m)
}
func (m *OneApiBranchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiBranchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiBranchInfo proto.InternalMessageInfo

func (m *OneApiBranchInfo) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *OneApiBranchInfo) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *OneApiBranchInfo) GetBranchInfo() *BranchInfo {
	if m != nil {
		return m.BranchInfo
	}
	return nil
}

// @core
type OneApiResponse struct {
	RespHeader           *RespHeader                 `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CheckResults         map[string]*OneApiCheckInfo `protobuf:"bytes,2,rep,name=check_results,json=checkResults" json:"check_results,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	GetResults           *GetResults                 `protobuf:"bytes,3,opt,name=get_results,json=getResults" json:"get_results,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *OneApiResponse) Reset()         { *m = OneApiResponse{} }
func (m *OneApiResponse) String() string { return proto.CompactTextString(m) }
func (*OneApiResponse) ProtoMessage()    {}
func (*OneApiResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{14}
}

func (m *OneApiResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiResponse.Unmarshal(m, b)
}
func (m *OneApiResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiResponse.Marshal(b, m, deterministic)
}
func (m *OneApiResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiResponse.Merge(m, src)
}
func (m *OneApiResponse) XXX_Size() int {
	return xxx_messageInfo_OneApiResponse.Size(m)
}
func (m *OneApiResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiResponse proto.InternalMessageInfo

func (m *OneApiResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *OneApiResponse) GetCheckResults() map[string]*OneApiCheckInfo {
	if m != nil {
		return m.CheckResults
	}
	return nil
}

func (m *OneApiResponse) GetGetResults() *GetResults {
	if m != nil {
		return m.GetResults
	}
	return nil
}

// @core
type SingleLaneProductRule struct {
	UniqueId             *string    `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	SkuInfo              []*SkuInfo `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	OrderInfo            *OrderInfo `protobuf:"bytes,3,opt,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	ProductId            []string   `protobuf:"bytes,4,rep,name=product_id,json=productId" json:"product_id,omitempty"`
	BuyerPurchaseTime    *uint32    `protobuf:"varint,5,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SingleLaneProductRule) Reset()         { *m = SingleLaneProductRule{} }
func (m *SingleLaneProductRule) String() string { return proto.CompactTextString(m) }
func (*SingleLaneProductRule) ProtoMessage()    {}
func (*SingleLaneProductRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{15}
}

func (m *SingleLaneProductRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleLaneProductRule.Unmarshal(m, b)
}
func (m *SingleLaneProductRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleLaneProductRule.Marshal(b, m, deterministic)
}
func (m *SingleLaneProductRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleLaneProductRule.Merge(m, src)
}
func (m *SingleLaneProductRule) XXX_Size() int {
	return xxx_messageInfo_SingleLaneProductRule.Size(m)
}
func (m *SingleLaneProductRule) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleLaneProductRule.DiscardUnknown(m)
}

var xxx_messageInfo_SingleLaneProductRule proto.InternalMessageInfo

func (m *SingleLaneProductRule) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SingleLaneProductRule) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func (m *SingleLaneProductRule) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *SingleLaneProductRule) GetProductId() []string {
	if m != nil {
		return m.ProductId
	}
	return nil
}

func (m *SingleLaneProductRule) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

// @core
type ServiceableItem struct {
	ItemId               *uint64  `protobuf:"varint,1,req,name=item_id,json=itemId" json:"item_id,omitempty"`
	ModelId              *uint64  `protobuf:"varint,2,opt,name=model_id,json=modelId" json:"model_id,omitempty"`
	CategoryId           *uint32  `protobuf:"varint,3,opt,name=category_id,json=categoryId" json:"category_id,omitempty"`
	GlobalCategoryId     *uint32  `protobuf:"varint,4,opt,name=global_category_id,json=globalCategoryId" json:"global_category_id,omitempty"`
	ItemPrice            *float32 `protobuf:"fixed32,5,opt,name=item_price,json=itemPrice" json:"item_price,omitempty"`
	ItemPriceUsd         *float32 `protobuf:"fixed32,6,opt,name=item_price_usd,json=itemPriceUsd" json:"item_price_usd,omitempty"`
	Quantity             *uint32  `protobuf:"varint,7,req,name=quantity" json:"quantity,omitempty"`
	Length               *float32 `protobuf:"fixed32,8,opt,name=length" json:"length,omitempty"`
	Width                *float32 `protobuf:"fixed32,9,opt,name=width" json:"width,omitempty"`
	Height               *float32 `protobuf:"fixed32,10,opt,name=height" json:"height,omitempty"`
	Weight               *float32 `protobuf:"fixed32,11,opt,name=weight" json:"weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServiceableItem) Reset()         { *m = ServiceableItem{} }
func (m *ServiceableItem) String() string { return proto.CompactTextString(m) }
func (*ServiceableItem) ProtoMessage()    {}
func (*ServiceableItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{16}
}

func (m *ServiceableItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceableItem.Unmarshal(m, b)
}
func (m *ServiceableItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceableItem.Marshal(b, m, deterministic)
}
func (m *ServiceableItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceableItem.Merge(m, src)
}
func (m *ServiceableItem) XXX_Size() int {
	return xxx_messageInfo_ServiceableItem.Size(m)
}
func (m *ServiceableItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceableItem.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceableItem proto.InternalMessageInfo

func (m *ServiceableItem) GetItemId() uint64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *ServiceableItem) GetModelId() uint64 {
	if m != nil && m.ModelId != nil {
		return *m.ModelId
	}
	return 0
}

func (m *ServiceableItem) GetCategoryId() uint32 {
	if m != nil && m.CategoryId != nil {
		return *m.CategoryId
	}
	return 0
}

func (m *ServiceableItem) GetGlobalCategoryId() uint32 {
	if m != nil && m.GlobalCategoryId != nil {
		return *m.GlobalCategoryId
	}
	return 0
}

func (m *ServiceableItem) GetItemPrice() float32 {
	if m != nil && m.ItemPrice != nil {
		return *m.ItemPrice
	}
	return 0
}

func (m *ServiceableItem) GetItemPriceUsd() float32 {
	if m != nil && m.ItemPriceUsd != nil {
		return *m.ItemPriceUsd
	}
	return 0
}

func (m *ServiceableItem) GetQuantity() uint32 {
	if m != nil && m.Quantity != nil {
		return *m.Quantity
	}
	return 0
}

func (m *ServiceableItem) GetLength() float32 {
	if m != nil && m.Length != nil {
		return *m.Length
	}
	return 0
}

func (m *ServiceableItem) GetWidth() float32 {
	if m != nil && m.Width != nil {
		return *m.Width
	}
	return 0
}

func (m *ServiceableItem) GetHeight() float32 {
	if m != nil && m.Height != nil {
		return *m.Height
	}
	return 0
}

func (m *ServiceableItem) GetWeight() float32 {
	if m != nil && m.Weight != nil {
		return *m.Weight
	}
	return 0
}

// @core
type LogisticsServiceableReq struct {
	LaneServiceability   *LaneAreaServiceabilityReq `protobuf:"bytes,1,req,name=lane_serviceability,json=laneServiceability" json:"lane_serviceability,omitempty"`
	SkuInfos             []*SkuInfo                 `protobuf:"bytes,2,rep,name=sku_infos,json=skuInfos" json:"sku_infos,omitempty"`
	SubPackageInfo       []*SubPackageInfo          `protobuf:"bytes,3,rep,name=sub_package_info,json=subPackageInfo" json:"sub_package_info,omitempty"`
	BuyerPurchaseTime    *uint32                    `protobuf:"varint,4,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *LogisticsServiceableReq) Reset()         { *m = LogisticsServiceableReq{} }
func (m *LogisticsServiceableReq) String() string { return proto.CompactTextString(m) }
func (*LogisticsServiceableReq) ProtoMessage()    {}
func (*LogisticsServiceableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{17}
}

func (m *LogisticsServiceableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogisticsServiceableReq.Unmarshal(m, b)
}
func (m *LogisticsServiceableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogisticsServiceableReq.Marshal(b, m, deterministic)
}
func (m *LogisticsServiceableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogisticsServiceableReq.Merge(m, src)
}
func (m *LogisticsServiceableReq) XXX_Size() int {
	return xxx_messageInfo_LogisticsServiceableReq.Size(m)
}
func (m *LogisticsServiceableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LogisticsServiceableReq.DiscardUnknown(m)
}

var xxx_messageInfo_LogisticsServiceableReq proto.InternalMessageInfo

func (m *LogisticsServiceableReq) GetLaneServiceability() *LaneAreaServiceabilityReq {
	if m != nil {
		return m.LaneServiceability
	}
	return nil
}

func (m *LogisticsServiceableReq) GetSkuInfos() []*SkuInfo {
	if m != nil {
		return m.SkuInfos
	}
	return nil
}

func (m *LogisticsServiceableReq) GetSubPackageInfo() []*SubPackageInfo {
	if m != nil {
		return m.SubPackageInfo
	}
	return nil
}

func (m *LogisticsServiceableReq) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

// @core
type OneApiCheckInfoForCheckoutScene struct {
	ProductRulesList       []*SingleLaneProductRule   `protobuf:"bytes,1,rep,name=product_rules_list,json=productRulesList" json:"product_rules_list,omitempty"`
	ProductServiceableList []*LogisticsServiceableReq `protobuf:"bytes,2,rep,name=product_serviceable_list,json=productServiceableList" json:"product_serviceable_list,omitempty"`
	IsCheckProductRule     *uint32                    `protobuf:"varint,3,opt,name=is_check_product_rule,json=isCheckProductRule,def=1" json:"is_check_product_rule,omitempty"`
	ParcelLibraryQueries   []*ParcelLibraryQuery      `protobuf:"bytes,4,rep,name=parcel_library_queries,json=parcelLibraryQueries" json:"parcel_library_queries,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                   `json:"-"`
	XXX_unrecognized       []byte                     `json:"-"`
	XXX_sizecache          int32                      `json:"-"`
}

func (m *OneApiCheckInfoForCheckoutScene) Reset()         { *m = OneApiCheckInfoForCheckoutScene{} }
func (m *OneApiCheckInfoForCheckoutScene) String() string { return proto.CompactTextString(m) }
func (*OneApiCheckInfoForCheckoutScene) ProtoMessage()    {}
func (*OneApiCheckInfoForCheckoutScene) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{18}
}

func (m *OneApiCheckInfoForCheckoutScene) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiCheckInfoForCheckoutScene.Unmarshal(m, b)
}
func (m *OneApiCheckInfoForCheckoutScene) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiCheckInfoForCheckoutScene.Marshal(b, m, deterministic)
}
func (m *OneApiCheckInfoForCheckoutScene) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiCheckInfoForCheckoutScene.Merge(m, src)
}
func (m *OneApiCheckInfoForCheckoutScene) XXX_Size() int {
	return xxx_messageInfo_OneApiCheckInfoForCheckoutScene.Size(m)
}
func (m *OneApiCheckInfoForCheckoutScene) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiCheckInfoForCheckoutScene.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiCheckInfoForCheckoutScene proto.InternalMessageInfo

const Default_OneApiCheckInfoForCheckoutScene_IsCheckProductRule uint32 = 1

func (m *OneApiCheckInfoForCheckoutScene) GetProductRulesList() []*SingleLaneProductRule {
	if m != nil {
		return m.ProductRulesList
	}
	return nil
}

func (m *OneApiCheckInfoForCheckoutScene) GetProductServiceableList() []*LogisticsServiceableReq {
	if m != nil {
		return m.ProductServiceableList
	}
	return nil
}

func (m *OneApiCheckInfoForCheckoutScene) GetIsCheckProductRule() uint32 {
	if m != nil && m.IsCheckProductRule != nil {
		return *m.IsCheckProductRule
	}
	return Default_OneApiCheckInfoForCheckoutScene_IsCheckProductRule
}

func (m *OneApiCheckInfoForCheckoutScene) GetParcelLibraryQueries() []*ParcelLibraryQuery {
	if m != nil {
		return m.ParcelLibraryQueries
	}
	return nil
}

// @core
type LaneServiceable struct {
	LaneCode             *string          `protobuf:"bytes,1,req,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	Code                 *int32           `protobuf:"varint,2,req,name=code" json:"code,omitempty"`
	Message              *string          `protobuf:"bytes,3,req,name=message" json:"message,omitempty"`
	AreaServiceable      *AreaServiceable `protobuf:"bytes,6,opt,name=area_serviceable,json=areaServiceable" json:"area_serviceable,omitempty"`
	LaneCodeGroup        []string         `protobuf:"bytes,8,rep,name=lane_code_group,json=laneCodeGroup" json:"lane_code_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *LaneServiceable) Reset()         { *m = LaneServiceable{} }
func (m *LaneServiceable) String() string { return proto.CompactTextString(m) }
func (*LaneServiceable) ProtoMessage()    {}
func (*LaneServiceable) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{19}
}

func (m *LaneServiceable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LaneServiceable.Unmarshal(m, b)
}
func (m *LaneServiceable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LaneServiceable.Marshal(b, m, deterministic)
}
func (m *LaneServiceable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LaneServiceable.Merge(m, src)
}
func (m *LaneServiceable) XXX_Size() int {
	return xxx_messageInfo_LaneServiceable.Size(m)
}
func (m *LaneServiceable) XXX_DiscardUnknown() {
	xxx_messageInfo_LaneServiceable.DiscardUnknown(m)
}

var xxx_messageInfo_LaneServiceable proto.InternalMessageInfo

func (m *LaneServiceable) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *LaneServiceable) GetCode() int32 {
	if m != nil && m.Code != nil {
		return *m.Code
	}
	return 0
}

func (m *LaneServiceable) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *LaneServiceable) GetAreaServiceable() *AreaServiceable {
	if m != nil {
		return m.AreaServiceable
	}
	return nil
}

func (m *LaneServiceable) GetLaneCodeGroup() []string {
	if m != nil {
		return m.LaneCodeGroup
	}
	return nil
}

// @core
type LogisticsServiceable struct {
	UniqueId             *string            `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	Errcode              *int32             `protobuf:"varint,2,req,name=errcode" json:"errcode,omitempty"`
	Message              *string            `protobuf:"bytes,3,req,name=message" json:"message,omitempty"`
	AvailableLanes       []*LaneServiceable `protobuf:"bytes,4,rep,name=available_lanes,json=availableLanes" json:"available_lanes,omitempty"`
	UnavailableLanes     []*LaneServiceable `protobuf:"bytes,5,rep,name=unavailable_lanes,json=unavailableLanes" json:"unavailable_lanes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *LogisticsServiceable) Reset()         { *m = LogisticsServiceable{} }
func (m *LogisticsServiceable) String() string { return proto.CompactTextString(m) }
func (*LogisticsServiceable) ProtoMessage()    {}
func (*LogisticsServiceable) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{20}
}

func (m *LogisticsServiceable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogisticsServiceable.Unmarshal(m, b)
}
func (m *LogisticsServiceable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogisticsServiceable.Marshal(b, m, deterministic)
}
func (m *LogisticsServiceable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogisticsServiceable.Merge(m, src)
}
func (m *LogisticsServiceable) XXX_Size() int {
	return xxx_messageInfo_LogisticsServiceable.Size(m)
}
func (m *LogisticsServiceable) XXX_DiscardUnknown() {
	xxx_messageInfo_LogisticsServiceable.DiscardUnknown(m)
}

var xxx_messageInfo_LogisticsServiceable proto.InternalMessageInfo

func (m *LogisticsServiceable) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *LogisticsServiceable) GetErrcode() int32 {
	if m != nil && m.Errcode != nil {
		return *m.Errcode
	}
	return 0
}

func (m *LogisticsServiceable) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *LogisticsServiceable) GetAvailableLanes() []*LaneServiceable {
	if m != nil {
		return m.AvailableLanes
	}
	return nil
}

func (m *LogisticsServiceable) GetUnavailableLanes() []*LaneServiceable {
	if m != nil {
		return m.UnavailableLanes
	}
	return nil
}

// @core
type OneApiRequestForCheckoutScene struct {
	ReqHeader            *ReqHeader                       `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	OneApiCheckInfo      *OneApiCheckInfoForCheckoutScene `protobuf:"bytes,2,req,name=one_api_check_info,json=oneApiCheckInfo" json:"one_api_check_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *OneApiRequestForCheckoutScene) Reset()         { *m = OneApiRequestForCheckoutScene{} }
func (m *OneApiRequestForCheckoutScene) String() string { return proto.CompactTextString(m) }
func (*OneApiRequestForCheckoutScene) ProtoMessage()    {}
func (*OneApiRequestForCheckoutScene) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{21}
}

func (m *OneApiRequestForCheckoutScene) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiRequestForCheckoutScene.Unmarshal(m, b)
}
func (m *OneApiRequestForCheckoutScene) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiRequestForCheckoutScene.Marshal(b, m, deterministic)
}
func (m *OneApiRequestForCheckoutScene) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiRequestForCheckoutScene.Merge(m, src)
}
func (m *OneApiRequestForCheckoutScene) XXX_Size() int {
	return xxx_messageInfo_OneApiRequestForCheckoutScene.Size(m)
}
func (m *OneApiRequestForCheckoutScene) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiRequestForCheckoutScene.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiRequestForCheckoutScene proto.InternalMessageInfo

func (m *OneApiRequestForCheckoutScene) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *OneApiRequestForCheckoutScene) GetOneApiCheckInfo() *OneApiCheckInfoForCheckoutScene {
	if m != nil {
		return m.OneApiCheckInfo
	}
	return nil
}

// @core
type OneApiResponseForCheckoutScene struct {
	RespHeader                    *RespHeader                          `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CheckProductRuleResultMap     map[string]*CheckProductRuleResponse `protobuf:"bytes,2,rep,name=check_product_rule_result_map,json=checkProductRuleResultMap" json:"check_product_rule_result_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CheckServiceableAreaResultMap map[string]*ProductServiceableRsp    `protobuf:"bytes,3,rep,name=check_serviceable_area_result_map,json=checkServiceableAreaResultMap" json:"check_serviceable_area_result_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ParcelLibraryInfoMap          map[string]*ParcelLibraryInfo        `protobuf:"bytes,4,rep,name=parcel_library_info_map,json=parcelLibraryInfoMap" json:"parcel_library_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral          struct{}                             `json:"-"`
	XXX_unrecognized              []byte                               `json:"-"`
	XXX_sizecache                 int32                                `json:"-"`
}

func (m *OneApiResponseForCheckoutScene) Reset()         { *m = OneApiResponseForCheckoutScene{} }
func (m *OneApiResponseForCheckoutScene) String() string { return proto.CompactTextString(m) }
func (*OneApiResponseForCheckoutScene) ProtoMessage()    {}
func (*OneApiResponseForCheckoutScene) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{22}
}

func (m *OneApiResponseForCheckoutScene) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiResponseForCheckoutScene.Unmarshal(m, b)
}
func (m *OneApiResponseForCheckoutScene) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiResponseForCheckoutScene.Marshal(b, m, deterministic)
}
func (m *OneApiResponseForCheckoutScene) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiResponseForCheckoutScene.Merge(m, src)
}
func (m *OneApiResponseForCheckoutScene) XXX_Size() int {
	return xxx_messageInfo_OneApiResponseForCheckoutScene.Size(m)
}
func (m *OneApiResponseForCheckoutScene) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiResponseForCheckoutScene.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiResponseForCheckoutScene proto.InternalMessageInfo

func (m *OneApiResponseForCheckoutScene) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *OneApiResponseForCheckoutScene) GetCheckProductRuleResultMap() map[string]*CheckProductRuleResponse {
	if m != nil {
		return m.CheckProductRuleResultMap
	}
	return nil
}

func (m *OneApiResponseForCheckoutScene) GetCheckServiceableAreaResultMap() map[string]*ProductServiceableRsp {
	if m != nil {
		return m.CheckServiceableAreaResultMap
	}
	return nil
}

func (m *OneApiResponseForCheckoutScene) GetParcelLibraryInfoMap() map[string]*ParcelLibraryInfo {
	if m != nil {
		return m.ParcelLibraryInfoMap
	}
	return nil
}

// @core
type OneApiRequestForFulfillment struct {
	ReqHeader            *ReqHeader                     `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	OneApiCheckInfo      *OneApiCheckInfoForFulfillment `protobuf:"bytes,2,req,name=one_api_check_info,json=oneApiCheckInfo" json:"one_api_check_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *OneApiRequestForFulfillment) Reset()         { *m = OneApiRequestForFulfillment{} }
func (m *OneApiRequestForFulfillment) String() string { return proto.CompactTextString(m) }
func (*OneApiRequestForFulfillment) ProtoMessage()    {}
func (*OneApiRequestForFulfillment) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{23}
}

func (m *OneApiRequestForFulfillment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiRequestForFulfillment.Unmarshal(m, b)
}
func (m *OneApiRequestForFulfillment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiRequestForFulfillment.Marshal(b, m, deterministic)
}
func (m *OneApiRequestForFulfillment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiRequestForFulfillment.Merge(m, src)
}
func (m *OneApiRequestForFulfillment) XXX_Size() int {
	return xxx_messageInfo_OneApiRequestForFulfillment.Size(m)
}
func (m *OneApiRequestForFulfillment) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiRequestForFulfillment.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiRequestForFulfillment proto.InternalMessageInfo

func (m *OneApiRequestForFulfillment) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *OneApiRequestForFulfillment) GetOneApiCheckInfo() *OneApiCheckInfoForFulfillment {
	if m != nil {
		return m.OneApiCheckInfo
	}
	return nil
}

// @core
type OneApiCheckInfoForFulfillment struct {
	ProductServiceableList []*LogisticsServiceableReq `protobuf:"bytes,1,rep,name=product_serviceable_list,json=productServiceableList" json:"product_serviceable_list,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                   `json:"-"`
	XXX_unrecognized       []byte                     `json:"-"`
	XXX_sizecache          int32                      `json:"-"`
}

func (m *OneApiCheckInfoForFulfillment) Reset()         { *m = OneApiCheckInfoForFulfillment{} }
func (m *OneApiCheckInfoForFulfillment) String() string { return proto.CompactTextString(m) }
func (*OneApiCheckInfoForFulfillment) ProtoMessage()    {}
func (*OneApiCheckInfoForFulfillment) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{24}
}

func (m *OneApiCheckInfoForFulfillment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiCheckInfoForFulfillment.Unmarshal(m, b)
}
func (m *OneApiCheckInfoForFulfillment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiCheckInfoForFulfillment.Marshal(b, m, deterministic)
}
func (m *OneApiCheckInfoForFulfillment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiCheckInfoForFulfillment.Merge(m, src)
}
func (m *OneApiCheckInfoForFulfillment) XXX_Size() int {
	return xxx_messageInfo_OneApiCheckInfoForFulfillment.Size(m)
}
func (m *OneApiCheckInfoForFulfillment) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiCheckInfoForFulfillment.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiCheckInfoForFulfillment proto.InternalMessageInfo

func (m *OneApiCheckInfoForFulfillment) GetProductServiceableList() []*LogisticsServiceableReq {
	if m != nil {
		return m.ProductServiceableList
	}
	return nil
}

// @core
type OneApiResponseForFulfillment struct {
	RespHeader                    *RespHeader                       `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CheckServiceableAreaResultMap map[string]*ProductServiceableRsp `protobuf:"bytes,2,rep,name=check_serviceable_area_result_map,json=checkServiceableAreaResultMap" json:"check_serviceable_area_result_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CheckLaneRuleResultMap        map[string]*CheckLaneRuleResponse `protobuf:"bytes,3,rep,name=check_lane_rule_result_map,json=checkLaneRuleResultMap" json:"check_lane_rule_result_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral          struct{}                          `json:"-"`
	XXX_unrecognized              []byte                            `json:"-"`
	XXX_sizecache                 int32                             `json:"-"`
}

func (m *OneApiResponseForFulfillment) Reset()         { *m = OneApiResponseForFulfillment{} }
func (m *OneApiResponseForFulfillment) String() string { return proto.CompactTextString(m) }
func (*OneApiResponseForFulfillment) ProtoMessage()    {}
func (*OneApiResponseForFulfillment) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{25}
}

func (m *OneApiResponseForFulfillment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiResponseForFulfillment.Unmarshal(m, b)
}
func (m *OneApiResponseForFulfillment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiResponseForFulfillment.Marshal(b, m, deterministic)
}
func (m *OneApiResponseForFulfillment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiResponseForFulfillment.Merge(m, src)
}
func (m *OneApiResponseForFulfillment) XXX_Size() int {
	return xxx_messageInfo_OneApiResponseForFulfillment.Size(m)
}
func (m *OneApiResponseForFulfillment) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiResponseForFulfillment.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiResponseForFulfillment proto.InternalMessageInfo

func (m *OneApiResponseForFulfillment) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *OneApiResponseForFulfillment) GetCheckServiceableAreaResultMap() map[string]*ProductServiceableRsp {
	if m != nil {
		return m.CheckServiceableAreaResultMap
	}
	return nil
}

func (m *OneApiResponseForFulfillment) GetCheckLaneRuleResultMap() map[string]*CheckLaneRuleResponse {
	if m != nil {
		return m.CheckLaneRuleResultMap
	}
	return nil
}

// @core
type OneApiRequestForReroute struct {
	ReqHeader            *ReqHeader         `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	OneApiInfo           *RerouteOneApiInfo `protobuf:"bytes,2,req,name=one_api_info,json=oneApiInfo" json:"one_api_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *OneApiRequestForReroute) Reset()         { *m = OneApiRequestForReroute{} }
func (m *OneApiRequestForReroute) String() string { return proto.CompactTextString(m) }
func (*OneApiRequestForReroute) ProtoMessage()    {}
func (*OneApiRequestForReroute) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{26}
}

func (m *OneApiRequestForReroute) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiRequestForReroute.Unmarshal(m, b)
}
func (m *OneApiRequestForReroute) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiRequestForReroute.Marshal(b, m, deterministic)
}
func (m *OneApiRequestForReroute) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiRequestForReroute.Merge(m, src)
}
func (m *OneApiRequestForReroute) XXX_Size() int {
	return xxx_messageInfo_OneApiRequestForReroute.Size(m)
}
func (m *OneApiRequestForReroute) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiRequestForReroute.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiRequestForReroute proto.InternalMessageInfo

func (m *OneApiRequestForReroute) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *OneApiRequestForReroute) GetOneApiInfo() *RerouteOneApiInfo {
	if m != nil {
		return m.OneApiInfo
	}
	return nil
}

// @core
type RerouteOneApiInfo struct {
	LaneServiceableList  []*LaneServiceableReq `protobuf:"bytes,1,rep,name=lane_serviceable_list,json=laneServiceableList" json:"lane_serviceable_list,omitempty"`
	SkuInfos             []*SkuInfo            `protobuf:"bytes,2,rep,name=sku_infos,json=skuInfos" json:"sku_infos,omitempty"`
	BuyerPurchaseTime    *uint32               `protobuf:"varint,3,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *RerouteOneApiInfo) Reset()         { *m = RerouteOneApiInfo{} }
func (m *RerouteOneApiInfo) String() string { return proto.CompactTextString(m) }
func (*RerouteOneApiInfo) ProtoMessage()    {}
func (*RerouteOneApiInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{27}
}

func (m *RerouteOneApiInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RerouteOneApiInfo.Unmarshal(m, b)
}
func (m *RerouteOneApiInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RerouteOneApiInfo.Marshal(b, m, deterministic)
}
func (m *RerouteOneApiInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RerouteOneApiInfo.Merge(m, src)
}
func (m *RerouteOneApiInfo) XXX_Size() int {
	return xxx_messageInfo_RerouteOneApiInfo.Size(m)
}
func (m *RerouteOneApiInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RerouteOneApiInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RerouteOneApiInfo proto.InternalMessageInfo

func (m *RerouteOneApiInfo) GetLaneServiceableList() []*LaneServiceableReq {
	if m != nil {
		return m.LaneServiceableList
	}
	return nil
}

func (m *RerouteOneApiInfo) GetSkuInfos() []*SkuInfo {
	if m != nil {
		return m.SkuInfos
	}
	return nil
}

func (m *RerouteOneApiInfo) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

// @core
type OneApiResponseForReroute struct {
	RespHeader           *RespHeader                 `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	RerouteCheckRsp      map[string]*RerouteCheckRsp `protobuf:"bytes,2,rep,name=reroute_check_rsp,json=rerouteCheckRsp" json:"reroute_check_rsp,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *OneApiResponseForReroute) Reset()         { *m = OneApiResponseForReroute{} }
func (m *OneApiResponseForReroute) String() string { return proto.CompactTextString(m) }
func (*OneApiResponseForReroute) ProtoMessage()    {}
func (*OneApiResponseForReroute) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{28}
}

func (m *OneApiResponseForReroute) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneApiResponseForReroute.Unmarshal(m, b)
}
func (m *OneApiResponseForReroute) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneApiResponseForReroute.Marshal(b, m, deterministic)
}
func (m *OneApiResponseForReroute) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneApiResponseForReroute.Merge(m, src)
}
func (m *OneApiResponseForReroute) XXX_Size() int {
	return xxx_messageInfo_OneApiResponseForReroute.Size(m)
}
func (m *OneApiResponseForReroute) XXX_DiscardUnknown() {
	xxx_messageInfo_OneApiResponseForReroute.DiscardUnknown(m)
}

var xxx_messageInfo_OneApiResponseForReroute proto.InternalMessageInfo

func (m *OneApiResponseForReroute) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *OneApiResponseForReroute) GetRerouteCheckRsp() map[string]*RerouteCheckRsp {
	if m != nil {
		return m.RerouteCheckRsp
	}
	return nil
}

// @core
type RerouteCheckRsp struct {
	LaneCheckResult      []*LaneCheckResult `protobuf:"bytes,1,rep,name=lane_check_result,json=laneCheckResult" json:"lane_check_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *RerouteCheckRsp) Reset()         { *m = RerouteCheckRsp{} }
func (m *RerouteCheckRsp) String() string { return proto.CompactTextString(m) }
func (*RerouteCheckRsp) ProtoMessage()    {}
func (*RerouteCheckRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{29}
}

func (m *RerouteCheckRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RerouteCheckRsp.Unmarshal(m, b)
}
func (m *RerouteCheckRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RerouteCheckRsp.Marshal(b, m, deterministic)
}
func (m *RerouteCheckRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RerouteCheckRsp.Merge(m, src)
}
func (m *RerouteCheckRsp) XXX_Size() int {
	return xxx_messageInfo_RerouteCheckRsp.Size(m)
}
func (m *RerouteCheckRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RerouteCheckRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RerouteCheckRsp proto.InternalMessageInfo

func (m *RerouteCheckRsp) GetLaneCheckResult() []*LaneCheckResult {
	if m != nil {
		return m.LaneCheckResult
	}
	return nil
}

// @core
type LaneCheckResult struct {
	Code                 *int32                       `protobuf:"varint,1,req,name=code" json:"code,omitempty"`
	Message              *string                      `protobuf:"bytes,2,req,name=message" json:"message,omitempty"`
	LaneCode             *string                      `protobuf:"bytes,3,req,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	LaneCodeGroup        []string                     `protobuf:"bytes,4,rep,name=lane_code_group,json=laneCodeGroup" json:"lane_code_group,omitempty"`
	Serviceable          []*Serviceable               `protobuf:"bytes,5,rep,name=serviceable" json:"serviceable,omitempty"`
	RuleLimitInfo        []*SingleCheckLineRuleResult `protobuf:"bytes,6,rep,name=rule_limit_info,json=ruleLimitInfo" json:"rule_limit_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *LaneCheckResult) Reset()         { *m = LaneCheckResult{} }
func (m *LaneCheckResult) String() string { return proto.CompactTextString(m) }
func (*LaneCheckResult) ProtoMessage()    {}
func (*LaneCheckResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_795a0c347be66bb5, []int{30}
}

func (m *LaneCheckResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LaneCheckResult.Unmarshal(m, b)
}
func (m *LaneCheckResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LaneCheckResult.Marshal(b, m, deterministic)
}
func (m *LaneCheckResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LaneCheckResult.Merge(m, src)
}
func (m *LaneCheckResult) XXX_Size() int {
	return xxx_messageInfo_LaneCheckResult.Size(m)
}
func (m *LaneCheckResult) XXX_DiscardUnknown() {
	xxx_messageInfo_LaneCheckResult.DiscardUnknown(m)
}

var xxx_messageInfo_LaneCheckResult proto.InternalMessageInfo

func (m *LaneCheckResult) GetCode() int32 {
	if m != nil && m.Code != nil {
		return *m.Code
	}
	return 0
}

func (m *LaneCheckResult) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *LaneCheckResult) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *LaneCheckResult) GetLaneCodeGroup() []string {
	if m != nil {
		return m.LaneCodeGroup
	}
	return nil
}

func (m *LaneCheckResult) GetServiceable() []*Serviceable {
	if m != nil {
		return m.Serviceable
	}
	return nil
}

func (m *LaneCheckResult) GetRuleLimitInfo() []*SingleCheckLineRuleResult {
	if m != nil {
		return m.RuleLimitInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*LineWaybillCheckAttr)(nil), "lcos_protobuf.LineWaybillCheckAttr")
	proto.RegisterType((*GetTimeslotForOneApiRequest)(nil), "lcos_protobuf.GetTimeslotForOneApiRequest")
	proto.RegisterType((*LineWaybillGetAttr)(nil), "lcos_protobuf.LineWaybillGetAttr")
	proto.RegisterType((*BranchGetReq)(nil), "lcos_protobuf.BranchGetReq")
	proto.RegisterType((*TwStoreGetReq)(nil), "lcos_protobuf.TwStoreGetReq")
	proto.RegisterType((*OneApiInfo)(nil), "lcos_protobuf.OneApiInfo")
	proto.RegisterType((*OneApiRequest)(nil), "lcos_protobuf.OneApiRequest")
	proto.RegisterType((*OneApiCheckInfo)(nil), "lcos_protobuf.OneApiCheckInfo")
	proto.RegisterType((*OneApiLineRuleInfo)(nil), "lcos_protobuf.OneApiLineRuleInfo")
	proto.RegisterType((*CheckResults)(nil), "lcos_protobuf.CheckResults")
	proto.RegisterMapType((map[string]*CheckLineRuleResponse)(nil), "lcos_protobuf.CheckResults.LineRuleCheckResultEntry")
	proto.RegisterMapType((map[string]*CheckPickupTimeResponse)(nil), "lcos_protobuf.CheckResults.PickupTimeCheckResultEntry")
	proto.RegisterMapType((map[string]*CheckLineServiceableAreaResponse)(nil), "lcos_protobuf.CheckResults.ServiceableCheckResultEntry")
	proto.RegisterType((*OneApiPickupTimeInfo)(nil), "lcos_protobuf.OneApiPickupTimeInfo")
	proto.RegisterType((*OneApiGetPickupTimeslotsResponse)(nil), "lcos_protobuf.OneApiGetPickupTimeslotsResponse")
	proto.RegisterType((*GetResults)(nil), "lcos_protobuf.GetResults")
	proto.RegisterMapType((map[string]*OneApiBranchInfo)(nil), "lcos_protobuf.GetResults.DestBranchResultsEntry")
	proto.RegisterMapType((map[string]*OneApiGetPickupTimeslotsResponse)(nil), "lcos_protobuf.GetResults.TimeslotResultsEntry")
	proto.RegisterMapType((map[string]*BatchGetStoreByStoreIDResponse)(nil), "lcos_protobuf.GetResults.TwStoreResultsEntry")
	proto.RegisterType((*OneApiBranchInfo)(nil), "lcos_protobuf.OneApiBranchInfo")
	proto.RegisterType((*OneApiResponse)(nil), "lcos_protobuf.OneApiResponse")
	proto.RegisterMapType((map[string]*OneApiCheckInfo)(nil), "lcos_protobuf.OneApiResponse.CheckResultsEntry")
	proto.RegisterType((*SingleLaneProductRule)(nil), "lcos_protobuf.SingleLaneProductRule")
	proto.RegisterType((*ServiceableItem)(nil), "lcos_protobuf.ServiceableItem")
	proto.RegisterType((*LogisticsServiceableReq)(nil), "lcos_protobuf.LogisticsServiceableReq")
	proto.RegisterType((*OneApiCheckInfoForCheckoutScene)(nil), "lcos_protobuf.OneApiCheckInfoForCheckoutScene")
	proto.RegisterType((*LaneServiceable)(nil), "lcos_protobuf.LaneServiceable")
	proto.RegisterType((*LogisticsServiceable)(nil), "lcos_protobuf.LogisticsServiceable")
	proto.RegisterType((*OneApiRequestForCheckoutScene)(nil), "lcos_protobuf.OneApiRequestForCheckoutScene")
	proto.RegisterType((*OneApiResponseForCheckoutScene)(nil), "lcos_protobuf.OneApiResponseForCheckoutScene")
	proto.RegisterMapType((map[string]*CheckProductRuleResponse)(nil), "lcos_protobuf.OneApiResponseForCheckoutScene.CheckProductRuleResultMapEntry")
	proto.RegisterMapType((map[string]*ProductServiceableRsp)(nil), "lcos_protobuf.OneApiResponseForCheckoutScene.CheckServiceableAreaResultMapEntry")
	proto.RegisterMapType((map[string]*ParcelLibraryInfo)(nil), "lcos_protobuf.OneApiResponseForCheckoutScene.ParcelLibraryInfoMapEntry")
	proto.RegisterType((*OneApiRequestForFulfillment)(nil), "lcos_protobuf.OneApiRequestForFulfillment")
	proto.RegisterType((*OneApiCheckInfoForFulfillment)(nil), "lcos_protobuf.OneApiCheckInfoForFulfillment")
	proto.RegisterType((*OneApiResponseForFulfillment)(nil), "lcos_protobuf.OneApiResponseForFulfillment")
	proto.RegisterMapType((map[string]*CheckLaneRuleResponse)(nil), "lcos_protobuf.OneApiResponseForFulfillment.CheckLaneRuleResultMapEntry")
	proto.RegisterMapType((map[string]*ProductServiceableRsp)(nil), "lcos_protobuf.OneApiResponseForFulfillment.CheckServiceableAreaResultMapEntry")
	proto.RegisterType((*OneApiRequestForReroute)(nil), "lcos_protobuf.OneApiRequestForReroute")
	proto.RegisterType((*RerouteOneApiInfo)(nil), "lcos_protobuf.RerouteOneApiInfo")
	proto.RegisterType((*OneApiResponseForReroute)(nil), "lcos_protobuf.OneApiResponseForReroute")
	proto.RegisterMapType((map[string]*RerouteCheckRsp)(nil), "lcos_protobuf.OneApiResponseForReroute.RerouteCheckRspEntry")
	proto.RegisterType((*RerouteCheckRsp)(nil), "lcos_protobuf.RerouteCheckRsp")
	proto.RegisterType((*LaneCheckResult)(nil), "lcos_protobuf.LaneCheckResult")
}

func init() {
	proto.RegisterFile("lcos_one_api.proto", fileDescriptor_795a0c347be66bb5)
}

var fileDescriptor_795a0c347be66bb5 = []byte{
	// 2641 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x5a, 0xcf, 0x6f, 0x1b, 0xc7,
	0xf5, 0xf7, 0x92, 0xd4, 0x0f, 0x3e, 0x8a, 0x26, 0x39, 0xfa, 0x61, 0x8a, 0xb2, 0x6c, 0x99, 0x09,
	0x1c, 0x21, 0x71, 0x94, 0x44, 0x5f, 0x7f, 0xe3, 0x42, 0x75, 0x0b, 0xd8, 0x8a, 0xad, 0x32, 0x55,
	0x12, 0x75, 0xe5, 0xc4, 0x30, 0x8a, 0x60, 0xbd, 0xda, 0x1d, 0x53, 0x5b, 0xad, 0x76, 0xa9, 0x9d,
	0x59, 0x09, 0x2a, 0x8a, 0x00, 0x6d, 0xd1, 0x6b, 0x4f, 0x45, 0x51, 0xe4, 0x58, 0xb4, 0xe7, 0xa2,
	0x87, 0x1c, 0x7a, 0xea, 0x9f, 0x90, 0x43, 0x8f, 0xbd, 0x06, 0xbd, 0xb6, 0xe8, 0xb5, 0x28, 0x50,
	0xcc, 0xcc, 0xce, 0x72, 0x66, 0x77, 0x49, 0x59, 0x92, 0x8b, 0x9e, 0xb4, 0xf3, 0x66, 0xe6, 0xbd,
	0x37, 0xf3, 0x7e, 0xcc, 0xe7, 0x3d, 0x0a, 0x90, 0xef, 0x84, 0xc4, 0x0a, 0x03, 0x6c, 0xd9, 0x03,
	0x6f, 0x6d, 0x10, 0x85, 0x34, 0x44, 0x75, 0x4e, 0xe3, 0xdf, 0x7b, 0xf1, 0x8b, 0x4e, 0x83, 0x0f,
	0xf7, 0x6c, 0x82, 0xc5, 0x7c, 0x67, 0x81, 0x13, 0x08, 0x8e, 0x8e, 0x3d, 0x07, 0xdb, 0x7b, 0xbe,
	0xa4, 0x5f, 0xe3, 0xf4, 0x13, 0xec, 0xf5, 0xf7, 0xa9, 0xe5, 0x7b, 0x87, 0x1e, 0x4d, 0x26, 0xda,
	0x82, 0xa1, 0xe7, 0x1c, 0xc4, 0x03, 0xeb, 0xc4, 0x0b, 0xdc, 0xf0, 0x24, 0x99, 0x69, 0x09, 0xde,
	0x91, 0x1d, 0x38, 0xfb, 0x09, 0x69, 0x96, 0x93, 0xe8, 0x89, 0x45, 0x68, 0x18, 0x49, 0xd6, 0xd7,
	0x85, 0x48, 0x07, 0x07, 0xb8, 0x40, 0xf0, 0xbc, 0xe0, 0x6f, 0x47, 0x0e, 0xf6, 0x2d, 0xdf, 0xdb,
	0x13, 0xe4, 0xee, 0xbf, 0xca, 0x30, 0xb7, 0xed, 0x05, 0xf8, 0xa9, 0x7d, 0xba, 0xe7, 0xf9, 0xfe,
	0xe6, 0x3e, 0x76, 0x0e, 0x1e, 0x50, 0x1a, 0xa1, 0x79, 0x98, 0x8c, 0xf0, 0x91, 0x15, 0x84, 0x6d,
	0x63, 0xa5, 0xb4, 0x5a, 0x35, 0x27, 0x22, 0x7c, 0xf4, 0x71, 0x88, 0xae, 0xc1, 0x94, 0xef, 0x05,
	0xd8, 0xf2, 0xdc, 0x76, 0x89, 0xd3, 0x27, 0xd9, 0xb0, 0xe7, 0xa2, 0x77, 0x61, 0xce, 0x23, 0x96,
	0xc3, 0xf6, 0xab, 0xd2, 0xdb, 0xe5, 0x15, 0x63, 0xb5, 0x6e, 0x22, 0x8f, 0x70, 0xd6, 0xbb, 0xc3,
	0x19, 0xf4, 0x8e, 0xb2, 0x23, 0x39, 0x37, 0xf5, 0x0e, 0x71, 0xbb, 0xc2, 0x77, 0xb4, 0x92, 0x1d,
	0x3b, 0x7c, 0xe6, 0x89, 0x77, 0x88, 0xd1, 0x5b, 0x80, 0xd2, 0x0d, 0x5c, 0x89, 0x28, 0xf6, 0x71,
	0x7b, 0x82, 0x2f, 0x6f, 0x24, 0xcb, 0xd9, 0x59, 0xcc, 0xd8, 0xc7, 0xe8, 0x29, 0x34, 0x15, 0x35,
	0x2c, 0x2f, 0x78, 0x11, 0xb6, 0x27, 0x57, 0x8c, 0xd5, 0xda, 0xfa, 0x9d, 0x35, 0xcd, 0x76, 0x6b,
	0x59, 0xc5, 0x1e, 0x44, 0xd8, 0x7e, 0x68, 0x13, 0xdc, 0x0b, 0x5e, 0x84, 0xeb, 0x66, 0x43, 0xe1,
	0xc2, 0x28, 0xe8, 0x3e, 0xd4, 0x12, 0x6d, 0x39, 0xcf, 0x29, 0xce, 0x73, 0x29, 0xc3, 0x73, 0x3b,
	0x74, 0x6c, 0xea, 0x85, 0x01, 0xdb, 0x61, 0x82, 0x58, 0xcf, 0x77, 0x7f, 0x17, 0x66, 0x5c, 0xec,
	0x7b, 0xc7, 0x38, 0x12, 0xdb, 0xa7, 0xcf, 0xde, 0x5e, 0x4b, 0x36, 0xf0, 0xfd, 0xdb, 0xd0, 0x54,
	0xee, 0x4a, 0xf0, 0xa8, 0x72, 0x1e, 0xdd, 0xa2, 0x63, 0x0d, 0x6f, 0x8f, 0xb3, 0xba, 0x3a, 0xd0,
	0xc6, 0xdd, 0x7f, 0x1a, 0xb0, 0xb4, 0x85, 0x29, 0x1b, 0x13, 0x3f, 0xa4, 0x8f, 0xc3, 0xe8, 0x93,
	0x00, 0x3f, 0x18, 0x78, 0x26, 0x3e, 0x8a, 0x31, 0xa1, 0xe8, 0x66, 0x7a, 0x56, 0x6e, 0x19, 0xe6,
	0x09, 0x75, 0x79, 0x1c, 0x6e, 0x92, 0x36, 0x4c, 0x39, 0x61, 0x1c, 0xd0, 0xe8, 0x34, 0x71, 0x07,
	0x39, 0x64, 0xd6, 0x55, 0x15, 0x8d, 0xec, 0xa0, 0xcf, 0xbd, 0xa6, 0xcc, 0x79, 0xb4, 0x86, 0x3c,
	0x4c, 0x36, 0xd3, 0x73, 0xd1, 0x32, 0x00, 0xa1, 0x76, 0x44, 0x55, 0x27, 0xa8, 0x72, 0x0a, 0x97,
	0xf4, 0x1a, 0xd4, 0x0f, 0x71, 0xe4, 0xec, 0xdb, 0x01, 0xb5, 0xe8, 0xe9, 0x40, 0xda, 0x7d, 0x46,
	0x12, 0x9f, 0x9c, 0x0e, 0xf8, 0x22, 0xdb, 0xe1, 0x1a, 0x58, 0xfd, 0x28, 0x8c, 0x07, 0xdc, 0xe2,
	0x75, 0x73, 0x26, 0x21, 0x6e, 0x31, 0x5a, 0xf7, 0x0f, 0x25, 0x40, 0x8a, 0xcb, 0x6f, 0x61, 0x7a,
	0x21, 0x87, 0xff, 0x0c, 0x5a, 0x7d, 0x2c, 0xb4, 0x65, 0x77, 0x27, 0x4c, 0x51, 0xe6, 0xa6, 0x78,
	0x33, 0x63, 0x8a, 0x31, 0x57, 0x6c, 0x36, 0xfa, 0xc3, 0x49, 0x6e, 0xe1, 0x0f, 0x61, 0xd6, 0xc5,
	0x84, 0x26, 0x01, 0x6f, 0x31, 0x19, 0x11, 0x3e, 0xe2, 0x17, 0x92, 0x77, 0x94, 0x87, 0x7c, 0xd1,
	0x16, 0xa6, 0x26, 0x3e, 0x32, 0x9b, 0x6c, 0x9f, 0x4a, 0x41, 0x8f, 0xa0, 0x29, 0x93, 0x44, 0xca,
	0x68, 0x82, 0x33, 0xba, 0x9e, 0x61, 0xf4, 0xe4, 0x64, 0x97, 0xad, 0x4a, 0x38, 0xd5, 0xa9, 0x3a,
	0xec, 0xee, 0xc2, 0x8c, 0xc6, 0x76, 0x09, 0xaa, 0x89, 0x76, 0x9e, 0xdb, 0x36, 0x56, 0x8c, 0xd5,
	0x8a, 0x39, 0x2d, 0x08, 0x3d, 0x17, 0xdd, 0x86, 0x86, 0x54, 0x9d, 0x5d, 0xb7, 0xb8, 0x38, 0x66,
	0x85, 0xba, 0x20, 0x73, 0x23, 0xf4, 0xdc, 0xee, 0x3a, 0xd4, 0x35, 0xa1, 0xe8, 0x16, 0xcc, 0x44,
	0xe2, 0x52, 0x2c, 0xdf, 0x23, 0xb4, 0x6d, 0xac, 0x94, 0x57, 0xab, 0x66, 0x2d, 0xa1, 0x6d, 0x7b,
	0x84, 0x76, 0xbf, 0x2a, 0x03, 0x88, 0xeb, 0xe3, 0x57, 0x75, 0x0f, 0x20, 0x8c, 0x5c, 0x19, 0x4a,
	0x06, 0x3f, 0x58, 0x3b, 0x73, 0xb0, 0x4f, 0xd8, 0x02, 0xee, 0xfc, 0xd5, 0x50, 0x7e, 0xa2, 0xf7,
	0x60, 0x9a, 0x1c, 0xc4, 0x62, 0x5b, 0x69, 0xa5, 0xbc, 0x5a, 0x5b, 0x5f, 0xc8, 0x6c, 0xdb, 0x3d,
	0x88, 0xf9, 0xa6, 0x29, 0x22, 0x3e, 0x90, 0x03, 0xd7, 0x95, 0xcc, 0x73, 0x22, 0x7c, 0xc7, 0xb2,
	0x29, 0x8d, 0x84, 0xb6, 0x65, 0xce, 0xe6, 0xb5, 0x6c, 0x20, 0x17, 0xa4, 0x56, 0xb3, 0xed, 0xc8,
	0x4c, 0x95, 0x4c, 0x31, 0x2a, 0x3b, 0x1f, 0xfa, 0x1c, 0x3a, 0xcc, 0x4c, 0x23, 0x44, 0x54, 0xb8,
	0x88, 0x5b, 0xa3, 0x45, 0x24, 0xae, 0x6c, 0x2e, 0xf4, 0x31, 0x2d, 0x62, 0xbf, 0x05, 0x4d, 0x12,
	0xef, 0x59, 0x03, 0xdb, 0x39, 0xb0, 0xfb, 0x49, 0xf2, 0x98, 0xe0, 0x4c, 0x97, 0xb3, 0xc7, 0x8f,
	0xf7, 0x76, 0xc4, 0x2a, 0x91, 0x37, 0x88, 0x36, 0x46, 0x6b, 0x30, 0xbb, 0x17, 0x9f, 0xe2, 0xc8,
	0x1a, 0xc4, 0x2c, 0xfa, 0x08, 0x16, 0x41, 0x2b, 0xa2, 0xad, 0xc5, 0xa7, 0x76, 0x92, 0x19, 0xe6,
	0xdb, 0xdd, 0x5f, 0x18, 0x50, 0xd7, 0x33, 0xcb, 0x3d, 0x00, 0x16, 0x6d, 0xfb, 0xd8, 0x76, 0x71,
	0xc4, 0x23, 0x2e, 0x6f, 0x3a, 0x13, 0x1f, 0x7d, 0x8f, 0xcf, 0x9b, 0xd5, 0x48, 0x7e, 0xa2, 0x6f,
	0xc3, 0x4c, 0xf2, 0x12, 0x4b, 0xf3, 0xb1, 0xad, 0x8b, 0x59, 0xab, 0xa7, 0x4e, 0x62, 0x42, 0x98,
	0x7e, 0x77, 0x1f, 0x41, 0x43, 0xcc, 0x70, 0x63, 0xf0, 0xa3, 0xb4, 0x61, 0x2a, 0xc2, 0xd4, 0x09,
	0x5d, 0xcc, 0x1d, 0x68, 0xc2, 0x94, 0x43, 0x36, 0x73, 0x88, 0x09, 0xb1, 0xfb, 0x98, 0x3b, 0x70,
	0xd5, 0x94, 0xc3, 0xee, 0x97, 0x06, 0x20, 0xc1, 0x47, 0x3e, 0x37, 0x17, 0x65, 0x85, 0x76, 0xa0,
	0xc1, 0x5e, 0x31, 0x01, 0x05, 0x64, 0x0e, 0x61, 0x16, 0x59, 0xcd, 0x5a, 0xc4, 0x0b, 0xfa, 0x3e,
	0xd6, 0xde, 0x38, 0x13, 0x93, 0xd8, 0xa7, 0x66, 0x9d, 0x31, 0xd8, 0x66, 0xfb, 0xf9, 0x19, 0xff,
	0x34, 0x01, 0x33, 0x7c, 0x99, 0x98, 0x26, 0xe8, 0x08, 0xda, 0xea, 0x4b, 0x28, 0xbc, 0x38, 0xe2,
	0x93, 0x3c, 0xc6, 0x6a, 0xeb, 0xf7, 0x8a, 0x9e, 0x8e, 0x64, 0xfb, 0x9a, 0xf2, 0x32, 0x2a, 0xf4,
	0x47, 0x2c, 0xc9, 0x9b, 0x0b, 0xa4, 0x70, 0x12, 0x85, 0xd0, 0x56, 0x93, 0xbf, 0x26, 0x52, 0xc4,
	0xdb, 0xfb, 0xe3, 0x44, 0x0e, 0x5f, 0xad, 0x9c, 0xc4, 0xf9, 0x41, 0xd1, 0x1c, 0xf2, 0x60, 0x21,
	0x45, 0x04, 0xba, 0x38, 0x71, 0x9b, 0x77, 0xc7, 0x89, 0x93, 0xf7, 0x99, 0x13, 0x36, 0xeb, 0xe7,
	0x67, 0x3a, 0x3f, 0x86, 0xa5, 0x31, 0x57, 0x82, 0x9a, 0x50, 0x3e, 0xc0, 0xa7, 0xdc, 0x01, 0xaa,
	0x26, 0xfb, 0x44, 0x8f, 0x60, 0xe2, 0xd8, 0xf6, 0x63, 0x61, 0xfa, 0xda, 0xfa, 0x3b, 0x45, 0xaa,
	0x30, 0x15, 0x32, 0x10, 0xc4, 0xc4, 0x64, 0x10, 0x06, 0x04, 0x9b, 0x62, 0xf7, 0x46, 0xe9, 0x5b,
	0x46, 0x67, 0x00, 0x9d, 0xd1, 0x77, 0x53, 0x20, 0xfa, 0xbe, 0x2e, 0xfa, 0xf6, 0x78, 0x88, 0x50,
	0x24, 0xd1, 0x87, 0xf6, 0xa8, 0xeb, 0x29, 0x90, 0xb7, 0xa1, 0xcb, 0x7b, 0x7d, 0xd4, 0x51, 0x13,
	0xef, 0xcd, 0x4a, 0xeb, 0xfe, 0x08, 0xe6, 0x44, 0x5c, 0xe9, 0xb8, 0x05, 0xbd, 0x09, 0x09, 0x60,
	0xb0, 0x14, 0x88, 0x20, 0xd0, 0x48, 0x43, 0x4c, 0xec, 0xa6, 0x40, 0xe1, 0x36, 0x24, 0x24, 0x0b,
	0x07, 0xae, 0x58, 0x59, 0xe2, 0x2b, 0xeb, 0x82, 0xfc, 0x28, 0x70, 0x79, 0x4e, 0xfa, 0xbd, 0x01,
	0x2b, 0x42, 0xd8, 0x16, 0xa6, 0x43, 0x79, 0xec, 0x1d, 0x26, 0x52, 0x37, 0x3d, 0xa4, 0x4b, 0x23,
	0x43, 0xba, 0xa4, 0x86, 0xf4, 0x47, 0x05, 0x10, 0x4d, 0xe0, 0x82, 0xd7, 0x0a, 0xb3, 0xd4, 0x19,
	0x18, 0xed, 0x1f, 0x15, 0x00, 0xfe, 0x42, 0x8a, 0x68, 0x7e, 0x06, 0xcd, 0x14, 0x72, 0x08, 0x17,
	0x27, 0x49, 0x14, 0xaf, 0xe5, 0x51, 0x87, 0xf4, 0x70, 0x79, 0xb0, 0x64, 0x2c, 0xbc, 0xbb, 0x41,
	0x75, 0x2a, 0x7a, 0xae, 0x23, 0x0f, 0xc9, 0x5d, 0x04, 0xec, 0xbb, 0xa3, 0xb9, 0x7f, 0x90, 0xc2,
	0x0e, 0x8d, 0x7f, 0xcb, 0xcd, 0xd2, 0x19, 0x28, 0x4f, 0xf1, 0x88, 0x64, 0x2f, 0x02, 0xf4, 0xed,
	0x31, 0xca, 0x0b, 0x94, 0xa0, 0xf1, 0xbe, 0x4a, 0x35, 0x62, 0x87, 0xc0, 0x5c, 0xd1, 0x19, 0xcf,
	0x1f, 0x8d, 0x67, 0x79, 0x84, 0x1a, 0x1b, 0x18, 0x16, 0x8a, 0x8f, 0x5e, 0x20, 0xf6, 0xff, 0x75,
	0xb1, 0x37, 0x0b, 0xc5, 0x0a, 0x4e, 0xdc, 0x0b, 0xb4, 0xa0, 0x9f, 0x2d, 0xb8, 0x82, 0x02, 0x19,
	0x9b, 0xba, 0x8c, 0xec, 0x95, 0x3e, 0xb4, 0x29, 0x47, 0x70, 0x9c, 0xd5, 0xc3, 0x53, 0xfe, 0xa7,
	0xf7, 0x41, 0x51, 0x18, 0xfe, 0xcc, 0x80, 0x66, 0x56, 0xa3, 0x0b, 0x85, 0xc2, 0x06, 0xd4, 0x24,
	0x50, 0x1c, 0x46, 0xc1, 0x62, 0x21, 0x86, 0x15, 0x6f, 0xf5, 0x5e, 0xfa, 0xdd, 0xfd, 0x73, 0x09,
	0xae, 0x4a, 0xcc, 0x90, 0x44, 0xe3, 0x06, 0xd4, 0x22, 0x4c, 0x06, 0x3a, 0x6a, 0x58, 0xcc, 0xa1,
	0x06, 0x32, 0x48, 0x60, 0x03, 0x44, 0xe9, 0x37, 0x7a, 0x02, 0x75, 0xf5, 0x5d, 0x90, 0x6e, 0x5d,
	0x6c, 0x7f, 0x29, 0x51, 0x7b, 0x27, 0x84, 0xe7, 0xcd, 0x38, 0xea, 0xdb, 0xba, 0x01, 0x35, 0x81,
	0xab, 0xa5, 0x2f, 0x17, 0x1d, 0x70, 0xe8, 0xcb, 0x26, 0xf4, 0xd3, 0xef, 0x8e, 0x05, 0xad, 0x1c,
	0xfb, 0x02, 0xab, 0xde, 0xd5, 0xad, 0x7a, 0xa3, 0x50, 0xe1, 0x14, 0xcf, 0xa8, 0x66, 0xfc, 0xbb,
	0x01, 0xf3, 0x02, 0x36, 0x6c, 0xdb, 0x01, 0xde, 0x89, 0x42, 0x37, 0x76, 0x28, 0x2f, 0x8e, 0x97,
	0xa0, 0x1a, 0x07, 0xde, 0x51, 0x8c, 0x05, 0x80, 0x67, 0x36, 0x9b, 0x16, 0x84, 0x9e, 0x7b, 0x11,
	0x70, 0xac, 0x03, 0xf1, 0xf2, 0xcb, 0x03, 0xf1, 0x65, 0x80, 0x81, 0xd0, 0x8b, 0x69, 0x52, 0xe1,
	0x88, 0xbf, 0x9a, 0x50, 0x7a, 0xee, 0x28, 0x9c, 0x39, 0x31, 0x0a, 0x67, 0xfe, 0xa5, 0x04, 0x0d,
	0xe5, 0x19, 0xed, 0x51, 0x7c, 0xc8, 0x0a, 0x38, 0x8f, 0xe2, 0x43, 0x79, 0xd2, 0x8a, 0x39, 0xc9,
	0x86, 0x3d, 0x17, 0x2d, 0xc2, 0xf4, 0x61, 0xe8, 0x62, 0x5f, 0x56, 0x28, 0x15, 0x73, 0x8a, 0x8f,
	0x7b, 0x2e, 0xab, 0x7b, 0x1d, 0x9b, 0xe2, 0x7e, 0x18, 0x9d, 0x8a, 0x9a, 0x95, 0xc9, 0x03, 0x49,
	0xea, 0xb9, 0xe8, 0x0e, 0xa0, 0xbe, 0x1f, 0xee, 0xd9, 0xbe, 0xa5, 0xae, 0x13, 0x45, 0x6b, 0x53,
	0xcc, 0x6c, 0x0e, 0x57, 0x2f, 0x03, 0x70, 0x15, 0x06, 0x91, 0xe7, 0x08, 0xed, 0x4b, 0x66, 0x95,
	0x51, 0x76, 0x18, 0x01, 0xbd, 0x0e, 0x57, 0x87, 0xd3, 0x56, 0x4c, 0x5c, 0x0e, 0xa4, 0x4b, 0xe6,
	0x4c, 0xba, 0xe4, 0x53, 0xe2, 0xa2, 0x0e, 0x4c, 0x1f, 0xc5, 0x76, 0x40, 0x3d, 0x7a, 0xda, 0x9e,
	0xe2, 0x0f, 0x5a, 0x3a, 0x46, 0x0b, 0x30, 0xe9, 0xe3, 0xa0, 0x4f, 0xf7, 0x79, 0x3f, 0xa1, 0x64,
	0x26, 0x23, 0x34, 0x07, 0x13, 0x27, 0x9e, 0x4b, 0xf7, 0x79, 0x8b, 0xa0, 0x64, 0x8a, 0x01, 0x5b,
	0xbd, 0xcf, 0x1b, 0x50, 0x6d, 0x10, 0xab, 0xc5, 0x88, 0xd1, 0x45, 0x63, 0xaa, 0x5d, 0x13, 0x74,
	0x31, 0xea, 0xfe, 0xb6, 0x04, 0xd7, 0xb6, 0xc3, 0xbe, 0x47, 0xa8, 0xe7, 0x10, 0xe5, 0x7a, 0x59,
	0xd1, 0xf6, 0x0c, 0x66, 0x7d, 0x5b, 0x6d, 0x38, 0x79, 0x3e, 0x53, 0x50, 0x84, 0x66, 0x16, 0xc3,
	0x32, 0x37, 0x64, 0xd0, 0x66, 0x57, 0x5b, 0xcc, 0x0a, 0x4e, 0xc4, 0x98, 0xe8, 0x64, 0xf4, 0x7f,
	0x50, 0x95, 0x7e, 0x48, 0xce, 0x70, 0xc4, 0xe9, 0xc4, 0x11, 0x49, 0x61, 0x89, 0x53, 0x7e, 0x85,
	0x25, 0x4e, 0x65, 0x94, 0xeb, 0xfd, 0xad, 0x04, 0x37, 0x33, 0xb1, 0xf8, 0x38, 0x8c, 0xf8, 0x77,
	0x18, 0xd3, 0x5d, 0x07, 0x07, 0x18, 0x99, 0x80, 0xa4, 0xb7, 0x33, 0xa0, 0x4a, 0x86, 0x75, 0x6e,
	0x1e, 0x2b, 0x15, 0x06, 0xae, 0xd9, 0x1c, 0x0c, 0x07, 0x84, 0xd7, 0x74, 0xcf, 0xa1, 0x2d, 0x79,
	0xaa, 0x28, 0x9f, 0x73, 0x16, 0x97, 0x76, 0x3b, 0xd7, 0x5c, 0x2a, 0x34, 0xa5, 0xb9, 0x90, 0xf0,
	0x51, 0xc8, 0x5c, 0xc2, 0x5d, 0x98, 0x1f, 0xf6, 0xe9, 0x14, 0xf5, 0x45, 0x58, 0x6c, 0x18, 0xef,
	0xa5, 0xdd, 0x3d, 0x35, 0xc5, 0x3c, 0x85, 0x85, 0x61, 0xb3, 0x31, 0xb2, 0xa3, 0x53, 0xeb, 0x28,
	0xc6, 0x91, 0x87, 0xc9, 0x88, 0x32, 0x76, 0x87, 0x2f, 0xde, 0x16, 0x6b, 0x7f, 0x10, 0xe3, 0xe8,
	0xd4, 0x9c, 0x1b, 0x64, 0x69, 0x1e, 0x26, 0xdd, 0xaf, 0x0d, 0x68, 0x6c, 0xdb, 0x1a, 0x5c, 0x66,
	0xf9, 0x8c, 0x7b, 0x61, 0xfa, 0x3a, 0x55, 0xcd, 0x69, 0x46, 0xd8, 0x64, 0xcf, 0x13, 0x82, 0x0a,
	0xa7, 0x97, 0xf8, 0xab, 0x55, 0xc9, 0x3e, 0x59, 0x65, 0xfd, 0xc9, 0xea, 0x41, 0xd3, 0x8e, 0xb0,
	0xad, 0xf5, 0x30, 0x27, 0x0b, 0x33, 0xaf, 0xe6, 0xc9, 0x3e, 0x36, 0x1b, 0xb6, 0x4e, 0x60, 0x48,
	0x34, 0xd5, 0x2a, 0xe9, 0x47, 0x4d, 0xf3, 0x0c, 0x57, 0x97, 0xba, 0x89, 0x86, 0xd4, 0xbf, 0x0d,
	0x98, 0x2b, 0x32, 0xca, 0xf8, 0x34, 0xdd, 0x86, 0x29, 0x1c, 0x45, 0xca, 0xc9, 0xe4, 0x70, 0xcc,
	0xe1, 0xb6, 0xa0, 0x61, 0x1f, 0xdb, 0x9e, 0x2f, 0x5c, 0xc4, 0x0e, 0x52, 0x6b, 0xdc, 0x28, 0x88,
	0x54, 0xf5, 0x6c, 0x57, 0xd3, 0x6d, 0x6c, 0x86, 0xa0, 0xef, 0x43, 0x2b, 0x0e, 0xb2, 0xac, 0x26,
	0x5e, 0x8a, 0x55, 0x53, 0xd9, 0xc8, 0x99, 0x75, 0xbf, 0x32, 0x60, 0x59, 0xeb, 0x0e, 0xe4, 0x02,
	0xe7, 0xc2, 0xdd, 0x82, 0x1f, 0x02, 0x92, 0xdd, 0x02, 0xe1, 0xc0, 0x4a, 0xcf, 0x60, 0x6d, 0xfc,
	0x4b, 0x9a, 0x55, 0xc2, 0x6c, 0x84, 0xfa, 0x82, 0xee, 0x37, 0x93, 0x70, 0x43, 0xc7, 0x0b, 0x39,
	0xc5, 0x2f, 0x83, 0x58, 0x7e, 0x69, 0xc0, 0x72, 0x3e, 0xea, 0x12, 0xac, 0x61, 0x1d, 0xda, 0x83,
	0x24, 0xbe, 0xb7, 0xc7, 0x42, 0x98, 0xac, 0x4a, 0x6b, 0xd9, 0x48, 0x15, 0xf0, 0xe3, 0x23, 0x7b,
	0x20, 0xf0, 0xcd, 0xa2, 0x33, 0x6a, 0x1e, 0x7d, 0x69, 0xc0, 0xad, 0x5c, 0x83, 0xdf, 0xe2, 0xd1,
	0xa2, 0x28, 0x25, 0xb2, 0xed, 0xce, 0x05, 0x94, 0xca, 0x17, 0xc0, 0xaa, 0x62, 0xe2, 0x2e, 0x46,
	0xad, 0x41, 0x5f, 0xc0, 0xb5, 0x4c, 0xbe, 0x61, 0xa6, 0xe6, 0x1a, 0x09, 0x17, 0xdf, 0x3a, 0x9f,
	0x46, 0x5a, 0x3e, 0x62, 0xe6, 0x4e, 0x15, 0xd1, 0xd3, 0x52, 0x32, 0xd5, 0x89, 0xe1, 0xc6, 0xf8,
	0x9b, 0x2d, 0x80, 0x76, 0xdf, 0xd1, 0xa1, 0xdd, 0x1b, 0x85, 0xe5, 0xb9, 0xc6, 0x2f, 0x57, 0x83,
	0x1c, 0x43, 0xf7, 0xec, 0xbb, 0x3b, 0x7f, 0xa5, 0xbe, 0x93, 0x7b, 0x0a, 0x4c, 0x32, 0x50, 0xe5,
	0x7a, 0xb0, 0x38, 0xf2, 0x86, 0x0a, 0xc4, 0xbd, 0xaf, 0x8b, 0x5b, 0x19, 0x97, 0xfc, 0xb3, 0x30,
	0xf6, 0x8f, 0x06, 0x2c, 0x65, 0xd3, 0xc3, 0xe3, 0xd8, 0x7f, 0xe1, 0xf9, 0xfe, 0x21, 0x0e, 0x2e,
	0xd1, 0x4a, 0x7c, 0x36, 0x26, 0x39, 0xdc, 0x39, 0x33, 0x39, 0x28, 0x2a, 0xe4, 0x53, 0xc3, 0x4f,
	0xd3, 0x94, 0x36, 0x62, 0xcb, 0xd8, 0x77, 0xdb, 0x78, 0x15, 0xef, 0x76, 0xf7, 0x9b, 0x0a, 0x5c,
	0xcf, 0x39, 0xb9, 0xaa, 0xc2, 0x65, 0x92, 0xd3, 0x6f, 0x5e, 0x2a, 0x17, 0x88, 0x04, 0xf5, 0xf1,
	0x59, 0x91, 0xa7, 0x28, 0xf5, 0x0a, 0x32, 0xc1, 0xcf, 0x0d, 0xe8, 0x24, 0xad, 0x7a, 0x5b, 0xb6,
	0x04, 0x73, 0xf9, 0x69, 0xeb, 0xdc, 0x3a, 0xb1, 0xb7, 0xaa, 0x20, 0x5f, 0x2e, 0x38, 0x85, 0x93,
	0xff, 0xb3, 0xc0, 0x0c, 0x61, 0x69, 0x8c, 0xba, 0x17, 0xed, 0xd9, 0xd9, 0xa3, 0x7b, 0x76, 0xbf,
	0x36, 0xe0, 0x5a, 0x36, 0x3c, 0x4d, 0x1c, 0x85, 0x31, 0xbd, 0xc4, 0xbb, 0xfd, 0xb0, 0xb0, 0xcb,
	0xbf, 0x92, 0xdb, 0xca, 0xc5, 0x8c, 0x68, 0xf6, 0x7f, 0x6d, 0x40, 0x2b, 0xb7, 0x02, 0x7d, 0x0a,
	0xf3, 0x7a, 0xc1, 0xa2, 0x07, 0xdd, 0xad, 0x33, 0xd0, 0x0b, 0x3e, 0x32, 0x67, 0x7d, 0x9d, 0xc6,
	0x41, 0xf2, 0x85, 0x8a, 0x95, 0x11, 0x35, 0x46, 0x79, 0x54, 0x8d, 0xf1, 0xbb, 0x12, 0xb4, 0x73,
	0x8e, 0x2a, 0xef, 0xfa, 0x32, 0xd1, 0xbc, 0x0f, 0xad, 0x48, 0xb0, 0x91, 0xcd, 0x73, 0x22, 0x83,
	0xf7, 0xfe, 0x59, 0x81, 0x92, 0xc8, 0x97, 0xc6, 0x10, 0x2d, 0x0d, 0x92, 0x44, 0x47, 0x23, 0xd2,
	0xa9, 0x9d, 0x3d, 0x98, 0x2b, 0x5a, 0x78, 0xfe, 0xbe, 0x47, 0x86, 0x8b, 0xea, 0x91, 0x9f, 0x43,
	0x23, 0x33, 0x8b, 0x3e, 0x84, 0x96, 0x80, 0xe2, 0xf9, 0x1f, 0x3f, 0x8a, 0xf0, 0xaa, 0xd2, 0x97,
	0x31, 0x39, 0x86, 0x57, 0x08, 0xdd, 0x5f, 0x95, 0x44, 0x01, 0xa2, 0xfe, 0xfe, 0x20, 0x6b, 0x0c,
	0xa3, 0xb8, 0xc6, 0xc8, 0xb4, 0xc5, 0xb4, 0x72, 0xa5, 0x9c, 0x29, 0x57, 0x0a, 0xaa, 0x86, 0x4a,
	0x41, 0xd5, 0x80, 0xee, 0x43, 0x4d, 0xad, 0x51, 0x04, 0xf8, 0xee, 0x64, 0x7d, 0x4e, 0x71, 0x5d,
	0x75, 0x79, 0xd1, 0xef, 0x4e, 0x93, 0x97, 0xfa, 0xdd, 0x69, 0xfd, 0xaf, 0x15, 0x68, 0x6d, 0x3b,
	0x21, 0x11, 0x0e, 0x92, 0x08, 0x46, 0xcf, 0xe1, 0xe6, 0x16, 0xa6, 0x0f, 0x02, 0x97, 0x73, 0xd8,
	0xb4, 0x83, 0xcd, 0x08, 0xdb, 0x54, 0xfe, 0x32, 0xc9, 0x38, 0xa2, 0xeb, 0x23, 0x3c, 0x8c, 0x27,
	0x93, 0xce, 0xf2, 0x58, 0xff, 0xeb, 0x5e, 0x41, 0x3f, 0xe1, 0xff, 0xc2, 0x20, 0x25, 0xe4, 0x10,
	0xf8, 0x9d, 0x71, 0xdc, 0xb3, 0xab, 0x3b, 0x6f, 0x9f, 0x0b, 0x24, 0x76, 0xaf, 0xa0, 0x2f, 0x60,
	0x79, 0x8c, 0xf4, 0xcf, 0xd6, 0xff, 0xdb, 0xf2, 0x8f, 0x61, 0x51, 0x97, 0xaf, 0x3e, 0xf0, 0x6f,
	0x9e, 0x21, 0x5b, 0x59, 0xdb, 0x79, 0xeb, 0x1c, 0x0f, 0x62, 0xf7, 0x0a, 0x0a, 0x34, 0xb9, 0x9b,
	0x76, 0x90, 0x44, 0x1c, 0xb7, 0xe8, 0xed, 0x33, 0xe4, 0x26, 0x6b, 0x3b, 0x6f, 0xbc, 0x64, 0x6e,
	0xe9, 0x5e, 0xf9, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x34, 0x47, 0x2b, 0x86, 0x84, 0x25, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosOneApiServiceClient is the client API for LcosOneApiService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosOneApiServiceClient interface {
	GetAndCheckCanCreateWaybillLine(ctx context.Context, in *OneApiRequest, opts ...grpc.CallOption) (*OneApiResponse, error)
	GetAndCheckForCheckoutScene(ctx context.Context, in *OneApiRequestForCheckoutScene, opts ...grpc.CallOption) (*OneApiResponseForCheckoutScene, error)
	GetAndCheckForCheckoutSceneV2(ctx context.Context, in *OneApiRequestForCheckoutScene, opts ...grpc.CallOption) (*OneApiResponseForCheckoutScene, error)
	GetAndCheckForFulfillment(ctx context.Context, in *OneApiRequestForFulfillment, opts ...grpc.CallOption) (*OneApiResponseForFulfillment, error)
	GetAndCheckCanRerouteLine(ctx context.Context, in *OneApiRequestForReroute, opts ...grpc.CallOption) (*OneApiResponseForReroute, error)
}

type lcosOneApiServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosOneApiServiceClient(cc grpc.ClientConnInterface) LcosOneApiServiceClient {
	return &lcosOneApiServiceClient{cc}
}

func (c *lcosOneApiServiceClient) GetAndCheckCanCreateWaybillLine(ctx context.Context, in *OneApiRequest, opts ...grpc.CallOption) (*OneApiResponse, error) {
	out := new(OneApiResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosOneApiService/GetAndCheckCanCreateWaybillLine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosOneApiServiceClient) GetAndCheckForCheckoutScene(ctx context.Context, in *OneApiRequestForCheckoutScene, opts ...grpc.CallOption) (*OneApiResponseForCheckoutScene, error) {
	out := new(OneApiResponseForCheckoutScene)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosOneApiService/GetAndCheckForCheckoutScene", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosOneApiServiceClient) GetAndCheckForCheckoutSceneV2(ctx context.Context, in *OneApiRequestForCheckoutScene, opts ...grpc.CallOption) (*OneApiResponseForCheckoutScene, error) {
	out := new(OneApiResponseForCheckoutScene)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosOneApiService/GetAndCheckForCheckoutSceneV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosOneApiServiceClient) GetAndCheckForFulfillment(ctx context.Context, in *OneApiRequestForFulfillment, opts ...grpc.CallOption) (*OneApiResponseForFulfillment, error) {
	out := new(OneApiResponseForFulfillment)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosOneApiService/GetAndCheckForFulfillment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosOneApiServiceClient) GetAndCheckCanRerouteLine(ctx context.Context, in *OneApiRequestForReroute, opts ...grpc.CallOption) (*OneApiResponseForReroute, error) {
	out := new(OneApiResponseForReroute)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosOneApiService/GetAndCheckCanRerouteLine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosOneApiServiceServer is the server API for LcosOneApiService service.
type LcosOneApiServiceServer interface {
	GetAndCheckCanCreateWaybillLine(context.Context, *OneApiRequest) (*OneApiResponse, error)
	GetAndCheckForCheckoutScene(context.Context, *OneApiRequestForCheckoutScene) (*OneApiResponseForCheckoutScene, error)
	GetAndCheckForCheckoutSceneV2(context.Context, *OneApiRequestForCheckoutScene) (*OneApiResponseForCheckoutScene, error)
	GetAndCheckForFulfillment(context.Context, *OneApiRequestForFulfillment) (*OneApiResponseForFulfillment, error)
	GetAndCheckCanRerouteLine(context.Context, *OneApiRequestForReroute) (*OneApiResponseForReroute, error)
}

// UnimplementedLcosOneApiServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLcosOneApiServiceServer struct {
}

func (*UnimplementedLcosOneApiServiceServer) GetAndCheckCanCreateWaybillLine(ctx context.Context, req *OneApiRequest) (*OneApiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAndCheckCanCreateWaybillLine not implemented")
}
func (*UnimplementedLcosOneApiServiceServer) GetAndCheckForCheckoutScene(ctx context.Context, req *OneApiRequestForCheckoutScene) (*OneApiResponseForCheckoutScene, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAndCheckForCheckoutScene not implemented")
}
func (*UnimplementedLcosOneApiServiceServer) GetAndCheckForCheckoutSceneV2(ctx context.Context, req *OneApiRequestForCheckoutScene) (*OneApiResponseForCheckoutScene, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAndCheckForCheckoutSceneV2 not implemented")
}
func (*UnimplementedLcosOneApiServiceServer) GetAndCheckForFulfillment(ctx context.Context, req *OneApiRequestForFulfillment) (*OneApiResponseForFulfillment, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAndCheckForFulfillment not implemented")
}
func (*UnimplementedLcosOneApiServiceServer) GetAndCheckCanRerouteLine(ctx context.Context, req *OneApiRequestForReroute) (*OneApiResponseForReroute, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAndCheckCanRerouteLine not implemented")
}

func RegisterLcosOneApiServiceServer(s *grpc.Server, srv LcosOneApiServiceServer) {
	s.RegisterService(&_LcosOneApiService_serviceDesc, srv)
}

func _LcosOneApiService_GetAndCheckCanCreateWaybillLine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneApiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosOneApiServiceServer).GetAndCheckCanCreateWaybillLine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosOneApiService/GetAndCheckCanCreateWaybillLine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosOneApiServiceServer).GetAndCheckCanCreateWaybillLine(ctx, req.(*OneApiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosOneApiService_GetAndCheckForCheckoutScene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneApiRequestForCheckoutScene)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosOneApiServiceServer).GetAndCheckForCheckoutScene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosOneApiService/GetAndCheckForCheckoutScene",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosOneApiServiceServer).GetAndCheckForCheckoutScene(ctx, req.(*OneApiRequestForCheckoutScene))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosOneApiService_GetAndCheckForCheckoutSceneV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneApiRequestForCheckoutScene)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosOneApiServiceServer).GetAndCheckForCheckoutSceneV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosOneApiService/GetAndCheckForCheckoutSceneV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosOneApiServiceServer).GetAndCheckForCheckoutSceneV2(ctx, req.(*OneApiRequestForCheckoutScene))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosOneApiService_GetAndCheckForFulfillment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneApiRequestForFulfillment)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosOneApiServiceServer).GetAndCheckForFulfillment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosOneApiService/GetAndCheckForFulfillment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosOneApiServiceServer).GetAndCheckForFulfillment(ctx, req.(*OneApiRequestForFulfillment))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosOneApiService_GetAndCheckCanRerouteLine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneApiRequestForReroute)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosOneApiServiceServer).GetAndCheckCanRerouteLine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosOneApiService/GetAndCheckCanRerouteLine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosOneApiServiceServer).GetAndCheckCanRerouteLine(ctx, req.(*OneApiRequestForReroute))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosOneApiService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosOneApiService",
	HandlerType: (*LcosOneApiServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAndCheckCanCreateWaybillLine",
			Handler:    _LcosOneApiService_GetAndCheckCanCreateWaybillLine_Handler,
		},
		{
			MethodName: "GetAndCheckForCheckoutScene",
			Handler:    _LcosOneApiService_GetAndCheckForCheckoutScene_Handler,
		},
		{
			MethodName: "GetAndCheckForCheckoutSceneV2",
			Handler:    _LcosOneApiService_GetAndCheckForCheckoutSceneV2_Handler,
		},
		{
			MethodName: "GetAndCheckForFulfillment",
			Handler:    _LcosOneApiService_GetAndCheckForFulfillment_Handler,
		},
		{
			MethodName: "GetAndCheckCanRerouteLine",
			Handler:    _LcosOneApiService_GetAndCheckCanRerouteLine_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_one_api.proto",
}
