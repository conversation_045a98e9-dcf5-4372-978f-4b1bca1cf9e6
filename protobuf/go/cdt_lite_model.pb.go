// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cdt_lite_model.proto

package lcos_protobuf

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type CDTAutoUpdateCalculateDataTabLite struct {
	AutoUpdateRuleId      *uint64  `protobuf:"varint,1,req,name=auto_update_rule_id,json=autoUpdateRuleId" json:"auto_update_rule_id,omitempty"`
	ProductId             *string  `protobuf:"bytes,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	UpdateEvent           *uint32  `protobuf:"varint,3,req,name=update_event,json=updateEvent" json:"update_event,omitempty"`
	IsLm                  *uint32  `protobuf:"varint,4,req,name=is_lm,json=isLm" json:"is_lm,omitempty"`
	ThreePlUniqueKey      *string  `protobuf:"bytes,5,req,name=three_pl_unique_key,json=threePlUniqueKey" json:"three_pl_unique_key,omitempty"`
	OriginRegion          *string  `protobuf:"bytes,6,req,name=origin_region,json=originRegion" json:"origin_region,omitempty"`
	OriginLocationId      *int32   `protobuf:"varint,7,req,name=origin_location_id,json=originLocationId" json:"origin_location_id,omitempty"`
	DestinationRegion     *string  `protobuf:"bytes,8,req,name=destination_region,json=destinationRegion" json:"destination_region,omitempty"`
	DestinationLocationId *int32   `protobuf:"varint,9,req,name=destination_location_id,json=destinationLocationId" json:"destination_location_id,omitempty"`
	DestinationCepInitial *int32   `protobuf:"varint,10,req,name=destination_cep_initial,json=destinationCepInitial" json:"destination_cep_initial,omitempty"`
	DestinationCepFinal   *int32   `protobuf:"varint,11,req,name=destination_cep_final,json=destinationCepFinal" json:"destination_cep_final,omitempty"`
	DestinationPostcode   *string  `protobuf:"bytes,12,req,name=destination_postcode,json=destinationPostcode" json:"destination_postcode,omitempty"`
	LeadTimeMin           *float64 `protobuf:"fixed64,13,req,name=lead_time_min,json=leadTimeMin" json:"lead_time_min,omitempty"`
	LeadTimeMax           *float64 `protobuf:"fixed64,14,req,name=lead_time_max,json=leadTimeMax" json:"lead_time_max,omitempty"`
	CbLmLeadTimeMax       *float64 `protobuf:"fixed64,15,req,name=cb_lm_lead_time_max,json=cbLmLeadTimeMax" json:"cb_lm_lead_time_max,omitempty"`
	CdtVersion            *uint32  `protobuf:"varint,16,req,name=cdt_version,json=cdtVersion" json:"cdt_version,omitempty"`
	DdlForwardCdt         *float64 `protobuf:"fixed64,17,req,name=ddl_forward_cdt,json=ddlForwardCdt" json:"ddl_forward_cdt,omitempty"`
	DdlBackwardCdt        *float64 `protobuf:"fixed64,18,req,name=ddl_backward_cdt,json=ddlBackwardCdt" json:"ddl_backward_cdt,omitempty"`
	CdtExtraData          *string  `protobuf:"bytes,19,req,name=cdt_extra_data,json=cdtExtraData" json:"cdt_extra_data,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *CDTAutoUpdateCalculateDataTabLite) Reset()         { *m = CDTAutoUpdateCalculateDataTabLite{} }
func (m *CDTAutoUpdateCalculateDataTabLite) String() string { return proto.CompactTextString(m) }
func (*CDTAutoUpdateCalculateDataTabLite) ProtoMessage()    {}
func (*CDTAutoUpdateCalculateDataTabLite) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bbff4f5b8ec7824, []int{0}
}

func (m *CDTAutoUpdateCalculateDataTabLite) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CDTAutoUpdateCalculateDataTabLite.Unmarshal(m, b)
}
func (m *CDTAutoUpdateCalculateDataTabLite) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CDTAutoUpdateCalculateDataTabLite.Marshal(b, m, deterministic)
}
func (m *CDTAutoUpdateCalculateDataTabLite) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CDTAutoUpdateCalculateDataTabLite.Merge(m, src)
}
func (m *CDTAutoUpdateCalculateDataTabLite) XXX_Size() int {
	return xxx_messageInfo_CDTAutoUpdateCalculateDataTabLite.Size(m)
}
func (m *CDTAutoUpdateCalculateDataTabLite) XXX_DiscardUnknown() {
	xxx_messageInfo_CDTAutoUpdateCalculateDataTabLite.DiscardUnknown(m)
}

var xxx_messageInfo_CDTAutoUpdateCalculateDataTabLite proto.InternalMessageInfo

func (m *CDTAutoUpdateCalculateDataTabLite) GetAutoUpdateRuleId() uint64 {
	if m != nil && m.AutoUpdateRuleId != nil {
		return *m.AutoUpdateRuleId
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetUpdateEvent() uint32 {
	if m != nil && m.UpdateEvent != nil {
		return *m.UpdateEvent
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetIsLm() uint32 {
	if m != nil && m.IsLm != nil {
		return *m.IsLm
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetThreePlUniqueKey() string {
	if m != nil && m.ThreePlUniqueKey != nil {
		return *m.ThreePlUniqueKey
	}
	return ""
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetOriginRegion() string {
	if m != nil && m.OriginRegion != nil {
		return *m.OriginRegion
	}
	return ""
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetOriginLocationId() int32 {
	if m != nil && m.OriginLocationId != nil {
		return *m.OriginLocationId
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetDestinationRegion() string {
	if m != nil && m.DestinationRegion != nil {
		return *m.DestinationRegion
	}
	return ""
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetDestinationLocationId() int32 {
	if m != nil && m.DestinationLocationId != nil {
		return *m.DestinationLocationId
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetDestinationCepInitial() int32 {
	if m != nil && m.DestinationCepInitial != nil {
		return *m.DestinationCepInitial
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetDestinationCepFinal() int32 {
	if m != nil && m.DestinationCepFinal != nil {
		return *m.DestinationCepFinal
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetDestinationPostcode() string {
	if m != nil && m.DestinationPostcode != nil {
		return *m.DestinationPostcode
	}
	return ""
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetLeadTimeMin() float64 {
	if m != nil && m.LeadTimeMin != nil {
		return *m.LeadTimeMin
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetLeadTimeMax() float64 {
	if m != nil && m.LeadTimeMax != nil {
		return *m.LeadTimeMax
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetCbLmLeadTimeMax() float64 {
	if m != nil && m.CbLmLeadTimeMax != nil {
		return *m.CbLmLeadTimeMax
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetCdtVersion() uint32 {
	if m != nil && m.CdtVersion != nil {
		return *m.CdtVersion
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetDdlForwardCdt() float64 {
	if m != nil && m.DdlForwardCdt != nil {
		return *m.DdlForwardCdt
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetDdlBackwardCdt() float64 {
	if m != nil && m.DdlBackwardCdt != nil {
		return *m.DdlBackwardCdt
	}
	return 0
}

func (m *CDTAutoUpdateCalculateDataTabLite) GetCdtExtraData() string {
	if m != nil && m.CdtExtraData != nil {
		return *m.CdtExtraData
	}
	return ""
}

type CdtManualUpdateLocationDataTabLite struct {
	IsLm                  *uint32  `protobuf:"varint,1,req,name=is_lm,json=isLm" json:"is_lm,omitempty"`
	IsSiteLine            *uint32  `protobuf:"varint,2,req,name=is_site_line,json=isSiteLine" json:"is_site_line,omitempty"`
	LocationLevel         *int32   `protobuf:"varint,3,req,name=location_level,json=locationLevel" json:"location_level,omitempty"`
	OriginLocationId      *int32   `protobuf:"varint,4,req,name=origin_location_id,json=originLocationId" json:"origin_location_id,omitempty"`
	DestinationLocationId *int32   `protobuf:"varint,5,req,name=destination_location_id,json=destinationLocationId" json:"destination_location_id,omitempty"`
	ProductId             *string  `protobuf:"bytes,6,req,name=product_id,json=productId" json:"product_id,omitempty"`
	LeadTimeMin           *float64 `protobuf:"fixed64,7,req,name=lead_time_min,json=leadTimeMin" json:"lead_time_min,omitempty"`
	LeadTimeMax           *float64 `protobuf:"fixed64,8,req,name=lead_time_max,json=leadTimeMax" json:"lead_time_max,omitempty"`
	CbLmLeadTimeMax       *float64 `protobuf:"fixed64,9,req,name=cb_lm_lead_time_max,json=cbLmLeadTimeMax" json:"cb_lm_lead_time_max,omitempty"`
	Region                *string  `protobuf:"bytes,10,req,name=region" json:"region,omitempty"`
	ExtraData             *string  `protobuf:"bytes,11,req,name=extra_data,json=extraData" json:"extra_data,omitempty"`
	OverrideType          *string  `protobuf:"bytes,12,req,name=override_type,json=overrideType" json:"override_type,omitempty"`
	ObjectType            *uint32  `protobuf:"varint,13,req,name=object_type,json=objectType" json:"object_type,omitempty"`
	UpdateEvent           *uint32  `protobuf:"varint,14,req,name=update_event,json=updateEvent" json:"update_event,omitempty"`
	CdtExtraData          *string  `protobuf:"bytes,15,req,name=cdt_extra_data,json=cdtExtraData" json:"cdt_extra_data,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *CdtManualUpdateLocationDataTabLite) Reset()         { *m = CdtManualUpdateLocationDataTabLite{} }
func (m *CdtManualUpdateLocationDataTabLite) String() string { return proto.CompactTextString(m) }
func (*CdtManualUpdateLocationDataTabLite) ProtoMessage()    {}
func (*CdtManualUpdateLocationDataTabLite) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bbff4f5b8ec7824, []int{1}
}

func (m *CdtManualUpdateLocationDataTabLite) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CdtManualUpdateLocationDataTabLite.Unmarshal(m, b)
}
func (m *CdtManualUpdateLocationDataTabLite) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CdtManualUpdateLocationDataTabLite.Marshal(b, m, deterministic)
}
func (m *CdtManualUpdateLocationDataTabLite) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CdtManualUpdateLocationDataTabLite.Merge(m, src)
}
func (m *CdtManualUpdateLocationDataTabLite) XXX_Size() int {
	return xxx_messageInfo_CdtManualUpdateLocationDataTabLite.Size(m)
}
func (m *CdtManualUpdateLocationDataTabLite) XXX_DiscardUnknown() {
	xxx_messageInfo_CdtManualUpdateLocationDataTabLite.DiscardUnknown(m)
}

var xxx_messageInfo_CdtManualUpdateLocationDataTabLite proto.InternalMessageInfo

func (m *CdtManualUpdateLocationDataTabLite) GetIsLm() uint32 {
	if m != nil && m.IsLm != nil {
		return *m.IsLm
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetIsSiteLine() uint32 {
	if m != nil && m.IsSiteLine != nil {
		return *m.IsSiteLine
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetLocationLevel() int32 {
	if m != nil && m.LocationLevel != nil {
		return *m.LocationLevel
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetOriginLocationId() int32 {
	if m != nil && m.OriginLocationId != nil {
		return *m.OriginLocationId
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetDestinationLocationId() int32 {
	if m != nil && m.DestinationLocationId != nil {
		return *m.DestinationLocationId
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *CdtManualUpdateLocationDataTabLite) GetLeadTimeMin() float64 {
	if m != nil && m.LeadTimeMin != nil {
		return *m.LeadTimeMin
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetLeadTimeMax() float64 {
	if m != nil && m.LeadTimeMax != nil {
		return *m.LeadTimeMax
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetCbLmLeadTimeMax() float64 {
	if m != nil && m.CbLmLeadTimeMax != nil {
		return *m.CbLmLeadTimeMax
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *CdtManualUpdateLocationDataTabLite) GetExtraData() string {
	if m != nil && m.ExtraData != nil {
		return *m.ExtraData
	}
	return ""
}

func (m *CdtManualUpdateLocationDataTabLite) GetOverrideType() string {
	if m != nil && m.OverrideType != nil {
		return *m.OverrideType
	}
	return ""
}

func (m *CdtManualUpdateLocationDataTabLite) GetObjectType() uint32 {
	if m != nil && m.ObjectType != nil {
		return *m.ObjectType
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetUpdateEvent() uint32 {
	if m != nil && m.UpdateEvent != nil {
		return *m.UpdateEvent
	}
	return 0
}

func (m *CdtManualUpdateLocationDataTabLite) GetCdtExtraData() string {
	if m != nil && m.CdtExtraData != nil {
		return *m.CdtExtraData
	}
	return ""
}

type CdtManualUpdatePostcodeDataTabLite struct {
	IsLm                 *uint32  `protobuf:"varint,1,req,name=is_lm,json=isLm" json:"is_lm,omitempty"`
	IsSiteLine           *uint32  `protobuf:"varint,2,req,name=is_site_line,json=isSiteLine" json:"is_site_line,omitempty"`
	LocationLevel        *int32   `protobuf:"varint,3,req,name=location_level,json=locationLevel" json:"location_level,omitempty"`
	OriginLocationId     *int32   `protobuf:"varint,4,req,name=origin_location_id,json=originLocationId" json:"origin_location_id,omitempty"`
	DestinationPostcode  *string  `protobuf:"bytes,5,req,name=destination_postcode,json=destinationPostcode" json:"destination_postcode,omitempty"`
	ProductId            *string  `protobuf:"bytes,6,req,name=product_id,json=productId" json:"product_id,omitempty"`
	LeadTimeMin          *float64 `protobuf:"fixed64,7,req,name=lead_time_min,json=leadTimeMin" json:"lead_time_min,omitempty"`
	LeadTimeMax          *float64 `protobuf:"fixed64,8,req,name=lead_time_max,json=leadTimeMax" json:"lead_time_max,omitempty"`
	CbLmLeadTimeMax      *float64 `protobuf:"fixed64,9,req,name=cb_lm_lead_time_max,json=cbLmLeadTimeMax" json:"cb_lm_lead_time_max,omitempty"`
	Region               *string  `protobuf:"bytes,10,req,name=region" json:"region,omitempty"`
	ExtraData            *string  `protobuf:"bytes,11,req,name=extra_data,json=extraData" json:"extra_data,omitempty"`
	OverrideType         *string  `protobuf:"bytes,12,req,name=override_type,json=overrideType" json:"override_type,omitempty"`
	ObjectType           *uint32  `protobuf:"varint,13,req,name=object_type,json=objectType" json:"object_type,omitempty"`
	UpdateEvent          *uint32  `protobuf:"varint,14,req,name=update_event,json=updateEvent" json:"update_event,omitempty"`
	CdtExtraData         *string  `protobuf:"bytes,15,req,name=cdt_extra_data,json=cdtExtraData" json:"cdt_extra_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CdtManualUpdatePostcodeDataTabLite) Reset()         { *m = CdtManualUpdatePostcodeDataTabLite{} }
func (m *CdtManualUpdatePostcodeDataTabLite) String() string { return proto.CompactTextString(m) }
func (*CdtManualUpdatePostcodeDataTabLite) ProtoMessage()    {}
func (*CdtManualUpdatePostcodeDataTabLite) Descriptor() ([]byte, []int) {
	return fileDescriptor_6bbff4f5b8ec7824, []int{2}
}

func (m *CdtManualUpdatePostcodeDataTabLite) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CdtManualUpdatePostcodeDataTabLite.Unmarshal(m, b)
}
func (m *CdtManualUpdatePostcodeDataTabLite) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CdtManualUpdatePostcodeDataTabLite.Marshal(b, m, deterministic)
}
func (m *CdtManualUpdatePostcodeDataTabLite) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CdtManualUpdatePostcodeDataTabLite.Merge(m, src)
}
func (m *CdtManualUpdatePostcodeDataTabLite) XXX_Size() int {
	return xxx_messageInfo_CdtManualUpdatePostcodeDataTabLite.Size(m)
}
func (m *CdtManualUpdatePostcodeDataTabLite) XXX_DiscardUnknown() {
	xxx_messageInfo_CdtManualUpdatePostcodeDataTabLite.DiscardUnknown(m)
}

var xxx_messageInfo_CdtManualUpdatePostcodeDataTabLite proto.InternalMessageInfo

func (m *CdtManualUpdatePostcodeDataTabLite) GetIsLm() uint32 {
	if m != nil && m.IsLm != nil {
		return *m.IsLm
	}
	return 0
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetIsSiteLine() uint32 {
	if m != nil && m.IsSiteLine != nil {
		return *m.IsSiteLine
	}
	return 0
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetLocationLevel() int32 {
	if m != nil && m.LocationLevel != nil {
		return *m.LocationLevel
	}
	return 0
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetOriginLocationId() int32 {
	if m != nil && m.OriginLocationId != nil {
		return *m.OriginLocationId
	}
	return 0
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetDestinationPostcode() string {
	if m != nil && m.DestinationPostcode != nil {
		return *m.DestinationPostcode
	}
	return ""
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetLeadTimeMin() float64 {
	if m != nil && m.LeadTimeMin != nil {
		return *m.LeadTimeMin
	}
	return 0
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetLeadTimeMax() float64 {
	if m != nil && m.LeadTimeMax != nil {
		return *m.LeadTimeMax
	}
	return 0
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetCbLmLeadTimeMax() float64 {
	if m != nil && m.CbLmLeadTimeMax != nil {
		return *m.CbLmLeadTimeMax
	}
	return 0
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetExtraData() string {
	if m != nil && m.ExtraData != nil {
		return *m.ExtraData
	}
	return ""
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetOverrideType() string {
	if m != nil && m.OverrideType != nil {
		return *m.OverrideType
	}
	return ""
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetObjectType() uint32 {
	if m != nil && m.ObjectType != nil {
		return *m.ObjectType
	}
	return 0
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetUpdateEvent() uint32 {
	if m != nil && m.UpdateEvent != nil {
		return *m.UpdateEvent
	}
	return 0
}

func (m *CdtManualUpdatePostcodeDataTabLite) GetCdtExtraData() string {
	if m != nil && m.CdtExtraData != nil {
		return *m.CdtExtraData
	}
	return ""
}

func init() {
	proto.RegisterType((*CDTAutoUpdateCalculateDataTabLite)(nil), "lcos_protobuf.CDTAutoUpdateCalculateDataTabLite")
	proto.RegisterType((*CdtManualUpdateLocationDataTabLite)(nil), "lcos_protobuf.CdtManualUpdateLocationDataTabLite")
	proto.RegisterType((*CdtManualUpdatePostcodeDataTabLite)(nil), "lcos_protobuf.CdtManualUpdatePostcodeDataTabLite")
}

func init() {
	proto.RegisterFile("cdt_lite_model.proto", fileDescriptor_6bbff4f5b8ec7824)
}

var fileDescriptor_6bbff4f5b8ec7824 = []byte{
	// 686 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x94, 0xc1, 0x6f, 0x1a, 0x39,
	0x14, 0xc6, 0x15, 0x16, 0x48, 0x30, 0x0c, 0x10, 0x93, 0xec, 0xce, 0x65, 0xb5, 0x84, 0x6d, 0x2b,
	0x0e, 0x49, 0xa5, 0xf6, 0xd0, 0x7b, 0x4b, 0x12, 0x09, 0x95, 0x48, 0xd1, 0x94, 0xf4, 0x6a, 0x19,
	0xfb, 0x25, 0x75, 0xe3, 0x19, 0x4f, 0x67, 0xde, 0xd0, 0xf0, 0xcf, 0x55, 0x95, 0xfa, 0x8f, 0x55,
	0xf6, 0x0c, 0x68, 0x02, 0x21, 0xca, 0xa5, 0x52, 0x0f, 0xbd, 0xc1, 0xf7, 0xbe, 0xf7, 0x6c, 0xf0,
	0xef, 0x7b, 0xe4, 0x40, 0x48, 0x64, 0x5a, 0x21, 0xb0, 0xd0, 0x48, 0xd0, 0x2f, 0xe3, 0xc4, 0xa0,
	0xa1, 0x9e, 0x16, 0x26, 0x65, 0xee, 0xf3, 0x2c, 0xbb, 0x1e, 0x7c, 0xaf, 0x93, 0xa3, 0xd1, 0xe9,
	0xf4, 0x6d, 0x86, 0xe6, 0x2a, 0x96, 0x1c, 0x61, 0xc4, 0xb5, 0xc8, 0x34, 0x47, 0x38, 0xe5, 0xc8,
	0xa7, 0x7c, 0x36, 0x51, 0x08, 0xf4, 0x84, 0xf4, 0x78, 0x86, 0x86, 0x65, 0xce, 0xc2, 0x92, 0x4c,
	0x03, 0x53, 0xd2, 0xdf, 0xe9, 0x57, 0x86, 0xd5, 0xa0, 0xcb, 0x57, 0xcd, 0x41, 0xa6, 0x61, 0x2c,
	0xe9, 0xbf, 0x84, 0xc4, 0x89, 0x91, 0x99, 0x40, 0xeb, 0xaa, 0xf4, 0x2b, 0xc3, 0x46, 0xd0, 0x28,
	0x94, 0xb1, 0xa4, 0x47, 0xa4, 0x55, 0x0c, 0x82, 0x39, 0x44, 0xe8, 0xff, 0xd5, 0xaf, 0x0c, 0xbd,
	0xa0, 0x99, 0x6b, 0x67, 0x56, 0xa2, 0x3d, 0x52, 0x53, 0x29, 0xd3, 0xa1, 0x5f, 0x75, 0xb5, 0xaa,
	0x4a, 0x27, 0xa1, 0xbd, 0x05, 0x7e, 0x4a, 0x00, 0x58, 0xac, 0x59, 0x16, 0xa9, 0x2f, 0x19, 0xb0,
	0x5b, 0x58, 0xf8, 0x35, 0x37, 0xbf, 0xeb, 0x4a, 0x97, 0xfa, 0xca, 0x15, 0xde, 0xc3, 0x82, 0xfe,
	0x4f, 0x3c, 0x93, 0xa8, 0x1b, 0x15, 0xb1, 0x04, 0x6e, 0x94, 0x89, 0xfc, 0xba, 0x33, 0xb6, 0x72,
	0x31, 0x70, 0x1a, 0x3d, 0x26, 0xb4, 0x30, 0x69, 0x23, 0x38, 0x2a, 0x13, 0xd9, 0x2b, 0xef, 0xf6,
	0x2b, 0xc3, 0x5a, 0xd0, 0xcd, 0x2b, 0x93, 0xa2, 0x30, 0x96, 0xf4, 0x84, 0x50, 0x09, 0x29, 0xaa,
	0x28, 0x77, 0x16, 0x73, 0xf7, 0xdc, 0xdc, 0xfd, 0x52, 0xa5, 0x18, 0xfe, 0x86, 0xfc, 0x53, 0xb6,
	0x97, 0x4f, 0x68, 0xb8, 0x13, 0x0e, 0x4b, 0xe5, 0xd2, 0x31, 0x6b, 0x7d, 0x02, 0x62, 0xa6, 0x22,
	0x85, 0x8a, 0x6b, 0x9f, 0x6c, 0xf4, 0x8d, 0x20, 0x1e, 0xe7, 0x45, 0xfa, 0x9a, 0x1c, 0xae, 0xf7,
	0x5d, 0xab, 0x88, 0x6b, 0xbf, 0xe9, 0xba, 0x7a, 0xf7, 0xbb, 0xce, 0x6d, 0x89, 0xbe, 0x22, 0x07,
	0xe5, 0x9e, 0xd8, 0xa4, 0x28, 0x8c, 0x04, 0xbf, 0xe5, 0x7e, 0x54, 0xb9, 0xe5, 0xb2, 0x28, 0xd1,
	0x01, 0xf1, 0x34, 0x70, 0xc9, 0x50, 0x85, 0xc0, 0x42, 0x15, 0xf9, 0x5e, 0xbf, 0x32, 0xdc, 0x09,
	0x9a, 0x56, 0x9c, 0xaa, 0x10, 0x2e, 0x54, 0xb4, 0xe6, 0xe1, 0x77, 0x7e, 0x7b, 0xcd, 0xc3, 0xef,
	0xe8, 0x31, 0xe9, 0x89, 0x19, 0xd3, 0x21, 0xbb, 0xef, 0xec, 0x38, 0x67, 0x47, 0xcc, 0x26, 0xe1,
	0xa4, 0xe4, 0xfe, 0x8f, 0x34, 0x2d, 0xd0, 0x73, 0x48, 0x52, 0xfb, 0xa7, 0x77, 0x1d, 0x18, 0x44,
	0x48, 0xfc, 0x98, 0x2b, 0xf4, 0x05, 0xe9, 0x48, 0xa9, 0xd9, 0xb5, 0x49, 0xbe, 0xf2, 0x44, 0x32,
	0x21, 0xd1, 0xdf, 0x77, 0xa3, 0x3c, 0x29, 0xf5, 0x79, 0xae, 0x8e, 0x24, 0xd2, 0x21, 0xe9, 0x5a,
	0xdf, 0x8c, 0x8b, 0xdb, 0x95, 0x91, 0x3a, 0x63, 0x5b, 0x4a, 0xfd, 0xae, 0x90, 0xad, 0xf3, 0x19,
	0x69, 0xdb, 0x23, 0xe1, 0x0e, 0x13, 0xce, 0x24, 0x47, 0xee, 0xf7, 0x72, 0x84, 0x84, 0xc4, 0x33,
	0x2b, 0xda, 0x8c, 0x0c, 0x7e, 0x54, 0xc9, 0x60, 0x24, 0xf1, 0x82, 0x47, 0x19, 0xd7, 0x79, 0x0e,
	0x96, 0x6f, 0x59, 0xce, 0xd0, 0x0a, 0xe9, 0x9d, 0x12, 0xd2, 0x7d, 0xd2, 0x52, 0x29, 0x4b, 0x6d,
	0x48, 0xb5, 0x8a, 0xc0, 0x65, 0xc5, 0x0b, 0x88, 0x4a, 0x3f, 0x28, 0x84, 0x89, 0x8a, 0x80, 0x3e,
	0x27, 0xed, 0x15, 0x37, 0x1a, 0xe6, 0xa0, 0x5d, 0x5c, 0x6a, 0x81, 0xb7, 0x54, 0x27, 0x56, 0xdc,
	0xc2, 0x71, 0x75, 0x0b, 0xc7, 0x8f, 0x80, 0x59, 0x7b, 0x0c, 0xcc, 0xfb, 0xc1, 0xae, 0xaf, 0x07,
	0x7b, 0x03, 0x8c, 0xdd, 0x27, 0x80, 0xb1, 0xf7, 0x64, 0x30, 0x1a, 0x0f, 0x83, 0xf1, 0x37, 0xa9,
	0x17, 0x41, 0x24, 0xee, 0x42, 0xc5, 0x37, 0x7b, 0xd9, 0xd2, 0xcb, 0x35, 0xf3, 0xcb, 0xc2, 0xf2,
	0xd9, 0xdc, 0x7a, 0x98, 0x43, 0x92, 0x28, 0x09, 0x0c, 0x17, 0xf1, 0x92, 0xf8, 0xd6, 0x52, 0x9c,
	0x2e, 0x62, 0xb0, 0xd0, 0x99, 0xd9, 0x67, 0x10, 0x98, 0x5b, 0xbc, 0xfc, 0x79, 0x72, 0xc9, 0x19,
	0xd6, 0x77, 0x59, 0x7b, 0x73, 0x97, 0x6d, 0x52, 0xd4, 0x79, 0x80, 0xa2, 0x6f, 0x9b, 0x14, 0x2d,
	0x03, 0xf7, 0xbb, 0x52, 0xb4, 0x6d, 0x75, 0xd4, 0xb6, 0xaf, 0x8e, 0x3f, 0x00, 0xfd, 0x2a, 0x80,
	0x7e, 0x06, 0x00, 0x00, 0xff, 0xff, 0x52, 0x6e, 0xb6, 0xac, 0xef, 0x07, 0x00, 0x00,
}
