// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_cache.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type CacheApiRequest struct {
	Namespace            *string  `protobuf:"bytes,1,req,name=namespace" json:"namespace,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CacheApiRequest) Reset()         { *m = CacheApiRequest{} }
func (m *CacheApiRequest) String() string { return proto.CompactTextString(m) }
func (*CacheApiRequest) ProtoMessage()    {}
func (*CacheApiRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5a8b6ef40cfae0f, []int{0}
}

func (m *CacheApiRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CacheApiRequest.Unmarshal(m, b)
}
func (m *CacheApiRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CacheApiRequest.Marshal(b, m, deterministic)
}
func (m *CacheApiRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CacheApiRequest.Merge(m, src)
}
func (m *CacheApiRequest) XXX_Size() int {
	return xxx_messageInfo_CacheApiRequest.Size(m)
}
func (m *CacheApiRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CacheApiRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CacheApiRequest proto.InternalMessageInfo

func (m *CacheApiRequest) GetNamespace() string {
	if m != nil && m.Namespace != nil {
		return *m.Namespace
	}
	return ""
}

type SuccessBody struct {
	Result               *string  `protobuf:"bytes,1,req,name=result" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuccessBody) Reset()         { *m = SuccessBody{} }
func (m *SuccessBody) String() string { return proto.CompactTextString(m) }
func (*SuccessBody) ProtoMessage()    {}
func (*SuccessBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_f5a8b6ef40cfae0f, []int{1}
}

func (m *SuccessBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuccessBody.Unmarshal(m, b)
}
func (m *SuccessBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuccessBody.Marshal(b, m, deterministic)
}
func (m *SuccessBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuccessBody.Merge(m, src)
}
func (m *SuccessBody) XXX_Size() int {
	return xxx_messageInfo_SuccessBody.Size(m)
}
func (m *SuccessBody) XXX_DiscardUnknown() {
	xxx_messageInfo_SuccessBody.DiscardUnknown(m)
}

var xxx_messageInfo_SuccessBody proto.InternalMessageInfo

func (m *SuccessBody) GetResult() string {
	if m != nil && m.Result != nil {
		return *m.Result
	}
	return ""
}

func init() {
	proto.RegisterType((*CacheApiRequest)(nil), "lcos_protobuf.CacheApiRequest")
	proto.RegisterType((*SuccessBody)(nil), "lcos_protobuf.SuccessBody")
}

func init() {
	proto.RegisterFile("lcos_cache.proto", fileDescriptor_f5a8b6ef40cfae0f)
}

var fileDescriptor_f5a8b6ef40cfae0f = []byte{
	// 186 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0xc8, 0x49, 0xce, 0x2f,
	0x8e, 0x4f, 0x4e, 0x4c, 0xce, 0x48, 0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0xe2, 0x05, 0x8b,
	0x80, 0xd9, 0x49, 0xa5, 0x69, 0x4a, 0xfa, 0x5c, 0xfc, 0xce, 0x20, 0x59, 0xc7, 0x82, 0xcc, 0xa0,
	0xd4, 0xc2, 0xd2, 0xd4, 0xe2, 0x12, 0x21, 0x19, 0x2e, 0xce, 0xbc, 0xc4, 0xdc, 0xd4, 0xe2, 0x82,
	0xc4, 0xe4, 0x54, 0x09, 0x46, 0x05, 0x26, 0x0d, 0xce, 0x20, 0x84, 0x80, 0x92, 0x2a, 0x17, 0x77,
	0x70, 0x69, 0x72, 0x72, 0x6a, 0x71, 0xb1, 0x53, 0x7e, 0x4a, 0xa5, 0x90, 0x18, 0x17, 0x5b, 0x51,
	0x6a, 0x71, 0x69, 0x4e, 0x09, 0x54, 0x25, 0x94, 0x67, 0xf4, 0x8b, 0x11, 0x61, 0x70, 0x70, 0x6a,
	0x51, 0x59, 0x66, 0x72, 0xaa, 0x90, 0x17, 0x17, 0x97, 0x73, 0x4e, 0x6a, 0x62, 0x1e, 0x58, 0x5c,
	0x48, 0x4e, 0x0f, 0xc5, 0x25, 0x7a, 0x68, 0xce, 0x90, 0x92, 0x42, 0x93, 0x47, 0xb2, 0x55, 0x89,
	0x41, 0xc8, 0x83, 0x8b, 0xc3, 0x3d, 0xb5, 0x84, 0x1a, 0x26, 0x05, 0x72, 0x09, 0x06, 0xa5, 0xa6,
	0x15, 0xa5, 0x16, 0x67, 0xf8, 0xe4, 0x27, 0x27, 0xe6, 0x50, 0xc1, 0x48, 0x40, 0x00, 0x00, 0x00,
	0xff, 0xff, 0x44, 0x2b, 0x97, 0xf7, 0x76, 0x01, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// CacheApiServiceClient is the client API for CacheApiService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CacheApiServiceClient interface {
	// 清理缓存
	CleanCache(ctx context.Context, in *CacheApiRequest, opts ...grpc.CallOption) (*SuccessBody, error)
	// 查询缓存
	GetCache(ctx context.Context, in *CacheApiRequest, opts ...grpc.CallOption) (*SuccessBody, error)
	// 重新加载本地缓存
	RefreshLocalCache(ctx context.Context, in *CacheApiRequest, opts ...grpc.CallOption) (*SuccessBody, error)
}

type cacheApiServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCacheApiServiceClient(cc grpc.ClientConnInterface) CacheApiServiceClient {
	return &cacheApiServiceClient{cc}
}

func (c *cacheApiServiceClient) CleanCache(ctx context.Context, in *CacheApiRequest, opts ...grpc.CallOption) (*SuccessBody, error) {
	out := new(SuccessBody)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.CacheApiService/CleanCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cacheApiServiceClient) GetCache(ctx context.Context, in *CacheApiRequest, opts ...grpc.CallOption) (*SuccessBody, error) {
	out := new(SuccessBody)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.CacheApiService/GetCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cacheApiServiceClient) RefreshLocalCache(ctx context.Context, in *CacheApiRequest, opts ...grpc.CallOption) (*SuccessBody, error) {
	out := new(SuccessBody)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.CacheApiService/RefreshLocalCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CacheApiServiceServer is the server API for CacheApiService service.
type CacheApiServiceServer interface {
	// 清理缓存
	CleanCache(context.Context, *CacheApiRequest) (*SuccessBody, error)
	// 查询缓存
	GetCache(context.Context, *CacheApiRequest) (*SuccessBody, error)
	// 重新加载本地缓存
	RefreshLocalCache(context.Context, *CacheApiRequest) (*SuccessBody, error)
}

// UnimplementedCacheApiServiceServer can be embedded to have forward compatible implementations.
type UnimplementedCacheApiServiceServer struct {
}

func (*UnimplementedCacheApiServiceServer) CleanCache(ctx context.Context, req *CacheApiRequest) (*SuccessBody, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanCache not implemented")
}
func (*UnimplementedCacheApiServiceServer) GetCache(ctx context.Context, req *CacheApiRequest) (*SuccessBody, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCache not implemented")
}
func (*UnimplementedCacheApiServiceServer) RefreshLocalCache(ctx context.Context, req *CacheApiRequest) (*SuccessBody, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshLocalCache not implemented")
}

func RegisterCacheApiServiceServer(s *grpc.Server, srv CacheApiServiceServer) {
	s.RegisterService(&_CacheApiService_serviceDesc, srv)
}

func _CacheApiService_CleanCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CacheApiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CacheApiServiceServer).CleanCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.CacheApiService/CleanCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CacheApiServiceServer).CleanCache(ctx, req.(*CacheApiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CacheApiService_GetCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CacheApiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CacheApiServiceServer).GetCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.CacheApiService/GetCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CacheApiServiceServer).GetCache(ctx, req.(*CacheApiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CacheApiService_RefreshLocalCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CacheApiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CacheApiServiceServer).RefreshLocalCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.CacheApiService/RefreshLocalCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CacheApiServiceServer).RefreshLocalCache(ctx, req.(*CacheApiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CacheApiService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.CacheApiService",
	HandlerType: (*CacheApiServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CleanCache",
			Handler:    _CacheApiService_CleanCache_Handler,
		},
		{
			MethodName: "GetCache",
			Handler:    _CacheApiService_GetCache_Handler,
		},
		{
			MethodName: "RefreshLocalCache",
			Handler:    _CacheApiService_RefreshLocalCache_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_cache.proto",
}
