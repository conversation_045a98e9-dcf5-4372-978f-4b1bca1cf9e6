// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_branch.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type SubStatusEnum int32

const (
	SubStatusEnum_Unavailable   SubStatusEnum = 0
	SubStatusEnum_Available     SubStatusEnum = 1
	SubStatusEnum_ClosingSoon   SubStatusEnum = 2
	SubStatusEnum_TempClose     SubStatusEnum = 3
	SubStatusEnum_CheckoutLimit SubStatusEnum = 4
)

var SubStatusEnum_name = map[int32]string{
	0: "Unavailable",
	1: "Available",
	2: "ClosingSoon",
	3: "TempClose",
	4: "CheckoutLimit",
}

var SubStatusEnum_value = map[string]int32{
	"Unavailable":   0,
	"Available":     1,
	"ClosingSoon":   2,
	"TempClose":     3,
	"CheckoutLimit": 4,
}

func (x SubStatusEnum) Enum() *SubStatusEnum {
	p := new(SubStatusEnum)
	*p = x
	return p
}

func (x SubStatusEnum) String() string {
	return proto.EnumName(SubStatusEnum_name, int32(x))
}

func (x *SubStatusEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SubStatusEnum_value, data, "SubStatusEnum")
	if err != nil {
		return err
	}
	*x = SubStatusEnum(value)
	return nil
}

func (SubStatusEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{0}
}

type GetSyncBranchSupplyTypeRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	Region               *string    `protobuf:"bytes,2,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetSyncBranchSupplyTypeRequest) Reset()         { *m = GetSyncBranchSupplyTypeRequest{} }
func (m *GetSyncBranchSupplyTypeRequest) String() string { return proto.CompactTextString(m) }
func (*GetSyncBranchSupplyTypeRequest) ProtoMessage()    {}
func (*GetSyncBranchSupplyTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{0}
}

func (m *GetSyncBranchSupplyTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSyncBranchSupplyTypeRequest.Unmarshal(m, b)
}
func (m *GetSyncBranchSupplyTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSyncBranchSupplyTypeRequest.Marshal(b, m, deterministic)
}
func (m *GetSyncBranchSupplyTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSyncBranchSupplyTypeRequest.Merge(m, src)
}
func (m *GetSyncBranchSupplyTypeRequest) XXX_Size() int {
	return xxx_messageInfo_GetSyncBranchSupplyTypeRequest.Size(m)
}
func (m *GetSyncBranchSupplyTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSyncBranchSupplyTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSyncBranchSupplyTypeRequest proto.InternalMessageInfo

func (m *GetSyncBranchSupplyTypeRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetSyncBranchSupplyTypeRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type GetSyncBranchSupplyTypeResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	BranchSupplyType     []uint32    `protobuf:"varint,2,rep,name=branch_supply_type,json=branchSupplyType" json:"branch_supply_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetSyncBranchSupplyTypeResponse) Reset()         { *m = GetSyncBranchSupplyTypeResponse{} }
func (m *GetSyncBranchSupplyTypeResponse) String() string { return proto.CompactTextString(m) }
func (*GetSyncBranchSupplyTypeResponse) ProtoMessage()    {}
func (*GetSyncBranchSupplyTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{1}
}

func (m *GetSyncBranchSupplyTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSyncBranchSupplyTypeResponse.Unmarshal(m, b)
}
func (m *GetSyncBranchSupplyTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSyncBranchSupplyTypeResponse.Marshal(b, m, deterministic)
}
func (m *GetSyncBranchSupplyTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSyncBranchSupplyTypeResponse.Merge(m, src)
}
func (m *GetSyncBranchSupplyTypeResponse) XXX_Size() int {
	return xxx_messageInfo_GetSyncBranchSupplyTypeResponse.Size(m)
}
func (m *GetSyncBranchSupplyTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSyncBranchSupplyTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSyncBranchSupplyTypeResponse proto.InternalMessageInfo

func (m *GetSyncBranchSupplyTypeResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetSyncBranchSupplyTypeResponse) GetBranchSupplyType() []uint32 {
	if m != nil {
		return m.BranchSupplyType
	}
	return nil
}

type SyncBranchInfoRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BranchSupplyType     *uint32    `protobuf:"varint,2,req,name=branch_supply_type,json=branchSupplyType" json:"branch_supply_type,omitempty"`
	RemoteFilePath       *string    `protobuf:"bytes,3,req,name=remote_file_path,json=remoteFilePath" json:"remote_file_path,omitempty"`
	Region               *string    `protobuf:"bytes,4,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SyncBranchInfoRequest) Reset()         { *m = SyncBranchInfoRequest{} }
func (m *SyncBranchInfoRequest) String() string { return proto.CompactTextString(m) }
func (*SyncBranchInfoRequest) ProtoMessage()    {}
func (*SyncBranchInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{2}
}

func (m *SyncBranchInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncBranchInfoRequest.Unmarshal(m, b)
}
func (m *SyncBranchInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncBranchInfoRequest.Marshal(b, m, deterministic)
}
func (m *SyncBranchInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncBranchInfoRequest.Merge(m, src)
}
func (m *SyncBranchInfoRequest) XXX_Size() int {
	return xxx_messageInfo_SyncBranchInfoRequest.Size(m)
}
func (m *SyncBranchInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncBranchInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncBranchInfoRequest proto.InternalMessageInfo

func (m *SyncBranchInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *SyncBranchInfoRequest) GetBranchSupplyType() uint32 {
	if m != nil && m.BranchSupplyType != nil {
		return *m.BranchSupplyType
	}
	return 0
}

func (m *SyncBranchInfoRequest) GetRemoteFilePath() string {
	if m != nil && m.RemoteFilePath != nil {
		return *m.RemoteFilePath
	}
	return ""
}

func (m *SyncBranchInfoRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type SyncBranchInfoResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SyncBranchInfoResponse) Reset()         { *m = SyncBranchInfoResponse{} }
func (m *SyncBranchInfoResponse) String() string { return proto.CompactTextString(m) }
func (*SyncBranchInfoResponse) ProtoMessage()    {}
func (*SyncBranchInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{3}
}

func (m *SyncBranchInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncBranchInfoResponse.Unmarshal(m, b)
}
func (m *SyncBranchInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncBranchInfoResponse.Marshal(b, m, deterministic)
}
func (m *SyncBranchInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncBranchInfoResponse.Merge(m, src)
}
func (m *SyncBranchInfoResponse) XXX_Size() int {
	return xxx_messageInfo_SyncBranchInfoResponse.Size(m)
}
func (m *SyncBranchInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncBranchInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SyncBranchInfoResponse proto.InternalMessageInfo

func (m *SyncBranchInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

type GetBranchInfoRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BranchId             *uint64    `protobuf:"varint,2,req,name=branch_id,json=branchId" json:"branch_id,omitempty"`
	BranchGroupId        []uint32   `protobuf:"varint,3,rep,name=branch_group_id,json=branchGroupId" json:"branch_group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetBranchInfoRequest) Reset()         { *m = GetBranchInfoRequest{} }
func (m *GetBranchInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetBranchInfoRequest) ProtoMessage()    {}
func (*GetBranchInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{4}
}

func (m *GetBranchInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchInfoRequest.Unmarshal(m, b)
}
func (m *GetBranchInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetBranchInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchInfoRequest.Merge(m, src)
}
func (m *GetBranchInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetBranchInfoRequest.Size(m)
}
func (m *GetBranchInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchInfoRequest proto.InternalMessageInfo

func (m *GetBranchInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetBranchInfoRequest) GetBranchId() uint64 {
	if m != nil && m.BranchId != nil {
		return *m.BranchId
	}
	return 0
}

func (m *GetBranchInfoRequest) GetBranchGroupId() []uint32 {
	if m != nil {
		return m.BranchGroupId
	}
	return nil
}

type GetBranchInfoResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	BranchInfo           *BranchInfo `protobuf:"bytes,2,opt,name=branch_info,json=branchInfo" json:"branch_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetBranchInfoResponse) Reset()         { *m = GetBranchInfoResponse{} }
func (m *GetBranchInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetBranchInfoResponse) ProtoMessage()    {}
func (*GetBranchInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{5}
}

func (m *GetBranchInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchInfoResponse.Unmarshal(m, b)
}
func (m *GetBranchInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetBranchInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchInfoResponse.Merge(m, src)
}
func (m *GetBranchInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetBranchInfoResponse.Size(m)
}
func (m *GetBranchInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchInfoResponse proto.InternalMessageInfo

func (m *GetBranchInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetBranchInfoResponse) GetBranchInfo() *BranchInfo {
	if m != nil {
		return m.BranchInfo
	}
	return nil
}

// req
type BatchGetBranchInfoByBranchIdRequest struct {
	ReqHeader            *ReqHeader                        `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BranchInfoList       []*GetBranchInfoByBranchIdRequest `protobuf:"bytes,2,rep,name=branch_info_list,json=branchInfoList" json:"branch_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *BatchGetBranchInfoByBranchIdRequest) Reset()         { *m = BatchGetBranchInfoByBranchIdRequest{} }
func (m *BatchGetBranchInfoByBranchIdRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetBranchInfoByBranchIdRequest) ProtoMessage()    {}
func (*BatchGetBranchInfoByBranchIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{6}
}

func (m *BatchGetBranchInfoByBranchIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBranchInfoByBranchIdRequest.Unmarshal(m, b)
}
func (m *BatchGetBranchInfoByBranchIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBranchInfoByBranchIdRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetBranchInfoByBranchIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBranchInfoByBranchIdRequest.Merge(m, src)
}
func (m *BatchGetBranchInfoByBranchIdRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetBranchInfoByBranchIdRequest.Size(m)
}
func (m *BatchGetBranchInfoByBranchIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBranchInfoByBranchIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBranchInfoByBranchIdRequest proto.InternalMessageInfo

func (m *BatchGetBranchInfoByBranchIdRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetBranchInfoByBranchIdRequest) GetBranchInfoList() []*GetBranchInfoByBranchIdRequest {
	if m != nil {
		return m.BranchInfoList
	}
	return nil
}

// rsp
type BatchGetBranchInfoByBranchIdResponse struct {
	RespHeader           *RespHeader         `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Data                 []*SingleBranchInfo `protobuf:"bytes,2,rep,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetBranchInfoByBranchIdResponse) Reset()         { *m = BatchGetBranchInfoByBranchIdResponse{} }
func (m *BatchGetBranchInfoByBranchIdResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetBranchInfoByBranchIdResponse) ProtoMessage()    {}
func (*BatchGetBranchInfoByBranchIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{7}
}

func (m *BatchGetBranchInfoByBranchIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBranchInfoByBranchIdResponse.Unmarshal(m, b)
}
func (m *BatchGetBranchInfoByBranchIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBranchInfoByBranchIdResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetBranchInfoByBranchIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBranchInfoByBranchIdResponse.Merge(m, src)
}
func (m *BatchGetBranchInfoByBranchIdResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetBranchInfoByBranchIdResponse.Size(m)
}
func (m *BatchGetBranchInfoByBranchIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBranchInfoByBranchIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBranchInfoByBranchIdResponse proto.InternalMessageInfo

func (m *BatchGetBranchInfoByBranchIdResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetBranchInfoByBranchIdResponse) GetData() []*SingleBranchInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetBranchInfoByBranchIdRequest struct {
	UniqueId             *string  `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	BranchId             *uint64  `protobuf:"varint,2,req,name=branch_id,json=branchId" json:"branch_id,omitempty"`
	BranchGroup          *uint32  `protobuf:"varint,3,opt,name=branch_group,json=branchGroup" json:"branch_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBranchInfoByBranchIdRequest) Reset()         { *m = GetBranchInfoByBranchIdRequest{} }
func (m *GetBranchInfoByBranchIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetBranchInfoByBranchIdRequest) ProtoMessage()    {}
func (*GetBranchInfoByBranchIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{8}
}

func (m *GetBranchInfoByBranchIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchInfoByBranchIdRequest.Unmarshal(m, b)
}
func (m *GetBranchInfoByBranchIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchInfoByBranchIdRequest.Marshal(b, m, deterministic)
}
func (m *GetBranchInfoByBranchIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchInfoByBranchIdRequest.Merge(m, src)
}
func (m *GetBranchInfoByBranchIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetBranchInfoByBranchIdRequest.Size(m)
}
func (m *GetBranchInfoByBranchIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchInfoByBranchIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchInfoByBranchIdRequest proto.InternalMessageInfo

func (m *GetBranchInfoByBranchIdRequest) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *GetBranchInfoByBranchIdRequest) GetBranchId() uint64 {
	if m != nil && m.BranchId != nil {
		return *m.BranchId
	}
	return 0
}

func (m *GetBranchInfoByBranchIdRequest) GetBranchGroup() uint32 {
	if m != nil && m.BranchGroup != nil {
		return *m.BranchGroup
	}
	return 0
}

// req
type BatchGetBranchInfoByBranchRefRequest struct {
	ReqHeader            *ReqHeader                         `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BranchInfoList       []*GetBranchInfoByBranchRefRequest `protobuf:"bytes,2,rep,name=branch_info_list,json=branchInfoList" json:"branch_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *BatchGetBranchInfoByBranchRefRequest) Reset()         { *m = BatchGetBranchInfoByBranchRefRequest{} }
func (m *BatchGetBranchInfoByBranchRefRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetBranchInfoByBranchRefRequest) ProtoMessage()    {}
func (*BatchGetBranchInfoByBranchRefRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{9}
}

func (m *BatchGetBranchInfoByBranchRefRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBranchInfoByBranchRefRequest.Unmarshal(m, b)
}
func (m *BatchGetBranchInfoByBranchRefRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBranchInfoByBranchRefRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetBranchInfoByBranchRefRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBranchInfoByBranchRefRequest.Merge(m, src)
}
func (m *BatchGetBranchInfoByBranchRefRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetBranchInfoByBranchRefRequest.Size(m)
}
func (m *BatchGetBranchInfoByBranchRefRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBranchInfoByBranchRefRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBranchInfoByBranchRefRequest proto.InternalMessageInfo

func (m *BatchGetBranchInfoByBranchRefRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetBranchInfoByBranchRefRequest) GetBranchInfoList() []*GetBranchInfoByBranchRefRequest {
	if m != nil {
		return m.BranchInfoList
	}
	return nil
}

// rsp
type BatchGetBranchInfoByBranchRefResponse struct {
	RespHeader           *RespHeader         `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Data                 []*SingleBranchInfo `protobuf:"bytes,2,rep,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetBranchInfoByBranchRefResponse) Reset()         { *m = BatchGetBranchInfoByBranchRefResponse{} }
func (m *BatchGetBranchInfoByBranchRefResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetBranchInfoByBranchRefResponse) ProtoMessage()    {}
func (*BatchGetBranchInfoByBranchRefResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{10}
}

func (m *BatchGetBranchInfoByBranchRefResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBranchInfoByBranchRefResponse.Unmarshal(m, b)
}
func (m *BatchGetBranchInfoByBranchRefResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBranchInfoByBranchRefResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetBranchInfoByBranchRefResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBranchInfoByBranchRefResponse.Merge(m, src)
}
func (m *BatchGetBranchInfoByBranchRefResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetBranchInfoByBranchRefResponse.Size(m)
}
func (m *BatchGetBranchInfoByBranchRefResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBranchInfoByBranchRefResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBranchInfoByBranchRefResponse proto.InternalMessageInfo

func (m *BatchGetBranchInfoByBranchRefResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetBranchInfoByBranchRefResponse) GetData() []*SingleBranchInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetBranchInfoByBranchRefRequest struct {
	UniqueId             *string  `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	BranchRef            *string  `protobuf:"bytes,2,req,name=branch_ref,json=branchRef" json:"branch_ref,omitempty"`
	BranchGroup          *uint32  `protobuf:"varint,3,req,name=branch_group,json=branchGroup" json:"branch_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBranchInfoByBranchRefRequest) Reset()         { *m = GetBranchInfoByBranchRefRequest{} }
func (m *GetBranchInfoByBranchRefRequest) String() string { return proto.CompactTextString(m) }
func (*GetBranchInfoByBranchRefRequest) ProtoMessage()    {}
func (*GetBranchInfoByBranchRefRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{11}
}

func (m *GetBranchInfoByBranchRefRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchInfoByBranchRefRequest.Unmarshal(m, b)
}
func (m *GetBranchInfoByBranchRefRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchInfoByBranchRefRequest.Marshal(b, m, deterministic)
}
func (m *GetBranchInfoByBranchRefRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchInfoByBranchRefRequest.Merge(m, src)
}
func (m *GetBranchInfoByBranchRefRequest) XXX_Size() int {
	return xxx_messageInfo_GetBranchInfoByBranchRefRequest.Size(m)
}
func (m *GetBranchInfoByBranchRefRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchInfoByBranchRefRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchInfoByBranchRefRequest proto.InternalMessageInfo

func (m *GetBranchInfoByBranchRefRequest) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *GetBranchInfoByBranchRefRequest) GetBranchRef() string {
	if m != nil && m.BranchRef != nil {
		return *m.BranchRef
	}
	return ""
}

func (m *GetBranchInfoByBranchRefRequest) GetBranchGroup() uint32 {
	if m != nil && m.BranchGroup != nil {
		return *m.BranchGroup
	}
	return 0
}

// single
type SingleBranchInfo struct {
	UniqueId             *string        `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ErrCode              *int32         `protobuf:"varint,2,opt,name=err_code,json=errCode" json:"err_code,omitempty"`
	Message              *string        `protobuf:"bytes,3,opt,name=message" json:"message,omitempty"`
	Id                   *uint64        `protobuf:"varint,4,opt,name=id" json:"id,omitempty"`
	BranchGroup          *uint32        `protobuf:"varint,5,opt,name=branch_group,json=branchGroup" json:"branch_group,omitempty"`
	BranchType           *uint32        `protobuf:"varint,6,opt,name=branch_type,json=branchType" json:"branch_type,omitempty"`
	BranchRef            *string        `protobuf:"bytes,7,opt,name=branch_ref,json=branchRef" json:"branch_ref,omitempty"`
	BranchName           *string        `protobuf:"bytes,8,opt,name=branch_name,json=branchName" json:"branch_name,omitempty"`
	BranchAddress        *string        `protobuf:"bytes,9,opt,name=branch_address,json=branchAddress" json:"branch_address,omitempty"`
	Status               *uint32        `protobuf:"varint,10,opt,name=status" json:"status,omitempty"`
	ExtraData            *string        `protobuf:"bytes,11,opt,name=extra_data,json=extraData" json:"extra_data,omitempty"`
	PostalCode           *string        `protobuf:"bytes,12,opt,name=postal_code,json=postalCode" json:"postal_code,omitempty"`
	Latitude             *string        `protobuf:"bytes,13,opt,name=latitude" json:"latitude,omitempty"`
	Longitude            *string        `protobuf:"bytes,14,opt,name=longitude" json:"longitude,omitempty"`
	CreateTime           *uint32        `protobuf:"varint,15,opt,name=create_time,json=createTime" json:"create_time,omitempty"`
	UpdateTime           *uint32        `protobuf:"varint,16,opt,name=update_time,json=updateTime" json:"update_time,omitempty"`
	Country              *string        `protobuf:"bytes,17,opt,name=country" json:"country,omitempty"`
	State                *string        `protobuf:"bytes,18,opt,name=state" json:"state,omitempty"`
	City                 *string        `protobuf:"bytes,19,opt,name=city" json:"city,omitempty"`
	District             *string        `protobuf:"bytes,20,opt,name=district" json:"district,omitempty"`
	Street               *string        `protobuf:"bytes,21,opt,name=street" json:"street,omitempty"`
	BranchPhone          *string        `protobuf:"bytes,22,opt,name=branch_phone,json=branchPhone" json:"branch_phone,omitempty"`
	SubStatus            *SubStatusEnum `protobuf:"varint,23,opt,name=sub_status,json=subStatus,enum=lcos_protobuf.SubStatusEnum" json:"sub_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SingleBranchInfo) Reset()         { *m = SingleBranchInfo{} }
func (m *SingleBranchInfo) String() string { return proto.CompactTextString(m) }
func (*SingleBranchInfo) ProtoMessage()    {}
func (*SingleBranchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{12}
}

func (m *SingleBranchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleBranchInfo.Unmarshal(m, b)
}
func (m *SingleBranchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleBranchInfo.Marshal(b, m, deterministic)
}
func (m *SingleBranchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleBranchInfo.Merge(m, src)
}
func (m *SingleBranchInfo) XXX_Size() int {
	return xxx_messageInfo_SingleBranchInfo.Size(m)
}
func (m *SingleBranchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleBranchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SingleBranchInfo proto.InternalMessageInfo

func (m *SingleBranchInfo) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SingleBranchInfo) GetErrCode() int32 {
	if m != nil && m.ErrCode != nil {
		return *m.ErrCode
	}
	return 0
}

func (m *SingleBranchInfo) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *SingleBranchInfo) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *SingleBranchInfo) GetBranchGroup() uint32 {
	if m != nil && m.BranchGroup != nil {
		return *m.BranchGroup
	}
	return 0
}

func (m *SingleBranchInfo) GetBranchType() uint32 {
	if m != nil && m.BranchType != nil {
		return *m.BranchType
	}
	return 0
}

func (m *SingleBranchInfo) GetBranchRef() string {
	if m != nil && m.BranchRef != nil {
		return *m.BranchRef
	}
	return ""
}

func (m *SingleBranchInfo) GetBranchName() string {
	if m != nil && m.BranchName != nil {
		return *m.BranchName
	}
	return ""
}

func (m *SingleBranchInfo) GetBranchAddress() string {
	if m != nil && m.BranchAddress != nil {
		return *m.BranchAddress
	}
	return ""
}

func (m *SingleBranchInfo) GetStatus() uint32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *SingleBranchInfo) GetExtraData() string {
	if m != nil && m.ExtraData != nil {
		return *m.ExtraData
	}
	return ""
}

func (m *SingleBranchInfo) GetPostalCode() string {
	if m != nil && m.PostalCode != nil {
		return *m.PostalCode
	}
	return ""
}

func (m *SingleBranchInfo) GetLatitude() string {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return ""
}

func (m *SingleBranchInfo) GetLongitude() string {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return ""
}

func (m *SingleBranchInfo) GetCreateTime() uint32 {
	if m != nil && m.CreateTime != nil {
		return *m.CreateTime
	}
	return 0
}

func (m *SingleBranchInfo) GetUpdateTime() uint32 {
	if m != nil && m.UpdateTime != nil {
		return *m.UpdateTime
	}
	return 0
}

func (m *SingleBranchInfo) GetCountry() string {
	if m != nil && m.Country != nil {
		return *m.Country
	}
	return ""
}

func (m *SingleBranchInfo) GetState() string {
	if m != nil && m.State != nil {
		return *m.State
	}
	return ""
}

func (m *SingleBranchInfo) GetCity() string {
	if m != nil && m.City != nil {
		return *m.City
	}
	return ""
}

func (m *SingleBranchInfo) GetDistrict() string {
	if m != nil && m.District != nil {
		return *m.District
	}
	return ""
}

func (m *SingleBranchInfo) GetStreet() string {
	if m != nil && m.Street != nil {
		return *m.Street
	}
	return ""
}

func (m *SingleBranchInfo) GetBranchPhone() string {
	if m != nil && m.BranchPhone != nil {
		return *m.BranchPhone
	}
	return ""
}

func (m *SingleBranchInfo) GetSubStatus() SubStatusEnum {
	if m != nil && m.SubStatus != nil {
		return *m.SubStatus
	}
	return SubStatusEnum_Unavailable
}

type BranchInfo struct {
	BranchId              *uint64              `protobuf:"varint,1,req,name=branch_id,json=branchId" json:"branch_id,omitempty"`
	BranchName            *string              `protobuf:"bytes,2,req,name=branch_name,json=branchName" json:"branch_name,omitempty"`
	LocationId            *uint64              `protobuf:"varint,3,req,name=location_id,json=locationId" json:"location_id,omitempty"`
	LocationDivisionId    *uint64              `protobuf:"varint,4,req,name=location_division_id,json=locationDivisionId" json:"location_division_id,omitempty"`
	BranchType            *uint32              `protobuf:"varint,5,req,name=branch_type,json=branchType" json:"branch_type,omitempty"`
	Postalcode            *string              `protobuf:"bytes,6,req,name=postalcode" json:"postalcode,omitempty"`
	Longitude             *string              `protobuf:"bytes,7,req,name=longitude" json:"longitude,omitempty"`
	Latitude              *string              `protobuf:"bytes,8,req,name=latitude" json:"latitude,omitempty"`
	DetailAddress         *string              `protobuf:"bytes,9,req,name=detail_address,json=detailAddress" json:"detail_address,omitempty"`
	OpenHour              []*OpenHour          `protobuf:"bytes,10,rep,name=open_hour,json=openHour" json:"open_hour,omitempty"`
	BranchRef             *string              `protobuf:"bytes,11,req,name=branch_ref,json=branchRef" json:"branch_ref,omitempty"`
	BranchStatus          *uint32              `protobuf:"varint,12,opt,name=branch_status,json=branchStatus" json:"branch_status,omitempty"`
	BranchInfoExtraData   *BranchInfoExtraData `protobuf:"bytes,13,opt,name=branch_info_extra_data,json=branchInfoExtraData" json:"branch_info_extra_data,omitempty"`
	SubDistrict           *string              `protobuf:"bytes,14,opt,name=sub_district,json=subDistrict" json:"sub_district,omitempty"`
	BranchPhone           *string              `protobuf:"bytes,15,opt,name=branch_phone,json=branchPhone" json:"branch_phone,omitempty"`
	MaxParcelStayDuration *int32               `protobuf:"varint,16,opt,name=max_parcel_stay_duration,json=maxParcelStayDuration" json:"max_parcel_stay_duration,omitempty"`
	BranchCode            *string              `protobuf:"bytes,17,opt,name=branch_code,json=branchCode" json:"branch_code,omitempty"`
	Region                *string              `protobuf:"bytes,18,opt,name=region" json:"region,omitempty"`
	OpsType               []string             `protobuf:"bytes,19,rep,name=ops_type,json=opsType" json:"ops_type,omitempty"`
	LocationDescription   *string              `protobuf:"bytes,20,opt,name=location_description,json=locationDescription" json:"location_description,omitempty"`
	SubStatus             *SubStatusEnum       `protobuf:"varint,21,opt,name=sub_status,json=subStatus,enum=lcos_protobuf.SubStatusEnum" json:"sub_status,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *BranchInfo) Reset()         { *m = BranchInfo{} }
func (m *BranchInfo) String() string { return proto.CompactTextString(m) }
func (*BranchInfo) ProtoMessage()    {}
func (*BranchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{13}
}

func (m *BranchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BranchInfo.Unmarshal(m, b)
}
func (m *BranchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BranchInfo.Marshal(b, m, deterministic)
}
func (m *BranchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BranchInfo.Merge(m, src)
}
func (m *BranchInfo) XXX_Size() int {
	return xxx_messageInfo_BranchInfo.Size(m)
}
func (m *BranchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BranchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BranchInfo proto.InternalMessageInfo

func (m *BranchInfo) GetBranchId() uint64 {
	if m != nil && m.BranchId != nil {
		return *m.BranchId
	}
	return 0
}

func (m *BranchInfo) GetBranchName() string {
	if m != nil && m.BranchName != nil {
		return *m.BranchName
	}
	return ""
}

func (m *BranchInfo) GetLocationId() uint64 {
	if m != nil && m.LocationId != nil {
		return *m.LocationId
	}
	return 0
}

func (m *BranchInfo) GetLocationDivisionId() uint64 {
	if m != nil && m.LocationDivisionId != nil {
		return *m.LocationDivisionId
	}
	return 0
}

func (m *BranchInfo) GetBranchType() uint32 {
	if m != nil && m.BranchType != nil {
		return *m.BranchType
	}
	return 0
}

func (m *BranchInfo) GetPostalcode() string {
	if m != nil && m.Postalcode != nil {
		return *m.Postalcode
	}
	return ""
}

func (m *BranchInfo) GetLongitude() string {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return ""
}

func (m *BranchInfo) GetLatitude() string {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return ""
}

func (m *BranchInfo) GetDetailAddress() string {
	if m != nil && m.DetailAddress != nil {
		return *m.DetailAddress
	}
	return ""
}

func (m *BranchInfo) GetOpenHour() []*OpenHour {
	if m != nil {
		return m.OpenHour
	}
	return nil
}

func (m *BranchInfo) GetBranchRef() string {
	if m != nil && m.BranchRef != nil {
		return *m.BranchRef
	}
	return ""
}

func (m *BranchInfo) GetBranchStatus() uint32 {
	if m != nil && m.BranchStatus != nil {
		return *m.BranchStatus
	}
	return 0
}

func (m *BranchInfo) GetBranchInfoExtraData() *BranchInfoExtraData {
	if m != nil {
		return m.BranchInfoExtraData
	}
	return nil
}

func (m *BranchInfo) GetSubDistrict() string {
	if m != nil && m.SubDistrict != nil {
		return *m.SubDistrict
	}
	return ""
}

func (m *BranchInfo) GetBranchPhone() string {
	if m != nil && m.BranchPhone != nil {
		return *m.BranchPhone
	}
	return ""
}

func (m *BranchInfo) GetMaxParcelStayDuration() int32 {
	if m != nil && m.MaxParcelStayDuration != nil {
		return *m.MaxParcelStayDuration
	}
	return 0
}

func (m *BranchInfo) GetBranchCode() string {
	if m != nil && m.BranchCode != nil {
		return *m.BranchCode
	}
	return ""
}

func (m *BranchInfo) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *BranchInfo) GetOpsType() []string {
	if m != nil {
		return m.OpsType
	}
	return nil
}

func (m *BranchInfo) GetLocationDescription() string {
	if m != nil && m.LocationDescription != nil {
		return *m.LocationDescription
	}
	return ""
}

func (m *BranchInfo) GetSubStatus() SubStatusEnum {
	if m != nil && m.SubStatus != nil {
		return *m.SubStatus
	}
	return SubStatusEnum_Unavailable
}

// 用于给google maps提供信息
type FullBranchInfo struct {
	BranchId              *uint64              `protobuf:"varint,1,req,name=branch_id,json=branchId" json:"branch_id,omitempty"`
	BranchName            *string              `protobuf:"bytes,2,req,name=branch_name,json=branchName" json:"branch_name,omitempty"`
	LocationId            *uint64              `protobuf:"varint,3,req,name=location_id,json=locationId" json:"location_id,omitempty"`
	LocationDivisionId    *uint64              `protobuf:"varint,4,req,name=location_division_id,json=locationDivisionId" json:"location_division_id,omitempty"`
	BranchType            *uint32              `protobuf:"varint,5,req,name=branch_type,json=branchType" json:"branch_type,omitempty"`
	Postalcode            *string              `protobuf:"bytes,6,req,name=postalcode" json:"postalcode,omitempty"`
	Longitude             *string              `protobuf:"bytes,7,req,name=longitude" json:"longitude,omitempty"`
	Latitude              *string              `protobuf:"bytes,8,req,name=latitude" json:"latitude,omitempty"`
	DetailAddress         *string              `protobuf:"bytes,9,req,name=detail_address,json=detailAddress" json:"detail_address,omitempty"`
	OpenHour              []*OpenHour          `protobuf:"bytes,10,rep,name=open_hour,json=openHour" json:"open_hour,omitempty"`
	BranchRef             *string              `protobuf:"bytes,11,req,name=branch_ref,json=branchRef" json:"branch_ref,omitempty"`
	BranchStatus          *uint32              `protobuf:"varint,12,opt,name=branch_status,json=branchStatus" json:"branch_status,omitempty"`
	BranchInfoExtraData   *BranchInfoExtraData `protobuf:"bytes,13,opt,name=branch_info_extra_data,json=branchInfoExtraData" json:"branch_info_extra_data,omitempty"`
	SubDistrict           *string              `protobuf:"bytes,14,opt,name=sub_district,json=subDistrict" json:"sub_district,omitempty"`
	BranchPhone           *string              `protobuf:"bytes,15,opt,name=branch_phone,json=branchPhone" json:"branch_phone,omitempty"`
	MaxParcelStayDuration *int32               `protobuf:"varint,16,opt,name=max_parcel_stay_duration,json=maxParcelStayDuration" json:"max_parcel_stay_duration,omitempty"`
	BranchCode            *string              `protobuf:"bytes,17,opt,name=branch_code,json=branchCode" json:"branch_code,omitempty"`
	State                 *string              `protobuf:"bytes,18,opt,name=state" json:"state,omitempty"`
	City                  *string              `protobuf:"bytes,19,opt,name=city" json:"city,omitempty"`
	District              *string              `protobuf:"bytes,20,opt,name=district" json:"district,omitempty"`
	Street                *string              `protobuf:"bytes,21,opt,name=street" json:"street,omitempty"`
	BranchGroupId         *uint32              `protobuf:"varint,22,opt,name=branch_group_id,json=branchGroupId" json:"branch_group_id,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *FullBranchInfo) Reset()         { *m = FullBranchInfo{} }
func (m *FullBranchInfo) String() string { return proto.CompactTextString(m) }
func (*FullBranchInfo) ProtoMessage()    {}
func (*FullBranchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{14}
}

func (m *FullBranchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FullBranchInfo.Unmarshal(m, b)
}
func (m *FullBranchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FullBranchInfo.Marshal(b, m, deterministic)
}
func (m *FullBranchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FullBranchInfo.Merge(m, src)
}
func (m *FullBranchInfo) XXX_Size() int {
	return xxx_messageInfo_FullBranchInfo.Size(m)
}
func (m *FullBranchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FullBranchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FullBranchInfo proto.InternalMessageInfo

func (m *FullBranchInfo) GetBranchId() uint64 {
	if m != nil && m.BranchId != nil {
		return *m.BranchId
	}
	return 0
}

func (m *FullBranchInfo) GetBranchName() string {
	if m != nil && m.BranchName != nil {
		return *m.BranchName
	}
	return ""
}

func (m *FullBranchInfo) GetLocationId() uint64 {
	if m != nil && m.LocationId != nil {
		return *m.LocationId
	}
	return 0
}

func (m *FullBranchInfo) GetLocationDivisionId() uint64 {
	if m != nil && m.LocationDivisionId != nil {
		return *m.LocationDivisionId
	}
	return 0
}

func (m *FullBranchInfo) GetBranchType() uint32 {
	if m != nil && m.BranchType != nil {
		return *m.BranchType
	}
	return 0
}

func (m *FullBranchInfo) GetPostalcode() string {
	if m != nil && m.Postalcode != nil {
		return *m.Postalcode
	}
	return ""
}

func (m *FullBranchInfo) GetLongitude() string {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return ""
}

func (m *FullBranchInfo) GetLatitude() string {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return ""
}

func (m *FullBranchInfo) GetDetailAddress() string {
	if m != nil && m.DetailAddress != nil {
		return *m.DetailAddress
	}
	return ""
}

func (m *FullBranchInfo) GetOpenHour() []*OpenHour {
	if m != nil {
		return m.OpenHour
	}
	return nil
}

func (m *FullBranchInfo) GetBranchRef() string {
	if m != nil && m.BranchRef != nil {
		return *m.BranchRef
	}
	return ""
}

func (m *FullBranchInfo) GetBranchStatus() uint32 {
	if m != nil && m.BranchStatus != nil {
		return *m.BranchStatus
	}
	return 0
}

func (m *FullBranchInfo) GetBranchInfoExtraData() *BranchInfoExtraData {
	if m != nil {
		return m.BranchInfoExtraData
	}
	return nil
}

func (m *FullBranchInfo) GetSubDistrict() string {
	if m != nil && m.SubDistrict != nil {
		return *m.SubDistrict
	}
	return ""
}

func (m *FullBranchInfo) GetBranchPhone() string {
	if m != nil && m.BranchPhone != nil {
		return *m.BranchPhone
	}
	return ""
}

func (m *FullBranchInfo) GetMaxParcelStayDuration() int32 {
	if m != nil && m.MaxParcelStayDuration != nil {
		return *m.MaxParcelStayDuration
	}
	return 0
}

func (m *FullBranchInfo) GetBranchCode() string {
	if m != nil && m.BranchCode != nil {
		return *m.BranchCode
	}
	return ""
}

func (m *FullBranchInfo) GetState() string {
	if m != nil && m.State != nil {
		return *m.State
	}
	return ""
}

func (m *FullBranchInfo) GetCity() string {
	if m != nil && m.City != nil {
		return *m.City
	}
	return ""
}

func (m *FullBranchInfo) GetDistrict() string {
	if m != nil && m.District != nil {
		return *m.District
	}
	return ""
}

func (m *FullBranchInfo) GetStreet() string {
	if m != nil && m.Street != nil {
		return *m.Street
	}
	return ""
}

func (m *FullBranchInfo) GetBranchGroupId() uint32 {
	if m != nil && m.BranchGroupId != nil {
		return *m.BranchGroupId
	}
	return 0
}

type BranchInfoExtraData struct {
	DCNAME               *string  `protobuf:"bytes,1,opt,name=DCNAME" json:"DCNAME,omitempty"`
	DCCODE               *string  `protobuf:"bytes,2,opt,name=DCCODE" json:"DCCODE,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BranchInfoExtraData) Reset()         { *m = BranchInfoExtraData{} }
func (m *BranchInfoExtraData) String() string { return proto.CompactTextString(m) }
func (*BranchInfoExtraData) ProtoMessage()    {}
func (*BranchInfoExtraData) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{15}
}

func (m *BranchInfoExtraData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BranchInfoExtraData.Unmarshal(m, b)
}
func (m *BranchInfoExtraData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BranchInfoExtraData.Marshal(b, m, deterministic)
}
func (m *BranchInfoExtraData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BranchInfoExtraData.Merge(m, src)
}
func (m *BranchInfoExtraData) XXX_Size() int {
	return xxx_messageInfo_BranchInfoExtraData.Size(m)
}
func (m *BranchInfoExtraData) XXX_DiscardUnknown() {
	xxx_messageInfo_BranchInfoExtraData.DiscardUnknown(m)
}

var xxx_messageInfo_BranchInfoExtraData proto.InternalMessageInfo

func (m *BranchInfoExtraData) GetDCNAME() string {
	if m != nil && m.DCNAME != nil {
		return *m.DCNAME
	}
	return ""
}

func (m *BranchInfoExtraData) GetDCCODE() string {
	if m != nil && m.DCCODE != nil {
		return *m.DCCODE
	}
	return ""
}

type OpenHour struct {
	StartTime            *string  `protobuf:"bytes,1,req,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime              *string  `protobuf:"bytes,2,req,name=end_time,json=endTime" json:"end_time,omitempty"`
	DayOfWeek            []uint32 `protobuf:"varint,3,rep,name=day_of_week,json=dayOfWeek" json:"day_of_week,omitempty"`
	TimeZone             *string  `protobuf:"bytes,4,req,name=time_zone,json=timeZone" json:"time_zone,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenHour) Reset()         { *m = OpenHour{} }
func (m *OpenHour) String() string { return proto.CompactTextString(m) }
func (*OpenHour) ProtoMessage()    {}
func (*OpenHour) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{16}
}

func (m *OpenHour) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenHour.Unmarshal(m, b)
}
func (m *OpenHour) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenHour.Marshal(b, m, deterministic)
}
func (m *OpenHour) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenHour.Merge(m, src)
}
func (m *OpenHour) XXX_Size() int {
	return xxx_messageInfo_OpenHour.Size(m)
}
func (m *OpenHour) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenHour.DiscardUnknown(m)
}

var xxx_messageInfo_OpenHour proto.InternalMessageInfo

func (m *OpenHour) GetStartTime() string {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return ""
}

func (m *OpenHour) GetEndTime() string {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return ""
}

func (m *OpenHour) GetDayOfWeek() []uint32 {
	if m != nil {
		return m.DayOfWeek
	}
	return nil
}

func (m *OpenHour) GetTimeZone() string {
	if m != nil && m.TimeZone != nil {
		return *m.TimeZone
	}
	return ""
}

type GetBranchInfoByLocationIDRequest struct {
	ReqHeader            *ReqHeader      `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BranchGroupId        *uint32         `protobuf:"varint,2,req,name=branch_group_id,json=branchGroupId" json:"branch_group_id,omitempty"`
	LocationId           *uint64         `protobuf:"varint,3,req,name=location_id,json=locationId" json:"location_id,omitempty"`
	SubStatus            []SubStatusEnum `protobuf:"varint,4,rep,name=sub_status,json=subStatus,enum=lcos_protobuf.SubStatusEnum" json:"sub_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBranchInfoByLocationIDRequest) Reset()         { *m = GetBranchInfoByLocationIDRequest{} }
func (m *GetBranchInfoByLocationIDRequest) String() string { return proto.CompactTextString(m) }
func (*GetBranchInfoByLocationIDRequest) ProtoMessage()    {}
func (*GetBranchInfoByLocationIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{17}
}

func (m *GetBranchInfoByLocationIDRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchInfoByLocationIDRequest.Unmarshal(m, b)
}
func (m *GetBranchInfoByLocationIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchInfoByLocationIDRequest.Marshal(b, m, deterministic)
}
func (m *GetBranchInfoByLocationIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchInfoByLocationIDRequest.Merge(m, src)
}
func (m *GetBranchInfoByLocationIDRequest) XXX_Size() int {
	return xxx_messageInfo_GetBranchInfoByLocationIDRequest.Size(m)
}
func (m *GetBranchInfoByLocationIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchInfoByLocationIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchInfoByLocationIDRequest proto.InternalMessageInfo

func (m *GetBranchInfoByLocationIDRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetBranchInfoByLocationIDRequest) GetBranchGroupId() uint32 {
	if m != nil && m.BranchGroupId != nil {
		return *m.BranchGroupId
	}
	return 0
}

func (m *GetBranchInfoByLocationIDRequest) GetLocationId() uint64 {
	if m != nil && m.LocationId != nil {
		return *m.LocationId
	}
	return 0
}

func (m *GetBranchInfoByLocationIDRequest) GetSubStatus() []SubStatusEnum {
	if m != nil {
		return m.SubStatus
	}
	return nil
}

type GetBranchInfoByLocationIDResponse struct {
	RespHeader           *RespHeader   `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Data                 []*BranchInfo `protobuf:"bytes,2,rep,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBranchInfoByLocationIDResponse) Reset()         { *m = GetBranchInfoByLocationIDResponse{} }
func (m *GetBranchInfoByLocationIDResponse) String() string { return proto.CompactTextString(m) }
func (*GetBranchInfoByLocationIDResponse) ProtoMessage()    {}
func (*GetBranchInfoByLocationIDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{18}
}

func (m *GetBranchInfoByLocationIDResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchInfoByLocationIDResponse.Unmarshal(m, b)
}
func (m *GetBranchInfoByLocationIDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchInfoByLocationIDResponse.Marshal(b, m, deterministic)
}
func (m *GetBranchInfoByLocationIDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchInfoByLocationIDResponse.Merge(m, src)
}
func (m *GetBranchInfoByLocationIDResponse) XXX_Size() int {
	return xxx_messageInfo_GetBranchInfoByLocationIDResponse.Size(m)
}
func (m *GetBranchInfoByLocationIDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchInfoByLocationIDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchInfoByLocationIDResponse proto.InternalMessageInfo

func (m *GetBranchInfoByLocationIDResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetBranchInfoByLocationIDResponse) GetData() []*BranchInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetBranchSubLocationsRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BranchGroupId        *uint32    `protobuf:"varint,2,req,name=branch_group_id,json=branchGroupId" json:"branch_group_id,omitempty"`
	LocationId           *uint64    `protobuf:"varint,3,req,name=location_id,json=locationId" json:"location_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetBranchSubLocationsRequest) Reset()         { *m = GetBranchSubLocationsRequest{} }
func (m *GetBranchSubLocationsRequest) String() string { return proto.CompactTextString(m) }
func (*GetBranchSubLocationsRequest) ProtoMessage()    {}
func (*GetBranchSubLocationsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{19}
}

func (m *GetBranchSubLocationsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchSubLocationsRequest.Unmarshal(m, b)
}
func (m *GetBranchSubLocationsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchSubLocationsRequest.Marshal(b, m, deterministic)
}
func (m *GetBranchSubLocationsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchSubLocationsRequest.Merge(m, src)
}
func (m *GetBranchSubLocationsRequest) XXX_Size() int {
	return xxx_messageInfo_GetBranchSubLocationsRequest.Size(m)
}
func (m *GetBranchSubLocationsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchSubLocationsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchSubLocationsRequest proto.InternalMessageInfo

func (m *GetBranchSubLocationsRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetBranchSubLocationsRequest) GetBranchGroupId() uint32 {
	if m != nil && m.BranchGroupId != nil {
		return *m.BranchGroupId
	}
	return 0
}

func (m *GetBranchSubLocationsRequest) GetLocationId() uint64 {
	if m != nil && m.LocationId != nil {
		return *m.LocationId
	}
	return 0
}

type GetBranchSubLocationsResponse struct {
	RespHeader           *RespHeader     `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	LocationsIdMap       *LocationsIdMap `protobuf:"bytes,2,opt,name=locations_id_map,json=locationsIdMap" json:"locations_id_map,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBranchSubLocationsResponse) Reset()         { *m = GetBranchSubLocationsResponse{} }
func (m *GetBranchSubLocationsResponse) String() string { return proto.CompactTextString(m) }
func (*GetBranchSubLocationsResponse) ProtoMessage()    {}
func (*GetBranchSubLocationsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{20}
}

func (m *GetBranchSubLocationsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchSubLocationsResponse.Unmarshal(m, b)
}
func (m *GetBranchSubLocationsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchSubLocationsResponse.Marshal(b, m, deterministic)
}
func (m *GetBranchSubLocationsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchSubLocationsResponse.Merge(m, src)
}
func (m *GetBranchSubLocationsResponse) XXX_Size() int {
	return xxx_messageInfo_GetBranchSubLocationsResponse.Size(m)
}
func (m *GetBranchSubLocationsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchSubLocationsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchSubLocationsResponse proto.InternalMessageInfo

func (m *GetBranchSubLocationsResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetBranchSubLocationsResponse) GetLocationsIdMap() *LocationsIdMap {
	if m != nil {
		return m.LocationsIdMap
	}
	return nil
}

type LocationsIdMap struct {
	StateLocationsId     []uint64 `protobuf:"varint,1,rep,name=state_locations_id,json=stateLocationsId" json:"state_locations_id,omitempty"`
	CityLocationsId      []uint64 `protobuf:"varint,2,rep,name=city_locations_id,json=cityLocationsId" json:"city_locations_id,omitempty"`
	DistrictLocationsId  []uint64 `protobuf:"varint,3,rep,name=district_locations_id,json=districtLocationsId" json:"district_locations_id,omitempty"`
	StreetLocationsId    []uint64 `protobuf:"varint,4,rep,name=street_locations_id,json=streetLocationsId" json:"street_locations_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LocationsIdMap) Reset()         { *m = LocationsIdMap{} }
func (m *LocationsIdMap) String() string { return proto.CompactTextString(m) }
func (*LocationsIdMap) ProtoMessage()    {}
func (*LocationsIdMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{21}
}

func (m *LocationsIdMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LocationsIdMap.Unmarshal(m, b)
}
func (m *LocationsIdMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LocationsIdMap.Marshal(b, m, deterministic)
}
func (m *LocationsIdMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LocationsIdMap.Merge(m, src)
}
func (m *LocationsIdMap) XXX_Size() int {
	return xxx_messageInfo_LocationsIdMap.Size(m)
}
func (m *LocationsIdMap) XXX_DiscardUnknown() {
	xxx_messageInfo_LocationsIdMap.DiscardUnknown(m)
}

var xxx_messageInfo_LocationsIdMap proto.InternalMessageInfo

func (m *LocationsIdMap) GetStateLocationsId() []uint64 {
	if m != nil {
		return m.StateLocationsId
	}
	return nil
}

func (m *LocationsIdMap) GetCityLocationsId() []uint64 {
	if m != nil {
		return m.CityLocationsId
	}
	return nil
}

func (m *LocationsIdMap) GetDistrictLocationsId() []uint64 {
	if m != nil {
		return m.DistrictLocationsId
	}
	return nil
}

func (m *LocationsIdMap) GetStreetLocationsId() []uint64 {
	if m != nil {
		return m.StreetLocationsId
	}
	return nil
}

type OpsType struct {
	OpsType              []string `protobuf:"bytes,1,rep,name=ops_type,json=opsType" json:"ops_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpsType) Reset()         { *m = OpsType{} }
func (m *OpsType) String() string { return proto.CompactTextString(m) }
func (*OpsType) ProtoMessage()    {}
func (*OpsType) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{22}
}

func (m *OpsType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpsType.Unmarshal(m, b)
}
func (m *OpsType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpsType.Marshal(b, m, deterministic)
}
func (m *OpsType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpsType.Merge(m, src)
}
func (m *OpsType) XXX_Size() int {
	return xxx_messageInfo_OpsType.Size(m)
}
func (m *OpsType) XXX_DiscardUnknown() {
	xxx_messageInfo_OpsType.DiscardUnknown(m)
}

var xxx_messageInfo_OpsType proto.InternalMessageInfo

func (m *OpsType) GetOpsType() []string {
	if m != nil {
		return m.OpsType
	}
	return nil
}

type SearchBranchRequest struct {
	ReqHeader            *ReqHeader      `protobuf:"bytes,1,opt,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BranchGroupId        *uint32         `protobuf:"varint,2,opt,name=branch_group_id,json=branchGroupId" json:"branch_group_id,omitempty"`
	Keyword              *string         `protobuf:"bytes,3,opt,name=keyword" json:"keyword,omitempty"`
	FindType             *uint32         `protobuf:"varint,4,opt,name=find_type,json=findType" json:"find_type,omitempty"`
	Region               *string         `protobuf:"bytes,5,opt,name=region" json:"region,omitempty"`
	Size                 *int32          `protobuf:"varint,6,opt,name=size" json:"size,omitempty"`
	LocationSortType     *uint32         `protobuf:"varint,7,opt,name=location_sort_type,json=locationSortType" json:"location_sort_type,omitempty"`
	Longitude            *string         `protobuf:"bytes,8,opt,name=longitude" json:"longitude,omitempty"`
	Latitude             *string         `protobuf:"bytes,9,opt,name=latitude" json:"latitude,omitempty"`
	Zipcode              *string         `protobuf:"bytes,10,opt,name=zipcode" json:"zipcode,omitempty"`
	OpsTypeList          []*OpsType      `protobuf:"bytes,11,rep,name=ops_type_list,json=opsTypeList" json:"ops_type_list,omitempty"`
	Distance             *uint32         `protobuf:"varint,12,opt,name=distance" json:"distance,omitempty"`
	SubStatus            []SubStatusEnum `protobuf:"varint,13,rep,name=sub_status,json=subStatus,enum=lcos_protobuf.SubStatusEnum" json:"sub_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SearchBranchRequest) Reset()         { *m = SearchBranchRequest{} }
func (m *SearchBranchRequest) String() string { return proto.CompactTextString(m) }
func (*SearchBranchRequest) ProtoMessage()    {}
func (*SearchBranchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{23}
}

func (m *SearchBranchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchBranchRequest.Unmarshal(m, b)
}
func (m *SearchBranchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchBranchRequest.Marshal(b, m, deterministic)
}
func (m *SearchBranchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchBranchRequest.Merge(m, src)
}
func (m *SearchBranchRequest) XXX_Size() int {
	return xxx_messageInfo_SearchBranchRequest.Size(m)
}
func (m *SearchBranchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchBranchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchBranchRequest proto.InternalMessageInfo

func (m *SearchBranchRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *SearchBranchRequest) GetBranchGroupId() uint32 {
	if m != nil && m.BranchGroupId != nil {
		return *m.BranchGroupId
	}
	return 0
}

func (m *SearchBranchRequest) GetKeyword() string {
	if m != nil && m.Keyword != nil {
		return *m.Keyword
	}
	return ""
}

func (m *SearchBranchRequest) GetFindType() uint32 {
	if m != nil && m.FindType != nil {
		return *m.FindType
	}
	return 0
}

func (m *SearchBranchRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *SearchBranchRequest) GetSize() int32 {
	if m != nil && m.Size != nil {
		return *m.Size
	}
	return 0
}

func (m *SearchBranchRequest) GetLocationSortType() uint32 {
	if m != nil && m.LocationSortType != nil {
		return *m.LocationSortType
	}
	return 0
}

func (m *SearchBranchRequest) GetLongitude() string {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return ""
}

func (m *SearchBranchRequest) GetLatitude() string {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return ""
}

func (m *SearchBranchRequest) GetZipcode() string {
	if m != nil && m.Zipcode != nil {
		return *m.Zipcode
	}
	return ""
}

func (m *SearchBranchRequest) GetOpsTypeList() []*OpsType {
	if m != nil {
		return m.OpsTypeList
	}
	return nil
}

func (m *SearchBranchRequest) GetDistance() uint32 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

func (m *SearchBranchRequest) GetSubStatus() []SubStatusEnum {
	if m != nil {
		return m.SubStatus
	}
	return nil
}

type SearchBranchForGoogleMapsRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,opt,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BranchGroupId        []uint32   `protobuf:"varint,2,rep,name=branch_group_id,json=branchGroupId" json:"branch_group_id,omitempty"`
	Keyword              *string    `protobuf:"bytes,3,opt,name=keyword" json:"keyword,omitempty"`
	FindType             *uint32    `protobuf:"varint,4,opt,name=find_type,json=findType" json:"find_type,omitempty"`
	Region               *string    `protobuf:"bytes,5,opt,name=region" json:"region,omitempty"`
	Size                 *int32     `protobuf:"varint,6,opt,name=size" json:"size,omitempty"`
	LocationSortType     *uint32    `protobuf:"varint,7,opt,name=location_sort_type,json=locationSortType" json:"location_sort_type,omitempty"`
	Longitude            *string    `protobuf:"bytes,8,opt,name=longitude" json:"longitude,omitempty"`
	Latitude             *string    `protobuf:"bytes,9,opt,name=latitude" json:"latitude,omitempty"`
	Zipcode              *string    `protobuf:"bytes,10,opt,name=zipcode" json:"zipcode,omitempty"`
	Distance             *uint32    `protobuf:"varint,11,opt,name=distance" json:"distance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SearchBranchForGoogleMapsRequest) Reset()         { *m = SearchBranchForGoogleMapsRequest{} }
func (m *SearchBranchForGoogleMapsRequest) String() string { return proto.CompactTextString(m) }
func (*SearchBranchForGoogleMapsRequest) ProtoMessage()    {}
func (*SearchBranchForGoogleMapsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{24}
}

func (m *SearchBranchForGoogleMapsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchBranchForGoogleMapsRequest.Unmarshal(m, b)
}
func (m *SearchBranchForGoogleMapsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchBranchForGoogleMapsRequest.Marshal(b, m, deterministic)
}
func (m *SearchBranchForGoogleMapsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchBranchForGoogleMapsRequest.Merge(m, src)
}
func (m *SearchBranchForGoogleMapsRequest) XXX_Size() int {
	return xxx_messageInfo_SearchBranchForGoogleMapsRequest.Size(m)
}
func (m *SearchBranchForGoogleMapsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchBranchForGoogleMapsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchBranchForGoogleMapsRequest proto.InternalMessageInfo

func (m *SearchBranchForGoogleMapsRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *SearchBranchForGoogleMapsRequest) GetBranchGroupId() []uint32 {
	if m != nil {
		return m.BranchGroupId
	}
	return nil
}

func (m *SearchBranchForGoogleMapsRequest) GetKeyword() string {
	if m != nil && m.Keyword != nil {
		return *m.Keyword
	}
	return ""
}

func (m *SearchBranchForGoogleMapsRequest) GetFindType() uint32 {
	if m != nil && m.FindType != nil {
		return *m.FindType
	}
	return 0
}

func (m *SearchBranchForGoogleMapsRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *SearchBranchForGoogleMapsRequest) GetSize() int32 {
	if m != nil && m.Size != nil {
		return *m.Size
	}
	return 0
}

func (m *SearchBranchForGoogleMapsRequest) GetLocationSortType() uint32 {
	if m != nil && m.LocationSortType != nil {
		return *m.LocationSortType
	}
	return 0
}

func (m *SearchBranchForGoogleMapsRequest) GetLongitude() string {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return ""
}

func (m *SearchBranchForGoogleMapsRequest) GetLatitude() string {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return ""
}

func (m *SearchBranchForGoogleMapsRequest) GetZipcode() string {
	if m != nil && m.Zipcode != nil {
		return *m.Zipcode
	}
	return ""
}

func (m *SearchBranchForGoogleMapsRequest) GetDistance() uint32 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

type SearchBranchResponse struct {
	RespHeader           *RespHeader        `protobuf:"bytes,1,opt,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Count                *uint32            `protobuf:"varint,2,opt,name=count" json:"count,omitempty"`
	BranchInfo           []*BranchInfo      `protobuf:"bytes,3,rep,name=branch_info,json=branchInfo" json:"branch_info,omitempty"`
	Distance             map[uint64]float64 `protobuf:"bytes,4,rep,name=distance" json:"distance,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SearchBranchResponse) Reset()         { *m = SearchBranchResponse{} }
func (m *SearchBranchResponse) String() string { return proto.CompactTextString(m) }
func (*SearchBranchResponse) ProtoMessage()    {}
func (*SearchBranchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{25}
}

func (m *SearchBranchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchBranchResponse.Unmarshal(m, b)
}
func (m *SearchBranchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchBranchResponse.Marshal(b, m, deterministic)
}
func (m *SearchBranchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchBranchResponse.Merge(m, src)
}
func (m *SearchBranchResponse) XXX_Size() int {
	return xxx_messageInfo_SearchBranchResponse.Size(m)
}
func (m *SearchBranchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchBranchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchBranchResponse proto.InternalMessageInfo

func (m *SearchBranchResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *SearchBranchResponse) GetCount() uint32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *SearchBranchResponse) GetBranchInfo() []*BranchInfo {
	if m != nil {
		return m.BranchInfo
	}
	return nil
}

func (m *SearchBranchResponse) GetDistance() map[uint64]float64 {
	if m != nil {
		return m.Distance
	}
	return nil
}

type SearchBranchForGoogleMapsResponse struct {
	RespHeader           *RespHeader        `protobuf:"bytes,1,opt,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Count                *uint32            `protobuf:"varint,2,opt,name=count" json:"count,omitempty"`
	BranchInfo           []*FullBranchInfo  `protobuf:"bytes,3,rep,name=branch_info,json=branchInfo" json:"branch_info,omitempty"`
	Distance             map[uint64]float64 `protobuf:"bytes,4,rep,name=distance" json:"distance,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SearchBranchForGoogleMapsResponse) Reset()         { *m = SearchBranchForGoogleMapsResponse{} }
func (m *SearchBranchForGoogleMapsResponse) String() string { return proto.CompactTextString(m) }
func (*SearchBranchForGoogleMapsResponse) ProtoMessage()    {}
func (*SearchBranchForGoogleMapsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{26}
}

func (m *SearchBranchForGoogleMapsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchBranchForGoogleMapsResponse.Unmarshal(m, b)
}
func (m *SearchBranchForGoogleMapsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchBranchForGoogleMapsResponse.Marshal(b, m, deterministic)
}
func (m *SearchBranchForGoogleMapsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchBranchForGoogleMapsResponse.Merge(m, src)
}
func (m *SearchBranchForGoogleMapsResponse) XXX_Size() int {
	return xxx_messageInfo_SearchBranchForGoogleMapsResponse.Size(m)
}
func (m *SearchBranchForGoogleMapsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchBranchForGoogleMapsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchBranchForGoogleMapsResponse proto.InternalMessageInfo

func (m *SearchBranchForGoogleMapsResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *SearchBranchForGoogleMapsResponse) GetCount() uint32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *SearchBranchForGoogleMapsResponse) GetBranchInfo() []*FullBranchInfo {
	if m != nil {
		return m.BranchInfo
	}
	return nil
}

func (m *SearchBranchForGoogleMapsResponse) GetDistance() map[uint64]float64 {
	if m != nil {
		return m.Distance
	}
	return nil
}

type GetBranchListByBranchGroupIDRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,opt,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BranchGroupId        *uint32    `protobuf:"varint,2,req,name=branch_group_id,json=branchGroupId" json:"branch_group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetBranchListByBranchGroupIDRequest) Reset()         { *m = GetBranchListByBranchGroupIDRequest{} }
func (m *GetBranchListByBranchGroupIDRequest) String() string { return proto.CompactTextString(m) }
func (*GetBranchListByBranchGroupIDRequest) ProtoMessage()    {}
func (*GetBranchListByBranchGroupIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{27}
}

func (m *GetBranchListByBranchGroupIDRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchListByBranchGroupIDRequest.Unmarshal(m, b)
}
func (m *GetBranchListByBranchGroupIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchListByBranchGroupIDRequest.Marshal(b, m, deterministic)
}
func (m *GetBranchListByBranchGroupIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchListByBranchGroupIDRequest.Merge(m, src)
}
func (m *GetBranchListByBranchGroupIDRequest) XXX_Size() int {
	return xxx_messageInfo_GetBranchListByBranchGroupIDRequest.Size(m)
}
func (m *GetBranchListByBranchGroupIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchListByBranchGroupIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchListByBranchGroupIDRequest proto.InternalMessageInfo

func (m *GetBranchListByBranchGroupIDRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetBranchListByBranchGroupIDRequest) GetBranchGroupId() uint32 {
	if m != nil && m.BranchGroupId != nil {
		return *m.BranchGroupId
	}
	return 0
}

type GetBranchListByBranchGroupIDResponse struct {
	RespHeader           *RespHeader   `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	BranchInfoList       []*BranchInfo `protobuf:"bytes,2,rep,name=branch_info_list,json=branchInfoList" json:"branch_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBranchListByBranchGroupIDResponse) Reset()         { *m = GetBranchListByBranchGroupIDResponse{} }
func (m *GetBranchListByBranchGroupIDResponse) String() string { return proto.CompactTextString(m) }
func (*GetBranchListByBranchGroupIDResponse) ProtoMessage()    {}
func (*GetBranchListByBranchGroupIDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{28}
}

func (m *GetBranchListByBranchGroupIDResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBranchListByBranchGroupIDResponse.Unmarshal(m, b)
}
func (m *GetBranchListByBranchGroupIDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBranchListByBranchGroupIDResponse.Marshal(b, m, deterministic)
}
func (m *GetBranchListByBranchGroupIDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBranchListByBranchGroupIDResponse.Merge(m, src)
}
func (m *GetBranchListByBranchGroupIDResponse) XXX_Size() int {
	return xxx_messageInfo_GetBranchListByBranchGroupIDResponse.Size(m)
}
func (m *GetBranchListByBranchGroupIDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBranchListByBranchGroupIDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBranchListByBranchGroupIDResponse proto.InternalMessageInfo

func (m *GetBranchListByBranchGroupIDResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetBranchListByBranchGroupIDResponse) GetBranchInfoList() []*BranchInfo {
	if m != nil {
		return m.BranchInfoList
	}
	return nil
}

type SearchDropoffBranchRequest struct {
	UniqueId             *string              `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	SearchBranchRequest  *SearchBranchRequest `protobuf:"bytes,2,req,name=search_branch_request,json=searchBranchRequest" json:"search_branch_request,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SearchDropoffBranchRequest) Reset()         { *m = SearchDropoffBranchRequest{} }
func (m *SearchDropoffBranchRequest) String() string { return proto.CompactTextString(m) }
func (*SearchDropoffBranchRequest) ProtoMessage()    {}
func (*SearchDropoffBranchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{29}
}

func (m *SearchDropoffBranchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchDropoffBranchRequest.Unmarshal(m, b)
}
func (m *SearchDropoffBranchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchDropoffBranchRequest.Marshal(b, m, deterministic)
}
func (m *SearchDropoffBranchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchDropoffBranchRequest.Merge(m, src)
}
func (m *SearchDropoffBranchRequest) XXX_Size() int {
	return xxx_messageInfo_SearchDropoffBranchRequest.Size(m)
}
func (m *SearchDropoffBranchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchDropoffBranchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchDropoffBranchRequest proto.InternalMessageInfo

func (m *SearchDropoffBranchRequest) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SearchDropoffBranchRequest) GetSearchBranchRequest() *SearchBranchRequest {
	if m != nil {
		return m.SearchBranchRequest
	}
	return nil
}

type BatchSearchDropoffBranchRequest struct {
	ReqHeader                  *ReqHeader                    `protobuf:"bytes,1,opt,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	SearchDropoffBranchRequest []*SearchDropoffBranchRequest `protobuf:"bytes,2,rep,name=search_dropoff_branch_request,json=searchDropoffBranchRequest" json:"search_dropoff_branch_request,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                      `json:"-"`
	XXX_unrecognized           []byte                        `json:"-"`
	XXX_sizecache              int32                         `json:"-"`
}

func (m *BatchSearchDropoffBranchRequest) Reset()         { *m = BatchSearchDropoffBranchRequest{} }
func (m *BatchSearchDropoffBranchRequest) String() string { return proto.CompactTextString(m) }
func (*BatchSearchDropoffBranchRequest) ProtoMessage()    {}
func (*BatchSearchDropoffBranchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{30}
}

func (m *BatchSearchDropoffBranchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSearchDropoffBranchRequest.Unmarshal(m, b)
}
func (m *BatchSearchDropoffBranchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSearchDropoffBranchRequest.Marshal(b, m, deterministic)
}
func (m *BatchSearchDropoffBranchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSearchDropoffBranchRequest.Merge(m, src)
}
func (m *BatchSearchDropoffBranchRequest) XXX_Size() int {
	return xxx_messageInfo_BatchSearchDropoffBranchRequest.Size(m)
}
func (m *BatchSearchDropoffBranchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSearchDropoffBranchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSearchDropoffBranchRequest proto.InternalMessageInfo

func (m *BatchSearchDropoffBranchRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchSearchDropoffBranchRequest) GetSearchDropoffBranchRequest() []*SearchDropoffBranchRequest {
	if m != nil {
		return m.SearchDropoffBranchRequest
	}
	return nil
}

type BatchSearchDropoffBranchResponse struct {
	RespHeader           *RespHeader                      `protobuf:"bytes,1,opt,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	BranchResultMap      map[string]*SearchBranchResponse `protobuf:"bytes,2,rep,name=branch_result_map,json=branchResultMap" json:"branch_result_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *BatchSearchDropoffBranchResponse) Reset()         { *m = BatchSearchDropoffBranchResponse{} }
func (m *BatchSearchDropoffBranchResponse) String() string { return proto.CompactTextString(m) }
func (*BatchSearchDropoffBranchResponse) ProtoMessage()    {}
func (*BatchSearchDropoffBranchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{31}
}

func (m *BatchSearchDropoffBranchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSearchDropoffBranchResponse.Unmarshal(m, b)
}
func (m *BatchSearchDropoffBranchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSearchDropoffBranchResponse.Marshal(b, m, deterministic)
}
func (m *BatchSearchDropoffBranchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSearchDropoffBranchResponse.Merge(m, src)
}
func (m *BatchSearchDropoffBranchResponse) XXX_Size() int {
	return xxx_messageInfo_BatchSearchDropoffBranchResponse.Size(m)
}
func (m *BatchSearchDropoffBranchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSearchDropoffBranchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSearchDropoffBranchResponse proto.InternalMessageInfo

func (m *BatchSearchDropoffBranchResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchSearchDropoffBranchResponse) GetBranchResultMap() map[string]*SearchBranchResponse {
	if m != nil {
		return m.BranchResultMap
	}
	return nil
}

type GetSpxStationIdInfoByOrderIdRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,opt,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	OrderId              *uint64    `protobuf:"varint,2,opt,name=order_id,json=orderId" json:"order_id,omitempty"`
	Address              *string    `protobuf:"bytes,3,opt,name=address" json:"address,omitempty"`
	AddressL1            *string    `protobuf:"bytes,4,opt,name=address_l1,json=addressL1" json:"address_l1,omitempty"`
	AddressL2            *string    `protobuf:"bytes,5,opt,name=address_l2,json=addressL2" json:"address_l2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetSpxStationIdInfoByOrderIdRequest) Reset()         { *m = GetSpxStationIdInfoByOrderIdRequest{} }
func (m *GetSpxStationIdInfoByOrderIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetSpxStationIdInfoByOrderIdRequest) ProtoMessage()    {}
func (*GetSpxStationIdInfoByOrderIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{32}
}

func (m *GetSpxStationIdInfoByOrderIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpxStationIdInfoByOrderIdRequest.Unmarshal(m, b)
}
func (m *GetSpxStationIdInfoByOrderIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpxStationIdInfoByOrderIdRequest.Marshal(b, m, deterministic)
}
func (m *GetSpxStationIdInfoByOrderIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpxStationIdInfoByOrderIdRequest.Merge(m, src)
}
func (m *GetSpxStationIdInfoByOrderIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetSpxStationIdInfoByOrderIdRequest.Size(m)
}
func (m *GetSpxStationIdInfoByOrderIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpxStationIdInfoByOrderIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpxStationIdInfoByOrderIdRequest proto.InternalMessageInfo

func (m *GetSpxStationIdInfoByOrderIdRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetSpxStationIdInfoByOrderIdRequest) GetOrderId() uint64 {
	if m != nil && m.OrderId != nil {
		return *m.OrderId
	}
	return 0
}

func (m *GetSpxStationIdInfoByOrderIdRequest) GetAddress() string {
	if m != nil && m.Address != nil {
		return *m.Address
	}
	return ""
}

func (m *GetSpxStationIdInfoByOrderIdRequest) GetAddressL1() string {
	if m != nil && m.AddressL1 != nil {
		return *m.AddressL1
	}
	return ""
}

func (m *GetSpxStationIdInfoByOrderIdRequest) GetAddressL2() string {
	if m != nil && m.AddressL2 != nil {
		return *m.AddressL2
	}
	return ""
}

type GetSpxStationIdInfoByOrderIdResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	StationId            *uint64     `protobuf:"varint,2,opt,name=station_id,json=stationId" json:"station_id,omitempty"`
	DeliverMode          *int32      `protobuf:"varint,3,opt,name=deliver_mode,json=deliverMode" json:"deliver_mode,omitempty"`
	LineList             []string    `protobuf:"bytes,4,rep,name=line_list,json=lineList" json:"line_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetSpxStationIdInfoByOrderIdResponse) Reset()         { *m = GetSpxStationIdInfoByOrderIdResponse{} }
func (m *GetSpxStationIdInfoByOrderIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetSpxStationIdInfoByOrderIdResponse) ProtoMessage()    {}
func (*GetSpxStationIdInfoByOrderIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{33}
}

func (m *GetSpxStationIdInfoByOrderIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpxStationIdInfoByOrderIdResponse.Unmarshal(m, b)
}
func (m *GetSpxStationIdInfoByOrderIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpxStationIdInfoByOrderIdResponse.Marshal(b, m, deterministic)
}
func (m *GetSpxStationIdInfoByOrderIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpxStationIdInfoByOrderIdResponse.Merge(m, src)
}
func (m *GetSpxStationIdInfoByOrderIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetSpxStationIdInfoByOrderIdResponse.Size(m)
}
func (m *GetSpxStationIdInfoByOrderIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpxStationIdInfoByOrderIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpxStationIdInfoByOrderIdResponse proto.InternalMessageInfo

func (m *GetSpxStationIdInfoByOrderIdResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetSpxStationIdInfoByOrderIdResponse) GetStationId() uint64 {
	if m != nil && m.StationId != nil {
		return *m.StationId
	}
	return 0
}

func (m *GetSpxStationIdInfoByOrderIdResponse) GetDeliverMode() int32 {
	if m != nil && m.DeliverMode != nil {
		return *m.DeliverMode
	}
	return 0
}

func (m *GetSpxStationIdInfoByOrderIdResponse) GetLineList() []string {
	if m != nil {
		return m.LineList
	}
	return nil
}

type CheckHDAbilityRequest struct {
	ReqHeader                  *ReqHeader                   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BuyerId                    *string                      `protobuf:"bytes,2,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`
	CheckHdAbilityAddressItems []*CheckHDAbilityAddressItem `protobuf:"bytes,3,rep,name=check_hd_ability_address_items,json=checkHdAbilityAddressItems" json:"check_hd_ability_address_items,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                     `json:"-"`
	XXX_unrecognized           []byte                       `json:"-"`
	XXX_sizecache              int32                        `json:"-"`
}

func (m *CheckHDAbilityRequest) Reset()         { *m = CheckHDAbilityRequest{} }
func (m *CheckHDAbilityRequest) String() string { return proto.CompactTextString(m) }
func (*CheckHDAbilityRequest) ProtoMessage()    {}
func (*CheckHDAbilityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{34}
}

func (m *CheckHDAbilityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckHDAbilityRequest.Unmarshal(m, b)
}
func (m *CheckHDAbilityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckHDAbilityRequest.Marshal(b, m, deterministic)
}
func (m *CheckHDAbilityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckHDAbilityRequest.Merge(m, src)
}
func (m *CheckHDAbilityRequest) XXX_Size() int {
	return xxx_messageInfo_CheckHDAbilityRequest.Size(m)
}
func (m *CheckHDAbilityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckHDAbilityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckHDAbilityRequest proto.InternalMessageInfo

func (m *CheckHDAbilityRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *CheckHDAbilityRequest) GetBuyerId() string {
	if m != nil && m.BuyerId != nil {
		return *m.BuyerId
	}
	return ""
}

func (m *CheckHDAbilityRequest) GetCheckHdAbilityAddressItems() []*CheckHDAbilityAddressItem {
	if m != nil {
		return m.CheckHdAbilityAddressItems
	}
	return nil
}

type CheckHDAbilityAddressItem struct {
	Address              *string  `protobuf:"bytes,1,opt,name=address" json:"address,omitempty"`
	AddressL1            *string  `protobuf:"bytes,2,opt,name=address_l1,json=addressL1" json:"address_l1,omitempty"`
	AddressL2            *string  `protobuf:"bytes,3,opt,name=address_l2,json=addressL2" json:"address_l2,omitempty"`
	UniqueKey            *string  `protobuf:"bytes,4,opt,name=unique_key,json=uniqueKey" json:"unique_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckHDAbilityAddressItem) Reset()         { *m = CheckHDAbilityAddressItem{} }
func (m *CheckHDAbilityAddressItem) String() string { return proto.CompactTextString(m) }
func (*CheckHDAbilityAddressItem) ProtoMessage()    {}
func (*CheckHDAbilityAddressItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{35}
}

func (m *CheckHDAbilityAddressItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckHDAbilityAddressItem.Unmarshal(m, b)
}
func (m *CheckHDAbilityAddressItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckHDAbilityAddressItem.Marshal(b, m, deterministic)
}
func (m *CheckHDAbilityAddressItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckHDAbilityAddressItem.Merge(m, src)
}
func (m *CheckHDAbilityAddressItem) XXX_Size() int {
	return xxx_messageInfo_CheckHDAbilityAddressItem.Size(m)
}
func (m *CheckHDAbilityAddressItem) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckHDAbilityAddressItem.DiscardUnknown(m)
}

var xxx_messageInfo_CheckHDAbilityAddressItem proto.InternalMessageInfo

func (m *CheckHDAbilityAddressItem) GetAddress() string {
	if m != nil && m.Address != nil {
		return *m.Address
	}
	return ""
}

func (m *CheckHDAbilityAddressItem) GetAddressL1() string {
	if m != nil && m.AddressL1 != nil {
		return *m.AddressL1
	}
	return ""
}

func (m *CheckHDAbilityAddressItem) GetAddressL2() string {
	if m != nil && m.AddressL2 != nil {
		return *m.AddressL2
	}
	return ""
}

func (m *CheckHDAbilityAddressItem) GetUniqueKey() string {
	if m != nil && m.UniqueKey != nil {
		return *m.UniqueKey
	}
	return ""
}

type CheckHDAbilityResponse struct {
	RespHeader                   *RespHeader                     `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	BuyerId                      *string                         `protobuf:"bytes,2,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`
	CheckHdAbilityAddressResItem []*CheckHDAbilityAddressResItem `protobuf:"bytes,3,rep,name=check_hd_ability_address_res_item,json=checkHdAbilityAddressResItem" json:"check_hd_ability_address_res_item,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}                        `json:"-"`
	XXX_unrecognized             []byte                          `json:"-"`
	XXX_sizecache                int32                           `json:"-"`
}

func (m *CheckHDAbilityResponse) Reset()         { *m = CheckHDAbilityResponse{} }
func (m *CheckHDAbilityResponse) String() string { return proto.CompactTextString(m) }
func (*CheckHDAbilityResponse) ProtoMessage()    {}
func (*CheckHDAbilityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{36}
}

func (m *CheckHDAbilityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckHDAbilityResponse.Unmarshal(m, b)
}
func (m *CheckHDAbilityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckHDAbilityResponse.Marshal(b, m, deterministic)
}
func (m *CheckHDAbilityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckHDAbilityResponse.Merge(m, src)
}
func (m *CheckHDAbilityResponse) XXX_Size() int {
	return xxx_messageInfo_CheckHDAbilityResponse.Size(m)
}
func (m *CheckHDAbilityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckHDAbilityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckHDAbilityResponse proto.InternalMessageInfo

func (m *CheckHDAbilityResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *CheckHDAbilityResponse) GetBuyerId() string {
	if m != nil && m.BuyerId != nil {
		return *m.BuyerId
	}
	return ""
}

func (m *CheckHDAbilityResponse) GetCheckHdAbilityAddressResItem() []*CheckHDAbilityAddressResItem {
	if m != nil {
		return m.CheckHdAbilityAddressResItem
	}
	return nil
}

type CheckHDAbilityAddressResItem struct {
	Address              *string  `protobuf:"bytes,1,opt,name=address" json:"address,omitempty"`
	AddressL1            *string  `protobuf:"bytes,2,opt,name=address_l1,json=addressL1" json:"address_l1,omitempty"`
	AddressL2            *string  `protobuf:"bytes,3,opt,name=address_l2,json=addressL2" json:"address_l2,omitempty"`
	UniqueKey            *string  `protobuf:"bytes,4,opt,name=unique_key,json=uniqueKey" json:"unique_key,omitempty"`
	IsSupportHd          *int32   `protobuf:"varint,5,opt,name=is_support_hd,json=isSupportHd" json:"is_support_hd,omitempty"`
	ReturnType           *string  `protobuf:"bytes,6,opt,name=return_type,json=returnType" json:"return_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckHDAbilityAddressResItem) Reset()         { *m = CheckHDAbilityAddressResItem{} }
func (m *CheckHDAbilityAddressResItem) String() string { return proto.CompactTextString(m) }
func (*CheckHDAbilityAddressResItem) ProtoMessage()    {}
func (*CheckHDAbilityAddressResItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{37}
}

func (m *CheckHDAbilityAddressResItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckHDAbilityAddressResItem.Unmarshal(m, b)
}
func (m *CheckHDAbilityAddressResItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckHDAbilityAddressResItem.Marshal(b, m, deterministic)
}
func (m *CheckHDAbilityAddressResItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckHDAbilityAddressResItem.Merge(m, src)
}
func (m *CheckHDAbilityAddressResItem) XXX_Size() int {
	return xxx_messageInfo_CheckHDAbilityAddressResItem.Size(m)
}
func (m *CheckHDAbilityAddressResItem) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckHDAbilityAddressResItem.DiscardUnknown(m)
}

var xxx_messageInfo_CheckHDAbilityAddressResItem proto.InternalMessageInfo

func (m *CheckHDAbilityAddressResItem) GetAddress() string {
	if m != nil && m.Address != nil {
		return *m.Address
	}
	return ""
}

func (m *CheckHDAbilityAddressResItem) GetAddressL1() string {
	if m != nil && m.AddressL1 != nil {
		return *m.AddressL1
	}
	return ""
}

func (m *CheckHDAbilityAddressResItem) GetAddressL2() string {
	if m != nil && m.AddressL2 != nil {
		return *m.AddressL2
	}
	return ""
}

func (m *CheckHDAbilityAddressResItem) GetUniqueKey() string {
	if m != nil && m.UniqueKey != nil {
		return *m.UniqueKey
	}
	return ""
}

func (m *CheckHDAbilityAddressResItem) GetIsSupportHd() int32 {
	if m != nil && m.IsSupportHd != nil {
		return *m.IsSupportHd
	}
	return 0
}

func (m *CheckHDAbilityAddressResItem) GetReturnType() string {
	if m != nil && m.ReturnType != nil {
		return *m.ReturnType
	}
	return ""
}

type GetHDStationRequest struct {
	ReqHeader               *ReqHeader               `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	GetHdStationAddressItem *GetHDStationAddressItem `protobuf:"bytes,2,opt,name=get_hd_station_address_item,json=getHdStationAddressItem" json:"get_hd_station_address_item,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                 `json:"-"`
	XXX_unrecognized        []byte                   `json:"-"`
	XXX_sizecache           int32                    `json:"-"`
}

func (m *GetHDStationRequest) Reset()         { *m = GetHDStationRequest{} }
func (m *GetHDStationRequest) String() string { return proto.CompactTextString(m) }
func (*GetHDStationRequest) ProtoMessage()    {}
func (*GetHDStationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{38}
}

func (m *GetHDStationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHDStationRequest.Unmarshal(m, b)
}
func (m *GetHDStationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHDStationRequest.Marshal(b, m, deterministic)
}
func (m *GetHDStationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHDStationRequest.Merge(m, src)
}
func (m *GetHDStationRequest) XXX_Size() int {
	return xxx_messageInfo_GetHDStationRequest.Size(m)
}
func (m *GetHDStationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHDStationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHDStationRequest proto.InternalMessageInfo

func (m *GetHDStationRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetHDStationRequest) GetGetHdStationAddressItem() *GetHDStationAddressItem {
	if m != nil {
		return m.GetHdStationAddressItem
	}
	return nil
}

type GetHDStationAddressItem struct {
	AddressL1            *string  `protobuf:"bytes,1,opt,name=address_l1,json=addressL1" json:"address_l1,omitempty"`
	AddressL2            *string  `protobuf:"bytes,2,opt,name=address_l2,json=addressL2" json:"address_l2,omitempty"`
	Address              *string  `protobuf:"bytes,3,opt,name=address" json:"address,omitempty"`
	ShipmentId           *string  `protobuf:"bytes,4,opt,name=shipment_id,json=shipmentId" json:"shipment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHDStationAddressItem) Reset()         { *m = GetHDStationAddressItem{} }
func (m *GetHDStationAddressItem) String() string { return proto.CompactTextString(m) }
func (*GetHDStationAddressItem) ProtoMessage()    {}
func (*GetHDStationAddressItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{39}
}

func (m *GetHDStationAddressItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHDStationAddressItem.Unmarshal(m, b)
}
func (m *GetHDStationAddressItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHDStationAddressItem.Marshal(b, m, deterministic)
}
func (m *GetHDStationAddressItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHDStationAddressItem.Merge(m, src)
}
func (m *GetHDStationAddressItem) XXX_Size() int {
	return xxx_messageInfo_GetHDStationAddressItem.Size(m)
}
func (m *GetHDStationAddressItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHDStationAddressItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetHDStationAddressItem proto.InternalMessageInfo

func (m *GetHDStationAddressItem) GetAddressL1() string {
	if m != nil && m.AddressL1 != nil {
		return *m.AddressL1
	}
	return ""
}

func (m *GetHDStationAddressItem) GetAddressL2() string {
	if m != nil && m.AddressL2 != nil {
		return *m.AddressL2
	}
	return ""
}

func (m *GetHDStationAddressItem) GetAddress() string {
	if m != nil && m.Address != nil {
		return *m.Address
	}
	return ""
}

func (m *GetHDStationAddressItem) GetShipmentId() string {
	if m != nil && m.ShipmentId != nil {
		return *m.ShipmentId
	}
	return ""
}

type GetHDStationResponse struct {
	RespHeader                 *RespHeader                 `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	GetHdStationAddressResItem *GetHDStationAddressResItem `protobuf:"bytes,2,opt,name=get_hd_station_address_res_item,json=getHdStationAddressResItem" json:"get_hd_station_address_res_item,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                    `json:"-"`
	XXX_unrecognized           []byte                      `json:"-"`
	XXX_sizecache              int32                       `json:"-"`
}

func (m *GetHDStationResponse) Reset()         { *m = GetHDStationResponse{} }
func (m *GetHDStationResponse) String() string { return proto.CompactTextString(m) }
func (*GetHDStationResponse) ProtoMessage()    {}
func (*GetHDStationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{40}
}

func (m *GetHDStationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHDStationResponse.Unmarshal(m, b)
}
func (m *GetHDStationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHDStationResponse.Marshal(b, m, deterministic)
}
func (m *GetHDStationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHDStationResponse.Merge(m, src)
}
func (m *GetHDStationResponse) XXX_Size() int {
	return xxx_messageInfo_GetHDStationResponse.Size(m)
}
func (m *GetHDStationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHDStationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHDStationResponse proto.InternalMessageInfo

func (m *GetHDStationResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetHDStationResponse) GetGetHdStationAddressResItem() *GetHDStationAddressResItem {
	if m != nil {
		return m.GetHdStationAddressResItem
	}
	return nil
}

type GetHDStationAddressResItem struct {
	AddressL1            *string  `protobuf:"bytes,1,opt,name=address_l1,json=addressL1" json:"address_l1,omitempty"`
	AddressL2            *string  `protobuf:"bytes,2,opt,name=address_l2,json=addressL2" json:"address_l2,omitempty"`
	Address              *string  `protobuf:"bytes,3,opt,name=address" json:"address,omitempty"`
	StationId            *uint64  `protobuf:"varint,4,opt,name=station_id,json=stationId" json:"station_id,omitempty"`
	AddressLng           *float32 `protobuf:"fixed32,5,opt,name=address_lng,json=addressLng" json:"address_lng,omitempty"`
	AddressLat           *float32 `protobuf:"fixed32,6,opt,name=address_lat,json=addressLat" json:"address_lat,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHDStationAddressResItem) Reset()         { *m = GetHDStationAddressResItem{} }
func (m *GetHDStationAddressResItem) String() string { return proto.CompactTextString(m) }
func (*GetHDStationAddressResItem) ProtoMessage()    {}
func (*GetHDStationAddressResItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{41}
}

func (m *GetHDStationAddressResItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHDStationAddressResItem.Unmarshal(m, b)
}
func (m *GetHDStationAddressResItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHDStationAddressResItem.Marshal(b, m, deterministic)
}
func (m *GetHDStationAddressResItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHDStationAddressResItem.Merge(m, src)
}
func (m *GetHDStationAddressResItem) XXX_Size() int {
	return xxx_messageInfo_GetHDStationAddressResItem.Size(m)
}
func (m *GetHDStationAddressResItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHDStationAddressResItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetHDStationAddressResItem proto.InternalMessageInfo

func (m *GetHDStationAddressResItem) GetAddressL1() string {
	if m != nil && m.AddressL1 != nil {
		return *m.AddressL1
	}
	return ""
}

func (m *GetHDStationAddressResItem) GetAddressL2() string {
	if m != nil && m.AddressL2 != nil {
		return *m.AddressL2
	}
	return ""
}

func (m *GetHDStationAddressResItem) GetAddress() string {
	if m != nil && m.Address != nil {
		return *m.Address
	}
	return ""
}

func (m *GetHDStationAddressResItem) GetStationId() uint64 {
	if m != nil && m.StationId != nil {
		return *m.StationId
	}
	return 0
}

func (m *GetHDStationAddressResItem) GetAddressLng() float32 {
	if m != nil && m.AddressLng != nil {
		return *m.AddressLng
	}
	return 0
}

func (m *GetHDStationAddressResItem) GetAddressLat() float32 {
	if m != nil && m.AddressLat != nil {
		return *m.AddressLat
	}
	return 0
}

type QueryCacheValItem struct {
	CacheKey             *string  `protobuf:"bytes,1,req,name=cache_key,json=cacheKey" json:"cache_key,omitempty"`
	CacheType            *string  `protobuf:"bytes,2,req,name=cache_type,json=cacheType" json:"cache_type,omitempty"`
	ValType              *string  `protobuf:"bytes,3,req,name=val_type,json=valType" json:"val_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryCacheValItem) Reset()         { *m = QueryCacheValItem{} }
func (m *QueryCacheValItem) String() string { return proto.CompactTextString(m) }
func (*QueryCacheValItem) ProtoMessage()    {}
func (*QueryCacheValItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{42}
}

func (m *QueryCacheValItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCacheValItem.Unmarshal(m, b)
}
func (m *QueryCacheValItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCacheValItem.Marshal(b, m, deterministic)
}
func (m *QueryCacheValItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCacheValItem.Merge(m, src)
}
func (m *QueryCacheValItem) XXX_Size() int {
	return xxx_messageInfo_QueryCacheValItem.Size(m)
}
func (m *QueryCacheValItem) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCacheValItem.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCacheValItem proto.InternalMessageInfo

func (m *QueryCacheValItem) GetCacheKey() string {
	if m != nil && m.CacheKey != nil {
		return *m.CacheKey
	}
	return ""
}

func (m *QueryCacheValItem) GetCacheType() string {
	if m != nil && m.CacheType != nil {
		return *m.CacheType
	}
	return ""
}

func (m *QueryCacheValItem) GetValType() string {
	if m != nil && m.ValType != nil {
		return *m.ValType
	}
	return ""
}

type QueryCacheValReq struct {
	List                 []*QueryCacheValItem `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *QueryCacheValReq) Reset()         { *m = QueryCacheValReq{} }
func (m *QueryCacheValReq) String() string { return proto.CompactTextString(m) }
func (*QueryCacheValReq) ProtoMessage()    {}
func (*QueryCacheValReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{43}
}

func (m *QueryCacheValReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCacheValReq.Unmarshal(m, b)
}
func (m *QueryCacheValReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCacheValReq.Marshal(b, m, deterministic)
}
func (m *QueryCacheValReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCacheValReq.Merge(m, src)
}
func (m *QueryCacheValReq) XXX_Size() int {
	return xxx_messageInfo_QueryCacheValReq.Size(m)
}
func (m *QueryCacheValReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCacheValReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCacheValReq proto.InternalMessageInfo

func (m *QueryCacheValReq) GetList() []*QueryCacheValItem {
	if m != nil {
		return m.List
	}
	return nil
}

type QueryCacheValRespItem struct {
	CacheKey             *string  `protobuf:"bytes,1,opt,name=cache_key,json=cacheKey" json:"cache_key,omitempty"`
	CacheType            *string  `protobuf:"bytes,2,opt,name=cache_type,json=cacheType" json:"cache_type,omitempty"`
	ValType              *string  `protobuf:"bytes,3,opt,name=val_type,json=valType" json:"val_type,omitempty"`
	Value                *string  `protobuf:"bytes,4,opt,name=value" json:"value,omitempty"`
	Retcode              *int32   `protobuf:"varint,5,opt,name=retcode" json:"retcode,omitempty"`
	Message              *string  `protobuf:"bytes,6,opt,name=message" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryCacheValRespItem) Reset()         { *m = QueryCacheValRespItem{} }
func (m *QueryCacheValRespItem) String() string { return proto.CompactTextString(m) }
func (*QueryCacheValRespItem) ProtoMessage()    {}
func (*QueryCacheValRespItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{44}
}

func (m *QueryCacheValRespItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCacheValRespItem.Unmarshal(m, b)
}
func (m *QueryCacheValRespItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCacheValRespItem.Marshal(b, m, deterministic)
}
func (m *QueryCacheValRespItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCacheValRespItem.Merge(m, src)
}
func (m *QueryCacheValRespItem) XXX_Size() int {
	return xxx_messageInfo_QueryCacheValRespItem.Size(m)
}
func (m *QueryCacheValRespItem) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCacheValRespItem.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCacheValRespItem proto.InternalMessageInfo

func (m *QueryCacheValRespItem) GetCacheKey() string {
	if m != nil && m.CacheKey != nil {
		return *m.CacheKey
	}
	return ""
}

func (m *QueryCacheValRespItem) GetCacheType() string {
	if m != nil && m.CacheType != nil {
		return *m.CacheType
	}
	return ""
}

func (m *QueryCacheValRespItem) GetValType() string {
	if m != nil && m.ValType != nil {
		return *m.ValType
	}
	return ""
}

func (m *QueryCacheValRespItem) GetValue() string {
	if m != nil && m.Value != nil {
		return *m.Value
	}
	return ""
}

func (m *QueryCacheValRespItem) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *QueryCacheValRespItem) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

type QueryCacheValResp struct {
	RespHeader           *RespHeader              `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	List                 []*QueryCacheValRespItem `protobuf:"bytes,2,rep,name=list" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *QueryCacheValResp) Reset()         { *m = QueryCacheValResp{} }
func (m *QueryCacheValResp) String() string { return proto.CompactTextString(m) }
func (*QueryCacheValResp) ProtoMessage()    {}
func (*QueryCacheValResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{45}
}

func (m *QueryCacheValResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCacheValResp.Unmarshal(m, b)
}
func (m *QueryCacheValResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCacheValResp.Marshal(b, m, deterministic)
}
func (m *QueryCacheValResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCacheValResp.Merge(m, src)
}
func (m *QueryCacheValResp) XXX_Size() int {
	return xxx_messageInfo_QueryCacheValResp.Size(m)
}
func (m *QueryCacheValResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCacheValResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCacheValResp proto.InternalMessageInfo

func (m *QueryCacheValResp) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *QueryCacheValResp) GetList() []*QueryCacheValRespItem {
	if m != nil {
		return m.List
	}
	return nil
}

type GetDrivingDistanceWithHDStationRequest struct {
	ReqHeader            *ReqHeader       `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BuyerId              *string          `protobuf:"bytes,2,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`
	AddressList          []*CommonAddress `protobuf:"bytes,3,rep,name=address_list,json=addressList" json:"address_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetDrivingDistanceWithHDStationRequest) Reset() {
	*m = GetDrivingDistanceWithHDStationRequest{}
}
func (m *GetDrivingDistanceWithHDStationRequest) String() string { return proto.CompactTextString(m) }
func (*GetDrivingDistanceWithHDStationRequest) ProtoMessage()    {}
func (*GetDrivingDistanceWithHDStationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{46}
}

func (m *GetDrivingDistanceWithHDStationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDrivingDistanceWithHDStationRequest.Unmarshal(m, b)
}
func (m *GetDrivingDistanceWithHDStationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDrivingDistanceWithHDStationRequest.Marshal(b, m, deterministic)
}
func (m *GetDrivingDistanceWithHDStationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDrivingDistanceWithHDStationRequest.Merge(m, src)
}
func (m *GetDrivingDistanceWithHDStationRequest) XXX_Size() int {
	return xxx_messageInfo_GetDrivingDistanceWithHDStationRequest.Size(m)
}
func (m *GetDrivingDistanceWithHDStationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDrivingDistanceWithHDStationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDrivingDistanceWithHDStationRequest proto.InternalMessageInfo

func (m *GetDrivingDistanceWithHDStationRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetDrivingDistanceWithHDStationRequest) GetBuyerId() string {
	if m != nil && m.BuyerId != nil {
		return *m.BuyerId
	}
	return ""
}

func (m *GetDrivingDistanceWithHDStationRequest) GetAddressList() []*CommonAddress {
	if m != nil {
		return m.AddressList
	}
	return nil
}

type GetDrivingDistanceWithHDStationResponse struct {
	RespHeader                          *RespHeader                        `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	StationToAddressDrivingDistanceList []*StationToAddressDrivingDistance `protobuf:"bytes,2,rep,name=station_to_address_driving_distance_list,json=stationToAddressDrivingDistanceList" json:"station_to_address_driving_distance_list,omitempty"`
	XXX_NoUnkeyedLiteral                struct{}                           `json:"-"`
	XXX_unrecognized                    []byte                             `json:"-"`
	XXX_sizecache                       int32                              `json:"-"`
}

func (m *GetDrivingDistanceWithHDStationResponse) Reset() {
	*m = GetDrivingDistanceWithHDStationResponse{}
}
func (m *GetDrivingDistanceWithHDStationResponse) String() string { return proto.CompactTextString(m) }
func (*GetDrivingDistanceWithHDStationResponse) ProtoMessage()    {}
func (*GetDrivingDistanceWithHDStationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{47}
}

func (m *GetDrivingDistanceWithHDStationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDrivingDistanceWithHDStationResponse.Unmarshal(m, b)
}
func (m *GetDrivingDistanceWithHDStationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDrivingDistanceWithHDStationResponse.Marshal(b, m, deterministic)
}
func (m *GetDrivingDistanceWithHDStationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDrivingDistanceWithHDStationResponse.Merge(m, src)
}
func (m *GetDrivingDistanceWithHDStationResponse) XXX_Size() int {
	return xxx_messageInfo_GetDrivingDistanceWithHDStationResponse.Size(m)
}
func (m *GetDrivingDistanceWithHDStationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDrivingDistanceWithHDStationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDrivingDistanceWithHDStationResponse proto.InternalMessageInfo

func (m *GetDrivingDistanceWithHDStationResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetDrivingDistanceWithHDStationResponse) GetStationToAddressDrivingDistanceList() []*StationToAddressDrivingDistance {
	if m != nil {
		return m.StationToAddressDrivingDistanceList
	}
	return nil
}

type GetBuyerAddressCoordinateRequest struct {
	ReqHeader            *ReqHeader       `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BuyerId              *string          `protobuf:"bytes,2,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`
	AddressList          []*CommonAddress `protobuf:"bytes,3,rep,name=address_list,json=addressList" json:"address_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetBuyerAddressCoordinateRequest) Reset()         { *m = GetBuyerAddressCoordinateRequest{} }
func (m *GetBuyerAddressCoordinateRequest) String() string { return proto.CompactTextString(m) }
func (*GetBuyerAddressCoordinateRequest) ProtoMessage()    {}
func (*GetBuyerAddressCoordinateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{48}
}

func (m *GetBuyerAddressCoordinateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBuyerAddressCoordinateRequest.Unmarshal(m, b)
}
func (m *GetBuyerAddressCoordinateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBuyerAddressCoordinateRequest.Marshal(b, m, deterministic)
}
func (m *GetBuyerAddressCoordinateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBuyerAddressCoordinateRequest.Merge(m, src)
}
func (m *GetBuyerAddressCoordinateRequest) XXX_Size() int {
	return xxx_messageInfo_GetBuyerAddressCoordinateRequest.Size(m)
}
func (m *GetBuyerAddressCoordinateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBuyerAddressCoordinateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBuyerAddressCoordinateRequest proto.InternalMessageInfo

func (m *GetBuyerAddressCoordinateRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetBuyerAddressCoordinateRequest) GetBuyerId() string {
	if m != nil && m.BuyerId != nil {
		return *m.BuyerId
	}
	return ""
}

func (m *GetBuyerAddressCoordinateRequest) GetAddressList() []*CommonAddress {
	if m != nil {
		return m.AddressList
	}
	return nil
}

type GetBuyerAddressCoordinateResponse struct {
	RespHeader            *RespHeader          `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	AddressCoordinateList []*AddressCoordinate `protobuf:"bytes,2,rep,name=address_coordinate_list,json=addressCoordinateList" json:"address_coordinate_list,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *GetBuyerAddressCoordinateResponse) Reset()         { *m = GetBuyerAddressCoordinateResponse{} }
func (m *GetBuyerAddressCoordinateResponse) String() string { return proto.CompactTextString(m) }
func (*GetBuyerAddressCoordinateResponse) ProtoMessage()    {}
func (*GetBuyerAddressCoordinateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{49}
}

func (m *GetBuyerAddressCoordinateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBuyerAddressCoordinateResponse.Unmarshal(m, b)
}
func (m *GetBuyerAddressCoordinateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBuyerAddressCoordinateResponse.Marshal(b, m, deterministic)
}
func (m *GetBuyerAddressCoordinateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBuyerAddressCoordinateResponse.Merge(m, src)
}
func (m *GetBuyerAddressCoordinateResponse) XXX_Size() int {
	return xxx_messageInfo_GetBuyerAddressCoordinateResponse.Size(m)
}
func (m *GetBuyerAddressCoordinateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBuyerAddressCoordinateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBuyerAddressCoordinateResponse proto.InternalMessageInfo

func (m *GetBuyerAddressCoordinateResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetBuyerAddressCoordinateResponse) GetAddressCoordinateList() []*AddressCoordinate {
	if m != nil {
		return m.AddressCoordinateList
	}
	return nil
}

type SearchNearbyStationRequest struct {
	ReqHeader                 *ReqHeader               `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	CenterPointWithRadiusList []*CenterPointWithRadius `protobuf:"bytes,2,rep,name=center_point_with_radius_list,json=centerPointWithRadiusList" json:"center_point_with_radius_list,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}                 `json:"-"`
	XXX_unrecognized          []byte                   `json:"-"`
	XXX_sizecache             int32                    `json:"-"`
}

func (m *SearchNearbyStationRequest) Reset()         { *m = SearchNearbyStationRequest{} }
func (m *SearchNearbyStationRequest) String() string { return proto.CompactTextString(m) }
func (*SearchNearbyStationRequest) ProtoMessage()    {}
func (*SearchNearbyStationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{50}
}

func (m *SearchNearbyStationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchNearbyStationRequest.Unmarshal(m, b)
}
func (m *SearchNearbyStationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchNearbyStationRequest.Marshal(b, m, deterministic)
}
func (m *SearchNearbyStationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchNearbyStationRequest.Merge(m, src)
}
func (m *SearchNearbyStationRequest) XXX_Size() int {
	return xxx_messageInfo_SearchNearbyStationRequest.Size(m)
}
func (m *SearchNearbyStationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchNearbyStationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchNearbyStationRequest proto.InternalMessageInfo

func (m *SearchNearbyStationRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *SearchNearbyStationRequest) GetCenterPointWithRadiusList() []*CenterPointWithRadius {
	if m != nil {
		return m.CenterPointWithRadiusList
	}
	return nil
}

type SearchNearbyStationResponse struct {
	RespHeader           *RespHeader      `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	NearbyStationList    []*NearbyStation `protobuf:"bytes,2,rep,name=nearby_station_list,json=nearbyStationList" json:"nearby_station_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SearchNearbyStationResponse) Reset()         { *m = SearchNearbyStationResponse{} }
func (m *SearchNearbyStationResponse) String() string { return proto.CompactTextString(m) }
func (*SearchNearbyStationResponse) ProtoMessage()    {}
func (*SearchNearbyStationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{51}
}

func (m *SearchNearbyStationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchNearbyStationResponse.Unmarshal(m, b)
}
func (m *SearchNearbyStationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchNearbyStationResponse.Marshal(b, m, deterministic)
}
func (m *SearchNearbyStationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchNearbyStationResponse.Merge(m, src)
}
func (m *SearchNearbyStationResponse) XXX_Size() int {
	return xxx_messageInfo_SearchNearbyStationResponse.Size(m)
}
func (m *SearchNearbyStationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchNearbyStationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchNearbyStationResponse proto.InternalMessageInfo

func (m *SearchNearbyStationResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *SearchNearbyStationResponse) GetNearbyStationList() []*NearbyStation {
	if m != nil {
		return m.NearbyStationList
	}
	return nil
}

type GetDrivingDistanceFromMatrixRequest struct {
	ReqHeader            *ReqHeader          `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	StartingPointList    []*CommonCoordinate `protobuf:"bytes,2,rep,name=starting_point_list,json=startingPointList" json:"starting_point_list,omitempty"`
	TerminatePointList   []*CommonCoordinate `protobuf:"bytes,3,rep,name=terminate_point_list,json=terminatePointList" json:"terminate_point_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetDrivingDistanceFromMatrixRequest) Reset()         { *m = GetDrivingDistanceFromMatrixRequest{} }
func (m *GetDrivingDistanceFromMatrixRequest) String() string { return proto.CompactTextString(m) }
func (*GetDrivingDistanceFromMatrixRequest) ProtoMessage()    {}
func (*GetDrivingDistanceFromMatrixRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{52}
}

func (m *GetDrivingDistanceFromMatrixRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDrivingDistanceFromMatrixRequest.Unmarshal(m, b)
}
func (m *GetDrivingDistanceFromMatrixRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDrivingDistanceFromMatrixRequest.Marshal(b, m, deterministic)
}
func (m *GetDrivingDistanceFromMatrixRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDrivingDistanceFromMatrixRequest.Merge(m, src)
}
func (m *GetDrivingDistanceFromMatrixRequest) XXX_Size() int {
	return xxx_messageInfo_GetDrivingDistanceFromMatrixRequest.Size(m)
}
func (m *GetDrivingDistanceFromMatrixRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDrivingDistanceFromMatrixRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDrivingDistanceFromMatrixRequest proto.InternalMessageInfo

func (m *GetDrivingDistanceFromMatrixRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetDrivingDistanceFromMatrixRequest) GetStartingPointList() []*CommonCoordinate {
	if m != nil {
		return m.StartingPointList
	}
	return nil
}

func (m *GetDrivingDistanceFromMatrixRequest) GetTerminatePointList() []*CommonCoordinate {
	if m != nil {
		return m.TerminatePointList
	}
	return nil
}

type GetDrivingDistanceFromMatrixResponse struct {
	RespHeader               *RespHeader             `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CommonMatrixDistanceList []*CommonMatrixDistance `protobuf:"bytes,2,rep,name=common_matrix_distance_list,json=commonMatrixDistanceList" json:"common_matrix_distance_list,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                `json:"-"`
	XXX_unrecognized         []byte                  `json:"-"`
	XXX_sizecache            int32                   `json:"-"`
}

func (m *GetDrivingDistanceFromMatrixResponse) Reset()         { *m = GetDrivingDistanceFromMatrixResponse{} }
func (m *GetDrivingDistanceFromMatrixResponse) String() string { return proto.CompactTextString(m) }
func (*GetDrivingDistanceFromMatrixResponse) ProtoMessage()    {}
func (*GetDrivingDistanceFromMatrixResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{53}
}

func (m *GetDrivingDistanceFromMatrixResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDrivingDistanceFromMatrixResponse.Unmarshal(m, b)
}
func (m *GetDrivingDistanceFromMatrixResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDrivingDistanceFromMatrixResponse.Marshal(b, m, deterministic)
}
func (m *GetDrivingDistanceFromMatrixResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDrivingDistanceFromMatrixResponse.Merge(m, src)
}
func (m *GetDrivingDistanceFromMatrixResponse) XXX_Size() int {
	return xxx_messageInfo_GetDrivingDistanceFromMatrixResponse.Size(m)
}
func (m *GetDrivingDistanceFromMatrixResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDrivingDistanceFromMatrixResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDrivingDistanceFromMatrixResponse proto.InternalMessageInfo

func (m *GetDrivingDistanceFromMatrixResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetDrivingDistanceFromMatrixResponse) GetCommonMatrixDistanceList() []*CommonMatrixDistance {
	if m != nil {
		return m.CommonMatrixDistanceList
	}
	return nil
}

type GetCoordinateByAddressRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	AddressL1            *string    `protobuf:"bytes,2,opt,name=address_l1,json=addressL1" json:"address_l1,omitempty"`
	AddressL2            *string    `protobuf:"bytes,3,opt,name=address_l2,json=addressL2" json:"address_l2,omitempty"`
	Address              *string    `protobuf:"bytes,4,opt,name=address" json:"address,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetCoordinateByAddressRequest) Reset()         { *m = GetCoordinateByAddressRequest{} }
func (m *GetCoordinateByAddressRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoordinateByAddressRequest) ProtoMessage()    {}
func (*GetCoordinateByAddressRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{54}
}

func (m *GetCoordinateByAddressRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoordinateByAddressRequest.Unmarshal(m, b)
}
func (m *GetCoordinateByAddressRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoordinateByAddressRequest.Marshal(b, m, deterministic)
}
func (m *GetCoordinateByAddressRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoordinateByAddressRequest.Merge(m, src)
}
func (m *GetCoordinateByAddressRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoordinateByAddressRequest.Size(m)
}
func (m *GetCoordinateByAddressRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoordinateByAddressRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoordinateByAddressRequest proto.InternalMessageInfo

func (m *GetCoordinateByAddressRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetCoordinateByAddressRequest) GetAddressL1() string {
	if m != nil && m.AddressL1 != nil {
		return *m.AddressL1
	}
	return ""
}

func (m *GetCoordinateByAddressRequest) GetAddressL2() string {
	if m != nil && m.AddressL2 != nil {
		return *m.AddressL2
	}
	return ""
}

func (m *GetCoordinateByAddressRequest) GetAddress() string {
	if m != nil && m.Address != nil {
		return *m.Address
	}
	return ""
}

type GetCoordinateByAddressResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Longitude            *string     `protobuf:"bytes,2,opt,name=longitude" json:"longitude,omitempty"`
	Latitude             *string     `protobuf:"bytes,3,opt,name=latitude" json:"latitude,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetCoordinateByAddressResponse) Reset()         { *m = GetCoordinateByAddressResponse{} }
func (m *GetCoordinateByAddressResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoordinateByAddressResponse) ProtoMessage()    {}
func (*GetCoordinateByAddressResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{55}
}

func (m *GetCoordinateByAddressResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoordinateByAddressResponse.Unmarshal(m, b)
}
func (m *GetCoordinateByAddressResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoordinateByAddressResponse.Marshal(b, m, deterministic)
}
func (m *GetCoordinateByAddressResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoordinateByAddressResponse.Merge(m, src)
}
func (m *GetCoordinateByAddressResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoordinateByAddressResponse.Size(m)
}
func (m *GetCoordinateByAddressResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoordinateByAddressResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoordinateByAddressResponse proto.InternalMessageInfo

func (m *GetCoordinateByAddressResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetCoordinateByAddressResponse) GetLongitude() string {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return ""
}

func (m *GetCoordinateByAddressResponse) GetLatitude() string {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return ""
}

type StationToAddressDrivingDistance struct {
	Address                                 *CommonAddress                         `protobuf:"bytes,1,opt,name=address" json:"address,omitempty"`
	Coordinate                              *CommonCoordinate                      `protobuf:"bytes,2,opt,name=coordinate" json:"coordinate,omitempty"`
	IsAddressNotExist                       *bool                                  `protobuf:"varint,3,opt,name=is_address_not_exist,json=isAddressNotExist" json:"is_address_not_exist,omitempty"`
	StationToAddressDrivingDistanceItemList []*StationToAddressDrivingDistanceItem `protobuf:"bytes,4,rep,name=station_to_address_driving_distance_item_list,json=stationToAddressDrivingDistanceItemList" json:"station_to_address_driving_distance_item_list,omitempty"`
	XXX_NoUnkeyedLiteral                    struct{}                               `json:"-"`
	XXX_unrecognized                        []byte                                 `json:"-"`
	XXX_sizecache                           int32                                  `json:"-"`
}

func (m *StationToAddressDrivingDistance) Reset()         { *m = StationToAddressDrivingDistance{} }
func (m *StationToAddressDrivingDistance) String() string { return proto.CompactTextString(m) }
func (*StationToAddressDrivingDistance) ProtoMessage()    {}
func (*StationToAddressDrivingDistance) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{56}
}

func (m *StationToAddressDrivingDistance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationToAddressDrivingDistance.Unmarshal(m, b)
}
func (m *StationToAddressDrivingDistance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationToAddressDrivingDistance.Marshal(b, m, deterministic)
}
func (m *StationToAddressDrivingDistance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationToAddressDrivingDistance.Merge(m, src)
}
func (m *StationToAddressDrivingDistance) XXX_Size() int {
	return xxx_messageInfo_StationToAddressDrivingDistance.Size(m)
}
func (m *StationToAddressDrivingDistance) XXX_DiscardUnknown() {
	xxx_messageInfo_StationToAddressDrivingDistance.DiscardUnknown(m)
}

var xxx_messageInfo_StationToAddressDrivingDistance proto.InternalMessageInfo

func (m *StationToAddressDrivingDistance) GetAddress() *CommonAddress {
	if m != nil {
		return m.Address
	}
	return nil
}

func (m *StationToAddressDrivingDistance) GetCoordinate() *CommonCoordinate {
	if m != nil {
		return m.Coordinate
	}
	return nil
}

func (m *StationToAddressDrivingDistance) GetIsAddressNotExist() bool {
	if m != nil && m.IsAddressNotExist != nil {
		return *m.IsAddressNotExist
	}
	return false
}

func (m *StationToAddressDrivingDistance) GetStationToAddressDrivingDistanceItemList() []*StationToAddressDrivingDistanceItem {
	if m != nil {
		return m.StationToAddressDrivingDistanceItemList
	}
	return nil
}

type CommonAddress struct {
	Address              *string  `protobuf:"bytes,1,opt,name=address" json:"address,omitempty"`
	AddressL1            *string  `protobuf:"bytes,2,opt,name=address_l1,json=addressL1" json:"address_l1,omitempty"`
	AddressL2            *string  `protobuf:"bytes,3,opt,name=address_l2,json=addressL2" json:"address_l2,omitempty"`
	AddressL3            *string  `protobuf:"bytes,4,opt,name=address_l3,json=addressL3" json:"address_l3,omitempty"`
	AddressL4            *string  `protobuf:"bytes,5,opt,name=address_l4,json=addressL4" json:"address_l4,omitempty"`
	AddressL5            *string  `protobuf:"bytes,6,opt,name=address_l5,json=addressL5" json:"address_l5,omitempty"`
	AddressL6            *string  `protobuf:"bytes,7,opt,name=address_l6,json=addressL6" json:"address_l6,omitempty"`
	AddressL7            *string  `protobuf:"bytes,8,opt,name=address_l7,json=addressL7" json:"address_l7,omitempty"`
	AddressL8            *string  `protobuf:"bytes,9,opt,name=address_l8,json=addressL8" json:"address_l8,omitempty"`
	Zipcode              *string  `protobuf:"bytes,10,opt,name=zipcode" json:"zipcode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonAddress) Reset()         { *m = CommonAddress{} }
func (m *CommonAddress) String() string { return proto.CompactTextString(m) }
func (*CommonAddress) ProtoMessage()    {}
func (*CommonAddress) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{57}
}

func (m *CommonAddress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonAddress.Unmarshal(m, b)
}
func (m *CommonAddress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonAddress.Marshal(b, m, deterministic)
}
func (m *CommonAddress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonAddress.Merge(m, src)
}
func (m *CommonAddress) XXX_Size() int {
	return xxx_messageInfo_CommonAddress.Size(m)
}
func (m *CommonAddress) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonAddress.DiscardUnknown(m)
}

var xxx_messageInfo_CommonAddress proto.InternalMessageInfo

func (m *CommonAddress) GetAddress() string {
	if m != nil && m.Address != nil {
		return *m.Address
	}
	return ""
}

func (m *CommonAddress) GetAddressL1() string {
	if m != nil && m.AddressL1 != nil {
		return *m.AddressL1
	}
	return ""
}

func (m *CommonAddress) GetAddressL2() string {
	if m != nil && m.AddressL2 != nil {
		return *m.AddressL2
	}
	return ""
}

func (m *CommonAddress) GetAddressL3() string {
	if m != nil && m.AddressL3 != nil {
		return *m.AddressL3
	}
	return ""
}

func (m *CommonAddress) GetAddressL4() string {
	if m != nil && m.AddressL4 != nil {
		return *m.AddressL4
	}
	return ""
}

func (m *CommonAddress) GetAddressL5() string {
	if m != nil && m.AddressL5 != nil {
		return *m.AddressL5
	}
	return ""
}

func (m *CommonAddress) GetAddressL6() string {
	if m != nil && m.AddressL6 != nil {
		return *m.AddressL6
	}
	return ""
}

func (m *CommonAddress) GetAddressL7() string {
	if m != nil && m.AddressL7 != nil {
		return *m.AddressL7
	}
	return ""
}

func (m *CommonAddress) GetAddressL8() string {
	if m != nil && m.AddressL8 != nil {
		return *m.AddressL8
	}
	return ""
}

func (m *CommonAddress) GetZipcode() string {
	if m != nil && m.Zipcode != nil {
		return *m.Zipcode
	}
	return ""
}

type CommonCoordinate struct {
	Lat                  *float32 `protobuf:"fixed32,1,opt,name=lat" json:"lat,omitempty"`
	Lng                  *float32 `protobuf:"fixed32,2,opt,name=lng" json:"lng,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonCoordinate) Reset()         { *m = CommonCoordinate{} }
func (m *CommonCoordinate) String() string { return proto.CompactTextString(m) }
func (*CommonCoordinate) ProtoMessage()    {}
func (*CommonCoordinate) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{58}
}

func (m *CommonCoordinate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonCoordinate.Unmarshal(m, b)
}
func (m *CommonCoordinate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonCoordinate.Marshal(b, m, deterministic)
}
func (m *CommonCoordinate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonCoordinate.Merge(m, src)
}
func (m *CommonCoordinate) XXX_Size() int {
	return xxx_messageInfo_CommonCoordinate.Size(m)
}
func (m *CommonCoordinate) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonCoordinate.DiscardUnknown(m)
}

var xxx_messageInfo_CommonCoordinate proto.InternalMessageInfo

func (m *CommonCoordinate) GetLat() float32 {
	if m != nil && m.Lat != nil {
		return *m.Lat
	}
	return 0
}

func (m *CommonCoordinate) GetLng() float32 {
	if m != nil && m.Lng != nil {
		return *m.Lng
	}
	return 0
}

type StationToAddressDrivingDistanceItem struct {
	StationId            *int64   `protobuf:"varint,1,opt,name=station_id,json=stationId" json:"station_id,omitempty"`
	DrivingDistance      *float32 `protobuf:"fixed32,2,opt,name=driving_distance,json=drivingDistance" json:"driving_distance,omitempty"`
	Version              *uint64  `protobuf:"varint,3,opt,name=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StationToAddressDrivingDistanceItem) Reset()         { *m = StationToAddressDrivingDistanceItem{} }
func (m *StationToAddressDrivingDistanceItem) String() string { return proto.CompactTextString(m) }
func (*StationToAddressDrivingDistanceItem) ProtoMessage()    {}
func (*StationToAddressDrivingDistanceItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{59}
}

func (m *StationToAddressDrivingDistanceItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationToAddressDrivingDistanceItem.Unmarshal(m, b)
}
func (m *StationToAddressDrivingDistanceItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationToAddressDrivingDistanceItem.Marshal(b, m, deterministic)
}
func (m *StationToAddressDrivingDistanceItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationToAddressDrivingDistanceItem.Merge(m, src)
}
func (m *StationToAddressDrivingDistanceItem) XXX_Size() int {
	return xxx_messageInfo_StationToAddressDrivingDistanceItem.Size(m)
}
func (m *StationToAddressDrivingDistanceItem) XXX_DiscardUnknown() {
	xxx_messageInfo_StationToAddressDrivingDistanceItem.DiscardUnknown(m)
}

var xxx_messageInfo_StationToAddressDrivingDistanceItem proto.InternalMessageInfo

func (m *StationToAddressDrivingDistanceItem) GetStationId() int64 {
	if m != nil && m.StationId != nil {
		return *m.StationId
	}
	return 0
}

func (m *StationToAddressDrivingDistanceItem) GetDrivingDistance() float32 {
	if m != nil && m.DrivingDistance != nil {
		return *m.DrivingDistance
	}
	return 0
}

func (m *StationToAddressDrivingDistanceItem) GetVersion() uint64 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

type AddressCoordinate struct {
	Address              *CommonAddress    `protobuf:"bytes,1,opt,name=address" json:"address,omitempty"`
	Coordinate           *CommonCoordinate `protobuf:"bytes,2,opt,name=coordinate" json:"coordinate,omitempty"`
	StandardAddress      *string           `protobuf:"bytes,3,opt,name=standard_address,json=standardAddress" json:"standard_address,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddressCoordinate) Reset()         { *m = AddressCoordinate{} }
func (m *AddressCoordinate) String() string { return proto.CompactTextString(m) }
func (*AddressCoordinate) ProtoMessage()    {}
func (*AddressCoordinate) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{60}
}

func (m *AddressCoordinate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddressCoordinate.Unmarshal(m, b)
}
func (m *AddressCoordinate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddressCoordinate.Marshal(b, m, deterministic)
}
func (m *AddressCoordinate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddressCoordinate.Merge(m, src)
}
func (m *AddressCoordinate) XXX_Size() int {
	return xxx_messageInfo_AddressCoordinate.Size(m)
}
func (m *AddressCoordinate) XXX_DiscardUnknown() {
	xxx_messageInfo_AddressCoordinate.DiscardUnknown(m)
}

var xxx_messageInfo_AddressCoordinate proto.InternalMessageInfo

func (m *AddressCoordinate) GetAddress() *CommonAddress {
	if m != nil {
		return m.Address
	}
	return nil
}

func (m *AddressCoordinate) GetCoordinate() *CommonCoordinate {
	if m != nil {
		return m.Coordinate
	}
	return nil
}

func (m *AddressCoordinate) GetStandardAddress() string {
	if m != nil && m.StandardAddress != nil {
		return *m.StandardAddress
	}
	return ""
}

type CenterPointWithRadius struct {
	CenterPoint          *CommonCoordinate `protobuf:"bytes,1,opt,name=center_point,json=centerPoint" json:"center_point,omitempty"`
	Radius               *float32          `protobuf:"fixed32,2,opt,name=radius" json:"radius,omitempty"`
	Address              *CommonAddress    `protobuf:"bytes,3,opt,name=address" json:"address,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CenterPointWithRadius) Reset()         { *m = CenterPointWithRadius{} }
func (m *CenterPointWithRadius) String() string { return proto.CompactTextString(m) }
func (*CenterPointWithRadius) ProtoMessage()    {}
func (*CenterPointWithRadius) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{61}
}

func (m *CenterPointWithRadius) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CenterPointWithRadius.Unmarshal(m, b)
}
func (m *CenterPointWithRadius) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CenterPointWithRadius.Marshal(b, m, deterministic)
}
func (m *CenterPointWithRadius) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CenterPointWithRadius.Merge(m, src)
}
func (m *CenterPointWithRadius) XXX_Size() int {
	return xxx_messageInfo_CenterPointWithRadius.Size(m)
}
func (m *CenterPointWithRadius) XXX_DiscardUnknown() {
	xxx_messageInfo_CenterPointWithRadius.DiscardUnknown(m)
}

var xxx_messageInfo_CenterPointWithRadius proto.InternalMessageInfo

func (m *CenterPointWithRadius) GetCenterPoint() *CommonCoordinate {
	if m != nil {
		return m.CenterPoint
	}
	return nil
}

func (m *CenterPointWithRadius) GetRadius() float32 {
	if m != nil && m.Radius != nil {
		return *m.Radius
	}
	return 0
}

func (m *CenterPointWithRadius) GetAddress() *CommonAddress {
	if m != nil {
		return m.Address
	}
	return nil
}

type NearbyStation struct {
	CenterPoint          *CommonCoordinate    `protobuf:"bytes,1,opt,name=center_point,json=centerPoint" json:"center_point,omitempty"`
	Radius               *float32             `protobuf:"fixed32,2,opt,name=radius" json:"radius,omitempty"`
	Address              *CommonAddress       `protobuf:"bytes,3,opt,name=address" json:"address,omitempty"`
	StationList          []*StationCoordinate `protobuf:"bytes,4,rep,name=station_list,json=stationList" json:"station_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *NearbyStation) Reset()         { *m = NearbyStation{} }
func (m *NearbyStation) String() string { return proto.CompactTextString(m) }
func (*NearbyStation) ProtoMessage()    {}
func (*NearbyStation) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{62}
}

func (m *NearbyStation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NearbyStation.Unmarshal(m, b)
}
func (m *NearbyStation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NearbyStation.Marshal(b, m, deterministic)
}
func (m *NearbyStation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NearbyStation.Merge(m, src)
}
func (m *NearbyStation) XXX_Size() int {
	return xxx_messageInfo_NearbyStation.Size(m)
}
func (m *NearbyStation) XXX_DiscardUnknown() {
	xxx_messageInfo_NearbyStation.DiscardUnknown(m)
}

var xxx_messageInfo_NearbyStation proto.InternalMessageInfo

func (m *NearbyStation) GetCenterPoint() *CommonCoordinate {
	if m != nil {
		return m.CenterPoint
	}
	return nil
}

func (m *NearbyStation) GetRadius() float32 {
	if m != nil && m.Radius != nil {
		return *m.Radius
	}
	return 0
}

func (m *NearbyStation) GetAddress() *CommonAddress {
	if m != nil {
		return m.Address
	}
	return nil
}

func (m *NearbyStation) GetStationList() []*StationCoordinate {
	if m != nil {
		return m.StationList
	}
	return nil
}

type StationCoordinate struct {
	StationId            *int64            `protobuf:"varint,1,opt,name=station_id,json=stationId" json:"station_id,omitempty"`
	Coordinate           *CommonCoordinate `protobuf:"bytes,2,opt,name=coordinate" json:"coordinate,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *StationCoordinate) Reset()         { *m = StationCoordinate{} }
func (m *StationCoordinate) String() string { return proto.CompactTextString(m) }
func (*StationCoordinate) ProtoMessage()    {}
func (*StationCoordinate) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{63}
}

func (m *StationCoordinate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationCoordinate.Unmarshal(m, b)
}
func (m *StationCoordinate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationCoordinate.Marshal(b, m, deterministic)
}
func (m *StationCoordinate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationCoordinate.Merge(m, src)
}
func (m *StationCoordinate) XXX_Size() int {
	return xxx_messageInfo_StationCoordinate.Size(m)
}
func (m *StationCoordinate) XXX_DiscardUnknown() {
	xxx_messageInfo_StationCoordinate.DiscardUnknown(m)
}

var xxx_messageInfo_StationCoordinate proto.InternalMessageInfo

func (m *StationCoordinate) GetStationId() int64 {
	if m != nil && m.StationId != nil {
		return *m.StationId
	}
	return 0
}

func (m *StationCoordinate) GetCoordinate() *CommonCoordinate {
	if m != nil {
		return m.Coordinate
	}
	return nil
}

type CommonMatrixDistance struct {
	StartPoint           *CommonCoordinate `protobuf:"bytes,1,opt,name=start_point,json=startPoint" json:"start_point,omitempty"`
	EndPoint             *CommonCoordinate `protobuf:"bytes,2,opt,name=end_point,json=endPoint" json:"end_point,omitempty"`
	IsNoResult           *bool             `protobuf:"varint,3,opt,name=is_no_result,json=isNoResult" json:"is_no_result,omitempty"`
	Distance             *float32          `protobuf:"fixed32,4,opt,name=distance" json:"distance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CommonMatrixDistance) Reset()         { *m = CommonMatrixDistance{} }
func (m *CommonMatrixDistance) String() string { return proto.CompactTextString(m) }
func (*CommonMatrixDistance) ProtoMessage()    {}
func (*CommonMatrixDistance) Descriptor() ([]byte, []int) {
	return fileDescriptor_ffd9ac4d1c9569c0, []int{64}
}

func (m *CommonMatrixDistance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonMatrixDistance.Unmarshal(m, b)
}
func (m *CommonMatrixDistance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonMatrixDistance.Marshal(b, m, deterministic)
}
func (m *CommonMatrixDistance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonMatrixDistance.Merge(m, src)
}
func (m *CommonMatrixDistance) XXX_Size() int {
	return xxx_messageInfo_CommonMatrixDistance.Size(m)
}
func (m *CommonMatrixDistance) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonMatrixDistance.DiscardUnknown(m)
}

var xxx_messageInfo_CommonMatrixDistance proto.InternalMessageInfo

func (m *CommonMatrixDistance) GetStartPoint() *CommonCoordinate {
	if m != nil {
		return m.StartPoint
	}
	return nil
}

func (m *CommonMatrixDistance) GetEndPoint() *CommonCoordinate {
	if m != nil {
		return m.EndPoint
	}
	return nil
}

func (m *CommonMatrixDistance) GetIsNoResult() bool {
	if m != nil && m.IsNoResult != nil {
		return *m.IsNoResult
	}
	return false
}

func (m *CommonMatrixDistance) GetDistance() float32 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

func init() {
	proto.RegisterEnum("lcos_protobuf.SubStatusEnum", SubStatusEnum_name, SubStatusEnum_value)
	proto.RegisterType((*GetSyncBranchSupplyTypeRequest)(nil), "lcos_protobuf.GetSyncBranchSupplyTypeRequest")
	proto.RegisterType((*GetSyncBranchSupplyTypeResponse)(nil), "lcos_protobuf.GetSyncBranchSupplyTypeResponse")
	proto.RegisterType((*SyncBranchInfoRequest)(nil), "lcos_protobuf.SyncBranchInfoRequest")
	proto.RegisterType((*SyncBranchInfoResponse)(nil), "lcos_protobuf.SyncBranchInfoResponse")
	proto.RegisterType((*GetBranchInfoRequest)(nil), "lcos_protobuf.GetBranchInfoRequest")
	proto.RegisterType((*GetBranchInfoResponse)(nil), "lcos_protobuf.GetBranchInfoResponse")
	proto.RegisterType((*BatchGetBranchInfoByBranchIdRequest)(nil), "lcos_protobuf.BatchGetBranchInfoByBranchIdRequest")
	proto.RegisterType((*BatchGetBranchInfoByBranchIdResponse)(nil), "lcos_protobuf.BatchGetBranchInfoByBranchIdResponse")
	proto.RegisterType((*GetBranchInfoByBranchIdRequest)(nil), "lcos_protobuf.getBranchInfoByBranchIdRequest")
	proto.RegisterType((*BatchGetBranchInfoByBranchRefRequest)(nil), "lcos_protobuf.BatchGetBranchInfoByBranchRefRequest")
	proto.RegisterType((*BatchGetBranchInfoByBranchRefResponse)(nil), "lcos_protobuf.BatchGetBranchInfoByBranchRefResponse")
	proto.RegisterType((*GetBranchInfoByBranchRefRequest)(nil), "lcos_protobuf.getBranchInfoByBranchRefRequest")
	proto.RegisterType((*SingleBranchInfo)(nil), "lcos_protobuf.SingleBranchInfo")
	proto.RegisterType((*BranchInfo)(nil), "lcos_protobuf.BranchInfo")
	proto.RegisterType((*FullBranchInfo)(nil), "lcos_protobuf.FullBranchInfo")
	proto.RegisterType((*BranchInfoExtraData)(nil), "lcos_protobuf.BranchInfoExtraData")
	proto.RegisterType((*OpenHour)(nil), "lcos_protobuf.OpenHour")
	proto.RegisterType((*GetBranchInfoByLocationIDRequest)(nil), "lcos_protobuf.GetBranchInfoByLocationIDRequest")
	proto.RegisterType((*GetBranchInfoByLocationIDResponse)(nil), "lcos_protobuf.GetBranchInfoByLocationIDResponse")
	proto.RegisterType((*GetBranchSubLocationsRequest)(nil), "lcos_protobuf.GetBranchSubLocationsRequest")
	proto.RegisterType((*GetBranchSubLocationsResponse)(nil), "lcos_protobuf.GetBranchSubLocationsResponse")
	proto.RegisterType((*LocationsIdMap)(nil), "lcos_protobuf.LocationsIdMap")
	proto.RegisterType((*OpsType)(nil), "lcos_protobuf.OpsType")
	proto.RegisterType((*SearchBranchRequest)(nil), "lcos_protobuf.SearchBranchRequest")
	proto.RegisterType((*SearchBranchForGoogleMapsRequest)(nil), "lcos_protobuf.SearchBranchForGoogleMapsRequest")
	proto.RegisterType((*SearchBranchResponse)(nil), "lcos_protobuf.SearchBranchResponse")
	proto.RegisterMapType((map[uint64]float64)(nil), "lcos_protobuf.SearchBranchResponse.DistanceEntry")
	proto.RegisterType((*SearchBranchForGoogleMapsResponse)(nil), "lcos_protobuf.SearchBranchForGoogleMapsResponse")
	proto.RegisterMapType((map[uint64]float64)(nil), "lcos_protobuf.SearchBranchForGoogleMapsResponse.DistanceEntry")
	proto.RegisterType((*GetBranchListByBranchGroupIDRequest)(nil), "lcos_protobuf.GetBranchListByBranchGroupIDRequest")
	proto.RegisterType((*GetBranchListByBranchGroupIDResponse)(nil), "lcos_protobuf.GetBranchListByBranchGroupIDResponse")
	proto.RegisterType((*SearchDropoffBranchRequest)(nil), "lcos_protobuf.SearchDropoffBranchRequest")
	proto.RegisterType((*BatchSearchDropoffBranchRequest)(nil), "lcos_protobuf.BatchSearchDropoffBranchRequest")
	proto.RegisterType((*BatchSearchDropoffBranchResponse)(nil), "lcos_protobuf.BatchSearchDropoffBranchResponse")
	proto.RegisterMapType((map[string]*SearchBranchResponse)(nil), "lcos_protobuf.BatchSearchDropoffBranchResponse.BranchResultMapEntry")
	proto.RegisterType((*GetSpxStationIdInfoByOrderIdRequest)(nil), "lcos_protobuf.GetSpxStationIdInfoByOrderIdRequest")
	proto.RegisterType((*GetSpxStationIdInfoByOrderIdResponse)(nil), "lcos_protobuf.GetSpxStationIdInfoByOrderIdResponse")
	proto.RegisterType((*CheckHDAbilityRequest)(nil), "lcos_protobuf.CheckHDAbilityRequest")
	proto.RegisterType((*CheckHDAbilityAddressItem)(nil), "lcos_protobuf.CheckHDAbilityAddressItem")
	proto.RegisterType((*CheckHDAbilityResponse)(nil), "lcos_protobuf.CheckHDAbilityResponse")
	proto.RegisterType((*CheckHDAbilityAddressResItem)(nil), "lcos_protobuf.CheckHDAbilityAddressResItem")
	proto.RegisterType((*GetHDStationRequest)(nil), "lcos_protobuf.GetHDStationRequest")
	proto.RegisterType((*GetHDStationAddressItem)(nil), "lcos_protobuf.GetHDStationAddressItem")
	proto.RegisterType((*GetHDStationResponse)(nil), "lcos_protobuf.GetHDStationResponse")
	proto.RegisterType((*GetHDStationAddressResItem)(nil), "lcos_protobuf.GetHDStationAddressResItem")
	proto.RegisterType((*QueryCacheValItem)(nil), "lcos_protobuf.QueryCacheValItem")
	proto.RegisterType((*QueryCacheValReq)(nil), "lcos_protobuf.QueryCacheValReq")
	proto.RegisterType((*QueryCacheValRespItem)(nil), "lcos_protobuf.QueryCacheValRespItem")
	proto.RegisterType((*QueryCacheValResp)(nil), "lcos_protobuf.QueryCacheValResp")
	proto.RegisterType((*GetDrivingDistanceWithHDStationRequest)(nil), "lcos_protobuf.GetDrivingDistanceWithHDStationRequest")
	proto.RegisterType((*GetDrivingDistanceWithHDStationResponse)(nil), "lcos_protobuf.GetDrivingDistanceWithHDStationResponse")
	proto.RegisterType((*GetBuyerAddressCoordinateRequest)(nil), "lcos_protobuf.GetBuyerAddressCoordinateRequest")
	proto.RegisterType((*GetBuyerAddressCoordinateResponse)(nil), "lcos_protobuf.GetBuyerAddressCoordinateResponse")
	proto.RegisterType((*SearchNearbyStationRequest)(nil), "lcos_protobuf.SearchNearbyStationRequest")
	proto.RegisterType((*SearchNearbyStationResponse)(nil), "lcos_protobuf.SearchNearbyStationResponse")
	proto.RegisterType((*GetDrivingDistanceFromMatrixRequest)(nil), "lcos_protobuf.GetDrivingDistanceFromMatrixRequest")
	proto.RegisterType((*GetDrivingDistanceFromMatrixResponse)(nil), "lcos_protobuf.GetDrivingDistanceFromMatrixResponse")
	proto.RegisterType((*GetCoordinateByAddressRequest)(nil), "lcos_protobuf.GetCoordinateByAddressRequest")
	proto.RegisterType((*GetCoordinateByAddressResponse)(nil), "lcos_protobuf.GetCoordinateByAddressResponse")
	proto.RegisterType((*StationToAddressDrivingDistance)(nil), "lcos_protobuf.StationToAddressDrivingDistance")
	proto.RegisterType((*CommonAddress)(nil), "lcos_protobuf.CommonAddress")
	proto.RegisterType((*CommonCoordinate)(nil), "lcos_protobuf.CommonCoordinate")
	proto.RegisterType((*StationToAddressDrivingDistanceItem)(nil), "lcos_protobuf.StationToAddressDrivingDistanceItem")
	proto.RegisterType((*AddressCoordinate)(nil), "lcos_protobuf.AddressCoordinate")
	proto.RegisterType((*CenterPointWithRadius)(nil), "lcos_protobuf.CenterPointWithRadius")
	proto.RegisterType((*NearbyStation)(nil), "lcos_protobuf.NearbyStation")
	proto.RegisterType((*StationCoordinate)(nil), "lcos_protobuf.StationCoordinate")
	proto.RegisterType((*CommonMatrixDistance)(nil), "lcos_protobuf.CommonMatrixDistance")
}

func init() {
	proto.RegisterFile("lcos_branch.proto", fileDescriptor_ffd9ac4d1c9569c0)
}

var fileDescriptor_ffd9ac4d1c9569c0 = []byte{
	// 3750 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x3b, 0x5d, 0x6f, 0x1b, 0x57,
	0x76, 0x1a, 0x92, 0xb2, 0xc4, 0x43, 0x51, 0xa2, 0xae, 0x3e, 0x4c, 0xd3, 0x1f, 0xa2, 0x47, 0x76,
	0x22, 0x27, 0xb1, 0x12, 0xcb, 0x8e, 0xed, 0x3a, 0x45, 0x52, 0x5b, 0xb2, 0x25, 0xa1, 0xf2, 0x47,
	0x46, 0x6e, 0x9c, 0x06, 0x05, 0x06, 0x43, 0xce, 0x15, 0x35, 0x35, 0x39, 0x33, 0x9e, 0x19, 0xca,
	0xa6, 0x81, 0x00, 0x69, 0xd1, 0x16, 0x08, 0x5a, 0x34, 0x68, 0x50, 0xb4, 0x7d, 0xec, 0x43, 0x81,
	0x3c, 0xf4, 0x25, 0x0f, 0x05, 0xd2, 0xa0, 0x2d, 0xd0, 0xdd, 0x87, 0xc5, 0x02, 0x0b, 0x04, 0x58,
	0x60, 0xf3, 0xb0, 0xfb, 0xb4, 0xfb, 0xb0, 0xc0, 0xbe, 0x2c, 0xb0, 0x3f, 0x61, 0x71, 0xcf, 0xbd,
	0x33, 0x9c, 0x19, 0x0e, 0x87, 0xd4, 0x8a, 0x46, 0xb2, 0xd8, 0xbc, 0xcd, 0xbd, 0xe7, 0x7e, 0x9c,
	0x7b, 0xbe, 0xcf, 0xb9, 0x77, 0x60, 0xb6, 0x59, 0xb7, 0x5c, 0xb5, 0xe6, 0x68, 0x66, 0x7d, 0x7f,
	0xd5, 0x76, 0x2c, 0xcf, 0x22, 0x45, 0xec, 0xc2, 0xef, 0x5a, 0x7b, 0xaf, 0x32, 0xc3, 0x47, 0x68,
	0x2e, 0xe5, 0x70, 0xf9, 0x09, 0x9c, 0xd9, 0xa4, 0xde, 0x6e, 0xc7, 0xac, 0xdf, 0xc2, 0x69, 0xbb,
	0x6d, 0xdb, 0x6e, 0x76, 0x1e, 0x76, 0x6c, 0xaa, 0xd0, 0x27, 0x6d, 0xea, 0x7a, 0xe4, 0x1a, 0x80,
	0x43, 0x9f, 0xa8, 0xfb, 0x54, 0xd3, 0xa9, 0x53, 0x96, 0xaa, 0x99, 0x95, 0xc2, 0x5a, 0x79, 0x35,
	0xb2, 0xec, 0xaa, 0x42, 0x9f, 0x6c, 0x21, 0x5c, 0xc9, 0x3b, 0xfe, 0x27, 0x59, 0x84, 0x63, 0x0e,
	0x6d, 0x18, 0x96, 0x59, 0xce, 0x54, 0x33, 0x2b, 0x79, 0x45, 0xb4, 0xe4, 0xbf, 0x97, 0x60, 0xa9,
	0xef, 0x9e, 0xae, 0x6d, 0x99, 0x2e, 0x25, 0x37, 0xa0, 0xe0, 0x50, 0xd7, 0x8e, 0xee, 0x7a, 0xa2,
	0x67, 0x57, 0xd7, 0x16, 0xdb, 0x82, 0x13, 0x7c, 0x93, 0xd7, 0x80, 0x70, 0x12, 0xa8, 0x2e, 0x2e,
	0xac, 0x7a, 0x1d, 0x9b, 0x96, 0x33, 0xd5, 0xec, 0x4a, 0x51, 0x29, 0xd5, 0x62, 0x3b, 0xca, 0xff,
	0x2f, 0xc1, 0x42, 0x17, 0x95, 0x6d, 0x73, 0xcf, 0x3a, 0xf2, 0xc1, 0xfb, 0x21, 0x90, 0x49, 0x42,
	0x80, 0xac, 0x40, 0xc9, 0xa1, 0x2d, 0xcb, 0xa3, 0xea, 0x9e, 0xd1, 0xa4, 0xaa, 0xad, 0x79, 0xfb,
	0xe5, 0x2c, 0x12, 0x6c, 0x9a, 0xf7, 0xdf, 0x31, 0x9a, 0xf4, 0x81, 0xe6, 0xed, 0x87, 0x08, 0x9a,
	0x8b, 0x10, 0xf4, 0x21, 0x2c, 0xc6, 0x4f, 0x70, 0x74, 0x32, 0xca, 0xff, 0x2c, 0xc1, 0xfc, 0x26,
	0xf5, 0x46, 0x48, 0x97, 0x93, 0x90, 0x17, 0x74, 0x31, 0x74, 0x24, 0x47, 0x4e, 0x99, 0xe4, 0x1d,
	0xdb, 0x3a, 0x79, 0x09, 0x66, 0x04, 0xb0, 0xe1, 0x58, 0x6d, 0x9b, 0x0d, 0xc9, 0x22, 0xcb, 0x8a,
	0xbc, 0x7b, 0x93, 0xf5, 0x6e, 0xeb, 0xf2, 0x27, 0x12, 0x2c, 0xc4, 0xd0, 0x1a, 0x81, 0xcc, 0xdc,
	0x80, 0x82, 0x8f, 0x9a, 0xb9, 0x67, 0x95, 0x33, 0x55, 0x29, 0x61, 0x6e, 0x68, 0x4f, 0xa8, 0x05,
	0xdf, 0xf2, 0x17, 0x12, 0x2c, 0xdf, 0xd2, 0xbc, 0xfa, 0x7e, 0x04, 0xad, 0x5b, 0x1d, 0xf1, 0xad,
	0x1f, 0x99, 0x6e, 0x8f, 0xa0, 0x14, 0x42, 0x4e, 0x6d, 0x1a, 0xae, 0x87, 0xe2, 0x5c, 0x58, 0xbb,
	0x18, 0x9b, 0xde, 0x48, 0xc5, 0x40, 0x99, 0xee, 0x62, 0xbd, 0x63, 0xb8, 0x9e, 0xfc, 0xaf, 0x12,
	0x9c, 0x4b, 0xc7, 0x7c, 0x04, 0xa4, 0xbd, 0x0c, 0x39, 0x5d, 0xf3, 0x34, 0x81, 0xf1, 0x52, 0x6c,
	0xd2, 0xae, 0x61, 0x36, 0x9a, 0x34, 0x44, 0x59, 0x1c, 0x2c, 0x7f, 0x08, 0x67, 0xd2, 0xcf, 0xc2,
	0x84, 0xa9, 0x6d, 0x1a, 0x4f, 0xda, 0x94, 0x49, 0x8a, 0x84, 0xfa, 0x30, 0xc9, 0x3b, 0xb6, 0xf5,
	0x74, 0x49, 0x3b, 0x0b, 0x53, 0x61, 0x49, 0x2b, 0x67, 0xab, 0xd2, 0x4a, 0x51, 0x29, 0x84, 0xc4,
	0x4c, 0xfe, 0x32, 0x95, 0x30, 0x0a, 0xdd, 0x3b, 0x32, 0x4f, 0xdf, 0xef, 0xcb, 0xd3, 0xd5, 0x61,
	0x78, 0xda, 0x45, 0xa1, 0x87, 0xa9, 0xff, 0x26, 0xc1, 0xf9, 0x01, 0xb8, 0x7f, 0x53, 0x5c, 0xfd,
	0x48, 0x82, 0xa5, 0x01, 0xc7, 0x49, 0xe7, 0xeb, 0x69, 0x10, 0x8a, 0xa7, 0x3a, 0x74, 0x4f, 0xb8,
	0x15, 0xc1, 0x69, 0x85, 0xee, 0x25, 0x70, 0x36, 0x13, 0xe7, 0xec, 0x8f, 0xc6, 0xa1, 0x14, 0xc7,
	0x2e, 0x7d, 0xcf, 0x13, 0x30, 0x49, 0x1d, 0x47, 0xad, 0x5b, 0x3a, 0x45, 0xbb, 0x30, 0xae, 0x4c,
	0x50, 0xc7, 0x59, 0xb7, 0x74, 0x4a, 0xca, 0x30, 0xd1, 0xa2, 0xae, 0xab, 0x35, 0x28, 0x0a, 0x51,
	0x5e, 0xf1, 0x9b, 0x64, 0x1a, 0x32, 0x86, 0x5e, 0xce, 0x55, 0xa5, 0x95, 0x9c, 0x92, 0x31, 0x7a,
	0x65, 0x6e, 0xbc, 0x47, 0xe6, 0xc8, 0x52, 0x60, 0x82, 0xd0, 0x5d, 0x1c, 0xc3, 0x11, 0xe2, 0xb8,
	0xe8, 0x28, 0xa2, 0x87, 0x9f, 0xc0, 0x0d, 0x43, 0x87, 0xef, 0xce, 0x37, 0xb5, 0x16, 0x2d, 0x4f,
	0x22, 0x5c, 0xcc, 0xb8, 0xa7, 0xb5, 0x28, 0x39, 0x0f, 0x42, 0x54, 0x54, 0x4d, 0xd7, 0x1d, 0xea,
	0xba, 0xe5, 0x3c, 0x8e, 0x11, 0x06, 0xf6, 0x26, 0xef, 0x64, 0x5e, 0xc6, 0xf5, 0x34, 0xaf, 0xed,
	0x96, 0x01, 0x51, 0x10, 0x2d, 0xb6, 0x3d, 0x7d, 0xe6, 0x39, 0x9a, 0x8a, 0x7c, 0x2f, 0xf0, 0xed,
	0xb1, 0x67, 0x43, 0xf3, 0x34, 0xb6, 0xbd, 0x6d, 0xb9, 0x9e, 0xd6, 0xe4, 0x94, 0x9a, 0xe2, 0xdb,
	0xf3, 0x2e, 0x24, 0x56, 0x05, 0x26, 0x9b, 0x9a, 0x67, 0x78, 0x6d, 0x9d, 0x96, 0x8b, 0x08, 0x0d,
	0xda, 0xe4, 0x14, 0xe4, 0x9b, 0x96, 0xd9, 0xe0, 0xc0, 0x69, 0xbe, 0x74, 0xd0, 0xc1, 0x96, 0xae,
	0x3b, 0x54, 0xf3, 0xa8, 0xea, 0x19, 0x2d, 0x5a, 0x9e, 0xe1, 0x94, 0xe1, 0x5d, 0x0f, 0x8d, 0x16,
	0x0e, 0x68, 0xdb, 0x7a, 0x30, 0xa0, 0xc4, 0x07, 0xf0, 0x2e, 0x1c, 0x50, 0x86, 0x89, 0xba, 0xd5,
	0x36, 0x3d, 0xa7, 0x53, 0x9e, 0xe5, 0x8c, 0x12, 0x4d, 0x32, 0x0f, 0xe3, 0xec, 0x7c, 0xb4, 0x4c,
	0xb0, 0x9f, 0x37, 0x08, 0x81, 0x5c, 0xdd, 0xf0, 0x3a, 0xe5, 0x39, 0xec, 0xc4, 0x6f, 0x86, 0xbf,
	0x6e, 0xb8, 0x9e, 0x63, 0xd4, 0xbd, 0xf2, 0x3c, 0xc7, 0xdf, 0x6f, 0x73, 0x9a, 0x39, 0x94, 0x7a,
	0xe5, 0x05, 0x84, 0x88, 0x56, 0x88, 0xed, 0xf6, 0xbe, 0x65, 0xd2, 0xf2, 0x22, 0x42, 0x05, 0x9f,
	0x1e, 0xb0, 0x2e, 0xf2, 0x16, 0x80, 0xdb, 0xae, 0xa9, 0x82, 0xe4, 0xc7, 0xab, 0xd2, 0xca, 0xf4,
	0xda, 0xa9, 0xb8, 0x3a, 0xb5, 0x6b, 0xbb, 0x08, 0xbf, 0x6d, 0xb6, 0x5b, 0x4a, 0xde, 0xf5, 0x9b,
	0xf2, 0xd7, 0xc7, 0x00, 0xa2, 0x72, 0xdc, 0x35, 0x7b, 0x52, 0xcc, 0xec, 0xc5, 0xe4, 0x83, 0x2b,
	0x4f, 0x58, 0x3e, 0x96, 0xa0, 0xd0, 0xb4, 0xea, 0x9a, 0x67, 0x58, 0x26, 0xf7, 0xbe, 0x6c, 0x3e,
	0xf8, 0x5d, 0xdb, 0x3a, 0x79, 0x03, 0xe6, 0x83, 0x01, 0xba, 0x71, 0x60, 0xb8, 0x62, 0x64, 0x0e,
	0x47, 0x12, 0x1f, 0xb6, 0x21, 0x40, 0x91, 0x3d, 0x51, 0xa6, 0xc7, 0x51, 0x1f, 0xc3, 0x32, 0x7d,
	0x06, 0x84, 0x88, 0xa0, 0xd0, 0x1c, 0xe3, 0x38, 0x75, 0x7b, 0xa2, 0x82, 0x31, 0xc1, 0xf5, 0xbd,
	0x2b, 0x18, 0x61, 0x91, 0x9a, 0xe4, 0x6a, 0x1b, 0x88, 0xd4, 0x79, 0x98, 0xd6, 0xa9, 0xa7, 0x19,
	0xcd, 0x90, 0xb4, 0xb3, 0x11, 0x45, 0xde, 0xeb, 0x4b, 0xfb, 0x15, 0xc8, 0x5b, 0x36, 0x35, 0xd5,
	0x7d, 0xab, 0xed, 0x94, 0x01, 0x8d, 0xd9, 0xf1, 0x18, 0xf5, 0xef, 0xdb, 0xd4, 0xdc, 0xb2, 0xda,
	0x8e, 0x32, 0x69, 0x89, 0xaf, 0x98, 0x2a, 0x16, 0xe2, 0x76, 0x68, 0x19, 0x8a, 0x7e, 0x00, 0xc8,
	0xd9, 0x3a, 0x85, 0x12, 0x29, 0x64, 0x81, 0xf3, 0x8e, 0x3c, 0x82, 0xc5, 0xb0, 0x07, 0x08, 0xe9,
	0x56, 0x11, 0xa3, 0x0f, 0xb9, 0x6f, 0xf4, 0x71, 0xdb, 0x57, 0x3a, 0x65, 0xae, 0xd6, 0xdb, 0xc9,
	0x84, 0x8e, 0x49, 0x54, 0x20, 0xac, 0x5c, 0x9f, 0x0a, 0x6e, 0xbb, 0xb6, 0xe1, 0xcb, 0x6b, 0x5c,
	0x2e, 0x67, 0x7a, 0xe5, 0xf2, 0x1a, 0x94, 0x5b, 0xda, 0x33, 0xd5, 0xd6, 0x9c, 0x3a, 0x6d, 0xb2,
	0x73, 0x74, 0x54, 0xbd, 0xed, 0x20, 0x83, 0x51, 0xc1, 0xc6, 0x95, 0x85, 0x96, 0xf6, 0xec, 0x01,
	0x82, 0x77, 0x3d, 0xad, 0xb3, 0x21, 0x80, 0x21, 0x9e, 0x23, 0x4f, 0x67, 0xc3, 0x76, 0x08, 0x0d,
	0x41, 0x37, 0x8c, 0xe5, 0x3a, 0x27, 0x5a, 0xcc, 0xd0, 0x5a, 0xb6, 0xcb, 0x25, 0x65, 0xae, 0x9a,
	0x65, 0x5a, 0x6a, 0xd9, 0x2e, 0x8a, 0xc9, 0xa5, 0xb0, 0xe4, 0x51, 0xb7, 0xee, 0x18, 0x36, 0x22,
	0xc2, 0xf5, 0x70, 0x2e, 0x90, 0xbc, 0x2e, 0x28, 0xa6, 0x57, 0x0b, 0x87, 0xd3, 0xab, 0xff, 0x3d,
	0x06, 0xd3, 0x77, 0xda, 0xcd, 0xe6, 0x77, 0xba, 0xf5, 0x9d, 0x6e, 0xfd, 0x5e, 0xe9, 0xd6, 0x8b,
	0x75, 0x67, 0x09, 0x39, 0xda, 0x22, 0x52, 0x3f, 0x96, 0xa3, 0xdd, 0x86, 0xb9, 0x04, 0x8a, 0xb2,
	0x65, 0x37, 0xd6, 0xef, 0xdd, 0xbc, 0x7b, 0xbb, 0x2c, 0xf1, 0x65, 0x79, 0x8b, 0xf7, 0xaf, 0xdf,
	0xdf, 0xb8, 0x8d, 0xf1, 0x15, 0xf6, 0xb3, 0x96, 0xfc, 0x57, 0x12, 0x4c, 0xde, 0x0f, 0x89, 0x85,
	0xeb, 0x69, 0x8e, 0xc7, 0x5d, 0x3c, 0x0f, 0xd2, 0xf2, 0xd8, 0x83, 0x1e, 0x9e, 0x45, 0x69, 0xa6,
	0xce, 0x81, 0x5c, 0xfd, 0x26, 0xa8, 0xa9, 0x23, 0xe8, 0x0c, 0x14, 0x74, 0xad, 0xa3, 0x5a, 0x7b,
	0xea, 0x53, 0x4a, 0x1f, 0x8b, 0xac, 0x32, 0xaf, 0x6b, 0x9d, 0xfb, 0x7b, 0x8f, 0x28, 0x7d, 0xcc,
	0x34, 0x9b, 0x4d, 0x53, 0x9f, 0x33, 0x6e, 0xf1, 0xcc, 0x7a, 0x92, 0x75, 0x7c, 0x60, 0x99, 0x54,
	0xfe, 0x85, 0x04, 0xd5, 0x58, 0x20, 0xbd, 0xe3, 0x6b, 0xed, 0xc6, 0x91, 0xb3, 0x80, 0x04, 0x82,
	0xf2, 0x32, 0x41, 0x94, 0xa0, 0x83, 0xcd, 0x47, 0xd4, 0xda, 0xe5, 0xaa, 0xd9, 0xc3, 0x58, 0xbb,
	0x7f, 0x94, 0xe0, 0x6c, 0xca, 0x19, 0x47, 0x90, 0x2d, 0x5c, 0x8c, 0x64, 0x0b, 0x29, 0x79, 0x35,
	0xcf, 0x13, 0xfe, 0x5d, 0x82, 0x53, 0x01, 0x42, 0xbb, 0xed, 0x9a, 0x8f, 0x8d, 0xfb, 0xad, 0x21,
	0xb8, 0xfc, 0x1f, 0x12, 0x9c, 0xee, 0x83, 0xe2, 0x08, 0xe8, 0xb5, 0x09, 0x25, 0x7f, 0x2f, 0x57,
	0x35, 0x74, 0xb5, 0xa5, 0xd9, 0xa2, 0x26, 0x71, 0x3a, 0xb6, 0x40, 0xb0, 0xef, 0xb6, 0x7e, 0x57,
	0xb3, 0x95, 0xe9, 0x66, 0xa4, 0x2d, 0x7f, 0x25, 0xc1, 0x74, 0x74, 0x08, 0x79, 0x0d, 0x08, 0x5a,
	0x05, 0x35, 0xbc, 0x43, 0x59, 0xaa, 0x66, 0x57, 0x72, 0x4a, 0x09, 0x21, 0xa1, 0x09, 0xe4, 0x15,
	0x98, 0x65, 0xe6, 0x22, 0x3a, 0x38, 0x83, 0x83, 0x67, 0x18, 0x20, 0x3c, 0x76, 0x0d, 0x16, 0x7c,
	0x13, 0x12, 0x1d, 0x9f, 0xc5, 0xf1, 0x73, 0x3e, 0x30, 0x3c, 0x67, 0x15, 0xe6, 0xb8, 0x71, 0x89,
	0xce, 0xc8, 0xe1, 0x8c, 0x59, 0x0e, 0x0a, 0x8d, 0x97, 0xcf, 0xc1, 0xc4, 0x7d, 0x11, 0x14, 0x84,
	0xe3, 0x05, 0x29, 0x12, 0x2f, 0xc8, 0xbf, 0xc9, 0xc2, 0xdc, 0x2e, 0xd5, 0x9c, 0xfa, 0xbe, 0x9f,
	0x5f, 0x26, 0xcb, 0x8d, 0x74, 0x24, 0xb9, 0xe9, 0xb5, 0x7c, 0x2c, 0xd1, 0x78, 0x4c, 0x3b, 0x4f,
	0x2d, 0x47, 0xf7, 0x33, 0x42, 0xd1, 0x64, 0x56, 0x66, 0xcf, 0x60, 0x16, 0x8a, 0xa1, 0x9b, 0xc3,
	0xb9, 0x93, 0xac, 0x03, 0x8f, 0xd2, 0x0d, 0x89, 0xc6, 0x23, 0x21, 0x11, 0x81, 0x9c, 0x6b, 0x3c,
	0xe7, 0xc9, 0xe0, 0xb8, 0x82, 0xdf, 0x8c, 0x7f, 0x81, 0x68, 0xba, 0x16, 0x33, 0x88, 0x6c, 0xc5,
	0x09, 0x5c, 0x31, 0x90, 0x9a, 0x5d, 0xcb, 0xf1, 0x70, 0xe5, 0x88, 0x93, 0x9f, 0x8c, 0x67, 0x56,
	0x61, 0x27, 0x9f, 0x8f, 0xe5, 0x64, 0x65, 0x98, 0x78, 0x6e, 0xd8, 0xe8, 0x67, 0x80, 0x1f, 0x45,
	0x34, 0xc9, 0x0d, 0x28, 0xfa, 0x84, 0xe7, 0x85, 0x8b, 0x02, 0xaa, 0xf5, 0x62, 0x8f, 0x6f, 0x47,
	0x66, 0x28, 0x05, 0xc1, 0x95, 0x1d, 0xc3, 0xf5, 0x7c, 0xb7, 0xa3, 0x99, 0x75, 0x2a, 0x3c, 0x77,
	0xd0, 0x8e, 0x19, 0xb1, 0xe2, 0xe1, 0x8c, 0xd8, 0xdf, 0x64, 0xa1, 0x1a, 0x66, 0xf9, 0x1d, 0xcb,
	0xd9, 0xb4, 0xac, 0x46, 0x93, 0xde, 0xd5, 0x6c, 0xf7, 0xc5, 0xf0, 0x3f, 0xfb, 0x07, 0xca, 0xff,
	0x30, 0x0f, 0x0b, 0x51, 0x1e, 0xca, 0x9f, 0x67, 0x60, 0x3e, 0xaa, 0x79, 0xfd, 0xcc, 0xa1, 0x34,
	0xbc, 0x39, 0x9c, 0x87, 0x71, 0xcc, 0xd7, 0x85, 0xce, 0xf1, 0x46, 0xbc, 0x66, 0x9b, 0x1d, 0xe4,
	0x5b, 0x42, 0x35, 0x5b, 0x72, 0x37, 0x74, 0x84, 0x1c, 0x4e, 0xbc, 0x14, 0x17, 0xb4, 0x84, 0x43,
	0xac, 0x6e, 0x88, 0x39, 0xb7, 0x4d, 0xcf, 0xe9, 0x74, 0x4f, 0x5d, 0x79, 0x0b, 0x8a, 0x11, 0x10,
	0x29, 0x41, 0xf6, 0x31, 0xed, 0xe0, 0x29, 0x73, 0x0a, 0xfb, 0x64, 0x67, 0x38, 0xd0, 0x9a, 0x6d,
	0x5e, 0x43, 0x92, 0x14, 0xde, 0xb8, 0x91, 0xb9, 0x2e, 0xc9, 0x5f, 0x65, 0xe0, 0x6c, 0x8a, 0xe4,
	0xbe, 0x30, 0xfa, 0xbd, 0x9d, 0x44, 0xbf, 0xb8, 0x7f, 0x89, 0x66, 0x41, 0x11, 0x1a, 0x7e, 0xd0,
	0x43, 0xc3, 0xb7, 0x53, 0x68, 0x98, 0x78, 0xaa, 0x17, 0x43, 0xd0, 0xbf, 0x93, 0x60, 0x39, 0xf0,
	0xcd, 0xcc, 0xea, 0xf8, 0x65, 0x46, 0xae, 0xa5, 0x1b, 0x2f, 0xc6, 0x1a, 0xf4, 0x46, 0x11, 0xf2,
	0x67, 0x12, 0x9c, 0x4b, 0x47, 0x64, 0x04, 0xb1, 0xc2, 0x7a, 0xdf, 0x4a, 0x72, 0x8a, 0x2e, 0xc4,
	0x8b, 0xc6, 0xff, 0x24, 0x41, 0x85, 0x73, 0x6b, 0xc3, 0xb1, 0x6c, 0x6b, 0x6f, 0x2f, 0xea, 0x37,
	0x53, 0x0b, 0xa4, 0xef, 0xc1, 0x82, 0x8b, 0x53, 0xd5, 0x20, 0x6f, 0xc3, 0x59, 0x48, 0x93, 0xde,
	0x5c, 0x2b, 0xc1, 0x2f, 0x2b, 0x73, 0x6e, 0x6f, 0xa7, 0xfc, 0x43, 0x09, 0x96, 0xb0, 0x90, 0x9d,
	0x82, 0xd8, 0xef, 0xcc, 0xc2, 0x26, 0x9c, 0x16, 0x48, 0xeb, 0x7c, 0xdd, 0x5e, 0xe4, 0x19, 0x09,
	0x2f, 0x24, 0x22, 0x9f, 0x84, 0x8a, 0x52, 0x71, 0xfb, 0xc2, 0xe4, 0x2f, 0x33, 0x50, 0xed, 0x7f,
	0x94, 0x11, 0x68, 0xb8, 0x0d, 0xb3, 0x01, 0xfe, 0x6e, 0xbb, 0xe9, 0x89, 0x88, 0x91, 0x1d, 0x61,
	0x23, 0x2e, 0x05, 0x03, 0xf0, 0x58, 0x0d, 0x9a, 0xed, 0xa6, 0x77, 0x57, 0xb3, 0xb9, 0x6a, 0xce,
	0xd4, 0xa2, 0xbd, 0x95, 0x06, 0xcc, 0x27, 0x0d, 0x0c, 0x2b, 0x6a, 0x9e, 0x2b, 0xea, 0x1f, 0x85,
	0x15, 0xb5, 0xb0, 0xb6, 0x3c, 0x84, 0xa1, 0x0d, 0x6b, 0xf3, 0x8f, 0xb9, 0x36, 0xef, 0xda, 0xcf,
	0x98, 0xa7, 0xc7, 0xe8, 0x9b, 0xe7, 0x28, 0xf7, 0x1d, 0x9d, 0x3a, 0xfd, 0xaf, 0xd7, 0x86, 0x16,
	0x05, 0x16, 0x47, 0xb2, 0xa5, 0xfc, 0xa0, 0x2e, 0xa7, 0x4c, 0x58, 0x7c, 0x69, 0xe6, 0x03, 0xfd,
	0x0a, 0x87, 0x70, 0xe7, 0xa2, 0xc9, 0xd2, 0x51, 0xf1, 0xa9, 0x36, 0x2f, 0xa1, 0x3f, 0xcf, 0x2b,
	0x79, 0xd1, 0xb3, 0x73, 0x29, 0x02, 0x5e, 0x13, 0x4e, 0x3d, 0x00, 0xaf, 0xc9, 0xdf, 0xe3, 0x86,
	0x21, 0xe5, 0x4c, 0x23, 0x90, 0x09, 0x9e, 0x31, 0xfb, 0x29, 0x0c, 0x3f, 0x59, 0xde, 0xf5, 0xf7,
	0x23, 0x67, 0x61, 0x4a, 0xa7, 0x4d, 0xe3, 0x80, 0x3a, 0x6a, 0x8b, 0x39, 0xf9, 0x2c, 0xc6, 0x18,
	0x05, 0xd1, 0x77, 0x97, 0x39, 0xfa, 0x93, 0x90, 0x6f, 0x1a, 0xa6, 0x08, 0xf2, 0x72, 0x18, 0x62,
	0x4f, 0xb2, 0x0e, 0x34, 0x19, 0x3f, 0x95, 0x60, 0x61, 0x7d, 0x9f, 0xd6, 0x1f, 0x6f, 0x6d, 0xdc,
	0xac, 0x19, 0x4d, 0xc3, 0xeb, 0x1c, 0x39, 0x3b, 0x3b, 0x01, 0x93, 0xb5, 0x76, 0xa7, 0xcb, 0x89,
	0xbc, 0x32, 0x81, 0xed, 0x6d, 0x9d, 0x34, 0xe1, 0x4c, 0x9d, 0x6d, 0xa6, 0xee, 0xeb, 0xaa, 0xc6,
	0xb7, 0xf3, 0x8b, 0x4f, 0xaa, 0xe1, 0xd1, 0x96, 0x2b, 0xfc, 0xd7, 0x4a, 0x6c, 0x9f, 0x28, 0x86,
	0xa2, 0x32, 0xb5, 0xed, 0xd1, 0x96, 0x52, 0xc1, 0xf5, 0xb6, 0xf4, 0x5e, 0x90, 0x2b, 0x7f, 0x2a,
	0xc1, 0x89, 0xbe, 0x33, 0xc3, 0x52, 0x21, 0xa5, 0x49, 0x45, 0x26, 0x5d, 0x2a, 0xb2, 0x31, 0xa9,
	0x60, 0x60, 0x61, 0x65, 0x99, 0x06, 0x09, 0x99, 0xe2, 0x3d, 0x7f, 0x4a, 0x3b, 0xf2, 0x2f, 0x25,
	0x58, 0x8c, 0x13, 0x7c, 0x04, 0xfe, 0x23, 0x85, 0xe8, 0x1e, 0x9c, 0xed, 0x4b, 0x74, 0x87, 0x72,
	0xc2, 0x0b, 0xba, 0xbf, 0x3a, 0x0c, 0xdd, 0x15, 0xca, 0x49, 0x7f, 0x2a, 0x91, 0xf4, 0x02, 0x2a,
	0xff, 0x4c, 0x82, 0x53, 0x69, 0xd3, 0xbf, 0x21, 0xfa, 0x13, 0x19, 0x8a, 0x86, 0x8b, 0x4f, 0x3a,
	0x58, 0xd4, 0xbd, 0xaf, 0xa3, 0x5a, 0x8f, 0x2b, 0x05, 0xc3, 0xdd, 0xe5, 0x7d, 0x5b, 0x58, 0x37,
	0x70, 0xa8, 0xd7, 0x76, 0xcc, 0xee, 0x25, 0x5e, 0x9e, 0x51, 0x9b, 0x75, 0x61, 0x66, 0xfa, 0x5f,
	0x12, 0xcc, 0x6d, 0x52, 0x6f, 0x6b, 0x43, 0x28, 0xfe, 0x91, 0x75, 0x46, 0x87, 0x93, 0x0d, 0xca,
	0xd0, 0x51, 0x7d, 0x65, 0x0f, 0xab, 0x85, 0xb0, 0xb9, 0x2f, 0xc5, 0x56, 0x0a, 0x63, 0x10, 0xd6,
	0x89, 0xe3, 0x0d, 0xea, 0x6d, 0xe9, 0xbd, 0x00, 0xa6, 0x10, 0xc7, 0xfb, 0x4c, 0x8a, 0x11, 0x5d,
	0x4a, 0x27, 0x7a, 0x26, 0x4e, 0xf4, 0xfe, 0x26, 0x76, 0x09, 0x0a, 0xee, 0xbe, 0x61, 0xb7, 0xa8,
	0xe9, 0xa9, 0xe2, 0x32, 0x35, 0xaf, 0x80, 0xdf, 0xb5, 0xad, 0xcb, 0xdf, 0xe7, 0x2f, 0x54, 0x42,
	0xb4, 0x1c, 0x81, 0x3a, 0x98, 0x78, 0x45, 0x9d, 0x44, 0xcf, 0x40, 0xe2, 0x39, 0x4d, 0x2f, 0x0c,
	0xa6, 0xa9, 0x2f, 0xef, 0x95, 0x04, 0xb2, 0xfa, 0xd2, 0xfe, 0xb5, 0x04, 0x95, 0xfe, 0x53, 0x5f,
	0x18, 0x71, 0xa3, 0xce, 0x21, 0x17, 0x77, 0x0e, 0x4b, 0x50, 0x08, 0xd6, 0x35, 0x1b, 0x28, 0xe9,
	0x19, 0xc5, 0xdf, 0x6a, 0xc7, 0x6c, 0x44, 0x06, 0x68, 0x1e, 0x0a, 0x7a, 0x68, 0x80, 0xe6, 0xc9,
	0x7f, 0x09, 0xb3, 0xef, 0xb6, 0xa9, 0xd3, 0x59, 0xd7, 0xea, 0xfb, 0xf4, 0x3d, 0xad, 0x89, 0xa7,
	0x39, 0x09, 0xf9, 0x3a, 0x6b, 0xab, 0x3c, 0x44, 0xc0, 0x38, 0x12, 0x3b, 0x98, 0x7e, 0x9d, 0x06,
	0xe0, 0xc0, 0xe0, 0xb9, 0x54, 0x5e, 0xe1, 0xc3, 0xfd, 0x72, 0xcf, 0x81, 0xd6, 0xe4, 0x40, 0xfe,
	0x3e, 0x6a, 0xe2, 0x40, 0x6b, 0xa2, 0x52, 0x6d, 0x41, 0x29, 0xb2, 0x97, 0x42, 0x9f, 0x90, 0x2b,
	0x90, 0x43, 0xb7, 0x25, 0xa1, 0x79, 0xaa, 0xc6, 0x98, 0xd5, 0x83, 0x9a, 0x82, 0xa3, 0xe5, 0xff,
	0x96, 0x60, 0x21, 0xb6, 0x94, 0x6b, 0x27, 0xa1, 0x2e, 0xa5, 0xa2, 0x2e, 0xa5, 0xa1, 0x2e, 0x85,
	0x50, 0xef, 0x66, 0x31, 0x5c, 0xbc, 0x79, 0x83, 0xf1, 0xcd, 0xa1, 0x1e, 0xe6, 0xde, 0xdc, 0xc8,
	0xf8, 0xcd, 0xf0, 0x93, 0x83, 0x63, 0x91, 0x27, 0x07, 0xf2, 0xc7, 0x52, 0x8c, 0xe2, 0x0c, 0xf5,
	0x23, 0xa9, 0xc2, 0x75, 0x41, 0x42, 0x1e, 0x47, 0x9e, 0x4b, 0x23, 0xa1, 0x4f, 0x26, 0x41, 0xc6,
	0xff, 0x93, 0xe0, 0xa5, 0x4d, 0xea, 0x6d, 0x38, 0xc6, 0x81, 0x61, 0x36, 0xfc, 0x4c, 0xee, 0x91,
	0xe1, 0xed, 0x8f, 0xce, 0xf0, 0xa5, 0xf8, 0xad, 0x77, 0x60, 0x2a, 0x10, 0x4e, 0x76, 0x00, 0xee,
	0xa2, 0xe2, 0xa5, 0xa4, 0x75, 0xab, 0xd5, 0xea, 0xea, 0x9b, 0x2f, 0xce, 0x18, 0xdb, 0xfc, 0x5a,
	0x82, 0x97, 0x07, 0xe2, 0x3f, 0x02, 0x63, 0xf3, 0xb7, 0x12, 0xac, 0xf8, 0x6a, 0xe8, 0x59, 0x81,
	0xa5, 0xd1, 0xf9, 0xbe, 0xaa, 0x9f, 0x0e, 0xa7, 0x3d, 0x0f, 0x12, 0xe8, 0x3c, 0xb4, 0xc4, 0x41,
	0x62, 0x38, 0x2b, 0xcb, 0x6e, 0xfa, 0x00, 0x3c, 0xef, 0x17, 0xe2, 0x96, 0x83, 0xd1, 0x4f, 0x0c,
	0x5b, 0xb7, 0x2c, 0x47, 0x37, 0x4c, 0xcd, 0xa3, 0xdf, 0x6a, 0x4e, 0x7d, 0x29, 0xee, 0x2e, 0xfa,
	0x60, 0x3e, 0x02, 0x1e, 0xbd, 0x0f, 0xc7, 0x7d, 0x14, 0xeb, 0xc1, 0xca, 0x61, 0x8e, 0xc4, 0x6d,
	0x4b, 0x2f, 0x1a, 0x0b, 0x5a, 0xbc, 0x6b, 0x47, 0x68, 0x89, 0x48, 0xba, 0xef, 0x51, 0xcd, 0xa9,
	0x75, 0x46, 0xa5, 0x19, 0x7b, 0x70, 0xba, 0x4e, 0x4d, 0x8f, 0x3a, 0xaa, 0x6d, 0x19, 0xa6, 0xa7,
	0x3e, 0x35, 0xbc, 0x7d, 0xd5, 0xd1, 0x74, 0xa3, 0xed, 0xaa, 0x29, 0x0a, 0xbd, 0x8e, 0x73, 0x1e,
	0xb0, 0x29, 0x4c, 0xd2, 0x15, 0x9c, 0xa0, 0x9c, 0xa8, 0x27, 0x75, 0x23, 0xfe, 0x9f, 0x49, 0x70,
	0x32, 0x11, 0xff, 0x11, 0x50, 0x7d, 0x07, 0xe6, 0x4c, 0x5c, 0x34, 0x70, 0xc3, 0x21, 0xcc, 0xe3,
	0xf2, 0x11, 0xdd, 0x7e, 0xd6, 0x0c, 0x37, 0x11, 0xd3, 0xbf, 0xce, 0x60, 0x0e, 0x19, 0x13, 0xfd,
	0x3b, 0x8e, 0xd5, 0xba, 0xab, 0x79, 0x8e, 0xf1, 0xec, 0xc8, 0x24, 0xbf, 0x0f, 0x73, 0x78, 0x17,
	0xc9, 0xb4, 0x96, 0x13, 0x3d, 0x84, 0xee, 0x52, 0xa2, 0x38, 0x87, 0xe4, 0x63, 0xd6, 0x9f, 0x8b,
	0x54, 0xc6, 0x3a, 0xf9, 0xbb, 0x30, 0xef, 0x51, 0xa7, 0xc5, 0x85, 0x2d, 0xb4, 0x62, 0x76, 0xb8,
	0x15, 0x49, 0x30, 0x39, 0x58, 0x52, 0xfe, 0x01, 0x4f, 0x3a, 0x53, 0x88, 0x30, 0x02, 0xbe, 0xd5,
	0xe0, 0x64, 0x1d, 0x91, 0x51, 0x5b, 0xb8, 0x68, 0xa2, 0x0d, 0x5b, 0x4e, 0x44, 0x9f, 0x63, 0x11,
	0x18, 0xae, 0x72, 0x3d, 0xa1, 0x17, 0x0f, 0xf2, 0x39, 0xbf, 0x7b, 0xeb, 0x1e, 0xf7, 0x56, 0x37,
	0x43, 0x38, 0x22, 0x1f, 0x8f, 0x96, 0x40, 0x84, 0xc2, 0xad, 0x5c, 0x24, 0xdc, 0x92, 0xff, 0x45,
	0xc2, 0x77, 0xf6, 0x89, 0x28, 0x8f, 0x80, 0xea, 0x91, 0x2a, 0x7f, 0x26, 0xad, 0xca, 0x9f, 0x8d,
	0x56, 0xf9, 0xe5, 0x9f, 0x67, 0x60, 0x69, 0x80, 0x0b, 0x21, 0x57, 0xa3, 0xf9, 0xd6, 0x20, 0xfb,
	0x1c, 0xc4, 0x98, 0xef, 0x00, 0x74, 0x2d, 0xa6, 0x88, 0x9a, 0x07, 0x4a, 0x6e, 0x68, 0x0a, 0x79,
	0x1d, 0xe6, 0x0d, 0x37, 0xf0, 0x8a, 0xa6, 0xe5, 0xa9, 0xf4, 0x19, 0x57, 0x02, 0x69, 0x65, 0x52,
	0x99, 0x35, 0x5c, 0xb1, 0xe5, 0x3d, 0xcb, 0xbb, 0xcd, 0x00, 0xe4, 0x53, 0x09, 0x2e, 0x0e, 0xe3,
	0x4f, 0x59, 0x18, 0xdf, 0xad, 0x6a, 0x14, 0xd6, 0xd6, 0x0e, 0xe7, 0x54, 0x31, 0xd2, 0x79, 0xd9,
	0x1d, 0x3c, 0x08, 0xc5, 0xf5, 0x7f, 0x32, 0x50, 0x8c, 0x50, 0xe8, 0x45, 0x26, 0xb0, 0x01, 0xf8,
	0x72, 0xbc, 0x28, 0x75, 0x39, 0x02, 0xbe, 0x12, 0x2f, 0x4a, 0x5d, 0x89, 0x80, 0xdf, 0x14, 0xd1,
	0x65, 0x00, 0x7e, 0x33, 0x02, 0xbe, 0xea, 0x3f, 0x3f, 0xf5, 0xc1, 0x57, 0x23, 0xe0, 0x6b, 0xfe,
	0x4d, 0x93, 0x0f, 0xbe, 0x16, 0x01, 0x5f, 0x17, 0x77, 0x4d, 0x01, 0xf8, 0x7a, 0xff, 0xcb, 0x26,
	0xf9, 0x2a, 0x94, 0xe2, 0x32, 0x42, 0x4a, 0x90, 0x65, 0x49, 0x87, 0x84, 0x49, 0x07, 0xfb, 0xc4,
	0x1e, 0xb3, 0x81, 0x04, 0x63, 0x3d, 0x66, 0x83, 0x85, 0xc3, 0xcb, 0x43, 0xb0, 0x31, 0x96, 0x08,
	0xb1, 0x25, 0xb3, 0xe1, 0x44, 0xe8, 0x02, 0x94, 0xe2, 0xd2, 0x23, 0x76, 0x99, 0xd1, 0x63, 0x6a,
	0x52, 0x86, 0x89, 0x03, 0xea, 0xb8, 0x86, 0x65, 0x22, 0x67, 0x72, 0x8a, 0xdf, 0x64, 0xe1, 0xd5,
	0x6c, 0x4f, 0x54, 0xf0, 0xcd, 0xa9, 0xd5, 0x05, 0x28, 0x31, 0x94, 0x75, 0xcd, 0xd1, 0xd5, 0x68,
	0x7a, 0x38, 0xe3, 0xf7, 0x8b, 0x4d, 0xe5, 0xff, 0x94, 0x60, 0x21, 0x31, 0x2e, 0x20, 0xb7, 0x60,
	0x2a, 0x1c, 0x64, 0x88, 0x23, 0x0c, 0xc4, 0xa3, 0x10, 0x0a, 0x27, 0xf0, 0xda, 0x13, 0x57, 0x13,
	0x24, 0x15, 0xad, 0x30, 0x65, 0xb2, 0x87, 0xa0, 0x8c, 0xfc, 0x2b, 0x09, 0x8a, 0x91, 0x58, 0xe0,
	0xdb, 0x88, 0x25, 0x59, 0x87, 0xa9, 0x48, 0x4c, 0x93, 0x4b, 0x8c, 0x22, 0xc5, 0x09, 0xc2, 0x48,
	0xb9, 0xa1, 0x88, 0xc6, 0x85, 0xd9, 0x9e, 0x11, 0x83, 0x64, 0xf9, 0xa8, 0x82, 0x23, 0xff, 0x44,
	0x82, 0xf9, 0x24, 0x5f, 0x4d, 0xfe, 0x04, 0x0a, 0xfc, 0x71, 0xd6, 0xa1, 0xa8, 0xcc, 0x1f, 0x74,
	0x71, 0x22, 0xff, 0x31, 0xe4, 0xa9, 0xa9, 0x8b, 0xf9, 0x43, 0xa2, 0x36, 0x49, 0x4d, 0x9d, 0xcf,
	0xae, 0xc2, 0x94, 0xc1, 0x1c, 0x84, 0xb8, 0xfd, 0x10, 0x0e, 0x02, 0x0c, 0xf7, 0x9e, 0xc5, 0xaf,
	0x27, 0x22, 0x77, 0xd6, 0x39, 0x64, 0x63, 0xd0, 0x7e, 0xa5, 0x06, 0xc5, 0xc8, 0xb3, 0x02, 0x32,
	0x03, 0x85, 0x3f, 0x33, 0xb5, 0x03, 0xcd, 0x68, 0x6a, 0xb5, 0x26, 0x2d, 0x8d, 0x91, 0x22, 0xe4,
	0x6f, 0x06, 0x4d, 0x89, 0xc1, 0xd7, 0x9b, 0x96, 0x6b, 0x98, 0x8d, 0x5d, 0xcb, 0x32, 0x4b, 0x19,
	0x06, 0x7f, 0x48, 0x5b, 0x36, 0xeb, 0xa4, 0xa5, 0x2c, 0x99, 0x85, 0x22, 0x16, 0x30, 0xad, 0xb6,
	0xb7, 0x63, 0xb4, 0x0c, 0xaf, 0x94, 0x5b, 0xfb, 0x64, 0x0e, 0x8a, 0xe2, 0xb1, 0x10, 0x75, 0x0e,
	0x8c, 0x3a, 0x25, 0xcf, 0xb1, 0xa2, 0x96, 0xf4, 0x17, 0x1c, 0xb9, 0xd8, 0x5b, 0x5a, 0x4a, 0xf9,
	0x43, 0xaf, 0xb2, 0x3a, 0xec, 0x70, 0x1e, 0x69, 0xc8, 0x63, 0x44, 0x85, 0xe9, 0xe8, 0x1f, 0x63,
	0x24, 0x9e, 0x0c, 0x24, 0xfe, 0x12, 0x57, 0x39, 0x3f, 0x60, 0x54, 0xb0, 0xc1, 0x5f, 0x40, 0x31,
	0xf2, 0xa2, 0x8c, 0x2c, 0xf7, 0xe2, 0xd8, 0xbb, 0xfc, 0xb9, 0xf4, 0x41, 0xc1, 0xea, 0x1f, 0x4b,
	0x70, 0x2a, 0xed, 0xbf, 0x25, 0xb2, 0x96, 0x74, 0xe7, 0x95, 0xfe, 0x7b, 0x56, 0xe5, 0xf2, 0xa1,
	0xe6, 0x04, 0xb8, 0xfc, 0x83, 0x04, 0xa7, 0x53, 0x7f, 0xb7, 0x21, 0xc3, 0x2f, 0xdc, 0xfd, 0x0d,
	0xa6, 0x72, 0xe5, 0x70, 0x93, 0x02, 0x74, 0x3e, 0x92, 0xe0, 0x44, 0xdf, 0xb7, 0x7c, 0xe4, 0xf5,
	0x34, 0x02, 0x27, 0xbc, 0x6c, 0xac, 0xbc, 0x31, 0xfc, 0x84, 0x00, 0x05, 0x2f, 0xf4, 0x83, 0x5e,
	0xf8, 0x65, 0x1c, 0x79, 0xb5, 0xdf, 0x62, 0x09, 0x4f, 0xfc, 0x2a, 0xaf, 0x0d, 0x37, 0x38, 0xd8,
	0xf5, 0xcf, 0x61, 0x2a, 0x7c, 0x93, 0x48, 0x86, 0xb8, 0x76, 0xae, 0x0c, 0x73, 0x15, 0x29, 0x68,
	0xda, 0xf7, 0x29, 0x43, 0x0f, 0x4d, 0x07, 0x3d, 0x42, 0xea, 0xa1, 0xe9, 0xc0, 0x57, 0x12, 0x42,
	0xe2, 0xd3, 0x5e, 0x12, 0xf4, 0x48, 0xfc, 0x10, 0xef, 0x1f, 0x7a, 0x24, 0x7e, 0x98, 0xa7, 0x0a,
	0xf2, 0x18, 0xf9, 0x10, 0xca, 0xfd, 0xee, 0x90, 0xc9, 0xea, 0xd0, 0x97, 0xcd, 0x1c, 0x85, 0xd7,
	0x0f, 0x79, 0x39, 0xdd, 0x25, 0x45, 0xdf, 0xbb, 0xd3, 0x24, 0x52, 0x0c, 0xba, 0x3c, 0x4e, 0x22,
	0xc5, 0xc0, 0xcb, 0x59, 0x14, 0xf5, 0x65, 0x34, 0xf4, 0x37, 0xbb, 0x15, 0xa9, 0x2d, 0xab, 0x45,
	0x37, 0xf8, 0x1d, 0x6a, 0x47, 0x5c, 0x5f, 0xf5, 0x18, 0xd7, 0xc4, 0x6b, 0xd3, 0x1e, 0xe3, 0x9a,
	0x7c, 0xd7, 0x27, 0x8f, 0x91, 0x3a, 0x2c, 0x6e, 0xd2, 0xc8, 0x46, 0x7e, 0xb8, 0x23, 0xa7, 0xdc,
	0x49, 0xf4, 0x13, 0xfa, 0xa4, 0x1b, 0x14, 0x79, 0x8c, 0x28, 0x50, 0x60, 0x09, 0xab, 0xa8, 0xef,
	0x92, 0xa5, 0xf4, 0xea, 0xef, 0x93, 0x4a, 0x75, 0x50, 0x79, 0x58, 0x1e, 0x63, 0xe9, 0xd9, 0xd2,
	0x80, 0xb2, 0x2a, 0x79, 0xb3, 0x17, 0xbd, 0x21, 0xca, 0xc8, 0x95, 0xab, 0x87, 0x9d, 0xd6, 0x63,
	0x31, 0x13, 0x2b, 0x88, 0x89, 0x16, 0x33, 0xad, 0x4a, 0x9a, 0x68, 0x31, 0x53, 0x8b, 0x93, 0xf2,
	0x18, 0x31, 0xfd, 0xd7, 0xaa, 0xd1, 0xe0, 0x35, 0xf9, 0xf1, 0x49, 0x52, 0xad, 0xb0, 0xf2, 0xca,
	0x30, 0x43, 0xe3, 0x2a, 0xd4, 0xb7, 0x12, 0x94, 0xa4, 0x42, 0x83, 0x6a, 0x67, 0x49, 0x2a, 0x34,
	0xb0, 0xd4, 0x24, 0x8f, 0x91, 0xa7, 0x28, 0xcc, 0x09, 0x85, 0x11, 0x92, 0xe0, 0x01, 0xfa, 0x97,
	0x7c, 0x2a, 0x17, 0x87, 0x1c, 0xed, 0x6f, 0xfc, 0xdb, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf6, 0x83,
	0x2a, 0x93, 0x2d, 0x41, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// BranchServiceClient is the client API for BranchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BranchServiceClient interface {
	GetSyncBranchSupplyType(ctx context.Context, in *GetSyncBranchSupplyTypeRequest, opts ...grpc.CallOption) (*GetSyncBranchSupplyTypeResponse, error)
	SyncBranchInfo(ctx context.Context, in *SyncBranchInfoRequest, opts ...grpc.CallOption) (*SyncBranchInfoResponse, error)
	GetBranchInfo(ctx context.Context, in *GetBranchInfoRequest, opts ...grpc.CallOption) (*GetBranchInfoResponse, error)
	BatchGetBranchInfoByBranchId(ctx context.Context, in *BatchGetBranchInfoByBranchIdRequest, opts ...grpc.CallOption) (*BatchGetBranchInfoByBranchIdResponse, error)
	BatchGetBranchInfoByBranchRef(ctx context.Context, in *BatchGetBranchInfoByBranchRefRequest, opts ...grpc.CallOption) (*BatchGetBranchInfoByBranchRefResponse, error)
	GetBranchInfoByLocationID(ctx context.Context, in *GetBranchInfoByLocationIDRequest, opts ...grpc.CallOption) (*GetBranchInfoByLocationIDResponse, error)
	GetBranchSubLocations(ctx context.Context, in *GetBranchSubLocationsRequest, opts ...grpc.CallOption) (*GetBranchSubLocationsResponse, error)
	SearchBranch(ctx context.Context, in *SearchBranchRequest, opts ...grpc.CallOption) (*SearchBranchResponse, error)
	SearchBranchForGoogleMaps(ctx context.Context, in *SearchBranchForGoogleMapsRequest, opts ...grpc.CallOption) (*SearchBranchForGoogleMapsResponse, error)
	GetBranchListByBranchGroupID(ctx context.Context, in *GetBranchListByBranchGroupIDRequest, opts ...grpc.CallOption) (*GetBranchListByBranchGroupIDResponse, error)
	BatchSearchDropoffBranch(ctx context.Context, in *BatchSearchDropoffBranchRequest, opts ...grpc.CallOption) (*BatchSearchDropoffBranchResponse, error)
	GetSpxStationIdInfoByOrderId(ctx context.Context, in *GetSpxStationIdInfoByOrderIdRequest, opts ...grpc.CallOption) (*GetSpxStationIdInfoByOrderIdResponse, error)
	CheckAddressListHomeDeliveryAbility(ctx context.Context, in *CheckHDAbilityRequest, opts ...grpc.CallOption) (*CheckHDAbilityResponse, error)
	GetHomeDeliveryStation(ctx context.Context, in *GetHDStationRequest, opts ...grpc.CallOption) (*GetHDStationResponse, error)
	GetCacheVal(ctx context.Context, in *QueryCacheValReq, opts ...grpc.CallOption) (*QueryCacheValResp, error)
	GetDrivingDistanceWithHDStation(ctx context.Context, in *GetDrivingDistanceWithHDStationRequest, opts ...grpc.CallOption) (*GetDrivingDistanceWithHDStationResponse, error)
	GetBuyerAddressCoordinate(ctx context.Context, in *GetBuyerAddressCoordinateRequest, opts ...grpc.CallOption) (*GetBuyerAddressCoordinateResponse, error)
	SearchNearbyStation(ctx context.Context, in *SearchNearbyStationRequest, opts ...grpc.CallOption) (*SearchNearbyStationResponse, error)
	GetDrivingDistanceFromMatrix(ctx context.Context, in *GetDrivingDistanceFromMatrixRequest, opts ...grpc.CallOption) (*GetDrivingDistanceFromMatrixResponse, error)
	GetCoordinateByAddress(ctx context.Context, in *GetCoordinateByAddressRequest, opts ...grpc.CallOption) (*GetCoordinateByAddressResponse, error)
}

type branchServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBranchServiceClient(cc grpc.ClientConnInterface) BranchServiceClient {
	return &branchServiceClient{cc}
}

func (c *branchServiceClient) GetSyncBranchSupplyType(ctx context.Context, in *GetSyncBranchSupplyTypeRequest, opts ...grpc.CallOption) (*GetSyncBranchSupplyTypeResponse, error) {
	out := new(GetSyncBranchSupplyTypeResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetSyncBranchSupplyType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) SyncBranchInfo(ctx context.Context, in *SyncBranchInfoRequest, opts ...grpc.CallOption) (*SyncBranchInfoResponse, error) {
	out := new(SyncBranchInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/SyncBranchInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetBranchInfo(ctx context.Context, in *GetBranchInfoRequest, opts ...grpc.CallOption) (*GetBranchInfoResponse, error) {
	out := new(GetBranchInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetBranchInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) BatchGetBranchInfoByBranchId(ctx context.Context, in *BatchGetBranchInfoByBranchIdRequest, opts ...grpc.CallOption) (*BatchGetBranchInfoByBranchIdResponse, error) {
	out := new(BatchGetBranchInfoByBranchIdResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/BatchGetBranchInfoByBranchId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) BatchGetBranchInfoByBranchRef(ctx context.Context, in *BatchGetBranchInfoByBranchRefRequest, opts ...grpc.CallOption) (*BatchGetBranchInfoByBranchRefResponse, error) {
	out := new(BatchGetBranchInfoByBranchRefResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/BatchGetBranchInfoByBranchRef", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetBranchInfoByLocationID(ctx context.Context, in *GetBranchInfoByLocationIDRequest, opts ...grpc.CallOption) (*GetBranchInfoByLocationIDResponse, error) {
	out := new(GetBranchInfoByLocationIDResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetBranchInfoByLocationID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetBranchSubLocations(ctx context.Context, in *GetBranchSubLocationsRequest, opts ...grpc.CallOption) (*GetBranchSubLocationsResponse, error) {
	out := new(GetBranchSubLocationsResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetBranchSubLocations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) SearchBranch(ctx context.Context, in *SearchBranchRequest, opts ...grpc.CallOption) (*SearchBranchResponse, error) {
	out := new(SearchBranchResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/SearchBranch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) SearchBranchForGoogleMaps(ctx context.Context, in *SearchBranchForGoogleMapsRequest, opts ...grpc.CallOption) (*SearchBranchForGoogleMapsResponse, error) {
	out := new(SearchBranchForGoogleMapsResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/SearchBranchForGoogleMaps", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetBranchListByBranchGroupID(ctx context.Context, in *GetBranchListByBranchGroupIDRequest, opts ...grpc.CallOption) (*GetBranchListByBranchGroupIDResponse, error) {
	out := new(GetBranchListByBranchGroupIDResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetBranchListByBranchGroupID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) BatchSearchDropoffBranch(ctx context.Context, in *BatchSearchDropoffBranchRequest, opts ...grpc.CallOption) (*BatchSearchDropoffBranchResponse, error) {
	out := new(BatchSearchDropoffBranchResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/BatchSearchDropoffBranch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetSpxStationIdInfoByOrderId(ctx context.Context, in *GetSpxStationIdInfoByOrderIdRequest, opts ...grpc.CallOption) (*GetSpxStationIdInfoByOrderIdResponse, error) {
	out := new(GetSpxStationIdInfoByOrderIdResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetSpxStationIdInfoByOrderId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) CheckAddressListHomeDeliveryAbility(ctx context.Context, in *CheckHDAbilityRequest, opts ...grpc.CallOption) (*CheckHDAbilityResponse, error) {
	out := new(CheckHDAbilityResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/CheckAddressListHomeDeliveryAbility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetHomeDeliveryStation(ctx context.Context, in *GetHDStationRequest, opts ...grpc.CallOption) (*GetHDStationResponse, error) {
	out := new(GetHDStationResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetHomeDeliveryStation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetCacheVal(ctx context.Context, in *QueryCacheValReq, opts ...grpc.CallOption) (*QueryCacheValResp, error) {
	out := new(QueryCacheValResp)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetCacheVal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetDrivingDistanceWithHDStation(ctx context.Context, in *GetDrivingDistanceWithHDStationRequest, opts ...grpc.CallOption) (*GetDrivingDistanceWithHDStationResponse, error) {
	out := new(GetDrivingDistanceWithHDStationResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetDrivingDistanceWithHDStation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetBuyerAddressCoordinate(ctx context.Context, in *GetBuyerAddressCoordinateRequest, opts ...grpc.CallOption) (*GetBuyerAddressCoordinateResponse, error) {
	out := new(GetBuyerAddressCoordinateResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetBuyerAddressCoordinate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) SearchNearbyStation(ctx context.Context, in *SearchNearbyStationRequest, opts ...grpc.CallOption) (*SearchNearbyStationResponse, error) {
	out := new(SearchNearbyStationResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/SearchNearbyStation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetDrivingDistanceFromMatrix(ctx context.Context, in *GetDrivingDistanceFromMatrixRequest, opts ...grpc.CallOption) (*GetDrivingDistanceFromMatrixResponse, error) {
	out := new(GetDrivingDistanceFromMatrixResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetDrivingDistanceFromMatrix", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *branchServiceClient) GetCoordinateByAddress(ctx context.Context, in *GetCoordinateByAddressRequest, opts ...grpc.CallOption) (*GetCoordinateByAddressResponse, error) {
	out := new(GetCoordinateByAddressResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.BranchService/GetCoordinateByAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BranchServiceServer is the server API for BranchService service.
type BranchServiceServer interface {
	GetSyncBranchSupplyType(context.Context, *GetSyncBranchSupplyTypeRequest) (*GetSyncBranchSupplyTypeResponse, error)
	SyncBranchInfo(context.Context, *SyncBranchInfoRequest) (*SyncBranchInfoResponse, error)
	GetBranchInfo(context.Context, *GetBranchInfoRequest) (*GetBranchInfoResponse, error)
	BatchGetBranchInfoByBranchId(context.Context, *BatchGetBranchInfoByBranchIdRequest) (*BatchGetBranchInfoByBranchIdResponse, error)
	BatchGetBranchInfoByBranchRef(context.Context, *BatchGetBranchInfoByBranchRefRequest) (*BatchGetBranchInfoByBranchRefResponse, error)
	GetBranchInfoByLocationID(context.Context, *GetBranchInfoByLocationIDRequest) (*GetBranchInfoByLocationIDResponse, error)
	GetBranchSubLocations(context.Context, *GetBranchSubLocationsRequest) (*GetBranchSubLocationsResponse, error)
	SearchBranch(context.Context, *SearchBranchRequest) (*SearchBranchResponse, error)
	SearchBranchForGoogleMaps(context.Context, *SearchBranchForGoogleMapsRequest) (*SearchBranchForGoogleMapsResponse, error)
	GetBranchListByBranchGroupID(context.Context, *GetBranchListByBranchGroupIDRequest) (*GetBranchListByBranchGroupIDResponse, error)
	BatchSearchDropoffBranch(context.Context, *BatchSearchDropoffBranchRequest) (*BatchSearchDropoffBranchResponse, error)
	GetSpxStationIdInfoByOrderId(context.Context, *GetSpxStationIdInfoByOrderIdRequest) (*GetSpxStationIdInfoByOrderIdResponse, error)
	CheckAddressListHomeDeliveryAbility(context.Context, *CheckHDAbilityRequest) (*CheckHDAbilityResponse, error)
	GetHomeDeliveryStation(context.Context, *GetHDStationRequest) (*GetHDStationResponse, error)
	GetCacheVal(context.Context, *QueryCacheValReq) (*QueryCacheValResp, error)
	GetDrivingDistanceWithHDStation(context.Context, *GetDrivingDistanceWithHDStationRequest) (*GetDrivingDistanceWithHDStationResponse, error)
	GetBuyerAddressCoordinate(context.Context, *GetBuyerAddressCoordinateRequest) (*GetBuyerAddressCoordinateResponse, error)
	SearchNearbyStation(context.Context, *SearchNearbyStationRequest) (*SearchNearbyStationResponse, error)
	GetDrivingDistanceFromMatrix(context.Context, *GetDrivingDistanceFromMatrixRequest) (*GetDrivingDistanceFromMatrixResponse, error)
	GetCoordinateByAddress(context.Context, *GetCoordinateByAddressRequest) (*GetCoordinateByAddressResponse, error)
}

// UnimplementedBranchServiceServer can be embedded to have forward compatible implementations.
type UnimplementedBranchServiceServer struct {
}

func (*UnimplementedBranchServiceServer) GetSyncBranchSupplyType(ctx context.Context, req *GetSyncBranchSupplyTypeRequest) (*GetSyncBranchSupplyTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSyncBranchSupplyType not implemented")
}
func (*UnimplementedBranchServiceServer) SyncBranchInfo(ctx context.Context, req *SyncBranchInfoRequest) (*SyncBranchInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncBranchInfo not implemented")
}
func (*UnimplementedBranchServiceServer) GetBranchInfo(ctx context.Context, req *GetBranchInfoRequest) (*GetBranchInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBranchInfo not implemented")
}
func (*UnimplementedBranchServiceServer) BatchGetBranchInfoByBranchId(ctx context.Context, req *BatchGetBranchInfoByBranchIdRequest) (*BatchGetBranchInfoByBranchIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetBranchInfoByBranchId not implemented")
}
func (*UnimplementedBranchServiceServer) BatchGetBranchInfoByBranchRef(ctx context.Context, req *BatchGetBranchInfoByBranchRefRequest) (*BatchGetBranchInfoByBranchRefResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetBranchInfoByBranchRef not implemented")
}
func (*UnimplementedBranchServiceServer) GetBranchInfoByLocationID(ctx context.Context, req *GetBranchInfoByLocationIDRequest) (*GetBranchInfoByLocationIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBranchInfoByLocationID not implemented")
}
func (*UnimplementedBranchServiceServer) GetBranchSubLocations(ctx context.Context, req *GetBranchSubLocationsRequest) (*GetBranchSubLocationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBranchSubLocations not implemented")
}
func (*UnimplementedBranchServiceServer) SearchBranch(ctx context.Context, req *SearchBranchRequest) (*SearchBranchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchBranch not implemented")
}
func (*UnimplementedBranchServiceServer) SearchBranchForGoogleMaps(ctx context.Context, req *SearchBranchForGoogleMapsRequest) (*SearchBranchForGoogleMapsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchBranchForGoogleMaps not implemented")
}
func (*UnimplementedBranchServiceServer) GetBranchListByBranchGroupID(ctx context.Context, req *GetBranchListByBranchGroupIDRequest) (*GetBranchListByBranchGroupIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBranchListByBranchGroupID not implemented")
}
func (*UnimplementedBranchServiceServer) BatchSearchDropoffBranch(ctx context.Context, req *BatchSearchDropoffBranchRequest) (*BatchSearchDropoffBranchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchSearchDropoffBranch not implemented")
}
func (*UnimplementedBranchServiceServer) GetSpxStationIdInfoByOrderId(ctx context.Context, req *GetSpxStationIdInfoByOrderIdRequest) (*GetSpxStationIdInfoByOrderIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSpxStationIdInfoByOrderId not implemented")
}
func (*UnimplementedBranchServiceServer) CheckAddressListHomeDeliveryAbility(ctx context.Context, req *CheckHDAbilityRequest) (*CheckHDAbilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAddressListHomeDeliveryAbility not implemented")
}
func (*UnimplementedBranchServiceServer) GetHomeDeliveryStation(ctx context.Context, req *GetHDStationRequest) (*GetHDStationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHomeDeliveryStation not implemented")
}
func (*UnimplementedBranchServiceServer) GetCacheVal(ctx context.Context, req *QueryCacheValReq) (*QueryCacheValResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCacheVal not implemented")
}
func (*UnimplementedBranchServiceServer) GetDrivingDistanceWithHDStation(ctx context.Context, req *GetDrivingDistanceWithHDStationRequest) (*GetDrivingDistanceWithHDStationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDrivingDistanceWithHDStation not implemented")
}
func (*UnimplementedBranchServiceServer) GetBuyerAddressCoordinate(ctx context.Context, req *GetBuyerAddressCoordinateRequest) (*GetBuyerAddressCoordinateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBuyerAddressCoordinate not implemented")
}
func (*UnimplementedBranchServiceServer) SearchNearbyStation(ctx context.Context, req *SearchNearbyStationRequest) (*SearchNearbyStationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchNearbyStation not implemented")
}
func (*UnimplementedBranchServiceServer) GetDrivingDistanceFromMatrix(ctx context.Context, req *GetDrivingDistanceFromMatrixRequest) (*GetDrivingDistanceFromMatrixResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDrivingDistanceFromMatrix not implemented")
}
func (*UnimplementedBranchServiceServer) GetCoordinateByAddress(ctx context.Context, req *GetCoordinateByAddressRequest) (*GetCoordinateByAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCoordinateByAddress not implemented")
}

func RegisterBranchServiceServer(s *grpc.Server, srv BranchServiceServer) {
	s.RegisterService(&_BranchService_serviceDesc, srv)
}

func _BranchService_GetSyncBranchSupplyType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncBranchSupplyTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetSyncBranchSupplyType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetSyncBranchSupplyType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetSyncBranchSupplyType(ctx, req.(*GetSyncBranchSupplyTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_SyncBranchInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncBranchInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).SyncBranchInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/SyncBranchInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).SyncBranchInfo(ctx, req.(*SyncBranchInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetBranchInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBranchInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetBranchInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetBranchInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetBranchInfo(ctx, req.(*GetBranchInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_BatchGetBranchInfoByBranchId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetBranchInfoByBranchIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).BatchGetBranchInfoByBranchId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/BatchGetBranchInfoByBranchId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).BatchGetBranchInfoByBranchId(ctx, req.(*BatchGetBranchInfoByBranchIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_BatchGetBranchInfoByBranchRef_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetBranchInfoByBranchRefRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).BatchGetBranchInfoByBranchRef(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/BatchGetBranchInfoByBranchRef",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).BatchGetBranchInfoByBranchRef(ctx, req.(*BatchGetBranchInfoByBranchRefRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetBranchInfoByLocationID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBranchInfoByLocationIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetBranchInfoByLocationID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetBranchInfoByLocationID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetBranchInfoByLocationID(ctx, req.(*GetBranchInfoByLocationIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetBranchSubLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBranchSubLocationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetBranchSubLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetBranchSubLocations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetBranchSubLocations(ctx, req.(*GetBranchSubLocationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_SearchBranch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchBranchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).SearchBranch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/SearchBranch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).SearchBranch(ctx, req.(*SearchBranchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_SearchBranchForGoogleMaps_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchBranchForGoogleMapsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).SearchBranchForGoogleMaps(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/SearchBranchForGoogleMaps",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).SearchBranchForGoogleMaps(ctx, req.(*SearchBranchForGoogleMapsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetBranchListByBranchGroupID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBranchListByBranchGroupIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetBranchListByBranchGroupID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetBranchListByBranchGroupID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetBranchListByBranchGroupID(ctx, req.(*GetBranchListByBranchGroupIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_BatchSearchDropoffBranch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSearchDropoffBranchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).BatchSearchDropoffBranch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/BatchSearchDropoffBranch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).BatchSearchDropoffBranch(ctx, req.(*BatchSearchDropoffBranchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetSpxStationIdInfoByOrderId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSpxStationIdInfoByOrderIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetSpxStationIdInfoByOrderId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetSpxStationIdInfoByOrderId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetSpxStationIdInfoByOrderId(ctx, req.(*GetSpxStationIdInfoByOrderIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_CheckAddressListHomeDeliveryAbility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckHDAbilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).CheckAddressListHomeDeliveryAbility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/CheckAddressListHomeDeliveryAbility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).CheckAddressListHomeDeliveryAbility(ctx, req.(*CheckHDAbilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetHomeDeliveryStation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHDStationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetHomeDeliveryStation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetHomeDeliveryStation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetHomeDeliveryStation(ctx, req.(*GetHDStationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetCacheVal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCacheValReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetCacheVal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetCacheVal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetCacheVal(ctx, req.(*QueryCacheValReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetDrivingDistanceWithHDStation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDrivingDistanceWithHDStationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetDrivingDistanceWithHDStation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetDrivingDistanceWithHDStation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetDrivingDistanceWithHDStation(ctx, req.(*GetDrivingDistanceWithHDStationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetBuyerAddressCoordinate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBuyerAddressCoordinateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetBuyerAddressCoordinate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetBuyerAddressCoordinate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetBuyerAddressCoordinate(ctx, req.(*GetBuyerAddressCoordinateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_SearchNearbyStation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchNearbyStationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).SearchNearbyStation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/SearchNearbyStation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).SearchNearbyStation(ctx, req.(*SearchNearbyStationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetDrivingDistanceFromMatrix_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDrivingDistanceFromMatrixRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetDrivingDistanceFromMatrix(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetDrivingDistanceFromMatrix",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetDrivingDistanceFromMatrix(ctx, req.(*GetDrivingDistanceFromMatrixRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BranchService_GetCoordinateByAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoordinateByAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BranchServiceServer).GetCoordinateByAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.BranchService/GetCoordinateByAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BranchServiceServer).GetCoordinateByAddress(ctx, req.(*GetCoordinateByAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BranchService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.BranchService",
	HandlerType: (*BranchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSyncBranchSupplyType",
			Handler:    _BranchService_GetSyncBranchSupplyType_Handler,
		},
		{
			MethodName: "SyncBranchInfo",
			Handler:    _BranchService_SyncBranchInfo_Handler,
		},
		{
			MethodName: "GetBranchInfo",
			Handler:    _BranchService_GetBranchInfo_Handler,
		},
		{
			MethodName: "BatchGetBranchInfoByBranchId",
			Handler:    _BranchService_BatchGetBranchInfoByBranchId_Handler,
		},
		{
			MethodName: "BatchGetBranchInfoByBranchRef",
			Handler:    _BranchService_BatchGetBranchInfoByBranchRef_Handler,
		},
		{
			MethodName: "GetBranchInfoByLocationID",
			Handler:    _BranchService_GetBranchInfoByLocationID_Handler,
		},
		{
			MethodName: "GetBranchSubLocations",
			Handler:    _BranchService_GetBranchSubLocations_Handler,
		},
		{
			MethodName: "SearchBranch",
			Handler:    _BranchService_SearchBranch_Handler,
		},
		{
			MethodName: "SearchBranchForGoogleMaps",
			Handler:    _BranchService_SearchBranchForGoogleMaps_Handler,
		},
		{
			MethodName: "GetBranchListByBranchGroupID",
			Handler:    _BranchService_GetBranchListByBranchGroupID_Handler,
		},
		{
			MethodName: "BatchSearchDropoffBranch",
			Handler:    _BranchService_BatchSearchDropoffBranch_Handler,
		},
		{
			MethodName: "GetSpxStationIdInfoByOrderId",
			Handler:    _BranchService_GetSpxStationIdInfoByOrderId_Handler,
		},
		{
			MethodName: "CheckAddressListHomeDeliveryAbility",
			Handler:    _BranchService_CheckAddressListHomeDeliveryAbility_Handler,
		},
		{
			MethodName: "GetHomeDeliveryStation",
			Handler:    _BranchService_GetHomeDeliveryStation_Handler,
		},
		{
			MethodName: "GetCacheVal",
			Handler:    _BranchService_GetCacheVal_Handler,
		},
		{
			MethodName: "GetDrivingDistanceWithHDStation",
			Handler:    _BranchService_GetDrivingDistanceWithHDStation_Handler,
		},
		{
			MethodName: "GetBuyerAddressCoordinate",
			Handler:    _BranchService_GetBuyerAddressCoordinate_Handler,
		},
		{
			MethodName: "SearchNearbyStation",
			Handler:    _BranchService_SearchNearbyStation_Handler,
		},
		{
			MethodName: "GetDrivingDistanceFromMatrix",
			Handler:    _BranchService_GetDrivingDistanceFromMatrix_Handler,
		},
		{
			MethodName: "GetCoordinateByAddress",
			Handler:    _BranchService_GetCoordinateByAddress_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_branch.proto",
}
