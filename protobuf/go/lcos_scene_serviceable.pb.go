// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_scene_serviceable.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// serviceable context, directives for how to check serviceable
type ServiceableBasis struct {
	CheckOperation       *uint32                 `protobuf:"varint,1,req,name=check_operation,json=checkOperation" json:"check_operation,omitempty"`
	CheckBasic           *uint32                 `protobuf:"varint,2,req,name=check_basic,json=checkBasic" json:"check_basic,omitempty"`
	CollectType          *CollectTypeEnum        `protobuf:"varint,3,opt,name=collect_type,json=collectType,enum=lcos_protobuf.CollectTypeEnum" json:"collect_type,omitempty"`
	DeliverType          *DeliveryTypeEnum       `protobuf:"varint,4,opt,name=deliver_type,json=deliverType,enum=lcos_protobuf.DeliveryTypeEnum" json:"deliver_type,omitempty"`
	CheckPostalCode      *uint32                 `protobuf:"varint,5,opt,name=check_postal_code,json=checkPostalCode" json:"check_postal_code,omitempty"`
	PaymentMethod        *PaymentMethodEnum      `protobuf:"varint,6,opt,name=payment_method,json=paymentMethod,enum=lcos_protobuf.PaymentMethodEnum" json:"payment_method,omitempty"`
	SenderCheckLevel     *LocationCheckLevelEnum `protobuf:"varint,7,opt,name=sender_check_level,json=senderCheckLevel,enum=lcos_protobuf.LocationCheckLevelEnum" json:"sender_check_level,omitempty"`
	ReceiverCheckLevel   *LocationCheckLevelEnum `protobuf:"varint,8,opt,name=receiver_check_level,json=receiverCheckLevel,enum=lcos_protobuf.LocationCheckLevelEnum" json:"receiver_check_level,omitempty"`
	CheckSender          *uint32                 `protobuf:"varint,9,opt,name=check_sender,json=checkSender" json:"check_sender,omitempty"`
	CheckReceiver        *uint32                 `protobuf:"varint,10,opt,name=check_receiver,json=checkReceiver" json:"check_receiver,omitempty"`
	SkipZoneRoute        *uint32                 `protobuf:"varint,11,opt,name=skip_zone_route,json=skipZoneRoute" json:"skip_zone_route,omitempty"`
	SkipElectricFence    *bool                   `protobuf:"varint,12,opt,name=skip_electric_fence,json=skipElectricFence" json:"skip_electric_fence,omitempty"`
	UseElectricFence     *bool                   `protobuf:"varint,13,opt,name=use_electric_fence,json=useElectricFence" json:"use_electric_fence,omitempty"`
	IsCheckTradeIn       *int32                  `protobuf:"varint,14,opt,name=is_check_trade_in,json=isCheckTradeIn" json:"is_check_trade_in,omitempty"`
	CheckPredefinedRoute *bool                   `protobuf:"varint,15,opt,name=check_predefined_route,json=checkPredefinedRoute" json:"check_predefined_route,omitempty"`
	PredefinedRouteCodes []string                `protobuf:"bytes,16,rep,name=predefined_route_codes,json=predefinedRouteCodes" json:"predefined_route_codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ServiceableBasis) Reset()         { *m = ServiceableBasis{} }
func (m *ServiceableBasis) String() string { return proto.CompactTextString(m) }
func (*ServiceableBasis) ProtoMessage()    {}
func (*ServiceableBasis) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{0}
}

func (m *ServiceableBasis) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceableBasis.Unmarshal(m, b)
}
func (m *ServiceableBasis) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceableBasis.Marshal(b, m, deterministic)
}
func (m *ServiceableBasis) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceableBasis.Merge(m, src)
}
func (m *ServiceableBasis) XXX_Size() int {
	return xxx_messageInfo_ServiceableBasis.Size(m)
}
func (m *ServiceableBasis) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceableBasis.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceableBasis proto.InternalMessageInfo

func (m *ServiceableBasis) GetCheckOperation() uint32 {
	if m != nil && m.CheckOperation != nil {
		return *m.CheckOperation
	}
	return 0
}

func (m *ServiceableBasis) GetCheckBasic() uint32 {
	if m != nil && m.CheckBasic != nil {
		return *m.CheckBasic
	}
	return 0
}

func (m *ServiceableBasis) GetCollectType() CollectTypeEnum {
	if m != nil && m.CollectType != nil {
		return *m.CollectType
	}
	return CollectTypeEnum_PICK_UP_COLLECT
}

func (m *ServiceableBasis) GetDeliverType() DeliveryTypeEnum {
	if m != nil && m.DeliverType != nil {
		return *m.DeliverType
	}
	return DeliveryTypeEnum_TO_HOME
}

func (m *ServiceableBasis) GetCheckPostalCode() uint32 {
	if m != nil && m.CheckPostalCode != nil {
		return *m.CheckPostalCode
	}
	return 0
}

func (m *ServiceableBasis) GetPaymentMethod() PaymentMethodEnum {
	if m != nil && m.PaymentMethod != nil {
		return *m.PaymentMethod
	}
	return PaymentMethodEnum_STANDARD
}

func (m *ServiceableBasis) GetSenderCheckLevel() LocationCheckLevelEnum {
	if m != nil && m.SenderCheckLevel != nil {
		return *m.SenderCheckLevel
	}
	return LocationCheckLevelEnum_STATE
}

func (m *ServiceableBasis) GetReceiverCheckLevel() LocationCheckLevelEnum {
	if m != nil && m.ReceiverCheckLevel != nil {
		return *m.ReceiverCheckLevel
	}
	return LocationCheckLevelEnum_STATE
}

func (m *ServiceableBasis) GetCheckSender() uint32 {
	if m != nil && m.CheckSender != nil {
		return *m.CheckSender
	}
	return 0
}

func (m *ServiceableBasis) GetCheckReceiver() uint32 {
	if m != nil && m.CheckReceiver != nil {
		return *m.CheckReceiver
	}
	return 0
}

func (m *ServiceableBasis) GetSkipZoneRoute() uint32 {
	if m != nil && m.SkipZoneRoute != nil {
		return *m.SkipZoneRoute
	}
	return 0
}

func (m *ServiceableBasis) GetSkipElectricFence() bool {
	if m != nil && m.SkipElectricFence != nil {
		return *m.SkipElectricFence
	}
	return false
}

func (m *ServiceableBasis) GetUseElectricFence() bool {
	if m != nil && m.UseElectricFence != nil {
		return *m.UseElectricFence
	}
	return false
}

func (m *ServiceableBasis) GetIsCheckTradeIn() int32 {
	if m != nil && m.IsCheckTradeIn != nil {
		return *m.IsCheckTradeIn
	}
	return 0
}

func (m *ServiceableBasis) GetCheckPredefinedRoute() bool {
	if m != nil && m.CheckPredefinedRoute != nil {
		return *m.CheckPredefinedRoute
	}
	return false
}

func (m *ServiceableBasis) GetPredefinedRouteCodes() []string {
	if m != nil {
		return m.PredefinedRouteCodes
	}
	return nil
}

type AreaServiceability struct {
	CanPickup            *uint32  `protobuf:"varint,1,opt,name=can_pickup,json=canPickup" json:"can_pickup,omitempty"`
	CanCodPickup         *uint32  `protobuf:"varint,2,opt,name=can_cod_pickup,json=canCodPickup" json:"can_cod_pickup,omitempty"`
	CanDeliver           *uint32  `protobuf:"varint,3,opt,name=can_deliver,json=canDeliver" json:"can_deliver,omitempty"`
	CanCodDeliver        *uint32  `protobuf:"varint,4,opt,name=can_cod_deliver,json=canCodDeliver" json:"can_cod_deliver,omitempty"`
	PickupInEFence       *uint32  `protobuf:"varint,5,opt,name=pickup_in_e_fence,json=pickupInEFence" json:"pickup_in_e_fence,omitempty"`
	DeliverInEFence      *uint32  `protobuf:"varint,6,opt,name=deliver_in_e_fence,json=deliverInEFence" json:"deliver_in_e_fence,omitempty"`
	SupportTradeIn       *uint32  `protobuf:"varint,7,opt,name=support_trade_in,json=supportTradeIn" json:"support_trade_in,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AreaServiceability) Reset()         { *m = AreaServiceability{} }
func (m *AreaServiceability) String() string { return proto.CompactTextString(m) }
func (*AreaServiceability) ProtoMessage()    {}
func (*AreaServiceability) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{1}
}

func (m *AreaServiceability) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AreaServiceability.Unmarshal(m, b)
}
func (m *AreaServiceability) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AreaServiceability.Marshal(b, m, deterministic)
}
func (m *AreaServiceability) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AreaServiceability.Merge(m, src)
}
func (m *AreaServiceability) XXX_Size() int {
	return xxx_messageInfo_AreaServiceability.Size(m)
}
func (m *AreaServiceability) XXX_DiscardUnknown() {
	xxx_messageInfo_AreaServiceability.DiscardUnknown(m)
}

var xxx_messageInfo_AreaServiceability proto.InternalMessageInfo

func (m *AreaServiceability) GetCanPickup() uint32 {
	if m != nil && m.CanPickup != nil {
		return *m.CanPickup
	}
	return 0
}

func (m *AreaServiceability) GetCanCodPickup() uint32 {
	if m != nil && m.CanCodPickup != nil {
		return *m.CanCodPickup
	}
	return 0
}

func (m *AreaServiceability) GetCanDeliver() uint32 {
	if m != nil && m.CanDeliver != nil {
		return *m.CanDeliver
	}
	return 0
}

func (m *AreaServiceability) GetCanCodDeliver() uint32 {
	if m != nil && m.CanCodDeliver != nil {
		return *m.CanCodDeliver
	}
	return 0
}

func (m *AreaServiceability) GetPickupInEFence() uint32 {
	if m != nil && m.PickupInEFence != nil {
		return *m.PickupInEFence
	}
	return 0
}

func (m *AreaServiceability) GetDeliverInEFence() uint32 {
	if m != nil && m.DeliverInEFence != nil {
		return *m.DeliverInEFence
	}
	return 0
}

func (m *AreaServiceability) GetSupportTradeIn() uint32 {
	if m != nil && m.SupportTradeIn != nil {
		return *m.SupportTradeIn
	}
	return 0
}

type ServiceableAddress struct {
	StateLocationId      *uint32  `protobuf:"varint,1,opt,name=state_location_id,json=stateLocationId" json:"state_location_id,omitempty"`
	CityLocationId       *uint32  `protobuf:"varint,2,opt,name=city_location_id,json=cityLocationId" json:"city_location_id,omitempty"`
	DistrictLocationId   *uint32  `protobuf:"varint,3,opt,name=district_location_id,json=districtLocationId" json:"district_location_id,omitempty"`
	StreetLocationId     *uint32  `protobuf:"varint,4,opt,name=street_location_id,json=streetLocationId" json:"street_location_id,omitempty"`
	PostalCode           *string  `protobuf:"bytes,5,opt,name=postal_code,json=postalCode" json:"postal_code,omitempty"`
	Longitude            *string  `protobuf:"bytes,6,opt,name=longitude" json:"longitude,omitempty"`
	Latitude             *string  `protobuf:"bytes,7,opt,name=latitude" json:"latitude,omitempty"`
	Region               *string  `protobuf:"bytes,8,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServiceableAddress) Reset()         { *m = ServiceableAddress{} }
func (m *ServiceableAddress) String() string { return proto.CompactTextString(m) }
func (*ServiceableAddress) ProtoMessage()    {}
func (*ServiceableAddress) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{2}
}

func (m *ServiceableAddress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceableAddress.Unmarshal(m, b)
}
func (m *ServiceableAddress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceableAddress.Marshal(b, m, deterministic)
}
func (m *ServiceableAddress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceableAddress.Merge(m, src)
}
func (m *ServiceableAddress) XXX_Size() int {
	return xxx_messageInfo_ServiceableAddress.Size(m)
}
func (m *ServiceableAddress) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceableAddress.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceableAddress proto.InternalMessageInfo

func (m *ServiceableAddress) GetStateLocationId() uint32 {
	if m != nil && m.StateLocationId != nil {
		return *m.StateLocationId
	}
	return 0
}

func (m *ServiceableAddress) GetCityLocationId() uint32 {
	if m != nil && m.CityLocationId != nil {
		return *m.CityLocationId
	}
	return 0
}

func (m *ServiceableAddress) GetDistrictLocationId() uint32 {
	if m != nil && m.DistrictLocationId != nil {
		return *m.DistrictLocationId
	}
	return 0
}

func (m *ServiceableAddress) GetStreetLocationId() uint32 {
	if m != nil && m.StreetLocationId != nil {
		return *m.StreetLocationId
	}
	return 0
}

func (m *ServiceableAddress) GetPostalCode() string {
	if m != nil && m.PostalCode != nil {
		return *m.PostalCode
	}
	return ""
}

func (m *ServiceableAddress) GetLongitude() string {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return ""
}

func (m *ServiceableAddress) GetLatitude() string {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return ""
}

func (m *ServiceableAddress) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type AreaServiceable struct {
	Code                 *int32              `protobuf:"varint,1,req,name=code" json:"code,omitempty"`
	Message              *string             `protobuf:"bytes,2,req,name=message" json:"message,omitempty"`
	Ability              *AreaServiceability `protobuf:"bytes,3,opt,name=ability" json:"ability,omitempty"`
	ActualPoints         []*ActualPoint      `protobuf:"bytes,4,rep,name=actual_points,json=actualPoints" json:"actual_points,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AreaServiceable) Reset()         { *m = AreaServiceable{} }
func (m *AreaServiceable) String() string { return proto.CompactTextString(m) }
func (*AreaServiceable) ProtoMessage()    {}
func (*AreaServiceable) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{3}
}

func (m *AreaServiceable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AreaServiceable.Unmarshal(m, b)
}
func (m *AreaServiceable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AreaServiceable.Marshal(b, m, deterministic)
}
func (m *AreaServiceable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AreaServiceable.Merge(m, src)
}
func (m *AreaServiceable) XXX_Size() int {
	return xxx_messageInfo_AreaServiceable.Size(m)
}
func (m *AreaServiceable) XXX_DiscardUnknown() {
	xxx_messageInfo_AreaServiceable.DiscardUnknown(m)
}

var xxx_messageInfo_AreaServiceable proto.InternalMessageInfo

func (m *AreaServiceable) GetCode() int32 {
	if m != nil && m.Code != nil {
		return *m.Code
	}
	return 0
}

func (m *AreaServiceable) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *AreaServiceable) GetAbility() *AreaServiceability {
	if m != nil {
		return m.Ability
	}
	return nil
}

func (m *AreaServiceable) GetActualPoints() []*ActualPoint {
	if m != nil {
		return m.ActualPoints
	}
	return nil
}

type ActualPoint struct {
	SiteId               *string  `protobuf:"bytes,1,req,name=site_id,json=siteId" json:"site_id,omitempty"`
	PointId              *string  `protobuf:"bytes,2,req,name=point_id,json=pointId" json:"point_id,omitempty"`
	SiteSubType          *int32   `protobuf:"varint,3,req,name=site_sub_type,json=siteSubType" json:"site_sub_type,omitempty"`
	PointType            *int32   `protobuf:"varint,4,req,name=point_type,json=pointType" json:"point_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActualPoint) Reset()         { *m = ActualPoint{} }
func (m *ActualPoint) String() string { return proto.CompactTextString(m) }
func (*ActualPoint) ProtoMessage()    {}
func (*ActualPoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{4}
}

func (m *ActualPoint) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActualPoint.Unmarshal(m, b)
}
func (m *ActualPoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActualPoint.Marshal(b, m, deterministic)
}
func (m *ActualPoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActualPoint.Merge(m, src)
}
func (m *ActualPoint) XXX_Size() int {
	return xxx_messageInfo_ActualPoint.Size(m)
}
func (m *ActualPoint) XXX_DiscardUnknown() {
	xxx_messageInfo_ActualPoint.DiscardUnknown(m)
}

var xxx_messageInfo_ActualPoint proto.InternalMessageInfo

func (m *ActualPoint) GetSiteId() string {
	if m != nil && m.SiteId != nil {
		return *m.SiteId
	}
	return ""
}

func (m *ActualPoint) GetPointId() string {
	if m != nil && m.PointId != nil {
		return *m.PointId
	}
	return ""
}

func (m *ActualPoint) GetSiteSubType() int32 {
	if m != nil && m.SiteSubType != nil {
		return *m.SiteSubType
	}
	return 0
}

func (m *ActualPoint) GetPointType() int32 {
	if m != nil && m.PointType != nil {
		return *m.PointType
	}
	return 0
}

type LaneAreaServiceable struct {
	LaneCode             *string          `protobuf:"bytes,1,req,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	Serviceable          *AreaServiceable `protobuf:"bytes,2,req,name=serviceable" json:"serviceable,omitempty"`
	LaneCodeGroup        []string         `protobuf:"bytes,3,rep,name=lane_code_group,json=laneCodeGroup" json:"lane_code_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *LaneAreaServiceable) Reset()         { *m = LaneAreaServiceable{} }
func (m *LaneAreaServiceable) String() string { return proto.CompactTextString(m) }
func (*LaneAreaServiceable) ProtoMessage()    {}
func (*LaneAreaServiceable) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{5}
}

func (m *LaneAreaServiceable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LaneAreaServiceable.Unmarshal(m, b)
}
func (m *LaneAreaServiceable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LaneAreaServiceable.Marshal(b, m, deterministic)
}
func (m *LaneAreaServiceable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LaneAreaServiceable.Merge(m, src)
}
func (m *LaneAreaServiceable) XXX_Size() int {
	return xxx_messageInfo_LaneAreaServiceable.Size(m)
}
func (m *LaneAreaServiceable) XXX_DiscardUnknown() {
	xxx_messageInfo_LaneAreaServiceable.DiscardUnknown(m)
}

var xxx_messageInfo_LaneAreaServiceable proto.InternalMessageInfo

func (m *LaneAreaServiceable) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *LaneAreaServiceable) GetServiceable() *AreaServiceable {
	if m != nil {
		return m.Serviceable
	}
	return nil
}

func (m *LaneAreaServiceable) GetLaneCodeGroup() []string {
	if m != nil {
		return m.LaneCodeGroup
	}
	return nil
}

type LaneAreaServiceabilityReq struct {
	LaneCodes            []string            `protobuf:"bytes,1,rep,name=lane_codes,json=laneCodes" json:"lane_codes,omitempty"`
	UniqueId             *string             `protobuf:"bytes,2,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ProductId            *int32              `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id,omitempty"`
	Region               *string             `protobuf:"bytes,4,req,name=region" json:"region,omitempty"`
	Basis                *ServiceableBasis   `protobuf:"bytes,5,req,name=basis" json:"basis,omitempty"`
	PickupAddr           *ServiceableAddress `protobuf:"bytes,6,opt,name=pickup_addr,json=pickupAddr" json:"pickup_addr,omitempty"`
	DeliverAddr          *ServiceableAddress `protobuf:"bytes,7,opt,name=deliver_addr,json=deliverAddr" json:"deliver_addr,omitempty"`
	ItemId               *uint64             `protobuf:"varint,8,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	HandoverPointAddr    *ServiceableAddress `protobuf:"bytes,9,opt,name=handover_point_addr,json=handoverPointAddr" json:"handover_point_addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *LaneAreaServiceabilityReq) Reset()         { *m = LaneAreaServiceabilityReq{} }
func (m *LaneAreaServiceabilityReq) String() string { return proto.CompactTextString(m) }
func (*LaneAreaServiceabilityReq) ProtoMessage()    {}
func (*LaneAreaServiceabilityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{6}
}

func (m *LaneAreaServiceabilityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LaneAreaServiceabilityReq.Unmarshal(m, b)
}
func (m *LaneAreaServiceabilityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LaneAreaServiceabilityReq.Marshal(b, m, deterministic)
}
func (m *LaneAreaServiceabilityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LaneAreaServiceabilityReq.Merge(m, src)
}
func (m *LaneAreaServiceabilityReq) XXX_Size() int {
	return xxx_messageInfo_LaneAreaServiceabilityReq.Size(m)
}
func (m *LaneAreaServiceabilityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LaneAreaServiceabilityReq.DiscardUnknown(m)
}

var xxx_messageInfo_LaneAreaServiceabilityReq proto.InternalMessageInfo

func (m *LaneAreaServiceabilityReq) GetLaneCodes() []string {
	if m != nil {
		return m.LaneCodes
	}
	return nil
}

func (m *LaneAreaServiceabilityReq) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *LaneAreaServiceabilityReq) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *LaneAreaServiceabilityReq) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *LaneAreaServiceabilityReq) GetBasis() *ServiceableBasis {
	if m != nil {
		return m.Basis
	}
	return nil
}

func (m *LaneAreaServiceabilityReq) GetPickupAddr() *ServiceableAddress {
	if m != nil {
		return m.PickupAddr
	}
	return nil
}

func (m *LaneAreaServiceabilityReq) GetDeliverAddr() *ServiceableAddress {
	if m != nil {
		return m.DeliverAddr
	}
	return nil
}

func (m *LaneAreaServiceabilityReq) GetItemId() uint64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *LaneAreaServiceabilityReq) GetHandoverPointAddr() *ServiceableAddress {
	if m != nil {
		return m.HandoverPointAddr
	}
	return nil
}

type LaneInfo struct {
	Sequence             *int32   `protobuf:"varint,1,req,name=sequence" json:"sequence,omitempty"`
	ResourceId           *string  `protobuf:"bytes,2,req,name=resource_id,json=resourceId" json:"resource_id,omitempty"`
	ResourceType         *int32   `protobuf:"varint,3,req,name=resource_type,json=resourceType" json:"resource_type,omitempty"`
	ResourceSubType      *int32   `protobuf:"varint,4,req,name=resource_sub_type,json=resourceSubType" json:"resource_sub_type,omitempty"`
	NeedCheck            *int32   `protobuf:"varint,5,req,name=need_check,json=needCheck" json:"need_check,omitempty"`
	ActualPointId        []string `protobuf:"bytes,6,rep,name=actual_point_id,json=actualPointId" json:"actual_point_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LaneInfo) Reset()         { *m = LaneInfo{} }
func (m *LaneInfo) String() string { return proto.CompactTextString(m) }
func (*LaneInfo) ProtoMessage()    {}
func (*LaneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{7}
}

func (m *LaneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LaneInfo.Unmarshal(m, b)
}
func (m *LaneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LaneInfo.Marshal(b, m, deterministic)
}
func (m *LaneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LaneInfo.Merge(m, src)
}
func (m *LaneInfo) XXX_Size() int {
	return xxx_messageInfo_LaneInfo.Size(m)
}
func (m *LaneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LaneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LaneInfo proto.InternalMessageInfo

func (m *LaneInfo) GetSequence() int32 {
	if m != nil && m.Sequence != nil {
		return *m.Sequence
	}
	return 0
}

func (m *LaneInfo) GetResourceId() string {
	if m != nil && m.ResourceId != nil {
		return *m.ResourceId
	}
	return ""
}

func (m *LaneInfo) GetResourceType() int32 {
	if m != nil && m.ResourceType != nil {
		return *m.ResourceType
	}
	return 0
}

func (m *LaneInfo) GetResourceSubType() int32 {
	if m != nil && m.ResourceSubType != nil {
		return *m.ResourceSubType
	}
	return 0
}

func (m *LaneInfo) GetNeedCheck() int32 {
	if m != nil && m.NeedCheck != nil {
		return *m.NeedCheck
	}
	return 0
}

func (m *LaneInfo) GetActualPointId() []string {
	if m != nil {
		return m.ActualPointId
	}
	return nil
}

type BatchCheckProductServiceableReq struct {
	ReqHeader            *ReqHeader                   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductList          []*LaneAreaServiceabilityReq `protobuf:"bytes,2,rep,name=product_list,json=productList" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchCheckProductServiceableReq) Reset()         { *m = BatchCheckProductServiceableReq{} }
func (m *BatchCheckProductServiceableReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckProductServiceableReq) ProtoMessage()    {}
func (*BatchCheckProductServiceableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{8}
}

func (m *BatchCheckProductServiceableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductServiceableReq.Unmarshal(m, b)
}
func (m *BatchCheckProductServiceableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductServiceableReq.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductServiceableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductServiceableReq.Merge(m, src)
}
func (m *BatchCheckProductServiceableReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductServiceableReq.Size(m)
}
func (m *BatchCheckProductServiceableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductServiceableReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductServiceableReq proto.InternalMessageInfo

func (m *BatchCheckProductServiceableReq) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckProductServiceableReq) GetProductList() []*LaneAreaServiceabilityReq {
	if m != nil {
		return m.ProductList
	}
	return nil
}

type ProductServiceableRsp struct {
	ProductId            *int32                 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	UniqueId             *string                `protobuf:"bytes,2,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	LaneList             []*LaneAreaServiceable `protobuf:"bytes,3,rep,name=lane_list,json=laneList" json:"lane_list,omitempty"`
	ItemId               *uint64                `protobuf:"varint,4,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ProductServiceableRsp) Reset()         { *m = ProductServiceableRsp{} }
func (m *ProductServiceableRsp) String() string { return proto.CompactTextString(m) }
func (*ProductServiceableRsp) ProtoMessage()    {}
func (*ProductServiceableRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{9}
}

func (m *ProductServiceableRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductServiceableRsp.Unmarshal(m, b)
}
func (m *ProductServiceableRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductServiceableRsp.Marshal(b, m, deterministic)
}
func (m *ProductServiceableRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductServiceableRsp.Merge(m, src)
}
func (m *ProductServiceableRsp) XXX_Size() int {
	return xxx_messageInfo_ProductServiceableRsp.Size(m)
}
func (m *ProductServiceableRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductServiceableRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ProductServiceableRsp proto.InternalMessageInfo

func (m *ProductServiceableRsp) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *ProductServiceableRsp) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *ProductServiceableRsp) GetLaneList() []*LaneAreaServiceable {
	if m != nil {
		return m.LaneList
	}
	return nil
}

func (m *ProductServiceableRsp) GetItemId() uint64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

type BatchCheckProductServiceableRsp struct {
	RespHeader           *RespHeader              `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ServiceableList      []*ProductServiceableRsp `protobuf:"bytes,2,rep,name=serviceable_list,json=serviceableList" json:"serviceable_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchCheckProductServiceableRsp) Reset()         { *m = BatchCheckProductServiceableRsp{} }
func (m *BatchCheckProductServiceableRsp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckProductServiceableRsp) ProtoMessage()    {}
func (*BatchCheckProductServiceableRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{10}
}

func (m *BatchCheckProductServiceableRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductServiceableRsp.Unmarshal(m, b)
}
func (m *BatchCheckProductServiceableRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductServiceableRsp.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductServiceableRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductServiceableRsp.Merge(m, src)
}
func (m *BatchCheckProductServiceableRsp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductServiceableRsp.Size(m)
}
func (m *BatchCheckProductServiceableRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductServiceableRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductServiceableRsp proto.InternalMessageInfo

func (m *BatchCheckProductServiceableRsp) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckProductServiceableRsp) GetServiceableList() []*ProductServiceableRsp {
	if m != nil {
		return m.ServiceableList
	}
	return nil
}

type RerouteLaneServiceableReq struct {
	ReqHeader            *ReqHeader            `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LaneServiceableList  []*LaneServiceableReq `protobuf:"bytes,2,rep,name=lane_serviceable_list,json=laneServiceableList" json:"lane_serviceable_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *RerouteLaneServiceableReq) Reset()         { *m = RerouteLaneServiceableReq{} }
func (m *RerouteLaneServiceableReq) String() string { return proto.CompactTextString(m) }
func (*RerouteLaneServiceableReq) ProtoMessage()    {}
func (*RerouteLaneServiceableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{11}
}

func (m *RerouteLaneServiceableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RerouteLaneServiceableReq.Unmarshal(m, b)
}
func (m *RerouteLaneServiceableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RerouteLaneServiceableReq.Marshal(b, m, deterministic)
}
func (m *RerouteLaneServiceableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RerouteLaneServiceableReq.Merge(m, src)
}
func (m *RerouteLaneServiceableReq) XXX_Size() int {
	return xxx_messageInfo_RerouteLaneServiceableReq.Size(m)
}
func (m *RerouteLaneServiceableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RerouteLaneServiceableReq.DiscardUnknown(m)
}

var xxx_messageInfo_RerouteLaneServiceableReq proto.InternalMessageInfo

func (m *RerouteLaneServiceableReq) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *RerouteLaneServiceableReq) GetLaneServiceableList() []*LaneServiceableReq {
	if m != nil {
		return m.LaneServiceableList
	}
	return nil
}

type RerouteLaneServiceableRsp struct {
	RespHeader           *RespHeader               `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ServiceableRsp       []*RerouteLaneServiceable `protobuf:"bytes,2,rep,name=serviceable_rsp,json=serviceableRsp" json:"serviceable_rsp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *RerouteLaneServiceableRsp) Reset()         { *m = RerouteLaneServiceableRsp{} }
func (m *RerouteLaneServiceableRsp) String() string { return proto.CompactTextString(m) }
func (*RerouteLaneServiceableRsp) ProtoMessage()    {}
func (*RerouteLaneServiceableRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{12}
}

func (m *RerouteLaneServiceableRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RerouteLaneServiceableRsp.Unmarshal(m, b)
}
func (m *RerouteLaneServiceableRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RerouteLaneServiceableRsp.Marshal(b, m, deterministic)
}
func (m *RerouteLaneServiceableRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RerouteLaneServiceableRsp.Merge(m, src)
}
func (m *RerouteLaneServiceableRsp) XXX_Size() int {
	return xxx_messageInfo_RerouteLaneServiceableRsp.Size(m)
}
func (m *RerouteLaneServiceableRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RerouteLaneServiceableRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RerouteLaneServiceableRsp proto.InternalMessageInfo

func (m *RerouteLaneServiceableRsp) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *RerouteLaneServiceableRsp) GetServiceableRsp() []*RerouteLaneServiceable {
	if m != nil {
		return m.ServiceableRsp
	}
	return nil
}

type LaneServiceableReq struct {
	LaneCodes            []string                 `protobuf:"bytes,1,rep,name=lane_codes,json=laneCodes" json:"lane_codes,omitempty"`
	UniqueId             *string                  `protobuf:"bytes,2,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ProductId            *int32                   `protobuf:"varint,3,req,name=product_id,json=productId" json:"product_id,omitempty"`
	Region               *string                  `protobuf:"bytes,4,req,name=region" json:"region,omitempty"`
	Basis                *RerouteServiceableBasis `protobuf:"bytes,5,req,name=basis" json:"basis,omitempty"`
	PickupAddr           *ServiceableAddress      `protobuf:"bytes,6,opt,name=pickup_addr,json=pickupAddr" json:"pickup_addr,omitempty"`
	DeliverAddr          *ServiceableAddress      `protobuf:"bytes,7,opt,name=deliver_addr,json=deliverAddr" json:"deliver_addr,omitempty"`
	HandoverPointAddr    *ServiceableAddress      `protobuf:"bytes,8,opt,name=handover_point_addr,json=handoverPointAddr" json:"handover_point_addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *LaneServiceableReq) Reset()         { *m = LaneServiceableReq{} }
func (m *LaneServiceableReq) String() string { return proto.CompactTextString(m) }
func (*LaneServiceableReq) ProtoMessage()    {}
func (*LaneServiceableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{13}
}

func (m *LaneServiceableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LaneServiceableReq.Unmarshal(m, b)
}
func (m *LaneServiceableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LaneServiceableReq.Marshal(b, m, deterministic)
}
func (m *LaneServiceableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LaneServiceableReq.Merge(m, src)
}
func (m *LaneServiceableReq) XXX_Size() int {
	return xxx_messageInfo_LaneServiceableReq.Size(m)
}
func (m *LaneServiceableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LaneServiceableReq.DiscardUnknown(m)
}

var xxx_messageInfo_LaneServiceableReq proto.InternalMessageInfo

func (m *LaneServiceableReq) GetLaneCodes() []string {
	if m != nil {
		return m.LaneCodes
	}
	return nil
}

func (m *LaneServiceableReq) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *LaneServiceableReq) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *LaneServiceableReq) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *LaneServiceableReq) GetBasis() *RerouteServiceableBasis {
	if m != nil {
		return m.Basis
	}
	return nil
}

func (m *LaneServiceableReq) GetPickupAddr() *ServiceableAddress {
	if m != nil {
		return m.PickupAddr
	}
	return nil
}

func (m *LaneServiceableReq) GetDeliverAddr() *ServiceableAddress {
	if m != nil {
		return m.DeliverAddr
	}
	return nil
}

func (m *LaneServiceableReq) GetHandoverPointAddr() *ServiceableAddress {
	if m != nil {
		return m.HandoverPointAddr
	}
	return nil
}

// only for reroute check, some params different from the normal one
type RerouteServiceableBasis struct {
	CheckOperation       *uint32                 `protobuf:"varint,1,req,name=check_operation,json=checkOperation" json:"check_operation,omitempty"`
	CheckBasic           *uint32                 `protobuf:"varint,2,req,name=check_basic,json=checkBasic" json:"check_basic,omitempty"`
	CollectType          *CollectTypeEnum        `protobuf:"varint,3,opt,name=collect_type,json=collectType,enum=lcos_protobuf.CollectTypeEnum" json:"collect_type,omitempty"`
	DeliverType          *DeliveryTypeEnum       `protobuf:"varint,4,opt,name=deliver_type,json=deliverType,enum=lcos_protobuf.DeliveryTypeEnum" json:"deliver_type,omitempty"`
	SkipPostalCode       *uint32                 `protobuf:"varint,5,opt,name=skip_postal_code,json=skipPostalCode" json:"skip_postal_code,omitempty"`
	PaymentMethod        *PaymentMethodEnum      `protobuf:"varint,6,opt,name=payment_method,json=paymentMethod,enum=lcos_protobuf.PaymentMethodEnum" json:"payment_method,omitempty"`
	SenderCheckLevel     *LocationCheckLevelEnum `protobuf:"varint,7,opt,name=sender_check_level,json=senderCheckLevel,enum=lcos_protobuf.LocationCheckLevelEnum" json:"sender_check_level,omitempty"`
	ReceiverCheckLevel   *LocationCheckLevelEnum `protobuf:"varint,8,opt,name=receiver_check_level,json=receiverCheckLevel,enum=lcos_protobuf.LocationCheckLevelEnum" json:"receiver_check_level,omitempty"`
	CheckSender          *uint32                 `protobuf:"varint,9,opt,name=check_sender,json=checkSender" json:"check_sender,omitempty"`
	CheckReceiver        *uint32                 `protobuf:"varint,10,opt,name=check_receiver,json=checkReceiver" json:"check_receiver,omitempty"`
	SkipZoneRoute        *uint32                 `protobuf:"varint,11,opt,name=skip_zone_route,json=skipZoneRoute" json:"skip_zone_route,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *RerouteServiceableBasis) Reset()         { *m = RerouteServiceableBasis{} }
func (m *RerouteServiceableBasis) String() string { return proto.CompactTextString(m) }
func (*RerouteServiceableBasis) ProtoMessage()    {}
func (*RerouteServiceableBasis) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{14}
}

func (m *RerouteServiceableBasis) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RerouteServiceableBasis.Unmarshal(m, b)
}
func (m *RerouteServiceableBasis) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RerouteServiceableBasis.Marshal(b, m, deterministic)
}
func (m *RerouteServiceableBasis) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RerouteServiceableBasis.Merge(m, src)
}
func (m *RerouteServiceableBasis) XXX_Size() int {
	return xxx_messageInfo_RerouteServiceableBasis.Size(m)
}
func (m *RerouteServiceableBasis) XXX_DiscardUnknown() {
	xxx_messageInfo_RerouteServiceableBasis.DiscardUnknown(m)
}

var xxx_messageInfo_RerouteServiceableBasis proto.InternalMessageInfo

func (m *RerouteServiceableBasis) GetCheckOperation() uint32 {
	if m != nil && m.CheckOperation != nil {
		return *m.CheckOperation
	}
	return 0
}

func (m *RerouteServiceableBasis) GetCheckBasic() uint32 {
	if m != nil && m.CheckBasic != nil {
		return *m.CheckBasic
	}
	return 0
}

func (m *RerouteServiceableBasis) GetCollectType() CollectTypeEnum {
	if m != nil && m.CollectType != nil {
		return *m.CollectType
	}
	return CollectTypeEnum_PICK_UP_COLLECT
}

func (m *RerouteServiceableBasis) GetDeliverType() DeliveryTypeEnum {
	if m != nil && m.DeliverType != nil {
		return *m.DeliverType
	}
	return DeliveryTypeEnum_TO_HOME
}

func (m *RerouteServiceableBasis) GetSkipPostalCode() uint32 {
	if m != nil && m.SkipPostalCode != nil {
		return *m.SkipPostalCode
	}
	return 0
}

func (m *RerouteServiceableBasis) GetPaymentMethod() PaymentMethodEnum {
	if m != nil && m.PaymentMethod != nil {
		return *m.PaymentMethod
	}
	return PaymentMethodEnum_STANDARD
}

func (m *RerouteServiceableBasis) GetSenderCheckLevel() LocationCheckLevelEnum {
	if m != nil && m.SenderCheckLevel != nil {
		return *m.SenderCheckLevel
	}
	return LocationCheckLevelEnum_STATE
}

func (m *RerouteServiceableBasis) GetReceiverCheckLevel() LocationCheckLevelEnum {
	if m != nil && m.ReceiverCheckLevel != nil {
		return *m.ReceiverCheckLevel
	}
	return LocationCheckLevelEnum_STATE
}

func (m *RerouteServiceableBasis) GetCheckSender() uint32 {
	if m != nil && m.CheckSender != nil {
		return *m.CheckSender
	}
	return 0
}

func (m *RerouteServiceableBasis) GetCheckReceiver() uint32 {
	if m != nil && m.CheckReceiver != nil {
		return *m.CheckReceiver
	}
	return 0
}

func (m *RerouteServiceableBasis) GetSkipZoneRoute() uint32 {
	if m != nil && m.SkipZoneRoute != nil {
		return *m.SkipZoneRoute
	}
	return 0
}

type RerouteLaneServiceable struct {
	ProductId            *int32                    `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	UniqueId             *string                   `protobuf:"bytes,2,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ServiceableList      []*RerouteServiceableArea `protobuf:"bytes,3,rep,name=serviceable_list,json=serviceableList" json:"serviceable_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *RerouteLaneServiceable) Reset()         { *m = RerouteLaneServiceable{} }
func (m *RerouteLaneServiceable) String() string { return proto.CompactTextString(m) }
func (*RerouteLaneServiceable) ProtoMessage()    {}
func (*RerouteLaneServiceable) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{15}
}

func (m *RerouteLaneServiceable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RerouteLaneServiceable.Unmarshal(m, b)
}
func (m *RerouteLaneServiceable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RerouteLaneServiceable.Marshal(b, m, deterministic)
}
func (m *RerouteLaneServiceable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RerouteLaneServiceable.Merge(m, src)
}
func (m *RerouteLaneServiceable) XXX_Size() int {
	return xxx_messageInfo_RerouteLaneServiceable.Size(m)
}
func (m *RerouteLaneServiceable) XXX_DiscardUnknown() {
	xxx_messageInfo_RerouteLaneServiceable.DiscardUnknown(m)
}

var xxx_messageInfo_RerouteLaneServiceable proto.InternalMessageInfo

func (m *RerouteLaneServiceable) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *RerouteLaneServiceable) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *RerouteLaneServiceable) GetServiceableList() []*RerouteServiceableArea {
	if m != nil {
		return m.ServiceableList
	}
	return nil
}

type RerouteServiceableArea struct {
	Code                 *int32         `protobuf:"varint,1,req,name=code" json:"code,omitempty"`
	Message              *string        `protobuf:"bytes,2,req,name=message" json:"message,omitempty"`
	LaneCode             *string        `protobuf:"bytes,3,req,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	Serviceable          []*Serviceable `protobuf:"bytes,4,rep,name=serviceable" json:"serviceable,omitempty"`
	LaneCodeGroup        []string       `protobuf:"bytes,5,rep,name=lane_code_group,json=laneCodeGroup" json:"lane_code_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RerouteServiceableArea) Reset()         { *m = RerouteServiceableArea{} }
func (m *RerouteServiceableArea) String() string { return proto.CompactTextString(m) }
func (*RerouteServiceableArea) ProtoMessage()    {}
func (*RerouteServiceableArea) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{16}
}

func (m *RerouteServiceableArea) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RerouteServiceableArea.Unmarshal(m, b)
}
func (m *RerouteServiceableArea) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RerouteServiceableArea.Marshal(b, m, deterministic)
}
func (m *RerouteServiceableArea) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RerouteServiceableArea.Merge(m, src)
}
func (m *RerouteServiceableArea) XXX_Size() int {
	return xxx_messageInfo_RerouteServiceableArea.Size(m)
}
func (m *RerouteServiceableArea) XXX_DiscardUnknown() {
	xxx_messageInfo_RerouteServiceableArea.DiscardUnknown(m)
}

var xxx_messageInfo_RerouteServiceableArea proto.InternalMessageInfo

func (m *RerouteServiceableArea) GetCode() int32 {
	if m != nil && m.Code != nil {
		return *m.Code
	}
	return 0
}

func (m *RerouteServiceableArea) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *RerouteServiceableArea) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *RerouteServiceableArea) GetServiceable() []*Serviceable {
	if m != nil {
		return m.Serviceable
	}
	return nil
}

func (m *RerouteServiceableArea) GetLaneCodeGroup() []string {
	if m != nil {
		return m.LaneCodeGroup
	}
	return nil
}

type Serviceable struct {
	Code                 *int32         `protobuf:"varint,1,req,name=code" json:"code,omitempty"`
	Message              *string        `protobuf:"bytes,2,req,name=message" json:"message,omitempty"`
	ResourceId           *string        `protobuf:"bytes,3,req,name=resource_id,json=resourceId" json:"resource_id,omitempty"`
	Sequence             *uint32        `protobuf:"varint,4,req,name=sequence" json:"sequence,omitempty"`
	CanPickup            *uint32        `protobuf:"varint,5,opt,name=can_pickup,json=canPickup" json:"can_pickup,omitempty"`
	CanCodPickup         *uint32        `protobuf:"varint,6,opt,name=can_cod_pickup,json=canCodPickup" json:"can_cod_pickup,omitempty"`
	CanDeliver           *uint32        `protobuf:"varint,7,opt,name=can_deliver,json=canDeliver" json:"can_deliver,omitempty"`
	CanCodDeliver        *uint32        `protobuf:"varint,8,opt,name=can_cod_deliver,json=canCodDeliver" json:"can_cod_deliver,omitempty"`
	ActualPoints         []*ActualPoint `protobuf:"bytes,9,rep,name=actual_points,json=actualPoints" json:"actual_points,omitempty"`
	PickupInEFence       *uint32        `protobuf:"varint,10,opt,name=pickup_in_e_fence,json=pickupInEFence" json:"pickup_in_e_fence,omitempty"`
	DeliverInEFence      *uint32        `protobuf:"varint,11,opt,name=deliver_in_e_fence,json=deliverInEFence" json:"deliver_in_e_fence,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *Serviceable) Reset()         { *m = Serviceable{} }
func (m *Serviceable) String() string { return proto.CompactTextString(m) }
func (*Serviceable) ProtoMessage()    {}
func (*Serviceable) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{17}
}

func (m *Serviceable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Serviceable.Unmarshal(m, b)
}
func (m *Serviceable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Serviceable.Marshal(b, m, deterministic)
}
func (m *Serviceable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Serviceable.Merge(m, src)
}
func (m *Serviceable) XXX_Size() int {
	return xxx_messageInfo_Serviceable.Size(m)
}
func (m *Serviceable) XXX_DiscardUnknown() {
	xxx_messageInfo_Serviceable.DiscardUnknown(m)
}

var xxx_messageInfo_Serviceable proto.InternalMessageInfo

func (m *Serviceable) GetCode() int32 {
	if m != nil && m.Code != nil {
		return *m.Code
	}
	return 0
}

func (m *Serviceable) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *Serviceable) GetResourceId() string {
	if m != nil && m.ResourceId != nil {
		return *m.ResourceId
	}
	return ""
}

func (m *Serviceable) GetSequence() uint32 {
	if m != nil && m.Sequence != nil {
		return *m.Sequence
	}
	return 0
}

func (m *Serviceable) GetCanPickup() uint32 {
	if m != nil && m.CanPickup != nil {
		return *m.CanPickup
	}
	return 0
}

func (m *Serviceable) GetCanCodPickup() uint32 {
	if m != nil && m.CanCodPickup != nil {
		return *m.CanCodPickup
	}
	return 0
}

func (m *Serviceable) GetCanDeliver() uint32 {
	if m != nil && m.CanDeliver != nil {
		return *m.CanDeliver
	}
	return 0
}

func (m *Serviceable) GetCanCodDeliver() uint32 {
	if m != nil && m.CanCodDeliver != nil {
		return *m.CanCodDeliver
	}
	return 0
}

func (m *Serviceable) GetActualPoints() []*ActualPoint {
	if m != nil {
		return m.ActualPoints
	}
	return nil
}

func (m *Serviceable) GetPickupInEFence() uint32 {
	if m != nil && m.PickupInEFence != nil {
		return *m.PickupInEFence
	}
	return 0
}

func (m *Serviceable) GetDeliverInEFence() uint32 {
	if m != nil && m.DeliverInEFence != nil {
		return *m.DeliverInEFence
	}
	return 0
}

type BatchCheckProductServiceableForItemSceneReq struct {
	ReqHeader            *ReqHeader                        `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ServiceableList      []*ItemSceneProductServiceableReq `protobuf:"bytes,2,rep,name=serviceable_list,json=serviceableList" json:"serviceable_list,omitempty"`
	ParcelLibraryQueries []*ParcelLibraryQuery             `protobuf:"bytes,3,rep,name=parcel_library_queries,json=parcelLibraryQueries" json:"parcel_library_queries,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *BatchCheckProductServiceableForItemSceneReq) Reset() {
	*m = BatchCheckProductServiceableForItemSceneReq{}
}
func (m *BatchCheckProductServiceableForItemSceneReq) String() string {
	return proto.CompactTextString(m)
}
func (*BatchCheckProductServiceableForItemSceneReq) ProtoMessage() {}
func (*BatchCheckProductServiceableForItemSceneReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{18}
}

func (m *BatchCheckProductServiceableForItemSceneReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductServiceableForItemSceneReq.Unmarshal(m, b)
}
func (m *BatchCheckProductServiceableForItemSceneReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductServiceableForItemSceneReq.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductServiceableForItemSceneReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductServiceableForItemSceneReq.Merge(m, src)
}
func (m *BatchCheckProductServiceableForItemSceneReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductServiceableForItemSceneReq.Size(m)
}
func (m *BatchCheckProductServiceableForItemSceneReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductServiceableForItemSceneReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductServiceableForItemSceneReq proto.InternalMessageInfo

func (m *BatchCheckProductServiceableForItemSceneReq) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckProductServiceableForItemSceneReq) GetServiceableList() []*ItemSceneProductServiceableReq {
	if m != nil {
		return m.ServiceableList
	}
	return nil
}

func (m *BatchCheckProductServiceableForItemSceneReq) GetParcelLibraryQueries() []*ParcelLibraryQuery {
	if m != nil {
		return m.ParcelLibraryQueries
	}
	return nil
}

type ItemSceneProductServiceableReq struct {
	Region               *string                 `protobuf:"bytes,1,req,name=region" json:"region,omitempty"`
	ProductList          []*ItemSceneProductInfo `protobuf:"bytes,2,rep,name=product_list,json=productList" json:"product_list,omitempty"`
	PickupAddr           *ServiceableAddress     `protobuf:"bytes,3,opt,name=pickup_addr,json=pickupAddr" json:"pickup_addr,omitempty"`
	DeliverAddr          *ServiceableAddress     `protobuf:"bytes,4,opt,name=deliver_addr,json=deliverAddr" json:"deliver_addr,omitempty"`
	HandoverPointAddr    *ServiceableAddress     `protobuf:"bytes,5,opt,name=handover_point_addr,json=handoverPointAddr" json:"handover_point_addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ItemSceneProductServiceableReq) Reset()         { *m = ItemSceneProductServiceableReq{} }
func (m *ItemSceneProductServiceableReq) String() string { return proto.CompactTextString(m) }
func (*ItemSceneProductServiceableReq) ProtoMessage()    {}
func (*ItemSceneProductServiceableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{19}
}

func (m *ItemSceneProductServiceableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemSceneProductServiceableReq.Unmarshal(m, b)
}
func (m *ItemSceneProductServiceableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemSceneProductServiceableReq.Marshal(b, m, deterministic)
}
func (m *ItemSceneProductServiceableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemSceneProductServiceableReq.Merge(m, src)
}
func (m *ItemSceneProductServiceableReq) XXX_Size() int {
	return xxx_messageInfo_ItemSceneProductServiceableReq.Size(m)
}
func (m *ItemSceneProductServiceableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemSceneProductServiceableReq.DiscardUnknown(m)
}

var xxx_messageInfo_ItemSceneProductServiceableReq proto.InternalMessageInfo

func (m *ItemSceneProductServiceableReq) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *ItemSceneProductServiceableReq) GetProductList() []*ItemSceneProductInfo {
	if m != nil {
		return m.ProductList
	}
	return nil
}

func (m *ItemSceneProductServiceableReq) GetPickupAddr() *ServiceableAddress {
	if m != nil {
		return m.PickupAddr
	}
	return nil
}

func (m *ItemSceneProductServiceableReq) GetDeliverAddr() *ServiceableAddress {
	if m != nil {
		return m.DeliverAddr
	}
	return nil
}

func (m *ItemSceneProductServiceableReq) GetHandoverPointAddr() *ServiceableAddress {
	if m != nil {
		return m.HandoverPointAddr
	}
	return nil
}

type ItemSceneProductInfo struct {
	UniqueId             *string           `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ProductId            *int32            `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	LaneCodes            []string          `protobuf:"bytes,3,rep,name=lane_codes,json=laneCodes" json:"lane_codes,omitempty"`
	Basis                *ServiceableBasis `protobuf:"bytes,4,req,name=basis" json:"basis,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ItemSceneProductInfo) Reset()         { *m = ItemSceneProductInfo{} }
func (m *ItemSceneProductInfo) String() string { return proto.CompactTextString(m) }
func (*ItemSceneProductInfo) ProtoMessage()    {}
func (*ItemSceneProductInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{20}
}

func (m *ItemSceneProductInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemSceneProductInfo.Unmarshal(m, b)
}
func (m *ItemSceneProductInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemSceneProductInfo.Marshal(b, m, deterministic)
}
func (m *ItemSceneProductInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemSceneProductInfo.Merge(m, src)
}
func (m *ItemSceneProductInfo) XXX_Size() int {
	return xxx_messageInfo_ItemSceneProductInfo.Size(m)
}
func (m *ItemSceneProductInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemSceneProductInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ItemSceneProductInfo proto.InternalMessageInfo

func (m *ItemSceneProductInfo) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *ItemSceneProductInfo) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *ItemSceneProductInfo) GetLaneCodes() []string {
	if m != nil {
		return m.LaneCodes
	}
	return nil
}

func (m *ItemSceneProductInfo) GetBasis() *ServiceableBasis {
	if m != nil {
		return m.Basis
	}
	return nil
}

type BatchCheckProductServiceableForItemSceneResp struct {
	RespHeader           *RespHeader                        `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ServiceableList      []*ItemSceneProductServiceableResp `protobuf:"bytes,2,rep,name=serviceable_list,json=serviceableList" json:"serviceable_list,omitempty"`
	ParcelLibraryInfoMap map[string]*ParcelLibraryInfo      `protobuf:"bytes,3,rep,name=parcel_library_info_map,json=parcelLibraryInfoMap" json:"parcel_library_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *BatchCheckProductServiceableForItemSceneResp) Reset() {
	*m = BatchCheckProductServiceableForItemSceneResp{}
}
func (m *BatchCheckProductServiceableForItemSceneResp) String() string {
	return proto.CompactTextString(m)
}
func (*BatchCheckProductServiceableForItemSceneResp) ProtoMessage() {}
func (*BatchCheckProductServiceableForItemSceneResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{21}
}

func (m *BatchCheckProductServiceableForItemSceneResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductServiceableForItemSceneResp.Unmarshal(m, b)
}
func (m *BatchCheckProductServiceableForItemSceneResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductServiceableForItemSceneResp.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductServiceableForItemSceneResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductServiceableForItemSceneResp.Merge(m, src)
}
func (m *BatchCheckProductServiceableForItemSceneResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductServiceableForItemSceneResp.Size(m)
}
func (m *BatchCheckProductServiceableForItemSceneResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductServiceableForItemSceneResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductServiceableForItemSceneResp proto.InternalMessageInfo

func (m *BatchCheckProductServiceableForItemSceneResp) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckProductServiceableForItemSceneResp) GetServiceableList() []*ItemSceneProductServiceableResp {
	if m != nil {
		return m.ServiceableList
	}
	return nil
}

func (m *BatchCheckProductServiceableForItemSceneResp) GetParcelLibraryInfoMap() map[string]*ParcelLibraryInfo {
	if m != nil {
		return m.ParcelLibraryInfoMap
	}
	return nil
}

type ItemSceneProductServiceableResp struct {
	UniqueId             *string                `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ProductId            *int32                 `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	LaneList             []*LaneAreaServiceable `protobuf:"bytes,3,rep,name=lane_list,json=laneList" json:"lane_list,omitempty"`
	ItemId               *uint64                `protobuf:"varint,4,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ItemSceneProductServiceableResp) Reset()         { *m = ItemSceneProductServiceableResp{} }
func (m *ItemSceneProductServiceableResp) String() string { return proto.CompactTextString(m) }
func (*ItemSceneProductServiceableResp) ProtoMessage()    {}
func (*ItemSceneProductServiceableResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{22}
}

func (m *ItemSceneProductServiceableResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemSceneProductServiceableResp.Unmarshal(m, b)
}
func (m *ItemSceneProductServiceableResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemSceneProductServiceableResp.Marshal(b, m, deterministic)
}
func (m *ItemSceneProductServiceableResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemSceneProductServiceableResp.Merge(m, src)
}
func (m *ItemSceneProductServiceableResp) XXX_Size() int {
	return xxx_messageInfo_ItemSceneProductServiceableResp.Size(m)
}
func (m *ItemSceneProductServiceableResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemSceneProductServiceableResp.DiscardUnknown(m)
}

var xxx_messageInfo_ItemSceneProductServiceableResp proto.InternalMessageInfo

func (m *ItemSceneProductServiceableResp) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *ItemSceneProductServiceableResp) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *ItemSceneProductServiceableResp) GetLaneList() []*LaneAreaServiceable {
	if m != nil {
		return m.LaneList
	}
	return nil
}

func (m *ItemSceneProductServiceableResp) GetItemId() uint64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

type BatchCheckShopServiceableReq struct {
	ReqHeader            *ReqHeader                 `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	CheckList            []*CheckShopServiceableReq `protobuf:"bytes,2,rep,name=check_list,json=checkList" json:"check_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BatchCheckShopServiceableReq) Reset()         { *m = BatchCheckShopServiceableReq{} }
func (m *BatchCheckShopServiceableReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckShopServiceableReq) ProtoMessage()    {}
func (*BatchCheckShopServiceableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{23}
}

func (m *BatchCheckShopServiceableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckShopServiceableReq.Unmarshal(m, b)
}
func (m *BatchCheckShopServiceableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckShopServiceableReq.Marshal(b, m, deterministic)
}
func (m *BatchCheckShopServiceableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckShopServiceableReq.Merge(m, src)
}
func (m *BatchCheckShopServiceableReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckShopServiceableReq.Size(m)
}
func (m *BatchCheckShopServiceableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckShopServiceableReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckShopServiceableReq proto.InternalMessageInfo

func (m *BatchCheckShopServiceableReq) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckShopServiceableReq) GetCheckList() []*CheckShopServiceableReq {
	if m != nil {
		return m.CheckList
	}
	return nil
}

type ShopServiceableBasis struct {
	CheckSender          *uint32  `protobuf:"varint,1,opt,name=check_sender,json=checkSender" json:"check_sender,omitempty"`
	CheckReceiver        *uint32  `protobuf:"varint,2,opt,name=check_receiver,json=checkReceiver" json:"check_receiver,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShopServiceableBasis) Reset()         { *m = ShopServiceableBasis{} }
func (m *ShopServiceableBasis) String() string { return proto.CompactTextString(m) }
func (*ShopServiceableBasis) ProtoMessage()    {}
func (*ShopServiceableBasis) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{24}
}

func (m *ShopServiceableBasis) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShopServiceableBasis.Unmarshal(m, b)
}
func (m *ShopServiceableBasis) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShopServiceableBasis.Marshal(b, m, deterministic)
}
func (m *ShopServiceableBasis) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShopServiceableBasis.Merge(m, src)
}
func (m *ShopServiceableBasis) XXX_Size() int {
	return xxx_messageInfo_ShopServiceableBasis.Size(m)
}
func (m *ShopServiceableBasis) XXX_DiscardUnknown() {
	xxx_messageInfo_ShopServiceableBasis.DiscardUnknown(m)
}

var xxx_messageInfo_ShopServiceableBasis proto.InternalMessageInfo

func (m *ShopServiceableBasis) GetCheckSender() uint32 {
	if m != nil && m.CheckSender != nil {
		return *m.CheckSender
	}
	return 0
}

func (m *ShopServiceableBasis) GetCheckReceiver() uint32 {
	if m != nil && m.CheckReceiver != nil {
		return *m.CheckReceiver
	}
	return 0
}

type CheckShopServiceableReq struct {
	UniqueId             *string               `protobuf:"bytes,1,opt,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	Region               *string               `protobuf:"bytes,2,opt,name=region" json:"region,omitempty"`
	ShopId               *int32                `protobuf:"varint,3,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	ProductId            *int32                `protobuf:"varint,4,opt,name=product_id,json=productId" json:"product_id,omitempty"`
	Basis                *ShopServiceableBasis `protobuf:"bytes,5,opt,name=basis" json:"basis,omitempty"`
	PickupAddr           *ServiceableAddress   `protobuf:"bytes,6,opt,name=pickup_addr,json=pickupAddr" json:"pickup_addr,omitempty"`
	DeliverAddr          *ServiceableAddress   `protobuf:"bytes,7,opt,name=deliver_addr,json=deliverAddr" json:"deliver_addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CheckShopServiceableReq) Reset()         { *m = CheckShopServiceableReq{} }
func (m *CheckShopServiceableReq) String() string { return proto.CompactTextString(m) }
func (*CheckShopServiceableReq) ProtoMessage()    {}
func (*CheckShopServiceableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{25}
}

func (m *CheckShopServiceableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckShopServiceableReq.Unmarshal(m, b)
}
func (m *CheckShopServiceableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckShopServiceableReq.Marshal(b, m, deterministic)
}
func (m *CheckShopServiceableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckShopServiceableReq.Merge(m, src)
}
func (m *CheckShopServiceableReq) XXX_Size() int {
	return xxx_messageInfo_CheckShopServiceableReq.Size(m)
}
func (m *CheckShopServiceableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckShopServiceableReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckShopServiceableReq proto.InternalMessageInfo

func (m *CheckShopServiceableReq) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *CheckShopServiceableReq) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *CheckShopServiceableReq) GetShopId() int32 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

func (m *CheckShopServiceableReq) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *CheckShopServiceableReq) GetBasis() *ShopServiceableBasis {
	if m != nil {
		return m.Basis
	}
	return nil
}

func (m *CheckShopServiceableReq) GetPickupAddr() *ServiceableAddress {
	if m != nil {
		return m.PickupAddr
	}
	return nil
}

func (m *CheckShopServiceableReq) GetDeliverAddr() *ServiceableAddress {
	if m != nil {
		return m.DeliverAddr
	}
	return nil
}

type BatchCheckShopServiceableResp struct {
	RespHeader           *RespHeader                 `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ServiceableList      []*CheckShopServiceableResp `protobuf:"bytes,2,rep,name=serviceable_list,json=serviceableList" json:"serviceable_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchCheckShopServiceableResp) Reset()         { *m = BatchCheckShopServiceableResp{} }
func (m *BatchCheckShopServiceableResp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckShopServiceableResp) ProtoMessage()    {}
func (*BatchCheckShopServiceableResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{26}
}

func (m *BatchCheckShopServiceableResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckShopServiceableResp.Unmarshal(m, b)
}
func (m *BatchCheckShopServiceableResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckShopServiceableResp.Marshal(b, m, deterministic)
}
func (m *BatchCheckShopServiceableResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckShopServiceableResp.Merge(m, src)
}
func (m *BatchCheckShopServiceableResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckShopServiceableResp.Size(m)
}
func (m *BatchCheckShopServiceableResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckShopServiceableResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckShopServiceableResp proto.InternalMessageInfo

func (m *BatchCheckShopServiceableResp) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckShopServiceableResp) GetServiceableList() []*CheckShopServiceableResp {
	if m != nil {
		return m.ServiceableList
	}
	return nil
}

type CheckShopServiceableResp struct {
	UniqueId             *string  `protobuf:"bytes,1,opt,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ProductId            *int32   `protobuf:"varint,2,opt,name=product_id,json=productId" json:"product_id,omitempty"`
	ShopId               *int32   `protobuf:"varint,3,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	CanPickup            *bool    `protobuf:"varint,4,opt,name=can_pickup,json=canPickup" json:"can_pickup,omitempty"`
	PickupZone           *string  `protobuf:"bytes,5,opt,name=pickup_zone,json=pickupZone" json:"pickup_zone,omitempty"`
	CanDelvier           *bool    `protobuf:"varint,6,opt,name=can_delvier,json=canDelvier" json:"can_delvier,omitempty"`
	DeliverZone          *string  `protobuf:"bytes,7,opt,name=deliver_zone,json=deliverZone" json:"deliver_zone,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckShopServiceableResp) Reset()         { *m = CheckShopServiceableResp{} }
func (m *CheckShopServiceableResp) String() string { return proto.CompactTextString(m) }
func (*CheckShopServiceableResp) ProtoMessage()    {}
func (*CheckShopServiceableResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{27}
}

func (m *CheckShopServiceableResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckShopServiceableResp.Unmarshal(m, b)
}
func (m *CheckShopServiceableResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckShopServiceableResp.Marshal(b, m, deterministic)
}
func (m *CheckShopServiceableResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckShopServiceableResp.Merge(m, src)
}
func (m *CheckShopServiceableResp) XXX_Size() int {
	return xxx_messageInfo_CheckShopServiceableResp.Size(m)
}
func (m *CheckShopServiceableResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckShopServiceableResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckShopServiceableResp proto.InternalMessageInfo

func (m *CheckShopServiceableResp) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *CheckShopServiceableResp) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *CheckShopServiceableResp) GetShopId() int32 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

func (m *CheckShopServiceableResp) GetCanPickup() bool {
	if m != nil && m.CanPickup != nil {
		return *m.CanPickup
	}
	return false
}

func (m *CheckShopServiceableResp) GetPickupZone() string {
	if m != nil && m.PickupZone != nil {
		return *m.PickupZone
	}
	return ""
}

func (m *CheckShopServiceableResp) GetCanDelvier() bool {
	if m != nil && m.CanDelvier != nil {
		return *m.CanDelvier
	}
	return false
}

func (m *CheckShopServiceableResp) GetDeliverZone() string {
	if m != nil && m.DeliverZone != nil {
		return *m.DeliverZone
	}
	return ""
}

type SearchProductServiceableZoneReq struct {
	ReqHeader            *ReqHeader                             `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	AddressList          []*SearchProductServiceableZoneAddress `protobuf:"bytes,2,rep,name=address_list,json=addressList" json:"address_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *SearchProductServiceableZoneReq) Reset()         { *m = SearchProductServiceableZoneReq{} }
func (m *SearchProductServiceableZoneReq) String() string { return proto.CompactTextString(m) }
func (*SearchProductServiceableZoneReq) ProtoMessage()    {}
func (*SearchProductServiceableZoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{28}
}

func (m *SearchProductServiceableZoneReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchProductServiceableZoneReq.Unmarshal(m, b)
}
func (m *SearchProductServiceableZoneReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchProductServiceableZoneReq.Marshal(b, m, deterministic)
}
func (m *SearchProductServiceableZoneReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchProductServiceableZoneReq.Merge(m, src)
}
func (m *SearchProductServiceableZoneReq) XXX_Size() int {
	return xxx_messageInfo_SearchProductServiceableZoneReq.Size(m)
}
func (m *SearchProductServiceableZoneReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchProductServiceableZoneReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchProductServiceableZoneReq proto.InternalMessageInfo

func (m *SearchProductServiceableZoneReq) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *SearchProductServiceableZoneReq) GetAddressList() []*SearchProductServiceableZoneAddress {
	if m != nil {
		return m.AddressList
	}
	return nil
}

type SearchProductServiceableZoneAddress struct {
	UniqueId             *string             `protobuf:"bytes,1,opt,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ProductId            *int32              `protobuf:"varint,2,opt,name=product_id,json=productId" json:"product_id,omitempty"`
	Address              *ServiceableAddress `protobuf:"bytes,3,opt,name=address" json:"address,omitempty"`
	ShopId               *int32              `protobuf:"varint,4,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SearchProductServiceableZoneAddress) Reset()         { *m = SearchProductServiceableZoneAddress{} }
func (m *SearchProductServiceableZoneAddress) String() string { return proto.CompactTextString(m) }
func (*SearchProductServiceableZoneAddress) ProtoMessage()    {}
func (*SearchProductServiceableZoneAddress) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{29}
}

func (m *SearchProductServiceableZoneAddress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchProductServiceableZoneAddress.Unmarshal(m, b)
}
func (m *SearchProductServiceableZoneAddress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchProductServiceableZoneAddress.Marshal(b, m, deterministic)
}
func (m *SearchProductServiceableZoneAddress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchProductServiceableZoneAddress.Merge(m, src)
}
func (m *SearchProductServiceableZoneAddress) XXX_Size() int {
	return xxx_messageInfo_SearchProductServiceableZoneAddress.Size(m)
}
func (m *SearchProductServiceableZoneAddress) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchProductServiceableZoneAddress.DiscardUnknown(m)
}

var xxx_messageInfo_SearchProductServiceableZoneAddress proto.InternalMessageInfo

func (m *SearchProductServiceableZoneAddress) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SearchProductServiceableZoneAddress) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *SearchProductServiceableZoneAddress) GetAddress() *ServiceableAddress {
	if m != nil {
		return m.Address
	}
	return nil
}

func (m *SearchProductServiceableZoneAddress) GetShopId() int32 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

type SearchProductServiceableZoneResp struct {
	RespHeader           *RespHeader                           `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ZoneList             []*SearchProductServiceableZoneResult `protobuf:"bytes,2,rep,name=zone_list,json=zoneList" json:"zone_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *SearchProductServiceableZoneResp) Reset()         { *m = SearchProductServiceableZoneResp{} }
func (m *SearchProductServiceableZoneResp) String() string { return proto.CompactTextString(m) }
func (*SearchProductServiceableZoneResp) ProtoMessage()    {}
func (*SearchProductServiceableZoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{30}
}

func (m *SearchProductServiceableZoneResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchProductServiceableZoneResp.Unmarshal(m, b)
}
func (m *SearchProductServiceableZoneResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchProductServiceableZoneResp.Marshal(b, m, deterministic)
}
func (m *SearchProductServiceableZoneResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchProductServiceableZoneResp.Merge(m, src)
}
func (m *SearchProductServiceableZoneResp) XXX_Size() int {
	return xxx_messageInfo_SearchProductServiceableZoneResp.Size(m)
}
func (m *SearchProductServiceableZoneResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchProductServiceableZoneResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchProductServiceableZoneResp proto.InternalMessageInfo

func (m *SearchProductServiceableZoneResp) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *SearchProductServiceableZoneResp) GetZoneList() []*SearchProductServiceableZoneResult {
	if m != nil {
		return m.ZoneList
	}
	return nil
}

type SearchProductServiceableZoneResult struct {
	UniqueId             *string  `protobuf:"bytes,1,opt,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ZoneName             *string  `protobuf:"bytes,2,opt,name=zone_name,json=zoneName" json:"zone_name,omitempty"`
	Retcode              *int32   `protobuf:"varint,3,opt,name=retcode" json:"retcode,omitempty"`
	Message              *string  `protobuf:"bytes,4,opt,name=message" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchProductServiceableZoneResult) Reset()         { *m = SearchProductServiceableZoneResult{} }
func (m *SearchProductServiceableZoneResult) String() string { return proto.CompactTextString(m) }
func (*SearchProductServiceableZoneResult) ProtoMessage()    {}
func (*SearchProductServiceableZoneResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{31}
}

func (m *SearchProductServiceableZoneResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchProductServiceableZoneResult.Unmarshal(m, b)
}
func (m *SearchProductServiceableZoneResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchProductServiceableZoneResult.Marshal(b, m, deterministic)
}
func (m *SearchProductServiceableZoneResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchProductServiceableZoneResult.Merge(m, src)
}
func (m *SearchProductServiceableZoneResult) XXX_Size() int {
	return xxx_messageInfo_SearchProductServiceableZoneResult.Size(m)
}
func (m *SearchProductServiceableZoneResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchProductServiceableZoneResult.DiscardUnknown(m)
}

var xxx_messageInfo_SearchProductServiceableZoneResult proto.InternalMessageInfo

func (m *SearchProductServiceableZoneResult) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SearchProductServiceableZoneResult) GetZoneName() string {
	if m != nil && m.ZoneName != nil {
		return *m.ZoneName
	}
	return ""
}

func (m *SearchProductServiceableZoneResult) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *SearchProductServiceableZoneResult) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

type BatchGetProductServiceableRouteCodeReq struct {
	ReqHeader            *ReqHeader                           `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ReqList              []*GetProductServiceableRouteCodeReq `protobuf:"bytes,2,rep,name=req_list,json=reqList" json:"req_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *BatchGetProductServiceableRouteCodeReq) Reset() {
	*m = BatchGetProductServiceableRouteCodeReq{}
}
func (m *BatchGetProductServiceableRouteCodeReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetProductServiceableRouteCodeReq) ProtoMessage()    {}
func (*BatchGetProductServiceableRouteCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{32}
}

func (m *BatchGetProductServiceableRouteCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetProductServiceableRouteCodeReq.Unmarshal(m, b)
}
func (m *BatchGetProductServiceableRouteCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetProductServiceableRouteCodeReq.Marshal(b, m, deterministic)
}
func (m *BatchGetProductServiceableRouteCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetProductServiceableRouteCodeReq.Merge(m, src)
}
func (m *BatchGetProductServiceableRouteCodeReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetProductServiceableRouteCodeReq.Size(m)
}
func (m *BatchGetProductServiceableRouteCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetProductServiceableRouteCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetProductServiceableRouteCodeReq proto.InternalMessageInfo

func (m *BatchGetProductServiceableRouteCodeReq) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetProductServiceableRouteCodeReq) GetReqList() []*GetProductServiceableRouteCodeReq {
	if m != nil {
		return m.ReqList
	}
	return nil
}

type GetProductServiceableRouteCodeReq struct {
	UniqueId             *string  `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ProductId            *int32   `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	LaneCodes            []string `protobuf:"bytes,3,rep,name=lane_codes,json=laneCodes" json:"lane_codes,omitempty"`
	Region               *string  `protobuf:"bytes,4,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProductServiceableRouteCodeReq) Reset()         { *m = GetProductServiceableRouteCodeReq{} }
func (m *GetProductServiceableRouteCodeReq) String() string { return proto.CompactTextString(m) }
func (*GetProductServiceableRouteCodeReq) ProtoMessage()    {}
func (*GetProductServiceableRouteCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{33}
}

func (m *GetProductServiceableRouteCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductServiceableRouteCodeReq.Unmarshal(m, b)
}
func (m *GetProductServiceableRouteCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductServiceableRouteCodeReq.Marshal(b, m, deterministic)
}
func (m *GetProductServiceableRouteCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductServiceableRouteCodeReq.Merge(m, src)
}
func (m *GetProductServiceableRouteCodeReq) XXX_Size() int {
	return xxx_messageInfo_GetProductServiceableRouteCodeReq.Size(m)
}
func (m *GetProductServiceableRouteCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductServiceableRouteCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductServiceableRouteCodeReq proto.InternalMessageInfo

func (m *GetProductServiceableRouteCodeReq) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *GetProductServiceableRouteCodeReq) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *GetProductServiceableRouteCodeReq) GetLaneCodes() []string {
	if m != nil {
		return m.LaneCodes
	}
	return nil
}

func (m *GetProductServiceableRouteCodeReq) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type BatchGetProductServiceableRouteCodeResp struct {
	RespHeader           *RespHeader                           `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	RespList             []*GetProductServiceableRouteCodeResp `protobuf:"bytes,2,rep,name=resp_list,json=respList" json:"resp_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *BatchGetProductServiceableRouteCodeResp) Reset() {
	*m = BatchGetProductServiceableRouteCodeResp{}
}
func (m *BatchGetProductServiceableRouteCodeResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetProductServiceableRouteCodeResp) ProtoMessage()    {}
func (*BatchGetProductServiceableRouteCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{34}
}

func (m *BatchGetProductServiceableRouteCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetProductServiceableRouteCodeResp.Unmarshal(m, b)
}
func (m *BatchGetProductServiceableRouteCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetProductServiceableRouteCodeResp.Marshal(b, m, deterministic)
}
func (m *BatchGetProductServiceableRouteCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetProductServiceableRouteCodeResp.Merge(m, src)
}
func (m *BatchGetProductServiceableRouteCodeResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetProductServiceableRouteCodeResp.Size(m)
}
func (m *BatchGetProductServiceableRouteCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetProductServiceableRouteCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetProductServiceableRouteCodeResp proto.InternalMessageInfo

func (m *BatchGetProductServiceableRouteCodeResp) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetProductServiceableRouteCodeResp) GetRespList() []*GetProductServiceableRouteCodeResp {
	if m != nil {
		return m.RespList
	}
	return nil
}

type GetProductServiceableRouteCodeResp struct {
	UniqueId                  *string                         `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ProductId                 *int32                          `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	LaneServiceableRouteCodes []*LaneServiceableRouteCodeInfo `protobuf:"bytes,3,rep,name=lane_serviceable_route_codes,json=laneServiceableRouteCodes" json:"lane_serviceable_route_codes,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}                        `json:"-"`
	XXX_unrecognized          []byte                          `json:"-"`
	XXX_sizecache             int32                           `json:"-"`
}

func (m *GetProductServiceableRouteCodeResp) Reset()         { *m = GetProductServiceableRouteCodeResp{} }
func (m *GetProductServiceableRouteCodeResp) String() string { return proto.CompactTextString(m) }
func (*GetProductServiceableRouteCodeResp) ProtoMessage()    {}
func (*GetProductServiceableRouteCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{35}
}

func (m *GetProductServiceableRouteCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductServiceableRouteCodeResp.Unmarshal(m, b)
}
func (m *GetProductServiceableRouteCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductServiceableRouteCodeResp.Marshal(b, m, deterministic)
}
func (m *GetProductServiceableRouteCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductServiceableRouteCodeResp.Merge(m, src)
}
func (m *GetProductServiceableRouteCodeResp) XXX_Size() int {
	return xxx_messageInfo_GetProductServiceableRouteCodeResp.Size(m)
}
func (m *GetProductServiceableRouteCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductServiceableRouteCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductServiceableRouteCodeResp proto.InternalMessageInfo

func (m *GetProductServiceableRouteCodeResp) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *GetProductServiceableRouteCodeResp) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

func (m *GetProductServiceableRouteCodeResp) GetLaneServiceableRouteCodes() []*LaneServiceableRouteCodeInfo {
	if m != nil {
		return m.LaneServiceableRouteCodes
	}
	return nil
}

type LaneServiceableRouteCodeInfo struct {
	LaneCode             *string  `protobuf:"bytes,1,req,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	RouteCodes           []string `protobuf:"bytes,2,rep,name=route_codes,json=routeCodes" json:"route_codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LaneServiceableRouteCodeInfo) Reset()         { *m = LaneServiceableRouteCodeInfo{} }
func (m *LaneServiceableRouteCodeInfo) String() string { return proto.CompactTextString(m) }
func (*LaneServiceableRouteCodeInfo) ProtoMessage()    {}
func (*LaneServiceableRouteCodeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_640a234e8c37e683, []int{36}
}

func (m *LaneServiceableRouteCodeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LaneServiceableRouteCodeInfo.Unmarshal(m, b)
}
func (m *LaneServiceableRouteCodeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LaneServiceableRouteCodeInfo.Marshal(b, m, deterministic)
}
func (m *LaneServiceableRouteCodeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LaneServiceableRouteCodeInfo.Merge(m, src)
}
func (m *LaneServiceableRouteCodeInfo) XXX_Size() int {
	return xxx_messageInfo_LaneServiceableRouteCodeInfo.Size(m)
}
func (m *LaneServiceableRouteCodeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LaneServiceableRouteCodeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LaneServiceableRouteCodeInfo proto.InternalMessageInfo

func (m *LaneServiceableRouteCodeInfo) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *LaneServiceableRouteCodeInfo) GetRouteCodes() []string {
	if m != nil {
		return m.RouteCodes
	}
	return nil
}

func init() {
	proto.RegisterType((*ServiceableBasis)(nil), "lcos_protobuf.ServiceableBasis")
	proto.RegisterType((*AreaServiceability)(nil), "lcos_protobuf.AreaServiceability")
	proto.RegisterType((*ServiceableAddress)(nil), "lcos_protobuf.ServiceableAddress")
	proto.RegisterType((*AreaServiceable)(nil), "lcos_protobuf.AreaServiceable")
	proto.RegisterType((*ActualPoint)(nil), "lcos_protobuf.ActualPoint")
	proto.RegisterType((*LaneAreaServiceable)(nil), "lcos_protobuf.LaneAreaServiceable")
	proto.RegisterType((*LaneAreaServiceabilityReq)(nil), "lcos_protobuf.LaneAreaServiceabilityReq")
	proto.RegisterType((*LaneInfo)(nil), "lcos_protobuf.LaneInfo")
	proto.RegisterType((*BatchCheckProductServiceableReq)(nil), "lcos_protobuf.BatchCheckProductServiceableReq")
	proto.RegisterType((*ProductServiceableRsp)(nil), "lcos_protobuf.ProductServiceableRsp")
	proto.RegisterType((*BatchCheckProductServiceableRsp)(nil), "lcos_protobuf.BatchCheckProductServiceableRsp")
	proto.RegisterType((*RerouteLaneServiceableReq)(nil), "lcos_protobuf.RerouteLaneServiceableReq")
	proto.RegisterType((*RerouteLaneServiceableRsp)(nil), "lcos_protobuf.RerouteLaneServiceableRsp")
	proto.RegisterType((*LaneServiceableReq)(nil), "lcos_protobuf.LaneServiceableReq")
	proto.RegisterType((*RerouteServiceableBasis)(nil), "lcos_protobuf.RerouteServiceableBasis")
	proto.RegisterType((*RerouteLaneServiceable)(nil), "lcos_protobuf.RerouteLaneServiceable")
	proto.RegisterType((*RerouteServiceableArea)(nil), "lcos_protobuf.RerouteServiceableArea")
	proto.RegisterType((*Serviceable)(nil), "lcos_protobuf.Serviceable")
	proto.RegisterType((*BatchCheckProductServiceableForItemSceneReq)(nil), "lcos_protobuf.BatchCheckProductServiceableForItemSceneReq")
	proto.RegisterType((*ItemSceneProductServiceableReq)(nil), "lcos_protobuf.ItemSceneProductServiceableReq")
	proto.RegisterType((*ItemSceneProductInfo)(nil), "lcos_protobuf.ItemSceneProductInfo")
	proto.RegisterType((*BatchCheckProductServiceableForItemSceneResp)(nil), "lcos_protobuf.BatchCheckProductServiceableForItemSceneResp")
	proto.RegisterMapType((map[string]*ParcelLibraryInfo)(nil), "lcos_protobuf.BatchCheckProductServiceableForItemSceneResp.ParcelLibraryInfoMapEntry")
	proto.RegisterType((*ItemSceneProductServiceableResp)(nil), "lcos_protobuf.ItemSceneProductServiceableResp")
	proto.RegisterType((*BatchCheckShopServiceableReq)(nil), "lcos_protobuf.BatchCheckShopServiceableReq")
	proto.RegisterType((*ShopServiceableBasis)(nil), "lcos_protobuf.ShopServiceableBasis")
	proto.RegisterType((*CheckShopServiceableReq)(nil), "lcos_protobuf.CheckShopServiceableReq")
	proto.RegisterType((*BatchCheckShopServiceableResp)(nil), "lcos_protobuf.BatchCheckShopServiceableResp")
	proto.RegisterType((*CheckShopServiceableResp)(nil), "lcos_protobuf.CheckShopServiceableResp")
	proto.RegisterType((*SearchProductServiceableZoneReq)(nil), "lcos_protobuf.SearchProductServiceableZoneReq")
	proto.RegisterType((*SearchProductServiceableZoneAddress)(nil), "lcos_protobuf.SearchProductServiceableZoneAddress")
	proto.RegisterType((*SearchProductServiceableZoneResp)(nil), "lcos_protobuf.SearchProductServiceableZoneResp")
	proto.RegisterType((*SearchProductServiceableZoneResult)(nil), "lcos_protobuf.SearchProductServiceableZoneResult")
	proto.RegisterType((*BatchGetProductServiceableRouteCodeReq)(nil), "lcos_protobuf.BatchGetProductServiceableRouteCodeReq")
	proto.RegisterType((*GetProductServiceableRouteCodeReq)(nil), "lcos_protobuf.GetProductServiceableRouteCodeReq")
	proto.RegisterType((*BatchGetProductServiceableRouteCodeResp)(nil), "lcos_protobuf.BatchGetProductServiceableRouteCodeResp")
	proto.RegisterType((*GetProductServiceableRouteCodeResp)(nil), "lcos_protobuf.GetProductServiceableRouteCodeResp")
	proto.RegisterType((*LaneServiceableRouteCodeInfo)(nil), "lcos_protobuf.LaneServiceableRouteCodeInfo")
}

func init() {
	proto.RegisterFile("lcos_scene_serviceable.proto", fileDescriptor_640a234e8c37e683)
}

var fileDescriptor_640a234e8c37e683 = []byte{
	// 2445 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0xcd, 0x8f, 0x1c, 0x47,
	0x15, 0x4f, 0xcf, 0xc7, 0xee, 0xcc, 0x9b, 0xfd, 0x72, 0x79, 0x6d, 0xf7, 0x4e, 0xd6, 0xde, 0x75,
	0x3b, 0xb6, 0x27, 0xd8, 0x2c, 0x61, 0x95, 0x04, 0xb0, 0x23, 0x05, 0xdb, 0xb1, 0xcd, 0x88, 0x4d,
	0xe2, 0xf4, 0x26, 0x04, 0x10, 0x52, 0xd3, 0xdb, 0x5d, 0xeb, 0x6d, 0xb9, 0xa7, 0xbb, 0xa7, 0xab,
	0x7b, 0xc5, 0x72, 0x02, 0xc4, 0x0d, 0x4e, 0x1c, 0x22, 0x21, 0x21, 0x90, 0x10, 0x10, 0x24, 0x44,
	0x10, 0x07, 0x94, 0x03, 0x8a, 0x38, 0x23, 0x24, 0xfe, 0x03, 0xc4, 0x89, 0x23, 0x27, 0xce, 0x48,
	0xa8, 0x5e, 0x75, 0xcf, 0x54, 0x7f, 0xcc, 0x97, 0xc7, 0x31, 0x11, 0xe2, 0x36, 0xf5, 0xea, 0xd5,
	0xab, 0x57, 0xef, 0xab, 0x7e, 0xaf, 0x7a, 0x60, 0xd3, 0xb5, 0x7c, 0x66, 0x30, 0x8b, 0x7a, 0xd4,
	0x60, 0x34, 0x3c, 0x76, 0x2c, 0x6a, 0x1e, 0xb8, 0x74, 0x27, 0x08, 0xfd, 0xc8, 0x27, 0xcb, 0x38,
	0x8b, 0xbf, 0x0f, 0xe2, 0xc3, 0xf6, 0x2a, 0x0e, 0x0f, 0x4c, 0x96, 0xcc, 0xb7, 0xcf, 0x88, 0x79,
	0x33, 0xb4, 0xa8, 0x6b, 0xb8, 0xce, 0x81, 0x20, 0x6b, 0xff, 0x5c, 0x80, 0xb5, 0xfd, 0xa1, 0xb0,
	0xdb, 0x26, 0x73, 0x18, 0xb9, 0x0a, 0xab, 0xd6, 0x11, 0xb5, 0x1e, 0x19, 0x7e, 0x40, 0x43, 0x33,
	0x72, 0x7c, 0x4f, 0x55, 0xb6, 0x2b, 0x9d, 0x65, 0x7d, 0x05, 0xc9, 0x6f, 0xa6, 0x54, 0xb2, 0x05,
	0x2d, 0xc1, 0x78, 0x60, 0x32, 0xc7, 0x52, 0x2b, 0xc8, 0x04, 0x48, 0xe2, 0x92, 0x2c, 0x72, 0x0b,
	0x96, 0x2c, 0xdf, 0x75, 0xa9, 0x15, 0x19, 0xd1, 0x49, 0x40, 0xd5, 0xea, 0xb6, 0xd2, 0x59, 0xd9,
	0xbd, 0xb0, 0x93, 0x51, 0x76, 0xe7, 0x8e, 0x60, 0x79, 0xfb, 0x24, 0xa0, 0x77, 0xbd, 0xb8, 0xa7,
	0xb7, 0xac, 0x21, 0x81, 0xdc, 0x86, 0x25, 0x9b, 0xba, 0xce, 0x31, 0x0d, 0x85, 0x88, 0x1a, 0x8a,
	0xd8, 0xca, 0x89, 0x78, 0x4d, 0xb0, 0x9c, 0x0c, 0x65, 0x24, 0x8b, 0x50, 0xc6, 0xa7, 0xe0, 0x94,
	0xd0, 0x33, 0xf0, 0x59, 0x64, 0xba, 0x86, 0xe5, 0xdb, 0x54, 0xad, 0x6f, 0x2b, 0x9d, 0x65, 0x5d,
	0x9c, 0xf4, 0x01, 0xd2, 0xef, 0xf8, 0x36, 0x25, 0xf7, 0x61, 0x25, 0x30, 0x4f, 0x7a, 0xd4, 0x8b,
	0x8c, 0x1e, 0x8d, 0x8e, 0x7c, 0x5b, 0x5d, 0xc0, 0x1d, 0xb7, 0x73, 0x3b, 0x3e, 0x10, 0x4c, 0xaf,
	0x23, 0x0f, 0x6e, 0xb9, 0x1c, 0xc8, 0x24, 0xb2, 0x0f, 0x84, 0x51, 0xcf, 0xa6, 0xa1, 0x21, 0xf6,
	0x76, 0xe9, 0x31, 0x75, 0xd5, 0x45, 0x14, 0x76, 0x39, 0x27, 0x6c, 0xcf, 0xb7, 0xd0, 0xa2, 0x77,
	0x38, 0xe7, 0x1e, 0x67, 0x44, 0x89, 0x6b, 0x42, 0xc0, 0x90, 0x4a, 0xde, 0x85, 0xf5, 0x90, 0x5a,
	0x14, 0xcd, 0x21, 0x8b, 0x6d, 0xcc, 0x22, 0x96, 0xa4, 0x22, 0x24, 0xc1, 0x17, 0x61, 0x49, 0xc8,
	0x13, 0x5b, 0xaa, 0x4d, 0xb4, 0x8e, 0x70, 0xef, 0x3e, 0x92, 0xc8, 0x65, 0x10, 0xfe, 0x37, 0xd2,
	0xe5, 0x2a, 0x20, 0xd3, 0x32, 0x52, 0xf5, 0x84, 0x48, 0xae, 0xc0, 0x2a, 0x7b, 0xe4, 0x04, 0xc6,
	0xb7, 0x7d, 0x8f, 0x1a, 0xa1, 0x1f, 0x47, 0x54, 0x6d, 0x09, 0x3e, 0x4e, 0xfe, 0xba, 0xef, 0x51,
	0x9d, 0x13, 0xc9, 0x0e, 0x9c, 0x46, 0x3e, 0xca, 0x5d, 0x1d, 0x3a, 0x96, 0x71, 0x48, 0x3d, 0x8b,
	0xaa, 0x4b, 0xdb, 0x4a, 0xa7, 0xa1, 0x9f, 0xe2, 0x53, 0x77, 0x93, 0x99, 0x7b, 0x7c, 0x82, 0x5c,
	0x07, 0x12, 0x33, 0x9a, 0x67, 0x5f, 0x46, 0xf6, 0xb5, 0x98, 0xd1, 0x2c, 0xf7, 0xf3, 0x70, 0xca,
	0x61, 0x89, 0x89, 0xa2, 0xd0, 0xb4, 0xa9, 0xe1, 0x78, 0xea, 0xca, 0xb6, 0xd2, 0xa9, 0xeb, 0x2b,
	0x0e, 0xc3, 0x83, 0xbf, 0xcd, 0xc9, 0x5d, 0x8f, 0xbc, 0x08, 0x67, 0x93, 0xe8, 0x08, 0xa9, 0x4d,
	0x0f, 0x1d, 0x8f, 0xda, 0x89, 0xde, 0xab, 0x28, 0x7c, 0x5d, 0x84, 0xc8, 0x60, 0x52, 0xa8, 0xff,
	0x22, 0x9c, 0xcd, 0xf3, 0x63, 0x5c, 0x31, 0x75, 0x6d, 0xbb, 0xda, 0x69, 0xea, 0xeb, 0x41, 0x76,
	0x01, 0x0f, 0x2e, 0xa6, 0xfd, 0xa2, 0x02, 0xe4, 0x56, 0x48, 0xcd, 0x41, 0xce, 0x39, 0xae, 0x13,
	0x9d, 0x90, 0xf3, 0x00, 0x96, 0xe9, 0x19, 0x81, 0x63, 0x3d, 0x8a, 0x03, 0x55, 0x41, 0x73, 0x35,
	0x2d, 0xd3, 0x7b, 0x80, 0x04, 0xf2, 0x1c, 0xac, 0xf0, 0x69, 0xcb, 0xb7, 0x53, 0x96, 0x0a, 0xb2,
	0x2c, 0x59, 0xa6, 0x77, 0xc7, 0xb7, 0x13, 0x2e, 0x9e, 0x8d, 0xa6, 0x67, 0x24, 0x81, 0x8f, 0xb9,
	0xc6, 0xb3, 0xd1, 0xf4, 0x92, 0xe4, 0xe0, 0x9e, 0x49, 0xc5, 0xa4, 0x4c, 0xb5, 0xc4, 0x83, 0x28,
	0x27, 0xe5, 0x7b, 0x1e, 0x4e, 0x89, 0x6d, 0x0c, 0xc7, 0x33, 0x68, 0x62, 0x68, 0x91, 0x2e, 0x2b,
	0x62, 0xa2, 0xeb, 0xdd, 0x15, 0x66, 0xbe, 0x06, 0x24, 0xcd, 0x4e, 0x89, 0x77, 0x41, 0xa4, 0x56,
	0x32, 0x33, 0x60, 0xee, 0xc0, 0x1a, 0x8b, 0x83, 0xc0, 0x0f, 0xa3, 0xa1, 0x4b, 0x16, 0x85, 0xd8,
	0x84, 0x9e, 0xb8, 0x44, 0xfb, 0xa8, 0x02, 0x44, 0x2a, 0x4b, 0xb7, 0x6c, 0x3b, 0xa4, 0x8c, 0xf1,
	0x3c, 0x66, 0x91, 0x19, 0x51, 0xc3, 0x4d, 0x02, 0xdb, 0x70, 0xec, 0xc4, 0x5a, 0xab, 0x38, 0x91,
	0x06, 0x7c, 0xd7, 0xe6, 0x9b, 0x59, 0x4e, 0x74, 0x92, 0x61, 0x15, 0x56, 0x5b, 0xe1, 0x74, 0x89,
	0xf3, 0x05, 0x58, 0xb7, 0x1d, 0xc6, 0x43, 0x27, 0xca, 0x70, 0x0b, 0x03, 0x92, 0x74, 0x4e, 0x5a,
	0x71, 0x1d, 0x08, 0x8b, 0x42, 0x4a, 0xb3, 0xfc, 0xc2, 0x96, 0x6b, 0x62, 0x46, 0xe2, 0xde, 0x82,
	0x56, 0xbe, 0xee, 0x34, 0x75, 0x08, 0x86, 0x25, 0x67, 0x13, 0x9a, 0xae, 0xef, 0x3d, 0x74, 0xa2,
	0xd8, 0x16, 0xb6, 0x6b, 0xea, 0x43, 0x02, 0x69, 0x43, 0xc3, 0x35, 0x23, 0x31, 0xb9, 0x88, 0x93,
	0x83, 0x31, 0x39, 0x0b, 0x0b, 0x21, 0x7d, 0xc8, 0x0b, 0x74, 0x03, 0x67, 0x92, 0x91, 0xf6, 0x91,
	0x02, 0xab, 0x99, 0x30, 0x73, 0x29, 0x21, 0x50, 0xc3, 0xfd, 0x79, 0x29, 0xaf, 0xeb, 0xf8, 0x9b,
	0xa8, 0xb0, 0xd8, 0xa3, 0x8c, 0x99, 0x0f, 0x29, 0x16, 0xef, 0xa6, 0x9e, 0x0e, 0xc9, 0x4d, 0x58,
	0x4c, 0x82, 0x13, 0xed, 0xd0, 0xda, 0xbd, 0x98, 0xab, 0x2d, 0xc5, 0x28, 0xd6, 0xd3, 0x15, 0xe4,
	0x55, 0x58, 0x36, 0xad, 0x28, 0x36, 0x5d, 0x23, 0xf0, 0x1d, 0x2f, 0x62, 0x6a, 0x6d, 0xbb, 0xda,
	0x69, 0xed, 0xb6, 0xf3, 0x22, 0x90, 0xe7, 0x01, 0x67, 0xd1, 0x97, 0xcc, 0xe1, 0x80, 0x69, 0xdf,
	0x57, 0xa0, 0x25, 0xcd, 0x92, 0x73, 0xb0, 0xc8, 0x9c, 0x88, 0x0a, 0x77, 0x73, 0x3d, 0x17, 0xf8,
	0xb0, 0x6b, 0x93, 0x0d, 0x68, 0xe0, 0x16, 0xc2, 0xbb, 0x78, 0x02, 0x1c, 0x77, 0x6d, 0xa2, 0xc1,
	0x32, 0xae, 0x61, 0xf1, 0x41, 0x7a, 0xf9, 0xf0, 0x83, 0xb7, 0x38, 0x71, 0x3f, 0x3e, 0xc0, 0x8b,
	0xe1, 0x3c, 0x80, 0x58, 0x9e, 0x5c, 0x2d, 0x9c, 0xa1, 0x89, 0x14, 0x3e, 0xad, 0xfd, 0x44, 0x81,
	0xd3, 0x7b, 0xa6, 0x47, 0xf3, 0xa6, 0x7c, 0x16, 0x9a, 0xae, 0xe9, 0x89, 0x7c, 0x4f, 0x14, 0x6a,
	0x70, 0x02, 0x7a, 0xf3, 0x8b, 0xd0, 0x92, 0xae, 0x67, 0xd4, 0xaa, 0x55, 0xb8, 0xf2, 0x72, 0x12,
	0x75, 0x79, 0x09, 0xcf, 0xd3, 0x81, 0x78, 0xe3, 0x61, 0xe8, 0xc7, 0x81, 0x5a, 0xc5, 0x9a, 0xb2,
	0x9c, 0x6e, 0x72, 0x9f, 0x13, 0xb5, 0x3f, 0x56, 0x61, 0xa3, 0xa0, 0x9e, 0x70, 0x05, 0xed, 0xf3,
	0xb3, 0x0d, 0xa4, 0x30, 0x55, 0x41, 0x01, 0xcd, 0x54, 0x00, 0xe3, 0x67, 0x88, 0x3d, 0xa7, 0x1f,
	0xd3, 0xa1, 0xe9, 0x1a, 0x82, 0xd0, 0xb5, 0xd1, 0x2e, 0xa1, 0x6f, 0xc7, 0x56, 0x24, 0x12, 0x41,
	0xd8, 0x45, 0x50, 0xba, 0xb6, 0x14, 0x76, 0x35, 0xe1, 0x0d, 0x31, 0x22, 0x2f, 0x41, 0x9d, 0x23,
	0x01, 0xa6, 0xd6, 0xf1, 0xd0, 0xf9, 0x4b, 0x3a, 0x0f, 0x34, 0x74, 0xc1, 0x4d, 0x6e, 0x43, 0x2b,
	0xa9, 0x37, 0xa6, 0x6d, 0x87, 0x98, 0x01, 0xc5, 0x78, 0x2b, 0x96, 0x03, 0x1d, 0xc4, 0x2a, 0x3e,
	0x24, 0xaf, 0x0d, 0x61, 0x02, 0x0a, 0x59, 0x9c, 0x56, 0x48, 0x0a, 0x14, 0x50, 0xca, 0x39, 0x58,
	0x74, 0x22, 0xda, 0xe3, 0x87, 0xe6, 0x09, 0x55, 0xd3, 0x17, 0xf8, 0xb0, 0x6b, 0x93, 0xb7, 0xe0,
	0xf4, 0x91, 0xe9, 0xd9, 0x3e, 0x97, 0x2f, 0x22, 0x06, 0x77, 0x69, 0x4e, 0xbb, 0xcb, 0xa9, 0x74,
	0x35, 0xc6, 0x33, 0xa7, 0x6a, 0x7f, 0x57, 0xa0, 0xc1, 0xbd, 0xd7, 0xf5, 0x0e, 0x7d, 0x9e, 0xe4,
	0x8c, 0xf6, 0x63, 0xac, 0x9e, 0x22, 0x41, 0x07, 0x63, 0x5e, 0x3f, 0x42, 0xca, 0xfc, 0x38, 0xb4,
	0x24, 0x5f, 0x41, 0x4a, 0xea, 0xda, 0xe4, 0x12, 0x2c, 0x0f, 0x18, 0xa4, 0x48, 0x5f, 0x4a, 0x89,
	0x29, 0x06, 0x1a, 0x30, 0x0d, 0x52, 0x42, 0x44, 0xfc, 0x6a, 0x3a, 0x21, 0xa5, 0x85, 0x47, 0xa9,
	0x2d, 0xae, 0x4f, 0x74, 0x66, 0x5d, 0x6f, 0x72, 0x0a, 0xde, 0x9b, 0x3c, 0x3e, 0xe5, 0xf4, 0xe6,
	0x4a, 0x2d, 0x88, 0xf8, 0x94, 0x92, 0xb8, 0x6b, 0x6b, 0xef, 0x2b, 0xb0, 0x75, 0xdb, 0x8c, 0xac,
	0xa3, 0x3b, 0xe2, 0x02, 0xc5, 0xf0, 0x91, 0xa3, 0x9e, 0xf6, 0xc9, 0xe7, 0x00, 0x42, 0xda, 0x37,
	0x8e, 0xa8, 0xc9, 0x51, 0x87, 0x82, 0x71, 0xa3, 0xe6, 0xec, 0xa9, 0xd3, 0xfe, 0x97, 0x70, 0x5e,
	0x6f, 0x86, 0xe9, 0x4f, 0xf2, 0x65, 0x58, 0x4a, 0x43, 0xd4, 0x75, 0x58, 0xa4, 0x56, 0xb0, 0xc4,
	0x74, 0xf2, 0x08, 0x68, 0x54, 0x7a, 0xe8, 0xad, 0x64, 0xf5, 0x9e, 0xc3, 0x22, 0xed, 0xb7, 0x0a,
	0x9c, 0x29, 0xd1, 0x8f, 0x05, 0xb9, 0x4c, 0x50, 0xf2, 0x99, 0x30, 0x36, 0x8b, 0x5e, 0x4d, 0xca,
	0x04, 0xea, 0x57, 0x45, 0xfd, 0xb4, 0x49, 0xfa, 0xb9, 0x54, 0x94, 0x12, 0xae, 0x96, 0x1c, 0x8e,
	0x35, 0x39, 0x1c, 0xb5, 0x0f, 0x26, 0x59, 0x96, 0x05, 0xe4, 0x06, 0x86, 0x4d, 0x90, 0x35, 0xed,
	0x46, 0xc1, 0xb4, 0x2c, 0x48, 0x6c, 0xcb, 0x23, 0x2a, 0xf9, 0x4d, 0xde, 0x84, 0x35, 0xa9, 0x20,
	0xc9, 0x06, 0x7e, 0x2e, 0x0f, 0x83, 0xcb, 0xf6, 0xd6, 0x57, 0xa5, 0xd5, 0x68, 0xe0, 0xdf, 0x28,
	0xb0, 0xa1, 0x53, 0x44, 0x49, 0xfc, 0xc8, 0x4f, 0x2a, 0x08, 0xde, 0x81, 0x33, 0x68, 0xe1, 0x11,
	0xca, 0x5e, 0x2c, 0xb1, 0x76, 0x76, 0x6b, 0xfd, 0xb4, 0x9b, 0xa5, 0xa1, 0xb6, 0xef, 0x8f, 0xd6,
	0x76, 0x4e, 0xc3, 0xbe, 0x01, 0xb2, 0x69, 0x8c, 0x90, 0x05, 0x89, 0xaa, 0x97, 0x0b, 0xeb, 0x4b,
	0xb7, 0x5f, 0x61, 0x19, 0x5d, 0xb4, 0x9f, 0x57, 0x81, 0x94, 0x18, 0xf4, 0xbf, 0x50, 0xfb, 0x5f,
	0xc9, 0xd6, 0xfe, 0x2b, 0xe5, 0xe7, 0xf9, 0xe4, 0x5f, 0x01, 0x23, 0x2a, 0x7d, 0x63, 0x8e, 0x4a,
	0xff, 0xef, 0x1a, 0x9c, 0x1b, 0x71, 0xfe, 0xff, 0xbd, 0x5e, 0x9b, 0x83, 0x7c, 0xde, 0xd6, 0x15,
	0x5b, 0xed, 0x15, 0x4e, 0xff, 0x7f, 0xa7, 0xfd, 0xd4, 0x3b, 0x6d, 0xed, 0x57, 0x0a, 0x9c, 0x2d,
	0xaf, 0x27, 0x73, 0x5d, 0x6f, 0x0f, 0x4a, 0x2e, 0x89, 0xea, 0xb8, 0x62, 0x26, 0x67, 0x4b, 0x48,
	0xcd, 0xe2, 0x2d, 0xf1, 0xe7, 0xa1, 0xa2, 0x39, 0xde, 0x19, 0xbb, 0x97, 0x0c, 0x40, 0xaf, 0xe6,
	0x00, 0xfa, 0x2b, 0x59, 0x80, 0x5e, 0xde, 0x9b, 0xcc, 0x02, 0xce, 0xeb, 0x65, 0xe0, 0xfc, 0x67,
	0x55, 0x68, 0x3d, 0x7e, 0xfb, 0x95, 0xc3, 0x7c, 0xd5, 0x02, 0xe6, 0x93, 0x01, 0x63, 0x0d, 0x6b,
	0xc1, 0x10, 0x30, 0x66, 0x5f, 0x13, 0xea, 0x93, 0x5f, 0x13, 0x16, 0x26, 0xbf, 0x26, 0x2c, 0x4e,
	0xf3, 0x9a, 0xd0, 0x28, 0x7b, 0x4d, 0x28, 0x34, 0x83, 0xcd, 0xd9, 0x9a, 0xc1, 0xf2, 0xe7, 0x08,
	0x98, 0xe1, 0x39, 0xa2, 0x55, 0xfa, 0x1c, 0xa1, 0xfd, 0xb8, 0x02, 0xd7, 0xc6, 0x81, 0xa8, 0x7b,
	0x7e, 0xd8, 0x8d, 0x68, 0x6f, 0xdf, 0xa2, 0xde, 0x7c, 0x28, 0xe5, 0xab, 0x23, 0xd1, 0xd4, 0xa7,
	0x73, 0xcb, 0x07, 0xfb, 0x95, 0x82, 0xe5, 0x42, 0xc2, 0x90, 0x77, 0xe1, 0xec, 0xf0, 0x49, 0x37,
	0x34, 0xc3, 0x13, 0xa3, 0x1f, 0xd3, 0xd0, 0xa1, 0x2c, 0x49, 0xc4, 0x8b, 0x85, 0x52, 0xca, 0x99,
	0xf7, 0x04, 0xef, 0x5b, 0x31, 0x0d, 0x4f, 0xf4, 0xf5, 0x20, 0x4f, 0x73, 0x28, 0xd3, 0xfe, 0x56,
	0x81, 0x0b, 0xe3, 0x95, 0x91, 0x80, 0x80, 0x92, 0x01, 0x02, 0xf7, 0x4a, 0x81, 0xf9, 0xa5, 0x09,
	0x27, 0xe5, 0x5d, 0x50, 0x06, 0x93, 0xe7, 0x21, 0x41, 0xf5, 0x49, 0x40, 0x82, 0xda, 0x93, 0x84,
	0x04, 0xf5, 0x39, 0x20, 0xc1, 0xaf, 0x15, 0x58, 0x2f, 0x33, 0x41, 0xb6, 0xe2, 0x2a, 0x63, 0xa1,
	0x59, 0x25, 0x5f, 0xad, 0xb3, 0xa8, 0xaf, 0x9a, 0x47, 0x7d, 0x83, 0xee, 0xbc, 0x36, 0x4b, 0x77,
	0xae, 0xfd, 0xa9, 0x0a, 0xd7, 0xa7, 0x4f, 0x93, 0x39, 0xf1, 0xf1, 0xd7, 0x46, 0xa6, 0xca, 0xce,
	0x2c, 0xa9, 0x52, 0xd2, 0x82, 0x90, 0x1f, 0x2a, 0x70, 0x2e, 0x97, 0x2c, 0x8e, 0x77, 0xe8, 0x1b,
	0x3d, 0x33, 0x48, 0xb2, 0xe5, 0x9d, 0xdc, 0x16, 0xb3, 0x9c, 0x3a, 0x9b, 0x5a, 0xdc, 0x95, 0xaf,
	0x9b, 0xc1, 0x5d, 0x2f, 0x2a, 0x64, 0x58, 0x32, 0xd5, 0x76, 0x60, 0x63, 0xe4, 0x12, 0xb2, 0x06,
	0xd5, 0x47, 0xf4, 0x04, 0x9f, 0x36, 0x9b, 0x3a, 0xff, 0x49, 0x5e, 0x86, 0xfa, 0xb1, 0xe9, 0xc6,
	0x14, 0xdf, 0x30, 0x5b, 0x25, 0x18, 0x29, 0x27, 0x4a, 0x17, 0xec, 0x37, 0x2a, 0x9f, 0x57, 0xb4,
	0x0f, 0x15, 0xd8, 0x9a, 0x60, 0xae, 0xb9, 0xe2, 0xee, 0xe3, 0xeb, 0x73, 0x7f, 0xaa, 0xc0, 0xe6,
	0xd0, 0x0b, 0xfb, 0x47, 0x7e, 0xf0, 0xa4, 0x3a, 0xc7, 0xbb, 0x00, 0x09, 0xaa, 0x1b, 0x86, 0x58,
	0xbe, 0x67, 0x19, 0xb1, 0xa9, 0xde, 0xc4, 0x95, 0x88, 0x58, 0xbe, 0x09, 0xeb, 0x39, 0x06, 0x01,
	0xeb, 0xf3, 0x20, 0x4f, 0x99, 0x06, 0xe4, 0x55, 0x4a, 0x40, 0x9e, 0xf6, 0x97, 0x0a, 0x9c, 0x1b,
	0x75, 0xfa, 0x9c, 0xd3, 0x94, 0x8c, 0xd3, 0x86, 0xf5, 0xb9, 0x22, 0xbf, 0x0d, 0xe3, 0x5b, 0xea,
	0x91, 0x1f, 0xa4, 0x2f, 0xdc, 0x75, 0x7d, 0x81, 0x0f, 0x0b, 0x5e, 0xae, 0xe1, 0x9c, 0xe4, 0xe5,
	0x2f, 0x0c, 0x1b, 0x3c, 0xa5, 0xa4, 0xa0, 0x97, 0x99, 0xe1, 0x13, 0xd7, 0xdd, 0x69, 0xbf, 0x53,
	0xe0, 0xfc, 0x98, 0x80, 0x9a, 0xb3, 0x7a, 0xe9, 0x23, 0xab, 0xd7, 0xd5, 0xa9, 0x42, 0xab, 0xec,
	0xe5, 0xe4, 0x5f, 0x0a, 0xa8, 0x23, 0x95, 0x1d, 0x1b, 0x00, 0xf9, 0xac, 0xcd, 0xf9, 0x73, 0x5c,
	0x1c, 0x48, 0xf0, 0xb1, 0x86, 0xdf, 0xc0, 0x24, 0xf8, 0xb8, 0x35, 0x70, 0x26, 0xef, 0x3b, 0x06,
	0x9f, 0x33, 0x90, 0xc4, 0x7b, 0x0e, 0x09, 0x39, 0x1e, 0x3b, 0x54, 0x78, 0xbb, 0x91, 0x22, 0x47,
	0x4e, 0xe1, 0xc9, 0x91, 0xba, 0x12, 0x45, 0x88, 0xaf, 0x1a, 0xa9, 0x9f, 0xb8, 0x0c, 0xed, 0xf7,
	0x0a, 0x6c, 0xed, 0x53, 0x33, 0xb4, 0x8e, 0x8a, 0xf5, 0x0a, 0x1b, 0x9b, 0xf9, 0x5e, 0x8d, 0x96,
	0x4c, 0x11, 0x1c, 0xb2, 0x8b, 0x76, 0x0b, 0xa1, 0x34, 0x7a, 0xfb, 0x41, 0x6c, 0x25, 0x72, 0xd0,
	0x53, 0x1f, 0x2a, 0x70, 0x69, 0x8a, 0x45, 0x73, 0x39, 0xed, 0x26, 0x2c, 0x26, 0x5b, 0x4e, 0x0f,
	0x88, 0xd2, 0x15, 0xb2, 0xc7, 0x6b, 0xb2, 0xc7, 0xb5, 0x0f, 0x14, 0xd8, 0x1e, 0x6f, 0xed, 0xb9,
	0x9f, 0xbd, 0x9a, 0xd8, 0xa4, 0x4a, 0xe6, 0xfe, 0xec, 0x0c, 0xe6, 0xd6, 0x29, 0x8b, 0xdd, 0x48,
	0x6f, 0x70, 0x19, 0x68, 0xea, 0x1f, 0x29, 0xa0, 0x4d, 0x5e, 0x30, 0xde, 0xd2, 0xcf, 0x26, 0x3a,
	0x79, 0x66, 0x8f, 0x26, 0x25, 0x12, 0x37, 0x78, 0xc3, 0xec, 0x61, 0x67, 0x16, 0xd2, 0x28, 0x69,
	0x1f, 0xb9, 0xa9, 0xd2, 0xa1, 0xdc, 0xb3, 0xd5, 0x70, 0x51, 0x3a, 0xe4, 0x56, 0xbc, 0x82, 0xb5,
	0xe5, 0x3e, 0x8d, 0x4a, 0x6e, 0xd9, 0xf4, 0x23, 0xf0, 0x9c, 0xaf, 0xde, 0x0d, 0xbe, 0x50, 0xb2,
	0xe3, 0x0b, 0xb9, 0x65, 0x13, 0x37, 0xe7, 0x47, 0xe9, 0xa3, 0x15, 0xdf, 0x53, 0xe0, 0xe2, 0x64,
	0x5d, 0x3f, 0x46, 0x44, 0x3a, 0xe2, 0x2d, 0x51, 0xfb, 0x83, 0x02, 0x57, 0xa7, 0xb2, 0xe4, 0xfc,
	0x61, 0x89, 0x6b, 0xc7, 0x84, 0xe5, 0x64, 0x0d, 0xf4, 0x06, 0x97, 0x81, 0x06, 0xfd, 0xab, 0x02,
	0xda, 0x14, 0x2a, 0xcf, 0x63, 0x51, 0x17, 0x36, 0x0b, 0x2f, 0xde, 0xf2, 0x9f, 0x0f, 0x04, 0xfc,
	0xba, 0x36, 0xe1, 0xe1, 0x3b, 0x55, 0x07, 0x91, 0xe2, 0x86, 0x3b, 0x62, 0x96, 0x69, 0xdf, 0x80,
	0xcd, 0x71, 0x4b, 0xc7, 0x7f, 0x08, 0xdd, 0x82, 0x96, 0xac, 0x59, 0x05, 0xbd, 0x0f, 0xe1, 0x40,
	0xfa, 0xee, 0x3f, 0x1a, 0xb0, 0xbe, 0x67, 0xf9, 0x0c, 0x71, 0xa9, 0xfc, 0x56, 0xf2, 0x9d, 0x0c,
	0xec, 0x2b, 0x9a, 0x93, 0xec, 0xcc, 0x80, 0xd4, 0x75, 0xda, 0x6f, 0xcf, 0xc4, 0xcf, 0x02, 0xed,
	0x19, 0xf2, 0x3d, 0x05, 0x2e, 0x8c, 0xe3, 0xfa, 0xca, 0xee, 0x53, 0x50, 0xe2, 0x07, 0x69, 0x1e,
	0x8c, 0x6a, 0x42, 0x62, 0xf7, 0xd0, 0x71, 0xdd, 0x1e, 0xf5, 0xa2, 0xa7, 0xa0, 0xcd, 0x2f, 0x15,
	0xe8, 0x4c, 0xdb, 0x12, 0x91, 0x1b, 0x8f, 0xdd, 0x4b, 0xf5, 0xdb, 0x37, 0xe7, 0xe8, 0xc3, 0xb4,
	0x67, 0xc8, 0xb7, 0x60, 0x7b, 0xb8, 0xa2, 0xf0, 0x71, 0x04, 0xe3, 0x8f, 0x74, 0xa6, 0xfb, 0xdc,
	0x42, 0xfb, 0xed, 0x29, 0x39, 0x93, 0x9d, 0x37, 0x46, 0x82, 0x4b, 0x72, 0x6d, 0xe4, 0xa9, 0x8a,
	0xc8, 0xbe, 0x7d, 0x7d, 0x7a, 0x66, 0xdc, 0xf9, 0xbb, 0x0a, 0x6c, 0x8e, 0xbb, 0x10, 0x0b, 0xe1,
	0x31, 0x01, 0x5c, 0xb5, 0x3f, 0x33, 0xdb, 0xf5, 0xcc, 0x75, 0x78, 0x4f, 0x81, 0x4b, 0x53, 0x54,
	0x6d, 0xf2, 0x52, 0xd9, 0xd9, 0x26, 0xde, 0x43, 0xed, 0x97, 0x1f, 0x67, 0x19, 0x57, 0xec, 0x3f,
	0x01, 0x00, 0x00, 0xff, 0xff, 0x91, 0xa5, 0xef, 0xff, 0x3a, 0x29, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosSceneServiceableClient is the client API for LcosSceneServiceable service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosSceneServiceableClient interface {
	BatchCheckProductServiceable(ctx context.Context, in *BatchCheckProductServiceableReq, opts ...grpc.CallOption) (*BatchCheckProductServiceableRsp, error)
	BatchCheckProductServiceableV2(ctx context.Context, in *BatchCheckProductServiceableReq, opts ...grpc.CallOption) (*BatchCheckProductServiceableRsp, error)
	BatchCheckProductServiceableFulfillment(ctx context.Context, in *BatchCheckProductServiceableReq, opts ...grpc.CallOption) (*BatchCheckProductServiceableRsp, error)
	BatchCheckProductServiceableForItemScene(ctx context.Context, in *BatchCheckProductServiceableForItemSceneReq, opts ...grpc.CallOption) (*BatchCheckProductServiceableForItemSceneResp, error)
	BatchCheckLaneServiceableReroute(ctx context.Context, in *RerouteLaneServiceableReq, opts ...grpc.CallOption) (*RerouteLaneServiceableRsp, error)
	BatchCheckShopServiceable(ctx context.Context, in *BatchCheckShopServiceableReq, opts ...grpc.CallOption) (*BatchCheckShopServiceableResp, error)
	SearchProductServiceableZone(ctx context.Context, in *SearchProductServiceableZoneReq, opts ...grpc.CallOption) (*SearchProductServiceableZoneResp, error)
	BatchGetProductServiceableRouteCode(ctx context.Context, in *BatchGetProductServiceableRouteCodeReq, opts ...grpc.CallOption) (*BatchGetProductServiceableRouteCodeResp, error)
}

type lcosSceneServiceableClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosSceneServiceableClient(cc grpc.ClientConnInterface) LcosSceneServiceableClient {
	return &lcosSceneServiceableClient{cc}
}

func (c *lcosSceneServiceableClient) BatchCheckProductServiceable(ctx context.Context, in *BatchCheckProductServiceableReq, opts ...grpc.CallOption) (*BatchCheckProductServiceableRsp, error) {
	out := new(BatchCheckProductServiceableRsp)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosSceneServiceable/BatchCheckProductServiceable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosSceneServiceableClient) BatchCheckProductServiceableV2(ctx context.Context, in *BatchCheckProductServiceableReq, opts ...grpc.CallOption) (*BatchCheckProductServiceableRsp, error) {
	out := new(BatchCheckProductServiceableRsp)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosSceneServiceable/BatchCheckProductServiceableV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosSceneServiceableClient) BatchCheckProductServiceableFulfillment(ctx context.Context, in *BatchCheckProductServiceableReq, opts ...grpc.CallOption) (*BatchCheckProductServiceableRsp, error) {
	out := new(BatchCheckProductServiceableRsp)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosSceneServiceable/BatchCheckProductServiceableFulfillment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosSceneServiceableClient) BatchCheckProductServiceableForItemScene(ctx context.Context, in *BatchCheckProductServiceableForItemSceneReq, opts ...grpc.CallOption) (*BatchCheckProductServiceableForItemSceneResp, error) {
	out := new(BatchCheckProductServiceableForItemSceneResp)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosSceneServiceable/BatchCheckProductServiceableForItemScene", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosSceneServiceableClient) BatchCheckLaneServiceableReroute(ctx context.Context, in *RerouteLaneServiceableReq, opts ...grpc.CallOption) (*RerouteLaneServiceableRsp, error) {
	out := new(RerouteLaneServiceableRsp)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosSceneServiceable/BatchCheckLaneServiceableReroute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosSceneServiceableClient) BatchCheckShopServiceable(ctx context.Context, in *BatchCheckShopServiceableReq, opts ...grpc.CallOption) (*BatchCheckShopServiceableResp, error) {
	out := new(BatchCheckShopServiceableResp)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosSceneServiceable/BatchCheckShopServiceable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosSceneServiceableClient) SearchProductServiceableZone(ctx context.Context, in *SearchProductServiceableZoneReq, opts ...grpc.CallOption) (*SearchProductServiceableZoneResp, error) {
	out := new(SearchProductServiceableZoneResp)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosSceneServiceable/SearchProductServiceableZone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosSceneServiceableClient) BatchGetProductServiceableRouteCode(ctx context.Context, in *BatchGetProductServiceableRouteCodeReq, opts ...grpc.CallOption) (*BatchGetProductServiceableRouteCodeResp, error) {
	out := new(BatchGetProductServiceableRouteCodeResp)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosSceneServiceable/BatchGetProductServiceableRouteCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosSceneServiceableServer is the server API for LcosSceneServiceable service.
type LcosSceneServiceableServer interface {
	BatchCheckProductServiceable(context.Context, *BatchCheckProductServiceableReq) (*BatchCheckProductServiceableRsp, error)
	BatchCheckProductServiceableV2(context.Context, *BatchCheckProductServiceableReq) (*BatchCheckProductServiceableRsp, error)
	BatchCheckProductServiceableFulfillment(context.Context, *BatchCheckProductServiceableReq) (*BatchCheckProductServiceableRsp, error)
	BatchCheckProductServiceableForItemScene(context.Context, *BatchCheckProductServiceableForItemSceneReq) (*BatchCheckProductServiceableForItemSceneResp, error)
	BatchCheckLaneServiceableReroute(context.Context, *RerouteLaneServiceableReq) (*RerouteLaneServiceableRsp, error)
	BatchCheckShopServiceable(context.Context, *BatchCheckShopServiceableReq) (*BatchCheckShopServiceableResp, error)
	SearchProductServiceableZone(context.Context, *SearchProductServiceableZoneReq) (*SearchProductServiceableZoneResp, error)
	BatchGetProductServiceableRouteCode(context.Context, *BatchGetProductServiceableRouteCodeReq) (*BatchGetProductServiceableRouteCodeResp, error)
}

// UnimplementedLcosSceneServiceableServer can be embedded to have forward compatible implementations.
type UnimplementedLcosSceneServiceableServer struct {
}

func (*UnimplementedLcosSceneServiceableServer) BatchCheckProductServiceable(ctx context.Context, req *BatchCheckProductServiceableReq) (*BatchCheckProductServiceableRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckProductServiceable not implemented")
}
func (*UnimplementedLcosSceneServiceableServer) BatchCheckProductServiceableV2(ctx context.Context, req *BatchCheckProductServiceableReq) (*BatchCheckProductServiceableRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckProductServiceableV2 not implemented")
}
func (*UnimplementedLcosSceneServiceableServer) BatchCheckProductServiceableFulfillment(ctx context.Context, req *BatchCheckProductServiceableReq) (*BatchCheckProductServiceableRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckProductServiceableFulfillment not implemented")
}
func (*UnimplementedLcosSceneServiceableServer) BatchCheckProductServiceableForItemScene(ctx context.Context, req *BatchCheckProductServiceableForItemSceneReq) (*BatchCheckProductServiceableForItemSceneResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckProductServiceableForItemScene not implemented")
}
func (*UnimplementedLcosSceneServiceableServer) BatchCheckLaneServiceableReroute(ctx context.Context, req *RerouteLaneServiceableReq) (*RerouteLaneServiceableRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckLaneServiceableReroute not implemented")
}
func (*UnimplementedLcosSceneServiceableServer) BatchCheckShopServiceable(ctx context.Context, req *BatchCheckShopServiceableReq) (*BatchCheckShopServiceableResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckShopServiceable not implemented")
}
func (*UnimplementedLcosSceneServiceableServer) SearchProductServiceableZone(ctx context.Context, req *SearchProductServiceableZoneReq) (*SearchProductServiceableZoneResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchProductServiceableZone not implemented")
}
func (*UnimplementedLcosSceneServiceableServer) BatchGetProductServiceableRouteCode(ctx context.Context, req *BatchGetProductServiceableRouteCodeReq) (*BatchGetProductServiceableRouteCodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetProductServiceableRouteCode not implemented")
}

func RegisterLcosSceneServiceableServer(s *grpc.Server, srv LcosSceneServiceableServer) {
	s.RegisterService(&_LcosSceneServiceable_serviceDesc, srv)
}

func _LcosSceneServiceable_BatchCheckProductServiceable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckProductServiceableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosSceneServiceableServer).BatchCheckProductServiceable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosSceneServiceable/BatchCheckProductServiceable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosSceneServiceableServer).BatchCheckProductServiceable(ctx, req.(*BatchCheckProductServiceableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosSceneServiceable_BatchCheckProductServiceableV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckProductServiceableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosSceneServiceableServer).BatchCheckProductServiceableV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosSceneServiceable/BatchCheckProductServiceableV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosSceneServiceableServer).BatchCheckProductServiceableV2(ctx, req.(*BatchCheckProductServiceableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosSceneServiceable_BatchCheckProductServiceableFulfillment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckProductServiceableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosSceneServiceableServer).BatchCheckProductServiceableFulfillment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosSceneServiceable/BatchCheckProductServiceableFulfillment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosSceneServiceableServer).BatchCheckProductServiceableFulfillment(ctx, req.(*BatchCheckProductServiceableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosSceneServiceable_BatchCheckProductServiceableForItemScene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckProductServiceableForItemSceneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosSceneServiceableServer).BatchCheckProductServiceableForItemScene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosSceneServiceable/BatchCheckProductServiceableForItemScene",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosSceneServiceableServer).BatchCheckProductServiceableForItemScene(ctx, req.(*BatchCheckProductServiceableForItemSceneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosSceneServiceable_BatchCheckLaneServiceableReroute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RerouteLaneServiceableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosSceneServiceableServer).BatchCheckLaneServiceableReroute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosSceneServiceable/BatchCheckLaneServiceableReroute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosSceneServiceableServer).BatchCheckLaneServiceableReroute(ctx, req.(*RerouteLaneServiceableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosSceneServiceable_BatchCheckShopServiceable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckShopServiceableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosSceneServiceableServer).BatchCheckShopServiceable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosSceneServiceable/BatchCheckShopServiceable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosSceneServiceableServer).BatchCheckShopServiceable(ctx, req.(*BatchCheckShopServiceableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosSceneServiceable_SearchProductServiceableZone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchProductServiceableZoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosSceneServiceableServer).SearchProductServiceableZone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosSceneServiceable/SearchProductServiceableZone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosSceneServiceableServer).SearchProductServiceableZone(ctx, req.(*SearchProductServiceableZoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosSceneServiceable_BatchGetProductServiceableRouteCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetProductServiceableRouteCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosSceneServiceableServer).BatchGetProductServiceableRouteCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosSceneServiceable/BatchGetProductServiceableRouteCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosSceneServiceableServer).BatchGetProductServiceableRouteCode(ctx, req.(*BatchGetProductServiceableRouteCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosSceneServiceable_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosSceneServiceable",
	HandlerType: (*LcosSceneServiceableServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchCheckProductServiceable",
			Handler:    _LcosSceneServiceable_BatchCheckProductServiceable_Handler,
		},
		{
			MethodName: "BatchCheckProductServiceableV2",
			Handler:    _LcosSceneServiceable_BatchCheckProductServiceableV2_Handler,
		},
		{
			MethodName: "BatchCheckProductServiceableFulfillment",
			Handler:    _LcosSceneServiceable_BatchCheckProductServiceableFulfillment_Handler,
		},
		{
			MethodName: "BatchCheckProductServiceableForItemScene",
			Handler:    _LcosSceneServiceable_BatchCheckProductServiceableForItemScene_Handler,
		},
		{
			MethodName: "BatchCheckLaneServiceableReroute",
			Handler:    _LcosSceneServiceable_BatchCheckLaneServiceableReroute_Handler,
		},
		{
			MethodName: "BatchCheckShopServiceable",
			Handler:    _LcosSceneServiceable_BatchCheckShopServiceable_Handler,
		},
		{
			MethodName: "SearchProductServiceableZone",
			Handler:    _LcosSceneServiceable_SearchProductServiceableZone_Handler,
		},
		{
			MethodName: "BatchGetProductServiceableRouteCode",
			Handler:    _LcosSceneServiceable_BatchGetProductServiceableRouteCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_scene_serviceable.proto",
}
