// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_weight_limit.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GetTWSizeInfosRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductId            *string    `protobuf:"bytes,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	SizeId               *string    `protobuf:"bytes,3,opt,name=size_id,json=sizeId" json:"size_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTWSizeInfosRequest) Reset()         { *m = GetTWSizeInfosRequest{} }
func (m *GetTWSizeInfosRequest) String() string { return proto.CompactTextString(m) }
func (*GetTWSizeInfosRequest) ProtoMessage()    {}
func (*GetTWSizeInfosRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{0}
}

func (m *GetTWSizeInfosRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTWSizeInfosRequest.Unmarshal(m, b)
}
func (m *GetTWSizeInfosRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTWSizeInfosRequest.Marshal(b, m, deterministic)
}
func (m *GetTWSizeInfosRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTWSizeInfosRequest.Merge(m, src)
}
func (m *GetTWSizeInfosRequest) XXX_Size() int {
	return xxx_messageInfo_GetTWSizeInfosRequest.Size(m)
}
func (m *GetTWSizeInfosRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTWSizeInfosRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTWSizeInfosRequest proto.InternalMessageInfo

func (m *GetTWSizeInfosRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetTWSizeInfosRequest) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *GetTWSizeInfosRequest) GetSizeId() string {
	if m != nil && m.SizeId != nil {
		return *m.SizeId
	}
	return ""
}

type GetTWSizeInfosResponse struct {
	RespHeader           *RespHeader   `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	SizeInfos            []*TWSizeInfo `protobuf:"bytes,2,rep,name=size_infos,json=sizeInfos" json:"size_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetTWSizeInfosResponse) Reset()         { *m = GetTWSizeInfosResponse{} }
func (m *GetTWSizeInfosResponse) String() string { return proto.CompactTextString(m) }
func (*GetTWSizeInfosResponse) ProtoMessage()    {}
func (*GetTWSizeInfosResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{1}
}

func (m *GetTWSizeInfosResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTWSizeInfosResponse.Unmarshal(m, b)
}
func (m *GetTWSizeInfosResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTWSizeInfosResponse.Marshal(b, m, deterministic)
}
func (m *GetTWSizeInfosResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTWSizeInfosResponse.Merge(m, src)
}
func (m *GetTWSizeInfosResponse) XXX_Size() int {
	return xxx_messageInfo_GetTWSizeInfosResponse.Size(m)
}
func (m *GetTWSizeInfosResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTWSizeInfosResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTWSizeInfosResponse proto.InternalMessageInfo

func (m *GetTWSizeInfosResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetTWSizeInfosResponse) GetSizeInfos() []*TWSizeInfo {
	if m != nil {
		return m.SizeInfos
	}
	return nil
}

type TWSizeInfo struct {
	Id                   *uint64  `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	ProductId            *string  `protobuf:"bytes,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	SizeId               *string  `protobuf:"bytes,3,req,name=size_id,json=sizeId" json:"size_id,omitempty"`
	Name                 *string  `protobuf:"bytes,4,req,name=name" json:"name,omitempty"`
	Description          *string  `protobuf:"bytes,5,opt,name=description" json:"description,omitempty"`
	Country              *string  `protobuf:"bytes,6,opt,name=country" json:"country,omitempty"`
	Unit                 *string  `protobuf:"bytes,7,opt,name=unit" json:"unit,omitempty"`
	MaxSize              *float64 `protobuf:"fixed64,8,opt,name=max_size,json=maxSize" json:"max_size,omitempty"`
	MinSize              *float64 `protobuf:"fixed64,9,opt,name=min_size,json=minSize" json:"min_size,omitempty"`
	DefaultPrice         *float64 `protobuf:"fixed64,10,opt,name=default_price,json=defaultPrice" json:"default_price,omitempty"`
	ExtraData            *string  `protobuf:"bytes,11,opt,name=extra_data,json=extraData" json:"extra_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TWSizeInfo) Reset()         { *m = TWSizeInfo{} }
func (m *TWSizeInfo) String() string { return proto.CompactTextString(m) }
func (*TWSizeInfo) ProtoMessage()    {}
func (*TWSizeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{2}
}

func (m *TWSizeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TWSizeInfo.Unmarshal(m, b)
}
func (m *TWSizeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TWSizeInfo.Marshal(b, m, deterministic)
}
func (m *TWSizeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TWSizeInfo.Merge(m, src)
}
func (m *TWSizeInfo) XXX_Size() int {
	return xxx_messageInfo_TWSizeInfo.Size(m)
}
func (m *TWSizeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TWSizeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TWSizeInfo proto.InternalMessageInfo

func (m *TWSizeInfo) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *TWSizeInfo) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *TWSizeInfo) GetSizeId() string {
	if m != nil && m.SizeId != nil {
		return *m.SizeId
	}
	return ""
}

func (m *TWSizeInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *TWSizeInfo) GetDescription() string {
	if m != nil && m.Description != nil {
		return *m.Description
	}
	return ""
}

func (m *TWSizeInfo) GetCountry() string {
	if m != nil && m.Country != nil {
		return *m.Country
	}
	return ""
}

func (m *TWSizeInfo) GetUnit() string {
	if m != nil && m.Unit != nil {
		return *m.Unit
	}
	return ""
}

func (m *TWSizeInfo) GetMaxSize() float64 {
	if m != nil && m.MaxSize != nil {
		return *m.MaxSize
	}
	return 0
}

func (m *TWSizeInfo) GetMinSize() float64 {
	if m != nil && m.MinSize != nil {
		return *m.MinSize
	}
	return 0
}

func (m *TWSizeInfo) GetDefaultPrice() float64 {
	if m != nil && m.DefaultPrice != nil {
		return *m.DefaultPrice
	}
	return 0
}

func (m *TWSizeInfo) GetExtraData() string {
	if m != nil && m.ExtraData != nil {
		return *m.ExtraData
	}
	return ""
}

type CalculateFormulaRequest struct {
	ReqHeader            *ReqHeader              `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BaseInfo             *CalculateBaseInfoMulti `protobuf:"bytes,2,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	OrderInfo            *OrderInfo              `protobuf:"bytes,3,opt,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	SkuInfo              []*SkuInfo              `protobuf:"bytes,4,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *CalculateFormulaRequest) Reset()         { *m = CalculateFormulaRequest{} }
func (m *CalculateFormulaRequest) String() string { return proto.CompactTextString(m) }
func (*CalculateFormulaRequest) ProtoMessage()    {}
func (*CalculateFormulaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{3}
}

func (m *CalculateFormulaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculateFormulaRequest.Unmarshal(m, b)
}
func (m *CalculateFormulaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculateFormulaRequest.Marshal(b, m, deterministic)
}
func (m *CalculateFormulaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculateFormulaRequest.Merge(m, src)
}
func (m *CalculateFormulaRequest) XXX_Size() int {
	return xxx_messageInfo_CalculateFormulaRequest.Size(m)
}
func (m *CalculateFormulaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculateFormulaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CalculateFormulaRequest proto.InternalMessageInfo

func (m *CalculateFormulaRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *CalculateFormulaRequest) GetBaseInfo() *CalculateBaseInfoMulti {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *CalculateFormulaRequest) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *CalculateFormulaRequest) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

type CalculateFormulaResponse struct {
	RespHeader           *RespHeader                        `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	FormulaResult        map[uint32]*CalculateFormulaResult `protobuf:"bytes,2,rep,name=formula_result,json=formulaResult" json:"formula_result,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *CalculateFormulaResponse) Reset()         { *m = CalculateFormulaResponse{} }
func (m *CalculateFormulaResponse) String() string { return proto.CompactTextString(m) }
func (*CalculateFormulaResponse) ProtoMessage()    {}
func (*CalculateFormulaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{4}
}

func (m *CalculateFormulaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculateFormulaResponse.Unmarshal(m, b)
}
func (m *CalculateFormulaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculateFormulaResponse.Marshal(b, m, deterministic)
}
func (m *CalculateFormulaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculateFormulaResponse.Merge(m, src)
}
func (m *CalculateFormulaResponse) XXX_Size() int {
	return xxx_messageInfo_CalculateFormulaResponse.Size(m)
}
func (m *CalculateFormulaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculateFormulaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CalculateFormulaResponse proto.InternalMessageInfo

func (m *CalculateFormulaResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *CalculateFormulaResponse) GetFormulaResult() map[uint32]*CalculateFormulaResult {
	if m != nil {
		return m.FormulaResult
	}
	return nil
}

type CalculateFormulaResult struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Result               *float64    `protobuf:"fixed64,2,opt,name=result" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CalculateFormulaResult) Reset()         { *m = CalculateFormulaResult{} }
func (m *CalculateFormulaResult) String() string { return proto.CompactTextString(m) }
func (*CalculateFormulaResult) ProtoMessage()    {}
func (*CalculateFormulaResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{5}
}

func (m *CalculateFormulaResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculateFormulaResult.Unmarshal(m, b)
}
func (m *CalculateFormulaResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculateFormulaResult.Marshal(b, m, deterministic)
}
func (m *CalculateFormulaResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculateFormulaResult.Merge(m, src)
}
func (m *CalculateFormulaResult) XXX_Size() int {
	return xxx_messageInfo_CalculateFormulaResult.Size(m)
}
func (m *CalculateFormulaResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculateFormulaResult.DiscardUnknown(m)
}

var xxx_messageInfo_CalculateFormulaResult proto.InternalMessageInfo

func (m *CalculateFormulaResult) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *CalculateFormulaResult) GetResult() float64 {
	if m != nil && m.Result != nil {
		return *m.Result
	}
	return 0
}

type CalculateWeightRequest struct {
	ReqHeader            *ReqHeader         `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BaseInfo             *CalculateBaseInfo `protobuf:"bytes,2,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	OrderInfo            *OrderInfo         `protobuf:"bytes,3,req,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	SkuInfo              []*SkuInfo         `protobuf:"bytes,4,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CalculateWeightRequest) Reset()         { *m = CalculateWeightRequest{} }
func (m *CalculateWeightRequest) String() string { return proto.CompactTextString(m) }
func (*CalculateWeightRequest) ProtoMessage()    {}
func (*CalculateWeightRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{6}
}

func (m *CalculateWeightRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculateWeightRequest.Unmarshal(m, b)
}
func (m *CalculateWeightRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculateWeightRequest.Marshal(b, m, deterministic)
}
func (m *CalculateWeightRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculateWeightRequest.Merge(m, src)
}
func (m *CalculateWeightRequest) XXX_Size() int {
	return xxx_messageInfo_CalculateWeightRequest.Size(m)
}
func (m *CalculateWeightRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculateWeightRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CalculateWeightRequest proto.InternalMessageInfo

func (m *CalculateWeightRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *CalculateWeightRequest) GetBaseInfo() *CalculateBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *CalculateWeightRequest) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *CalculateWeightRequest) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

type CalculateWeightResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ResultWeight         *float64    `protobuf:"fixed64,2,opt,name=result_weight,json=resultWeight" json:"result_weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CalculateWeightResponse) Reset()         { *m = CalculateWeightResponse{} }
func (m *CalculateWeightResponse) String() string { return proto.CompactTextString(m) }
func (*CalculateWeightResponse) ProtoMessage()    {}
func (*CalculateWeightResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{7}
}

func (m *CalculateWeightResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculateWeightResponse.Unmarshal(m, b)
}
func (m *CalculateWeightResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculateWeightResponse.Marshal(b, m, deterministic)
}
func (m *CalculateWeightResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculateWeightResponse.Merge(m, src)
}
func (m *CalculateWeightResponse) XXX_Size() int {
	return xxx_messageInfo_CalculateWeightResponse.Size(m)
}
func (m *CalculateWeightResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculateWeightResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CalculateWeightResponse proto.InternalMessageInfo

func (m *CalculateWeightResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *CalculateWeightResponse) GetResultWeight() float64 {
	if m != nil && m.ResultWeight != nil {
		return *m.ResultWeight
	}
	return 0
}

type BatchCalculateWeightRequest struct {
	ReqHeader            *ReqHeader            `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	CalculateInfo        []*BatchCalculateInfo `protobuf:"bytes,2,rep,name=calculate_info,json=calculateInfo" json:"calculate_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchCalculateWeightRequest) Reset()         { *m = BatchCalculateWeightRequest{} }
func (m *BatchCalculateWeightRequest) String() string { return proto.CompactTextString(m) }
func (*BatchCalculateWeightRequest) ProtoMessage()    {}
func (*BatchCalculateWeightRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{8}
}

func (m *BatchCalculateWeightRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCalculateWeightRequest.Unmarshal(m, b)
}
func (m *BatchCalculateWeightRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCalculateWeightRequest.Marshal(b, m, deterministic)
}
func (m *BatchCalculateWeightRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCalculateWeightRequest.Merge(m, src)
}
func (m *BatchCalculateWeightRequest) XXX_Size() int {
	return xxx_messageInfo_BatchCalculateWeightRequest.Size(m)
}
func (m *BatchCalculateWeightRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCalculateWeightRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCalculateWeightRequest proto.InternalMessageInfo

func (m *BatchCalculateWeightRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCalculateWeightRequest) GetCalculateInfo() []*BatchCalculateInfo {
	if m != nil {
		return m.CalculateInfo
	}
	return nil
}

type BatchCalculateWeightResponse struct {
	RespHeader           *RespHeader             `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CalculateResult      []*BatchCalculateResult `protobuf:"bytes,2,rep,name=calculate_result,json=calculateResult" json:"calculate_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchCalculateWeightResponse) Reset()         { *m = BatchCalculateWeightResponse{} }
func (m *BatchCalculateWeightResponse) String() string { return proto.CompactTextString(m) }
func (*BatchCalculateWeightResponse) ProtoMessage()    {}
func (*BatchCalculateWeightResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{9}
}

func (m *BatchCalculateWeightResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCalculateWeightResponse.Unmarshal(m, b)
}
func (m *BatchCalculateWeightResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCalculateWeightResponse.Marshal(b, m, deterministic)
}
func (m *BatchCalculateWeightResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCalculateWeightResponse.Merge(m, src)
}
func (m *BatchCalculateWeightResponse) XXX_Size() int {
	return xxx_messageInfo_BatchCalculateWeightResponse.Size(m)
}
func (m *BatchCalculateWeightResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCalculateWeightResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCalculateWeightResponse proto.InternalMessageInfo

func (m *BatchCalculateWeightResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCalculateWeightResponse) GetCalculateResult() []*BatchCalculateResult {
	if m != nil {
		return m.CalculateResult
	}
	return nil
}

type CheckLineRuleRequest struct {
	ReqHeader            *ReqHeader        `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	SkuInfo              []*SkuInfo        `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	LineId               []string          `protobuf:"bytes,3,rep,name=line_id,json=lineId" json:"line_id,omitempty"`
	OrderInfo            *OrderInfo        `protobuf:"bytes,4,opt,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	SubPackageInfo       []*SubPackageInfo `protobuf:"bytes,5,rep,name=sub_package_info,json=subPackageInfo" json:"sub_package_info,omitempty"`
	BuyerPurchaseTime    *uint32           `protobuf:"varint,6,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CheckLineRuleRequest) Reset()         { *m = CheckLineRuleRequest{} }
func (m *CheckLineRuleRequest) String() string { return proto.CompactTextString(m) }
func (*CheckLineRuleRequest) ProtoMessage()    {}
func (*CheckLineRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{10}
}

func (m *CheckLineRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLineRuleRequest.Unmarshal(m, b)
}
func (m *CheckLineRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLineRuleRequest.Marshal(b, m, deterministic)
}
func (m *CheckLineRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLineRuleRequest.Merge(m, src)
}
func (m *CheckLineRuleRequest) XXX_Size() int {
	return xxx_messageInfo_CheckLineRuleRequest.Size(m)
}
func (m *CheckLineRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLineRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLineRuleRequest proto.InternalMessageInfo

func (m *CheckLineRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *CheckLineRuleRequest) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func (m *CheckLineRuleRequest) GetLineId() []string {
	if m != nil {
		return m.LineId
	}
	return nil
}

func (m *CheckLineRuleRequest) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *CheckLineRuleRequest) GetSubPackageInfo() []*SubPackageInfo {
	if m != nil {
		return m.SubPackageInfo
	}
	return nil
}

func (m *CheckLineRuleRequest) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

type SingleCheckLineRuleRequest struct {
	UniqueId             *string           `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	SkuInfo              []*SkuInfo        `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	LineId               []string          `protobuf:"bytes,3,rep,name=line_id,json=lineId" json:"line_id,omitempty"`
	OrderInfo            *OrderInfo        `protobuf:"bytes,4,opt,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	SubPackageInfo       []*SubPackageInfo `protobuf:"bytes,5,rep,name=sub_package_info,json=subPackageInfo" json:"sub_package_info,omitempty"`
	BuyerPurchaseTime    *uint32           `protobuf:"varint,6,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SingleCheckLineRuleRequest) Reset()         { *m = SingleCheckLineRuleRequest{} }
func (m *SingleCheckLineRuleRequest) String() string { return proto.CompactTextString(m) }
func (*SingleCheckLineRuleRequest) ProtoMessage()    {}
func (*SingleCheckLineRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{11}
}

func (m *SingleCheckLineRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleCheckLineRuleRequest.Unmarshal(m, b)
}
func (m *SingleCheckLineRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleCheckLineRuleRequest.Marshal(b, m, deterministic)
}
func (m *SingleCheckLineRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleCheckLineRuleRequest.Merge(m, src)
}
func (m *SingleCheckLineRuleRequest) XXX_Size() int {
	return xxx_messageInfo_SingleCheckLineRuleRequest.Size(m)
}
func (m *SingleCheckLineRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleCheckLineRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SingleCheckLineRuleRequest proto.InternalMessageInfo

func (m *SingleCheckLineRuleRequest) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SingleCheckLineRuleRequest) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func (m *SingleCheckLineRuleRequest) GetLineId() []string {
	if m != nil {
		return m.LineId
	}
	return nil
}

func (m *SingleCheckLineRuleRequest) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *SingleCheckLineRuleRequest) GetSubPackageInfo() []*SubPackageInfo {
	if m != nil {
		return m.SubPackageInfo
	}
	return nil
}

func (m *SingleCheckLineRuleRequest) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

type BatchCheckLineRuleRequest struct {
	ReqHeader            *ReqHeader                    `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	CheckLineRuleList    []*SingleCheckLineRuleRequest `protobuf:"bytes,2,rep,name=check_line_rule_list,json=checkLineRuleList" json:"check_line_rule_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatchCheckLineRuleRequest) Reset()         { *m = BatchCheckLineRuleRequest{} }
func (m *BatchCheckLineRuleRequest) String() string { return proto.CompactTextString(m) }
func (*BatchCheckLineRuleRequest) ProtoMessage()    {}
func (*BatchCheckLineRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{12}
}

func (m *BatchCheckLineRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckLineRuleRequest.Unmarshal(m, b)
}
func (m *BatchCheckLineRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckLineRuleRequest.Marshal(b, m, deterministic)
}
func (m *BatchCheckLineRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckLineRuleRequest.Merge(m, src)
}
func (m *BatchCheckLineRuleRequest) XXX_Size() int {
	return xxx_messageInfo_BatchCheckLineRuleRequest.Size(m)
}
func (m *BatchCheckLineRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckLineRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckLineRuleRequest proto.InternalMessageInfo

func (m *BatchCheckLineRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckLineRuleRequest) GetCheckLineRuleList() []*SingleCheckLineRuleRequest {
	if m != nil {
		return m.CheckLineRuleList
	}
	return nil
}

type CheckLineRuleResponse struct {
	RespHeader           *RespHeader                  `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	RuleLimitInfo        []*SingleCheckLineRuleResult `protobuf:"bytes,2,rep,name=rule_limit_info,json=ruleLimitInfo" json:"rule_limit_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *CheckLineRuleResponse) Reset()         { *m = CheckLineRuleResponse{} }
func (m *CheckLineRuleResponse) String() string { return proto.CompactTextString(m) }
func (*CheckLineRuleResponse) ProtoMessage()    {}
func (*CheckLineRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{13}
}

func (m *CheckLineRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLineRuleResponse.Unmarshal(m, b)
}
func (m *CheckLineRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLineRuleResponse.Marshal(b, m, deterministic)
}
func (m *CheckLineRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLineRuleResponse.Merge(m, src)
}
func (m *CheckLineRuleResponse) XXX_Size() int {
	return xxx_messageInfo_CheckLineRuleResponse.Size(m)
}
func (m *CheckLineRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLineRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLineRuleResponse proto.InternalMessageInfo

func (m *CheckLineRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *CheckLineRuleResponse) GetRuleLimitInfo() []*SingleCheckLineRuleResult {
	if m != nil {
		return m.RuleLimitInfo
	}
	return nil
}

type BatchCheckLineRuleResponse struct {
	RespHeader             *RespHeader                       `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CheckLineRuleResultMap map[string]*CheckLineRuleResponse `protobuf:"bytes,2,rep,name=check_line_rule_result_map,json=checkLineRuleResultMap" json:"check_line_rule_result_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral   struct{}                          `json:"-"`
	XXX_unrecognized       []byte                            `json:"-"`
	XXX_sizecache          int32                             `json:"-"`
}

func (m *BatchCheckLineRuleResponse) Reset()         { *m = BatchCheckLineRuleResponse{} }
func (m *BatchCheckLineRuleResponse) String() string { return proto.CompactTextString(m) }
func (*BatchCheckLineRuleResponse) ProtoMessage()    {}
func (*BatchCheckLineRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{14}
}

func (m *BatchCheckLineRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckLineRuleResponse.Unmarshal(m, b)
}
func (m *BatchCheckLineRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckLineRuleResponse.Marshal(b, m, deterministic)
}
func (m *BatchCheckLineRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckLineRuleResponse.Merge(m, src)
}
func (m *BatchCheckLineRuleResponse) XXX_Size() int {
	return xxx_messageInfo_BatchCheckLineRuleResponse.Size(m)
}
func (m *BatchCheckLineRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckLineRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckLineRuleResponse proto.InternalMessageInfo

func (m *BatchCheckLineRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckLineRuleResponse) GetCheckLineRuleResultMap() map[string]*CheckLineRuleResponse {
	if m != nil {
		return m.CheckLineRuleResultMap
	}
	return nil
}

type CheckProductRuleRequest struct {
	ReqHeader            *ReqHeader        `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	SkuInfo              []*SkuInfo        `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	ProductId            []string          `protobuf:"bytes,3,rep,name=product_id,json=productId" json:"product_id,omitempty"`
	OrderInfo            *OrderInfo        `protobuf:"bytes,4,opt,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	SubPackageInfo       []*SubPackageInfo `protobuf:"bytes,5,rep,name=sub_package_info,json=subPackageInfo" json:"sub_package_info,omitempty"`
	BuyerPurchaseTime    *uint32           `protobuf:"varint,6,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CheckProductRuleRequest) Reset()         { *m = CheckProductRuleRequest{} }
func (m *CheckProductRuleRequest) String() string { return proto.CompactTextString(m) }
func (*CheckProductRuleRequest) ProtoMessage()    {}
func (*CheckProductRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{15}
}

func (m *CheckProductRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckProductRuleRequest.Unmarshal(m, b)
}
func (m *CheckProductRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckProductRuleRequest.Marshal(b, m, deterministic)
}
func (m *CheckProductRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckProductRuleRequest.Merge(m, src)
}
func (m *CheckProductRuleRequest) XXX_Size() int {
	return xxx_messageInfo_CheckProductRuleRequest.Size(m)
}
func (m *CheckProductRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckProductRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckProductRuleRequest proto.InternalMessageInfo

func (m *CheckProductRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *CheckProductRuleRequest) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func (m *CheckProductRuleRequest) GetProductId() []string {
	if m != nil {
		return m.ProductId
	}
	return nil
}

func (m *CheckProductRuleRequest) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *CheckProductRuleRequest) GetSubPackageInfo() []*SubPackageInfo {
	if m != nil {
		return m.SubPackageInfo
	}
	return nil
}

func (m *CheckProductRuleRequest) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

type SingleProductRule struct {
	UniqueId             *string           `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	SkuInfo              []*SkuInfo        `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	ProductId            []string          `protobuf:"bytes,3,rep,name=product_id,json=productId" json:"product_id,omitempty"`
	OrderInfo            *OrderInfo        `protobuf:"bytes,4,opt,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	SubPackageInfo       []*SubPackageInfo `protobuf:"bytes,5,rep,name=sub_package_info,json=subPackageInfo" json:"sub_package_info,omitempty"`
	BuyerPurchaseTime    *uint32           `protobuf:"varint,6,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SingleProductRule) Reset()         { *m = SingleProductRule{} }
func (m *SingleProductRule) String() string { return proto.CompactTextString(m) }
func (*SingleProductRule) ProtoMessage()    {}
func (*SingleProductRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{16}
}

func (m *SingleProductRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleProductRule.Unmarshal(m, b)
}
func (m *SingleProductRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleProductRule.Marshal(b, m, deterministic)
}
func (m *SingleProductRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleProductRule.Merge(m, src)
}
func (m *SingleProductRule) XXX_Size() int {
	return xxx_messageInfo_SingleProductRule.Size(m)
}
func (m *SingleProductRule) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleProductRule.DiscardUnknown(m)
}

var xxx_messageInfo_SingleProductRule proto.InternalMessageInfo

func (m *SingleProductRule) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SingleProductRule) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func (m *SingleProductRule) GetProductId() []string {
	if m != nil {
		return m.ProductId
	}
	return nil
}

func (m *SingleProductRule) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *SingleProductRule) GetSubPackageInfo() []*SubPackageInfo {
	if m != nil {
		return m.SubPackageInfo
	}
	return nil
}

func (m *SingleProductRule) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

type BatchCheckProductRuleRequest struct {
	ReqHeader            *ReqHeader           `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductRules         []*SingleProductRule `protobuf:"bytes,2,rep,name=product_rules,json=productRules" json:"product_rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchCheckProductRuleRequest) Reset()         { *m = BatchCheckProductRuleRequest{} }
func (m *BatchCheckProductRuleRequest) String() string { return proto.CompactTextString(m) }
func (*BatchCheckProductRuleRequest) ProtoMessage()    {}
func (*BatchCheckProductRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{17}
}

func (m *BatchCheckProductRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductRuleRequest.Unmarshal(m, b)
}
func (m *BatchCheckProductRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductRuleRequest.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductRuleRequest.Merge(m, src)
}
func (m *BatchCheckProductRuleRequest) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductRuleRequest.Size(m)
}
func (m *BatchCheckProductRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductRuleRequest proto.InternalMessageInfo

func (m *BatchCheckProductRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckProductRuleRequest) GetProductRules() []*SingleProductRule {
	if m != nil {
		return m.ProductRules
	}
	return nil
}

type BatchCheckProductRuleForShoppingCartRequest struct {
	ReqHeader            *ReqHeader           `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductRules         []*SingleProductRule `protobuf:"bytes,2,rep,name=product_rules,json=productRules" json:"product_rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchCheckProductRuleForShoppingCartRequest) Reset() {
	*m = BatchCheckProductRuleForShoppingCartRequest{}
}
func (m *BatchCheckProductRuleForShoppingCartRequest) String() string {
	return proto.CompactTextString(m)
}
func (*BatchCheckProductRuleForShoppingCartRequest) ProtoMessage() {}
func (*BatchCheckProductRuleForShoppingCartRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{18}
}

func (m *BatchCheckProductRuleForShoppingCartRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductRuleForShoppingCartRequest.Unmarshal(m, b)
}
func (m *BatchCheckProductRuleForShoppingCartRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductRuleForShoppingCartRequest.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductRuleForShoppingCartRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductRuleForShoppingCartRequest.Merge(m, src)
}
func (m *BatchCheckProductRuleForShoppingCartRequest) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductRuleForShoppingCartRequest.Size(m)
}
func (m *BatchCheckProductRuleForShoppingCartRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductRuleForShoppingCartRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductRuleForShoppingCartRequest proto.InternalMessageInfo

func (m *BatchCheckProductRuleForShoppingCartRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckProductRuleForShoppingCartRequest) GetProductRules() []*SingleProductRule {
	if m != nil {
		return m.ProductRules
	}
	return nil
}

type CheckProductRuleResponse struct {
	RespHeader           *RespHeader                     `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	RuleLimitInfo        []*SingleCheckProductRuleResult `protobuf:"bytes,2,rep,name=rule_limit_info,json=ruleLimitInfo" json:"rule_limit_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *CheckProductRuleResponse) Reset()         { *m = CheckProductRuleResponse{} }
func (m *CheckProductRuleResponse) String() string { return proto.CompactTextString(m) }
func (*CheckProductRuleResponse) ProtoMessage()    {}
func (*CheckProductRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{19}
}

func (m *CheckProductRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckProductRuleResponse.Unmarshal(m, b)
}
func (m *CheckProductRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckProductRuleResponse.Marshal(b, m, deterministic)
}
func (m *CheckProductRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckProductRuleResponse.Merge(m, src)
}
func (m *CheckProductRuleResponse) XXX_Size() int {
	return xxx_messageInfo_CheckProductRuleResponse.Size(m)
}
func (m *CheckProductRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckProductRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckProductRuleResponse proto.InternalMessageInfo

func (m *CheckProductRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *CheckProductRuleResponse) GetRuleLimitInfo() []*SingleCheckProductRuleResult {
	if m != nil {
		return m.RuleLimitInfo
	}
	return nil
}

type BatchCheckProductRuleResponse struct {
	RespHeader                *RespHeader                          `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CheckProductRuleResultMap map[string]*CheckProductRuleResponse `protobuf:"bytes,2,rep,name=check_product_rule_result_map,json=checkProductRuleResultMap" json:"check_product_rule_result_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral      struct{}                             `json:"-"`
	XXX_unrecognized          []byte                               `json:"-"`
	XXX_sizecache             int32                                `json:"-"`
}

func (m *BatchCheckProductRuleResponse) Reset()         { *m = BatchCheckProductRuleResponse{} }
func (m *BatchCheckProductRuleResponse) String() string { return proto.CompactTextString(m) }
func (*BatchCheckProductRuleResponse) ProtoMessage()    {}
func (*BatchCheckProductRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{20}
}

func (m *BatchCheckProductRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductRuleResponse.Unmarshal(m, b)
}
func (m *BatchCheckProductRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductRuleResponse.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductRuleResponse.Merge(m, src)
}
func (m *BatchCheckProductRuleResponse) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductRuleResponse.Size(m)
}
func (m *BatchCheckProductRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductRuleResponse proto.InternalMessageInfo

func (m *BatchCheckProductRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckProductRuleResponse) GetCheckProductRuleResultMap() map[string]*CheckProductRuleResponse {
	if m != nil {
		return m.CheckProductRuleResultMap
	}
	return nil
}

type VolumetricWeightInfo struct {
	ItemId               *uint64  `protobuf:"varint,1,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	CategoryId           *uint64  `protobuf:"varint,2,opt,name=category_id,json=categoryId" json:"category_id,omitempty"`
	VolumetricWeight     *float64 `protobuf:"fixed64,3,opt,name=volumetric_weight,json=volumetricWeight" json:"volumetric_weight,omitempty"`
	ModelId              *uint64  `protobuf:"varint,4,opt,name=model_id,json=modelId" json:"model_id,omitempty"`
	VolumetricWeightInt  *uint32  `protobuf:"varint,5,opt,name=volumetric_weight_int,json=volumetricWeightInt" json:"volumetric_weight_int,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VolumetricWeightInfo) Reset()         { *m = VolumetricWeightInfo{} }
func (m *VolumetricWeightInfo) String() string { return proto.CompactTextString(m) }
func (*VolumetricWeightInfo) ProtoMessage()    {}
func (*VolumetricWeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{21}
}

func (m *VolumetricWeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VolumetricWeightInfo.Unmarshal(m, b)
}
func (m *VolumetricWeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VolumetricWeightInfo.Marshal(b, m, deterministic)
}
func (m *VolumetricWeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VolumetricWeightInfo.Merge(m, src)
}
func (m *VolumetricWeightInfo) XXX_Size() int {
	return xxx_messageInfo_VolumetricWeightInfo.Size(m)
}
func (m *VolumetricWeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VolumetricWeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VolumetricWeightInfo proto.InternalMessageInfo

func (m *VolumetricWeightInfo) GetItemId() uint64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *VolumetricWeightInfo) GetCategoryId() uint64 {
	if m != nil && m.CategoryId != nil {
		return *m.CategoryId
	}
	return 0
}

func (m *VolumetricWeightInfo) GetVolumetricWeight() float64 {
	if m != nil && m.VolumetricWeight != nil {
		return *m.VolumetricWeight
	}
	return 0
}

func (m *VolumetricWeightInfo) GetModelId() uint64 {
	if m != nil && m.ModelId != nil {
		return *m.ModelId
	}
	return 0
}

func (m *VolumetricWeightInfo) GetVolumetricWeightInt() uint32 {
	if m != nil && m.VolumetricWeightInt != nil {
		return *m.VolumetricWeightInt
	}
	return 0
}

type SingleRuleCheckResult struct {
	RuleType                *uint32                 `protobuf:"varint,1,req,name=rule_type,json=ruleType" json:"rule_type,omitempty"`
	CheckResult             *uint32                 `protobuf:"varint,2,req,name=check_result,json=checkResult" json:"check_result,omitempty"`
	ErrorMessage            *string                 `protobuf:"bytes,3,req,name=error_message,json=errorMessage" json:"error_message,omitempty"`
	CalculateResult         *float64                `protobuf:"fixed64,4,req,name=calculate_result,json=calculateResult" json:"calculate_result,omitempty"`
	LimitThresholdMin       *float64                `protobuf:"fixed64,5,req,name=limit_threshold_min,json=limitThresholdMin" json:"limit_threshold_min,omitempty"`
	LimitThresholdMax       *float64                `protobuf:"fixed64,6,req,name=limit_threshold_max,json=limitThresholdMax" json:"limit_threshold_max,omitempty"`
	IncludeVolumetricWeight *bool                   `protobuf:"varint,7,req,name=include_volumetric_weight,json=includeVolumetricWeight" json:"include_volumetric_weight,omitempty"`
	VolumetricWeightInfo    []*VolumetricWeightInfo `protobuf:"bytes,8,rep,name=volumetric_weight_info,json=volumetricWeightInfo" json:"volumetric_weight_info,omitempty"`
	CalculateResultInt      *uint32                 `protobuf:"varint,9,req,name=calculate_result_int,json=calculateResultInt" json:"calculate_result_int,omitempty"`
	LimitThresholdMinInt    *uint32                 `protobuf:"varint,10,req,name=limit_threshold_min_int,json=limitThresholdMinInt" json:"limit_threshold_min_int,omitempty"`
	LimitThresholdMaxInt    *uint32                 `protobuf:"varint,11,req,name=limit_threshold_max_int,json=limitThresholdMaxInt" json:"limit_threshold_max_int,omitempty"`
	Formula                 *uint32                 `protobuf:"varint,12,opt,name=formula" json:"formula,omitempty"`
	VolumetricFactor        *uint32                 `protobuf:"varint,13,opt,name=volumetric_factor,json=volumetricFactor" json:"volumetric_factor,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                `json:"-"`
	XXX_unrecognized        []byte                  `json:"-"`
	XXX_sizecache           int32                   `json:"-"`
}

func (m *SingleRuleCheckResult) Reset()         { *m = SingleRuleCheckResult{} }
func (m *SingleRuleCheckResult) String() string { return proto.CompactTextString(m) }
func (*SingleRuleCheckResult) ProtoMessage()    {}
func (*SingleRuleCheckResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{22}
}

func (m *SingleRuleCheckResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleRuleCheckResult.Unmarshal(m, b)
}
func (m *SingleRuleCheckResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleRuleCheckResult.Marshal(b, m, deterministic)
}
func (m *SingleRuleCheckResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleRuleCheckResult.Merge(m, src)
}
func (m *SingleRuleCheckResult) XXX_Size() int {
	return xxx_messageInfo_SingleRuleCheckResult.Size(m)
}
func (m *SingleRuleCheckResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleRuleCheckResult.DiscardUnknown(m)
}

var xxx_messageInfo_SingleRuleCheckResult proto.InternalMessageInfo

func (m *SingleRuleCheckResult) GetRuleType() uint32 {
	if m != nil && m.RuleType != nil {
		return *m.RuleType
	}
	return 0
}

func (m *SingleRuleCheckResult) GetCheckResult() uint32 {
	if m != nil && m.CheckResult != nil {
		return *m.CheckResult
	}
	return 0
}

func (m *SingleRuleCheckResult) GetErrorMessage() string {
	if m != nil && m.ErrorMessage != nil {
		return *m.ErrorMessage
	}
	return ""
}

func (m *SingleRuleCheckResult) GetCalculateResult() float64 {
	if m != nil && m.CalculateResult != nil {
		return *m.CalculateResult
	}
	return 0
}

func (m *SingleRuleCheckResult) GetLimitThresholdMin() float64 {
	if m != nil && m.LimitThresholdMin != nil {
		return *m.LimitThresholdMin
	}
	return 0
}

func (m *SingleRuleCheckResult) GetLimitThresholdMax() float64 {
	if m != nil && m.LimitThresholdMax != nil {
		return *m.LimitThresholdMax
	}
	return 0
}

func (m *SingleRuleCheckResult) GetIncludeVolumetricWeight() bool {
	if m != nil && m.IncludeVolumetricWeight != nil {
		return *m.IncludeVolumetricWeight
	}
	return false
}

func (m *SingleRuleCheckResult) GetVolumetricWeightInfo() []*VolumetricWeightInfo {
	if m != nil {
		return m.VolumetricWeightInfo
	}
	return nil
}

func (m *SingleRuleCheckResult) GetCalculateResultInt() uint32 {
	if m != nil && m.CalculateResultInt != nil {
		return *m.CalculateResultInt
	}
	return 0
}

func (m *SingleRuleCheckResult) GetLimitThresholdMinInt() uint32 {
	if m != nil && m.LimitThresholdMinInt != nil {
		return *m.LimitThresholdMinInt
	}
	return 0
}

func (m *SingleRuleCheckResult) GetLimitThresholdMaxInt() uint32 {
	if m != nil && m.LimitThresholdMaxInt != nil {
		return *m.LimitThresholdMaxInt
	}
	return 0
}

func (m *SingleRuleCheckResult) GetFormula() uint32 {
	if m != nil && m.Formula != nil {
		return *m.Formula
	}
	return 0
}

func (m *SingleRuleCheckResult) GetVolumetricFactor() uint32 {
	if m != nil && m.VolumetricFactor != nil {
		return *m.VolumetricFactor
	}
	return 0
}

type SingleProductCheckResult struct {
	ProductId            *string                  `protobuf:"bytes,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	CheckResult          *uint32                  `protobuf:"varint,2,req,name=check_result,json=checkResult" json:"check_result,omitempty"`
	RuleCheckDetail      []*SingleRuleCheckResult `protobuf:"bytes,3,rep,name=rule_check_detail,json=ruleCheckDetail" json:"rule_check_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *SingleProductCheckResult) Reset()         { *m = SingleProductCheckResult{} }
func (m *SingleProductCheckResult) String() string { return proto.CompactTextString(m) }
func (*SingleProductCheckResult) ProtoMessage()    {}
func (*SingleProductCheckResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{23}
}

func (m *SingleProductCheckResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleProductCheckResult.Unmarshal(m, b)
}
func (m *SingleProductCheckResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleProductCheckResult.Marshal(b, m, deterministic)
}
func (m *SingleProductCheckResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleProductCheckResult.Merge(m, src)
}
func (m *SingleProductCheckResult) XXX_Size() int {
	return xxx_messageInfo_SingleProductCheckResult.Size(m)
}
func (m *SingleProductCheckResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleProductCheckResult.DiscardUnknown(m)
}

var xxx_messageInfo_SingleProductCheckResult proto.InternalMessageInfo

func (m *SingleProductCheckResult) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *SingleProductCheckResult) GetCheckResult() uint32 {
	if m != nil && m.CheckResult != nil {
		return *m.CheckResult
	}
	return 0
}

func (m *SingleProductCheckResult) GetRuleCheckDetail() []*SingleRuleCheckResult {
	if m != nil {
		return m.RuleCheckDetail
	}
	return nil
}

type CheckProductRuleForShoppingCartResponse struct {
	RespHeader           *RespHeader                 `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ProductCheckResult   []*SingleProductCheckResult `protobuf:"bytes,2,rep,name=product_check_result,json=productCheckResult" json:"product_check_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CheckProductRuleForShoppingCartResponse) Reset() {
	*m = CheckProductRuleForShoppingCartResponse{}
}
func (m *CheckProductRuleForShoppingCartResponse) String() string { return proto.CompactTextString(m) }
func (*CheckProductRuleForShoppingCartResponse) ProtoMessage()    {}
func (*CheckProductRuleForShoppingCartResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{24}
}

func (m *CheckProductRuleForShoppingCartResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckProductRuleForShoppingCartResponse.Unmarshal(m, b)
}
func (m *CheckProductRuleForShoppingCartResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckProductRuleForShoppingCartResponse.Marshal(b, m, deterministic)
}
func (m *CheckProductRuleForShoppingCartResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckProductRuleForShoppingCartResponse.Merge(m, src)
}
func (m *CheckProductRuleForShoppingCartResponse) XXX_Size() int {
	return xxx_messageInfo_CheckProductRuleForShoppingCartResponse.Size(m)
}
func (m *CheckProductRuleForShoppingCartResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckProductRuleForShoppingCartResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckProductRuleForShoppingCartResponse proto.InternalMessageInfo

func (m *CheckProductRuleForShoppingCartResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *CheckProductRuleForShoppingCartResponse) GetProductCheckResult() []*SingleProductCheckResult {
	if m != nil {
		return m.ProductCheckResult
	}
	return nil
}

type BatchCheckProductRuleForShoppingCartResponse struct {
	RespHeader                *RespHeader                                         `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CheckProductRuleResultMap map[string]*CheckProductRuleForShoppingCartResponse `protobuf:"bytes,2,rep,name=check_product_rule_result_map,json=checkProductRuleResultMap" json:"check_product_rule_result_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral      struct{}                                            `json:"-"`
	XXX_unrecognized          []byte                                              `json:"-"`
	XXX_sizecache             int32                                               `json:"-"`
}

func (m *BatchCheckProductRuleForShoppingCartResponse) Reset() {
	*m = BatchCheckProductRuleForShoppingCartResponse{}
}
func (m *BatchCheckProductRuleForShoppingCartResponse) String() string {
	return proto.CompactTextString(m)
}
func (*BatchCheckProductRuleForShoppingCartResponse) ProtoMessage() {}
func (*BatchCheckProductRuleForShoppingCartResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{25}
}

func (m *BatchCheckProductRuleForShoppingCartResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductRuleForShoppingCartResponse.Unmarshal(m, b)
}
func (m *BatchCheckProductRuleForShoppingCartResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductRuleForShoppingCartResponse.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductRuleForShoppingCartResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductRuleForShoppingCartResponse.Merge(m, src)
}
func (m *BatchCheckProductRuleForShoppingCartResponse) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductRuleForShoppingCartResponse.Size(m)
}
func (m *BatchCheckProductRuleForShoppingCartResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductRuleForShoppingCartResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductRuleForShoppingCartResponse proto.InternalMessageInfo

func (m *BatchCheckProductRuleForShoppingCartResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckProductRuleForShoppingCartResponse) GetCheckProductRuleResultMap() map[string]*CheckProductRuleForShoppingCartResponse {
	if m != nil {
		return m.CheckProductRuleResultMap
	}
	return nil
}

type GetLineRuleRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineId               *string    `protobuf:"bytes,2,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetLineRuleRequest) Reset()         { *m = GetLineRuleRequest{} }
func (m *GetLineRuleRequest) String() string { return proto.CompactTextString(m) }
func (*GetLineRuleRequest) ProtoMessage()    {}
func (*GetLineRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{26}
}

func (m *GetLineRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLineRuleRequest.Unmarshal(m, b)
}
func (m *GetLineRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLineRuleRequest.Marshal(b, m, deterministic)
}
func (m *GetLineRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLineRuleRequest.Merge(m, src)
}
func (m *GetLineRuleRequest) XXX_Size() int {
	return xxx_messageInfo_GetLineRuleRequest.Size(m)
}
func (m *GetLineRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLineRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLineRuleRequest proto.InternalMessageInfo

func (m *GetLineRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetLineRuleRequest) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

type GetLineRuleResponse struct {
	RespHeader           *RespHeader       `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	RuleInfoDetail       []*RuleInfoDetail `protobuf:"bytes,2,rep,name=rule_info_detail,json=ruleInfoDetail" json:"rule_info_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetLineRuleResponse) Reset()         { *m = GetLineRuleResponse{} }
func (m *GetLineRuleResponse) String() string { return proto.CompactTextString(m) }
func (*GetLineRuleResponse) ProtoMessage()    {}
func (*GetLineRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{27}
}

func (m *GetLineRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLineRuleResponse.Unmarshal(m, b)
}
func (m *GetLineRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLineRuleResponse.Marshal(b, m, deterministic)
}
func (m *GetLineRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLineRuleResponse.Merge(m, src)
}
func (m *GetLineRuleResponse) XXX_Size() int {
	return xxx_messageInfo_GetLineRuleResponse.Size(m)
}
func (m *GetLineRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLineRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLineRuleResponse proto.InternalMessageInfo

func (m *GetLineRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetLineRuleResponse) GetRuleInfoDetail() []*RuleInfoDetail {
	if m != nil {
		return m.RuleInfoDetail
	}
	return nil
}

type BatchGetLineRuleRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineId               []string   `protobuf:"bytes,2,rep,name=line_id,json=lineId" json:"line_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetLineRuleRequest) Reset()         { *m = BatchGetLineRuleRequest{} }
func (m *BatchGetLineRuleRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetLineRuleRequest) ProtoMessage()    {}
func (*BatchGetLineRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{28}
}

func (m *BatchGetLineRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLineRuleRequest.Unmarshal(m, b)
}
func (m *BatchGetLineRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLineRuleRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetLineRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLineRuleRequest.Merge(m, src)
}
func (m *BatchGetLineRuleRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetLineRuleRequest.Size(m)
}
func (m *BatchGetLineRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLineRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLineRuleRequest proto.InternalMessageInfo

func (m *BatchGetLineRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetLineRuleRequest) GetLineId() []string {
	if m != nil {
		return m.LineId
	}
	return nil
}

type BatchGetLineRuleResponse struct {
	RespHeader           *RespHeader     `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	LineRuleInfo         []*LineRuleInfo `protobuf:"bytes,2,rep,name=line_rule_info,json=lineRuleInfo" json:"line_rule_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetLineRuleResponse) Reset()         { *m = BatchGetLineRuleResponse{} }
func (m *BatchGetLineRuleResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetLineRuleResponse) ProtoMessage()    {}
func (*BatchGetLineRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{29}
}

func (m *BatchGetLineRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLineRuleResponse.Unmarshal(m, b)
}
func (m *BatchGetLineRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLineRuleResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetLineRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLineRuleResponse.Merge(m, src)
}
func (m *BatchGetLineRuleResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetLineRuleResponse.Size(m)
}
func (m *BatchGetLineRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLineRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLineRuleResponse proto.InternalMessageInfo

func (m *BatchGetLineRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetLineRuleResponse) GetLineRuleInfo() []*LineRuleInfo {
	if m != nil {
		return m.LineRuleInfo
	}
	return nil
}

type GetProductRuleRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductId            *string    `protobuf:"bytes,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetProductRuleRequest) Reset()         { *m = GetProductRuleRequest{} }
func (m *GetProductRuleRequest) String() string { return proto.CompactTextString(m) }
func (*GetProductRuleRequest) ProtoMessage()    {}
func (*GetProductRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{30}
}

func (m *GetProductRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductRuleRequest.Unmarshal(m, b)
}
func (m *GetProductRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductRuleRequest.Marshal(b, m, deterministic)
}
func (m *GetProductRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductRuleRequest.Merge(m, src)
}
func (m *GetProductRuleRequest) XXX_Size() int {
	return xxx_messageInfo_GetProductRuleRequest.Size(m)
}
func (m *GetProductRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductRuleRequest proto.InternalMessageInfo

func (m *GetProductRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetProductRuleRequest) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

type BatchGetProductRuleRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductIdList        []string   `protobuf:"bytes,2,rep,name=product_id_list,json=productIdList" json:"product_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetProductRuleRequest) Reset()         { *m = BatchGetProductRuleRequest{} }
func (m *BatchGetProductRuleRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetProductRuleRequest) ProtoMessage()    {}
func (*BatchGetProductRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{31}
}

func (m *BatchGetProductRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetProductRuleRequest.Unmarshal(m, b)
}
func (m *BatchGetProductRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetProductRuleRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetProductRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetProductRuleRequest.Merge(m, src)
}
func (m *BatchGetProductRuleRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetProductRuleRequest.Size(m)
}
func (m *BatchGetProductRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetProductRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetProductRuleRequest proto.InternalMessageInfo

func (m *BatchGetProductRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetProductRuleRequest) GetProductIdList() []string {
	if m != nil {
		return m.ProductIdList
	}
	return nil
}

type GetProductRuleResponse struct {
	RespHeader           *RespHeader       `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	RuleInfoDetail       []*RuleInfoDetail `protobuf:"bytes,2,rep,name=rule_info_detail,json=ruleInfoDetail" json:"rule_info_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetProductRuleResponse) Reset()         { *m = GetProductRuleResponse{} }
func (m *GetProductRuleResponse) String() string { return proto.CompactTextString(m) }
func (*GetProductRuleResponse) ProtoMessage()    {}
func (*GetProductRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{32}
}

func (m *GetProductRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProductRuleResponse.Unmarshal(m, b)
}
func (m *GetProductRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProductRuleResponse.Marshal(b, m, deterministic)
}
func (m *GetProductRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProductRuleResponse.Merge(m, src)
}
func (m *GetProductRuleResponse) XXX_Size() int {
	return xxx_messageInfo_GetProductRuleResponse.Size(m)
}
func (m *GetProductRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProductRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetProductRuleResponse proto.InternalMessageInfo

func (m *GetProductRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetProductRuleResponse) GetRuleInfoDetail() []*RuleInfoDetail {
	if m != nil {
		return m.RuleInfoDetail
	}
	return nil
}

type RealRuleInfoDetailList struct {
	RuleInfoDetail       []*RealRuleInfoDetail `protobuf:"bytes,1,rep,name=rule_info_detail,json=ruleInfoDetail" json:"rule_info_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *RealRuleInfoDetailList) Reset()         { *m = RealRuleInfoDetailList{} }
func (m *RealRuleInfoDetailList) String() string { return proto.CompactTextString(m) }
func (*RealRuleInfoDetailList) ProtoMessage()    {}
func (*RealRuleInfoDetailList) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{33}
}

func (m *RealRuleInfoDetailList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RealRuleInfoDetailList.Unmarshal(m, b)
}
func (m *RealRuleInfoDetailList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RealRuleInfoDetailList.Marshal(b, m, deterministic)
}
func (m *RealRuleInfoDetailList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RealRuleInfoDetailList.Merge(m, src)
}
func (m *RealRuleInfoDetailList) XXX_Size() int {
	return xxx_messageInfo_RealRuleInfoDetailList.Size(m)
}
func (m *RealRuleInfoDetailList) XXX_DiscardUnknown() {
	xxx_messageInfo_RealRuleInfoDetailList.DiscardUnknown(m)
}

var xxx_messageInfo_RealRuleInfoDetailList proto.InternalMessageInfo

func (m *RealRuleInfoDetailList) GetRuleInfoDetail() []*RealRuleInfoDetail {
	if m != nil {
		return m.RuleInfoDetail
	}
	return nil
}

type BatchGetProductRuleResponse struct {
	RespHeader           *RespHeader                        `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	RuleInfoDetailList   map[string]*RealRuleInfoDetailList `protobuf:"bytes,2,rep,name=rule_info_detail_list,json=ruleInfoDetailList" json:"rule_info_detail_list,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *BatchGetProductRuleResponse) Reset()         { *m = BatchGetProductRuleResponse{} }
func (m *BatchGetProductRuleResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetProductRuleResponse) ProtoMessage()    {}
func (*BatchGetProductRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{34}
}

func (m *BatchGetProductRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetProductRuleResponse.Unmarshal(m, b)
}
func (m *BatchGetProductRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetProductRuleResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetProductRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetProductRuleResponse.Merge(m, src)
}
func (m *BatchGetProductRuleResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetProductRuleResponse.Size(m)
}
func (m *BatchGetProductRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetProductRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetProductRuleResponse proto.InternalMessageInfo

func (m *BatchGetProductRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetProductRuleResponse) GetRuleInfoDetailList() map[string]*RealRuleInfoDetailList {
	if m != nil {
		return m.RuleInfoDetailList
	}
	return nil
}

type CalculateBaseInfo struct {
	Formula              *uint32            `protobuf:"varint,1,req,name=formula" json:"formula,omitempty"`
	VolumetricFactor     *uint32            `protobuf:"varint,2,opt,name=volumetric_factor,json=volumetricFactor" json:"volumetric_factor,omitempty"`
	SortFlag             *uint32            `protobuf:"varint,3,opt,name=sort_flag,json=sortFlag" json:"sort_flag,omitempty"`
	ConditionParams      []*ConditionParams `protobuf:"bytes,4,rep,name=condition_params,json=conditionParams" json:"condition_params,omitempty"`
	ParcelLimits         *SizeLimitInfo     `protobuf:"bytes,5,opt,name=parcel_limits,json=parcelLimits" json:"parcel_limits,omitempty"`
	BuyerPurchaseTime    *uint32            `protobuf:"varint,6,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CalculateBaseInfo) Reset()         { *m = CalculateBaseInfo{} }
func (m *CalculateBaseInfo) String() string { return proto.CompactTextString(m) }
func (*CalculateBaseInfo) ProtoMessage()    {}
func (*CalculateBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{35}
}

func (m *CalculateBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculateBaseInfo.Unmarshal(m, b)
}
func (m *CalculateBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculateBaseInfo.Marshal(b, m, deterministic)
}
func (m *CalculateBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculateBaseInfo.Merge(m, src)
}
func (m *CalculateBaseInfo) XXX_Size() int {
	return xxx_messageInfo_CalculateBaseInfo.Size(m)
}
func (m *CalculateBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculateBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CalculateBaseInfo proto.InternalMessageInfo

func (m *CalculateBaseInfo) GetFormula() uint32 {
	if m != nil && m.Formula != nil {
		return *m.Formula
	}
	return 0
}

func (m *CalculateBaseInfo) GetVolumetricFactor() uint32 {
	if m != nil && m.VolumetricFactor != nil {
		return *m.VolumetricFactor
	}
	return 0
}

func (m *CalculateBaseInfo) GetSortFlag() uint32 {
	if m != nil && m.SortFlag != nil {
		return *m.SortFlag
	}
	return 0
}

func (m *CalculateBaseInfo) GetConditionParams() []*ConditionParams {
	if m != nil {
		return m.ConditionParams
	}
	return nil
}

func (m *CalculateBaseInfo) GetParcelLimits() *SizeLimitInfo {
	if m != nil {
		return m.ParcelLimits
	}
	return nil
}

func (m *CalculateBaseInfo) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

type CalculateBaseInfoMulti struct {
	Formula              []uint32       `protobuf:"varint,1,rep,name=formula" json:"formula,omitempty"`
	VolumetricFactor     *uint32        `protobuf:"varint,2,opt,name=volumetric_factor,json=volumetricFactor" json:"volumetric_factor,omitempty"`
	SortFlag             *uint32        `protobuf:"varint,3,opt,name=sort_flag,json=sortFlag" json:"sort_flag,omitempty"`
	ParcelLimits         *SizeLimitInfo `protobuf:"bytes,4,opt,name=parcel_limits,json=parcelLimits" json:"parcel_limits,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CalculateBaseInfoMulti) Reset()         { *m = CalculateBaseInfoMulti{} }
func (m *CalculateBaseInfoMulti) String() string { return proto.CompactTextString(m) }
func (*CalculateBaseInfoMulti) ProtoMessage()    {}
func (*CalculateBaseInfoMulti) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{36}
}

func (m *CalculateBaseInfoMulti) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculateBaseInfoMulti.Unmarshal(m, b)
}
func (m *CalculateBaseInfoMulti) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculateBaseInfoMulti.Marshal(b, m, deterministic)
}
func (m *CalculateBaseInfoMulti) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculateBaseInfoMulti.Merge(m, src)
}
func (m *CalculateBaseInfoMulti) XXX_Size() int {
	return xxx_messageInfo_CalculateBaseInfoMulti.Size(m)
}
func (m *CalculateBaseInfoMulti) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculateBaseInfoMulti.DiscardUnknown(m)
}

var xxx_messageInfo_CalculateBaseInfoMulti proto.InternalMessageInfo

func (m *CalculateBaseInfoMulti) GetFormula() []uint32 {
	if m != nil {
		return m.Formula
	}
	return nil
}

func (m *CalculateBaseInfoMulti) GetVolumetricFactor() uint32 {
	if m != nil && m.VolumetricFactor != nil {
		return *m.VolumetricFactor
	}
	return 0
}

func (m *CalculateBaseInfoMulti) GetSortFlag() uint32 {
	if m != nil && m.SortFlag != nil {
		return *m.SortFlag
	}
	return 0
}

func (m *CalculateBaseInfoMulti) GetParcelLimits() *SizeLimitInfo {
	if m != nil {
		return m.ParcelLimits
	}
	return nil
}

type ConditionParams struct {
	KeyParam             *float64 `protobuf:"fixed64,1,opt,name=key_param,json=keyParam" json:"key_param,omitempty"`
	KeySymbol            *uint32  `protobuf:"varint,2,opt,name=key_symbol,json=keySymbol" json:"key_symbol,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConditionParams) Reset()         { *m = ConditionParams{} }
func (m *ConditionParams) String() string { return proto.CompactTextString(m) }
func (*ConditionParams) ProtoMessage()    {}
func (*ConditionParams) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{37}
}

func (m *ConditionParams) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConditionParams.Unmarshal(m, b)
}
func (m *ConditionParams) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConditionParams.Marshal(b, m, deterministic)
}
func (m *ConditionParams) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConditionParams.Merge(m, src)
}
func (m *ConditionParams) XXX_Size() int {
	return xxx_messageInfo_ConditionParams.Size(m)
}
func (m *ConditionParams) XXX_DiscardUnknown() {
	xxx_messageInfo_ConditionParams.DiscardUnknown(m)
}

var xxx_messageInfo_ConditionParams proto.InternalMessageInfo

func (m *ConditionParams) GetKeyParam() float64 {
	if m != nil && m.KeyParam != nil {
		return *m.KeyParam
	}
	return 0
}

func (m *ConditionParams) GetKeySymbol() uint32 {
	if m != nil && m.KeySymbol != nil {
		return *m.KeySymbol
	}
	return 0
}

type SingleCheckLineRuleResult struct {
	LineId               *string            `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	CheckResult          *uint32            `protobuf:"varint,2,req,name=check_result,json=checkResult" json:"check_result,omitempty"`
	LimitDetail          []*RuleLimitDetail `protobuf:"bytes,3,rep,name=limit_detail,json=limitDetail" json:"limit_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SingleCheckLineRuleResult) Reset()         { *m = SingleCheckLineRuleResult{} }
func (m *SingleCheckLineRuleResult) String() string { return proto.CompactTextString(m) }
func (*SingleCheckLineRuleResult) ProtoMessage()    {}
func (*SingleCheckLineRuleResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{38}
}

func (m *SingleCheckLineRuleResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleCheckLineRuleResult.Unmarshal(m, b)
}
func (m *SingleCheckLineRuleResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleCheckLineRuleResult.Marshal(b, m, deterministic)
}
func (m *SingleCheckLineRuleResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleCheckLineRuleResult.Merge(m, src)
}
func (m *SingleCheckLineRuleResult) XXX_Size() int {
	return xxx_messageInfo_SingleCheckLineRuleResult.Size(m)
}
func (m *SingleCheckLineRuleResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleCheckLineRuleResult.DiscardUnknown(m)
}

var xxx_messageInfo_SingleCheckLineRuleResult proto.InternalMessageInfo

func (m *SingleCheckLineRuleResult) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *SingleCheckLineRuleResult) GetCheckResult() uint32 {
	if m != nil && m.CheckResult != nil {
		return *m.CheckResult
	}
	return 0
}

func (m *SingleCheckLineRuleResult) GetLimitDetail() []*RuleLimitDetail {
	if m != nil {
		return m.LimitDetail
	}
	return nil
}

type SingleCheckProductRuleResult struct {
	ProductId            *string            `protobuf:"bytes,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	CheckResult          *uint32            `protobuf:"varint,2,req,name=check_result,json=checkResult" json:"check_result,omitempty"`
	LimitDetail          []*RuleLimitDetail `protobuf:"bytes,3,rep,name=limit_detail,json=limitDetail" json:"limit_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SingleCheckProductRuleResult) Reset()         { *m = SingleCheckProductRuleResult{} }
func (m *SingleCheckProductRuleResult) String() string { return proto.CompactTextString(m) }
func (*SingleCheckProductRuleResult) ProtoMessage()    {}
func (*SingleCheckProductRuleResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{39}
}

func (m *SingleCheckProductRuleResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleCheckProductRuleResult.Unmarshal(m, b)
}
func (m *SingleCheckProductRuleResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleCheckProductRuleResult.Marshal(b, m, deterministic)
}
func (m *SingleCheckProductRuleResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleCheckProductRuleResult.Merge(m, src)
}
func (m *SingleCheckProductRuleResult) XXX_Size() int {
	return xxx_messageInfo_SingleCheckProductRuleResult.Size(m)
}
func (m *SingleCheckProductRuleResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleCheckProductRuleResult.DiscardUnknown(m)
}

var xxx_messageInfo_SingleCheckProductRuleResult proto.InternalMessageInfo

func (m *SingleCheckProductRuleResult) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *SingleCheckProductRuleResult) GetCheckResult() uint32 {
	if m != nil && m.CheckResult != nil {
		return *m.CheckResult
	}
	return 0
}

func (m *SingleCheckProductRuleResult) GetLimitDetail() []*RuleLimitDetail {
	if m != nil {
		return m.LimitDetail
	}
	return nil
}

type RuleLimitDetail struct {
	RuleType             *uint32  `protobuf:"varint,1,req,name=rule_type,json=ruleType" json:"rule_type,omitempty"`
	SingleCheckResult    *uint32  `protobuf:"varint,2,req,name=single_check_result,json=singleCheckResult" json:"single_check_result,omitempty"`
	Reason               *string  `protobuf:"bytes,3,req,name=reason" json:"reason,omitempty"`
	CalculateResult      *float64 `protobuf:"fixed64,4,opt,name=calculate_result,json=calculateResult" json:"calculate_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RuleLimitDetail) Reset()         { *m = RuleLimitDetail{} }
func (m *RuleLimitDetail) String() string { return proto.CompactTextString(m) }
func (*RuleLimitDetail) ProtoMessage()    {}
func (*RuleLimitDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{40}
}

func (m *RuleLimitDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RuleLimitDetail.Unmarshal(m, b)
}
func (m *RuleLimitDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RuleLimitDetail.Marshal(b, m, deterministic)
}
func (m *RuleLimitDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuleLimitDetail.Merge(m, src)
}
func (m *RuleLimitDetail) XXX_Size() int {
	return xxx_messageInfo_RuleLimitDetail.Size(m)
}
func (m *RuleLimitDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_RuleLimitDetail.DiscardUnknown(m)
}

var xxx_messageInfo_RuleLimitDetail proto.InternalMessageInfo

func (m *RuleLimitDetail) GetRuleType() uint32 {
	if m != nil && m.RuleType != nil {
		return *m.RuleType
	}
	return 0
}

func (m *RuleLimitDetail) GetSingleCheckResult() uint32 {
	if m != nil && m.SingleCheckResult != nil {
		return *m.SingleCheckResult
	}
	return 0
}

func (m *RuleLimitDetail) GetReason() string {
	if m != nil && m.Reason != nil {
		return *m.Reason
	}
	return ""
}

func (m *RuleLimitDetail) GetCalculateResult() float64 {
	if m != nil && m.CalculateResult != nil {
		return *m.CalculateResult
	}
	return 0
}

type LineRuleInfo struct {
	LineId               *string           `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	RuleInfoDetail       []*RuleInfoDetail `protobuf:"bytes,2,rep,name=rule_info_detail,json=ruleInfoDetail" json:"rule_info_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *LineRuleInfo) Reset()         { *m = LineRuleInfo{} }
func (m *LineRuleInfo) String() string { return proto.CompactTextString(m) }
func (*LineRuleInfo) ProtoMessage()    {}
func (*LineRuleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{41}
}

func (m *LineRuleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineRuleInfo.Unmarshal(m, b)
}
func (m *LineRuleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineRuleInfo.Marshal(b, m, deterministic)
}
func (m *LineRuleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineRuleInfo.Merge(m, src)
}
func (m *LineRuleInfo) XXX_Size() int {
	return xxx_messageInfo_LineRuleInfo.Size(m)
}
func (m *LineRuleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LineRuleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LineRuleInfo proto.InternalMessageInfo

func (m *LineRuleInfo) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *LineRuleInfo) GetRuleInfoDetail() []*RuleInfoDetail {
	if m != nil {
		return m.RuleInfoDetail
	}
	return nil
}

type RuleInfoDetail struct {
	RuleType             *uint32  `protobuf:"varint,1,req,name=rule_type,json=ruleType" json:"rule_type,omitempty"`
	LimitFlag            *uint32  `protobuf:"varint,2,req,name=limit_flag,json=limitFlag" json:"limit_flag,omitempty"`
	MaxWeight            *float64 `protobuf:"fixed64,3,opt,name=max_weight,json=maxWeight" json:"max_weight,omitempty"`
	MinWeight            *float64 `protobuf:"fixed64,4,opt,name=min_weight,json=minWeight" json:"min_weight,omitempty"`
	MaxSize              *float64 `protobuf:"fixed64,5,opt,name=max_size,json=maxSize" json:"max_size,omitempty"`
	MinSize              *float64 `protobuf:"fixed64,6,opt,name=min_size,json=minSize" json:"min_size,omitempty"`
	MaxLength            *float64 `protobuf:"fixed64,7,opt,name=max_length,json=maxLength" json:"max_length,omitempty"`
	MinLength            *float64 `protobuf:"fixed64,8,opt,name=min_length,json=minLength" json:"min_length,omitempty"`
	MaxWidth             *float64 `protobuf:"fixed64,9,opt,name=max_width,json=maxWidth" json:"max_width,omitempty"`
	MinWidth             *float64 `protobuf:"fixed64,10,opt,name=min_width,json=minWidth" json:"min_width,omitempty"`
	MaxHeight            *float64 `protobuf:"fixed64,11,opt,name=max_height,json=maxHeight" json:"max_height,omitempty"`
	MinHeight            *float64 `protobuf:"fixed64,12,opt,name=min_height,json=minHeight" json:"min_height,omitempty"`
	UseParcelLib         *bool    `protobuf:"varint,13,opt,name=use_parcel_lib,json=useParcelLib" json:"use_parcel_lib,omitempty"`
	ByPass               *bool    `protobuf:"varint,14,opt,name=by_pass,json=byPass" json:"by_pass,omitempty"`
	ByPassPurchaseTime   *uint32  `protobuf:"varint,15,opt,name=by_pass_purchase_time,json=byPassPurchaseTime" json:"by_pass_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RuleInfoDetail) Reset()         { *m = RuleInfoDetail{} }
func (m *RuleInfoDetail) String() string { return proto.CompactTextString(m) }
func (*RuleInfoDetail) ProtoMessage()    {}
func (*RuleInfoDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{42}
}

func (m *RuleInfoDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RuleInfoDetail.Unmarshal(m, b)
}
func (m *RuleInfoDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RuleInfoDetail.Marshal(b, m, deterministic)
}
func (m *RuleInfoDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuleInfoDetail.Merge(m, src)
}
func (m *RuleInfoDetail) XXX_Size() int {
	return xxx_messageInfo_RuleInfoDetail.Size(m)
}
func (m *RuleInfoDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_RuleInfoDetail.DiscardUnknown(m)
}

var xxx_messageInfo_RuleInfoDetail proto.InternalMessageInfo

func (m *RuleInfoDetail) GetRuleType() uint32 {
	if m != nil && m.RuleType != nil {
		return *m.RuleType
	}
	return 0
}

func (m *RuleInfoDetail) GetLimitFlag() uint32 {
	if m != nil && m.LimitFlag != nil {
		return *m.LimitFlag
	}
	return 0
}

func (m *RuleInfoDetail) GetMaxWeight() float64 {
	if m != nil && m.MaxWeight != nil {
		return *m.MaxWeight
	}
	return 0
}

func (m *RuleInfoDetail) GetMinWeight() float64 {
	if m != nil && m.MinWeight != nil {
		return *m.MinWeight
	}
	return 0
}

func (m *RuleInfoDetail) GetMaxSize() float64 {
	if m != nil && m.MaxSize != nil {
		return *m.MaxSize
	}
	return 0
}

func (m *RuleInfoDetail) GetMinSize() float64 {
	if m != nil && m.MinSize != nil {
		return *m.MinSize
	}
	return 0
}

func (m *RuleInfoDetail) GetMaxLength() float64 {
	if m != nil && m.MaxLength != nil {
		return *m.MaxLength
	}
	return 0
}

func (m *RuleInfoDetail) GetMinLength() float64 {
	if m != nil && m.MinLength != nil {
		return *m.MinLength
	}
	return 0
}

func (m *RuleInfoDetail) GetMaxWidth() float64 {
	if m != nil && m.MaxWidth != nil {
		return *m.MaxWidth
	}
	return 0
}

func (m *RuleInfoDetail) GetMinWidth() float64 {
	if m != nil && m.MinWidth != nil {
		return *m.MinWidth
	}
	return 0
}

func (m *RuleInfoDetail) GetMaxHeight() float64 {
	if m != nil && m.MaxHeight != nil {
		return *m.MaxHeight
	}
	return 0
}

func (m *RuleInfoDetail) GetMinHeight() float64 {
	if m != nil && m.MinHeight != nil {
		return *m.MinHeight
	}
	return 0
}

func (m *RuleInfoDetail) GetUseParcelLib() bool {
	if m != nil && m.UseParcelLib != nil {
		return *m.UseParcelLib
	}
	return false
}

func (m *RuleInfoDetail) GetByPass() bool {
	if m != nil && m.ByPass != nil {
		return *m.ByPass
	}
	return false
}

func (m *RuleInfoDetail) GetByPassPurchaseTime() uint32 {
	if m != nil && m.ByPassPurchaseTime != nil {
		return *m.ByPassPurchaseTime
	}
	return 0
}

type RealRuleInfoDetail struct {
	Id                     *uint64  `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	ProductId              *string  `protobuf:"bytes,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	Region                 *string  `protobuf:"bytes,3,req,name=region" json:"region,omitempty"`
	RuleType               *uint32  `protobuf:"varint,4,req,name=rule_type,json=ruleType" json:"rule_type,omitempty"`
	LimitFlag              *uint32  `protobuf:"varint,5,req,name=limit_flag,json=limitFlag" json:"limit_flag,omitempty"`
	MaxWeight              *float64 `protobuf:"fixed64,6,opt,name=max_weight,json=maxWeight" json:"max_weight,omitempty"`
	MinWeight              *float64 `protobuf:"fixed64,7,opt,name=min_weight,json=minWeight" json:"min_weight,omitempty"`
	MaxSize                *float64 `protobuf:"fixed64,8,opt,name=max_size,json=maxSize" json:"max_size,omitempty"`
	MinSize                *float64 `protobuf:"fixed64,9,opt,name=min_size,json=minSize" json:"min_size,omitempty"`
	MaxLength              *float64 `protobuf:"fixed64,10,opt,name=max_length,json=maxLength" json:"max_length,omitempty"`
	MinLength              *float64 `protobuf:"fixed64,11,opt,name=min_length,json=minLength" json:"min_length,omitempty"`
	MaxWidth               *float64 `protobuf:"fixed64,12,opt,name=max_width,json=maxWidth" json:"max_width,omitempty"`
	MinWidth               *float64 `protobuf:"fixed64,13,opt,name=min_width,json=minWidth" json:"min_width,omitempty"`
	MaxHeight              *float64 `protobuf:"fixed64,14,opt,name=max_height,json=maxHeight" json:"max_height,omitempty"`
	MinHeight              *float64 `protobuf:"fixed64,15,opt,name=min_height,json=minHeight" json:"min_height,omitempty"`
	MaxSymbol              *uint32  `protobuf:"varint,16,opt,name=max_symbol,json=maxSymbol" json:"max_symbol,omitempty"`
	MinSymbol              *uint32  `protobuf:"varint,17,opt,name=min_symbol,json=minSymbol" json:"min_symbol,omitempty"`
	VolumetricFactor       *uint32  `protobuf:"varint,18,opt,name=volumetric_factor,json=volumetricFactor" json:"volumetric_factor,omitempty"`
	SortFlag               *uint32  `protobuf:"varint,19,opt,name=sort_flag,json=sortFlag" json:"sort_flag,omitempty"`
	Formula                *uint32  `protobuf:"varint,20,opt,name=formula" json:"formula,omitempty"`
	ConditionFormulaParams *string  `protobuf:"bytes,21,opt,name=condition_formula_params,json=conditionFormulaParams" json:"condition_formula_params,omitempty"`
	UseParcelLibrary       *bool    `protobuf:"varint,22,opt,name=use_parcel_library,json=useParcelLibrary" json:"use_parcel_library,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *RealRuleInfoDetail) Reset()         { *m = RealRuleInfoDetail{} }
func (m *RealRuleInfoDetail) String() string { return proto.CompactTextString(m) }
func (*RealRuleInfoDetail) ProtoMessage()    {}
func (*RealRuleInfoDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{43}
}

func (m *RealRuleInfoDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RealRuleInfoDetail.Unmarshal(m, b)
}
func (m *RealRuleInfoDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RealRuleInfoDetail.Marshal(b, m, deterministic)
}
func (m *RealRuleInfoDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RealRuleInfoDetail.Merge(m, src)
}
func (m *RealRuleInfoDetail) XXX_Size() int {
	return xxx_messageInfo_RealRuleInfoDetail.Size(m)
}
func (m *RealRuleInfoDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_RealRuleInfoDetail.DiscardUnknown(m)
}

var xxx_messageInfo_RealRuleInfoDetail proto.InternalMessageInfo

func (m *RealRuleInfoDetail) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *RealRuleInfoDetail) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *RealRuleInfoDetail) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *RealRuleInfoDetail) GetRuleType() uint32 {
	if m != nil && m.RuleType != nil {
		return *m.RuleType
	}
	return 0
}

func (m *RealRuleInfoDetail) GetLimitFlag() uint32 {
	if m != nil && m.LimitFlag != nil {
		return *m.LimitFlag
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMaxWeight() float64 {
	if m != nil && m.MaxWeight != nil {
		return *m.MaxWeight
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMinWeight() float64 {
	if m != nil && m.MinWeight != nil {
		return *m.MinWeight
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMaxSize() float64 {
	if m != nil && m.MaxSize != nil {
		return *m.MaxSize
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMinSize() float64 {
	if m != nil && m.MinSize != nil {
		return *m.MinSize
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMaxLength() float64 {
	if m != nil && m.MaxLength != nil {
		return *m.MaxLength
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMinLength() float64 {
	if m != nil && m.MinLength != nil {
		return *m.MinLength
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMaxWidth() float64 {
	if m != nil && m.MaxWidth != nil {
		return *m.MaxWidth
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMinWidth() float64 {
	if m != nil && m.MinWidth != nil {
		return *m.MinWidth
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMaxHeight() float64 {
	if m != nil && m.MaxHeight != nil {
		return *m.MaxHeight
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMinHeight() float64 {
	if m != nil && m.MinHeight != nil {
		return *m.MinHeight
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMaxSymbol() uint32 {
	if m != nil && m.MaxSymbol != nil {
		return *m.MaxSymbol
	}
	return 0
}

func (m *RealRuleInfoDetail) GetMinSymbol() uint32 {
	if m != nil && m.MinSymbol != nil {
		return *m.MinSymbol
	}
	return 0
}

func (m *RealRuleInfoDetail) GetVolumetricFactor() uint32 {
	if m != nil && m.VolumetricFactor != nil {
		return *m.VolumetricFactor
	}
	return 0
}

func (m *RealRuleInfoDetail) GetSortFlag() uint32 {
	if m != nil && m.SortFlag != nil {
		return *m.SortFlag
	}
	return 0
}

func (m *RealRuleInfoDetail) GetFormula() uint32 {
	if m != nil && m.Formula != nil {
		return *m.Formula
	}
	return 0
}

func (m *RealRuleInfoDetail) GetConditionFormulaParams() string {
	if m != nil && m.ConditionFormulaParams != nil {
		return *m.ConditionFormulaParams
	}
	return ""
}

func (m *RealRuleInfoDetail) GetUseParcelLibrary() bool {
	if m != nil && m.UseParcelLibrary != nil {
		return *m.UseParcelLibrary
	}
	return false
}

type BatchCalculateInfo struct {
	CalculateId          *string                       `protobuf:"bytes,1,req,name=calculate_id,json=calculateId" json:"calculate_id,omitempty"`
	BaseInfo             *CalculateBaseInfo            `protobuf:"bytes,2,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	SkuGroup             []*BatchCalculateSkuGroupInfo `protobuf:"bytes,3,rep,name=sku_group,json=skuGroup" json:"sku_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatchCalculateInfo) Reset()         { *m = BatchCalculateInfo{} }
func (m *BatchCalculateInfo) String() string { return proto.CompactTextString(m) }
func (*BatchCalculateInfo) ProtoMessage()    {}
func (*BatchCalculateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{44}
}

func (m *BatchCalculateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCalculateInfo.Unmarshal(m, b)
}
func (m *BatchCalculateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCalculateInfo.Marshal(b, m, deterministic)
}
func (m *BatchCalculateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCalculateInfo.Merge(m, src)
}
func (m *BatchCalculateInfo) XXX_Size() int {
	return xxx_messageInfo_BatchCalculateInfo.Size(m)
}
func (m *BatchCalculateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCalculateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCalculateInfo proto.InternalMessageInfo

func (m *BatchCalculateInfo) GetCalculateId() string {
	if m != nil && m.CalculateId != nil {
		return *m.CalculateId
	}
	return ""
}

func (m *BatchCalculateInfo) GetBaseInfo() *CalculateBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *BatchCalculateInfo) GetSkuGroup() []*BatchCalculateSkuGroupInfo {
	if m != nil {
		return m.SkuGroup
	}
	return nil
}

type BatchCalculateSkuGroupInfo struct {
	GroupId              *string    `protobuf:"bytes,1,req,name=group_id,json=groupId" json:"group_id,omitempty"`
	SkuInfo              []*SkuInfo `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchCalculateSkuGroupInfo) Reset()         { *m = BatchCalculateSkuGroupInfo{} }
func (m *BatchCalculateSkuGroupInfo) String() string { return proto.CompactTextString(m) }
func (*BatchCalculateSkuGroupInfo) ProtoMessage()    {}
func (*BatchCalculateSkuGroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{45}
}

func (m *BatchCalculateSkuGroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCalculateSkuGroupInfo.Unmarshal(m, b)
}
func (m *BatchCalculateSkuGroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCalculateSkuGroupInfo.Marshal(b, m, deterministic)
}
func (m *BatchCalculateSkuGroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCalculateSkuGroupInfo.Merge(m, src)
}
func (m *BatchCalculateSkuGroupInfo) XXX_Size() int {
	return xxx_messageInfo_BatchCalculateSkuGroupInfo.Size(m)
}
func (m *BatchCalculateSkuGroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCalculateSkuGroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCalculateSkuGroupInfo proto.InternalMessageInfo

func (m *BatchCalculateSkuGroupInfo) GetGroupId() string {
	if m != nil && m.GroupId != nil {
		return *m.GroupId
	}
	return ""
}

func (m *BatchCalculateSkuGroupInfo) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

type BatchCalculateResult struct {
	CalculateId          *string                         `protobuf:"bytes,1,req,name=calculate_id,json=calculateId" json:"calculate_id,omitempty"`
	GroupResult          []*BatchCalculateSkuGroupResult `protobuf:"bytes,2,rep,name=group_result,json=groupResult" json:"group_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *BatchCalculateResult) Reset()         { *m = BatchCalculateResult{} }
func (m *BatchCalculateResult) String() string { return proto.CompactTextString(m) }
func (*BatchCalculateResult) ProtoMessage()    {}
func (*BatchCalculateResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{46}
}

func (m *BatchCalculateResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCalculateResult.Unmarshal(m, b)
}
func (m *BatchCalculateResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCalculateResult.Marshal(b, m, deterministic)
}
func (m *BatchCalculateResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCalculateResult.Merge(m, src)
}
func (m *BatchCalculateResult) XXX_Size() int {
	return xxx_messageInfo_BatchCalculateResult.Size(m)
}
func (m *BatchCalculateResult) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCalculateResult.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCalculateResult proto.InternalMessageInfo

func (m *BatchCalculateResult) GetCalculateId() string {
	if m != nil && m.CalculateId != nil {
		return *m.CalculateId
	}
	return ""
}

func (m *BatchCalculateResult) GetGroupResult() []*BatchCalculateSkuGroupResult {
	if m != nil {
		return m.GroupResult
	}
	return nil
}

type BatchCalculateSkuGroupResult struct {
	GroupId              *string  `protobuf:"bytes,1,req,name=group_id,json=groupId" json:"group_id,omitempty"`
	ResultWeight         *float64 `protobuf:"fixed64,2,req,name=result_weight,json=resultWeight" json:"result_weight,omitempty"`
	CalculateMessage     *string  `protobuf:"bytes,3,req,name=calculate_message,json=calculateMessage" json:"calculate_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCalculateSkuGroupResult) Reset()         { *m = BatchCalculateSkuGroupResult{} }
func (m *BatchCalculateSkuGroupResult) String() string { return proto.CompactTextString(m) }
func (*BatchCalculateSkuGroupResult) ProtoMessage()    {}
func (*BatchCalculateSkuGroupResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{47}
}

func (m *BatchCalculateSkuGroupResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCalculateSkuGroupResult.Unmarshal(m, b)
}
func (m *BatchCalculateSkuGroupResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCalculateSkuGroupResult.Marshal(b, m, deterministic)
}
func (m *BatchCalculateSkuGroupResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCalculateSkuGroupResult.Merge(m, src)
}
func (m *BatchCalculateSkuGroupResult) XXX_Size() int {
	return xxx_messageInfo_BatchCalculateSkuGroupResult.Size(m)
}
func (m *BatchCalculateSkuGroupResult) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCalculateSkuGroupResult.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCalculateSkuGroupResult proto.InternalMessageInfo

func (m *BatchCalculateSkuGroupResult) GetGroupId() string {
	if m != nil && m.GroupId != nil {
		return *m.GroupId
	}
	return ""
}

func (m *BatchCalculateSkuGroupResult) GetResultWeight() float64 {
	if m != nil && m.ResultWeight != nil {
		return *m.ResultWeight
	}
	return 0
}

func (m *BatchCalculateSkuGroupResult) GetCalculateMessage() string {
	if m != nil && m.CalculateMessage != nil {
		return *m.CalculateMessage
	}
	return ""
}

// 批量获取产品校验重量req对象
type BatchCalculateProductValidateWeightRequest struct {
	ReqHeader            *ReqHeader                        `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ReqInfoList          []*SingleProductValidateWeightReq `protobuf:"bytes,2,rep,name=req_info_list,json=reqInfoList" json:"req_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *BatchCalculateProductValidateWeightRequest) Reset() {
	*m = BatchCalculateProductValidateWeightRequest{}
}
func (m *BatchCalculateProductValidateWeightRequest) String() string {
	return proto.CompactTextString(m)
}
func (*BatchCalculateProductValidateWeightRequest) ProtoMessage() {}
func (*BatchCalculateProductValidateWeightRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{48}
}

func (m *BatchCalculateProductValidateWeightRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCalculateProductValidateWeightRequest.Unmarshal(m, b)
}
func (m *BatchCalculateProductValidateWeightRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCalculateProductValidateWeightRequest.Marshal(b, m, deterministic)
}
func (m *BatchCalculateProductValidateWeightRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCalculateProductValidateWeightRequest.Merge(m, src)
}
func (m *BatchCalculateProductValidateWeightRequest) XXX_Size() int {
	return xxx_messageInfo_BatchCalculateProductValidateWeightRequest.Size(m)
}
func (m *BatchCalculateProductValidateWeightRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCalculateProductValidateWeightRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCalculateProductValidateWeightRequest proto.InternalMessageInfo

func (m *BatchCalculateProductValidateWeightRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCalculateProductValidateWeightRequest) GetReqInfoList() []*SingleProductValidateWeightReq {
	if m != nil {
		return m.ReqInfoList
	}
	return nil
}

type ProductValidateWeightBaseInfo struct {
	QueryId              *uint32            `protobuf:"varint,1,req,name=query_id,json=queryId" json:"query_id,omitempty"`
	ProductId            *string            `protobuf:"bytes,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	ConditionParams      []*ConditionParams `protobuf:"bytes,3,rep,name=condition_params,json=conditionParams" json:"condition_params,omitempty"`
	SortFlag             *uint32            `protobuf:"varint,4,opt,name=sort_flag,json=sortFlag" json:"sort_flag,omitempty"`
	VolumetricFactor     *uint32            `protobuf:"varint,5,opt,name=volumetric_factor,json=volumetricFactor" json:"volumetric_factor,omitempty"`
	BuyerPurchaseTime    *uint32            `protobuf:"varint,6,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ProductValidateWeightBaseInfo) Reset()         { *m = ProductValidateWeightBaseInfo{} }
func (m *ProductValidateWeightBaseInfo) String() string { return proto.CompactTextString(m) }
func (*ProductValidateWeightBaseInfo) ProtoMessage()    {}
func (*ProductValidateWeightBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{49}
}

func (m *ProductValidateWeightBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductValidateWeightBaseInfo.Unmarshal(m, b)
}
func (m *ProductValidateWeightBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductValidateWeightBaseInfo.Marshal(b, m, deterministic)
}
func (m *ProductValidateWeightBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductValidateWeightBaseInfo.Merge(m, src)
}
func (m *ProductValidateWeightBaseInfo) XXX_Size() int {
	return xxx_messageInfo_ProductValidateWeightBaseInfo.Size(m)
}
func (m *ProductValidateWeightBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductValidateWeightBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ProductValidateWeightBaseInfo proto.InternalMessageInfo

func (m *ProductValidateWeightBaseInfo) GetQueryId() uint32 {
	if m != nil && m.QueryId != nil {
		return *m.QueryId
	}
	return 0
}

func (m *ProductValidateWeightBaseInfo) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *ProductValidateWeightBaseInfo) GetConditionParams() []*ConditionParams {
	if m != nil {
		return m.ConditionParams
	}
	return nil
}

func (m *ProductValidateWeightBaseInfo) GetSortFlag() uint32 {
	if m != nil && m.SortFlag != nil {
		return *m.SortFlag
	}
	return 0
}

func (m *ProductValidateWeightBaseInfo) GetVolumetricFactor() uint32 {
	if m != nil && m.VolumetricFactor != nil {
		return *m.VolumetricFactor
	}
	return 0
}

func (m *ProductValidateWeightBaseInfo) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

type SingleProductValidateWeightReq struct {
	BaseInfo             *ProductValidateWeightBaseInfo `protobuf:"bytes,1,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	OrderInfo            *OrderInfo                     `protobuf:"bytes,2,req,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	SkuInfo              []*SkuInfo                     `protobuf:"bytes,3,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *SingleProductValidateWeightReq) Reset()         { *m = SingleProductValidateWeightReq{} }
func (m *SingleProductValidateWeightReq) String() string { return proto.CompactTextString(m) }
func (*SingleProductValidateWeightReq) ProtoMessage()    {}
func (*SingleProductValidateWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{50}
}

func (m *SingleProductValidateWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleProductValidateWeightReq.Unmarshal(m, b)
}
func (m *SingleProductValidateWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleProductValidateWeightReq.Marshal(b, m, deterministic)
}
func (m *SingleProductValidateWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleProductValidateWeightReq.Merge(m, src)
}
func (m *SingleProductValidateWeightReq) XXX_Size() int {
	return xxx_messageInfo_SingleProductValidateWeightReq.Size(m)
}
func (m *SingleProductValidateWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleProductValidateWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_SingleProductValidateWeightReq proto.InternalMessageInfo

func (m *SingleProductValidateWeightReq) GetBaseInfo() *ProductValidateWeightBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *SingleProductValidateWeightReq) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *SingleProductValidateWeightReq) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

type BatchCalculateProductValidateWeightResponse struct {
	RespHeader           *RespHeader                          `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ResultList           []*SingleProductValidateWeightResult `protobuf:"bytes,2,rep,name=result_list,json=resultList" json:"result_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *BatchCalculateProductValidateWeightResponse) Reset() {
	*m = BatchCalculateProductValidateWeightResponse{}
}
func (m *BatchCalculateProductValidateWeightResponse) String() string {
	return proto.CompactTextString(m)
}
func (*BatchCalculateProductValidateWeightResponse) ProtoMessage() {}
func (*BatchCalculateProductValidateWeightResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{51}
}

func (m *BatchCalculateProductValidateWeightResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCalculateProductValidateWeightResponse.Unmarshal(m, b)
}
func (m *BatchCalculateProductValidateWeightResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCalculateProductValidateWeightResponse.Marshal(b, m, deterministic)
}
func (m *BatchCalculateProductValidateWeightResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCalculateProductValidateWeightResponse.Merge(m, src)
}
func (m *BatchCalculateProductValidateWeightResponse) XXX_Size() int {
	return xxx_messageInfo_BatchCalculateProductValidateWeightResponse.Size(m)
}
func (m *BatchCalculateProductValidateWeightResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCalculateProductValidateWeightResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCalculateProductValidateWeightResponse proto.InternalMessageInfo

func (m *BatchCalculateProductValidateWeightResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCalculateProductValidateWeightResponse) GetResultList() []*SingleProductValidateWeightResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

type SingleProductValidateWeightResult struct {
	ItemCode             *int32   `protobuf:"varint,1,req,name=item_code,json=itemCode" json:"item_code,omitempty"`
	ItemMsg              *string  `protobuf:"bytes,2,req,name=item_msg,json=itemMsg" json:"item_msg,omitempty"`
	QueryId              *uint32  `protobuf:"varint,3,req,name=query_id,json=queryId" json:"query_id,omitempty"`
	ProductId            *string  `protobuf:"bytes,4,req,name=product_id,json=productId" json:"product_id,omitempty"`
	ResultWeight         *float64 `protobuf:"fixed64,5,req,name=result_weight,json=resultWeight" json:"result_weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingleProductValidateWeightResult) Reset()         { *m = SingleProductValidateWeightResult{} }
func (m *SingleProductValidateWeightResult) String() string { return proto.CompactTextString(m) }
func (*SingleProductValidateWeightResult) ProtoMessage()    {}
func (*SingleProductValidateWeightResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{52}
}

func (m *SingleProductValidateWeightResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleProductValidateWeightResult.Unmarshal(m, b)
}
func (m *SingleProductValidateWeightResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleProductValidateWeightResult.Marshal(b, m, deterministic)
}
func (m *SingleProductValidateWeightResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleProductValidateWeightResult.Merge(m, src)
}
func (m *SingleProductValidateWeightResult) XXX_Size() int {
	return xxx_messageInfo_SingleProductValidateWeightResult.Size(m)
}
func (m *SingleProductValidateWeightResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleProductValidateWeightResult.DiscardUnknown(m)
}

var xxx_messageInfo_SingleProductValidateWeightResult proto.InternalMessageInfo

func (m *SingleProductValidateWeightResult) GetItemCode() int32 {
	if m != nil && m.ItemCode != nil {
		return *m.ItemCode
	}
	return 0
}

func (m *SingleProductValidateWeightResult) GetItemMsg() string {
	if m != nil && m.ItemMsg != nil {
		return *m.ItemMsg
	}
	return ""
}

func (m *SingleProductValidateWeightResult) GetQueryId() uint32 {
	if m != nil && m.QueryId != nil {
		return *m.QueryId
	}
	return 0
}

func (m *SingleProductValidateWeightResult) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *SingleProductValidateWeightResult) GetResultWeight() float64 {
	if m != nil && m.ResultWeight != nil {
		return *m.ResultWeight
	}
	return 0
}

type ProductSideLimitsRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductIdList        []string   `protobuf:"bytes,2,rep,name=product_id_list,json=productIdList" json:"product_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ProductSideLimitsRequest) Reset()         { *m = ProductSideLimitsRequest{} }
func (m *ProductSideLimitsRequest) String() string { return proto.CompactTextString(m) }
func (*ProductSideLimitsRequest) ProtoMessage()    {}
func (*ProductSideLimitsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{53}
}

func (m *ProductSideLimitsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductSideLimitsRequest.Unmarshal(m, b)
}
func (m *ProductSideLimitsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductSideLimitsRequest.Marshal(b, m, deterministic)
}
func (m *ProductSideLimitsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductSideLimitsRequest.Merge(m, src)
}
func (m *ProductSideLimitsRequest) XXX_Size() int {
	return xxx_messageInfo_ProductSideLimitsRequest.Size(m)
}
func (m *ProductSideLimitsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductSideLimitsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ProductSideLimitsRequest proto.InternalMessageInfo

func (m *ProductSideLimitsRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *ProductSideLimitsRequest) GetProductIdList() []string {
	if m != nil {
		return m.ProductIdList
	}
	return nil
}

type ProductSideLimitsResponse struct {
	RespHeader           *RespHeader               `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	SizeLimitsInfo       map[string]*SizeLimitInfo `protobuf:"bytes,2,rep,name=size_limits_info,json=sizeLimitsInfo" json:"size_limits_info,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ProductSideLimitsResponse) Reset()         { *m = ProductSideLimitsResponse{} }
func (m *ProductSideLimitsResponse) String() string { return proto.CompactTextString(m) }
func (*ProductSideLimitsResponse) ProtoMessage()    {}
func (*ProductSideLimitsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{54}
}

func (m *ProductSideLimitsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductSideLimitsResponse.Unmarshal(m, b)
}
func (m *ProductSideLimitsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductSideLimitsResponse.Marshal(b, m, deterministic)
}
func (m *ProductSideLimitsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductSideLimitsResponse.Merge(m, src)
}
func (m *ProductSideLimitsResponse) XXX_Size() int {
	return xxx_messageInfo_ProductSideLimitsResponse.Size(m)
}
func (m *ProductSideLimitsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductSideLimitsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ProductSideLimitsResponse proto.InternalMessageInfo

func (m *ProductSideLimitsResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *ProductSideLimitsResponse) GetSizeLimitsInfo() map[string]*SizeLimitInfo {
	if m != nil {
		return m.SizeLimitsInfo
	}
	return nil
}

type SizeLimitInfo struct {
	MaxLength            *float64 `protobuf:"fixed64,1,req,name=max_length,json=maxLength" json:"max_length,omitempty"`
	MinLength            *float64 `protobuf:"fixed64,2,req,name=min_length,json=minLength" json:"min_length,omitempty"`
	MaxWidth             *float64 `protobuf:"fixed64,3,req,name=max_width,json=maxWidth" json:"max_width,omitempty"`
	MinWidth             *float64 `protobuf:"fixed64,4,req,name=min_width,json=minWidth" json:"min_width,omitempty"`
	MaxHeight            *float64 `protobuf:"fixed64,5,req,name=max_height,json=maxHeight" json:"max_height,omitempty"`
	MinHeight            *float64 `protobuf:"fixed64,6,req,name=min_height,json=minHeight" json:"min_height,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SizeLimitInfo) Reset()         { *m = SizeLimitInfo{} }
func (m *SizeLimitInfo) String() string { return proto.CompactTextString(m) }
func (*SizeLimitInfo) ProtoMessage()    {}
func (*SizeLimitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{55}
}

func (m *SizeLimitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SizeLimitInfo.Unmarshal(m, b)
}
func (m *SizeLimitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SizeLimitInfo.Marshal(b, m, deterministic)
}
func (m *SizeLimitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SizeLimitInfo.Merge(m, src)
}
func (m *SizeLimitInfo) XXX_Size() int {
	return xxx_messageInfo_SizeLimitInfo.Size(m)
}
func (m *SizeLimitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SizeLimitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SizeLimitInfo proto.InternalMessageInfo

func (m *SizeLimitInfo) GetMaxLength() float64 {
	if m != nil && m.MaxLength != nil {
		return *m.MaxLength
	}
	return 0
}

func (m *SizeLimitInfo) GetMinLength() float64 {
	if m != nil && m.MinLength != nil {
		return *m.MinLength
	}
	return 0
}

func (m *SizeLimitInfo) GetMaxWidth() float64 {
	if m != nil && m.MaxWidth != nil {
		return *m.MaxWidth
	}
	return 0
}

func (m *SizeLimitInfo) GetMinWidth() float64 {
	if m != nil && m.MinWidth != nil {
		return *m.MinWidth
	}
	return 0
}

func (m *SizeLimitInfo) GetMaxHeight() float64 {
	if m != nil && m.MaxHeight != nil {
		return *m.MaxHeight
	}
	return 0
}

func (m *SizeLimitInfo) GetMinHeight() float64 {
	if m != nil && m.MinHeight != nil {
		return *m.MinHeight
	}
	return 0
}

type SingleCheckLaneRuleResult struct {
	LaneCode             *string                      `protobuf:"bytes,1,req,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	Code                 *int32                       `protobuf:"varint,2,req,name=code" json:"code,omitempty"`
	Message              *string                      `protobuf:"bytes,3,opt,name=message" json:"message,omitempty"`
	RuleType             *uint32                      `protobuf:"varint,4,opt,name=rule_type,json=ruleType" json:"rule_type,omitempty"`
	LineRuleRes          []*SingleCheckLineRuleResult `protobuf:"bytes,5,rep,name=line_rule_res,json=lineRuleRes" json:"line_rule_res,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *SingleCheckLaneRuleResult) Reset()         { *m = SingleCheckLaneRuleResult{} }
func (m *SingleCheckLaneRuleResult) String() string { return proto.CompactTextString(m) }
func (*SingleCheckLaneRuleResult) ProtoMessage()    {}
func (*SingleCheckLaneRuleResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{56}
}

func (m *SingleCheckLaneRuleResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleCheckLaneRuleResult.Unmarshal(m, b)
}
func (m *SingleCheckLaneRuleResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleCheckLaneRuleResult.Marshal(b, m, deterministic)
}
func (m *SingleCheckLaneRuleResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleCheckLaneRuleResult.Merge(m, src)
}
func (m *SingleCheckLaneRuleResult) XXX_Size() int {
	return xxx_messageInfo_SingleCheckLaneRuleResult.Size(m)
}
func (m *SingleCheckLaneRuleResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleCheckLaneRuleResult.DiscardUnknown(m)
}

var xxx_messageInfo_SingleCheckLaneRuleResult proto.InternalMessageInfo

func (m *SingleCheckLaneRuleResult) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *SingleCheckLaneRuleResult) GetCode() int32 {
	if m != nil && m.Code != nil {
		return *m.Code
	}
	return 0
}

func (m *SingleCheckLaneRuleResult) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *SingleCheckLaneRuleResult) GetRuleType() uint32 {
	if m != nil && m.RuleType != nil {
		return *m.RuleType
	}
	return 0
}

func (m *SingleCheckLaneRuleResult) GetLineRuleRes() []*SingleCheckLineRuleResult {
	if m != nil {
		return m.LineRuleRes
	}
	return nil
}

type CheckLaneRuleResponse struct {
	RuleLimitInfoMap     map[string]*SingleCheckLaneRuleResult `protobuf:"bytes,1,rep,name=rule_limit_info_map,json=ruleLimitInfoMap" json:"rule_limit_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *CheckLaneRuleResponse) Reset()         { *m = CheckLaneRuleResponse{} }
func (m *CheckLaneRuleResponse) String() string { return proto.CompactTextString(m) }
func (*CheckLaneRuleResponse) ProtoMessage()    {}
func (*CheckLaneRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{57}
}

func (m *CheckLaneRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLaneRuleResponse.Unmarshal(m, b)
}
func (m *CheckLaneRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLaneRuleResponse.Marshal(b, m, deterministic)
}
func (m *CheckLaneRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLaneRuleResponse.Merge(m, src)
}
func (m *CheckLaneRuleResponse) XXX_Size() int {
	return xxx_messageInfo_CheckLaneRuleResponse.Size(m)
}
func (m *CheckLaneRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLaneRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLaneRuleResponse proto.InternalMessageInfo

func (m *CheckLaneRuleResponse) GetRuleLimitInfoMap() map[string]*SingleCheckLaneRuleResult {
	if m != nil {
		return m.RuleLimitInfoMap
	}
	return nil
}

type SingleCheckLaneRuleRequest struct {
	UniqueId             *string           `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	Region               *string           `protobuf:"bytes,2,req,name=region" json:"region,omitempty"`
	SkuInfo              []*SkuInfo        `protobuf:"bytes,3,rep,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	LaneCodeList         []string          `protobuf:"bytes,4,rep,name=lane_code_list,json=laneCodeList" json:"lane_code_list,omitempty"`
	OrderInfo            *OrderInfo        `protobuf:"bytes,5,opt,name=order_info,json=orderInfo" json:"order_info,omitempty"`
	SubPackageInfo       []*SubPackageInfo `protobuf:"bytes,6,rep,name=sub_package_info,json=subPackageInfo" json:"sub_package_info,omitempty"`
	BuyerPurchaseTime    *uint32           `protobuf:"varint,7,opt,name=buyer_purchase_time,json=buyerPurchaseTime" json:"buyer_purchase_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SingleCheckLaneRuleRequest) Reset()         { *m = SingleCheckLaneRuleRequest{} }
func (m *SingleCheckLaneRuleRequest) String() string { return proto.CompactTextString(m) }
func (*SingleCheckLaneRuleRequest) ProtoMessage()    {}
func (*SingleCheckLaneRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{58}
}

func (m *SingleCheckLaneRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleCheckLaneRuleRequest.Unmarshal(m, b)
}
func (m *SingleCheckLaneRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleCheckLaneRuleRequest.Marshal(b, m, deterministic)
}
func (m *SingleCheckLaneRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleCheckLaneRuleRequest.Merge(m, src)
}
func (m *SingleCheckLaneRuleRequest) XXX_Size() int {
	return xxx_messageInfo_SingleCheckLaneRuleRequest.Size(m)
}
func (m *SingleCheckLaneRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleCheckLaneRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SingleCheckLaneRuleRequest proto.InternalMessageInfo

func (m *SingleCheckLaneRuleRequest) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SingleCheckLaneRuleRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *SingleCheckLaneRuleRequest) GetSkuInfo() []*SkuInfo {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func (m *SingleCheckLaneRuleRequest) GetLaneCodeList() []string {
	if m != nil {
		return m.LaneCodeList
	}
	return nil
}

func (m *SingleCheckLaneRuleRequest) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *SingleCheckLaneRuleRequest) GetSubPackageInfo() []*SubPackageInfo {
	if m != nil {
		return m.SubPackageInfo
	}
	return nil
}

func (m *SingleCheckLaneRuleRequest) GetBuyerPurchaseTime() uint32 {
	if m != nil && m.BuyerPurchaseTime != nil {
		return *m.BuyerPurchaseTime
	}
	return 0
}

type BatchCheckLaneRuleRequest struct {
	ReqHeader            *ReqHeader                    `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	CheckLaneRuleList    []*SingleCheckLaneRuleRequest `protobuf:"bytes,2,rep,name=check_lane_rule_list,json=checkLaneRuleList" json:"check_lane_rule_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatchCheckLaneRuleRequest) Reset()         { *m = BatchCheckLaneRuleRequest{} }
func (m *BatchCheckLaneRuleRequest) String() string { return proto.CompactTextString(m) }
func (*BatchCheckLaneRuleRequest) ProtoMessage()    {}
func (*BatchCheckLaneRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{59}
}

func (m *BatchCheckLaneRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckLaneRuleRequest.Unmarshal(m, b)
}
func (m *BatchCheckLaneRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckLaneRuleRequest.Marshal(b, m, deterministic)
}
func (m *BatchCheckLaneRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckLaneRuleRequest.Merge(m, src)
}
func (m *BatchCheckLaneRuleRequest) XXX_Size() int {
	return xxx_messageInfo_BatchCheckLaneRuleRequest.Size(m)
}
func (m *BatchCheckLaneRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckLaneRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckLaneRuleRequest proto.InternalMessageInfo

func (m *BatchCheckLaneRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckLaneRuleRequest) GetCheckLaneRuleList() []*SingleCheckLaneRuleRequest {
	if m != nil {
		return m.CheckLaneRuleList
	}
	return nil
}

type BatchCheckLaneRuleResponse struct {
	RespHeader             *RespHeader                       `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CheckLaneRuleResultMap map[string]*CheckLaneRuleResponse `protobuf:"bytes,2,rep,name=check_lane_rule_result_map,json=checkLaneRuleResultMap" json:"check_lane_rule_result_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral   struct{}                          `json:"-"`
	XXX_unrecognized       []byte                            `json:"-"`
	XXX_sizecache          int32                             `json:"-"`
}

func (m *BatchCheckLaneRuleResponse) Reset()         { *m = BatchCheckLaneRuleResponse{} }
func (m *BatchCheckLaneRuleResponse) String() string { return proto.CompactTextString(m) }
func (*BatchCheckLaneRuleResponse) ProtoMessage()    {}
func (*BatchCheckLaneRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{60}
}

func (m *BatchCheckLaneRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckLaneRuleResponse.Unmarshal(m, b)
}
func (m *BatchCheckLaneRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckLaneRuleResponse.Marshal(b, m, deterministic)
}
func (m *BatchCheckLaneRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckLaneRuleResponse.Merge(m, src)
}
func (m *BatchCheckLaneRuleResponse) XXX_Size() int {
	return xxx_messageInfo_BatchCheckLaneRuleResponse.Size(m)
}
func (m *BatchCheckLaneRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckLaneRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckLaneRuleResponse proto.InternalMessageInfo

func (m *BatchCheckLaneRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckLaneRuleResponse) GetCheckLaneRuleResultMap() map[string]*CheckLaneRuleResponse {
	if m != nil {
		return m.CheckLaneRuleResultMap
	}
	return nil
}

type BatchCheckProductRuleForParcelLibRequest struct {
	ReqHeader            *ReqHeader    `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ParcelDetail         []*ParcelItem `protobuf:"bytes,2,rep,name=parcel_detail,json=parcelDetail" json:"parcel_detail,omitempty"`
	Region               *string       `protobuf:"bytes,3,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchCheckProductRuleForParcelLibRequest) Reset() {
	*m = BatchCheckProductRuleForParcelLibRequest{}
}
func (m *BatchCheckProductRuleForParcelLibRequest) String() string { return proto.CompactTextString(m) }
func (*BatchCheckProductRuleForParcelLibRequest) ProtoMessage()    {}
func (*BatchCheckProductRuleForParcelLibRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{61}
}

func (m *BatchCheckProductRuleForParcelLibRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductRuleForParcelLibRequest.Unmarshal(m, b)
}
func (m *BatchCheckProductRuleForParcelLibRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductRuleForParcelLibRequest.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductRuleForParcelLibRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductRuleForParcelLibRequest.Merge(m, src)
}
func (m *BatchCheckProductRuleForParcelLibRequest) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductRuleForParcelLibRequest.Size(m)
}
func (m *BatchCheckProductRuleForParcelLibRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductRuleForParcelLibRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductRuleForParcelLibRequest proto.InternalMessageInfo

func (m *BatchCheckProductRuleForParcelLibRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckProductRuleForParcelLibRequest) GetParcelDetail() []*ParcelItem {
	if m != nil {
		return m.ParcelDetail
	}
	return nil
}

func (m *BatchCheckProductRuleForParcelLibRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type BatchCheckProductRuleForParcelLibResponse struct {
	RespHeader           *RespHeader    `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Results              []*CheckResult `protobuf:"bytes,2,rep,name=results" json:"results,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchCheckProductRuleForParcelLibResponse) Reset() {
	*m = BatchCheckProductRuleForParcelLibResponse{}
}
func (m *BatchCheckProductRuleForParcelLibResponse) String() string {
	return proto.CompactTextString(m)
}
func (*BatchCheckProductRuleForParcelLibResponse) ProtoMessage() {}
func (*BatchCheckProductRuleForParcelLibResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{62}
}

func (m *BatchCheckProductRuleForParcelLibResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckProductRuleForParcelLibResponse.Unmarshal(m, b)
}
func (m *BatchCheckProductRuleForParcelLibResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckProductRuleForParcelLibResponse.Marshal(b, m, deterministic)
}
func (m *BatchCheckProductRuleForParcelLibResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckProductRuleForParcelLibResponse.Merge(m, src)
}
func (m *BatchCheckProductRuleForParcelLibResponse) XXX_Size() int {
	return xxx_messageInfo_BatchCheckProductRuleForParcelLibResponse.Size(m)
}
func (m *BatchCheckProductRuleForParcelLibResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckProductRuleForParcelLibResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckProductRuleForParcelLibResponse proto.InternalMessageInfo

func (m *BatchCheckProductRuleForParcelLibResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckProductRuleForParcelLibResponse) GetResults() []*CheckResult {
	if m != nil {
		return m.Results
	}
	return nil
}

type ParcelItem struct {
	Length               *string  `protobuf:"bytes,1,req,name=length" json:"length,omitempty"`
	Width                *string  `protobuf:"bytes,2,req,name=width" json:"width,omitempty"`
	Height               *string  `protobuf:"bytes,3,req,name=height" json:"height,omitempty"`
	Weight               *string  `protobuf:"bytes,4,req,name=weight" json:"weight,omitempty"`
	CombinationId        *uint64  `protobuf:"varint,5,req,name=combination_id,json=combinationId" json:"combination_id,omitempty"`
	Version              *uint32  `protobuf:"varint,6,req,name=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ParcelItem) Reset()         { *m = ParcelItem{} }
func (m *ParcelItem) String() string { return proto.CompactTextString(m) }
func (*ParcelItem) ProtoMessage()    {}
func (*ParcelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{63}
}

func (m *ParcelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ParcelItem.Unmarshal(m, b)
}
func (m *ParcelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ParcelItem.Marshal(b, m, deterministic)
}
func (m *ParcelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ParcelItem.Merge(m, src)
}
func (m *ParcelItem) XXX_Size() int {
	return xxx_messageInfo_ParcelItem.Size(m)
}
func (m *ParcelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ParcelItem.DiscardUnknown(m)
}

var xxx_messageInfo_ParcelItem proto.InternalMessageInfo

func (m *ParcelItem) GetLength() string {
	if m != nil && m.Length != nil {
		return *m.Length
	}
	return ""
}

func (m *ParcelItem) GetWidth() string {
	if m != nil && m.Width != nil {
		return *m.Width
	}
	return ""
}

func (m *ParcelItem) GetHeight() string {
	if m != nil && m.Height != nil {
		return *m.Height
	}
	return ""
}

func (m *ParcelItem) GetWeight() string {
	if m != nil && m.Weight != nil {
		return *m.Weight
	}
	return ""
}

func (m *ParcelItem) GetCombinationId() uint64 {
	if m != nil && m.CombinationId != nil {
		return *m.CombinationId
	}
	return 0
}

func (m *ParcelItem) GetVersion() uint32 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

type CheckResult struct {
	CombinationId        *uint64               `protobuf:"varint,1,req,name=combination_id,json=combinationId" json:"combination_id,omitempty"`
	Version              *uint32               `protobuf:"varint,2,req,name=version" json:"version,omitempty"`
	CheckResults         []*ProductCheckResult `protobuf:"bytes,3,rep,name=check_results,json=checkResults" json:"check_results,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CheckResult) Reset()         { *m = CheckResult{} }
func (m *CheckResult) String() string { return proto.CompactTextString(m) }
func (*CheckResult) ProtoMessage()    {}
func (*CheckResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{64}
}

func (m *CheckResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckResult.Unmarshal(m, b)
}
func (m *CheckResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckResult.Marshal(b, m, deterministic)
}
func (m *CheckResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckResult.Merge(m, src)
}
func (m *CheckResult) XXX_Size() int {
	return xxx_messageInfo_CheckResult.Size(m)
}
func (m *CheckResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckResult.DiscardUnknown(m)
}

var xxx_messageInfo_CheckResult proto.InternalMessageInfo

func (m *CheckResult) GetCombinationId() uint64 {
	if m != nil && m.CombinationId != nil {
		return *m.CombinationId
	}
	return 0
}

func (m *CheckResult) GetVersion() uint32 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

func (m *CheckResult) GetCheckResults() []*ProductCheckResult {
	if m != nil {
		return m.CheckResults
	}
	return nil
}

type ProductCheckResult struct {
	ProductId            *string  `protobuf:"bytes,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	Result               *int32   `protobuf:"varint,2,req,name=result" json:"result,omitempty"`
	Message              *string  `protobuf:"bytes,3,req,name=message" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductCheckResult) Reset()         { *m = ProductCheckResult{} }
func (m *ProductCheckResult) String() string { return proto.CompactTextString(m) }
func (*ProductCheckResult) ProtoMessage()    {}
func (*ProductCheckResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3356ed855260a3, []int{65}
}

func (m *ProductCheckResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductCheckResult.Unmarshal(m, b)
}
func (m *ProductCheckResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductCheckResult.Marshal(b, m, deterministic)
}
func (m *ProductCheckResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductCheckResult.Merge(m, src)
}
func (m *ProductCheckResult) XXX_Size() int {
	return xxx_messageInfo_ProductCheckResult.Size(m)
}
func (m *ProductCheckResult) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductCheckResult.DiscardUnknown(m)
}

var xxx_messageInfo_ProductCheckResult proto.InternalMessageInfo

func (m *ProductCheckResult) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *ProductCheckResult) GetResult() int32 {
	if m != nil && m.Result != nil {
		return *m.Result
	}
	return 0
}

func (m *ProductCheckResult) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func init() {
	proto.RegisterType((*GetTWSizeInfosRequest)(nil), "lcos_protobuf.GetTWSizeInfosRequest")
	proto.RegisterType((*GetTWSizeInfosResponse)(nil), "lcos_protobuf.GetTWSizeInfosResponse")
	proto.RegisterType((*TWSizeInfo)(nil), "lcos_protobuf.TWSizeInfo")
	proto.RegisterType((*CalculateFormulaRequest)(nil), "lcos_protobuf.CalculateFormulaRequest")
	proto.RegisterType((*CalculateFormulaResponse)(nil), "lcos_protobuf.CalculateFormulaResponse")
	proto.RegisterMapType((map[uint32]*CalculateFormulaResult)(nil), "lcos_protobuf.CalculateFormulaResponse.FormulaResultEntry")
	proto.RegisterType((*CalculateFormulaResult)(nil), "lcos_protobuf.CalculateFormulaResult")
	proto.RegisterType((*CalculateWeightRequest)(nil), "lcos_protobuf.CalculateWeightRequest")
	proto.RegisterType((*CalculateWeightResponse)(nil), "lcos_protobuf.CalculateWeightResponse")
	proto.RegisterType((*BatchCalculateWeightRequest)(nil), "lcos_protobuf.BatchCalculateWeightRequest")
	proto.RegisterType((*BatchCalculateWeightResponse)(nil), "lcos_protobuf.BatchCalculateWeightResponse")
	proto.RegisterType((*CheckLineRuleRequest)(nil), "lcos_protobuf.CheckLineRuleRequest")
	proto.RegisterType((*SingleCheckLineRuleRequest)(nil), "lcos_protobuf.SingleCheckLineRuleRequest")
	proto.RegisterType((*BatchCheckLineRuleRequest)(nil), "lcos_protobuf.BatchCheckLineRuleRequest")
	proto.RegisterType((*CheckLineRuleResponse)(nil), "lcos_protobuf.CheckLineRuleResponse")
	proto.RegisterType((*BatchCheckLineRuleResponse)(nil), "lcos_protobuf.BatchCheckLineRuleResponse")
	proto.RegisterMapType((map[string]*CheckLineRuleResponse)(nil), "lcos_protobuf.BatchCheckLineRuleResponse.CheckLineRuleResultMapEntry")
	proto.RegisterType((*CheckProductRuleRequest)(nil), "lcos_protobuf.CheckProductRuleRequest")
	proto.RegisterType((*SingleProductRule)(nil), "lcos_protobuf.SingleProductRule")
	proto.RegisterType((*BatchCheckProductRuleRequest)(nil), "lcos_protobuf.BatchCheckProductRuleRequest")
	proto.RegisterType((*BatchCheckProductRuleForShoppingCartRequest)(nil), "lcos_protobuf.BatchCheckProductRuleForShoppingCartRequest")
	proto.RegisterType((*CheckProductRuleResponse)(nil), "lcos_protobuf.CheckProductRuleResponse")
	proto.RegisterType((*BatchCheckProductRuleResponse)(nil), "lcos_protobuf.BatchCheckProductRuleResponse")
	proto.RegisterMapType((map[string]*CheckProductRuleResponse)(nil), "lcos_protobuf.BatchCheckProductRuleResponse.CheckProductRuleResultMapEntry")
	proto.RegisterType((*VolumetricWeightInfo)(nil), "lcos_protobuf.VolumetricWeightInfo")
	proto.RegisterType((*SingleRuleCheckResult)(nil), "lcos_protobuf.SingleRuleCheckResult")
	proto.RegisterType((*SingleProductCheckResult)(nil), "lcos_protobuf.SingleProductCheckResult")
	proto.RegisterType((*CheckProductRuleForShoppingCartResponse)(nil), "lcos_protobuf.CheckProductRuleForShoppingCartResponse")
	proto.RegisterType((*BatchCheckProductRuleForShoppingCartResponse)(nil), "lcos_protobuf.BatchCheckProductRuleForShoppingCartResponse")
	proto.RegisterMapType((map[string]*CheckProductRuleForShoppingCartResponse)(nil), "lcos_protobuf.BatchCheckProductRuleForShoppingCartResponse.CheckProductRuleResultMapEntry")
	proto.RegisterType((*GetLineRuleRequest)(nil), "lcos_protobuf.GetLineRuleRequest")
	proto.RegisterType((*GetLineRuleResponse)(nil), "lcos_protobuf.GetLineRuleResponse")
	proto.RegisterType((*BatchGetLineRuleRequest)(nil), "lcos_protobuf.BatchGetLineRuleRequest")
	proto.RegisterType((*BatchGetLineRuleResponse)(nil), "lcos_protobuf.BatchGetLineRuleResponse")
	proto.RegisterType((*GetProductRuleRequest)(nil), "lcos_protobuf.GetProductRuleRequest")
	proto.RegisterType((*BatchGetProductRuleRequest)(nil), "lcos_protobuf.BatchGetProductRuleRequest")
	proto.RegisterType((*GetProductRuleResponse)(nil), "lcos_protobuf.GetProductRuleResponse")
	proto.RegisterType((*RealRuleInfoDetailList)(nil), "lcos_protobuf.RealRuleInfoDetailList")
	proto.RegisterType((*BatchGetProductRuleResponse)(nil), "lcos_protobuf.BatchGetProductRuleResponse")
	proto.RegisterMapType((map[string]*RealRuleInfoDetailList)(nil), "lcos_protobuf.BatchGetProductRuleResponse.RuleInfoDetailListEntry")
	proto.RegisterType((*CalculateBaseInfo)(nil), "lcos_protobuf.CalculateBaseInfo")
	proto.RegisterType((*CalculateBaseInfoMulti)(nil), "lcos_protobuf.CalculateBaseInfoMulti")
	proto.RegisterType((*ConditionParams)(nil), "lcos_protobuf.ConditionParams")
	proto.RegisterType((*SingleCheckLineRuleResult)(nil), "lcos_protobuf.SingleCheckLineRuleResult")
	proto.RegisterType((*SingleCheckProductRuleResult)(nil), "lcos_protobuf.SingleCheckProductRuleResult")
	proto.RegisterType((*RuleLimitDetail)(nil), "lcos_protobuf.RuleLimitDetail")
	proto.RegisterType((*LineRuleInfo)(nil), "lcos_protobuf.LineRuleInfo")
	proto.RegisterType((*RuleInfoDetail)(nil), "lcos_protobuf.RuleInfoDetail")
	proto.RegisterType((*RealRuleInfoDetail)(nil), "lcos_protobuf.RealRuleInfoDetail")
	proto.RegisterType((*BatchCalculateInfo)(nil), "lcos_protobuf.BatchCalculateInfo")
	proto.RegisterType((*BatchCalculateSkuGroupInfo)(nil), "lcos_protobuf.BatchCalculateSkuGroupInfo")
	proto.RegisterType((*BatchCalculateResult)(nil), "lcos_protobuf.BatchCalculateResult")
	proto.RegisterType((*BatchCalculateSkuGroupResult)(nil), "lcos_protobuf.BatchCalculateSkuGroupResult")
	proto.RegisterType((*BatchCalculateProductValidateWeightRequest)(nil), "lcos_protobuf.BatchCalculateProductValidateWeightRequest")
	proto.RegisterType((*ProductValidateWeightBaseInfo)(nil), "lcos_protobuf.ProductValidateWeightBaseInfo")
	proto.RegisterType((*SingleProductValidateWeightReq)(nil), "lcos_protobuf.SingleProductValidateWeightReq")
	proto.RegisterType((*BatchCalculateProductValidateWeightResponse)(nil), "lcos_protobuf.BatchCalculateProductValidateWeightResponse")
	proto.RegisterType((*SingleProductValidateWeightResult)(nil), "lcos_protobuf.SingleProductValidateWeightResult")
	proto.RegisterType((*ProductSideLimitsRequest)(nil), "lcos_protobuf.ProductSideLimitsRequest")
	proto.RegisterType((*ProductSideLimitsResponse)(nil), "lcos_protobuf.ProductSideLimitsResponse")
	proto.RegisterMapType((map[string]*SizeLimitInfo)(nil), "lcos_protobuf.ProductSideLimitsResponse.SizeLimitsInfoEntry")
	proto.RegisterType((*SizeLimitInfo)(nil), "lcos_protobuf.SizeLimitInfo")
	proto.RegisterType((*SingleCheckLaneRuleResult)(nil), "lcos_protobuf.SingleCheckLaneRuleResult")
	proto.RegisterType((*CheckLaneRuleResponse)(nil), "lcos_protobuf.CheckLaneRuleResponse")
	proto.RegisterMapType((map[string]*SingleCheckLaneRuleResult)(nil), "lcos_protobuf.CheckLaneRuleResponse.RuleLimitInfoMapEntry")
	proto.RegisterType((*SingleCheckLaneRuleRequest)(nil), "lcos_protobuf.SingleCheckLaneRuleRequest")
	proto.RegisterType((*BatchCheckLaneRuleRequest)(nil), "lcos_protobuf.BatchCheckLaneRuleRequest")
	proto.RegisterType((*BatchCheckLaneRuleResponse)(nil), "lcos_protobuf.BatchCheckLaneRuleResponse")
	proto.RegisterMapType((map[string]*CheckLaneRuleResponse)(nil), "lcos_protobuf.BatchCheckLaneRuleResponse.CheckLaneRuleResultMapEntry")
	proto.RegisterType((*BatchCheckProductRuleForParcelLibRequest)(nil), "lcos_protobuf.BatchCheckProductRuleForParcelLibRequest")
	proto.RegisterType((*BatchCheckProductRuleForParcelLibResponse)(nil), "lcos_protobuf.BatchCheckProductRuleForParcelLibResponse")
	proto.RegisterType((*ParcelItem)(nil), "lcos_protobuf.ParcelItem")
	proto.RegisterType((*CheckResult)(nil), "lcos_protobuf.CheckResult")
	proto.RegisterType((*ProductCheckResult)(nil), "lcos_protobuf.ProductCheckResult")
}

func init() {
	proto.RegisterFile("lcos_weight_limit.proto", fileDescriptor_5c3356ed855260a3)
}

var fileDescriptor_5c3356ed855260a3 = []byte{
	// 3491 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x3b, 0x4d, 0x8c, 0x1c, 0x47,
	0xd5, 0xdb, 0xbd, 0xb3, 0x3f, 0xf3, 0x66, 0x66, 0x7f, 0x6a, 0x7f, 0x3c, 0x3b, 0x8e, 0xfd, 0xad,
	0xdb, 0x7f, 0xeb, 0xd8, 0xdf, 0x2a, 0xb1, 0xbe, 0x8f, 0x18, 0x87, 0x44, 0xb2, 0x9d, 0xd8, 0x59,
	0xc5, 0x0e, 0x9b, 0x5e, 0x2b, 0x51, 0x22, 0xa4, 0x51, 0xed, 0x4c, 0xed, 0x6c, 0xb3, 0x3d, 0xd3,
	0xb3, 0xfd, 0x63, 0x76, 0x1d, 0x71, 0x00, 0x81, 0x04, 0x42, 0x20, 0x4e, 0x08, 0x10, 0x89, 0x12,
	0x10, 0x17, 0x22, 0x10, 0x42, 0x20, 0x10, 0xa0, 0xc0, 0x85, 0x13, 0xe4, 0xc0, 0x05, 0x2e, 0x9c,
	0x38, 0x71, 0xe5, 0xce, 0x01, 0xd5, 0xab, 0xea, 0x99, 0xfe, 0xa9, 0xe9, 0x99, 0xf5, 0xb6, 0xf9,
	0xbd, 0x75, 0xd5, 0xab, 0xaa, 0xf7, 0xea, 0xd5, 0xab, 0xf7, 0x57, 0xaf, 0xe1, 0x84, 0xdd, 0x70,
	0xbc, 0xfa, 0xa7, 0x98, 0xd5, 0xda, 0xf5, 0xeb, 0xb6, 0xd5, 0xb6, 0xfc, 0xf5, 0xae, 0xeb, 0xf8,
	0x0e, 0xa9, 0x20, 0x00, 0xbf, 0xb7, 0x83, 0x9d, 0xda, 0x2c, 0x36, 0xb7, 0xa9, 0xc7, 0x04, 0xdc,
	0xf8, 0x82, 0x06, 0x4b, 0x77, 0x98, 0x7f, 0xff, 0xf5, 0x2d, 0xeb, 0x21, 0xdb, 0xe8, 0xec, 0x38,
	0x9e, 0xc9, 0xf6, 0x03, 0xe6, 0xf9, 0xe4, 0x19, 0x00, 0x97, 0xed, 0xd7, 0x77, 0x19, 0x6d, 0x32,
	0xb7, 0xaa, 0xad, 0xea, 0x6b, 0xa5, 0xab, 0xd5, 0xf5, 0xd8, 0x72, 0xeb, 0x26, 0xdb, 0x7f, 0x09,
	0xe1, 0x66, 0xd1, 0x0d, 0x3f, 0xc9, 0x29, 0x80, 0xae, 0xeb, 0x34, 0x83, 0x86, 0x5f, 0xb7, 0x9a,
	0x55, 0x7d, 0x55, 0x5f, 0x2b, 0x9a, 0x45, 0xd9, 0xb3, 0xd1, 0x24, 0x27, 0x60, 0xca, 0xb3, 0x1e,
	0x32, 0x0e, 0x1b, 0x5f, 0xd5, 0xd6, 0x8a, 0xe6, 0x24, 0x6f, 0x6e, 0x34, 0x8d, 0xaf, 0x68, 0xb0,
	0x9c, 0x24, 0xc5, 0xeb, 0x3a, 0x1d, 0x8f, 0x91, 0xeb, 0x50, 0x72, 0x99, 0xd7, 0x8d, 0x13, 0xb3,
	0x92, 0x22, 0xc6, 0xeb, 0x4a, 0x6a, 0xc0, 0xed, 0x7d, 0x93, 0x6b, 0x00, 0x02, 0x1f, 0x5f, 0xb1,
	0xaa, 0xaf, 0x8e, 0x2b, 0xa6, 0xf6, 0x71, 0x9a, 0x45, 0x2f, 0xc4, 0x6e, 0xfc, 0x48, 0x07, 0xe8,
	0x43, 0xc8, 0x0c, 0xe8, 0x56, 0x13, 0x71, 0x17, 0x4c, 0xdd, 0x6a, 0x1e, 0x69, 0x9f, 0x7a, 0x7f,
	0x9f, 0x84, 0x40, 0xa1, 0x43, 0xdb, 0xac, 0x5a, 0xc0, 0x5e, 0xfc, 0x26, 0xab, 0x50, 0x6a, 0x32,
	0xaf, 0xe1, 0x5a, 0x5d, 0xdf, 0x72, 0x3a, 0xd5, 0x09, 0x64, 0x4c, 0xb4, 0x8b, 0x54, 0x61, 0xaa,
	0xe1, 0x04, 0x1d, 0xdf, 0x3d, 0xac, 0x4e, 0x22, 0x34, 0x6c, 0xf2, 0xf5, 0x82, 0x8e, 0xe5, 0x57,
	0xa7, 0xb0, 0x1b, 0xbf, 0xc9, 0x0a, 0x4c, 0xb7, 0xe9, 0x41, 0x9d, 0x63, 0xac, 0x4e, 0xaf, 0x6a,
	0x6b, 0x9a, 0x39, 0xd5, 0xa6, 0x07, 0x7c, 0x2b, 0x08, 0xb2, 0x3a, 0x02, 0x54, 0x94, 0x20, 0xab,
	0x83, 0xa0, 0xb3, 0x50, 0x69, 0xb2, 0x1d, 0x1a, 0xd8, 0x7e, 0xbd, 0xeb, 0x5a, 0x0d, 0x56, 0x05,
	0x84, 0x97, 0x65, 0xe7, 0x26, 0xef, 0xe3, 0xdb, 0x66, 0x07, 0xbe, 0x4b, 0xeb, 0x4d, 0xea, 0xd3,
	0x6a, 0x09, 0x91, 0x16, 0xb1, 0xe7, 0x05, 0xea, 0x53, 0xe3, 0xb3, 0x3a, 0x9c, 0xb8, 0x45, 0xed,
	0x46, 0x60, 0x53, 0x9f, 0xdd, 0x76, 0xdc, 0x76, 0x60, 0xd3, 0x63, 0x8b, 0xd4, 0x4d, 0x28, 0x72,
	0x99, 0xc5, 0x33, 0x44, 0x4e, 0x97, 0xae, 0x9e, 0x4f, 0xcc, 0xeb, 0xe1, 0xbc, 0x49, 0x3d, 0x3c,
	0xaf, 0x7b, 0x81, 0xed, 0x5b, 0xe6, 0xf4, 0xb6, 0x6c, 0x72, 0xe4, 0x8e, 0xdb, 0x64, 0xae, 0x58,
	0x84, 0x8b, 0x5e, 0x1a, 0xf9, 0xc7, 0xf9, 0x00, 0x21, 0x06, 0x4e, 0xf8, 0x49, 0x9e, 0x86, 0x69,
	0x6f, 0x2f, 0x10, 0xd3, 0x0a, 0x28, 0x3e, 0xcb, 0x89, 0x69, 0x5b, 0x7b, 0x01, 0x4e, 0x9a, 0xf2,
	0xc4, 0x87, 0xf1, 0x6d, 0x1d, 0xaa, 0x69, 0x26, 0xe4, 0x20, 0xcc, 0x14, 0x66, 0x76, 0xc4, 0x72,
	0x75, 0x97, 0x79, 0x81, 0xed, 0x4b, 0x81, 0xbe, 0x3e, 0x88, 0x1b, 0x09, 0xe4, 0xeb, 0xfd, 0x76,
	0x60, 0xfb, 0x2f, 0x72, 0xf9, 0x31, 0x2b, 0x3b, 0xd1, 0xbe, 0x5a, 0x0b, 0x48, 0x7a, 0x10, 0x99,
	0x83, 0xf1, 0x3d, 0x76, 0x58, 0xd5, 0x56, 0xb5, 0xb5, 0x8a, 0xc9, 0x3f, 0xc9, 0xb3, 0x30, 0xf1,
	0x80, 0xda, 0x01, 0xab, 0xea, 0xc8, 0xca, 0xf3, 0xc3, 0x29, 0x08, 0x6c, 0xdf, 0x14, 0x73, 0xae,
	0xeb, 0xd7, 0x34, 0xc3, 0x86, 0x65, 0xf5, 0xa0, 0x63, 0x71, 0x68, 0x19, 0x26, 0x7b, 0x9c, 0xe1,
	0xc2, 0x2b, 0x5b, 0xc6, 0xdf, 0xb4, 0x08, 0xba, 0xd7, 0x51, 0x51, 0x1e, 0x5b, 0x2c, 0x9f, 0x4b,
	0x8b, 0xe5, 0xea, 0x30, 0xb1, 0xcc, 0x90, 0x48, 0xfd, 0x31, 0x4a, 0xe4, 0xc3, 0xc8, 0xad, 0x0c,
	0x77, 0x9f, 0x83, 0x3c, 0x9e, 0x85, 0x8a, 0xe0, 0xaf, 0xb4, 0x3d, 0x92, 0xe9, 0x65, 0xd1, 0x29,
	0x10, 0x19, 0xef, 0x6a, 0x70, 0xf2, 0x26, 0xf5, 0x1b, 0xbb, 0x79, 0xf3, 0xff, 0x25, 0x98, 0x69,
	0x84, 0x4b, 0x86, 0x87, 0xc0, 0xb9, 0x71, 0x26, 0x31, 0x39, 0x8e, 0x1c, 0x19, 0x53, 0x69, 0x44,
	0x9b, 0xc6, 0xf7, 0x34, 0x78, 0x42, 0x4d, 0x62, 0x0e, 0x4c, 0x7a, 0x05, 0xe6, 0xfa, 0x64, 0xc6,
	0xae, 0xed, 0xd9, 0x4c, 0x42, 0xe5, 0x95, 0x99, 0x6d, 0xc4, 0x3b, 0x8c, 0x0f, 0x75, 0x58, 0xbc,
	0xb5, 0xcb, 0x1a, 0x7b, 0x77, 0xad, 0x0e, 0x33, 0x03, 0x9b, 0x1d, 0x9b, 0x91, 0x51, 0x81, 0xd2,
	0x47, 0x12, 0x28, 0x6e, 0xde, 0x6c, 0xab, 0x23, 0xcd, 0xdb, 0x38, 0x37, 0x6f, 0xbc, 0xb9, 0xd1,
	0x4c, 0x48, 0x75, 0x61, 0x74, 0x3d, 0x7b, 0x07, 0xe6, 0xbc, 0x60, 0xbb, 0xde, 0xa5, 0x8d, 0x3d,
	0xda, 0x92, 0xe7, 0x39, 0x81, 0xc4, 0x9c, 0x4a, 0x12, 0x13, 0x6c, 0x6f, 0x8a, 0x51, 0xb8, 0xc6,
	0x8c, 0x17, 0x6b, 0x93, 0x75, 0x58, 0xd8, 0x0e, 0x0e, 0x99, 0x5b, 0xef, 0x06, 0x6e, 0x63, 0x97,
	0x5f, 0x50, 0xdf, 0x6a, 0x33, 0x34, 0x9b, 0x15, 0x73, 0x1e, 0x41, 0x9b, 0x12, 0x72, 0xdf, 0x6a,
	0x33, 0xe3, 0xa7, 0x3a, 0xd4, 0xb6, 0xac, 0x4e, 0xcb, 0x66, 0x4a, 0xae, 0x9e, 0x84, 0x62, 0xd0,
	0xb1, 0xf6, 0x03, 0xdc, 0xab, 0x86, 0x46, 0x7b, 0x5a, 0x74, 0x6c, 0x34, 0xff, 0xcb, 0x39, 0xf7,
	0x43, 0x0d, 0x56, 0x84, 0xcc, 0xe6, 0x2a, 0x8e, 0x6f, 0xc2, 0x62, 0x83, 0x2f, 0x58, 0x47, 0x3e,
	0xb9, 0x81, 0xcd, 0xea, 0xb6, 0xe5, 0x85, 0x97, 0xe6, 0x52, 0x72, 0x4f, 0x03, 0x8f, 0xce, 0x9c,
	0x6f, 0x44, 0x7b, 0xef, 0x5a, 0x9e, 0x6f, 0x7c, 0x57, 0x83, 0xa5, 0xc4, 0xd8, 0x1c, 0xae, 0xf8,
	0x26, 0xcc, 0x4a, 0x32, 0xdb, 0x96, 0x1f, 0x95, 0x86, 0xb5, 0x51, 0x88, 0xc5, 0x6b, 0x5e, 0x71,
	0x91, 0xc4, 0xb6, 0xe5, 0xa3, 0x46, 0xfa, 0x50, 0x87, 0x9a, 0x8a, 0xb5, 0x39, 0x10, 0xfb, 0x19,
	0x0d, 0x6a, 0x49, 0xfe, 0x4a, 0x2d, 0xde, 0xa6, 0x5d, 0x49, 0xf8, 0x8b, 0x4a, 0xd5, 0xa4, 0xa2,
	0x65, 0x5d, 0xb1, 0x9b, 0x7b, 0xb4, 0x2b, 0x9c, 0x8b, 0xe5, 0x86, 0x12, 0x58, 0x73, 0xe0, 0x64,
	0xc6, 0xb4, 0xa8, 0xbb, 0x51, 0x14, 0xee, 0xc6, 0xf5, 0xb8, 0xbb, 0x71, 0x2e, 0x69, 0x67, 0x55,
	0x94, 0x45, 0xbd, 0x8d, 0x3f, 0x70, 0xbf, 0x94, 0x0f, 0xda, 0x14, 0x1e, 0xfa, 0x3f, 0x4b, 0x6f,
	0xc6, 0xa3, 0x06, 0xa1, 0x00, 0x22, 0x51, 0xc3, 0xbf, 0x9f, 0x0e, 0xf8, 0xb1, 0x0e, 0xf3, 0x42,
	0xaa, 0x23, 0x9c, 0xcd, 0x5d, 0x69, 0xfe, 0xc7, 0xb1, 0xed, 0x9d, 0x9e, 0xc7, 0x91, 0xb7, 0x50,
	0xbe, 0x08, 0x95, 0x90, 0x55, 0xfc, 0x6a, 0x87, 0x31, 0xef, 0xaa, 0x52, 0x13, 0x45, 0x11, 0x97,
	0xbb, 0xfd, 0x86, 0x67, 0x7c, 0x5f, 0x83, 0xcb, 0x4a, 0x02, 0x6f, 0x3b, 0xee, 0xd6, 0xae, 0xd3,
	0xed, 0x5a, 0x9d, 0xd6, 0x2d, 0xea, 0xfa, 0xff, 0x2a, 0xf4, 0xbe, 0xaf, 0x41, 0x35, 0xcd, 0xcb,
	0x1c, 0xd4, 0xe5, 0xd6, 0x20, 0xdd, 0x7e, 0x79, 0xb0, 0x6e, 0x8f, 0xd3, 0xa0, 0x50, 0xef, 0x7f,
	0xd6, 0xe1, 0xd4, 0x80, 0xe3, 0xcf, 0x81, 0xe4, 0x2f, 0x6b, 0x70, 0x4a, 0x68, 0xf8, 0x28, 0x67,
	0xd3, 0x4a, 0xfe, 0xe5, 0x81, 0x4a, 0x5e, 0x41, 0xd1, 0xba, 0x7a, 0x67, 0x3d, 0x55, 0xbf, 0xd2,
	0x18, 0x04, 0xaf, 0x05, 0x70, 0x3a, 0x7b, 0xb2, 0x42, 0xe1, 0x3f, 0x17, 0x57, 0xf8, 0x17, 0x55,
	0x0a, 0x5f, 0x41, 0x65, 0x54, 0xe7, 0xff, 0x4e, 0x83, 0xc5, 0xd7, 0x1c, 0x3b, 0x68, 0x33, 0xdf,
	0xb5, 0x1a, 0xc2, 0xa3, 0x0f, 0x5d, 0x30, 0xcb, 0x67, 0x6d, 0xa1, 0x9b, 0xb4, 0xb5, 0x82, 0x39,
	0xc9, 0x9b, 0x1b, 0x4d, 0xf2, 0x3f, 0x50, 0x6a, 0x50, 0x9f, 0xb5, 0x1c, 0xf7, 0x50, 0x24, 0x75,
	0x38, 0x10, 0xc2, 0xae, 0x8d, 0x26, 0xb9, 0x0c, 0xf3, 0x0f, 0x7a, 0x2b, 0x86, 0x41, 0xcf, 0x38,
	0x06, 0x3d, 0x73, 0x0f, 0x12, 0xa8, 0x30, 0xd5, 0xe2, 0x34, 0x99, 0xcd, 0x97, 0x2a, 0xe0, 0x52,
	0x53, 0xd8, 0xde, 0x68, 0x92, 0xab, 0xb0, 0x94, 0x5a, 0xa7, 0x6e, 0x75, 0x7c, 0x4c, 0xfd, 0x54,
	0xcc, 0x85, 0x07, 0x29, 0xb2, 0x7d, 0xe3, 0xf3, 0x13, 0xb0, 0x24, 0x64, 0x8c, 0x6f, 0x18, 0x19,
	0x20, 0x03, 0xe6, 0x93, 0x50, 0xc4, 0x03, 0xf6, 0x0f, 0xbb, 0x0c, 0x25, 0xa5, 0x62, 0x4e, 0xf3,
	0x8e, 0xfb, 0x87, 0x5d, 0x46, 0xce, 0x40, 0x59, 0xc8, 0x42, 0x2f, 0xf4, 0xe0, 0xf0, 0x52, 0x23,
	0x32, 0xff, 0x2c, 0x54, 0x98, 0xeb, 0x3a, 0x6e, 0xbd, 0xcd, 0x3c, 0x8f, 0xb6, 0x98, 0xcc, 0x58,
	0x95, 0xb1, 0xf3, 0x9e, 0xe8, 0x23, 0x97, 0x14, 0x61, 0x4c, 0x61, 0x55, 0x5f, 0xd3, 0x52, 0x11,
	0x0a, 0x57, 0x86, 0xe2, 0xb6, 0xf8, 0xbb, 0x2e, 0xf3, 0x76, 0x1d, 0xbb, 0x59, 0x6f, 0x5b, 0x9d,
	0xea, 0x04, 0x8e, 0x9e, 0x47, 0xd0, 0xfd, 0x10, 0x72, 0xcf, 0xea, 0x28, 0xc7, 0xd3, 0x83, 0xea,
	0xa4, 0x72, 0x3c, 0x3d, 0x20, 0xd7, 0x61, 0xc5, 0xea, 0x34, 0xec, 0xa0, 0xc9, 0xea, 0xe9, 0xd3,
	0x98, 0x5a, 0xd5, 0xd7, 0xa6, 0xcd, 0x13, 0x72, 0x40, 0xf2, 0xfc, 0xc9, 0x1b, 0xb0, 0xac, 0xe2,
	0xfc, 0x8e, 0x53, 0x9d, 0x56, 0xc6, 0x64, 0x2a, 0x01, 0x32, 0x17, 0x1f, 0xa8, 0xc4, 0xea, 0x29,
	0x58, 0x4c, 0x72, 0x08, 0xcf, 0xb4, 0x88, 0x1c, 0x27, 0x09, 0x2e, 0x6d, 0x74, 0x7c, 0xf2, 0xff,
	0x70, 0x42, 0xc1, 0x28, 0x9c, 0x04, 0x38, 0x69, 0x31, 0xc5, 0xac, 0x41, 0xd3, 0xe8, 0x01, 0x4e,
	0x2b, 0x29, 0xa7, 0xd1, 0x03, 0x3e, 0xad, 0x0a, 0x53, 0x32, 0xd7, 0x53, 0x2d, 0xa3, 0x98, 0x85,
	0xcd, 0x84, 0x58, 0xef, 0xd0, 0x86, 0xef, 0xb8, 0xd5, 0x0a, 0x8e, 0x89, 0x88, 0xf5, 0x6d, 0xec,
	0x37, 0x7e, 0xa0, 0x41, 0x35, 0xa6, 0x8d, 0xa3, 0xa2, 0x18, 0x37, 0xd4, 0x5a, 0x32, 0x2b, 0x3a,
	0x82, 0x30, 0x6e, 0xc2, 0x3c, 0x0a, 0xb3, 0x18, 0xd7, 0x64, 0x3e, 0xb5, 0x6c, 0xb4, 0xf8, 0x69,
	0xaf, 0x4f, 0x79, 0x1b, 0x4c, 0x54, 0xd7, 0xd8, 0xf1, 0x02, 0x4e, 0x36, 0x7e, 0xad, 0xc1, 0xc5,
	0xa1, 0x56, 0x2c, 0x07, 0xb5, 0xfb, 0x06, 0x2c, 0x86, 0x7b, 0x4f, 0x6c, 0x72, 0x5c, 0xa1, 0xc1,
	0x06, 0xb1, 0xd0, 0x24, 0xdd, 0x54, 0x9f, 0xf1, 0xd5, 0x71, 0xb8, 0x32, 0x9a, 0x35, 0xce, 0x61,
	0x1f, 0xdf, 0x1a, 0xd1, 0x7c, 0xbc, 0x39, 0x8a, 0xf9, 0x18, 0x40, 0xe0, 0x31, 0xac, 0xc9, 0xe7,
	0xb4, 0x47, 0x30, 0x27, 0x77, 0xe3, 0xe6, 0xe4, 0x23, 0x43, 0xcc, 0xc9, 0x00, 0xaa, 0xa3, 0xd6,
	0x65, 0x07, 0xc8, 0x1d, 0xe6, 0xe7, 0x16, 0xf4, 0x46, 0xd2, 0x02, 0xe2, 0x2d, 0x41, 0xa6, 0x05,
	0x8c, 0x6f, 0x6a, 0xb0, 0x10, 0x43, 0x94, 0xc3, 0x09, 0xdf, 0x81, 0x39, 0x3c, 0x52, 0xae, 0xf7,
	0xc2, 0x2b, 0xa6, 0x2b, 0xdd, 0x5e, 0x8e, 0x92, 0x2b, 0x37, 0x71, 0x95, 0xcc, 0x19, 0x37, 0xd6,
	0x36, 0xf6, 0xe0, 0x04, 0x9e, 0xfa, 0x63, 0xe3, 0x44, 0x24, 0x41, 0x62, 0x7c, 0x43, 0x83, 0x6a,
	0x1a, 0x5b, 0x0e, 0xec, 0xb8, 0x01, 0x33, 0xfd, 0x50, 0x38, 0xe2, 0xe1, 0x9d, 0x4c, 0x4c, 0x0f,
	0x91, 0xa2, 0x0d, 0x28, 0xdb, 0x91, 0x96, 0xe1, 0xe0, 0x3b, 0x5a, 0x9e, 0x7e, 0x7c, 0xf6, 0xfb,
	0x92, 0xf1, 0x69, 0x99, 0x1f, 0xc8, 0x19, 0xeb, 0x05, 0x98, 0xed, 0x63, 0xed, 0xa7, 0x5d, 0x8a,
	0x66, 0xa5, 0x87, 0x1a, 0xf3, 0x28, 0x6f, 0x8b, 0xd7, 0xba, 0xbc, 0x3d, 0xd7, 0xdc, 0x04, 0x93,
	0xc1, 0xb2, 0xc9, 0xa8, 0x1d, 0x1f, 0xc5, 0x29, 0x27, 0x2f, 0x2b, 0x50, 0x68, 0xca, 0xbc, 0x71,
	0x7a, 0x81, 0x14, 0x9a, 0x0f, 0x74, 0x99, 0xdb, 0x7e, 0x0c, 0xbc, 0x08, 0x60, 0x29, 0x49, 0x68,
	0x34, 0x0f, 0x76, 0x53, 0xa5, 0x7d, 0xd5, 0x64, 0xac, 0xa7, 0xd9, 0x20, 0xb4, 0x2c, 0x71, 0x53,
	0x80, 0x9a, 0x0d, 0x27, 0x06, 0x0c, 0x57, 0xa8, 0xd5, 0x21, 0xaf, 0x40, 0xea, 0x23, 0x88, 0x6a,
	0xd1, 0x9f, 0xe8, 0x30, 0x9f, 0x7a, 0x24, 0x89, 0x7a, 0x2a, 0xc2, 0x9d, 0xcd, 0xf6, 0x54, 0x74,
	0xb5, 0xa7, 0xc2, 0xfd, 0x62, 0xcf, 0x71, 0xfd, 0xfa, 0x8e, 0x4d, 0x5b, 0xe8, 0xa5, 0x57, 0xcc,
	0x69, 0xde, 0x71, 0xdb, 0xa6, 0x2d, 0xb2, 0x01, 0x73, 0x0d, 0xa7, 0xd3, 0xb4, 0x7c, 0xcb, 0xe9,
	0xd4, 0xbb, 0xd4, 0xa5, 0x6d, 0x4f, 0xbe, 0xa6, 0x9c, 0x4e, 0x1a, 0x87, 0x70, 0xd8, 0x26, 0x8e,
	0x32, 0x67, 0x1b, 0xf1, 0x0e, 0x72, 0x03, 0x2a, 0x5d, 0xea, 0x36, 0x98, 0x2d, 0x82, 0x44, 0x0f,
	0xbd, 0xf8, 0xd2, 0xd5, 0x27, 0x52, 0x16, 0xff, 0x61, 0x3f, 0x04, 0x34, 0xcb, 0x62, 0x0a, 0x76,
	0x78, 0x47, 0xce, 0x1f, 0xfc, 0x2a, 0xfa, 0x9e, 0x15, 0x7b, 0xf3, 0x8c, 0x33, 0x6f, 0xfc, 0xf1,
	0x30, 0x2f, 0xb5, 0xe3, 0xc2, 0x51, 0x77, 0x6c, 0xdc, 0x83, 0xd9, 0x04, 0x63, 0x39, 0xca, 0x3d,
	0x76, 0x28, 0x0e, 0x03, 0xa5, 0x4c, 0x33, 0xa7, 0xf7, 0xd8, 0x21, 0x42, 0xb9, 0x3e, 0xe4, 0x40,
	0xef, 0xb0, 0xbd, 0xed, 0xd8, 0x92, 0x6a, 0x3e, 0x7c, 0x0b, 0x3b, 0xb8, 0x99, 0x5c, 0x19, 0x98,
	0x5d, 0x8d, 0xda, 0x14, 0x2d, 0x6a, 0x5d, 0x47, 0x71, 0x48, 0x6f, 0x40, 0x59, 0x78, 0xdb, 0x31,
	0x5f, 0xf4, 0xb4, 0x42, 0x1f, 0xe1, 0xce, 0xa4, 0xa6, 0x28, 0xd9, 0xfd, 0x86, 0xf1, 0x1d, 0x0d,
	0x9e, 0xc8, 0x4a, 0x0f, 0xe4, 0xe0, 0x36, 0xe7, 0x40, 0xe5, 0x7b, 0x1a, 0xcc, 0x26, 0x06, 0x64,
	0x87, 0x96, 0xeb, 0xb0, 0xe0, 0xe1, 0xae, 0xea, 0x0a, 0xea, 0xe6, 0xbd, 0xfe, 0x86, 0x25, 0x8d,
	0xf8, 0x38, 0x4b, 0x3d, 0xa7, 0x13, 0x96, 0x44, 0x88, 0xd6, 0x80, 0xd0, 0x52, 0x53, 0x84, 0x96,
	0x46, 0x17, 0xca, 0x51, 0x2b, 0x3c, 0xf8, 0x60, 0x73, 0xb3, 0x24, 0x7f, 0x19, 0x87, 0x99, 0xf8,
	0x90, 0x6c, 0xa6, 0x9c, 0x02, 0x10, 0x07, 0x81, 0x17, 0x47, 0xf0, 0xa2, 0x88, 0x3d, 0x78, 0x73,
	0x4e, 0x01, 0xf0, 0x58, 0x2d, 0x96, 0x3a, 0x28, 0xb6, 0xe9, 0x81, 0x0c, 0x4f, 0x39, 0xd8, 0xea,
	0x84, 0xe0, 0x82, 0x04, 0x5b, 0x9d, 0x48, 0x4a, 0x21, 0x2c, 0xec, 0x98, 0x18, 0x5c, 0xd8, 0x31,
	0x19, 0x2f, 0xec, 0x90, 0x38, 0x6d, 0xd6, 0x69, 0xf9, 0xbb, 0x58, 0x28, 0x22, 0x70, 0xde, 0xc5,
	0x8e, 0x10, 0xa7, 0x04, 0x4f, 0xf7, 0x70, 0x4a, 0xf0, 0x49, 0x28, 0x22, 0xc5, 0x56, 0xd3, 0xdf,
	0x95, 0x25, 0x23, 0x9c, 0x88, 0xd7, 0x79, 0x1b, 0x81, 0x9c, 0x5e, 0x04, 0x82, 0x04, 0x5a, 0x1d,
	0x01, 0x94, 0x78, 0x77, 0xc5, 0x66, 0x4a, 0x3d, 0xbc, 0x2f, 0xc5, 0xf6, 0x2a, 0xc1, 0xe5, 0x1e,
	0x5e, 0x09, 0x3e, 0x07, 0x33, 0x81, 0xc7, 0xea, 0x3d, 0x3d, 0xb3, 0x8d, 0x11, 0xe9, 0xb4, 0x59,
	0x0e, 0x3c, 0xb6, 0x29, 0x35, 0xc9, 0x36, 0x17, 0x80, 0x6d, 0xae, 0x32, 0x3c, 0xaf, 0x3a, 0x83,
	0xe0, 0xc9, 0xed, 0xc3, 0x4d, 0xea, 0x79, 0xe4, 0x69, 0x58, 0x92, 0x80, 0x84, 0x4e, 0x9d, 0x45,
	0xd5, 0x41, 0xc4, 0xb0, 0x98, 0x52, 0xfd, 0xed, 0x04, 0x90, 0xb4, 0xc9, 0x3a, 0x6a, 0xe5, 0x0f,
	0x4a, 0x79, 0xcb, 0x8a, 0x4a, 0x39, 0x6f, 0xc5, 0xa5, 0xa6, 0x90, 0x29, 0x35, 0x13, 0xd9, 0x52,
	0x33, 0x99, 0x2d, 0x35, 0x53, 0x59, 0x52, 0x33, 0x7a, 0x39, 0x50, 0x5c, 0x6a, 0x20, 0x5b, 0x6a,
	0x4a, 0x99, 0x52, 0x53, 0xce, 0x92, 0x9a, 0x4a, 0xa6, 0xd4, 0xcc, 0x64, 0x4b, 0xcd, 0x6c, 0x52,
	0x6a, 0xe4, 0x6c, 0x69, 0x26, 0xe6, 0x84, 0x99, 0xe0, 0xbb, 0xc5, 0x8e, 0x70, 0xb6, 0x04, 0xcf,
	0x4b, 0xb0, 0xd5, 0x91, 0x60, 0xa5, 0x85, 0x24, 0xa3, 0x58, 0xc8, 0x85, 0x84, 0x85, 0x8c, 0x58,
	0xe1, 0xc5, 0x78, 0xb2, 0xe5, 0x1a, 0x54, 0xfb, 0x8e, 0x47, 0x58, 0xce, 0x23, 0x1d, 0x90, 0x25,
	0x74, 0xad, 0x96, 0x7b, 0x70, 0x59, 0x18, 0x23, 0xed, 0xe3, 0x15, 0x20, 0xf1, 0x1b, 0xe1, 0x52,
	0xf7, 0xb0, 0xba, 0x8c, 0x62, 0x3f, 0x17, 0xbd, 0x15, 0xbc, 0xdf, 0xf8, 0x40, 0x03, 0x92, 0x2e,
	0x7d, 0x40, 0x5b, 0xd2, 0xaf, 0x9a, 0x08, 0xd5, 0x66, 0xa9, 0x5f, 0x10, 0xd1, 0x3c, 0x6e, 0x61,
	0xcb, 0x6d, 0x28, 0x7a, 0x7b, 0x41, 0xbd, 0xe5, 0x3a, 0x41, 0x57, 0xda, 0xa1, 0x4b, 0x99, 0x95,
	0x0e, 0x5b, 0x7b, 0xc1, 0x1d, 0x3e, 0x58, 0xac, 0xe3, 0xc9, 0x96, 0xf1, 0xc9, 0xf0, 0x09, 0x54,
	0x35, 0x8e, 0x4b, 0x2e, 0x62, 0xe8, 0xef, 0x61, 0x0a, 0xdb, 0x8f, 0xf4, 0xc0, 0x64, 0x7c, 0x51,
	0x83, 0x45, 0x55, 0xf9, 0xc5, 0x28, 0xec, 0x7a, 0x05, 0xca, 0x82, 0x92, 0x58, 0xbe, 0xe7, 0xf2,
	0x48, 0x5b, 0x96, 0x39, 0x9f, 0x52, 0xab, 0xdf, 0x30, 0xbe, 0x94, 0xaa, 0x46, 0x89, 0x8f, 0xce,
	0xda, 0xba, 0xa2, 0x22, 0x47, 0x4f, 0x56, 0xe4, 0x70, 0x29, 0xef, 0xef, 0x29, 0x9e, 0xf3, 0xed,
	0x1b, 0x62, 0x99, 0xf7, 0x35, 0x7e, 0xa6, 0xc1, 0x93, 0x71, 0x6a, 0xa4, 0xfb, 0xf2, 0x1a, 0xb5,
	0xad, 0x66, 0x7e, 0xd5, 0x3c, 0xaf, 0x72, 0xca, 0xf7, 0x85, 0xbd, 0x8e, 0x84, 0x39, 0xff, 0x9b,
	0x95, 0x36, 0x4b, 0x91, 0x60, 0x96, 0x5c, 0xb6, 0xcf, 0x0f, 0x14, 0x83, 0xd4, 0x77, 0x74, 0x38,
	0xa5, 0x1c, 0xd9, 0x0b, 0x34, 0x56, 0x60, 0x7a, 0x3f, 0x60, 0x22, 0xdb, 0x2f, 0x23, 0x0d, 0x6c,
	0x6f, 0x0c, 0xd5, 0xf2, 0xaa, 0xf0, 0x61, 0xfc, 0xd1, 0xc2, 0x87, 0x98, 0x1e, 0x29, 0x24, 0xf4,
	0x88, 0x52, 0x23, 0x4d, 0x0c, 0xd0, 0x48, 0x47, 0x8d, 0x22, 0xfe, 0xa8, 0xc1, 0xe9, 0x6c, 0x86,
	0x92, 0x8d, 0xa8, 0x2e, 0x10, 0xc7, 0x79, 0x25, 0xb1, 0xc1, 0x4c, 0x16, 0x0f, 0x2c, 0x78, 0xd3,
	0x1f, 0xad, 0xe0, 0x6d, 0x7c, 0xb4, 0xfb, 0xfc, 0xcb, 0xde, 0xf3, 0xe5, 0x10, 0xa9, 0xcd, 0x21,
	0x50, 0x7f, 0x15, 0xe7, 0xf2, 0x3b, 0x17, 0x91, 0xdb, 0xa7, 0x8e, 0x22, 0xb7, 0xa8, 0x03, 0x40,
	0x2c, 0x82, 0x92, 0xfb, 0x73, 0x0d, 0xce, 0x0c, 0x9d, 0xc1, 0x05, 0x07, 0xdf, 0xb1, 0x1a, 0x4e,
	0x53, 0xf8, 0xa1, 0x13, 0xe6, 0x34, 0xef, 0xb8, 0xe5, 0x34, 0xd1, 0xb2, 0x23, 0xb0, 0xed, 0xb5,
	0xa4, 0xf4, 0xe2, 0xa3, 0xd7, 0x3d, 0xaf, 0x15, 0x93, 0xfa, 0xf1, 0x2c, 0xa9, 0x2f, 0x24, 0xa5,
	0x3e, 0xa5, 0x5e, 0x26, 0xd2, 0xea, 0xc5, 0x78, 0x0b, 0xaa, 0x92, 0xea, 0x2d, 0xab, 0x29, 0xa2,
	0x09, 0xef, 0x1f, 0x96, 0x98, 0x7a, 0x57, 0x87, 0x15, 0x05, 0xf6, 0x1c, 0x8e, 0x79, 0x07, 0xe6,
	0xb0, 0xa2, 0x5b, 0x44, 0xbc, 0x51, 0xeb, 0xf2, 0x31, 0xf5, 0x85, 0x48, 0xe3, 0xef, 0x07, 0xc4,
	0x1e, 0x97, 0x52, 0x91, 0x84, 0x99, 0xf1, 0x62, 0x9d, 0xb5, 0x3a, 0x2c, 0x28, 0x86, 0x29, 0x92,
	0x2f, 0x57, 0xe3, 0xc9, 0x97, 0xec, 0xe0, 0x3b, 0x92, 0x73, 0xf9, 0x8d, 0x06, 0x95, 0x18, 0x30,
	0xe1, 0xea, 0x69, 0x78, 0xa6, 0x03, 0x5d, 0x3d, 0x5d, 0x82, 0xd5, 0xae, 0xde, 0x38, 0x42, 0x07,
	0xb8, 0x7a, 0x05, 0x09, 0x54, 0xbb, 0x7a, 0x13, 0x3d, 0xbc, 0x4a, 0x57, 0x6f, 0xb2, 0x87, 0x57,
	0x80, 0x8d, 0xdf, 0x27, 0x42, 0x7e, 0x1a, 0x0b, 0xf9, 0x4f, 0x42, 0xd1, 0xa6, 0x1d, 0xd6, 0xbf,
	0x1c, 0x45, 0x73, 0x9a, 0x77, 0xe0, 0xe5, 0x20, 0x50, 0xc0, 0x7e, 0x1d, 0x2f, 0x0d, 0x7e, 0x73,
	0x8f, 0xad, 0x6f, 0x0b, 0xb1, 0xc4, 0x5e, 0x36, 0x93, 0x9e, 0xbb, 0x16, 0xf3, 0xdc, 0xef, 0x42,
	0x25, 0x56, 0x47, 0x25, 0xeb, 0x47, 0x46, 0xaf, 0xfc, 0x2a, 0xd9, 0xfd, 0xb6, 0xf1, 0xd7, 0x5e,
	0x7d, 0x1a, 0x4d, 0x24, 0xb8, 0x2d, 0x58, 0x48, 0xd4, 0x21, 0xe0, 0x53, 0x8c, 0xa6, 0x2e, 0x00,
	0x57, 0x2d, 0xd1, 0x8f, 0xfe, 0x31, 0x59, 0x14, 0x3e, 0xb5, 0xcc, 0xb9, 0x89, 0xee, 0x5a, 0x1b,
	0x96, 0x94, 0x43, 0x15, 0x32, 0xf8, 0x7c, 0x5c, 0x06, 0xb3, 0x76, 0x1d, 0x3b, 0x9e, 0xa8, 0x3c,
	0xfe, 0x29, 0x51, 0x80, 0x49, 0x8f, 0x50, 0x80, 0xd9, 0x0f, 0xb6, 0xf4, 0x58, 0xb0, 0x75, 0x74,
	0x93, 0xc1, 0xe3, 0xcd, 0x9e, 0xc0, 0x08, 0x05, 0x53, 0x40, 0x05, 0x53, 0x0e, 0xa5, 0x06, 0xd3,
	0xc7, 0x71, 0x23, 0x36, 0x71, 0xbc, 0x52, 0xa3, 0xc9, 0x1c, 0x4b, 0x8d, 0xa6, 0x46, 0xac, 0xd2,
	0xa4, 0xb9, 0x57, 0x69, 0xd2, 0x23, 0x56, 0x69, 0x52, 0x65, 0x95, 0x26, 0x8d, 0x54, 0x69, 0x26,
	0xaa, 0x1f, 0xe9, 0xe3, 0xa9, 0x7e, 0xa4, 0x8f, 0x52, 0xfd, 0x48, 0x95, 0xd5, 0x8f, 0x34, 0xab,
	0xfa, 0x91, 0x0e, 0xac, 0x7e, 0xa4, 0x79, 0x56, 0x3f, 0xd2, 0xc1, 0xd5, 0x8f, 0xbf, 0xd0, 0x60,
	0x6d, 0xd0, 0xeb, 0x6c, 0x2f, 0x68, 0x3c, 0xb6, 0x44, 0x3c, 0xdf, 0x4b, 0x0a, 0xc7, 0xf2, 0x6d,
	0xc9, 0x83, 0x11, 0x08, 0x37, 0x7c, 0xd6, 0x0e, 0x33, 0xc2, 0x32, 0xcf, 0x32, 0x20, 0x71, 0x62,
	0xbc, 0xad, 0xc1, 0xa5, 0x11, 0xa8, 0xcf, 0x41, 0x38, 0xfe, 0x0f, 0xa6, 0x84, 0x2c, 0x84, 0x55,
	0x68, 0x35, 0x15, 0xa7, 0xa5, 0x06, 0x0b, 0x87, 0x1a, 0xef, 0x6b, 0x00, 0xfd, 0x4d, 0xf1, 0x6d,
	0x44, 0x0c, 0x69, 0xd1, 0x94, 0x2d, 0xb2, 0x08, 0x13, 0xc2, 0x0a, 0x0a, 0x4d, 0x25, 0x1a, 0x7c,
	0xf4, 0x6e, 0x98, 0x0b, 0xc4, 0xd1, 0xa2, 0xc5, 0xfb, 0x7b, 0x49, 0x40, 0xec, 0x17, 0x2d, 0x72,
	0x1e, 0x66, 0x1a, 0x4e, 0x7b, 0xdb, 0xea, 0x50, 0x8c, 0x3c, 0xac, 0x26, 0x9a, 0xcd, 0x82, 0x59,
	0x89, 0xf4, 0x6e, 0x34, 0xb9, 0x31, 0x7b, 0xc0, 0x5c, 0x8f, 0x33, 0x73, 0x52, 0x78, 0x78, 0xb2,
	0x69, 0x7c, 0x4d, 0x83, 0x52, 0x34, 0x29, 0x9b, 0x5e, 0x50, 0x1b, 0xb2, 0xa0, 0x1e, 0x5b, 0x90,
	0xdc, 0x86, 0x4a, 0x34, 0xfd, 0x1b, 0x86, 0x41, 0x67, 0xd4, 0x4e, 0x51, 0x94, 0x83, 0xe5, 0x48,
	0x02, 0xdb, 0x33, 0x18, 0x90, 0xa3, 0x17, 0x94, 0x44, 0xff, 0xf7, 0xe1, 0xa6, 0x5c, 0xb6, 0xe2,
	0xc6, 0x5c, 0x8f, 0x18, 0xf3, 0xab, 0xef, 0xcf, 0xc1, 0xf2, 0xdd, 0x86, 0xe3, 0x09, 0x67, 0x15,
	0x6d, 0xdc, 0x16, 0x73, 0x1f, 0x58, 0x0d, 0x46, 0xb6, 0x61, 0x36, 0xf1, 0x03, 0x08, 0x19, 0xf8,
	0x5f, 0x53, 0x2c, 0xea, 0xad, 0x5d, 0x18, 0x36, 0x4c, 0x08, 0xa7, 0x31, 0x46, 0xf6, 0x93, 0x79,
	0x06, 0x89, 0xe8, 0xc9, 0xcc, 0x74, 0x41, 0x1c, 0xdb, 0xe5, 0x91, 0xc6, 0xf6, 0x50, 0x7e, 0x02,
	0x2a, 0x31, 0xbf, 0x83, 0x9c, 0xcd, 0xae, 0x9e, 0x16, 0x48, 0x46, 0x2a, 0xb1, 0x36, 0xc6, 0xc8,
	0x5e, 0x98, 0x65, 0x8a, 0xa1, 0x58, 0x1b, 0xa1, 0x7e, 0x5c, 0xe0, 0xb9, 0x34, 0x72, 0xa5, 0xb9,
	0x31, 0x46, 0x5e, 0x83, 0x52, 0xe4, 0xf1, 0x9f, 0x24, 0x65, 0x2c, 0x5d, 0x86, 0x50, 0x33, 0xb2,
	0x86, 0xf4, 0xd6, 0x65, 0x30, 0x97, 0xac, 0x2c, 0x20, 0x17, 0x06, 0x3c, 0xb0, 0x26, 0x31, 0x5c,
	0x1c, 0x3a, 0x2e, 0x8a, 0x26, 0xa9, 0xc3, 0x52, 0x68, 0x06, 0x14, 0x04, 0xd7, 0x46, 0xad, 0x80,
	0x34, 0xc6, 0x88, 0x0f, 0x4b, 0x4a, 0x7d, 0x49, 0x2e, 0x8f, 0x56, 0xf0, 0x29, 0x10, 0x5e, 0x39,
	0x4a, 0x75, 0xa8, 0x31, 0x46, 0xde, 0x1a, 0x50, 0xd1, 0x7c, 0xdb, 0x71, 0x6f, 0xd8, 0x2d, 0xe7,
	0xf1, 0x22, 0x7f, 0x4f, 0x83, 0x73, 0xa3, 0xd4, 0x1f, 0x91, 0xeb, 0x8f, 0x54, 0xb4, 0x24, 0x88,
	0x7a, 0xf6, 0x18, 0x05, 0x4f, 0xc6, 0x18, 0xa9, 0xc3, 0x4c, 0xfc, 0x7d, 0x9e, 0x9c, 0x4b, 0x0b,
	0xa7, 0x82, 0x17, 0xe7, 0x87, 0x8c, 0xea, 0x21, 0xe8, 0xc0, 0x82, 0xa2, 0x0a, 0x80, 0x5c, 0x1a,
	0xa5, 0x52, 0x40, 0xa0, 0x7a, 0x72, 0xf4, 0xa2, 0x02, 0x63, 0x8c, 0xbc, 0xa3, 0xc1, 0xd9, 0x11,
	0x92, 0x2c, 0xe4, 0xa3, 0x99, 0xfa, 0x2a, 0x2b, 0x9d, 0x58, 0xbb, 0xfe, 0x28, 0x53, 0x13, 0x1c,
	0x8f, 0xfc, 0x52, 0xae, 0xe2, 0x78, 0xfa, 0xe7, 0x77, 0x15, 0xc7, 0x15, 0xff, 0xa5, 0x1b, 0x63,
	0xa4, 0x2b, 0x5d, 0xeb, 0x3e, 0x8b, 0xfa, 0x41, 0x3f, 0xb9, 0x38, 0x3c, 0x2d, 0x20, 0xd0, 0xad,
	0x8d, 0x9a, 0x3f, 0x48, 0xa9, 0x5b, 0x3a, 0x5c, 0xdd, 0xd2, 0x91, 0xd5, 0x2d, 0x55, 0xea, 0xab,
	0xc4, 0x3f, 0xba, 0xe4, 0xc2, 0xd0, 0x3f, 0x7d, 0x07, 0xe8, 0xab, 0x01, 0xff, 0x24, 0x1b, 0x63,
	0xe4, 0xeb, 0x1a, 0x9c, 0x19, 0xea, 0xe0, 0x91, 0x67, 0x46, 0xbc, 0x7d, 0x49, 0x87, 0xb6, 0x76,
	0xed, 0xe8, 0x13, 0x43, 0xd2, 0xfe, 0x1e, 0x00, 0x00, 0xff, 0xff, 0x7e, 0x4c, 0x71, 0x44, 0x5a,
	0x41, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosWeightLimitServiceClient is the client API for LcosWeightLimitService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosWeightLimitServiceClient interface {
	CalculateWeight(ctx context.Context, in *CalculateWeightRequest, opts ...grpc.CallOption) (*CalculateWeightResponse, error)
	BatchCalculateWeight(ctx context.Context, in *BatchCalculateWeightRequest, opts ...grpc.CallOption) (*BatchCalculateWeightResponse, error)
	CheckLineRule(ctx context.Context, in *CheckLineRuleRequest, opts ...grpc.CallOption) (*CheckLineRuleResponse, error)
	BatchCheckLineRule(ctx context.Context, in *BatchCheckLineRuleRequest, opts ...grpc.CallOption) (*BatchCheckLineRuleResponse, error)
	GetLineRule(ctx context.Context, in *GetLineRuleRequest, opts ...grpc.CallOption) (*GetLineRuleResponse, error)
	BatchGetLineRule(ctx context.Context, in *BatchGetLineRuleRequest, opts ...grpc.CallOption) (*BatchGetLineRuleResponse, error)
	CheckProductRule(ctx context.Context, in *CheckProductRuleRequest, opts ...grpc.CallOption) (*CheckProductRuleResponse, error)
	BatchCheckProductRule(ctx context.Context, in *BatchCheckProductRuleRequest, opts ...grpc.CallOption) (*BatchCheckProductRuleResponse, error)
	BatchCheckProductRuleForAlgo(ctx context.Context, in *BatchCheckProductRuleRequest, opts ...grpc.CallOption) (*BatchCheckProductRuleResponse, error)
	BatchCheckProductRuleForShoppingCart(ctx context.Context, in *BatchCheckProductRuleForShoppingCartRequest, opts ...grpc.CallOption) (*BatchCheckProductRuleForShoppingCartResponse, error)
	GetProductRule(ctx context.Context, in *GetProductRuleRequest, opts ...grpc.CallOption) (*GetProductRuleResponse, error)
	BatchGetProductRule(ctx context.Context, in *BatchGetProductRuleRequest, opts ...grpc.CallOption) (*BatchGetProductRuleResponse, error)
	BatchCalculateProductValidateWeight(ctx context.Context, in *BatchCalculateProductValidateWeightRequest, opts ...grpc.CallOption) (*BatchCalculateProductValidateWeightResponse, error)
	GetTWSizeInfos(ctx context.Context, in *GetTWSizeInfosRequest, opts ...grpc.CallOption) (*GetTWSizeInfosResponse, error)
	BatchGetProductSideLimits(ctx context.Context, in *ProductSideLimitsRequest, opts ...grpc.CallOption) (*ProductSideLimitsResponse, error)
	// SPLN-22250 lane rule, if one line is not valid, the whole lane is not valid
	BatchCheckLaneRule(ctx context.Context, in *BatchCheckLaneRuleRequest, opts ...grpc.CallOption) (*BatchCheckLaneRuleResponse, error)
	CalculateFormula(ctx context.Context, in *CalculateFormulaRequest, opts ...grpc.CallOption) (*CalculateFormulaResponse, error)
	BatchCheckProductRuleForParcelLib(ctx context.Context, in *BatchCheckProductRuleForParcelLibRequest, opts ...grpc.CallOption) (*BatchCheckProductRuleForParcelLibResponse, error)
}

type lcosWeightLimitServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosWeightLimitServiceClient(cc grpc.ClientConnInterface) LcosWeightLimitServiceClient {
	return &lcosWeightLimitServiceClient{cc}
}

func (c *lcosWeightLimitServiceClient) CalculateWeight(ctx context.Context, in *CalculateWeightRequest, opts ...grpc.CallOption) (*CalculateWeightResponse, error) {
	out := new(CalculateWeightResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/CalculateWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchCalculateWeight(ctx context.Context, in *BatchCalculateWeightRequest, opts ...grpc.CallOption) (*BatchCalculateWeightResponse, error) {
	out := new(BatchCalculateWeightResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchCalculateWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) CheckLineRule(ctx context.Context, in *CheckLineRuleRequest, opts ...grpc.CallOption) (*CheckLineRuleResponse, error) {
	out := new(CheckLineRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/CheckLineRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchCheckLineRule(ctx context.Context, in *BatchCheckLineRuleRequest, opts ...grpc.CallOption) (*BatchCheckLineRuleResponse, error) {
	out := new(BatchCheckLineRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchCheckLineRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) GetLineRule(ctx context.Context, in *GetLineRuleRequest, opts ...grpc.CallOption) (*GetLineRuleResponse, error) {
	out := new(GetLineRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/GetLineRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchGetLineRule(ctx context.Context, in *BatchGetLineRuleRequest, opts ...grpc.CallOption) (*BatchGetLineRuleResponse, error) {
	out := new(BatchGetLineRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchGetLineRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) CheckProductRule(ctx context.Context, in *CheckProductRuleRequest, opts ...grpc.CallOption) (*CheckProductRuleResponse, error) {
	out := new(CheckProductRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/CheckProductRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchCheckProductRule(ctx context.Context, in *BatchCheckProductRuleRequest, opts ...grpc.CallOption) (*BatchCheckProductRuleResponse, error) {
	out := new(BatchCheckProductRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchCheckProductRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchCheckProductRuleForAlgo(ctx context.Context, in *BatchCheckProductRuleRequest, opts ...grpc.CallOption) (*BatchCheckProductRuleResponse, error) {
	out := new(BatchCheckProductRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchCheckProductRuleForAlgo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchCheckProductRuleForShoppingCart(ctx context.Context, in *BatchCheckProductRuleForShoppingCartRequest, opts ...grpc.CallOption) (*BatchCheckProductRuleForShoppingCartResponse, error) {
	out := new(BatchCheckProductRuleForShoppingCartResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchCheckProductRuleForShoppingCart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) GetProductRule(ctx context.Context, in *GetProductRuleRequest, opts ...grpc.CallOption) (*GetProductRuleResponse, error) {
	out := new(GetProductRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/GetProductRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchGetProductRule(ctx context.Context, in *BatchGetProductRuleRequest, opts ...grpc.CallOption) (*BatchGetProductRuleResponse, error) {
	out := new(BatchGetProductRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchGetProductRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchCalculateProductValidateWeight(ctx context.Context, in *BatchCalculateProductValidateWeightRequest, opts ...grpc.CallOption) (*BatchCalculateProductValidateWeightResponse, error) {
	out := new(BatchCalculateProductValidateWeightResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchCalculateProductValidateWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) GetTWSizeInfos(ctx context.Context, in *GetTWSizeInfosRequest, opts ...grpc.CallOption) (*GetTWSizeInfosResponse, error) {
	out := new(GetTWSizeInfosResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/GetTWSizeInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchGetProductSideLimits(ctx context.Context, in *ProductSideLimitsRequest, opts ...grpc.CallOption) (*ProductSideLimitsResponse, error) {
	out := new(ProductSideLimitsResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchGetProductSideLimits", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchCheckLaneRule(ctx context.Context, in *BatchCheckLaneRuleRequest, opts ...grpc.CallOption) (*BatchCheckLaneRuleResponse, error) {
	out := new(BatchCheckLaneRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchCheckLaneRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) CalculateFormula(ctx context.Context, in *CalculateFormulaRequest, opts ...grpc.CallOption) (*CalculateFormulaResponse, error) {
	out := new(CalculateFormulaResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/CalculateFormula", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosWeightLimitServiceClient) BatchCheckProductRuleForParcelLib(ctx context.Context, in *BatchCheckProductRuleForParcelLibRequest, opts ...grpc.CallOption) (*BatchCheckProductRuleForParcelLibResponse, error) {
	out := new(BatchCheckProductRuleForParcelLibResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosWeightLimitService/BatchCheckProductRuleForParcelLib", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosWeightLimitServiceServer is the server API for LcosWeightLimitService service.
type LcosWeightLimitServiceServer interface {
	CalculateWeight(context.Context, *CalculateWeightRequest) (*CalculateWeightResponse, error)
	BatchCalculateWeight(context.Context, *BatchCalculateWeightRequest) (*BatchCalculateWeightResponse, error)
	CheckLineRule(context.Context, *CheckLineRuleRequest) (*CheckLineRuleResponse, error)
	BatchCheckLineRule(context.Context, *BatchCheckLineRuleRequest) (*BatchCheckLineRuleResponse, error)
	GetLineRule(context.Context, *GetLineRuleRequest) (*GetLineRuleResponse, error)
	BatchGetLineRule(context.Context, *BatchGetLineRuleRequest) (*BatchGetLineRuleResponse, error)
	CheckProductRule(context.Context, *CheckProductRuleRequest) (*CheckProductRuleResponse, error)
	BatchCheckProductRule(context.Context, *BatchCheckProductRuleRequest) (*BatchCheckProductRuleResponse, error)
	BatchCheckProductRuleForAlgo(context.Context, *BatchCheckProductRuleRequest) (*BatchCheckProductRuleResponse, error)
	BatchCheckProductRuleForShoppingCart(context.Context, *BatchCheckProductRuleForShoppingCartRequest) (*BatchCheckProductRuleForShoppingCartResponse, error)
	GetProductRule(context.Context, *GetProductRuleRequest) (*GetProductRuleResponse, error)
	BatchGetProductRule(context.Context, *BatchGetProductRuleRequest) (*BatchGetProductRuleResponse, error)
	BatchCalculateProductValidateWeight(context.Context, *BatchCalculateProductValidateWeightRequest) (*BatchCalculateProductValidateWeightResponse, error)
	GetTWSizeInfos(context.Context, *GetTWSizeInfosRequest) (*GetTWSizeInfosResponse, error)
	BatchGetProductSideLimits(context.Context, *ProductSideLimitsRequest) (*ProductSideLimitsResponse, error)
	// SPLN-22250 lane rule, if one line is not valid, the whole lane is not valid
	BatchCheckLaneRule(context.Context, *BatchCheckLaneRuleRequest) (*BatchCheckLaneRuleResponse, error)
	CalculateFormula(context.Context, *CalculateFormulaRequest) (*CalculateFormulaResponse, error)
	BatchCheckProductRuleForParcelLib(context.Context, *BatchCheckProductRuleForParcelLibRequest) (*BatchCheckProductRuleForParcelLibResponse, error)
}

// UnimplementedLcosWeightLimitServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLcosWeightLimitServiceServer struct {
}

func (*UnimplementedLcosWeightLimitServiceServer) CalculateWeight(ctx context.Context, req *CalculateWeightRequest) (*CalculateWeightResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateWeight not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchCalculateWeight(ctx context.Context, req *BatchCalculateWeightRequest) (*BatchCalculateWeightResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCalculateWeight not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) CheckLineRule(ctx context.Context, req *CheckLineRuleRequest) (*CheckLineRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLineRule not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchCheckLineRule(ctx context.Context, req *BatchCheckLineRuleRequest) (*BatchCheckLineRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckLineRule not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) GetLineRule(ctx context.Context, req *GetLineRuleRequest) (*GetLineRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLineRule not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchGetLineRule(ctx context.Context, req *BatchGetLineRuleRequest) (*BatchGetLineRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetLineRule not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) CheckProductRule(ctx context.Context, req *CheckProductRuleRequest) (*CheckProductRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckProductRule not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchCheckProductRule(ctx context.Context, req *BatchCheckProductRuleRequest) (*BatchCheckProductRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckProductRule not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchCheckProductRuleForAlgo(ctx context.Context, req *BatchCheckProductRuleRequest) (*BatchCheckProductRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckProductRuleForAlgo not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchCheckProductRuleForShoppingCart(ctx context.Context, req *BatchCheckProductRuleForShoppingCartRequest) (*BatchCheckProductRuleForShoppingCartResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckProductRuleForShoppingCart not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) GetProductRule(ctx context.Context, req *GetProductRuleRequest) (*GetProductRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProductRule not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchGetProductRule(ctx context.Context, req *BatchGetProductRuleRequest) (*BatchGetProductRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetProductRule not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchCalculateProductValidateWeight(ctx context.Context, req *BatchCalculateProductValidateWeightRequest) (*BatchCalculateProductValidateWeightResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCalculateProductValidateWeight not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) GetTWSizeInfos(ctx context.Context, req *GetTWSizeInfosRequest) (*GetTWSizeInfosResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTWSizeInfos not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchGetProductSideLimits(ctx context.Context, req *ProductSideLimitsRequest) (*ProductSideLimitsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetProductSideLimits not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchCheckLaneRule(ctx context.Context, req *BatchCheckLaneRuleRequest) (*BatchCheckLaneRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckLaneRule not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) CalculateFormula(ctx context.Context, req *CalculateFormulaRequest) (*CalculateFormulaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateFormula not implemented")
}
func (*UnimplementedLcosWeightLimitServiceServer) BatchCheckProductRuleForParcelLib(ctx context.Context, req *BatchCheckProductRuleForParcelLibRequest) (*BatchCheckProductRuleForParcelLibResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckProductRuleForParcelLib not implemented")
}

func RegisterLcosWeightLimitServiceServer(s *grpc.Server, srv LcosWeightLimitServiceServer) {
	s.RegisterService(&_LcosWeightLimitService_serviceDesc, srv)
}

func _LcosWeightLimitService_CalculateWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateWeightRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).CalculateWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/CalculateWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).CalculateWeight(ctx, req.(*CalculateWeightRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchCalculateWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCalculateWeightRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchCalculateWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchCalculateWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchCalculateWeight(ctx, req.(*BatchCalculateWeightRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_CheckLineRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLineRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).CheckLineRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/CheckLineRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).CheckLineRule(ctx, req.(*CheckLineRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchCheckLineRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckLineRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchCheckLineRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchCheckLineRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchCheckLineRule(ctx, req.(*BatchCheckLineRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_GetLineRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLineRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).GetLineRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/GetLineRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).GetLineRule(ctx, req.(*GetLineRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchGetLineRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetLineRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchGetLineRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchGetLineRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchGetLineRule(ctx, req.(*BatchGetLineRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_CheckProductRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckProductRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).CheckProductRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/CheckProductRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).CheckProductRule(ctx, req.(*CheckProductRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchCheckProductRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckProductRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchCheckProductRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchCheckProductRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchCheckProductRule(ctx, req.(*BatchCheckProductRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchCheckProductRuleForAlgo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckProductRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchCheckProductRuleForAlgo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchCheckProductRuleForAlgo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchCheckProductRuleForAlgo(ctx, req.(*BatchCheckProductRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchCheckProductRuleForShoppingCart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckProductRuleForShoppingCartRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchCheckProductRuleForShoppingCart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchCheckProductRuleForShoppingCart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchCheckProductRuleForShoppingCart(ctx, req.(*BatchCheckProductRuleForShoppingCartRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_GetProductRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProductRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).GetProductRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/GetProductRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).GetProductRule(ctx, req.(*GetProductRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchGetProductRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetProductRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchGetProductRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchGetProductRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchGetProductRule(ctx, req.(*BatchGetProductRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchCalculateProductValidateWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCalculateProductValidateWeightRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchCalculateProductValidateWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchCalculateProductValidateWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchCalculateProductValidateWeight(ctx, req.(*BatchCalculateProductValidateWeightRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_GetTWSizeInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTWSizeInfosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).GetTWSizeInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/GetTWSizeInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).GetTWSizeInfos(ctx, req.(*GetTWSizeInfosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchGetProductSideLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductSideLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchGetProductSideLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchGetProductSideLimits",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchGetProductSideLimits(ctx, req.(*ProductSideLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchCheckLaneRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckLaneRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchCheckLaneRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchCheckLaneRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchCheckLaneRule(ctx, req.(*BatchCheckLaneRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_CalculateFormula_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateFormulaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).CalculateFormula(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/CalculateFormula",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).CalculateFormula(ctx, req.(*CalculateFormulaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosWeightLimitService_BatchCheckProductRuleForParcelLib_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckProductRuleForParcelLibRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosWeightLimitServiceServer).BatchCheckProductRuleForParcelLib(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosWeightLimitService/BatchCheckProductRuleForParcelLib",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosWeightLimitServiceServer).BatchCheckProductRuleForParcelLib(ctx, req.(*BatchCheckProductRuleForParcelLibRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosWeightLimitService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosWeightLimitService",
	HandlerType: (*LcosWeightLimitServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CalculateWeight",
			Handler:    _LcosWeightLimitService_CalculateWeight_Handler,
		},
		{
			MethodName: "BatchCalculateWeight",
			Handler:    _LcosWeightLimitService_BatchCalculateWeight_Handler,
		},
		{
			MethodName: "CheckLineRule",
			Handler:    _LcosWeightLimitService_CheckLineRule_Handler,
		},
		{
			MethodName: "BatchCheckLineRule",
			Handler:    _LcosWeightLimitService_BatchCheckLineRule_Handler,
		},
		{
			MethodName: "GetLineRule",
			Handler:    _LcosWeightLimitService_GetLineRule_Handler,
		},
		{
			MethodName: "BatchGetLineRule",
			Handler:    _LcosWeightLimitService_BatchGetLineRule_Handler,
		},
		{
			MethodName: "CheckProductRule",
			Handler:    _LcosWeightLimitService_CheckProductRule_Handler,
		},
		{
			MethodName: "BatchCheckProductRule",
			Handler:    _LcosWeightLimitService_BatchCheckProductRule_Handler,
		},
		{
			MethodName: "BatchCheckProductRuleForAlgo",
			Handler:    _LcosWeightLimitService_BatchCheckProductRuleForAlgo_Handler,
		},
		{
			MethodName: "BatchCheckProductRuleForShoppingCart",
			Handler:    _LcosWeightLimitService_BatchCheckProductRuleForShoppingCart_Handler,
		},
		{
			MethodName: "GetProductRule",
			Handler:    _LcosWeightLimitService_GetProductRule_Handler,
		},
		{
			MethodName: "BatchGetProductRule",
			Handler:    _LcosWeightLimitService_BatchGetProductRule_Handler,
		},
		{
			MethodName: "BatchCalculateProductValidateWeight",
			Handler:    _LcosWeightLimitService_BatchCalculateProductValidateWeight_Handler,
		},
		{
			MethodName: "GetTWSizeInfos",
			Handler:    _LcosWeightLimitService_GetTWSizeInfos_Handler,
		},
		{
			MethodName: "BatchGetProductSideLimits",
			Handler:    _LcosWeightLimitService_BatchGetProductSideLimits_Handler,
		},
		{
			MethodName: "BatchCheckLaneRule",
			Handler:    _LcosWeightLimitService_BatchCheckLaneRule_Handler,
		},
		{
			MethodName: "CalculateFormula",
			Handler:    _LcosWeightLimitService_CalculateFormula_Handler,
		},
		{
			MethodName: "BatchCheckProductRuleForParcelLib",
			Handler:    _LcosWeightLimitService_BatchCheckProductRuleForParcelLib_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_weight_limit.proto",
}
