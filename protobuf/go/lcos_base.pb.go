// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_base.proto

package lcos_protobuf

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type Scene int32

const (
	Scene_UnknownScene       Scene = 0
	Scene_PDPScene           Scene = 1
	Scene_CheckoutScene      Scene = 2
	Scene_ShipmentScene      Scene = 3
	Scene_RRScene            Scene = 4
	Scene_SearchScene        Scene = 5
	Scene_SearchOfflineScene Scene = 6
)

var Scene_name = map[int32]string{
	0: "UnknownScene",
	1: "PDPScene",
	2: "CheckoutScene",
	3: "ShipmentScene",
	4: "RRScene",
	5: "SearchScene",
	6: "SearchOfflineScene",
}

var Scene_value = map[string]int32{
	"UnknownScene":       0,
	"PDPScene":           1,
	"CheckoutScene":      2,
	"ShipmentScene":      3,
	"RRScene":            4,
	"SearchScene":        5,
	"SearchOfflineScene": 6,
}

func (x Scene) Enum() *Scene {
	p := new(Scene)
	*p = x
	return p
}

func (x Scene) String() string {
	return proto.EnumName(Scene_name, int32(x))
}

func (x *Scene) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Scene_value, data, "Scene")
	if err != nil {
		return err
	}
	*x = Scene(value)
	return nil
}

func (Scene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{0}
}

type LocationCheckLevelEnum int32

const (
	LocationCheckLevelEnum_STATE    LocationCheckLevelEnum = 1
	LocationCheckLevelEnum_CITY     LocationCheckLevelEnum = 2
	LocationCheckLevelEnum_DISTRICT LocationCheckLevelEnum = 3
	LocationCheckLevelEnum_STREET   LocationCheckLevelEnum = 4
)

var LocationCheckLevelEnum_name = map[int32]string{
	1: "STATE",
	2: "CITY",
	3: "DISTRICT",
	4: "STREET",
}

var LocationCheckLevelEnum_value = map[string]int32{
	"STATE":    1,
	"CITY":     2,
	"DISTRICT": 3,
	"STREET":   4,
}

func (x LocationCheckLevelEnum) Enum() *LocationCheckLevelEnum {
	p := new(LocationCheckLevelEnum)
	*p = x
	return p
}

func (x LocationCheckLevelEnum) String() string {
	return proto.EnumName(LocationCheckLevelEnum_name, int32(x))
}

func (x *LocationCheckLevelEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(LocationCheckLevelEnum_value, data, "LocationCheckLevelEnum")
	if err != nil {
		return err
	}
	*x = LocationCheckLevelEnum(value)
	return nil
}

func (LocationCheckLevelEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{1}
}

type PaymentMethodEnum int32

const (
	PaymentMethodEnum_STANDARD PaymentMethodEnum = 1
	PaymentMethodEnum_COD      PaymentMethodEnum = 2
)

var PaymentMethodEnum_name = map[int32]string{
	1: "STANDARD",
	2: "COD",
}

var PaymentMethodEnum_value = map[string]int32{
	"STANDARD": 1,
	"COD":      2,
}

func (x PaymentMethodEnum) Enum() *PaymentMethodEnum {
	p := new(PaymentMethodEnum)
	*p = x
	return p
}

func (x PaymentMethodEnum) String() string {
	return proto.EnumName(PaymentMethodEnum_name, int32(x))
}

func (x *PaymentMethodEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PaymentMethodEnum_value, data, "PaymentMethodEnum")
	if err != nil {
		return err
	}
	*x = PaymentMethodEnum(value)
	return nil
}

func (PaymentMethodEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{2}
}

type CollectTypeEnum int32

const (
	CollectTypeEnum_PICK_UP_COLLECT  CollectTypeEnum = 1
	CollectTypeEnum_DROP_OFF_COLLECT CollectTypeEnum = 2
	CollectTypeEnum_B2C_COLLECT      CollectTypeEnum = 4
)

var CollectTypeEnum_name = map[int32]string{
	1: "PICK_UP_COLLECT",
	2: "DROP_OFF_COLLECT",
	4: "B2C_COLLECT",
}

var CollectTypeEnum_value = map[string]int32{
	"PICK_UP_COLLECT":  1,
	"DROP_OFF_COLLECT": 2,
	"B2C_COLLECT":      4,
}

func (x CollectTypeEnum) Enum() *CollectTypeEnum {
	p := new(CollectTypeEnum)
	*p = x
	return p
}

func (x CollectTypeEnum) String() string {
	return proto.EnumName(CollectTypeEnum_name, int32(x))
}

func (x *CollectTypeEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CollectTypeEnum_value, data, "CollectTypeEnum")
	if err != nil {
		return err
	}
	*x = CollectTypeEnum(value)
	return nil
}

func (CollectTypeEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{3}
}

type DeliveryTypeEnum int32

const (
	DeliveryTypeEnum_TO_HOME   DeliveryTypeEnum = 8
	DeliveryTypeEnum_TO_SITE   DeliveryTypeEnum = 16
	DeliveryTypeEnum_TO_TWS    DeliveryTypeEnum = 32
	DeliveryTypeEnum_TO_3PL    DeliveryTypeEnum = 64
	DeliveryTypeEnum_TO_BRANCH DeliveryTypeEnum = 128
)

var DeliveryTypeEnum_name = map[int32]string{
	8:   "TO_HOME",
	16:  "TO_SITE",
	32:  "TO_TWS",
	64:  "TO_3PL",
	128: "TO_BRANCH",
}

var DeliveryTypeEnum_value = map[string]int32{
	"TO_HOME":   8,
	"TO_SITE":   16,
	"TO_TWS":    32,
	"TO_3PL":    64,
	"TO_BRANCH": 128,
}

func (x DeliveryTypeEnum) Enum() *DeliveryTypeEnum {
	p := new(DeliveryTypeEnum)
	*p = x
	return p
}

func (x DeliveryTypeEnum) String() string {
	return proto.EnumName(DeliveryTypeEnum_name, int32(x))
}

func (x *DeliveryTypeEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(DeliveryTypeEnum_value, data, "DeliveryTypeEnum")
	if err != nil {
		return err
	}
	*x = DeliveryTypeEnum(value)
	return nil
}

func (DeliveryTypeEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{4}
}

type CdtScenarioEnum int32

const (
	CdtScenarioEnum_PDP      CdtScenarioEnum = 1
	CdtScenarioEnum_CheckOut CdtScenarioEnum = 2
	CdtScenarioEnum_ItemCard CdtScenarioEnum = 3
)

var CdtScenarioEnum_name = map[int32]string{
	1: "PDP",
	2: "CheckOut",
	3: "ItemCard",
}

var CdtScenarioEnum_value = map[string]int32{
	"PDP":      1,
	"CheckOut": 2,
	"ItemCard": 3,
}

func (x CdtScenarioEnum) Enum() *CdtScenarioEnum {
	p := new(CdtScenarioEnum)
	*p = x
	return p
}

func (x CdtScenarioEnum) String() string {
	return proto.EnumName(CdtScenarioEnum_name, int32(x))
}

func (x *CdtScenarioEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CdtScenarioEnum_value, data, "CdtScenarioEnum")
	if err != nil {
		return err
	}
	*x = CdtScenarioEnum(value)
	return nil
}

func (CdtScenarioEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{5}
}

type MChannelRuleEnum int32

const (
	MChannelRuleEnum_FastestFChannel        MChannelRuleEnum = 0
	MChannelRuleEnum_SlowestFChannel        MChannelRuleEnum = 1
	MChannelRuleEnum_WeightedAverage        MChannelRuleEnum = 2
	MChannelRuleEnum_HighestADO             MChannelRuleEnum = 3
	MChannelRuleEnum_WeightedAverageSPFOnly MChannelRuleEnum = 4
)

var MChannelRuleEnum_name = map[int32]string{
	0: "FastestFChannel",
	1: "SlowestFChannel",
	2: "WeightedAverage",
	3: "HighestADO",
	4: "WeightedAverageSPFOnly",
}

var MChannelRuleEnum_value = map[string]int32{
	"FastestFChannel":        0,
	"SlowestFChannel":        1,
	"WeightedAverage":        2,
	"HighestADO":             3,
	"WeightedAverageSPFOnly": 4,
}

func (x MChannelRuleEnum) Enum() *MChannelRuleEnum {
	p := new(MChannelRuleEnum)
	*p = x
	return p
}

func (x MChannelRuleEnum) String() string {
	return proto.EnumName(MChannelRuleEnum_name, int32(x))
}

func (x *MChannelRuleEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MChannelRuleEnum_value, data, "MChannelRuleEnum")
	if err != nil {
		return err
	}
	*x = MChannelRuleEnum(value)
	return nil
}

func (MChannelRuleEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{6}
}

type CdtLocationLevelEnum int32

const (
	CdtLocationLevelEnum_Country  CdtLocationLevelEnum = -1
	CdtLocationLevelEnum_State    CdtLocationLevelEnum = 0
	CdtLocationLevelEnum_City     CdtLocationLevelEnum = 1
	CdtLocationLevelEnum_District CdtLocationLevelEnum = 2
)

var CdtLocationLevelEnum_name = map[int32]string{
	-1: "Country",
	0:  "State",
	1:  "City",
	2:  "District",
}

var CdtLocationLevelEnum_value = map[string]int32{
	"Country":  -1,
	"State":    0,
	"City":     1,
	"District": 2,
}

func (x CdtLocationLevelEnum) Enum() *CdtLocationLevelEnum {
	p := new(CdtLocationLevelEnum)
	*p = x
	return p
}

func (x CdtLocationLevelEnum) String() string {
	return proto.EnumName(CdtLocationLevelEnum_name, int32(x))
}

func (x *CdtLocationLevelEnum) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CdtLocationLevelEnum_value, data, "CdtLocationLevelEnum")
	if err != nil {
		return err
	}
	*x = CdtLocationLevelEnum(value)
	return nil
}

func (CdtLocationLevelEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{7}
}

type CdtScene int32

const (
	CdtScene_Default    CdtScene = 0
	CdtScene_Simulation CdtScene = 1
	CdtScene_Offline    CdtScene = 2
)

var CdtScene_name = map[int32]string{
	0: "Default",
	1: "Simulation",
	2: "Offline",
}

var CdtScene_value = map[string]int32{
	"Default":    0,
	"Simulation": 1,
	"Offline":    2,
}

func (x CdtScene) Enum() *CdtScene {
	p := new(CdtScene)
	*p = x
	return p
}

func (x CdtScene) String() string {
	return proto.EnumName(CdtScene_name, int32(x))
}

func (x *CdtScene) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CdtScene_value, data, "CdtScene")
	if err != nil {
		return err
	}
	*x = CdtScene(value)
	return nil
}

func (CdtScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{8}
}

type ReqHeader struct {
	RequestId            *string  `protobuf:"bytes,1,req,name=request_id,json=requestId" json:"request_id,omitempty"`
	Account              *string  `protobuf:"bytes,2,req,name=account" json:"account,omitempty"`
	Token                *string  `protobuf:"bytes,3,req,name=token" json:"token,omitempty"`
	Timestamp            *uint32  `protobuf:"varint,4,req,name=timestamp" json:"timestamp,omitempty"`
	CallerIp             *string  `protobuf:"bytes,5,req,name=caller_ip,json=callerIp" json:"caller_ip,omitempty"`
	DeployRegion         *string  `protobuf:"bytes,6,opt,name=deploy_region,json=deployRegion" json:"deploy_region,omitempty"`
	LogHit               *bool    `protobuf:"varint,7,opt,name=log_hit,json=logHit" json:"log_hit,omitempty"`
	Scene                *Scene   `protobuf:"varint,8,opt,name=scene,enum=lcos_protobuf.Scene" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReqHeader) Reset()         { *m = ReqHeader{} }
func (m *ReqHeader) String() string { return proto.CompactTextString(m) }
func (*ReqHeader) ProtoMessage()    {}
func (*ReqHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{0}
}

func (m *ReqHeader) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReqHeader.Unmarshal(m, b)
}
func (m *ReqHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReqHeader.Marshal(b, m, deterministic)
}
func (m *ReqHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqHeader.Merge(m, src)
}
func (m *ReqHeader) XXX_Size() int {
	return xxx_messageInfo_ReqHeader.Size(m)
}
func (m *ReqHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqHeader.DiscardUnknown(m)
}

var xxx_messageInfo_ReqHeader proto.InternalMessageInfo

func (m *ReqHeader) GetRequestId() string {
	if m != nil && m.RequestId != nil {
		return *m.RequestId
	}
	return ""
}

func (m *ReqHeader) GetAccount() string {
	if m != nil && m.Account != nil {
		return *m.Account
	}
	return ""
}

func (m *ReqHeader) GetToken() string {
	if m != nil && m.Token != nil {
		return *m.Token
	}
	return ""
}

func (m *ReqHeader) GetTimestamp() uint32 {
	if m != nil && m.Timestamp != nil {
		return *m.Timestamp
	}
	return 0
}

func (m *ReqHeader) GetCallerIp() string {
	if m != nil && m.CallerIp != nil {
		return *m.CallerIp
	}
	return ""
}

func (m *ReqHeader) GetDeployRegion() string {
	if m != nil && m.DeployRegion != nil {
		return *m.DeployRegion
	}
	return ""
}

func (m *ReqHeader) GetLogHit() bool {
	if m != nil && m.LogHit != nil {
		return *m.LogHit
	}
	return false
}

func (m *ReqHeader) GetScene() Scene {
	if m != nil && m.Scene != nil {
		return *m.Scene
	}
	return Scene_UnknownScene
}

type RespHeader struct {
	RequestId            *string  `protobuf:"bytes,1,req,name=request_id,json=requestId" json:"request_id,omitempty"`
	Retcode              *int32   `protobuf:"varint,2,req,name=retcode" json:"retcode,omitempty"`
	Message              *string  `protobuf:"bytes,3,req,name=message" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RespHeader) Reset()         { *m = RespHeader{} }
func (m *RespHeader) String() string { return proto.CompactTextString(m) }
func (*RespHeader) ProtoMessage()    {}
func (*RespHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{1}
}

func (m *RespHeader) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RespHeader.Unmarshal(m, b)
}
func (m *RespHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RespHeader.Marshal(b, m, deterministic)
}
func (m *RespHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RespHeader.Merge(m, src)
}
func (m *RespHeader) XXX_Size() int {
	return xxx_messageInfo_RespHeader.Size(m)
}
func (m *RespHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_RespHeader.DiscardUnknown(m)
}

var xxx_messageInfo_RespHeader proto.InternalMessageInfo

func (m *RespHeader) GetRequestId() string {
	if m != nil && m.RequestId != nil {
		return *m.RequestId
	}
	return ""
}

func (m *RespHeader) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *RespHeader) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

type LocationInfo struct {
	StateLocationId      *uint32  `protobuf:"varint,1,opt,name=state_location_id,json=stateLocationId" json:"state_location_id,omitempty"`
	CityLocationId       *uint32  `protobuf:"varint,2,opt,name=city_location_id,json=cityLocationId" json:"city_location_id,omitempty"`
	DistrictLocationId   *uint32  `protobuf:"varint,3,opt,name=district_location_id,json=districtLocationId" json:"district_location_id,omitempty"`
	StreetLocationId     *uint32  `protobuf:"varint,4,opt,name=street_location_id,json=streetLocationId" json:"street_location_id,omitempty"`
	Postcode             *string  `protobuf:"bytes,5,opt,name=postcode" json:"postcode,omitempty"`
	Longitude            *string  `protobuf:"bytes,6,opt,name=longitude" json:"longitude,omitempty"`
	Latitude             *string  `protobuf:"bytes,7,opt,name=latitude" json:"latitude,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LocationInfo) Reset()         { *m = LocationInfo{} }
func (m *LocationInfo) String() string { return proto.CompactTextString(m) }
func (*LocationInfo) ProtoMessage()    {}
func (*LocationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{2}
}

func (m *LocationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LocationInfo.Unmarshal(m, b)
}
func (m *LocationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LocationInfo.Marshal(b, m, deterministic)
}
func (m *LocationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LocationInfo.Merge(m, src)
}
func (m *LocationInfo) XXX_Size() int {
	return xxx_messageInfo_LocationInfo.Size(m)
}
func (m *LocationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LocationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LocationInfo proto.InternalMessageInfo

func (m *LocationInfo) GetStateLocationId() uint32 {
	if m != nil && m.StateLocationId != nil {
		return *m.StateLocationId
	}
	return 0
}

func (m *LocationInfo) GetCityLocationId() uint32 {
	if m != nil && m.CityLocationId != nil {
		return *m.CityLocationId
	}
	return 0
}

func (m *LocationInfo) GetDistrictLocationId() uint32 {
	if m != nil && m.DistrictLocationId != nil {
		return *m.DistrictLocationId
	}
	return 0
}

func (m *LocationInfo) GetStreetLocationId() uint32 {
	if m != nil && m.StreetLocationId != nil {
		return *m.StreetLocationId
	}
	return 0
}

func (m *LocationInfo) GetPostcode() string {
	if m != nil && m.Postcode != nil {
		return *m.Postcode
	}
	return ""
}

func (m *LocationInfo) GetLongitude() string {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return ""
}

func (m *LocationInfo) GetLatitude() string {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return ""
}

type SkuInfo struct {
	ItemId               *uint64  `protobuf:"varint,1,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	CategoryId           *uint64  `protobuf:"varint,2,opt,name=category_id,json=categoryId" json:"category_id,omitempty"`
	Weight               *float64 `protobuf:"fixed64,3,opt,name=weight" json:"weight,omitempty"`
	Quantity             *uint32  `protobuf:"varint,4,opt,name=quantity" json:"quantity,omitempty"`
	Length               *float64 `protobuf:"fixed64,5,opt,name=length" json:"length,omitempty"`
	Width                *float64 `protobuf:"fixed64,6,opt,name=width" json:"width,omitempty"`
	Height               *float64 `protobuf:"fixed64,7,opt,name=height" json:"height,omitempty"`
	ItemPriceUsd         *float64 `protobuf:"fixed64,8,opt,name=item_price_usd,json=itemPriceUsd" json:"item_price_usd,omitempty"`
	ItemPrice            *float64 `protobuf:"fixed64,9,opt,name=item_price,json=itemPrice" json:"item_price,omitempty"`
	ModelId              *uint64  `protobuf:"varint,10,opt,name=model_id,json=modelId" json:"model_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkuInfo) Reset()         { *m = SkuInfo{} }
func (m *SkuInfo) String() string { return proto.CompactTextString(m) }
func (*SkuInfo) ProtoMessage()    {}
func (*SkuInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{3}
}

func (m *SkuInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkuInfo.Unmarshal(m, b)
}
func (m *SkuInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkuInfo.Marshal(b, m, deterministic)
}
func (m *SkuInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkuInfo.Merge(m, src)
}
func (m *SkuInfo) XXX_Size() int {
	return xxx_messageInfo_SkuInfo.Size(m)
}
func (m *SkuInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SkuInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SkuInfo proto.InternalMessageInfo

func (m *SkuInfo) GetItemId() uint64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *SkuInfo) GetCategoryId() uint64 {
	if m != nil && m.CategoryId != nil {
		return *m.CategoryId
	}
	return 0
}

func (m *SkuInfo) GetWeight() float64 {
	if m != nil && m.Weight != nil {
		return *m.Weight
	}
	return 0
}

func (m *SkuInfo) GetQuantity() uint32 {
	if m != nil && m.Quantity != nil {
		return *m.Quantity
	}
	return 0
}

func (m *SkuInfo) GetLength() float64 {
	if m != nil && m.Length != nil {
		return *m.Length
	}
	return 0
}

func (m *SkuInfo) GetWidth() float64 {
	if m != nil && m.Width != nil {
		return *m.Width
	}
	return 0
}

func (m *SkuInfo) GetHeight() float64 {
	if m != nil && m.Height != nil {
		return *m.Height
	}
	return 0
}

func (m *SkuInfo) GetItemPriceUsd() float64 {
	if m != nil && m.ItemPriceUsd != nil {
		return *m.ItemPriceUsd
	}
	return 0
}

func (m *SkuInfo) GetItemPrice() float64 {
	if m != nil && m.ItemPrice != nil {
		return *m.ItemPrice
	}
	return 0
}

func (m *SkuInfo) GetModelId() uint64 {
	if m != nil && m.ModelId != nil {
		return *m.ModelId
	}
	return 0
}

type SubPackageInfo struct {
	Weight               *float64 `protobuf:"fixed64,1,opt,name=weight" json:"weight,omitempty"`
	Length               *float64 `protobuf:"fixed64,2,opt,name=length" json:"length,omitempty"`
	Width                *float64 `protobuf:"fixed64,3,opt,name=width" json:"width,omitempty"`
	Height               *float64 `protobuf:"fixed64,4,opt,name=height" json:"height,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubPackageInfo) Reset()         { *m = SubPackageInfo{} }
func (m *SubPackageInfo) String() string { return proto.CompactTextString(m) }
func (*SubPackageInfo) ProtoMessage()    {}
func (*SubPackageInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{4}
}

func (m *SubPackageInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubPackageInfo.Unmarshal(m, b)
}
func (m *SubPackageInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubPackageInfo.Marshal(b, m, deterministic)
}
func (m *SubPackageInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubPackageInfo.Merge(m, src)
}
func (m *SubPackageInfo) XXX_Size() int {
	return xxx_messageInfo_SubPackageInfo.Size(m)
}
func (m *SubPackageInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SubPackageInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SubPackageInfo proto.InternalMessageInfo

func (m *SubPackageInfo) GetWeight() float64 {
	if m != nil && m.Weight != nil {
		return *m.Weight
	}
	return 0
}

func (m *SubPackageInfo) GetLength() float64 {
	if m != nil && m.Length != nil {
		return *m.Length
	}
	return 0
}

func (m *SubPackageInfo) GetWidth() float64 {
	if m != nil && m.Width != nil {
		return *m.Width
	}
	return 0
}

func (m *SubPackageInfo) GetHeight() float64 {
	if m != nil && m.Height != nil {
		return *m.Height
	}
	return 0
}

type OrderInfo struct {
	// @inject_tag: validate:"omitempty,gte=0"
	OrderActualWeight *float64 `protobuf:"fixed64,1,opt,name=order_actual_weight,json=orderActualWeight" json:"order_actual_weight,omitempty" validate:"omitempty,gte=0"`
	// @inject_tag: validate:"omitempty,gte=0"
	OrderVolumetricWeight *float64 `protobuf:"fixed64,2,opt,name=order_volumetric_weight,json=orderVolumetricWeight" json:"order_volumetric_weight,omitempty" validate:"omitempty,gte=0"`
	// @inject_tag: validate:"omitempty,gte=0"
	OrderLength *float64 `protobuf:"fixed64,3,opt,name=order_length,json=orderLength" json:"order_length,omitempty" validate:"omitempty,gte=0"`
	// @inject_tag: validate:"omitempty,gte=0"
	OrderWidth *float64 `protobuf:"fixed64,4,opt,name=order_width,json=orderWidth" json:"order_width,omitempty" validate:"omitempty,gte=0"`
	// @inject_tag: validate:"omitempty,gte=0"
	OrderHeight          *float64 `protobuf:"fixed64,5,opt,name=order_height,json=orderHeight" json:"order_height,omitempty" validate:"omitempty,gte=0"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderInfo) Reset()         { *m = OrderInfo{} }
func (m *OrderInfo) String() string { return proto.CompactTextString(m) }
func (*OrderInfo) ProtoMessage()    {}
func (*OrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a434ad7476226795, []int{5}
}

func (m *OrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderInfo.Unmarshal(m, b)
}
func (m *OrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderInfo.Marshal(b, m, deterministic)
}
func (m *OrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderInfo.Merge(m, src)
}
func (m *OrderInfo) XXX_Size() int {
	return xxx_messageInfo_OrderInfo.Size(m)
}
func (m *OrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderInfo proto.InternalMessageInfo

func (m *OrderInfo) GetOrderActualWeight() float64 {
	if m != nil && m.OrderActualWeight != nil {
		return *m.OrderActualWeight
	}
	return 0
}

func (m *OrderInfo) GetOrderVolumetricWeight() float64 {
	if m != nil && m.OrderVolumetricWeight != nil {
		return *m.OrderVolumetricWeight
	}
	return 0
}

func (m *OrderInfo) GetOrderLength() float64 {
	if m != nil && m.OrderLength != nil {
		return *m.OrderLength
	}
	return 0
}

func (m *OrderInfo) GetOrderWidth() float64 {
	if m != nil && m.OrderWidth != nil {
		return *m.OrderWidth
	}
	return 0
}

func (m *OrderInfo) GetOrderHeight() float64 {
	if m != nil && m.OrderHeight != nil {
		return *m.OrderHeight
	}
	return 0
}

func init() {
	proto.RegisterEnum("lcos_protobuf.Scene", Scene_name, Scene_value)
	proto.RegisterEnum("lcos_protobuf.LocationCheckLevelEnum", LocationCheckLevelEnum_name, LocationCheckLevelEnum_value)
	proto.RegisterEnum("lcos_protobuf.PaymentMethodEnum", PaymentMethodEnum_name, PaymentMethodEnum_value)
	proto.RegisterEnum("lcos_protobuf.CollectTypeEnum", CollectTypeEnum_name, CollectTypeEnum_value)
	proto.RegisterEnum("lcos_protobuf.DeliveryTypeEnum", DeliveryTypeEnum_name, DeliveryTypeEnum_value)
	proto.RegisterEnum("lcos_protobuf.CdtScenarioEnum", CdtScenarioEnum_name, CdtScenarioEnum_value)
	proto.RegisterEnum("lcos_protobuf.MChannelRuleEnum", MChannelRuleEnum_name, MChannelRuleEnum_value)
	proto.RegisterEnum("lcos_protobuf.CdtLocationLevelEnum", CdtLocationLevelEnum_name, CdtLocationLevelEnum_value)
	proto.RegisterEnum("lcos_protobuf.CdtScene", CdtScene_name, CdtScene_value)
	proto.RegisterType((*ReqHeader)(nil), "lcos_protobuf.ReqHeader")
	proto.RegisterType((*RespHeader)(nil), "lcos_protobuf.RespHeader")
	proto.RegisterType((*LocationInfo)(nil), "lcos_protobuf.LocationInfo")
	proto.RegisterType((*SkuInfo)(nil), "lcos_protobuf.SkuInfo")
	proto.RegisterType((*SubPackageInfo)(nil), "lcos_protobuf.SubPackageInfo")
	proto.RegisterType((*OrderInfo)(nil), "lcos_protobuf.OrderInfo")
}

func init() {
	proto.RegisterFile("lcos_base.proto", fileDescriptor_a434ad7476226795)
}

var fileDescriptor_a434ad7476226795 = []byte{
	// 1085 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x55, 0x51, 0x6f, 0xe3, 0x44,
	0x10, 0xae, 0x9d, 0xa4, 0x4e, 0xa6, 0x69, 0xbb, 0xdd, 0x2b, 0xbd, 0x70, 0x80, 0xae, 0x14, 0x1e,
	0xa2, 0x08, 0x55, 0xe8, 0x40, 0xf7, 0x4c, 0xce, 0x49, 0xa9, 0x45, 0x7b, 0xb6, 0xd6, 0x3e, 0x4e,
	0x3c, 0x59, 0x3e, 0x7b, 0x9a, 0x58, 0xdd, 0x78, 0x53, 0x7b, 0xdd, 0x2a, 0x0f, 0x48, 0x48, 0xfc,
	0x0b, 0x24, 0xfe, 0x18, 0x7f, 0xe6, 0xd0, 0xee, 0x3a, 0x69, 0x83, 0xee, 0x81, 0x3c, 0xf9, 0xfb,
	0xe6, 0x9b, 0x9d, 0xf9, 0x66, 0xd6, 0x0e, 0x1c, 0xf2, 0x54, 0x54, 0xf1, 0x87, 0xa4, 0xc2, 0xf3,
	0x65, 0x29, 0xa4, 0xa0, 0xfb, 0x9a, 0xd0, 0xcf, 0x1f, 0xea, 0x9b, 0xb3, 0x8f, 0x16, 0xf4, 0x18,
	0xde, 0x5d, 0x62, 0x92, 0x61, 0x49, 0xbf, 0x02, 0x28, 0xf1, 0xae, 0xc6, 0x4a, 0xc6, 0x79, 0x36,
	0xb0, 0x4e, 0xed, 0x61, 0x8f, 0xf5, 0x1a, 0xc6, 0xcb, 0xe8, 0x00, 0x9c, 0x24, 0x4d, 0x45, 0x5d,
	0xc8, 0x81, 0xad, 0x63, 0x6b, 0x48, 0x8f, 0xa1, 0x23, 0xc5, 0x2d, 0x16, 0x83, 0x96, 0xe6, 0x0d,
	0xa0, 0x5f, 0x42, 0x4f, 0xe6, 0x0b, 0xac, 0x64, 0xb2, 0x58, 0x0e, 0xda, 0xa7, 0xf6, 0x70, 0x9f,
	0x3d, 0x12, 0xf4, 0x0b, 0xe8, 0xa5, 0x09, 0xe7, 0x58, 0xc6, 0xf9, 0x72, 0xd0, 0xd1, 0x79, 0x5d,
	0x43, 0x78, 0x4b, 0xfa, 0x0d, 0xec, 0x67, 0xb8, 0xe4, 0x62, 0x15, 0x97, 0x38, 0xcb, 0x45, 0x31,
	0xd8, 0x3d, 0xb5, 0x86, 0x3d, 0xd6, 0x37, 0x24, 0xd3, 0x1c, 0x7d, 0x0e, 0x0e, 0x17, 0xb3, 0x78,
	0x9e, 0xcb, 0x81, 0x73, 0x6a, 0x0d, 0xbb, 0x6c, 0x97, 0x8b, 0xd9, 0x65, 0x2e, 0xe9, 0x08, 0x3a,
	0x55, 0x8a, 0x05, 0x0e, 0xba, 0xa7, 0xd6, 0xf0, 0xe0, 0xd5, 0xf1, 0xf9, 0x96, 0xe9, 0xf3, 0x50,
	0xc5, 0x98, 0x91, 0x9c, 0xc5, 0x00, 0x0c, 0xab, 0xe5, 0xff, 0x9e, 0x40, 0x89, 0x32, 0x15, 0x19,
	0xea, 0x09, 0x74, 0xd8, 0x1a, 0xaa, 0xc8, 0x02, 0xab, 0x2a, 0x99, 0x61, 0x33, 0x83, 0x35, 0x3c,
	0xfb, 0xcb, 0x86, 0xfe, 0x95, 0x48, 0x13, 0x99, 0x8b, 0xc2, 0x2b, 0x6e, 0x04, 0x1d, 0xc1, 0x51,
	0x25, 0x13, 0x89, 0x31, 0x6f, 0x58, 0x53, 0xca, 0x1a, 0xee, 0xb3, 0x43, 0x1d, 0xd8, 0xa8, 0x33,
	0x3a, 0x04, 0x92, 0xe6, 0x72, 0xb5, 0x25, 0xb5, 0xb5, 0xf4, 0x40, 0xf1, 0x4f, 0x94, 0xdf, 0xc3,
	0x71, 0x96, 0x57, 0xb2, 0xcc, 0x53, 0xb9, 0xa5, 0x6e, 0x69, 0x35, 0x5d, 0xc7, 0x9e, 0x64, 0x7c,
	0x07, 0xb4, 0x92, 0x25, 0xe2, 0xb6, 0xbe, 0xad, 0xf5, 0xc4, 0x44, 0x9e, 0xa8, 0x5f, 0x40, 0x77,
	0x29, 0x2a, 0xe3, 0xbd, 0xa3, 0x97, 0xb1, 0xc1, 0x6a, 0xd1, 0x5c, 0x14, 0xb3, 0x5c, 0xd6, 0x19,
	0x36, 0x9b, 0x7a, 0x24, 0x54, 0x26, 0x4f, 0xa4, 0x09, 0x3a, 0x26, 0x73, 0x8d, 0xcf, 0xfe, 0xb6,
	0xc1, 0x09, 0x6f, 0x6b, 0x3d, 0x97, 0xe7, 0xe0, 0xe4, 0x12, 0x17, 0xeb, 0x69, 0xb4, 0xd9, 0xae,
	0x82, 0x5e, 0x46, 0x5f, 0xc2, 0x5e, 0x9a, 0x48, 0x9c, 0x89, 0x72, 0xb5, 0xf6, 0xdf, 0x66, 0xb0,
	0xa6, 0xbc, 0x8c, 0x9e, 0xc0, 0xee, 0x03, 0xe6, 0xb3, 0xb9, 0xd4, 0x6e, 0x2d, 0xd6, 0x20, 0x55,
	0xf9, 0xae, 0x4e, 0x0a, 0x99, 0xcb, 0x55, 0xe3, 0x6b, 0x83, 0x55, 0x0e, 0xc7, 0x62, 0x26, 0xe7,
	0xda, 0x8d, 0xc5, 0x1a, 0xa4, 0xae, 0xf2, 0x43, 0x9e, 0xc9, 0xb9, 0xf6, 0x61, 0x31, 0x03, 0x94,
	0x7a, 0x6e, 0x2a, 0x38, 0x46, 0x6d, 0x10, 0xfd, 0x16, 0x0e, 0x74, 0xcf, 0xcb, 0x32, 0x4f, 0x31,
	0xae, 0xab, 0x4c, 0x5f, 0x39, 0x8b, 0xf5, 0x15, 0x1b, 0x28, 0xf2, 0x5d, 0x95, 0xa9, 0x5b, 0xf5,
	0xa8, 0x1a, 0xf4, 0xb4, 0xa2, 0xb7, 0x51, 0xd0, 0xcf, 0xa1, 0xbb, 0x10, 0x19, 0x72, 0x65, 0x0e,
	0xb4, 0x39, 0x47, 0x63, 0x2f, 0x3b, 0x2b, 0xe0, 0x20, 0xac, 0x3f, 0x04, 0x49, 0x7a, 0x9b, 0xcc,
	0x50, 0x4f, 0xe9, 0xd1, 0xab, 0xb5, 0xe5, 0xf5, 0xd1, 0x8f, 0xfd, 0x69, 0x3f, 0xad, 0x4f, 0xfb,
	0x69, 0x3f, 0xf5, 0x73, 0xf6, 0x8f, 0x05, 0x3d, 0xbf, 0xcc, 0xb0, 0xd4, 0xb5, 0xce, 0xe1, 0x99,
	0x50, 0x20, 0x4e, 0x52, 0x59, 0x27, 0x3c, 0xde, 0x2a, 0x7c, 0xa4, 0x43, 0x63, 0x1d, 0x79, 0x6f,
	0x7a, 0x78, 0x0d, 0xcf, 0x8d, 0xfe, 0x5e, 0xf0, 0x7a, 0x81, 0xea, 0xc2, 0xad, 0x73, 0x4c, 0x53,
	0x9f, 0xe9, 0xf0, 0xaf, 0x9b, 0x68, 0x93, 0xf7, 0x35, 0xf4, 0x4d, 0x5e, 0xe3, 0xc0, 0xb4, 0xba,
	0xa7, 0xb9, 0x2b, 0x63, 0xe3, 0x25, 0x18, 0x18, 0x1b, 0x33, 0xa6, 0x6b, 0xd0, 0xd4, 0x7b, 0xed,
	0x68, 0x73, 0x46, 0xe3, 0xab, 0xf3, 0xe4, 0x8c, 0x4b, 0x4d, 0x8d, 0xfe, 0xb4, 0xa0, 0xa3, 0xdf,
	0x7d, 0x4a, 0xa0, 0xff, 0xae, 0xb8, 0x2d, 0xc4, 0x43, 0xa1, 0x31, 0xd9, 0xa1, 0x7d, 0xe8, 0x06,
	0x93, 0xc0, 0x20, 0x8b, 0x1e, 0xc1, 0xbe, 0x3b, 0xc7, 0xf4, 0x56, 0xd4, 0xd2, 0x50, 0xb6, 0xa2,
	0xc2, 0x79, 0xbe, 0x5c, 0x60, 0xd1, 0x50, 0x2d, 0xba, 0x07, 0x0e, 0x63, 0x06, 0xb4, 0xe9, 0x21,
	0xec, 0x85, 0x98, 0x94, 0xe9, 0xdc, 0x10, 0x1d, 0x7a, 0x02, 0xd4, 0x10, 0xfe, 0xcd, 0x0d, 0xcf,
	0x0b, 0x34, 0xfc, 0xee, 0xe8, 0x67, 0x38, 0x59, 0xbf, 0x56, 0xba, 0xc6, 0x15, 0xde, 0x23, 0x9f,
	0x16, 0xf5, 0x82, 0xf6, 0xa0, 0x13, 0x46, 0xe3, 0x68, 0x4a, 0x2c, 0xda, 0x85, 0xb6, 0xeb, 0x45,
	0xbf, 0x11, 0x5b, 0x35, 0x36, 0xf1, 0xc2, 0x88, 0x79, 0x6e, 0x44, 0x5a, 0x14, 0x60, 0x37, 0x8c,
	0xd8, 0x74, 0x1a, 0x91, 0xf6, 0x68, 0x04, 0x47, 0x41, 0xb2, 0x52, 0x0d, 0x5d, 0xa3, 0x9c, 0x8b,
	0x4c, 0x9f, 0xd1, 0x87, 0x6e, 0x18, 0x8d, 0xdf, 0x4e, 0xc6, 0x6c, 0x42, 0x2c, 0xea, 0x40, 0xcb,
	0xf5, 0x27, 0xc4, 0x1e, 0x5d, 0xc3, 0xa1, 0x2b, 0x38, 0xc7, 0x54, 0x46, 0xab, 0x25, 0x6a, 0xe5,
	0x33, 0x38, 0x0c, 0x3c, 0xf7, 0x97, 0xf8, 0x5d, 0x10, 0xbb, 0xfe, 0xd5, 0xd5, 0xd4, 0x8d, 0x88,
	0x45, 0x8f, 0x81, 0x4c, 0x98, 0x1f, 0xc4, 0xfe, 0xc5, 0xc5, 0x86, 0xb5, 0x95, 0xb7, 0x37, 0xaf,
	0xdc, 0x0d, 0xd1, 0x1e, 0x45, 0x40, 0x26, 0xc8, 0xf3, 0x7b, 0x2c, 0x57, 0x9b, 0xf3, 0xf6, 0xc0,
	0x89, 0xfc, 0xf8, 0xd2, 0xbf, 0x9e, 0x92, 0x6e, 0x03, 0x42, 0x2f, 0x9a, 0x12, 0xa2, 0x9a, 0x8e,
	0xfc, 0x38, 0x7a, 0x1f, 0x92, 0xd3, 0xe6, 0xf9, 0x87, 0xe0, 0x8a, 0xfc, 0x44, 0x0f, 0xa0, 0x17,
	0xf9, 0xf1, 0x1b, 0x36, 0x7e, 0xeb, 0x5e, 0x92, 0x3f, 0xac, 0xd1, 0x6b, 0x38, 0x74, 0x33, 0x3d,
	0xdd, 0xa4, 0xcc, 0x85, 0x3e, 0xd4, 0x81, 0x56, 0x30, 0x09, 0x88, 0xa5, 0x7c, 0xe9, 0x69, 0xf9,
	0xb5, 0x34, 0x43, 0xf1, 0x24, 0x2e, 0xdc, 0xa4, 0xcc, 0x48, 0x6b, 0xf4, 0x3b, 0x90, 0x6b, 0x77,
	0x9e, 0x14, 0x05, 0x72, 0x56, 0xf3, 0x8d, 0xbb, 0x8b, 0xa4, 0x92, 0x58, 0xc9, 0x8b, 0x26, 0x44,
	0x76, 0x14, 0x19, 0x72, 0xf1, 0xf0, 0x94, 0xb4, 0x14, 0x69, 0xae, 0x21, 0x66, 0xe3, 0x7b, 0x2c,
	0x93, 0x99, 0xda, 0xf6, 0x01, 0xc0, 0x65, 0x3e, 0x9b, 0x63, 0x25, 0xc7, 0x13, 0x9f, 0xb4, 0xe8,
	0x0b, 0x38, 0xf9, 0x8f, 0x28, 0x0c, 0x2e, 0xfc, 0x82, 0xaf, 0x48, 0x7b, 0xe4, 0xc3, 0xb1, 0x9b,
	0x6d, 0x3e, 0x95, 0x8f, 0xeb, 0x3c, 0x06, 0xc7, 0x55, 0xff, 0x8e, 0xe5, 0x8a, 0x7c, 0x5c, 0xff,
	0x2c, 0xbd, 0x64, 0xf5, 0x91, 0x27, 0x3b, 0x7a, 0xc9, 0xb9, 0x5c, 0x19, 0x77, 0x93, 0xe6, 0x03,
	0x4d, 0xec, 0xd1, 0x8f, 0xd0, 0x6d, 0xe6, 0x80, 0x6a, 0x90, 0x13, 0xbc, 0x49, 0x6a, 0x2e, 0xc9,
	0x8e, 0xea, 0x2a, 0xcc, 0x17, 0x35, 0xd7, 0x85, 0x88, 0xa5, 0x82, 0xcd, 0xe5, 0x22, 0xf6, 0xbf,
	0x01, 0x00, 0x00, 0xff, 0xff, 0xea, 0xb9, 0x5a, 0x3c, 0xeb, 0x07, 0x00, 0x00,
}
