// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_pis_branch.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

func init() {
	proto.RegisterFile("lcos_pis_branch.proto", fileDescriptor_7c44cd60eb7d4905)
}

var fileDescriptor_7c44cd60eb7d4905 = []byte{
	// 140 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0xcd, 0x49, 0xce, 0x2f,
	0x8e, 0x2f, 0xc8, 0x2c, 0x8e, 0x4f, 0x2a, 0x4a, 0xcc, 0x4b, 0xce, 0xd0, 0x2b, 0x28, 0xca, 0x2f,
	0xc9, 0x17, 0xe2, 0x85, 0x08, 0x83, 0xd8, 0x49, 0xa5, 0x69, 0x52, 0x7c, 0x30, 0x55, 0x10, 0x69,
	0xa3, 0x5a, 0x2e, 0x81, 0x00, 0xcf, 0x60, 0x27, 0xb0, 0x8e, 0xe0, 0xd4, 0xa2, 0xb2, 0xcc, 0xe4,
	0x54, 0xa1, 0x4c, 0x2e, 0x19, 0xf7, 0xd4, 0x12, 0x88, 0x98, 0x4f, 0x66, 0x71, 0x89, 0x53, 0x25,
	0x84, 0xed, 0x5e, 0x94, 0x5f, 0x5a, 0xe0, 0xe9, 0x22, 0xa4, 0xa4, 0x87, 0x62, 0xa6, 0x9e, 0x4b,
	0x66, 0x71, 0x49, 0x51, 0x66, 0x52, 0x69, 0x49, 0x66, 0x7e, 0x5e, 0x50, 0x6a, 0x61, 0x69, 0x6a,
	0x71, 0x89, 0x94, 0x32, 0x5e, 0x35, 0xc5, 0x05, 0xf9, 0x79, 0xc5, 0xa9, 0x4a, 0x0c, 0x80, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x63, 0x57, 0xbd, 0xd1, 0xb5, 0x00, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// PISBranchServiceClient is the client API for PISBranchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PISBranchServiceClient interface {
	//
	//通过BranchGroupID获取branch列表；
	GetBranchListByBranchGroupID(ctx context.Context, in *DistributionRequest, opts ...grpc.CallOption) (*DistributionResponse, error)
}

type pISBranchServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPISBranchServiceClient(cc grpc.ClientConnInterface) PISBranchServiceClient {
	return &pISBranchServiceClient{cc}
}

func (c *pISBranchServiceClient) GetBranchListByBranchGroupID(ctx context.Context, in *DistributionRequest, opts ...grpc.CallOption) (*DistributionResponse, error) {
	out := new(DistributionResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.PISBranchService/GetBranchListByBranchGroupID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PISBranchServiceServer is the server API for PISBranchService service.
type PISBranchServiceServer interface {
	//
	//通过BranchGroupID获取branch列表；
	GetBranchListByBranchGroupID(context.Context, *DistributionRequest) (*DistributionResponse, error)
}

// UnimplementedPISBranchServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPISBranchServiceServer struct {
}

func (*UnimplementedPISBranchServiceServer) GetBranchListByBranchGroupID(ctx context.Context, req *DistributionRequest) (*DistributionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBranchListByBranchGroupID not implemented")
}

func RegisterPISBranchServiceServer(s *grpc.Server, srv PISBranchServiceServer) {
	s.RegisterService(&_PISBranchService_serviceDesc, srv)
}

func _PISBranchService_GetBranchListByBranchGroupID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DistributionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PISBranchServiceServer).GetBranchListByBranchGroupID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.PISBranchService/GetBranchListByBranchGroupID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PISBranchServiceServer).GetBranchListByBranchGroupID(ctx, req.(*DistributionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PISBranchService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.PISBranchService",
	HandlerType: (*PISBranchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBranchListByBranchGroupID",
			Handler:    _PISBranchService_GetBranchListByBranchGroupID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_pis_branch.proto",
}
