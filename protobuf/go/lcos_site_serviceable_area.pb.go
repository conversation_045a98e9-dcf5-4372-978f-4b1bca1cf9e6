// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_site_serviceable_area.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type SingleGetActualSiteIDList struct {
	UniqueId             *string  `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	SiteId               *string  `protobuf:"bytes,2,req,name=site_id,json=siteId" json:"site_id,omitempty"`
	StateLocationId      *uint32  `protobuf:"varint,3,opt,name=state_location_id,json=stateLocationId" json:"state_location_id,omitempty"`
	CityLocationId       *uint32  `protobuf:"varint,4,opt,name=city_location_id,json=cityLocationId" json:"city_location_id,omitempty"`
	DistrictLocationId   *uint32  `protobuf:"varint,5,opt,name=district_location_id,json=districtLocationId" json:"district_location_id,omitempty"`
	StreetLocationId     *uint32  `protobuf:"varint,6,opt,name=street_location_id,json=streetLocationId" json:"street_location_id,omitempty"`
	Zipcode              *string  `protobuf:"bytes,7,opt,name=zipcode" json:"zipcode,omitempty"`
	SkipPostcode         *uint32  `protobuf:"varint,8,opt,name=skip_postcode,json=skipPostcode" json:"skip_postcode,omitempty"`
	SkipCheck            *uint32  `protobuf:"varint,9,opt,name=skip_check,json=skipCheck" json:"skip_check,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingleGetActualSiteIDList) Reset()         { *m = SingleGetActualSiteIDList{} }
func (m *SingleGetActualSiteIDList) String() string { return proto.CompactTextString(m) }
func (*SingleGetActualSiteIDList) ProtoMessage()    {}
func (*SingleGetActualSiteIDList) Descriptor() ([]byte, []int) {
	return fileDescriptor_be3a741a29ff4cea, []int{0}
}

func (m *SingleGetActualSiteIDList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleGetActualSiteIDList.Unmarshal(m, b)
}
func (m *SingleGetActualSiteIDList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleGetActualSiteIDList.Marshal(b, m, deterministic)
}
func (m *SingleGetActualSiteIDList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleGetActualSiteIDList.Merge(m, src)
}
func (m *SingleGetActualSiteIDList) XXX_Size() int {
	return xxx_messageInfo_SingleGetActualSiteIDList.Size(m)
}
func (m *SingleGetActualSiteIDList) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleGetActualSiteIDList.DiscardUnknown(m)
}

var xxx_messageInfo_SingleGetActualSiteIDList proto.InternalMessageInfo

func (m *SingleGetActualSiteIDList) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SingleGetActualSiteIDList) GetSiteId() string {
	if m != nil && m.SiteId != nil {
		return *m.SiteId
	}
	return ""
}

func (m *SingleGetActualSiteIDList) GetStateLocationId() uint32 {
	if m != nil && m.StateLocationId != nil {
		return *m.StateLocationId
	}
	return 0
}

func (m *SingleGetActualSiteIDList) GetCityLocationId() uint32 {
	if m != nil && m.CityLocationId != nil {
		return *m.CityLocationId
	}
	return 0
}

func (m *SingleGetActualSiteIDList) GetDistrictLocationId() uint32 {
	if m != nil && m.DistrictLocationId != nil {
		return *m.DistrictLocationId
	}
	return 0
}

func (m *SingleGetActualSiteIDList) GetStreetLocationId() uint32 {
	if m != nil && m.StreetLocationId != nil {
		return *m.StreetLocationId
	}
	return 0
}

func (m *SingleGetActualSiteIDList) GetZipcode() string {
	if m != nil && m.Zipcode != nil {
		return *m.Zipcode
	}
	return ""
}

func (m *SingleGetActualSiteIDList) GetSkipPostcode() uint32 {
	if m != nil && m.SkipPostcode != nil {
		return *m.SkipPostcode
	}
	return 0
}

func (m *SingleGetActualSiteIDList) GetSkipCheck() uint32 {
	if m != nil && m.SkipCheck != nil {
		return *m.SkipCheck
	}
	return 0
}

type BatchGetActualSiteIDListRequest struct {
	ReqHeader            *ReqHeader                   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	SiteList             []*SingleGetActualSiteIDList `protobuf:"bytes,2,rep,name=site_list,json=siteList" json:"site_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchGetActualSiteIDListRequest) Reset()         { *m = BatchGetActualSiteIDListRequest{} }
func (m *BatchGetActualSiteIDListRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetActualSiteIDListRequest) ProtoMessage()    {}
func (*BatchGetActualSiteIDListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_be3a741a29ff4cea, []int{1}
}

func (m *BatchGetActualSiteIDListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetActualSiteIDListRequest.Unmarshal(m, b)
}
func (m *BatchGetActualSiteIDListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetActualSiteIDListRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetActualSiteIDListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetActualSiteIDListRequest.Merge(m, src)
}
func (m *BatchGetActualSiteIDListRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetActualSiteIDListRequest.Size(m)
}
func (m *BatchGetActualSiteIDListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetActualSiteIDListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetActualSiteIDListRequest proto.InternalMessageInfo

func (m *BatchGetActualSiteIDListRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetActualSiteIDListRequest) GetSiteList() []*SingleGetActualSiteIDList {
	if m != nil {
		return m.SiteList
	}
	return nil
}

type ActualSiteList struct {
	ActualSiteIdList     []string `protobuf:"bytes,1,rep,name=actual_site_id_list,json=actualSiteIdList" json:"actual_site_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActualSiteList) Reset()         { *m = ActualSiteList{} }
func (m *ActualSiteList) String() string { return proto.CompactTextString(m) }
func (*ActualSiteList) ProtoMessage()    {}
func (*ActualSiteList) Descriptor() ([]byte, []int) {
	return fileDescriptor_be3a741a29ff4cea, []int{2}
}

func (m *ActualSiteList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActualSiteList.Unmarshal(m, b)
}
func (m *ActualSiteList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActualSiteList.Marshal(b, m, deterministic)
}
func (m *ActualSiteList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActualSiteList.Merge(m, src)
}
func (m *ActualSiteList) XXX_Size() int {
	return xxx_messageInfo_ActualSiteList.Size(m)
}
func (m *ActualSiteList) XXX_DiscardUnknown() {
	xxx_messageInfo_ActualSiteList.DiscardUnknown(m)
}

var xxx_messageInfo_ActualSiteList proto.InternalMessageInfo

func (m *ActualSiteList) GetActualSiteIdList() []string {
	if m != nil {
		return m.ActualSiteIdList
	}
	return nil
}

type BatchGetActualSiteIDListResponse struct {
	RespHeader           *RespHeader                `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ActualSiteMap        map[string]*ActualSiteList `protobuf:"bytes,2,rep,name=actual_site_map,json=actualSiteMap" json:"actual_site_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BatchGetActualSiteIDListResponse) Reset()         { *m = BatchGetActualSiteIDListResponse{} }
func (m *BatchGetActualSiteIDListResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetActualSiteIDListResponse) ProtoMessage()    {}
func (*BatchGetActualSiteIDListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_be3a741a29ff4cea, []int{3}
}

func (m *BatchGetActualSiteIDListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetActualSiteIDListResponse.Unmarshal(m, b)
}
func (m *BatchGetActualSiteIDListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetActualSiteIDListResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetActualSiteIDListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetActualSiteIDListResponse.Merge(m, src)
}
func (m *BatchGetActualSiteIDListResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetActualSiteIDListResponse.Size(m)
}
func (m *BatchGetActualSiteIDListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetActualSiteIDListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetActualSiteIDListResponse proto.InternalMessageInfo

func (m *BatchGetActualSiteIDListResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetActualSiteIDListResponse) GetActualSiteMap() map[string]*ActualSiteList {
	if m != nil {
		return m.ActualSiteMap
	}
	return nil
}

func init() {
	proto.RegisterType((*SingleGetActualSiteIDList)(nil), "lcos_protobuf.SingleGetActualSiteIDList")
	proto.RegisterType((*BatchGetActualSiteIDListRequest)(nil), "lcos_protobuf.BatchGetActualSiteIDListRequest")
	proto.RegisterType((*ActualSiteList)(nil), "lcos_protobuf.ActualSiteList")
	proto.RegisterType((*BatchGetActualSiteIDListResponse)(nil), "lcos_protobuf.BatchGetActualSiteIDListResponse")
	proto.RegisterMapType((map[string]*ActualSiteList)(nil), "lcos_protobuf.BatchGetActualSiteIDListResponse.ActualSiteMapEntry")
}

func init() {
	proto.RegisterFile("lcos_site_serviceable_area.proto", fileDescriptor_be3a741a29ff4cea)
}

var fileDescriptor_be3a741a29ff4cea = []byte{
	// 522 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x93, 0x51, 0x8f, 0xd2, 0x40,
	0x10, 0xc7, 0x6d, 0xf1, 0x0e, 0x3a, 0xc8, 0x81, 0xab, 0x89, 0x3d, 0xcc, 0x69, 0x83, 0x2f, 0x8d,
	0x51, 0x34, 0xf8, 0xa0, 0xb9, 0x17, 0x73, 0xa7, 0x17, 0x25, 0xc1, 0xc4, 0x94, 0x0f, 0xd0, 0x2c,
	0xdb, 0x51, 0x56, 0x2a, 0x2d, 0xbb, 0xdb, 0x4b, 0x30, 0xf1, 0xb3, 0x18, 0x9f, 0xfd, 0x5c, 0x7e,
	0x0f, 0xb3, 0x43, 0x11, 0xca, 0x49, 0xd4, 0xb7, 0x99, 0xff, 0xff, 0x37, 0xdb, 0x9d, 0x99, 0x2d,
	0x04, 0xa9, 0xc8, 0x74, 0xac, 0xa5, 0xc1, 0x58, 0xa3, 0xba, 0x94, 0x02, 0xf9, 0x24, 0xc5, 0x98,
	0x2b, 0xe4, 0xfd, 0x5c, 0x65, 0x26, 0x63, 0x2d, 0x22, 0x28, 0x9e, 0x14, 0x1f, 0xba, 0x6d, 0x4a,
	0x27, 0x5c, 0xe3, 0xca, 0xef, 0xfd, 0x74, 0xe1, 0x78, 0x2c, 0xe7, 0x1f, 0x53, 0x7c, 0x83, 0xe6,
	0x4c, 0x98, 0x82, 0xa7, 0x63, 0x69, 0x70, 0xf8, 0x7a, 0x24, 0xb5, 0x61, 0x77, 0xc1, 0x2b, 0xe6,
	0x72, 0x51, 0x60, 0x2c, 0x13, 0xdf, 0x09, 0xdc, 0xd0, 0x8b, 0x1a, 0x2b, 0x61, 0x98, 0xb0, 0x3b,
	0x50, 0xa7, 0x2f, 0xcb, 0xc4, 0x77, 0xc9, 0x3a, 0xb4, 0xe9, 0x30, 0x61, 0x0f, 0xe1, 0xa6, 0x36,
	0xdc, 0x60, 0x9c, 0x66, 0x82, 0x1b, 0x99, 0xcd, 0x2d, 0x52, 0x0b, 0x9c, 0xb0, 0x15, 0xb5, 0xc9,
	0x18, 0x95, 0xfa, 0x30, 0x61, 0x21, 0x74, 0x84, 0x34, 0xcb, 0x0a, 0x7a, 0x9d, 0xd0, 0x23, 0xab,
	0x6f, 0x91, 0x4f, 0xe1, 0x76, 0x22, 0xb5, 0x51, 0x52, 0x98, 0x0a, 0x7d, 0x40, 0x34, 0x5b, 0x7b,
	0x5b, 0x15, 0x8f, 0x80, 0x69, 0xa3, 0x10, 0xab, 0xfc, 0x21, 0xf1, 0x9d, 0x95, 0xb3, 0x45, 0xfb,
	0x50, 0xff, 0x22, 0x73, 0x91, 0x25, 0xe8, 0xd7, 0x03, 0x27, 0xf4, 0xa2, 0x75, 0xca, 0x1e, 0x40,
	0x4b, 0xcf, 0x64, 0x1e, 0xe7, 0x99, 0x36, 0xe4, 0x37, 0xe8, 0x88, 0x1b, 0x56, 0x7c, 0x5f, 0x6a,
	0xec, 0x04, 0x80, 0x20, 0x31, 0x45, 0x31, 0xf3, 0x3d, 0x22, 0x3c, 0xab, 0xbc, 0xb2, 0x42, 0xef,
	0xbb, 0x03, 0xf7, 0xcf, 0xb9, 0x11, 0xd3, 0x3f, 0x8c, 0x39, 0xc2, 0x45, 0x81, 0xda, 0xb0, 0xe7,
	0x00, 0x0a, 0x17, 0xf1, 0x14, 0x79, 0x82, 0x8a, 0xc6, 0xdd, 0x1c, 0xf8, 0xfd, 0xca, 0x02, 0xfb,
	0x11, 0x2e, 0xde, 0x92, 0x1f, 0x79, 0x6a, 0x1d, 0xb2, 0x0b, 0xf0, 0x68, 0x13, 0xa9, 0xd4, 0xc6,
	0x77, 0x83, 0x5a, 0xd8, 0x1c, 0x84, 0x3b, 0x75, 0x7b, 0x77, 0x1c, 0x35, 0x6c, 0xa9, 0x8d, 0x7a,
	0x2f, 0xe1, 0x68, 0xe3, 0xd2, 0xfe, 0x1f, 0xc3, 0x2d, 0x4e, 0x4a, 0x5c, 0x6e, 0x7a, 0xf5, 0x09,
	0x27, 0xa8, 0x85, 0x5e, 0xd4, 0xe1, 0x9b, 0xa3, 0x12, 0x3a, 0xe0, 0x87, 0x0b, 0xc1, 0xfe, 0x26,
	0x75, 0x9e, 0xcd, 0x35, 0xb2, 0x53, 0x68, 0x2a, 0xd4, 0x79, 0xb5, 0xcd, 0xe3, 0x2b, 0x6d, 0xea,
	0xbc, 0xec, 0x13, 0xd4, 0xef, 0x98, 0x7d, 0x82, 0xf6, 0xf6, 0x7d, 0x3e, 0xf3, 0xbc, 0x6c, 0xf7,
	0x7c, 0xa7, 0xfe, 0x6f, 0xb7, 0xe8, 0x6f, 0x8c, 0x77, 0x3c, 0xbf, 0x98, 0x1b, 0xb5, 0x8c, 0x5a,
	0x7c, 0x5b, 0xeb, 0xc6, 0xc0, 0xae, 0x42, 0xac, 0x03, 0xb5, 0x19, 0x2e, 0x7d, 0x87, 0x5e, 0x88,
	0x0d, 0xd9, 0x33, 0x38, 0xb8, 0xe4, 0x69, 0x81, 0xbe, 0x1b, 0x38, 0x61, 0x73, 0x70, 0xb2, 0x73,
	0x93, 0xea, 0x44, 0xa3, 0x15, 0x7b, 0xea, 0xbe, 0x70, 0x06, 0xdf, 0x1c, 0xb8, 0x37, 0x12, 0x99,
	0xb6, 0xde, 0x78, 0xf3, 0xf7, 0x9e, 0x29, 0xe4, 0x65, 0xca, 0xbe, 0x82, 0xbf, 0xaf, 0x13, 0xd6,
	0xff, 0xe7, 0x96, 0xe9, 0x75, 0x75, 0x9f, 0xfc, 0xe7, 0x88, 0x7a, 0xd7, 0x7e, 0x05, 0x00, 0x00,
	0xff, 0xff, 0xdb, 0x10, 0xb4, 0xc9, 0x5f, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosSiteServiceableAreaServiceClient is the client API for LcosSiteServiceableAreaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosSiteServiceableAreaServiceClient interface {
	// 批量获取site serviceable area信息
	BatchGetActualSiteIDList(ctx context.Context, in *BatchGetActualSiteIDListRequest, opts ...grpc.CallOption) (*BatchGetActualSiteIDListResponse, error)
}

type lcosSiteServiceableAreaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosSiteServiceableAreaServiceClient(cc grpc.ClientConnInterface) LcosSiteServiceableAreaServiceClient {
	return &lcosSiteServiceableAreaServiceClient{cc}
}

func (c *lcosSiteServiceableAreaServiceClient) BatchGetActualSiteIDList(ctx context.Context, in *BatchGetActualSiteIDListRequest, opts ...grpc.CallOption) (*BatchGetActualSiteIDListResponse, error) {
	out := new(BatchGetActualSiteIDListResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosSiteServiceableAreaService/BatchGetActualSiteIDList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosSiteServiceableAreaServiceServer is the server API for LcosSiteServiceableAreaService service.
type LcosSiteServiceableAreaServiceServer interface {
	// 批量获取site serviceable area信息
	BatchGetActualSiteIDList(context.Context, *BatchGetActualSiteIDListRequest) (*BatchGetActualSiteIDListResponse, error)
}

// UnimplementedLcosSiteServiceableAreaServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLcosSiteServiceableAreaServiceServer struct {
}

func (*UnimplementedLcosSiteServiceableAreaServiceServer) BatchGetActualSiteIDList(ctx context.Context, req *BatchGetActualSiteIDListRequest) (*BatchGetActualSiteIDListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetActualSiteIDList not implemented")
}

func RegisterLcosSiteServiceableAreaServiceServer(s *grpc.Server, srv LcosSiteServiceableAreaServiceServer) {
	s.RegisterService(&_LcosSiteServiceableAreaService_serviceDesc, srv)
}

func _LcosSiteServiceableAreaService_BatchGetActualSiteIDList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetActualSiteIDListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosSiteServiceableAreaServiceServer).BatchGetActualSiteIDList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosSiteServiceableAreaService/BatchGetActualSiteIDList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosSiteServiceableAreaServiceServer).BatchGetActualSiteIDList(ctx, req.(*BatchGetActualSiteIDListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosSiteServiceableAreaService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosSiteServiceableAreaService",
	HandlerType: (*LcosSiteServiceableAreaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetActualSiteIDList",
			Handler:    _LcosSiteServiceableAreaService_BatchGetActualSiteIDList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_site_serviceable_area.proto",
}
