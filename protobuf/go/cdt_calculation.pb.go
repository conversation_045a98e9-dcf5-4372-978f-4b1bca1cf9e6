// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cdt_calculation.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type AddressInfo struct {
	StateLocationId      *int32   `protobuf:"varint,1,opt,name=state_location_id,json=stateLocationId" json:"state_location_id,omitempty"`
	CityLocationId       *int32   `protobuf:"varint,2,opt,name=city_location_id,json=cityLocationId" json:"city_location_id,omitempty"`
	DistrictLocationId   *int32   `protobuf:"varint,3,opt,name=district_location_id,json=districtLocationId" json:"district_location_id,omitempty"`
	Postcode             *string  `protobuf:"bytes,4,opt,name=postcode" json:"postcode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddressInfo) Reset()         { *m = AddressInfo{} }
func (m *AddressInfo) String() string { return proto.CompactTextString(m) }
func (*AddressInfo) ProtoMessage()    {}
func (*AddressInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{0}
}

func (m *AddressInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddressInfo.Unmarshal(m, b)
}
func (m *AddressInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddressInfo.Marshal(b, m, deterministic)
}
func (m *AddressInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddressInfo.Merge(m, src)
}
func (m *AddressInfo) XXX_Size() int {
	return xxx_messageInfo_AddressInfo.Size(m)
}
func (m *AddressInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AddressInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AddressInfo proto.InternalMessageInfo

func (m *AddressInfo) GetStateLocationId() int32 {
	if m != nil && m.StateLocationId != nil {
		return *m.StateLocationId
	}
	return 0
}

func (m *AddressInfo) GetCityLocationId() int32 {
	if m != nil && m.CityLocationId != nil {
		return *m.CityLocationId
	}
	return 0
}

func (m *AddressInfo) GetDistrictLocationId() int32 {
	if m != nil && m.DistrictLocationId != nil {
		return *m.DistrictLocationId
	}
	return 0
}

func (m *AddressInfo) GetPostcode() string {
	if m != nil && m.Postcode != nil {
		return *m.Postcode
	}
	return ""
}

type CdtSkus struct {
	ShopId               *string    `protobuf:"bytes,1,req,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	SkuItems             []*SkuInfo `protobuf:"bytes,2,rep,name=sku_items,json=skuItems" json:"sku_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CdtSkus) Reset()         { *m = CdtSkus{} }
func (m *CdtSkus) String() string { return proto.CompactTextString(m) }
func (*CdtSkus) ProtoMessage()    {}
func (*CdtSkus) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{1}
}

func (m *CdtSkus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CdtSkus.Unmarshal(m, b)
}
func (m *CdtSkus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CdtSkus.Marshal(b, m, deterministic)
}
func (m *CdtSkus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CdtSkus.Merge(m, src)
}
func (m *CdtSkus) XXX_Size() int {
	return xxx_messageInfo_CdtSkus.Size(m)
}
func (m *CdtSkus) XXX_DiscardUnknown() {
	xxx_messageInfo_CdtSkus.DiscardUnknown(m)
}

var xxx_messageInfo_CdtSkus proto.InternalMessageInfo

func (m *CdtSkus) GetShopId() string {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return ""
}

func (m *CdtSkus) GetSkuItems() []*SkuInfo {
	if m != nil {
		return m.SkuItems
	}
	return nil
}

type SimpleLineInfo struct {
	LineId               *string  `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	LineSubType          *uint32  `protobuf:"varint,2,req,name=line_sub_type,json=lineSubType" json:"line_sub_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleLineInfo) Reset()         { *m = SimpleLineInfo{} }
func (m *SimpleLineInfo) String() string { return proto.CompactTextString(m) }
func (*SimpleLineInfo) ProtoMessage()    {}
func (*SimpleLineInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{2}
}

func (m *SimpleLineInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleLineInfo.Unmarshal(m, b)
}
func (m *SimpleLineInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleLineInfo.Marshal(b, m, deterministic)
}
func (m *SimpleLineInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleLineInfo.Merge(m, src)
}
func (m *SimpleLineInfo) XXX_Size() int {
	return xxx_messageInfo_SimpleLineInfo.Size(m)
}
func (m *SimpleLineInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleLineInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleLineInfo proto.InternalMessageInfo

func (m *SimpleLineInfo) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *SimpleLineInfo) GetLineSubType() uint32 {
	if m != nil && m.LineSubType != nil {
		return *m.LineSubType
	}
	return 0
}

type ProductInfo struct {
	QueryId                        *string           `protobuf:"bytes,1,req,name=query_id,json=queryId" json:"query_id,omitempty"`
	ProductId                      *string           `protobuf:"bytes,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	IsCb                           *int32            `protobuf:"varint,3,req,name=is_cb,json=isCb" json:"is_cb,omitempty"`
	IsSiteLine                     *int32            `protobuf:"varint,4,req,name=is_site_line,json=isSiteLine" json:"is_site_line,omitempty"`
	IsLm                           *int32            `protobuf:"varint,5,req,name=is_lm,json=isLm" json:"is_lm,omitempty"`
	Region                         *string           `protobuf:"bytes,6,req,name=region" json:"region,omitempty"`
	TplUniqueKey                   *string           `protobuf:"bytes,7,opt,name=tpl_unique_key,json=tplUniqueKey" json:"tpl_unique_key,omitempty"`
	SellerAddr                     *AddressInfo      `protobuf:"bytes,8,req,name=seller_addr,json=sellerAddr" json:"seller_addr,omitempty"`
	BuyerAddr                      *AddressInfo      `protobuf:"bytes,9,req,name=buyer_addr,json=buyerAddr" json:"buyer_addr,omitempty"`
	SkuInfo                        *CdtSkus          `protobuf:"bytes,10,opt,name=sku_info,json=skuInfo" json:"sku_info,omitempty"`
	LaneCode                       *string           `protobuf:"bytes,11,opt,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	LineList                       []*SimpleLineInfo `protobuf:"bytes,12,rep,name=line_list,json=lineList" json:"line_list,omitempty"`
	UpdateEvent                    *uint32           `protobuf:"varint,13,opt,name=update_event,json=updateEvent" json:"update_event,omitempty"`
	NextEvent                      *uint32           `protobuf:"varint,14,opt,name=next_event,json=nextEvent" json:"next_event,omitempty"`
	ItemId                         *uint64           `protobuf:"varint,15,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	BuyerId                        *uint64           `protobuf:"varint,16,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`
	Scenario                       *CdtScenarioEnum  `protobuf:"varint,17,opt,name=scenario,enum=lcos_protobuf.CdtScenarioEnum" json:"scenario,omitempty"`
	AvailableFulfillmentChannelIds []*FChannelInfo   `protobuf:"bytes,18,rep,name=available_fulfillment_channel_ids,json=availableFulfillmentChannelIds" json:"available_fulfillment_channel_ids,omitempty"`
	NeedVolume                     *int32            `protobuf:"varint,19,opt,name=need_volume,json=needVolume" json:"need_volume,omitempty"`
	StartDay                       *uint32           `protobuf:"varint,20,opt,name=start_day,json=startDay" json:"start_day,omitempty"`
	StartHour                      *uint32           `protobuf:"varint,21,opt,name=start_hour,json=startHour" json:"start_hour,omitempty"`
	EndDay                         *uint32           `protobuf:"varint,22,opt,name=end_day,json=endDay" json:"end_day,omitempty"`
	EndHour                        *uint32           `protobuf:"varint,23,opt,name=end_hour,json=endHour" json:"end_hour,omitempty"`
	GroupTag                       *uint32           `protobuf:"varint,24,opt,name=group_tag,json=groupTag" json:"group_tag,omitempty"`
	RequestTime                    *uint32           `protobuf:"varint,25,opt,name=request_time,json=requestTime" json:"request_time,omitempty"`
	StartFcodeType                 *uint32           `protobuf:"varint,26,opt,name=start_fcode_type,json=startFcodeType" json:"start_fcode_type,omitempty"`
	Scene                          *CdtScene         `protobuf:"varint,27,opt,name=scene,enum=lcos_protobuf.CdtScene" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral           struct{}          `json:"-"`
	XXX_unrecognized               []byte            `json:"-"`
	XXX_sizecache                  int32             `json:"-"`
}

func (m *ProductInfo) Reset()         { *m = ProductInfo{} }
func (m *ProductInfo) String() string { return proto.CompactTextString(m) }
func (*ProductInfo) ProtoMessage()    {}
func (*ProductInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{3}
}

func (m *ProductInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductInfo.Unmarshal(m, b)
}
func (m *ProductInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductInfo.Marshal(b, m, deterministic)
}
func (m *ProductInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductInfo.Merge(m, src)
}
func (m *ProductInfo) XXX_Size() int {
	return xxx_messageInfo_ProductInfo.Size(m)
}
func (m *ProductInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ProductInfo proto.InternalMessageInfo

func (m *ProductInfo) GetQueryId() string {
	if m != nil && m.QueryId != nil {
		return *m.QueryId
	}
	return ""
}

func (m *ProductInfo) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *ProductInfo) GetIsCb() int32 {
	if m != nil && m.IsCb != nil {
		return *m.IsCb
	}
	return 0
}

func (m *ProductInfo) GetIsSiteLine() int32 {
	if m != nil && m.IsSiteLine != nil {
		return *m.IsSiteLine
	}
	return 0
}

func (m *ProductInfo) GetIsLm() int32 {
	if m != nil && m.IsLm != nil {
		return *m.IsLm
	}
	return 0
}

func (m *ProductInfo) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *ProductInfo) GetTplUniqueKey() string {
	if m != nil && m.TplUniqueKey != nil {
		return *m.TplUniqueKey
	}
	return ""
}

func (m *ProductInfo) GetSellerAddr() *AddressInfo {
	if m != nil {
		return m.SellerAddr
	}
	return nil
}

func (m *ProductInfo) GetBuyerAddr() *AddressInfo {
	if m != nil {
		return m.BuyerAddr
	}
	return nil
}

func (m *ProductInfo) GetSkuInfo() *CdtSkus {
	if m != nil {
		return m.SkuInfo
	}
	return nil
}

func (m *ProductInfo) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *ProductInfo) GetLineList() []*SimpleLineInfo {
	if m != nil {
		return m.LineList
	}
	return nil
}

func (m *ProductInfo) GetUpdateEvent() uint32 {
	if m != nil && m.UpdateEvent != nil {
		return *m.UpdateEvent
	}
	return 0
}

func (m *ProductInfo) GetNextEvent() uint32 {
	if m != nil && m.NextEvent != nil {
		return *m.NextEvent
	}
	return 0
}

func (m *ProductInfo) GetItemId() uint64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *ProductInfo) GetBuyerId() uint64 {
	if m != nil && m.BuyerId != nil {
		return *m.BuyerId
	}
	return 0
}

func (m *ProductInfo) GetScenario() CdtScenarioEnum {
	if m != nil && m.Scenario != nil {
		return *m.Scenario
	}
	return CdtScenarioEnum_PDP
}

func (m *ProductInfo) GetAvailableFulfillmentChannelIds() []*FChannelInfo {
	if m != nil {
		return m.AvailableFulfillmentChannelIds
	}
	return nil
}

func (m *ProductInfo) GetNeedVolume() int32 {
	if m != nil && m.NeedVolume != nil {
		return *m.NeedVolume
	}
	return 0
}

func (m *ProductInfo) GetStartDay() uint32 {
	if m != nil && m.StartDay != nil {
		return *m.StartDay
	}
	return 0
}

func (m *ProductInfo) GetStartHour() uint32 {
	if m != nil && m.StartHour != nil {
		return *m.StartHour
	}
	return 0
}

func (m *ProductInfo) GetEndDay() uint32 {
	if m != nil && m.EndDay != nil {
		return *m.EndDay
	}
	return 0
}

func (m *ProductInfo) GetEndHour() uint32 {
	if m != nil && m.EndHour != nil {
		return *m.EndHour
	}
	return 0
}

func (m *ProductInfo) GetGroupTag() uint32 {
	if m != nil && m.GroupTag != nil {
		return *m.GroupTag
	}
	return 0
}

func (m *ProductInfo) GetRequestTime() uint32 {
	if m != nil && m.RequestTime != nil {
		return *m.RequestTime
	}
	return 0
}

func (m *ProductInfo) GetStartFcodeType() uint32 {
	if m != nil && m.StartFcodeType != nil {
		return *m.StartFcodeType
	}
	return 0
}

func (m *ProductInfo) GetScene() CdtScene {
	if m != nil && m.Scene != nil {
		return *m.Scene
	}
	return CdtScene_Default
}

type FChannelInfo struct {
	FulfillmentChannelId *string  `protobuf:"bytes,1,req,name=fulfillment_channel_id,json=fulfillmentChannelId" json:"fulfillment_channel_id,omitempty"`
	IsCb                 *int32   `protobuf:"varint,2,req,name=is_cb,json=isCb" json:"is_cb,omitempty"`
	IsSiteLine           *int32   `protobuf:"varint,3,req,name=is_site_line,json=isSiteLine" json:"is_site_line,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FChannelInfo) Reset()         { *m = FChannelInfo{} }
func (m *FChannelInfo) String() string { return proto.CompactTextString(m) }
func (*FChannelInfo) ProtoMessage()    {}
func (*FChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{4}
}

func (m *FChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FChannelInfo.Unmarshal(m, b)
}
func (m *FChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FChannelInfo.Marshal(b, m, deterministic)
}
func (m *FChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FChannelInfo.Merge(m, src)
}
func (m *FChannelInfo) XXX_Size() int {
	return xxx_messageInfo_FChannelInfo.Size(m)
}
func (m *FChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FChannelInfo proto.InternalMessageInfo

func (m *FChannelInfo) GetFulfillmentChannelId() string {
	if m != nil && m.FulfillmentChannelId != nil {
		return *m.FulfillmentChannelId
	}
	return ""
}

func (m *FChannelInfo) GetIsCb() int32 {
	if m != nil && m.IsCb != nil {
		return *m.IsCb
	}
	return 0
}

func (m *FChannelInfo) GetIsSiteLine() int32 {
	if m != nil && m.IsSiteLine != nil {
		return *m.IsSiteLine
	}
	return 0
}

type Error struct {
	Retcode              *int32   `protobuf:"varint,1,req,name=retcode" json:"retcode,omitempty"`
	Message              *string  `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{5}
}

func (m *Error) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Error.Unmarshal(m, b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Error.Marshal(b, m, deterministic)
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return xxx_messageInfo_Error.Size(m)
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *Error) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

type CdtReply struct {
	QueryId              *string                     `protobuf:"bytes,1,req,name=query_id,json=queryId" json:"query_id,omitempty"`
	CdtMin               *float64                    `protobuf:"fixed64,2,opt,name=cdt_min,json=cdtMin" json:"cdt_min,omitempty"`
	CdtMax               *float64                    `protobuf:"fixed64,3,opt,name=cdt_max,json=cdtMax" json:"cdt_max,omitempty"`
	CbLmMax              *float64                    `protobuf:"fixed64,4,opt,name=cb_lm_max,json=cbLmMax" json:"cb_lm_max,omitempty"`
	OverrideType         *string                     `protobuf:"bytes,5,opt,name=override_type,json=overrideType" json:"override_type,omitempty"`
	Error                *Error                      `protobuf:"bytes,6,opt,name=error" json:"error,omitempty"`
	ItemId               *uint64                     `protobuf:"varint,7,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	FchannelCdtAndVolume []*FChannelCdtAndVolumeInfo `protobuf:"bytes,8,rep,name=fchannel_cdt_and_volume,json=fchannelCdtAndVolume" json:"fchannel_cdt_and_volume,omitempty"`
	DayAndTime           []*DayAndTime               `protobuf:"bytes,9,rep,name=day_and_time,json=dayAndTime" json:"day_and_time,omitempty"`
	RequestUpdateEvent   *uint32                     `protobuf:"varint,10,opt,name=request_update_event,json=requestUpdateEvent" json:"request_update_event,omitempty"`
	StartDay             *uint32                     `protobuf:"varint,20,opt,name=start_day,json=startDay" json:"start_day,omitempty"`
	StartHour            *uint32                     `protobuf:"varint,21,opt,name=start_hour,json=startHour" json:"start_hour,omitempty"`
	EndDay               *uint32                     `protobuf:"varint,22,opt,name=end_day,json=endDay" json:"end_day,omitempty"`
	EndHour              *uint32                     `protobuf:"varint,23,opt,name=end_hour,json=endHour" json:"end_hour,omitempty"`
	GroupTag             *uint32                     `protobuf:"varint,24,opt,name=group_tag,json=groupTag" json:"group_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CdtReply) Reset()         { *m = CdtReply{} }
func (m *CdtReply) String() string { return proto.CompactTextString(m) }
func (*CdtReply) ProtoMessage()    {}
func (*CdtReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{6}
}

func (m *CdtReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CdtReply.Unmarshal(m, b)
}
func (m *CdtReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CdtReply.Marshal(b, m, deterministic)
}
func (m *CdtReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CdtReply.Merge(m, src)
}
func (m *CdtReply) XXX_Size() int {
	return xxx_messageInfo_CdtReply.Size(m)
}
func (m *CdtReply) XXX_DiscardUnknown() {
	xxx_messageInfo_CdtReply.DiscardUnknown(m)
}

var xxx_messageInfo_CdtReply proto.InternalMessageInfo

func (m *CdtReply) GetQueryId() string {
	if m != nil && m.QueryId != nil {
		return *m.QueryId
	}
	return ""
}

func (m *CdtReply) GetCdtMin() float64 {
	if m != nil && m.CdtMin != nil {
		return *m.CdtMin
	}
	return 0
}

func (m *CdtReply) GetCdtMax() float64 {
	if m != nil && m.CdtMax != nil {
		return *m.CdtMax
	}
	return 0
}

func (m *CdtReply) GetCbLmMax() float64 {
	if m != nil && m.CbLmMax != nil {
		return *m.CbLmMax
	}
	return 0
}

func (m *CdtReply) GetOverrideType() string {
	if m != nil && m.OverrideType != nil {
		return *m.OverrideType
	}
	return ""
}

func (m *CdtReply) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *CdtReply) GetItemId() uint64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *CdtReply) GetFchannelCdtAndVolume() []*FChannelCdtAndVolumeInfo {
	if m != nil {
		return m.FchannelCdtAndVolume
	}
	return nil
}

func (m *CdtReply) GetDayAndTime() []*DayAndTime {
	if m != nil {
		return m.DayAndTime
	}
	return nil
}

func (m *CdtReply) GetRequestUpdateEvent() uint32 {
	if m != nil && m.RequestUpdateEvent != nil {
		return *m.RequestUpdateEvent
	}
	return 0
}

func (m *CdtReply) GetStartDay() uint32 {
	if m != nil && m.StartDay != nil {
		return *m.StartDay
	}
	return 0
}

func (m *CdtReply) GetStartHour() uint32 {
	if m != nil && m.StartHour != nil {
		return *m.StartHour
	}
	return 0
}

func (m *CdtReply) GetEndDay() uint32 {
	if m != nil && m.EndDay != nil {
		return *m.EndDay
	}
	return 0
}

func (m *CdtReply) GetEndHour() uint32 {
	if m != nil && m.EndHour != nil {
		return *m.EndHour
	}
	return 0
}

func (m *CdtReply) GetGroupTag() uint32 {
	if m != nil && m.GroupTag != nil {
		return *m.GroupTag
	}
	return 0
}

type FChannelCdtAndVolumeInfo struct {
	Retcode              *int32                                 `protobuf:"varint,1,req,name=retcode" json:"retcode,omitempty"`
	Message              *string                                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	FulfillmentChannelId *string                                `protobuf:"bytes,3,opt,name=fulfillment_channel_id,json=fulfillmentChannelId" json:"fulfillment_channel_id,omitempty"`
	CdtInfos             []*AllLevelCdtInfoWithLocationLevel    `protobuf:"bytes,4,rep,name=cdt_infos,json=cdtInfos" json:"cdt_infos,omitempty"`
	VolumeInfos          []*AllLevelVolumeInfoWithLocationLevel `protobuf:"bytes,5,rep,name=volume_infos,json=volumeInfos" json:"volume_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *FChannelCdtAndVolumeInfo) Reset()         { *m = FChannelCdtAndVolumeInfo{} }
func (m *FChannelCdtAndVolumeInfo) String() string { return proto.CompactTextString(m) }
func (*FChannelCdtAndVolumeInfo) ProtoMessage()    {}
func (*FChannelCdtAndVolumeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{7}
}

func (m *FChannelCdtAndVolumeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FChannelCdtAndVolumeInfo.Unmarshal(m, b)
}
func (m *FChannelCdtAndVolumeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FChannelCdtAndVolumeInfo.Marshal(b, m, deterministic)
}
func (m *FChannelCdtAndVolumeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FChannelCdtAndVolumeInfo.Merge(m, src)
}
func (m *FChannelCdtAndVolumeInfo) XXX_Size() int {
	return xxx_messageInfo_FChannelCdtAndVolumeInfo.Size(m)
}
func (m *FChannelCdtAndVolumeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FChannelCdtAndVolumeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FChannelCdtAndVolumeInfo proto.InternalMessageInfo

func (m *FChannelCdtAndVolumeInfo) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *FChannelCdtAndVolumeInfo) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *FChannelCdtAndVolumeInfo) GetFulfillmentChannelId() string {
	if m != nil && m.FulfillmentChannelId != nil {
		return *m.FulfillmentChannelId
	}
	return ""
}

func (m *FChannelCdtAndVolumeInfo) GetCdtInfos() []*AllLevelCdtInfoWithLocationLevel {
	if m != nil {
		return m.CdtInfos
	}
	return nil
}

func (m *FChannelCdtAndVolumeInfo) GetVolumeInfos() []*AllLevelVolumeInfoWithLocationLevel {
	if m != nil {
		return m.VolumeInfos
	}
	return nil
}

type AllLevelCdtInfoWithLocationLevel struct {
	CdtMin               *float64              `protobuf:"fixed64,1,opt,name=cdt_min,json=cdtMin" json:"cdt_min,omitempty"`
	CdtMax               *float64              `protobuf:"fixed64,2,opt,name=cdt_max,json=cdtMax" json:"cdt_max,omitempty"`
	OriginLocationLevel  *CdtLocationLevelEnum `protobuf:"varint,3,opt,name=origin_location_level,json=originLocationLevel,enum=lcos_protobuf.CdtLocationLevelEnum" json:"origin_location_level,omitempty"`
	DestLocationLevel    *CdtLocationLevelEnum `protobuf:"varint,4,opt,name=dest_location_level,json=destLocationLevel,enum=lcos_protobuf.CdtLocationLevelEnum" json:"dest_location_level,omitempty"`
	DayAndTimeList       []*DayAndTime         `protobuf:"bytes,5,rep,name=day_and_time_list,json=dayAndTimeList" json:"day_and_time_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AllLevelCdtInfoWithLocationLevel) Reset()         { *m = AllLevelCdtInfoWithLocationLevel{} }
func (m *AllLevelCdtInfoWithLocationLevel) String() string { return proto.CompactTextString(m) }
func (*AllLevelCdtInfoWithLocationLevel) ProtoMessage()    {}
func (*AllLevelCdtInfoWithLocationLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{8}
}

func (m *AllLevelCdtInfoWithLocationLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllLevelCdtInfoWithLocationLevel.Unmarshal(m, b)
}
func (m *AllLevelCdtInfoWithLocationLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllLevelCdtInfoWithLocationLevel.Marshal(b, m, deterministic)
}
func (m *AllLevelCdtInfoWithLocationLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllLevelCdtInfoWithLocationLevel.Merge(m, src)
}
func (m *AllLevelCdtInfoWithLocationLevel) XXX_Size() int {
	return xxx_messageInfo_AllLevelCdtInfoWithLocationLevel.Size(m)
}
func (m *AllLevelCdtInfoWithLocationLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_AllLevelCdtInfoWithLocationLevel.DiscardUnknown(m)
}

var xxx_messageInfo_AllLevelCdtInfoWithLocationLevel proto.InternalMessageInfo

func (m *AllLevelCdtInfoWithLocationLevel) GetCdtMin() float64 {
	if m != nil && m.CdtMin != nil {
		return *m.CdtMin
	}
	return 0
}

func (m *AllLevelCdtInfoWithLocationLevel) GetCdtMax() float64 {
	if m != nil && m.CdtMax != nil {
		return *m.CdtMax
	}
	return 0
}

func (m *AllLevelCdtInfoWithLocationLevel) GetOriginLocationLevel() CdtLocationLevelEnum {
	if m != nil && m.OriginLocationLevel != nil {
		return *m.OriginLocationLevel
	}
	return CdtLocationLevelEnum_Country
}

func (m *AllLevelCdtInfoWithLocationLevel) GetDestLocationLevel() CdtLocationLevelEnum {
	if m != nil && m.DestLocationLevel != nil {
		return *m.DestLocationLevel
	}
	return CdtLocationLevelEnum_Country
}

func (m *AllLevelCdtInfoWithLocationLevel) GetDayAndTimeList() []*DayAndTime {
	if m != nil {
		return m.DayAndTimeList
	}
	return nil
}

type AllLevelVolumeInfoWithLocationLevel struct {
	Volume               *uint64               `protobuf:"varint,1,opt,name=volume" json:"volume,omitempty"`
	OriginLocationLevel  *CdtLocationLevelEnum `protobuf:"varint,2,opt,name=origin_location_level,json=originLocationLevel,enum=lcos_protobuf.CdtLocationLevelEnum" json:"origin_location_level,omitempty"`
	DestLocationLevel    *CdtLocationLevelEnum `protobuf:"varint,3,opt,name=dest_location_level,json=destLocationLevel,enum=lcos_protobuf.CdtLocationLevelEnum" json:"dest_location_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AllLevelVolumeInfoWithLocationLevel) Reset()         { *m = AllLevelVolumeInfoWithLocationLevel{} }
func (m *AllLevelVolumeInfoWithLocationLevel) String() string { return proto.CompactTextString(m) }
func (*AllLevelVolumeInfoWithLocationLevel) ProtoMessage()    {}
func (*AllLevelVolumeInfoWithLocationLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{9}
}

func (m *AllLevelVolumeInfoWithLocationLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllLevelVolumeInfoWithLocationLevel.Unmarshal(m, b)
}
func (m *AllLevelVolumeInfoWithLocationLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllLevelVolumeInfoWithLocationLevel.Marshal(b, m, deterministic)
}
func (m *AllLevelVolumeInfoWithLocationLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllLevelVolumeInfoWithLocationLevel.Merge(m, src)
}
func (m *AllLevelVolumeInfoWithLocationLevel) XXX_Size() int {
	return xxx_messageInfo_AllLevelVolumeInfoWithLocationLevel.Size(m)
}
func (m *AllLevelVolumeInfoWithLocationLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_AllLevelVolumeInfoWithLocationLevel.DiscardUnknown(m)
}

var xxx_messageInfo_AllLevelVolumeInfoWithLocationLevel proto.InternalMessageInfo

func (m *AllLevelVolumeInfoWithLocationLevel) GetVolume() uint64 {
	if m != nil && m.Volume != nil {
		return *m.Volume
	}
	return 0
}

func (m *AllLevelVolumeInfoWithLocationLevel) GetOriginLocationLevel() CdtLocationLevelEnum {
	if m != nil && m.OriginLocationLevel != nil {
		return *m.OriginLocationLevel
	}
	return CdtLocationLevelEnum_Country
}

func (m *AllLevelVolumeInfoWithLocationLevel) GetDestLocationLevel() CdtLocationLevelEnum {
	if m != nil && m.DestLocationLevel != nil {
		return *m.DestLocationLevel
	}
	return CdtLocationLevelEnum_Country
}

type DayAndTime struct {
	Day                  *uint32  `protobuf:"varint,1,req,name=day" json:"day,omitempty"`
	StartHour            *uint32  `protobuf:"varint,4,req,name=start_hour,json=startHour" json:"start_hour,omitempty"`
	EndHour              *uint32  `protobuf:"varint,5,req,name=end_hour,json=endHour" json:"end_hour,omitempty"`
	CdtMin               *float64 `protobuf:"fixed64,6,req,name=cdt_min,json=cdtMin" json:"cdt_min,omitempty"`
	CdtMax               *float64 `protobuf:"fixed64,7,req,name=cdt_max,json=cdtMax" json:"cdt_max,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayAndTime) Reset()         { *m = DayAndTime{} }
func (m *DayAndTime) String() string { return proto.CompactTextString(m) }
func (*DayAndTime) ProtoMessage()    {}
func (*DayAndTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{10}
}

func (m *DayAndTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayAndTime.Unmarshal(m, b)
}
func (m *DayAndTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayAndTime.Marshal(b, m, deterministic)
}
func (m *DayAndTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayAndTime.Merge(m, src)
}
func (m *DayAndTime) XXX_Size() int {
	return xxx_messageInfo_DayAndTime.Size(m)
}
func (m *DayAndTime) XXX_DiscardUnknown() {
	xxx_messageInfo_DayAndTime.DiscardUnknown(m)
}

var xxx_messageInfo_DayAndTime proto.InternalMessageInfo

func (m *DayAndTime) GetDay() uint32 {
	if m != nil && m.Day != nil {
		return *m.Day
	}
	return 0
}

func (m *DayAndTime) GetStartHour() uint32 {
	if m != nil && m.StartHour != nil {
		return *m.StartHour
	}
	return 0
}

func (m *DayAndTime) GetEndHour() uint32 {
	if m != nil && m.EndHour != nil {
		return *m.EndHour
	}
	return 0
}

func (m *DayAndTime) GetCdtMin() float64 {
	if m != nil && m.CdtMin != nil {
		return *m.CdtMin
	}
	return 0
}

func (m *DayAndTime) GetCdtMax() float64 {
	if m != nil && m.CdtMax != nil {
		return *m.CdtMax
	}
	return 0
}

type QueryCdtInfoRequest struct {
	ReqHeader            *ReqHeader     `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductInfo          []*ProductInfo `protobuf:"bytes,2,rep,name=product_info,json=productInfo" json:"product_info,omitempty"`
	ObjectType           *uint32        `protobuf:"varint,3,opt,name=object_type,json=objectType" json:"object_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *QueryCdtInfoRequest) Reset()         { *m = QueryCdtInfoRequest{} }
func (m *QueryCdtInfoRequest) String() string { return proto.CompactTextString(m) }
func (*QueryCdtInfoRequest) ProtoMessage()    {}
func (*QueryCdtInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{11}
}

func (m *QueryCdtInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCdtInfoRequest.Unmarshal(m, b)
}
func (m *QueryCdtInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCdtInfoRequest.Marshal(b, m, deterministic)
}
func (m *QueryCdtInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCdtInfoRequest.Merge(m, src)
}
func (m *QueryCdtInfoRequest) XXX_Size() int {
	return xxx_messageInfo_QueryCdtInfoRequest.Size(m)
}
func (m *QueryCdtInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCdtInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCdtInfoRequest proto.InternalMessageInfo

func (m *QueryCdtInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *QueryCdtInfoRequest) GetProductInfo() []*ProductInfo {
	if m != nil {
		return m.ProductInfo
	}
	return nil
}

func (m *QueryCdtInfoRequest) GetObjectType() uint32 {
	if m != nil && m.ObjectType != nil {
		return *m.ObjectType
	}
	return 0
}

type QueryCdtInfoForTrackingRequest struct {
	ReqHeader            *ReqHeader   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductInfo          *ProductInfo `protobuf:"bytes,2,req,name=product_info,json=productInfo" json:"product_info,omitempty"`
	CbLmInboundDate      *uint32      `protobuf:"varint,3,req,name=cb_lm_inbound_date,json=cbLmInboundDate" json:"cb_lm_inbound_date,omitempty"`
	ForderId             *uint64      `protobuf:"varint,4,opt,name=forder_id,json=forderId" json:"forder_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QueryCdtInfoForTrackingRequest) Reset()         { *m = QueryCdtInfoForTrackingRequest{} }
func (m *QueryCdtInfoForTrackingRequest) String() string { return proto.CompactTextString(m) }
func (*QueryCdtInfoForTrackingRequest) ProtoMessage()    {}
func (*QueryCdtInfoForTrackingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{12}
}

func (m *QueryCdtInfoForTrackingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCdtInfoForTrackingRequest.Unmarshal(m, b)
}
func (m *QueryCdtInfoForTrackingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCdtInfoForTrackingRequest.Marshal(b, m, deterministic)
}
func (m *QueryCdtInfoForTrackingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCdtInfoForTrackingRequest.Merge(m, src)
}
func (m *QueryCdtInfoForTrackingRequest) XXX_Size() int {
	return xxx_messageInfo_QueryCdtInfoForTrackingRequest.Size(m)
}
func (m *QueryCdtInfoForTrackingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCdtInfoForTrackingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCdtInfoForTrackingRequest proto.InternalMessageInfo

func (m *QueryCdtInfoForTrackingRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *QueryCdtInfoForTrackingRequest) GetProductInfo() *ProductInfo {
	if m != nil {
		return m.ProductInfo
	}
	return nil
}

func (m *QueryCdtInfoForTrackingRequest) GetCbLmInboundDate() uint32 {
	if m != nil && m.CbLmInboundDate != nil {
		return *m.CbLmInboundDate
	}
	return 0
}

func (m *QueryCdtInfoForTrackingRequest) GetForderId() uint64 {
	if m != nil && m.ForderId != nil {
		return *m.ForderId
	}
	return 0
}

type QueryCdtInfoRequestResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CdtReplyList         []*CdtReply `protobuf:"bytes,2,rep,name=cdt_reply_list,json=cdtReplyList" json:"cdt_reply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *QueryCdtInfoRequestResponse) Reset()         { *m = QueryCdtInfoRequestResponse{} }
func (m *QueryCdtInfoRequestResponse) String() string { return proto.CompactTextString(m) }
func (*QueryCdtInfoRequestResponse) ProtoMessage()    {}
func (*QueryCdtInfoRequestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{13}
}

func (m *QueryCdtInfoRequestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCdtInfoRequestResponse.Unmarshal(m, b)
}
func (m *QueryCdtInfoRequestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCdtInfoRequestResponse.Marshal(b, m, deterministic)
}
func (m *QueryCdtInfoRequestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCdtInfoRequestResponse.Merge(m, src)
}
func (m *QueryCdtInfoRequestResponse) XXX_Size() int {
	return xxx_messageInfo_QueryCdtInfoRequestResponse.Size(m)
}
func (m *QueryCdtInfoRequestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCdtInfoRequestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCdtInfoRequestResponse proto.InternalMessageInfo

func (m *QueryCdtInfoRequestResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *QueryCdtInfoRequestResponse) GetCdtReplyList() []*CdtReply {
	if m != nil {
		return m.CdtReplyList
	}
	return nil
}

type QueryCdtInfoForTrackingResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CbLmCbMax            *float64    `protobuf:"fixed64,2,opt,name=cb_lm_cb_max,json=cbLmCbMax" json:"cb_lm_cb_max,omitempty"`
	CbLmHolidayExt       *float64    `protobuf:"fixed64,3,opt,name=cb_lm_holiday_ext,json=cbLmHolidayExt" json:"cb_lm_holiday_ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *QueryCdtInfoForTrackingResponse) Reset()         { *m = QueryCdtInfoForTrackingResponse{} }
func (m *QueryCdtInfoForTrackingResponse) String() string { return proto.CompactTextString(m) }
func (*QueryCdtInfoForTrackingResponse) ProtoMessage()    {}
func (*QueryCdtInfoForTrackingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{14}
}

func (m *QueryCdtInfoForTrackingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCdtInfoForTrackingResponse.Unmarshal(m, b)
}
func (m *QueryCdtInfoForTrackingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCdtInfoForTrackingResponse.Marshal(b, m, deterministic)
}
func (m *QueryCdtInfoForTrackingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCdtInfoForTrackingResponse.Merge(m, src)
}
func (m *QueryCdtInfoForTrackingResponse) XXX_Size() int {
	return xxx_messageInfo_QueryCdtInfoForTrackingResponse.Size(m)
}
func (m *QueryCdtInfoForTrackingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCdtInfoForTrackingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCdtInfoForTrackingResponse proto.InternalMessageInfo

func (m *QueryCdtInfoForTrackingResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *QueryCdtInfoForTrackingResponse) GetCbLmCbMax() float64 {
	if m != nil && m.CbLmCbMax != nil {
		return *m.CbLmCbMax
	}
	return 0
}

func (m *QueryCdtInfoForTrackingResponse) GetCbLmHolidayExt() float64 {
	if m != nil && m.CbLmHolidayExt != nil {
		return *m.CbLmHolidayExt
	}
	return 0
}

type QueryEventCodeRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	IsSiteLine           *uint32    `protobuf:"varint,2,req,name=is_site_line,json=isSiteLine" json:"is_site_line,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *QueryEventCodeRequest) Reset()         { *m = QueryEventCodeRequest{} }
func (m *QueryEventCodeRequest) String() string { return proto.CompactTextString(m) }
func (*QueryEventCodeRequest) ProtoMessage()    {}
func (*QueryEventCodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{15}
}

func (m *QueryEventCodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryEventCodeRequest.Unmarshal(m, b)
}
func (m *QueryEventCodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryEventCodeRequest.Marshal(b, m, deterministic)
}
func (m *QueryEventCodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryEventCodeRequest.Merge(m, src)
}
func (m *QueryEventCodeRequest) XXX_Size() int {
	return xxx_messageInfo_QueryEventCodeRequest.Size(m)
}
func (m *QueryEventCodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryEventCodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryEventCodeRequest proto.InternalMessageInfo

func (m *QueryEventCodeRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *QueryEventCodeRequest) GetIsSiteLine() uint32 {
	if m != nil && m.IsSiteLine != nil {
		return *m.IsSiteLine
	}
	return 0
}

type QueryEventCodeResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ActionCodeList       []string    `protobuf:"bytes,2,rep,name=action_code_list,json=actionCodeList" json:"action_code_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *QueryEventCodeResponse) Reset()         { *m = QueryEventCodeResponse{} }
func (m *QueryEventCodeResponse) String() string { return proto.CompactTextString(m) }
func (*QueryEventCodeResponse) ProtoMessage()    {}
func (*QueryEventCodeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{16}
}

func (m *QueryEventCodeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryEventCodeResponse.Unmarshal(m, b)
}
func (m *QueryEventCodeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryEventCodeResponse.Marshal(b, m, deterministic)
}
func (m *QueryEventCodeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryEventCodeResponse.Merge(m, src)
}
func (m *QueryEventCodeResponse) XXX_Size() int {
	return xxx_messageInfo_QueryEventCodeResponse.Size(m)
}
func (m *QueryEventCodeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryEventCodeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryEventCodeResponse proto.InternalMessageInfo

func (m *QueryEventCodeResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *QueryEventCodeResponse) GetActionCodeList() []string {
	if m != nil {
		return m.ActionCodeList
	}
	return nil
}

type CdtQueryInfo struct {
	ProductId                   *string  `protobuf:"bytes,1,opt,name=product_id,json=productId" json:"product_id,omitempty"`
	TplUniqueKey                *string  `protobuf:"bytes,2,opt,name=tpl_unique_key,json=tplUniqueKey" json:"tpl_unique_key,omitempty"`
	IsLm                        *int32   `protobuf:"varint,3,opt,name=is_lm,json=isLm" json:"is_lm,omitempty"`
	IsSiteLine                  *int32   `protobuf:"varint,4,opt,name=is_site_line,json=isSiteLine" json:"is_site_line,omitempty"`
	OriginLocationId            *int32   `protobuf:"varint,5,opt,name=origin_location_id,json=originLocationId" json:"origin_location_id,omitempty"`
	DestinationLocationId       *int32   `protobuf:"varint,6,opt,name=destination_location_id,json=destinationLocationId" json:"destination_location_id,omitempty"`
	DestinationCeprangePostcode *int32   `protobuf:"varint,7,opt,name=destination_ceprange_postcode,json=destinationCeprangePostcode" json:"destination_ceprange_postcode,omitempty"`
	DestinationPostcode         *string  `protobuf:"bytes,8,opt,name=destination_postcode,json=destinationPostcode" json:"destination_postcode,omitempty"`
	QueryType                   *string  `protobuf:"bytes,9,opt,name=query_type,json=queryType" json:"query_type,omitempty"`
	LaneCode                    *string  `protobuf:"bytes,10,opt,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	UpdateEvent                 *string  `protobuf:"bytes,11,opt,name=update_event,json=updateEvent" json:"update_event,omitempty"`
	OriginLocationLevel         *int32   `protobuf:"varint,12,opt,name=origin_location_level,json=originLocationLevel" json:"origin_location_level,omitempty"`
	DestinationLocationLevel    *int32   `protobuf:"varint,13,opt,name=destination_location_level,json=destinationLocationLevel" json:"destination_location_level,omitempty"`
	XXX_NoUnkeyedLiteral        struct{} `json:"-"`
	XXX_unrecognized            []byte   `json:"-"`
	XXX_sizecache               int32    `json:"-"`
}

func (m *CdtQueryInfo) Reset()         { *m = CdtQueryInfo{} }
func (m *CdtQueryInfo) String() string { return proto.CompactTextString(m) }
func (*CdtQueryInfo) ProtoMessage()    {}
func (*CdtQueryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{17}
}

func (m *CdtQueryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CdtQueryInfo.Unmarshal(m, b)
}
func (m *CdtQueryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CdtQueryInfo.Marshal(b, m, deterministic)
}
func (m *CdtQueryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CdtQueryInfo.Merge(m, src)
}
func (m *CdtQueryInfo) XXX_Size() int {
	return xxx_messageInfo_CdtQueryInfo.Size(m)
}
func (m *CdtQueryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CdtQueryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CdtQueryInfo proto.InternalMessageInfo

func (m *CdtQueryInfo) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *CdtQueryInfo) GetTplUniqueKey() string {
	if m != nil && m.TplUniqueKey != nil {
		return *m.TplUniqueKey
	}
	return ""
}

func (m *CdtQueryInfo) GetIsLm() int32 {
	if m != nil && m.IsLm != nil {
		return *m.IsLm
	}
	return 0
}

func (m *CdtQueryInfo) GetIsSiteLine() int32 {
	if m != nil && m.IsSiteLine != nil {
		return *m.IsSiteLine
	}
	return 0
}

func (m *CdtQueryInfo) GetOriginLocationId() int32 {
	if m != nil && m.OriginLocationId != nil {
		return *m.OriginLocationId
	}
	return 0
}

func (m *CdtQueryInfo) GetDestinationLocationId() int32 {
	if m != nil && m.DestinationLocationId != nil {
		return *m.DestinationLocationId
	}
	return 0
}

func (m *CdtQueryInfo) GetDestinationCeprangePostcode() int32 {
	if m != nil && m.DestinationCeprangePostcode != nil {
		return *m.DestinationCeprangePostcode
	}
	return 0
}

func (m *CdtQueryInfo) GetDestinationPostcode() string {
	if m != nil && m.DestinationPostcode != nil {
		return *m.DestinationPostcode
	}
	return ""
}

func (m *CdtQueryInfo) GetQueryType() string {
	if m != nil && m.QueryType != nil {
		return *m.QueryType
	}
	return ""
}

func (m *CdtQueryInfo) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *CdtQueryInfo) GetUpdateEvent() string {
	if m != nil && m.UpdateEvent != nil {
		return *m.UpdateEvent
	}
	return ""
}

func (m *CdtQueryInfo) GetOriginLocationLevel() int32 {
	if m != nil && m.OriginLocationLevel != nil {
		return *m.OriginLocationLevel
	}
	return 0
}

func (m *CdtQueryInfo) GetDestinationLocationLevel() int32 {
	if m != nil && m.DestinationLocationLevel != nil {
		return *m.DestinationLocationLevel
	}
	return 0
}

// mid result for auto update rule
type AutoUpdateProcess struct {
	AutoUpdateRuleId     *uint64  `protobuf:"varint,1,opt,name=auto_update_rule_id,json=autoUpdateRuleId" json:"auto_update_rule_id,omitempty"`
	CdtMin               *float64 `protobuf:"fixed64,2,opt,name=cdt_min,json=cdtMin" json:"cdt_min,omitempty"`
	CdtMax               *float64 `protobuf:"fixed64,3,opt,name=cdt_max,json=cdtMax" json:"cdt_max,omitempty"`
	LmCdtMax             *float64 `protobuf:"fixed64,4,opt,name=lm_cdt_max,json=lmCdtMax" json:"lm_cdt_max,omitempty"`
	CdtVersion           *int64   `protobuf:"varint,5,opt,name=cdt_version,json=cdtVersion" json:"cdt_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AutoUpdateProcess) Reset()         { *m = AutoUpdateProcess{} }
func (m *AutoUpdateProcess) String() string { return proto.CompactTextString(m) }
func (*AutoUpdateProcess) ProtoMessage()    {}
func (*AutoUpdateProcess) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{18}
}

func (m *AutoUpdateProcess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoUpdateProcess.Unmarshal(m, b)
}
func (m *AutoUpdateProcess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoUpdateProcess.Marshal(b, m, deterministic)
}
func (m *AutoUpdateProcess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoUpdateProcess.Merge(m, src)
}
func (m *AutoUpdateProcess) XXX_Size() int {
	return xxx_messageInfo_AutoUpdateProcess.Size(m)
}
func (m *AutoUpdateProcess) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoUpdateProcess.DiscardUnknown(m)
}

var xxx_messageInfo_AutoUpdateProcess proto.InternalMessageInfo

func (m *AutoUpdateProcess) GetAutoUpdateRuleId() uint64 {
	if m != nil && m.AutoUpdateRuleId != nil {
		return *m.AutoUpdateRuleId
	}
	return 0
}

func (m *AutoUpdateProcess) GetCdtMin() float64 {
	if m != nil && m.CdtMin != nil {
		return *m.CdtMin
	}
	return 0
}

func (m *AutoUpdateProcess) GetCdtMax() float64 {
	if m != nil && m.CdtMax != nil {
		return *m.CdtMax
	}
	return 0
}

func (m *AutoUpdateProcess) GetLmCdtMax() float64 {
	if m != nil && m.LmCdtMax != nil {
		return *m.LmCdtMax
	}
	return 0
}

func (m *AutoUpdateProcess) GetCdtVersion() int64 {
	if m != nil && m.CdtVersion != nil {
		return *m.CdtVersion
	}
	return 0
}

// mid result for manual update rule
type ManualUpdateProcess struct {
	CdtMin               *float64 `protobuf:"fixed64,1,opt,name=cdt_min,json=cdtMin" json:"cdt_min,omitempty"`
	CdtMax               *float64 `protobuf:"fixed64,2,opt,name=cdt_max,json=cdtMax" json:"cdt_max,omitempty"`
	LmCdtMax             *float64 `protobuf:"fixed64,3,opt,name=lm_cdt_max,json=lmCdtMax" json:"lm_cdt_max,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualUpdateProcess) Reset()         { *m = ManualUpdateProcess{} }
func (m *ManualUpdateProcess) String() string { return proto.CompactTextString(m) }
func (*ManualUpdateProcess) ProtoMessage()    {}
func (*ManualUpdateProcess) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{19}
}

func (m *ManualUpdateProcess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualUpdateProcess.Unmarshal(m, b)
}
func (m *ManualUpdateProcess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualUpdateProcess.Marshal(b, m, deterministic)
}
func (m *ManualUpdateProcess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualUpdateProcess.Merge(m, src)
}
func (m *ManualUpdateProcess) XXX_Size() int {
	return xxx_messageInfo_ManualUpdateProcess.Size(m)
}
func (m *ManualUpdateProcess) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualUpdateProcess.DiscardUnknown(m)
}

var xxx_messageInfo_ManualUpdateProcess proto.InternalMessageInfo

func (m *ManualUpdateProcess) GetCdtMin() float64 {
	if m != nil && m.CdtMin != nil {
		return *m.CdtMin
	}
	return 0
}

func (m *ManualUpdateProcess) GetCdtMax() float64 {
	if m != nil && m.CdtMax != nil {
		return *m.CdtMax
	}
	return 0
}

func (m *ManualUpdateProcess) GetLmCdtMax() float64 {
	if m != nil && m.LmCdtMax != nil {
		return *m.LmCdtMax
	}
	return 0
}

type ManualManipulationProcess struct {
	CdtMinDelta          *float64 `protobuf:"fixed64,1,opt,name=cdt_min_delta,json=cdtMinDelta" json:"cdt_min_delta,omitempty"`
	CdtMaxDelta          *float64 `protobuf:"fixed64,2,opt,name=cdt_max_delta,json=cdtMaxDelta" json:"cdt_max_delta,omitempty"`
	CbLmDelta            *float64 `protobuf:"fixed64,3,opt,name=cb_lm_delta,json=cbLmDelta" json:"cb_lm_delta,omitempty"`
	RecordId             *int64   `protobuf:"varint,4,opt,name=record_id,json=recordId" json:"record_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualManipulationProcess) Reset()         { *m = ManualManipulationProcess{} }
func (m *ManualManipulationProcess) String() string { return proto.CompactTextString(m) }
func (*ManualManipulationProcess) ProtoMessage()    {}
func (*ManualManipulationProcess) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{20}
}

func (m *ManualManipulationProcess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualManipulationProcess.Unmarshal(m, b)
}
func (m *ManualManipulationProcess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualManipulationProcess.Marshal(b, m, deterministic)
}
func (m *ManualManipulationProcess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualManipulationProcess.Merge(m, src)
}
func (m *ManualManipulationProcess) XXX_Size() int {
	return xxx_messageInfo_ManualManipulationProcess.Size(m)
}
func (m *ManualManipulationProcess) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualManipulationProcess.DiscardUnknown(m)
}

var xxx_messageInfo_ManualManipulationProcess proto.InternalMessageInfo

func (m *ManualManipulationProcess) GetCdtMinDelta() float64 {
	if m != nil && m.CdtMinDelta != nil {
		return *m.CdtMinDelta
	}
	return 0
}

func (m *ManualManipulationProcess) GetCdtMaxDelta() float64 {
	if m != nil && m.CdtMaxDelta != nil {
		return *m.CdtMaxDelta
	}
	return 0
}

func (m *ManualManipulationProcess) GetCbLmDelta() float64 {
	if m != nil && m.CbLmDelta != nil {
		return *m.CbLmDelta
	}
	return 0
}

func (m *ManualManipulationProcess) GetRecordId() int64 {
	if m != nil && m.RecordId != nil {
		return *m.RecordId
	}
	return 0
}

type CDTProcess struct {
	CdtQueryInfo                *CdtQueryInfo              `protobuf:"bytes,1,opt,name=cdt_query_info,json=cdtQueryInfo" json:"cdt_query_info,omitempty"`
	ManualManipulationQueryInfo *CdtQueryInfo              `protobuf:"bytes,2,opt,name=manual_manipulation_query_info,json=manualManipulationQueryInfo" json:"manual_manipulation_query_info,omitempty"`
	AutoUpdateProcess           *AutoUpdateProcess         `protobuf:"bytes,3,opt,name=auto_update_process,json=autoUpdateProcess" json:"auto_update_process,omitempty"`
	ManualUpdateProcess         *ManualUpdateProcess       `protobuf:"bytes,4,opt,name=manual_update_process,json=manualUpdateProcess" json:"manual_update_process,omitempty"`
	ManualManipulationProcess   *ManualManipulationProcess `protobuf:"bytes,5,opt,name=manual_manipulation_process,json=manualManipulationProcess" json:"manual_manipulation_process,omitempty"`
	GroupTag                    *uint32                    `protobuf:"varint,6,opt,name=group_tag,json=groupTag" json:"group_tag,omitempty"`
	XXX_NoUnkeyedLiteral        struct{}                   `json:"-"`
	XXX_unrecognized            []byte                     `json:"-"`
	XXX_sizecache               int32                      `json:"-"`
}

func (m *CDTProcess) Reset()         { *m = CDTProcess{} }
func (m *CDTProcess) String() string { return proto.CompactTextString(m) }
func (*CDTProcess) ProtoMessage()    {}
func (*CDTProcess) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{21}
}

func (m *CDTProcess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CDTProcess.Unmarshal(m, b)
}
func (m *CDTProcess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CDTProcess.Marshal(b, m, deterministic)
}
func (m *CDTProcess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CDTProcess.Merge(m, src)
}
func (m *CDTProcess) XXX_Size() int {
	return xxx_messageInfo_CDTProcess.Size(m)
}
func (m *CDTProcess) XXX_DiscardUnknown() {
	xxx_messageInfo_CDTProcess.DiscardUnknown(m)
}

var xxx_messageInfo_CDTProcess proto.InternalMessageInfo

func (m *CDTProcess) GetCdtQueryInfo() *CdtQueryInfo {
	if m != nil {
		return m.CdtQueryInfo
	}
	return nil
}

func (m *CDTProcess) GetManualManipulationQueryInfo() *CdtQueryInfo {
	if m != nil {
		return m.ManualManipulationQueryInfo
	}
	return nil
}

func (m *CDTProcess) GetAutoUpdateProcess() *AutoUpdateProcess {
	if m != nil {
		return m.AutoUpdateProcess
	}
	return nil
}

func (m *CDTProcess) GetManualUpdateProcess() *ManualUpdateProcess {
	if m != nil {
		return m.ManualUpdateProcess
	}
	return nil
}

func (m *CDTProcess) GetManualManipulationProcess() *ManualManipulationProcess {
	if m != nil {
		return m.ManualManipulationProcess
	}
	return nil
}

func (m *CDTProcess) GetGroupTag() uint32 {
	if m != nil && m.GroupTag != nil {
		return *m.GroupTag
	}
	return 0
}

type NonWorkingDayProcess struct {
	EddMinNwdList        []string `protobuf:"bytes,1,rep,name=edd_min_nwd_list,json=eddMinNwdList" json:"edd_min_nwd_list,omitempty"`
	EddMaxNwdList        []string `protobuf:"bytes,2,rep,name=edd_max_nwd_list,json=eddMaxNwdList" json:"edd_max_nwd_list,omitempty"`
	LineIdList           []string `protobuf:"bytes,3,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonWorkingDayProcess) Reset()         { *m = NonWorkingDayProcess{} }
func (m *NonWorkingDayProcess) String() string { return proto.CompactTextString(m) }
func (*NonWorkingDayProcess) ProtoMessage()    {}
func (*NonWorkingDayProcess) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{22}
}

func (m *NonWorkingDayProcess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonWorkingDayProcess.Unmarshal(m, b)
}
func (m *NonWorkingDayProcess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonWorkingDayProcess.Marshal(b, m, deterministic)
}
func (m *NonWorkingDayProcess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonWorkingDayProcess.Merge(m, src)
}
func (m *NonWorkingDayProcess) XXX_Size() int {
	return xxx_messageInfo_NonWorkingDayProcess.Size(m)
}
func (m *NonWorkingDayProcess) XXX_DiscardUnknown() {
	xxx_messageInfo_NonWorkingDayProcess.DiscardUnknown(m)
}

var xxx_messageInfo_NonWorkingDayProcess proto.InternalMessageInfo

func (m *NonWorkingDayProcess) GetEddMinNwdList() []string {
	if m != nil {
		return m.EddMinNwdList
	}
	return nil
}

func (m *NonWorkingDayProcess) GetEddMaxNwdList() []string {
	if m != nil {
		return m.EddMaxNwdList
	}
	return nil
}

func (m *NonWorkingDayProcess) GetLineIdList() []string {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

// mid info when calculating edd
type EDDProcess struct {
	CdtProcess           *CDTProcess           `protobuf:"bytes,1,opt,name=cdt_process,json=cdtProcess" json:"cdt_process,omitempty"`
	NonWorkingDayList    []string              `protobuf:"bytes,2,rep,name=non_working_day_list,json=nonWorkingDayList" json:"non_working_day_list,omitempty"`
	NonWorkingDayProcess *NonWorkingDayProcess `protobuf:"bytes,3,opt,name=non_working_day_process,json=nonWorkingDayProcess" json:"non_working_day_process,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *EDDProcess) Reset()         { *m = EDDProcess{} }
func (m *EDDProcess) String() string { return proto.CompactTextString(m) }
func (*EDDProcess) ProtoMessage()    {}
func (*EDDProcess) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{23}
}

func (m *EDDProcess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EDDProcess.Unmarshal(m, b)
}
func (m *EDDProcess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EDDProcess.Marshal(b, m, deterministic)
}
func (m *EDDProcess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EDDProcess.Merge(m, src)
}
func (m *EDDProcess) XXX_Size() int {
	return xxx_messageInfo_EDDProcess.Size(m)
}
func (m *EDDProcess) XXX_DiscardUnknown() {
	xxx_messageInfo_EDDProcess.DiscardUnknown(m)
}

var xxx_messageInfo_EDDProcess proto.InternalMessageInfo

func (m *EDDProcess) GetCdtProcess() *CDTProcess {
	if m != nil {
		return m.CdtProcess
	}
	return nil
}

func (m *EDDProcess) GetNonWorkingDayList() []string {
	if m != nil {
		return m.NonWorkingDayList
	}
	return nil
}

func (m *EDDProcess) GetNonWorkingDayProcess() *NonWorkingDayProcess {
	if m != nil {
		return m.NonWorkingDayProcess
	}
	return nil
}

type GetEddInfoResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Edd                  *int64      `protobuf:"varint,2,opt,name=edd" json:"edd,omitempty"`
	EddProcess           *EDDProcess `protobuf:"bytes,3,opt,name=edd_process,json=eddProcess" json:"edd_process,omitempty"`
	EddMin               *int64      `protobuf:"varint,4,opt,name=edd_min,json=eddMin" json:"edd_min,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetEddInfoResponse) Reset()         { *m = GetEddInfoResponse{} }
func (m *GetEddInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetEddInfoResponse) ProtoMessage()    {}
func (*GetEddInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{24}
}

func (m *GetEddInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEddInfoResponse.Unmarshal(m, b)
}
func (m *GetEddInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEddInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetEddInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEddInfoResponse.Merge(m, src)
}
func (m *GetEddInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetEddInfoResponse.Size(m)
}
func (m *GetEddInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEddInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEddInfoResponse proto.InternalMessageInfo

func (m *GetEddInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetEddInfoResponse) GetEdd() int64 {
	if m != nil && m.Edd != nil {
		return *m.Edd
	}
	return 0
}

func (m *GetEddInfoResponse) GetEddProcess() *EDDProcess {
	if m != nil {
		return m.EddProcess
	}
	return nil
}

func (m *GetEddInfoResponse) GetEddMin() int64 {
	if m != nil && m.EddMin != nil {
		return *m.EddMin
	}
	return 0
}

type SingleEDDExtension struct {
	UniqueId             *string           `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	ProductId            *string           `protobuf:"bytes,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	StateLocationId      *int32            `protobuf:"varint,3,opt,name=state_location_id,json=stateLocationId" json:"state_location_id,omitempty"`
	Region               *string           `protobuf:"bytes,4,req,name=region" json:"region,omitempty"`
	Adjustment           *int32            `protobuf:"varint,5,req,name=adjustment" json:"adjustment,omitempty"`
	Edd                  *int64            `protobuf:"varint,6,req,name=edd" json:"edd,omitempty"`
	IsLm                 *int32            `protobuf:"varint,7,req,name=is_lm,json=isLm" json:"is_lm,omitempty"`
	IsSitLine            *int32            `protobuf:"varint,8,req,name=is_sit_line,json=isSitLine" json:"is_sit_line,omitempty"`
	LineIdList           []*SimpleLineInfo `protobuf:"bytes,9,rep,name=line_id_list,json=lineIdList" json:"line_id_list,omitempty"`
	SlsTn                *string           `protobuf:"bytes,10,opt,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	EddMinAdjustment     *int32            `protobuf:"varint,11,req,name=edd_min_adjustment,json=eddMinAdjustment" json:"edd_min_adjustment,omitempty"`
	EddMin               *int64            `protobuf:"varint,12,req,name=edd_min,json=eddMin" json:"edd_min,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SingleEDDExtension) Reset()         { *m = SingleEDDExtension{} }
func (m *SingleEDDExtension) String() string { return proto.CompactTextString(m) }
func (*SingleEDDExtension) ProtoMessage()    {}
func (*SingleEDDExtension) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{25}
}

func (m *SingleEDDExtension) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleEDDExtension.Unmarshal(m, b)
}
func (m *SingleEDDExtension) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleEDDExtension.Marshal(b, m, deterministic)
}
func (m *SingleEDDExtension) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleEDDExtension.Merge(m, src)
}
func (m *SingleEDDExtension) XXX_Size() int {
	return xxx_messageInfo_SingleEDDExtension.Size(m)
}
func (m *SingleEDDExtension) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleEDDExtension.DiscardUnknown(m)
}

var xxx_messageInfo_SingleEDDExtension proto.InternalMessageInfo

func (m *SingleEDDExtension) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SingleEDDExtension) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *SingleEDDExtension) GetStateLocationId() int32 {
	if m != nil && m.StateLocationId != nil {
		return *m.StateLocationId
	}
	return 0
}

func (m *SingleEDDExtension) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *SingleEDDExtension) GetAdjustment() int32 {
	if m != nil && m.Adjustment != nil {
		return *m.Adjustment
	}
	return 0
}

func (m *SingleEDDExtension) GetEdd() int64 {
	if m != nil && m.Edd != nil {
		return *m.Edd
	}
	return 0
}

func (m *SingleEDDExtension) GetIsLm() int32 {
	if m != nil && m.IsLm != nil {
		return *m.IsLm
	}
	return 0
}

func (m *SingleEDDExtension) GetIsSitLine() int32 {
	if m != nil && m.IsSitLine != nil {
		return *m.IsSitLine
	}
	return 0
}

func (m *SingleEDDExtension) GetLineIdList() []*SimpleLineInfo {
	if m != nil {
		return m.LineIdList
	}
	return nil
}

func (m *SingleEDDExtension) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

func (m *SingleEDDExtension) GetEddMinAdjustment() int32 {
	if m != nil && m.EddMinAdjustment != nil {
		return *m.EddMinAdjustment
	}
	return 0
}

func (m *SingleEDDExtension) GetEddMin() int64 {
	if m != nil && m.EddMin != nil {
		return *m.EddMin
	}
	return 0
}

type GetExtendedEDDRequest struct {
	ReqHeader            *ReqHeader            `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	EddExtensionList     []*SingleEDDExtension `protobuf:"bytes,2,rep,name=edd_extension_list,json=eddExtensionList" json:"edd_extension_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetExtendedEDDRequest) Reset()         { *m = GetExtendedEDDRequest{} }
func (m *GetExtendedEDDRequest) String() string { return proto.CompactTextString(m) }
func (*GetExtendedEDDRequest) ProtoMessage()    {}
func (*GetExtendedEDDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{26}
}

func (m *GetExtendedEDDRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtendedEDDRequest.Unmarshal(m, b)
}
func (m *GetExtendedEDDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtendedEDDRequest.Marshal(b, m, deterministic)
}
func (m *GetExtendedEDDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtendedEDDRequest.Merge(m, src)
}
func (m *GetExtendedEDDRequest) XXX_Size() int {
	return xxx_messageInfo_GetExtendedEDDRequest.Size(m)
}
func (m *GetExtendedEDDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtendedEDDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtendedEDDRequest proto.InternalMessageInfo

func (m *GetExtendedEDDRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetExtendedEDDRequest) GetEddExtensionList() []*SingleEDDExtension {
	if m != nil {
		return m.EddExtensionList
	}
	return nil
}

type SingleEDDResponse struct {
	ExtendedEdd               *int64                `protobuf:"varint,1,req,name=extended_edd,json=extendedEdd" json:"extended_edd,omitempty"`
	ExtendedNonWorkingDayList []string              `protobuf:"bytes,2,rep,name=extended_non_working_day_list,json=extendedNonWorkingDayList" json:"extended_non_working_day_list,omitempty"`
	ExtendedDays              *int32                `protobuf:"varint,3,opt,name=extended_days,json=extendedDays" json:"extended_days,omitempty"`
	SlsTn                     *string               `protobuf:"bytes,4,opt,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	ExtendedEddMin            *int64                `protobuf:"varint,5,req,name=extended_edd_min,json=extendedEddMin" json:"extended_edd_min,omitempty"`
	NonWorkingDayProcess      *NonWorkingDayProcess `protobuf:"bytes,6,opt,name=non_working_day_process,json=nonWorkingDayProcess" json:"non_working_day_process,omitempty"`
	EddMinExtendedDays        *int32                `protobuf:"varint,7,opt,name=edd_min_extended_days,json=eddMinExtendedDays" json:"edd_min_extended_days,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}              `json:"-"`
	XXX_unrecognized          []byte                `json:"-"`
	XXX_sizecache             int32                 `json:"-"`
}

func (m *SingleEDDResponse) Reset()         { *m = SingleEDDResponse{} }
func (m *SingleEDDResponse) String() string { return proto.CompactTextString(m) }
func (*SingleEDDResponse) ProtoMessage()    {}
func (*SingleEDDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{27}
}

func (m *SingleEDDResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleEDDResponse.Unmarshal(m, b)
}
func (m *SingleEDDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleEDDResponse.Marshal(b, m, deterministic)
}
func (m *SingleEDDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleEDDResponse.Merge(m, src)
}
func (m *SingleEDDResponse) XXX_Size() int {
	return xxx_messageInfo_SingleEDDResponse.Size(m)
}
func (m *SingleEDDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleEDDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SingleEDDResponse proto.InternalMessageInfo

func (m *SingleEDDResponse) GetExtendedEdd() int64 {
	if m != nil && m.ExtendedEdd != nil {
		return *m.ExtendedEdd
	}
	return 0
}

func (m *SingleEDDResponse) GetExtendedNonWorkingDayList() []string {
	if m != nil {
		return m.ExtendedNonWorkingDayList
	}
	return nil
}

func (m *SingleEDDResponse) GetExtendedDays() int32 {
	if m != nil && m.ExtendedDays != nil {
		return *m.ExtendedDays
	}
	return 0
}

func (m *SingleEDDResponse) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

func (m *SingleEDDResponse) GetExtendedEddMin() int64 {
	if m != nil && m.ExtendedEddMin != nil {
		return *m.ExtendedEddMin
	}
	return 0
}

func (m *SingleEDDResponse) GetNonWorkingDayProcess() *NonWorkingDayProcess {
	if m != nil {
		return m.NonWorkingDayProcess
	}
	return nil
}

func (m *SingleEDDResponse) GetEddMinExtendedDays() int32 {
	if m != nil && m.EddMinExtendedDays != nil {
		return *m.EddMinExtendedDays
	}
	return 0
}

type GetExtendedEDDResponse struct {
	RespHeader             *RespHeader                   `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ExtendedEddResponseMap map[string]*SingleEDDResponse `protobuf:"bytes,2,rep,name=extended_edd_response_map,json=extendedEddResponseMap" json:"extended_edd_response_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral   struct{}                      `json:"-"`
	XXX_unrecognized       []byte                        `json:"-"`
	XXX_sizecache          int32                         `json:"-"`
}

func (m *GetExtendedEDDResponse) Reset()         { *m = GetExtendedEDDResponse{} }
func (m *GetExtendedEDDResponse) String() string { return proto.CompactTextString(m) }
func (*GetExtendedEDDResponse) ProtoMessage()    {}
func (*GetExtendedEDDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{28}
}

func (m *GetExtendedEDDResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtendedEDDResponse.Unmarshal(m, b)
}
func (m *GetExtendedEDDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtendedEDDResponse.Marshal(b, m, deterministic)
}
func (m *GetExtendedEDDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtendedEDDResponse.Merge(m, src)
}
func (m *GetExtendedEDDResponse) XXX_Size() int {
	return xxx_messageInfo_GetExtendedEDDResponse.Size(m)
}
func (m *GetExtendedEDDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtendedEDDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtendedEDDResponse proto.InternalMessageInfo

func (m *GetExtendedEDDResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetExtendedEDDResponse) GetExtendedEddResponseMap() map[string]*SingleEDDResponse {
	if m != nil {
		return m.ExtendedEddResponseMap
	}
	return nil
}

type GetEddCalculationInfoRequest struct {
	ReqHeader            *ReqHeader   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductInfo          *ProductInfo `protobuf:"bytes,2,req,name=product_info,json=productInfo" json:"product_info,omitempty"`
	EventTime            *uint32      `protobuf:"varint,3,req,name=event_time,json=eventTime" json:"event_time,omitempty"`
	ForderId             *uint64      `protobuf:"varint,4,opt,name=forder_id,json=forderId" json:"forder_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetEddCalculationInfoRequest) Reset()         { *m = GetEddCalculationInfoRequest{} }
func (m *GetEddCalculationInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetEddCalculationInfoRequest) ProtoMessage()    {}
func (*GetEddCalculationInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{29}
}

func (m *GetEddCalculationInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEddCalculationInfoRequest.Unmarshal(m, b)
}
func (m *GetEddCalculationInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEddCalculationInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetEddCalculationInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEddCalculationInfoRequest.Merge(m, src)
}
func (m *GetEddCalculationInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetEddCalculationInfoRequest.Size(m)
}
func (m *GetEddCalculationInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEddCalculationInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEddCalculationInfoRequest proto.InternalMessageInfo

func (m *GetEddCalculationInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetEddCalculationInfoRequest) GetProductInfo() *ProductInfo {
	if m != nil {
		return m.ProductInfo
	}
	return nil
}

func (m *GetEddCalculationInfoRequest) GetEventTime() uint32 {
	if m != nil && m.EventTime != nil {
		return *m.EventTime
	}
	return 0
}

func (m *GetEddCalculationInfoRequest) GetForderId() uint64 {
	if m != nil && m.ForderId != nil {
		return *m.ForderId
	}
	return 0
}

type EDDCalculationInfo struct {
	EddMinAvailable      *bool    `protobuf:"varint,1,opt,name=edd_min_available,json=eddMinAvailable" json:"edd_min_available,omitempty"`
	EddRangeLimit        *int64   `protobuf:"varint,2,opt,name=edd_range_limit,json=eddRangeLimit" json:"edd_range_limit,omitempty"`
	CdtMin               *float64 `protobuf:"fixed64,3,opt,name=cdt_min,json=cdtMin" json:"cdt_min,omitempty"`
	CdtMax               *float64 `protobuf:"fixed64,4,opt,name=cdt_max,json=cdtMax" json:"cdt_max,omitempty"`
	Holiday              []string `protobuf:"bytes,5,rep,name=holiday" json:"holiday,omitempty"`
	Weekend              []int32  `protobuf:"varint,6,rep,name=weekend" json:"weekend,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EDDCalculationInfo) Reset()         { *m = EDDCalculationInfo{} }
func (m *EDDCalculationInfo) String() string { return proto.CompactTextString(m) }
func (*EDDCalculationInfo) ProtoMessage()    {}
func (*EDDCalculationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{30}
}

func (m *EDDCalculationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EDDCalculationInfo.Unmarshal(m, b)
}
func (m *EDDCalculationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EDDCalculationInfo.Marshal(b, m, deterministic)
}
func (m *EDDCalculationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EDDCalculationInfo.Merge(m, src)
}
func (m *EDDCalculationInfo) XXX_Size() int {
	return xxx_messageInfo_EDDCalculationInfo.Size(m)
}
func (m *EDDCalculationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EDDCalculationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EDDCalculationInfo proto.InternalMessageInfo

func (m *EDDCalculationInfo) GetEddMinAvailable() bool {
	if m != nil && m.EddMinAvailable != nil {
		return *m.EddMinAvailable
	}
	return false
}

func (m *EDDCalculationInfo) GetEddRangeLimit() int64 {
	if m != nil && m.EddRangeLimit != nil {
		return *m.EddRangeLimit
	}
	return 0
}

func (m *EDDCalculationInfo) GetCdtMin() float64 {
	if m != nil && m.CdtMin != nil {
		return *m.CdtMin
	}
	return 0
}

func (m *EDDCalculationInfo) GetCdtMax() float64 {
	if m != nil && m.CdtMax != nil {
		return *m.CdtMax
	}
	return 0
}

func (m *EDDCalculationInfo) GetHoliday() []string {
	if m != nil {
		return m.Holiday
	}
	return nil
}

func (m *EDDCalculationInfo) GetWeekend() []int32 {
	if m != nil {
		return m.Weekend
	}
	return nil
}

type DDLCalculationInfo struct {
	ForwardCdt           *float64 `protobuf:"fixed64,1,opt,name=forward_cdt,json=forwardCdt" json:"forward_cdt,omitempty"`
	BackwardCdt          *float64 `protobuf:"fixed64,2,opt,name=backward_cdt,json=backwardCdt" json:"backward_cdt,omitempty"`
	NextEventForwardCdt  *float64 `protobuf:"fixed64,3,opt,name=next_event_forward_cdt,json=nextEventForwardCdt" json:"next_event_forward_cdt,omitempty"`
	NextEventBackwardCdt *float64 `protobuf:"fixed64,4,opt,name=next_event_backward_cdt,json=nextEventBackwardCdt" json:"next_event_backward_cdt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DDLCalculationInfo) Reset()         { *m = DDLCalculationInfo{} }
func (m *DDLCalculationInfo) String() string { return proto.CompactTextString(m) }
func (*DDLCalculationInfo) ProtoMessage()    {}
func (*DDLCalculationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{31}
}

func (m *DDLCalculationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DDLCalculationInfo.Unmarshal(m, b)
}
func (m *DDLCalculationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DDLCalculationInfo.Marshal(b, m, deterministic)
}
func (m *DDLCalculationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DDLCalculationInfo.Merge(m, src)
}
func (m *DDLCalculationInfo) XXX_Size() int {
	return xxx_messageInfo_DDLCalculationInfo.Size(m)
}
func (m *DDLCalculationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DDLCalculationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DDLCalculationInfo proto.InternalMessageInfo

func (m *DDLCalculationInfo) GetForwardCdt() float64 {
	if m != nil && m.ForwardCdt != nil {
		return *m.ForwardCdt
	}
	return 0
}

func (m *DDLCalculationInfo) GetBackwardCdt() float64 {
	if m != nil && m.BackwardCdt != nil {
		return *m.BackwardCdt
	}
	return 0
}

func (m *DDLCalculationInfo) GetNextEventForwardCdt() float64 {
	if m != nil && m.NextEventForwardCdt != nil {
		return *m.NextEventForwardCdt
	}
	return 0
}

func (m *DDLCalculationInfo) GetNextEventBackwardCdt() float64 {
	if m != nil && m.NextEventBackwardCdt != nil {
		return *m.NextEventBackwardCdt
	}
	return 0
}

type GetEddCalculationInfoResponse struct {
	RespHeader           *RespHeader         `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	EddInfo              *EDDCalculationInfo `protobuf:"bytes,2,opt,name=edd_info,json=eddInfo" json:"edd_info,omitempty"`
	DdlInfo              *DDLCalculationInfo `protobuf:"bytes,3,opt,name=ddl_info,json=ddlInfo" json:"ddl_info,omitempty"`
	EddProcess           *EDDProcess         `protobuf:"bytes,4,opt,name=edd_process,json=eddProcess" json:"edd_process,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetEddCalculationInfoResponse) Reset()         { *m = GetEddCalculationInfoResponse{} }
func (m *GetEddCalculationInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetEddCalculationInfoResponse) ProtoMessage()    {}
func (*GetEddCalculationInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{32}
}

func (m *GetEddCalculationInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEddCalculationInfoResponse.Unmarshal(m, b)
}
func (m *GetEddCalculationInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEddCalculationInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetEddCalculationInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEddCalculationInfoResponse.Merge(m, src)
}
func (m *GetEddCalculationInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetEddCalculationInfoResponse.Size(m)
}
func (m *GetEddCalculationInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEddCalculationInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEddCalculationInfoResponse proto.InternalMessageInfo

func (m *GetEddCalculationInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetEddCalculationInfoResponse) GetEddInfo() *EDDCalculationInfo {
	if m != nil {
		return m.EddInfo
	}
	return nil
}

func (m *GetEddCalculationInfoResponse) GetDdlInfo() *DDLCalculationInfo {
	if m != nil {
		return m.DdlInfo
	}
	return nil
}

func (m *GetEddCalculationInfoResponse) GetEddProcess() *EDDProcess {
	if m != nil {
		return m.EddProcess
	}
	return nil
}

type GetEddInfoByAlgoRequest struct {
	ReqHeader            *ReqHeader   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductInfo          *ProductInfo `protobuf:"bytes,2,req,name=product_info,json=productInfo" json:"product_info,omitempty"`
	EventTime            *uint32      `protobuf:"varint,3,req,name=event_time,json=eventTime" json:"event_time,omitempty"`
	EventTrackingCode    *string      `protobuf:"bytes,4,req,name=event_tracking_code,json=eventTrackingCode" json:"event_tracking_code,omitempty"`
	SlsTn                *string      `protobuf:"bytes,5,req,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetEddInfoByAlgoRequest) Reset()         { *m = GetEddInfoByAlgoRequest{} }
func (m *GetEddInfoByAlgoRequest) String() string { return proto.CompactTextString(m) }
func (*GetEddInfoByAlgoRequest) ProtoMessage()    {}
func (*GetEddInfoByAlgoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{33}
}

func (m *GetEddInfoByAlgoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEddInfoByAlgoRequest.Unmarshal(m, b)
}
func (m *GetEddInfoByAlgoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEddInfoByAlgoRequest.Marshal(b, m, deterministic)
}
func (m *GetEddInfoByAlgoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEddInfoByAlgoRequest.Merge(m, src)
}
func (m *GetEddInfoByAlgoRequest) XXX_Size() int {
	return xxx_messageInfo_GetEddInfoByAlgoRequest.Size(m)
}
func (m *GetEddInfoByAlgoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEddInfoByAlgoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEddInfoByAlgoRequest proto.InternalMessageInfo

func (m *GetEddInfoByAlgoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetEddInfoByAlgoRequest) GetProductInfo() *ProductInfo {
	if m != nil {
		return m.ProductInfo
	}
	return nil
}

func (m *GetEddInfoByAlgoRequest) GetEventTime() uint32 {
	if m != nil && m.EventTime != nil {
		return *m.EventTime
	}
	return 0
}

func (m *GetEddInfoByAlgoRequest) GetEventTrackingCode() string {
	if m != nil && m.EventTrackingCode != nil {
		return *m.EventTrackingCode
	}
	return ""
}

func (m *GetEddInfoByAlgoRequest) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

type AlgoResult struct {
	EddMin               *string  `protobuf:"bytes,1,req,name=edd_min,json=eddMin" json:"edd_min,omitempty"`
	EddMax               *string  `protobuf:"bytes,2,req,name=edd_max,json=eddMax" json:"edd_max,omitempty"`
	EventType            *string  `protobuf:"bytes,3,req,name=event_type,json=eventType" json:"event_type,omitempty"`
	EventTime            *int64   `protobuf:"varint,4,req,name=event_time,json=eventTime" json:"event_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AlgoResult) Reset()         { *m = AlgoResult{} }
func (m *AlgoResult) String() string { return proto.CompactTextString(m) }
func (*AlgoResult) ProtoMessage()    {}
func (*AlgoResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{34}
}

func (m *AlgoResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AlgoResult.Unmarshal(m, b)
}
func (m *AlgoResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AlgoResult.Marshal(b, m, deterministic)
}
func (m *AlgoResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AlgoResult.Merge(m, src)
}
func (m *AlgoResult) XXX_Size() int {
	return xxx_messageInfo_AlgoResult.Size(m)
}
func (m *AlgoResult) XXX_DiscardUnknown() {
	xxx_messageInfo_AlgoResult.DiscardUnknown(m)
}

var xxx_messageInfo_AlgoResult proto.InternalMessageInfo

func (m *AlgoResult) GetEddMin() string {
	if m != nil && m.EddMin != nil {
		return *m.EddMin
	}
	return ""
}

func (m *AlgoResult) GetEddMax() string {
	if m != nil && m.EddMax != nil {
		return *m.EddMax
	}
	return ""
}

func (m *AlgoResult) GetEventType() string {
	if m != nil && m.EventType != nil {
		return *m.EventType
	}
	return ""
}

func (m *AlgoResult) GetEventTime() int64 {
	if m != nil && m.EventTime != nil {
		return *m.EventTime
	}
	return 0
}

type GetEddInfoByAlgoResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	EddMin               *int64      `protobuf:"varint,2,opt,name=edd_min,json=eddMin" json:"edd_min,omitempty"`
	EddMax               *int64      `protobuf:"varint,3,opt,name=edd_max,json=eddMax" json:"edd_max,omitempty"`
	EddProcess           *EDDProcess `protobuf:"bytes,4,opt,name=edd_process,json=eddProcess" json:"edd_process,omitempty"`
	AlgoResult           *AlgoResult `protobuf:"bytes,5,opt,name=algo_result,json=algoResult" json:"algo_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetEddInfoByAlgoResponse) Reset()         { *m = GetEddInfoByAlgoResponse{} }
func (m *GetEddInfoByAlgoResponse) String() string { return proto.CompactTextString(m) }
func (*GetEddInfoByAlgoResponse) ProtoMessage()    {}
func (*GetEddInfoByAlgoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{35}
}

func (m *GetEddInfoByAlgoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEddInfoByAlgoResponse.Unmarshal(m, b)
}
func (m *GetEddInfoByAlgoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEddInfoByAlgoResponse.Marshal(b, m, deterministic)
}
func (m *GetEddInfoByAlgoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEddInfoByAlgoResponse.Merge(m, src)
}
func (m *GetEddInfoByAlgoResponse) XXX_Size() int {
	return xxx_messageInfo_GetEddInfoByAlgoResponse.Size(m)
}
func (m *GetEddInfoByAlgoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEddInfoByAlgoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEddInfoByAlgoResponse proto.InternalMessageInfo

func (m *GetEddInfoByAlgoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetEddInfoByAlgoResponse) GetEddMin() int64 {
	if m != nil && m.EddMin != nil {
		return *m.EddMin
	}
	return 0
}

func (m *GetEddInfoByAlgoResponse) GetEddMax() int64 {
	if m != nil && m.EddMax != nil {
		return *m.EddMax
	}
	return 0
}

func (m *GetEddInfoByAlgoResponse) GetEddProcess() *EDDProcess {
	if m != nil {
		return m.EddProcess
	}
	return nil
}

func (m *GetEddInfoByAlgoResponse) GetAlgoResult() *AlgoResult {
	if m != nil {
		return m.AlgoResult
	}
	return nil
}

// for possible new query params, put it here
type QueryInfo struct {
	QueryId              *string  `protobuf:"bytes,1,req,name=query_id,json=queryId" json:"query_id,omitempty"`
	SlsTn                *string  `protobuf:"bytes,2,opt,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	Region               *string  `protobuf:"bytes,3,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryInfo) Reset()         { *m = QueryInfo{} }
func (m *QueryInfo) String() string { return proto.CompactTextString(m) }
func (*QueryInfo) ProtoMessage()    {}
func (*QueryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{36}
}

func (m *QueryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryInfo.Unmarshal(m, b)
}
func (m *QueryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryInfo.Marshal(b, m, deterministic)
}
func (m *QueryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryInfo.Merge(m, src)
}
func (m *QueryInfo) XXX_Size() int {
	return xxx_messageInfo_QueryInfo.Size(m)
}
func (m *QueryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_QueryInfo proto.InternalMessageInfo

func (m *QueryInfo) GetQueryId() string {
	if m != nil && m.QueryId != nil {
		return *m.QueryId
	}
	return ""
}

func (m *QueryInfo) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

func (m *QueryInfo) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type QueryEDDInfoRequest struct {
	ReqHeader            *ReqHeader   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	QueryInfoList        []*QueryInfo `protobuf:"bytes,2,rep,name=query_info_list,json=queryInfoList" json:"query_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QueryEDDInfoRequest) Reset()         { *m = QueryEDDInfoRequest{} }
func (m *QueryEDDInfoRequest) String() string { return proto.CompactTextString(m) }
func (*QueryEDDInfoRequest) ProtoMessage()    {}
func (*QueryEDDInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{37}
}

func (m *QueryEDDInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryEDDInfoRequest.Unmarshal(m, b)
}
func (m *QueryEDDInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryEDDInfoRequest.Marshal(b, m, deterministic)
}
func (m *QueryEDDInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryEDDInfoRequest.Merge(m, src)
}
func (m *QueryEDDInfoRequest) XXX_Size() int {
	return xxx_messageInfo_QueryEDDInfoRequest.Size(m)
}
func (m *QueryEDDInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryEDDInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryEDDInfoRequest proto.InternalMessageInfo

func (m *QueryEDDInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *QueryEDDInfoRequest) GetQueryInfoList() []*QueryInfo {
	if m != nil {
		return m.QueryInfoList
	}
	return nil
}

type EDDInfo struct {
	QueryId              *string  `protobuf:"bytes,1,opt,name=query_id,json=queryId" json:"query_id,omitempty"`
	EddMax               *int64   `protobuf:"varint,2,opt,name=edd_max,json=eddMax" json:"edd_max,omitempty"`
	EddMin               *int64   `protobuf:"varint,3,opt,name=edd_min,json=eddMin" json:"edd_min,omitempty"`
	Ctime                *int64   `protobuf:"varint,4,opt,name=ctime" json:"ctime,omitempty"`
	ProductId            *string  `protobuf:"bytes,5,opt,name=product_id,json=productId" json:"product_id,omitempty"`
	SlsTn                *string  `protobuf:"bytes,6,opt,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	Retcode              *int32   `protobuf:"varint,7,opt,name=retcode" json:"retcode,omitempty"`
	ErrorMessage         *string  `protobuf:"bytes,8,opt,name=error_message,json=errorMessage" json:"error_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EDDInfo) Reset()         { *m = EDDInfo{} }
func (m *EDDInfo) String() string { return proto.CompactTextString(m) }
func (*EDDInfo) ProtoMessage()    {}
func (*EDDInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{38}
}

func (m *EDDInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EDDInfo.Unmarshal(m, b)
}
func (m *EDDInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EDDInfo.Marshal(b, m, deterministic)
}
func (m *EDDInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EDDInfo.Merge(m, src)
}
func (m *EDDInfo) XXX_Size() int {
	return xxx_messageInfo_EDDInfo.Size(m)
}
func (m *EDDInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EDDInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EDDInfo proto.InternalMessageInfo

func (m *EDDInfo) GetQueryId() string {
	if m != nil && m.QueryId != nil {
		return *m.QueryId
	}
	return ""
}

func (m *EDDInfo) GetEddMax() int64 {
	if m != nil && m.EddMax != nil {
		return *m.EddMax
	}
	return 0
}

func (m *EDDInfo) GetEddMin() int64 {
	if m != nil && m.EddMin != nil {
		return *m.EddMin
	}
	return 0
}

func (m *EDDInfo) GetCtime() int64 {
	if m != nil && m.Ctime != nil {
		return *m.Ctime
	}
	return 0
}

func (m *EDDInfo) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *EDDInfo) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

func (m *EDDInfo) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *EDDInfo) GetErrorMessage() string {
	if m != nil && m.ErrorMessage != nil {
		return *m.ErrorMessage
	}
	return ""
}

type QueryEDDInfoResponse struct {
	RespHeader           *RespHeader         `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	EddInfoMap           map[string]*EDDInfo `protobuf:"bytes,2,rep,name=edd_info_map,json=eddInfoMap" json:"edd_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *QueryEDDInfoResponse) Reset()         { *m = QueryEDDInfoResponse{} }
func (m *QueryEDDInfoResponse) String() string { return proto.CompactTextString(m) }
func (*QueryEDDInfoResponse) ProtoMessage()    {}
func (*QueryEDDInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{39}
}

func (m *QueryEDDInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryEDDInfoResponse.Unmarshal(m, b)
}
func (m *QueryEDDInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryEDDInfoResponse.Marshal(b, m, deterministic)
}
func (m *QueryEDDInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryEDDInfoResponse.Merge(m, src)
}
func (m *QueryEDDInfoResponse) XXX_Size() int {
	return xxx_messageInfo_QueryEDDInfoResponse.Size(m)
}
func (m *QueryEDDInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryEDDInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryEDDInfoResponse proto.InternalMessageInfo

func (m *QueryEDDInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *QueryEDDInfoResponse) GetEddInfoMap() map[string]*EDDInfo {
	if m != nil {
		return m.EddInfoMap
	}
	return nil
}

// --- 获取Region维度可用的mcahnnel规则---
type ListMChannelRuleByRegionRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	Region               *string    `protobuf:"bytes,2,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ListMChannelRuleByRegionRequest) Reset()         { *m = ListMChannelRuleByRegionRequest{} }
func (m *ListMChannelRuleByRegionRequest) String() string { return proto.CompactTextString(m) }
func (*ListMChannelRuleByRegionRequest) ProtoMessage()    {}
func (*ListMChannelRuleByRegionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{40}
}

func (m *ListMChannelRuleByRegionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMChannelRuleByRegionRequest.Unmarshal(m, b)
}
func (m *ListMChannelRuleByRegionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMChannelRuleByRegionRequest.Marshal(b, m, deterministic)
}
func (m *ListMChannelRuleByRegionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMChannelRuleByRegionRequest.Merge(m, src)
}
func (m *ListMChannelRuleByRegionRequest) XXX_Size() int {
	return xxx_messageInfo_ListMChannelRuleByRegionRequest.Size(m)
}
func (m *ListMChannelRuleByRegionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMChannelRuleByRegionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListMChannelRuleByRegionRequest proto.InternalMessageInfo

func (m *ListMChannelRuleByRegionRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *ListMChannelRuleByRegionRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type MChannelRuleByRegionResponse struct {
	RespHeader           *RespHeader         `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	MchannelRuleList     []*MChannelRuleList `protobuf:"bytes,2,rep,name=mchannel_rule_list,json=mchannelRuleList" json:"mchannel_rule_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MChannelRuleByRegionResponse) Reset()         { *m = MChannelRuleByRegionResponse{} }
func (m *MChannelRuleByRegionResponse) String() string { return proto.CompactTextString(m) }
func (*MChannelRuleByRegionResponse) ProtoMessage()    {}
func (*MChannelRuleByRegionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{41}
}

func (m *MChannelRuleByRegionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MChannelRuleByRegionResponse.Unmarshal(m, b)
}
func (m *MChannelRuleByRegionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MChannelRuleByRegionResponse.Marshal(b, m, deterministic)
}
func (m *MChannelRuleByRegionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MChannelRuleByRegionResponse.Merge(m, src)
}
func (m *MChannelRuleByRegionResponse) XXX_Size() int {
	return xxx_messageInfo_MChannelRuleByRegionResponse.Size(m)
}
func (m *MChannelRuleByRegionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MChannelRuleByRegionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MChannelRuleByRegionResponse proto.InternalMessageInfo

func (m *MChannelRuleByRegionResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *MChannelRuleByRegionResponse) GetMchannelRuleList() []*MChannelRuleList {
	if m != nil {
		return m.MchannelRuleList
	}
	return nil
}

type MChannelRuleList struct {
	ProductId            *string           `protobuf:"bytes,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	MaxCdtRule           *MChannelRuleEnum `protobuf:"varint,2,opt,name=max_cdt_rule,json=maxCdtRule,enum=lcos_protobuf.MChannelRuleEnum" json:"max_cdt_rule,omitempty"`
	MinCdtRule           *MChannelRuleEnum `protobuf:"varint,3,opt,name=min_cdt_rule,json=minCdtRule,enum=lcos_protobuf.MChannelRuleEnum" json:"min_cdt_rule,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MChannelRuleList) Reset()         { *m = MChannelRuleList{} }
func (m *MChannelRuleList) String() string { return proto.CompactTextString(m) }
func (*MChannelRuleList) ProtoMessage()    {}
func (*MChannelRuleList) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{42}
}

func (m *MChannelRuleList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MChannelRuleList.Unmarshal(m, b)
}
func (m *MChannelRuleList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MChannelRuleList.Marshal(b, m, deterministic)
}
func (m *MChannelRuleList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MChannelRuleList.Merge(m, src)
}
func (m *MChannelRuleList) XXX_Size() int {
	return xxx_messageInfo_MChannelRuleList.Size(m)
}
func (m *MChannelRuleList) XXX_DiscardUnknown() {
	xxx_messageInfo_MChannelRuleList.DiscardUnknown(m)
}

var xxx_messageInfo_MChannelRuleList proto.InternalMessageInfo

func (m *MChannelRuleList) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *MChannelRuleList) GetMaxCdtRule() MChannelRuleEnum {
	if m != nil && m.MaxCdtRule != nil {
		return *m.MaxCdtRule
	}
	return MChannelRuleEnum_FastestFChannel
}

func (m *MChannelRuleList) GetMinCdtRule() MChannelRuleEnum {
	if m != nil && m.MinCdtRule != nil {
		return *m.MinCdtRule
	}
	return MChannelRuleEnum_FastestFChannel
}

// --- 获取Region维度可用的mcahnnel 灰度开关规则---
type ListMChannelGreyConfigByRegionRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	Region               *string    `protobuf:"bytes,2,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ListMChannelGreyConfigByRegionRequest) Reset()         { *m = ListMChannelGreyConfigByRegionRequest{} }
func (m *ListMChannelGreyConfigByRegionRequest) String() string { return proto.CompactTextString(m) }
func (*ListMChannelGreyConfigByRegionRequest) ProtoMessage()    {}
func (*ListMChannelGreyConfigByRegionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{43}
}

func (m *ListMChannelGreyConfigByRegionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMChannelGreyConfigByRegionRequest.Unmarshal(m, b)
}
func (m *ListMChannelGreyConfigByRegionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMChannelGreyConfigByRegionRequest.Marshal(b, m, deterministic)
}
func (m *ListMChannelGreyConfigByRegionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMChannelGreyConfigByRegionRequest.Merge(m, src)
}
func (m *ListMChannelGreyConfigByRegionRequest) XXX_Size() int {
	return xxx_messageInfo_ListMChannelGreyConfigByRegionRequest.Size(m)
}
func (m *ListMChannelGreyConfigByRegionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMChannelGreyConfigByRegionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListMChannelGreyConfigByRegionRequest proto.InternalMessageInfo

func (m *ListMChannelGreyConfigByRegionRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *ListMChannelGreyConfigByRegionRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type MChannelGreyConfigByRegionResponse struct {
	RespHeader             *RespHeader               `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	MchannelGreyConfigList []*MChannelGreyConfigList `protobuf:"bytes,2,rep,name=mchannel_grey_config_list,json=mchannelGreyConfigList" json:"mchannel_grey_config_list,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                  `json:"-"`
	XXX_unrecognized       []byte                    `json:"-"`
	XXX_sizecache          int32                     `json:"-"`
}

func (m *MChannelGreyConfigByRegionResponse) Reset()         { *m = MChannelGreyConfigByRegionResponse{} }
func (m *MChannelGreyConfigByRegionResponse) String() string { return proto.CompactTextString(m) }
func (*MChannelGreyConfigByRegionResponse) ProtoMessage()    {}
func (*MChannelGreyConfigByRegionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{44}
}

func (m *MChannelGreyConfigByRegionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MChannelGreyConfigByRegionResponse.Unmarshal(m, b)
}
func (m *MChannelGreyConfigByRegionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MChannelGreyConfigByRegionResponse.Marshal(b, m, deterministic)
}
func (m *MChannelGreyConfigByRegionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MChannelGreyConfigByRegionResponse.Merge(m, src)
}
func (m *MChannelGreyConfigByRegionResponse) XXX_Size() int {
	return xxx_messageInfo_MChannelGreyConfigByRegionResponse.Size(m)
}
func (m *MChannelGreyConfigByRegionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MChannelGreyConfigByRegionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MChannelGreyConfigByRegionResponse proto.InternalMessageInfo

func (m *MChannelGreyConfigByRegionResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *MChannelGreyConfigByRegionResponse) GetMchannelGreyConfigList() []*MChannelGreyConfigList {
	if m != nil {
		return m.MchannelGreyConfigList
	}
	return nil
}

type MChannelGreyConfigList struct {
	ProductId            *string  `protobuf:"bytes,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	Percentage           *uint32  `protobuf:"varint,2,req,name=percentage" json:"percentage,omitempty"`
	BuyerIdsList         []uint32 `protobuf:"varint,3,rep,name=buyer_ids_list,json=buyerIdsList" json:"buyer_ids_list,omitempty"`
	StateLocationIdList  []uint32 `protobuf:"varint,4,rep,name=state_location_id_list,json=stateLocationIdList" json:"state_location_id_list,omitempty"`
	CityLocationIdList   []uint32 `protobuf:"varint,5,rep,name=city_location_id_list,json=cityLocationIdList" json:"city_location_id_list,omitempty"`
	ScenarioList         []uint32 `protobuf:"varint,6,rep,name=scenario_list,json=scenarioList" json:"scenario_list,omitempty"`
	FChannelNumsList     []uint32 `protobuf:"varint,7,rep,name=f_channel_nums_list,json=fChannelNumsList" json:"f_channel_nums_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MChannelGreyConfigList) Reset()         { *m = MChannelGreyConfigList{} }
func (m *MChannelGreyConfigList) String() string { return proto.CompactTextString(m) }
func (*MChannelGreyConfigList) ProtoMessage()    {}
func (*MChannelGreyConfigList) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{45}
}

func (m *MChannelGreyConfigList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MChannelGreyConfigList.Unmarshal(m, b)
}
func (m *MChannelGreyConfigList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MChannelGreyConfigList.Marshal(b, m, deterministic)
}
func (m *MChannelGreyConfigList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MChannelGreyConfigList.Merge(m, src)
}
func (m *MChannelGreyConfigList) XXX_Size() int {
	return xxx_messageInfo_MChannelGreyConfigList.Size(m)
}
func (m *MChannelGreyConfigList) XXX_DiscardUnknown() {
	xxx_messageInfo_MChannelGreyConfigList.DiscardUnknown(m)
}

var xxx_messageInfo_MChannelGreyConfigList proto.InternalMessageInfo

func (m *MChannelGreyConfigList) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *MChannelGreyConfigList) GetPercentage() uint32 {
	if m != nil && m.Percentage != nil {
		return *m.Percentage
	}
	return 0
}

func (m *MChannelGreyConfigList) GetBuyerIdsList() []uint32 {
	if m != nil {
		return m.BuyerIdsList
	}
	return nil
}

func (m *MChannelGreyConfigList) GetStateLocationIdList() []uint32 {
	if m != nil {
		return m.StateLocationIdList
	}
	return nil
}

func (m *MChannelGreyConfigList) GetCityLocationIdList() []uint32 {
	if m != nil {
		return m.CityLocationIdList
	}
	return nil
}

func (m *MChannelGreyConfigList) GetScenarioList() []uint32 {
	if m != nil {
		return m.ScenarioList
	}
	return nil
}

func (m *MChannelGreyConfigList) GetFChannelNumsList() []uint32 {
	if m != nil {
		return m.FChannelNumsList
	}
	return nil
}

type ListABTestRuleByRegionRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	Region               *string    `protobuf:"bytes,2,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ListABTestRuleByRegionRequest) Reset()         { *m = ListABTestRuleByRegionRequest{} }
func (m *ListABTestRuleByRegionRequest) String() string { return proto.CompactTextString(m) }
func (*ListABTestRuleByRegionRequest) ProtoMessage()    {}
func (*ListABTestRuleByRegionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{46}
}

func (m *ListABTestRuleByRegionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListABTestRuleByRegionRequest.Unmarshal(m, b)
}
func (m *ListABTestRuleByRegionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListABTestRuleByRegionRequest.Marshal(b, m, deterministic)
}
func (m *ListABTestRuleByRegionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListABTestRuleByRegionRequest.Merge(m, src)
}
func (m *ListABTestRuleByRegionRequest) XXX_Size() int {
	return xxx_messageInfo_ListABTestRuleByRegionRequest.Size(m)
}
func (m *ListABTestRuleByRegionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListABTestRuleByRegionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListABTestRuleByRegionRequest proto.InternalMessageInfo

func (m *ListABTestRuleByRegionRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *ListABTestRuleByRegionRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type ListABTestRuleByRegionResponse struct {
	RespHeader           *RespHeader       `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	AbTestRuleList       []*ABTestRuleList `protobuf:"bytes,2,rep,name=ab_test_rule_list,json=abTestRuleList" json:"ab_test_rule_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ListABTestRuleByRegionResponse) Reset()         { *m = ListABTestRuleByRegionResponse{} }
func (m *ListABTestRuleByRegionResponse) String() string { return proto.CompactTextString(m) }
func (*ListABTestRuleByRegionResponse) ProtoMessage()    {}
func (*ListABTestRuleByRegionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{47}
}

func (m *ListABTestRuleByRegionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListABTestRuleByRegionResponse.Unmarshal(m, b)
}
func (m *ListABTestRuleByRegionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListABTestRuleByRegionResponse.Marshal(b, m, deterministic)
}
func (m *ListABTestRuleByRegionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListABTestRuleByRegionResponse.Merge(m, src)
}
func (m *ListABTestRuleByRegionResponse) XXX_Size() int {
	return xxx_messageInfo_ListABTestRuleByRegionResponse.Size(m)
}
func (m *ListABTestRuleByRegionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListABTestRuleByRegionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListABTestRuleByRegionResponse proto.InternalMessageInfo

func (m *ListABTestRuleByRegionResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *ListABTestRuleByRegionResponse) GetAbTestRuleList() []*ABTestRuleList {
	if m != nil {
		return m.AbTestRuleList
	}
	return nil
}

type ABTestRuleList struct {
	ProductId            *string              `protobuf:"bytes,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	ActiveTime           *uint32              `protobuf:"varint,2,req,name=active_time,json=activeTime" json:"active_time,omitempty"`
	AbTestRuleDetails    []*ABTestRuleDetails `protobuf:"bytes,3,rep,name=ab_test_rule_details,json=abTestRuleDetails" json:"ab_test_rule_details,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ABTestRuleList) Reset()         { *m = ABTestRuleList{} }
func (m *ABTestRuleList) String() string { return proto.CompactTextString(m) }
func (*ABTestRuleList) ProtoMessage()    {}
func (*ABTestRuleList) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{48}
}

func (m *ABTestRuleList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ABTestRuleList.Unmarshal(m, b)
}
func (m *ABTestRuleList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ABTestRuleList.Marshal(b, m, deterministic)
}
func (m *ABTestRuleList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ABTestRuleList.Merge(m, src)
}
func (m *ABTestRuleList) XXX_Size() int {
	return xxx_messageInfo_ABTestRuleList.Size(m)
}
func (m *ABTestRuleList) XXX_DiscardUnknown() {
	xxx_messageInfo_ABTestRuleList.DiscardUnknown(m)
}

var xxx_messageInfo_ABTestRuleList proto.InternalMessageInfo

func (m *ABTestRuleList) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *ABTestRuleList) GetActiveTime() uint32 {
	if m != nil && m.ActiveTime != nil {
		return *m.ActiveTime
	}
	return 0
}

func (m *ABTestRuleList) GetAbTestRuleDetails() []*ABTestRuleDetails {
	if m != nil {
		return m.AbTestRuleDetails
	}
	return nil
}

type ABTestRuleDetails struct {
	GroupTag             *uint32  `protobuf:"varint,1,req,name=group_tag,json=groupTag" json:"group_tag,omitempty"`
	Groups               []uint32 `protobuf:"varint,2,rep,name=groups" json:"groups,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ABTestRuleDetails) Reset()         { *m = ABTestRuleDetails{} }
func (m *ABTestRuleDetails) String() string { return proto.CompactTextString(m) }
func (*ABTestRuleDetails) ProtoMessage()    {}
func (*ABTestRuleDetails) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{49}
}

func (m *ABTestRuleDetails) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ABTestRuleDetails.Unmarshal(m, b)
}
func (m *ABTestRuleDetails) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ABTestRuleDetails.Marshal(b, m, deterministic)
}
func (m *ABTestRuleDetails) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ABTestRuleDetails.Merge(m, src)
}
func (m *ABTestRuleDetails) XXX_Size() int {
	return xxx_messageInfo_ABTestRuleDetails.Size(m)
}
func (m *ABTestRuleDetails) XXX_DiscardUnknown() {
	xxx_messageInfo_ABTestRuleDetails.DiscardUnknown(m)
}

var xxx_messageInfo_ABTestRuleDetails proto.InternalMessageInfo

func (m *ABTestRuleDetails) GetGroupTag() uint32 {
	if m != nil && m.GroupTag != nil {
		return *m.GroupTag
	}
	return 0
}

func (m *ABTestRuleDetails) GetGroups() []uint32 {
	if m != nil {
		return m.Groups
	}
	return nil
}

// SPLN-33865 用于根据请求入参计算修正规则的简单product信息
type SimpleProductInfo struct {
	QueryId              *string      `protobuf:"bytes,1,req,name=query_id,json=queryId" json:"query_id,omitempty"`
	ProductId            *string      `protobuf:"bytes,2,req,name=product_id,json=productId" json:"product_id,omitempty"`
	IsCb                 *int32       `protobuf:"varint,3,req,name=is_cb,json=isCb" json:"is_cb,omitempty"`
	IsSiteLine           *int32       `protobuf:"varint,4,req,name=is_site_line,json=isSiteLine" json:"is_site_line,omitempty"`
	IsLm                 *int32       `protobuf:"varint,5,req,name=is_lm,json=isLm" json:"is_lm,omitempty"`
	Region               *string      `protobuf:"bytes,6,req,name=region" json:"region,omitempty"`
	SellerAddr           *AddressInfo `protobuf:"bytes,7,req,name=seller_addr,json=sellerAddr" json:"seller_addr,omitempty"`
	BuyerAddr            *AddressInfo `protobuf:"bytes,8,req,name=buyer_addr,json=buyerAddr" json:"buyer_addr,omitempty"`
	UpdateEvent          *uint32      `protobuf:"varint,9,opt,name=update_event,json=updateEvent" json:"update_event,omitempty"`
	RequestTime          *uint32      `protobuf:"varint,10,opt,name=request_time,json=requestTime" json:"request_time,omitempty"`
	Scene                *CdtScene    `protobuf:"varint,11,opt,name=scene,enum=lcos_protobuf.CdtScene" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SimpleProductInfo) Reset()         { *m = SimpleProductInfo{} }
func (m *SimpleProductInfo) String() string { return proto.CompactTextString(m) }
func (*SimpleProductInfo) ProtoMessage()    {}
func (*SimpleProductInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{50}
}

func (m *SimpleProductInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleProductInfo.Unmarshal(m, b)
}
func (m *SimpleProductInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleProductInfo.Marshal(b, m, deterministic)
}
func (m *SimpleProductInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleProductInfo.Merge(m, src)
}
func (m *SimpleProductInfo) XXX_Size() int {
	return xxx_messageInfo_SimpleProductInfo.Size(m)
}
func (m *SimpleProductInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleProductInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleProductInfo proto.InternalMessageInfo

func (m *SimpleProductInfo) GetQueryId() string {
	if m != nil && m.QueryId != nil {
		return *m.QueryId
	}
	return ""
}

func (m *SimpleProductInfo) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *SimpleProductInfo) GetIsCb() int32 {
	if m != nil && m.IsCb != nil {
		return *m.IsCb
	}
	return 0
}

func (m *SimpleProductInfo) GetIsSiteLine() int32 {
	if m != nil && m.IsSiteLine != nil {
		return *m.IsSiteLine
	}
	return 0
}

func (m *SimpleProductInfo) GetIsLm() int32 {
	if m != nil && m.IsLm != nil {
		return *m.IsLm
	}
	return 0
}

func (m *SimpleProductInfo) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *SimpleProductInfo) GetSellerAddr() *AddressInfo {
	if m != nil {
		return m.SellerAddr
	}
	return nil
}

func (m *SimpleProductInfo) GetBuyerAddr() *AddressInfo {
	if m != nil {
		return m.BuyerAddr
	}
	return nil
}

func (m *SimpleProductInfo) GetUpdateEvent() uint32 {
	if m != nil && m.UpdateEvent != nil {
		return *m.UpdateEvent
	}
	return 0
}

func (m *SimpleProductInfo) GetRequestTime() uint32 {
	if m != nil && m.RequestTime != nil {
		return *m.RequestTime
	}
	return 0
}

func (m *SimpleProductInfo) GetScene() CdtScene {
	if m != nil && m.Scene != nil {
		return *m.Scene
	}
	return CdtScene_Default
}

type QueryManualManipulationRequest struct {
	ReqHeader            *ReqHeader           `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductInfo          []*SimpleProductInfo `protobuf:"bytes,2,rep,name=product_info,json=productInfo" json:"product_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *QueryManualManipulationRequest) Reset()         { *m = QueryManualManipulationRequest{} }
func (m *QueryManualManipulationRequest) String() string { return proto.CompactTextString(m) }
func (*QueryManualManipulationRequest) ProtoMessage()    {}
func (*QueryManualManipulationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{51}
}

func (m *QueryManualManipulationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryManualManipulationRequest.Unmarshal(m, b)
}
func (m *QueryManualManipulationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryManualManipulationRequest.Marshal(b, m, deterministic)
}
func (m *QueryManualManipulationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryManualManipulationRequest.Merge(m, src)
}
func (m *QueryManualManipulationRequest) XXX_Size() int {
	return xxx_messageInfo_QueryManualManipulationRequest.Size(m)
}
func (m *QueryManualManipulationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryManualManipulationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryManualManipulationRequest proto.InternalMessageInfo

func (m *QueryManualManipulationRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *QueryManualManipulationRequest) GetProductInfo() []*SimpleProductInfo {
	if m != nil {
		return m.ProductInfo
	}
	return nil
}

// SPLN-33865 单个修正规则返回
type DeltaReply struct {
	QueryId              *string  `protobuf:"bytes,1,req,name=query_id,json=queryId" json:"query_id,omitempty"`
	Retcode              *int32   `protobuf:"varint,2,opt,name=retcode" json:"retcode,omitempty"`
	Message              *string  `protobuf:"bytes,3,opt,name=message" json:"message,omitempty"`
	DeltaMin             *float64 `protobuf:"fixed64,4,opt,name=delta_min,json=deltaMin" json:"delta_min,omitempty"`
	DeltaMax             *float64 `protobuf:"fixed64,5,opt,name=delta_max,json=deltaMax" json:"delta_max,omitempty"`
	EffectTime           *uint32  `protobuf:"varint,6,opt,name=effect_time,json=effectTime" json:"effect_time,omitempty"`
	ExpireTime           *uint32  `protobuf:"varint,7,opt,name=expire_time,json=expireTime" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeltaReply) Reset()         { *m = DeltaReply{} }
func (m *DeltaReply) String() string { return proto.CompactTextString(m) }
func (*DeltaReply) ProtoMessage()    {}
func (*DeltaReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{52}
}

func (m *DeltaReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeltaReply.Unmarshal(m, b)
}
func (m *DeltaReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeltaReply.Marshal(b, m, deterministic)
}
func (m *DeltaReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeltaReply.Merge(m, src)
}
func (m *DeltaReply) XXX_Size() int {
	return xxx_messageInfo_DeltaReply.Size(m)
}
func (m *DeltaReply) XXX_DiscardUnknown() {
	xxx_messageInfo_DeltaReply.DiscardUnknown(m)
}

var xxx_messageInfo_DeltaReply proto.InternalMessageInfo

func (m *DeltaReply) GetQueryId() string {
	if m != nil && m.QueryId != nil {
		return *m.QueryId
	}
	return ""
}

func (m *DeltaReply) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *DeltaReply) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *DeltaReply) GetDeltaMin() float64 {
	if m != nil && m.DeltaMin != nil {
		return *m.DeltaMin
	}
	return 0
}

func (m *DeltaReply) GetDeltaMax() float64 {
	if m != nil && m.DeltaMax != nil {
		return *m.DeltaMax
	}
	return 0
}

func (m *DeltaReply) GetEffectTime() uint32 {
	if m != nil && m.EffectTime != nil {
		return *m.EffectTime
	}
	return 0
}

func (m *DeltaReply) GetExpireTime() uint32 {
	if m != nil && m.ExpireTime != nil {
		return *m.ExpireTime
	}
	return 0
}

type QueryManualManipulationResponse struct {
	RespHeader           *RespHeader            `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	DeltaReplyMap        map[string]*DeltaReply `protobuf:"bytes,2,rep,name=delta_reply_map,json=deltaReplyMap" json:"delta_reply_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *QueryManualManipulationResponse) Reset()         { *m = QueryManualManipulationResponse{} }
func (m *QueryManualManipulationResponse) String() string { return proto.CompactTextString(m) }
func (*QueryManualManipulationResponse) ProtoMessage()    {}
func (*QueryManualManipulationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{53}
}

func (m *QueryManualManipulationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryManualManipulationResponse.Unmarshal(m, b)
}
func (m *QueryManualManipulationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryManualManipulationResponse.Marshal(b, m, deterministic)
}
func (m *QueryManualManipulationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryManualManipulationResponse.Merge(m, src)
}
func (m *QueryManualManipulationResponse) XXX_Size() int {
	return xxx_messageInfo_QueryManualManipulationResponse.Size(m)
}
func (m *QueryManualManipulationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryManualManipulationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryManualManipulationResponse proto.InternalMessageInfo

func (m *QueryManualManipulationResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *QueryManualManipulationResponse) GetDeltaReplyMap() map[string]*DeltaReply {
	if m != nil {
		return m.DeltaReplyMap
	}
	return nil
}

type GetFallbackEddInfoRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	Region               *string    `protobuf:"bytes,2,req,name=region" json:"region,omitempty"`
	ForderId             *uint64    `protobuf:"varint,3,req,name=forder_id,json=forderId" json:"forder_id,omitempty"`
	DaysToDelivery       *uint32    `protobuf:"varint,4,req,name=days_to_delivery,json=daysToDelivery" json:"days_to_delivery,omitempty"`
	RequestTime          *uint32    `protobuf:"varint,5,opt,name=request_time,json=requestTime" json:"request_time,omitempty"`
	SlsTn                *string    `protobuf:"bytes,6,opt,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetFallbackEddInfoRequest) Reset()         { *m = GetFallbackEddInfoRequest{} }
func (m *GetFallbackEddInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetFallbackEddInfoRequest) ProtoMessage()    {}
func (*GetFallbackEddInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{54}
}

func (m *GetFallbackEddInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFallbackEddInfoRequest.Unmarshal(m, b)
}
func (m *GetFallbackEddInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFallbackEddInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetFallbackEddInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFallbackEddInfoRequest.Merge(m, src)
}
func (m *GetFallbackEddInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetFallbackEddInfoRequest.Size(m)
}
func (m *GetFallbackEddInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFallbackEddInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFallbackEddInfoRequest proto.InternalMessageInfo

func (m *GetFallbackEddInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetFallbackEddInfoRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *GetFallbackEddInfoRequest) GetForderId() uint64 {
	if m != nil && m.ForderId != nil {
		return *m.ForderId
	}
	return 0
}

func (m *GetFallbackEddInfoRequest) GetDaysToDelivery() uint32 {
	if m != nil && m.DaysToDelivery != nil {
		return *m.DaysToDelivery
	}
	return 0
}

func (m *GetFallbackEddInfoRequest) GetRequestTime() uint32 {
	if m != nil && m.RequestTime != nil {
		return *m.RequestTime
	}
	return 0
}

func (m *GetFallbackEddInfoRequest) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

type GetFallbackEddInfoResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	EddMin               *uint32     `protobuf:"varint,2,opt,name=edd_min,json=eddMin" json:"edd_min,omitempty"`
	EddMax               *uint32     `protobuf:"varint,3,opt,name=edd_max,json=eddMax" json:"edd_max,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetFallbackEddInfoResponse) Reset()         { *m = GetFallbackEddInfoResponse{} }
func (m *GetFallbackEddInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetFallbackEddInfoResponse) ProtoMessage()    {}
func (*GetFallbackEddInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_40e3c3afd1113fea, []int{55}
}

func (m *GetFallbackEddInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFallbackEddInfoResponse.Unmarshal(m, b)
}
func (m *GetFallbackEddInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFallbackEddInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetFallbackEddInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFallbackEddInfoResponse.Merge(m, src)
}
func (m *GetFallbackEddInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetFallbackEddInfoResponse.Size(m)
}
func (m *GetFallbackEddInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFallbackEddInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFallbackEddInfoResponse proto.InternalMessageInfo

func (m *GetFallbackEddInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetFallbackEddInfoResponse) GetEddMin() uint32 {
	if m != nil && m.EddMin != nil {
		return *m.EddMin
	}
	return 0
}

func (m *GetFallbackEddInfoResponse) GetEddMax() uint32 {
	if m != nil && m.EddMax != nil {
		return *m.EddMax
	}
	return 0
}

func init() {
	proto.RegisterType((*AddressInfo)(nil), "lcos_protobuf.AddressInfo")
	proto.RegisterType((*CdtSkus)(nil), "lcos_protobuf.CdtSkus")
	proto.RegisterType((*SimpleLineInfo)(nil), "lcos_protobuf.SimpleLineInfo")
	proto.RegisterType((*ProductInfo)(nil), "lcos_protobuf.ProductInfo")
	proto.RegisterType((*FChannelInfo)(nil), "lcos_protobuf.FChannelInfo")
	proto.RegisterType((*Error)(nil), "lcos_protobuf.Error")
	proto.RegisterType((*CdtReply)(nil), "lcos_protobuf.CdtReply")
	proto.RegisterType((*FChannelCdtAndVolumeInfo)(nil), "lcos_protobuf.FChannelCdtAndVolumeInfo")
	proto.RegisterType((*AllLevelCdtInfoWithLocationLevel)(nil), "lcos_protobuf.AllLevelCdtInfoWithLocationLevel")
	proto.RegisterType((*AllLevelVolumeInfoWithLocationLevel)(nil), "lcos_protobuf.AllLevelVolumeInfoWithLocationLevel")
	proto.RegisterType((*DayAndTime)(nil), "lcos_protobuf.DayAndTime")
	proto.RegisterType((*QueryCdtInfoRequest)(nil), "lcos_protobuf.QueryCdtInfoRequest")
	proto.RegisterType((*QueryCdtInfoForTrackingRequest)(nil), "lcos_protobuf.QueryCdtInfoForTrackingRequest")
	proto.RegisterType((*QueryCdtInfoRequestResponse)(nil), "lcos_protobuf.QueryCdtInfoRequestResponse")
	proto.RegisterType((*QueryCdtInfoForTrackingResponse)(nil), "lcos_protobuf.QueryCdtInfoForTrackingResponse")
	proto.RegisterType((*QueryEventCodeRequest)(nil), "lcos_protobuf.QueryEventCodeRequest")
	proto.RegisterType((*QueryEventCodeResponse)(nil), "lcos_protobuf.QueryEventCodeResponse")
	proto.RegisterType((*CdtQueryInfo)(nil), "lcos_protobuf.CdtQueryInfo")
	proto.RegisterType((*AutoUpdateProcess)(nil), "lcos_protobuf.AutoUpdateProcess")
	proto.RegisterType((*ManualUpdateProcess)(nil), "lcos_protobuf.ManualUpdateProcess")
	proto.RegisterType((*ManualManipulationProcess)(nil), "lcos_protobuf.ManualManipulationProcess")
	proto.RegisterType((*CDTProcess)(nil), "lcos_protobuf.CDTProcess")
	proto.RegisterType((*NonWorkingDayProcess)(nil), "lcos_protobuf.NonWorkingDayProcess")
	proto.RegisterType((*EDDProcess)(nil), "lcos_protobuf.EDDProcess")
	proto.RegisterType((*GetEddInfoResponse)(nil), "lcos_protobuf.GetEddInfoResponse")
	proto.RegisterType((*SingleEDDExtension)(nil), "lcos_protobuf.SingleEDDExtension")
	proto.RegisterType((*GetExtendedEDDRequest)(nil), "lcos_protobuf.GetExtendedEDDRequest")
	proto.RegisterType((*SingleEDDResponse)(nil), "lcos_protobuf.SingleEDDResponse")
	proto.RegisterType((*GetExtendedEDDResponse)(nil), "lcos_protobuf.GetExtendedEDDResponse")
	proto.RegisterMapType((map[string]*SingleEDDResponse)(nil), "lcos_protobuf.GetExtendedEDDResponse.ExtendedEddResponseMapEntry")
	proto.RegisterType((*GetEddCalculationInfoRequest)(nil), "lcos_protobuf.GetEddCalculationInfoRequest")
	proto.RegisterType((*EDDCalculationInfo)(nil), "lcos_protobuf.EDDCalculationInfo")
	proto.RegisterType((*DDLCalculationInfo)(nil), "lcos_protobuf.DDLCalculationInfo")
	proto.RegisterType((*GetEddCalculationInfoResponse)(nil), "lcos_protobuf.GetEddCalculationInfoResponse")
	proto.RegisterType((*GetEddInfoByAlgoRequest)(nil), "lcos_protobuf.GetEddInfoByAlgoRequest")
	proto.RegisterType((*AlgoResult)(nil), "lcos_protobuf.AlgoResult")
	proto.RegisterType((*GetEddInfoByAlgoResponse)(nil), "lcos_protobuf.GetEddInfoByAlgoResponse")
	proto.RegisterType((*QueryInfo)(nil), "lcos_protobuf.QueryInfo")
	proto.RegisterType((*QueryEDDInfoRequest)(nil), "lcos_protobuf.QueryEDDInfoRequest")
	proto.RegisterType((*EDDInfo)(nil), "lcos_protobuf.EDDInfo")
	proto.RegisterType((*QueryEDDInfoResponse)(nil), "lcos_protobuf.QueryEDDInfoResponse")
	proto.RegisterMapType((map[string]*EDDInfo)(nil), "lcos_protobuf.QueryEDDInfoResponse.EddInfoMapEntry")
	proto.RegisterType((*ListMChannelRuleByRegionRequest)(nil), "lcos_protobuf.ListMChannelRuleByRegionRequest")
	proto.RegisterType((*MChannelRuleByRegionResponse)(nil), "lcos_protobuf.MChannelRuleByRegionResponse")
	proto.RegisterType((*MChannelRuleList)(nil), "lcos_protobuf.MChannelRuleList")
	proto.RegisterType((*ListMChannelGreyConfigByRegionRequest)(nil), "lcos_protobuf.ListMChannelGreyConfigByRegionRequest")
	proto.RegisterType((*MChannelGreyConfigByRegionResponse)(nil), "lcos_protobuf.MChannelGreyConfigByRegionResponse")
	proto.RegisterType((*MChannelGreyConfigList)(nil), "lcos_protobuf.MChannelGreyConfigList")
	proto.RegisterType((*ListABTestRuleByRegionRequest)(nil), "lcos_protobuf.ListABTestRuleByRegionRequest")
	proto.RegisterType((*ListABTestRuleByRegionResponse)(nil), "lcos_protobuf.ListABTestRuleByRegionResponse")
	proto.RegisterType((*ABTestRuleList)(nil), "lcos_protobuf.ABTestRuleList")
	proto.RegisterType((*ABTestRuleDetails)(nil), "lcos_protobuf.ABTestRuleDetails")
	proto.RegisterType((*SimpleProductInfo)(nil), "lcos_protobuf.SimpleProductInfo")
	proto.RegisterType((*QueryManualManipulationRequest)(nil), "lcos_protobuf.QueryManualManipulationRequest")
	proto.RegisterType((*DeltaReply)(nil), "lcos_protobuf.DeltaReply")
	proto.RegisterType((*QueryManualManipulationResponse)(nil), "lcos_protobuf.QueryManualManipulationResponse")
	proto.RegisterMapType((map[string]*DeltaReply)(nil), "lcos_protobuf.QueryManualManipulationResponse.DeltaReplyMapEntry")
	proto.RegisterType((*GetFallbackEddInfoRequest)(nil), "lcos_protobuf.GetFallbackEddInfoRequest")
	proto.RegisterType((*GetFallbackEddInfoResponse)(nil), "lcos_protobuf.GetFallbackEddInfoResponse")
}

func init() {
	proto.RegisterFile("cdt_calculation.proto", fileDescriptor_40e3c3afd1113fea)
}

var fileDescriptor_40e3c3afd1113fea = []byte{
	// 4066 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3a, 0x4b, 0x6c, 0x24, 0x49,
	0x56, 0x9d, 0xf5, 0x71, 0x55, 0xbd, 0x2a, 0x97, 0xed, 0xf4, 0xaf, 0x5c, 0xde, 0xb6, 0xdd, 0xd9,
	0x33, 0x3b, 0xde, 0x99, 0xe9, 0x1e, 0xda, 0xb3, 0x2c, 0x30, 0x33, 0x2b, 0xd6, 0x5d, 0xe5, 0xee,
	0xb6, 0xb0, 0x87, 0xd9, 0x6c, 0xf7, 0x8c, 0x04, 0xd2, 0xe6, 0x64, 0x65, 0x84, 0xdd, 0xb9, 0xce,
	0xca, 0xac, 0xce, 0x8f, 0xbb, 0x4a, 0xac, 0xd0, 0xde, 0x90, 0x86, 0x0b, 0x12, 0x17, 0xe0, 0xb0,
	0x02, 0x21, 0x90, 0xe6, 0x82, 0x84, 0x38, 0xc0, 0x9e, 0x90, 0x10, 0x12, 0x9c, 0x38, 0x20, 0x21,
	0xce, 0xec, 0x15, 0xed, 0x99, 0x2b, 0x8a, 0x17, 0x91, 0x99, 0x91, 0x59, 0x59, 0xb6, 0x7b, 0xdb,
	0x8d, 0x96, 0x53, 0x55, 0xbe, 0x78, 0xf1, 0xe2, 0xc5, 0xfb, 0xc5, 0x7b, 0x2f, 0x02, 0x56, 0x2d,
	0x12, 0x1a, 0x96, 0xe9, 0x58, 0x91, 0x63, 0x86, 0xb6, 0xe7, 0xde, 0x1f, 0xf9, 0x5e, 0xe8, 0xa9,
	0xf3, 0x8e, 0xe5, 0x05, 0x06, 0xfe, 0x1f, 0x44, 0xa7, 0xdd, 0x05, 0xfc, 0x1c, 0x98, 0x01, 0xe5,
	0xe3, 0xda, 0xdf, 0x2a, 0xd0, 0xdc, 0x27, 0xc4, 0xa7, 0x41, 0x70, 0xe8, 0x9e, 0x7a, 0xea, 0xbb,
	0xb0, 0x14, 0x84, 0x66, 0x48, 0x0d, 0xc7, 0xb3, 0x90, 0x8e, 0x61, 0x93, 0x8e, 0xb2, 0xa3, 0xec,
	0x56, 0xf5, 0x05, 0x1c, 0x38, 0x12, 0xf0, 0x43, 0xa2, 0xee, 0xc2, 0xa2, 0x65, 0x87, 0x93, 0x0c,
	0x6a, 0x09, 0x51, 0xdb, 0x0c, 0x2e, 0x61, 0xfe, 0x0a, 0xac, 0x10, 0x3b, 0x08, 0x7d, 0xdb, 0x0a,
	0x33, 0xd8, 0x65, 0xc4, 0x56, 0xe3, 0x31, 0x69, 0x46, 0x17, 0xea, 0x23, 0x2f, 0x08, 0x2d, 0x8f,
	0xd0, 0x4e, 0x65, 0x47, 0xd9, 0x6d, 0xe8, 0xc9, 0xb7, 0xf6, 0x05, 0xd4, 0x7a, 0x24, 0x7c, 0x7a,
	0x1e, 0x05, 0xea, 0x3a, 0xd4, 0x82, 0xe7, 0xde, 0x88, 0x33, 0x59, 0xda, 0x6d, 0xe8, 0x73, 0xec,
	0xf3, 0x90, 0xa8, 0x1f, 0x42, 0x23, 0x38, 0x8f, 0x0c, 0x3b, 0xa4, 0xc3, 0xa0, 0x53, 0xda, 0x29,
	0xef, 0x36, 0xf7, 0xd6, 0xee, 0x67, 0x64, 0x71, 0xff, 0xe9, 0x79, 0xc4, 0xb6, 0xac, 0xd7, 0x83,
	0xf3, 0xe8, 0x90, 0xe1, 0x69, 0xc7, 0xd0, 0x7e, 0x6a, 0x0f, 0x47, 0x0e, 0x3d, 0xb2, 0x5d, 0x8a,
	0xe2, 0x58, 0x87, 0x9a, 0x63, 0xbb, 0x54, 0xa2, 0xcf, 0x3e, 0x0f, 0x89, 0xaa, 0xc1, 0x3c, 0x0e,
	0x04, 0xd1, 0xc0, 0x08, 0x27, 0x23, 0xda, 0x29, 0xed, 0x94, 0x76, 0xe7, 0xf5, 0x26, 0x03, 0x3e,
	0x8d, 0x06, 0x27, 0x93, 0x11, 0xd5, 0xfe, 0xb4, 0x0e, 0xcd, 0xcf, 0x7c, 0x8f, 0x44, 0x56, 0x88,
	0xc4, 0x36, 0xa0, 0xfe, 0x22, 0xa2, 0xfe, 0x24, 0xa5, 0x56, 0xc3, 0xef, 0x43, 0xa2, 0xde, 0x06,
	0x18, 0x71, 0x4c, 0x2e, 0x44, 0x36, 0xd8, 0x10, 0x90, 0x43, 0xa2, 0x2e, 0x43, 0xd5, 0x0e, 0x0c,
	0x6b, 0xd0, 0x29, 0xef, 0x94, 0x76, 0xab, 0x7a, 0xc5, 0x0e, 0x7a, 0x03, 0x75, 0x07, 0x5a, 0x76,
	0x60, 0x04, 0x36, 0x53, 0x96, 0xed, 0x32, 0x31, 0xb1, 0x31, 0xb0, 0x83, 0xa7, 0x76, 0x88, 0x3b,
	0x10, 0xd3, 0x9c, 0x61, 0xa7, 0x1a, 0x4f, 0x3b, 0x1a, 0xaa, 0x6b, 0x30, 0xe7, 0xd3, 0x33, 0xdb,
	0x73, 0x3b, 0x73, 0x7c, 0x47, 0xfc, 0x4b, 0x7d, 0x0b, 0xda, 0xe1, 0xc8, 0x31, 0x22, 0xd7, 0x7e,
	0x11, 0x51, 0xe3, 0x9c, 0x4e, 0x3a, 0x35, 0x94, 0x7b, 0x2b, 0x1c, 0x39, 0xcf, 0x10, 0xf8, 0x5b,
	0x74, 0xa2, 0x7e, 0x0c, 0xcd, 0x80, 0x3a, 0x0e, 0xf5, 0x0d, 0x93, 0x10, 0xbf, 0x53, 0xdf, 0x29,
	0xed, 0x36, 0xf7, 0xba, 0x39, 0xc9, 0x4a, 0x06, 0xa5, 0x03, 0x47, 0x67, 0x20, 0xf5, 0x37, 0x00,
	0x06, 0xd1, 0x24, 0x9e, 0xdb, 0xb8, 0x72, 0x6e, 0x03, 0xb1, 0x71, 0xea, 0x03, 0xa8, 0xa3, 0x3e,
	0xdd, 0x53, 0xaf, 0x03, 0x3b, 0x4a, 0x81, 0x3a, 0x85, 0x49, 0xe8, 0xb5, 0x80, 0xeb, 0x55, 0xdd,
	0x84, 0x86, 0x63, 0xba, 0xd4, 0x40, 0x1b, 0x6a, 0x72, 0x1b, 0x62, 0x80, 0x9e, 0x47, 0xa8, 0xfa,
	0x11, 0x34, 0x50, 0x7f, 0x8e, 0x1d, 0x84, 0x9d, 0x16, 0xda, 0xc7, 0xed, 0xbc, 0x7d, 0x64, 0x4c,
	0x41, 0xaf, 0x33, 0xfc, 0x23, 0x3b, 0x08, 0xd5, 0x3b, 0xd0, 0x8a, 0x46, 0x84, 0x39, 0x09, 0xbd,
	0xa0, 0x6e, 0xd8, 0x99, 0xdf, 0x51, 0x98, 0xea, 0x39, 0xec, 0x80, 0x81, 0x98, 0x3e, 0x5d, 0x3a,
	0x0e, 0x05, 0x42, 0x1b, 0x11, 0x1a, 0x0c, 0xc2, 0x87, 0xd7, 0xa1, 0xc6, 0x2c, 0x93, 0xe9, 0x7a,
	0x61, 0x47, 0xd9, 0xad, 0xe8, 0x73, 0xec, 0xf3, 0x90, 0x30, 0x13, 0xe1, 0x12, 0xb2, 0x49, 0x67,
	0x11, 0x47, 0x6a, 0xf8, 0x7d, 0x48, 0xd4, 0x8f, 0xa0, 0x1e, 0x58, 0xd4, 0x35, 0x7d, 0xdb, 0xeb,
	0x2c, 0xed, 0x28, 0xbb, 0xed, 0xbd, 0xad, 0x02, 0x09, 0x08, 0x8c, 0x03, 0x37, 0x1a, 0xea, 0x09,
	0xbe, 0x7a, 0x0a, 0x77, 0xcc, 0x0b, 0xd3, 0x76, 0xcc, 0x81, 0x43, 0x8d, 0xd3, 0xc8, 0x39, 0xb5,
	0x1d, 0x67, 0x48, 0xdd, 0xd0, 0xb0, 0x9e, 0x9b, 0xae, 0x4b, 0x1d, 0xc3, 0x26, 0x41, 0x47, 0x45,
	0x29, 0x6c, 0xe6, 0x88, 0x3e, 0xea, 0x71, 0x14, 0x94, 0xc1, 0x56, 0x42, 0xe5, 0x51, 0x4a, 0x24,
	0x46, 0x20, 0x81, 0xba, 0x0d, 0x4d, 0x97, 0x52, 0x62, 0x5c, 0x78, 0x4e, 0x34, 0xa4, 0x9d, 0x65,
	0x74, 0x6f, 0x60, 0xa0, 0xcf, 0x11, 0xc2, 0x74, 0x12, 0x84, 0xa6, 0x1f, 0x1a, 0xc4, 0x9c, 0x74,
	0x56, 0x50, 0x2c, 0x75, 0x04, 0xf4, 0xcd, 0x09, 0x13, 0x1a, 0x1f, 0x7c, 0xee, 0x45, 0x7e, 0x67,
	0x95, 0x0b, 0x0d, 0x21, 0x4f, 0xbc, 0xc8, 0x67, 0x42, 0xa3, 0x2e, 0xc1, 0x99, 0x6b, 0x38, 0x36,
	0x47, 0x5d, 0xc2, 0xe6, 0x6d, 0x40, 0x9d, 0x0d, 0xe0, 0xac, 0x75, 0x1c, 0x61, 0x88, 0x38, 0x67,
	0x13, 0x1a, 0x67, 0xbe, 0x17, 0x8d, 0x8c, 0xd0, 0x3c, 0xeb, 0x74, 0xf8, 0x7a, 0x08, 0x38, 0x31,
	0xcf, 0x98, 0x1e, 0x7d, 0xfa, 0x22, 0xa2, 0x41, 0x68, 0x84, 0xf6, 0x90, 0x76, 0x36, 0xb8, 0x1e,
	0x05, 0xec, 0xc4, 0x1e, 0x52, 0x16, 0xe2, 0x38, 0x4b, 0xa7, 0xcc, 0x8a, 0xb8, 0xa7, 0x77, 0x11,
	0xad, 0x8d, 0xf0, 0x47, 0x0c, 0xcc, 0x9c, 0x5d, 0xbd, 0x07, 0x55, 0x26, 0x6e, 0xda, 0xd9, 0x44,
	0xdd, 0xac, 0x17, 0xeb, 0x86, 0xea, 0x1c, 0x4b, 0xfb, 0x3d, 0x68, 0xc9, 0x92, 0x55, 0xbf, 0x0d,
	0x6b, 0xc5, 0x7a, 0x11, 0x91, 0x62, 0xe5, 0xb4, 0x40, 0xe0, 0x69, 0x5c, 0x28, 0x5d, 0x12, 0x17,
	0xca, 0xf9, 0xb8, 0xa0, 0x7d, 0x0c, 0xd5, 0x03, 0xdf, 0xf7, 0x7c, 0xb5, 0x03, 0x35, 0x9f, 0xf2,
	0x20, 0xab, 0x20, 0x56, 0xfc, 0xc9, 0x46, 0x86, 0x34, 0x08, 0xcc, 0x33, 0x8a, 0x21, 0xbd, 0xa1,
	0xc7, 0x9f, 0xda, 0x4f, 0x2b, 0x50, 0xef, 0x91, 0x50, 0xa7, 0x23, 0x67, 0x72, 0x59, 0x48, 0x5b,
	0x87, 0x1a, 0x3b, 0x92, 0x86, 0xb6, 0x8b, 0x14, 0x14, 0x7d, 0xce, 0x22, 0xe1, 0xb1, 0xed, 0x26,
	0x03, 0xe6, 0x18, 0xe3, 0xbf, 0x18, 0x30, 0xc7, 0x6a, 0x17, 0x1a, 0xd6, 0xc0, 0x70, 0x86, 0x38,
	0x54, 0xc1, 0xa1, 0x9a, 0x35, 0x38, 0x1a, 0xb2, 0xb1, 0xbb, 0x30, 0xef, 0x5d, 0x50, 0xdf, 0xb7,
	0x63, 0x2d, 0x54, 0x79, 0x70, 0x8a, 0x81, 0xa8, 0x83, 0x77, 0xa1, 0x4a, 0xd9, 0xbe, 0x3a, 0x73,
	0x18, 0x21, 0x56, 0x72, 0x3a, 0xc0, 0x3d, 0xeb, 0x1c, 0x45, 0x76, 0xc1, 0x5a, 0xc6, 0x05, 0x7f,
	0x00, 0xeb, 0xa7, 0xb1, 0xf8, 0x19, 0x9f, 0xa6, 0x9b, 0xd8, 0x73, 0x1d, 0x3d, 0xe4, 0x9d, 0x19,
	0x1e, 0xd2, 0x23, 0xe1, 0xbe, 0x2b, 0x0c, 0x1d, 0xbd, 0x65, 0x25, 0xa6, 0x23, 0x8f, 0xa8, 0x1f,
	0x43, 0x8b, 0x98, 0x13, 0x24, 0x8b, 0x56, 0xd7, 0x40, 0xa2, 0x1b, 0x39, 0xa2, 0x7d, 0x73, 0xb2,
	0xef, 0x12, 0x66, 0x83, 0x3a, 0x90, 0xe4, 0x3f, 0x3b, 0x48, 0x63, 0x93, 0xcd, 0x84, 0x20, 0x40,
	0x9b, 0x54, 0xc5, 0xd8, 0x33, 0x29, 0x12, 0xfd, 0x32, 0x79, 0x9c, 0xf6, 0x75, 0x09, 0x3a, 0xb3,
	0xc4, 0xf5, 0x8b, 0x18, 0xe3, 0x25, 0x6e, 0x53, 0x46, 0xc4, 0x62, 0xb7, 0x39, 0x82, 0x06, 0xd3,
	0x2c, 0x3b, 0x4c, 0x82, 0x4e, 0x05, 0xe5, 0xff, 0x41, 0xfe, 0x18, 0x72, 0x9c, 0x23, 0x7a, 0x81,
	0x5c, 0x32, 0xe6, 0xbe, 0xb0, 0xc3, 0xe7, 0x71, 0x76, 0x82, 0x70, 0xbd, 0x6e, 0xf1, 0x91, 0x40,
	0x7d, 0x06, 0x2d, 0x6e, 0x1f, 0x82, 0x60, 0x15, 0x09, 0xee, 0xcd, 0x20, 0x98, 0x6e, 0x78, 0x9a,
	0x66, 0xf3, 0x22, 0x19, 0x0c, 0xb4, 0x7f, 0x2d, 0xc1, 0xce, 0x55, 0x5c, 0xc8, 0x4e, 0xa6, 0xcc,
	0x72, 0xb2, 0x52, 0xc6, 0xc9, 0xbe, 0x80, 0x55, 0xcf, 0xb7, 0xcf, 0x6c, 0x37, 0x4d, 0xc4, 0x1c,
	0x46, 0x0a, 0x05, 0xd6, 0xde, 0xbb, 0x3b, 0x1d, 0xb7, 0x32, 0x2b, 0xe2, 0xc1, 0xb2, 0xcc, 0x29,
	0x64, 0x59, 0x79, 0x0a, 0xcb, 0x84, 0xd9, 0x65, 0x8e, 0x6c, 0xe5, 0xfa, 0x64, 0x97, 0xd8, 0xfc,
	0x2c, 0xd1, 0x3e, 0x2c, 0xc9, 0xce, 0xc2, 0x8f, 0xeb, 0xea, 0x55, 0x1e, 0xd3, 0x4e, 0x3d, 0x86,
	0x1d, 0xd8, 0xda, 0x7f, 0x2b, 0x70, 0xf7, 0x1a, 0xf2, 0x67, 0xa9, 0x91, 0xf0, 0x74, 0x85, 0x87,
	0x04, 0xfe, 0x35, 0x5b, 0x66, 0xa5, 0x37, 0x23, 0xb3, 0xf2, 0xeb, 0xc8, 0x4c, 0xfb, 0x4a, 0x01,
	0x48, 0x85, 0xa1, 0x2e, 0x42, 0x99, 0x39, 0xb0, 0x82, 0xf9, 0x29, 0xfb, 0x9b, 0xf3, 0xfa, 0x0a,
	0x0e, 0x48, 0x5e, 0x2f, 0x3b, 0x77, 0x15, 0x07, 0x13, 0xe7, 0x96, 0xcc, 0x8d, 0x25, 0x8f, 0x85,
	0xe6, 0x56, 0x4b, 0x07, 0xcc, 0xb1, 0xf6, 0x37, 0x0a, 0x2c, 0x7f, 0x9f, 0x9d, 0x08, 0xc2, 0x84,
	0x75, 0x1e, 0xa1, 0xd4, 0x5f, 0x03, 0xf0, 0xe9, 0x0b, 0xe3, 0x39, 0x35, 0x09, 0xf5, 0x91, 0xb9,
	0xe6, 0x5e, 0x27, 0xb7, 0x61, 0x9d, 0xbe, 0x78, 0x82, 0xe3, 0x7a, 0xc3, 0x8f, 0xff, 0xaa, 0xdf,
	0x85, 0x56, 0x92, 0x29, 0xb3, 0x64, 0x90, 0xe7, 0xf6, 0xf9, 0x2c, 0x52, 0x4a, 0xbb, 0xf5, 0xe6,
	0x48, 0xca, 0xc1, 0xb7, 0xa1, 0xe9, 0x0d, 0x7e, 0x48, 0xad, 0x90, 0x9f, 0x22, 0x65, 0x0c, 0x50,
	0xc0, 0x41, 0x98, 0xb4, 0xff, 0x4c, 0x81, 0x2d, 0x99, 0xe1, 0x47, 0x9e, 0x7f, 0xe2, 0x9b, 0xd6,
	0xb9, 0xed, 0x9e, 0xbd, 0x01, 0xde, 0x4b, 0xaf, 0xc2, 0xfb, 0x7b, 0xa0, 0xf2, 0xf3, 0xd1, 0x76,
	0x07, 0x5e, 0x84, 0x81, 0x39, 0xe4, 0xc7, 0xfb, 0xbc, 0xbe, 0xc0, 0x0e, 0xca, 0x43, 0x0e, 0xef,
	0x9b, 0x21, 0x66, 0x5a, 0xa7, 0x9e, 0x4f, 0x78, 0x2a, 0x59, 0x41, 0x73, 0xae, 0x73, 0xc0, 0x21,
	0xd1, 0xfe, 0x44, 0x81, 0xcd, 0x02, 0xad, 0xe8, 0x34, 0x18, 0x79, 0x6e, 0xc0, 0xb2, 0xe3, 0xa6,
	0x4f, 0x83, 0x51, 0x76, 0x8b, 0x1b, 0x53, 0x5b, 0x0c, 0x46, 0x62, 0x8f, 0xe0, 0x27, 0xff, 0xd5,
	0xef, 0x42, 0x9b, 0x99, 0x82, 0xcf, 0xf2, 0x03, 0xee, 0xaf, 0x5c, 0x45, 0x05, 0x19, 0x11, 0xe6,
	0x10, 0x7a, 0xcb, 0x12, 0xff, 0xd0, 0x57, 0xbf, 0x56, 0x60, 0x7b, 0xa6, 0xfc, 0x6f, 0x80, 0xbd,
	0x6d, 0x68, 0x71, 0x21, 0x5a, 0x03, 0x29, 0x3a, 0x36, 0x98, 0xf8, 0x7a, 0x03, 0x16, 0x20, 0xbf,
	0x05, 0x4b, 0x1c, 0xe1, 0xb9, 0xe7, 0xd8, 0x2c, 0xf8, 0xd0, 0x71, 0x28, 0x12, 0x95, 0x36, 0xc3,
	0x7a, 0xc2, 0xc1, 0x07, 0xe3, 0x50, 0xf3, 0x61, 0x15, 0x59, 0xc5, 0x93, 0x96, 0x95, 0x15, 0xaf,
	0x6d, 0x21, 0xf9, 0xdc, 0x8d, 0x57, 0x95, 0x72, 0xee, 0xf6, 0xfb, 0xb0, 0x96, 0x5f, 0xf3, 0x06,
	0xa4, 0xb2, 0x0b, 0x8b, 0xa6, 0x85, 0x11, 0x08, 0xf3, 0xdc, 0x44, 0x6d, 0x0d, 0xbd, 0xcd, 0xe1,
	0x6c, 0x25, 0xd4, 0xcf, 0x3f, 0x54, 0xa0, 0xd5, 0x23, 0x21, 0xf2, 0x80, 0x56, 0x99, 0x2d, 0x5d,
	0x15, 0x3c, 0x76, 0xa5, 0xd2, 0x75, 0xba, 0xac, 0x2c, 0x15, 0x94, 0x95, 0x49, 0xa5, 0xca, 0x3b,
	0x02, 0xbc, 0x52, 0x9d, 0x2e, 0x70, 0x95, 0x5c, 0x81, 0xfb, 0x3e, 0xa8, 0xf9, 0xc0, 0x6c, 0x13,
	0x4c, 0x0d, 0xab, 0xfa, 0x62, 0x36, 0xe0, 0x1e, 0x12, 0xf5, 0x3b, 0xb0, 0xce, 0xa2, 0xa5, 0xed,
	0x8a, 0x58, 0x2b, 0x4d, 0x99, 0xc3, 0x29, 0xab, 0xd2, 0xb0, 0x34, 0xef, 0x21, 0xdc, 0x96, 0xe7,
	0x59, 0x74, 0xe4, 0x9b, 0xee, 0x19, 0x35, 0x92, 0x06, 0x45, 0x0d, 0x67, 0x6f, 0x4a, 0x48, 0x3d,
	0x81, 0xf3, 0x99, 0x40, 0x51, 0x1f, 0xc0, 0x8a, 0x4c, 0x23, 0x99, 0x5a, 0x47, 0x61, 0x2c, 0x4b,
	0x63, 0xc9, 0x94, 0xdb, 0x00, 0x3c, 0xb7, 0xc6, 0x48, 0xd5, 0xe0, 0x82, 0x45, 0x08, 0x26, 0xbb,
	0x99, 0xf2, 0x16, 0x72, 0xe5, 0x6d, 0xbe, 0x44, 0xe5, 0xe5, 0x6f, 0xa6, 0x44, 0xdd, 0x9b, 0x75,
	0xa8, 0xb5, 0x70, 0x37, 0x85, 0xe7, 0xd5, 0x27, 0xd0, 0x2d, 0x94, 0x20, 0x9f, 0x38, 0x8f, 0x13,
	0x3b, 0x05, 0x42, 0xe4, 0x07, 0xd3, 0xdf, 0x29, 0xb0, 0xb4, 0x1f, 0x85, 0x1e, 0x4f, 0x4f, 0x3f,
	0xf3, 0x3d, 0x8b, 0x06, 0x81, 0x7a, 0x0f, 0x96, 0xcd, 0x28, 0xf4, 0xe2, 0x7c, 0xd6, 0x8f, 0x1c,
	0x1a, 0x1b, 0x52, 0x45, 0x5f, 0x34, 0x13, 0x7c, 0x3d, 0x72, 0xe8, 0x2f, 0x54, 0x56, 0x7c, 0x03,
	0x80, 0xb9, 0xbb, 0x18, 0xe3, 0x75, 0x45, 0xdd, 0x19, 0xf6, 0xf8, 0xe8, 0x36, 0x34, 0xd9, 0xd0,
	0x05, 0xf5, 0x03, 0xdb, 0x73, 0xd1, 0x76, 0xca, 0x3a, 0x58, 0x24, 0xfc, 0x9c, 0x43, 0x34, 0x0a,
	0xcb, 0xc7, 0xa6, 0x1b, 0x99, 0x4e, 0x96, 0xed, 0x57, 0xcf, 0xbc, 0xb2, 0x7c, 0x94, 0xb3, 0x7c,
	0x68, 0x7f, 0xae, 0xc0, 0x06, 0x5f, 0xe7, 0xd8, 0x74, 0xed, 0x91, 0xe8, 0xe2, 0xc5, 0xab, 0x69,
	0x30, 0x2f, 0x56, 0x33, 0x08, 0x75, 0x42, 0x53, 0xac, 0xd9, 0xe4, 0x6b, 0xf6, 0x19, 0x28, 0xc1,
	0x31, 0xc7, 0x02, 0xa7, 0x94, 0xe2, 0x98, 0x63, 0x8e, 0xb3, 0x05, 0x4d, 0x1e, 0xdc, 0x38, 0x46,
	0x39, 0x0d, 0x7e, 0x7c, 0x7c, 0x13, 0x1a, 0x3e, 0xb5, 0x3c, 0x9f, 0xc4, 0xa7, 0x46, 0x59, 0xaf,
	0x73, 0xc0, 0x21, 0xd1, 0x7e, 0x56, 0x06, 0xe8, 0xf5, 0x4f, 0x62, 0x9e, 0xf6, 0x79, 0xa0, 0x17,
	0xf5, 0x1f, 0x3b, 0xcf, 0x14, 0x2c, 0xbb, 0x36, 0xa7, 0x03, 0x7d, 0x12, 0x2d, 0x30, 0xd8, 0xa7,
	0xb1, 0xe3, 0x4b, 0xd8, 0x1a, 0xe2, 0x9e, 0x8d, 0xa1, 0xb4, 0x69, 0x99, 0x64, 0xe9, 0x6a, 0x92,
	0x9b, 0xc3, 0x29, 0xb1, 0xa5, 0x2b, 0x7c, 0x96, 0xb5, 0xae, 0x11, 0xe7, 0x1d, 0x37, 0xde, 0xdc,
	0xdb, 0xc9, 0xe7, 0xe8, 0x79, 0xe3, 0xd4, 0x97, 0xcc, 0x29, 0x7b, 0xfd, 0x1c, 0x56, 0x05, 0xcf,
	0x39, 0x9a, 0x15, 0xa4, 0xa9, 0xe5, 0x68, 0x16, 0xd8, 0x8e, 0xbe, 0x3c, 0x2c, 0x30, 0xa8, 0xe7,
	0xb0, 0x59, 0x24, 0x8b, 0x98, 0x7a, 0x15, 0xa9, 0xef, 0x16, 0x52, 0x2f, 0xb0, 0x18, 0x7d, 0x63,
	0x38, 0xd3, 0x98, 0x32, 0x25, 0xda, 0x5c, 0xae, 0x44, 0xfb, 0x4a, 0x81, 0x95, 0x4f, 0x3d, 0xf7,
	0x0b, 0xcf, 0x67, 0x47, 0x6e, 0xdf, 0x9c, 0xc4, 0xb3, 0xde, 0x81, 0x45, 0x4a, 0x08, 0x9a, 0xa0,
	0xfb, 0x92, 0xf0, 0x23, 0x42, 0xc1, 0x23, 0x62, 0x9e, 0x12, 0x72, 0x6c, 0xbb, 0x9f, 0xbe, 0x24,
	0xd8, 0x1e, 0x8b, 0x11, 0xcd, 0x71, 0x8a, 0x58, 0x4a, 0x11, 0xcd, 0x71, 0x8c, 0xb8, 0x03, 0x2d,
	0xd1, 0x5c, 0xe5, 0x48, 0x65, 0x44, 0x02, 0xde, 0x61, 0xc5, 0xc3, 0xe6, 0x3f, 0x14, 0x80, 0x83,
	0x7e, 0x3f, 0x66, 0xe1, 0x23, 0xee, 0xab, 0xb1, 0x48, 0xb8, 0xb9, 0xe5, 0x4f, 0xb8, 0xd4, 0x42,
	0xd1, 0x8d, 0xe3, 0xb9, 0x1f, 0xc0, 0x8a, 0xeb, 0xb9, 0xc6, 0x4b, 0xbe, 0x2f, 0x56, 0xd3, 0xca,
	0x9c, 0x2d, 0xb9, 0xf2, 0x96, 0x91, 0xbb, 0xdf, 0x81, 0xf5, 0xfc, 0x84, 0xac, 0xf5, 0xe4, 0xf3,
	0xf3, 0x22, 0xa9, 0xe9, 0x2b, 0x6e, 0x01, 0x54, 0xfb, 0x7b, 0x05, 0xd4, 0xc7, 0x34, 0x3c, 0x20,
	0x84, 0x67, 0x5f, 0x37, 0x70, 0x82, 0x2f, 0x42, 0x99, 0x12, 0xde, 0x7f, 0x2f, 0xeb, 0xec, 0x2f,
	0xa3, 0xc6, 0xf4, 0x90, 0x65, 0x3a, 0x4f, 0x2d, 0x95, 0xae, 0x0e, 0x94, 0x10, 0x29, 0xba, 0x09,
	0x65, 0x8b, 0x28, 0x30, 0xc7, 0x75, 0xac, 0xfd, 0x59, 0x19, 0xd4, 0xa7, 0xb6, 0x7b, 0xe6, 0xd0,
	0x83, 0x7e, 0xff, 0x60, 0x1c, 0x52, 0x97, 0x05, 0x49, 0x66, 0x52, 0xe2, 0x84, 0x4f, 0x1a, 0x41,
	0x75, 0x0e, 0xb8, 0xba, 0xb9, 0x5d, 0x78, 0xe5, 0x50, 0x2e, 0xbe, 0x72, 0x48, 0x9b, 0xd7, 0x95,
	0x4c, 0xf3, 0x7a, 0x0b, 0xc0, 0x24, 0x3f, 0x8c, 0x82, 0x90, 0x15, 0xfa, 0xa2, 0xdd, 0x2d, 0x41,
	0x62, 0xe9, 0xb0, 0xa2, 0x45, 0x48, 0x27, 0xc9, 0x38, 0x6a, 0x52, 0x6f, 0x7c, 0x0b, 0x9a, 0x3c,
	0xe3, 0xe0, 0x09, 0x47, 0x1d, 0x87, 0x1a, 0x98, 0x70, 0x60, 0xbe, 0xf1, 0x9b, 0x39, 0x8b, 0x6d,
	0x5c, 0xa7, 0x71, 0x2c, 0x19, 0xb4, 0xba, 0x0a, 0x73, 0x81, 0x13, 0x18, 0xa1, 0x2b, 0x4e, 0xec,
	0x6a, 0xe0, 0x04, 0x27, 0x2e, 0xcb, 0x63, 0x62, 0xdf, 0x92, 0xb6, 0xd1, 0xc4, 0xe5, 0x17, 0xb9,
	0xe4, 0xf7, 0xd3, 0xcd, 0x48, 0xca, 0x69, 0xe1, 0x86, 0x62, 0xe5, 0xfc, 0x85, 0x02, 0xab, 0xcc,
	0xac, 0x98, 0x5a, 0x08, 0x25, 0x07, 0xfd, 0xfe, 0x6b, 0x27, 0xa4, 0xbf, 0xcd, 0x39, 0xa3, 0xb1,
	0xa6, 0xe5, 0x8c, 0xfe, 0xce, 0xd4, 0xbe, 0xf3, 0x76, 0x81, 0xcc, 0x27, 0x5f, 0xe8, 0xd2, 0x3f,
	0x2f, 0xc1, 0x52, 0x82, 0x98, 0x58, 0xfe, 0x1d, 0x68, 0x51, 0xc1, 0xb5, 0xc1, 0x14, 0xa5, 0xe0,
	0xbe, 0x9a, 0x31, 0xec, 0x80, 0x10, 0xf5, 0x7b, 0x70, 0x3b, 0x41, 0xb9, 0xc4, 0x93, 0x37, 0x62,
	0xa4, 0x4f, 0xa7, 0x3c, 0xfa, 0x2e, 0xcc, 0x27, 0x14, 0x88, 0x39, 0x09, 0x84, 0x91, 0x25, 0x2b,
	0xf7, 0xcd, 0x49, 0x20, 0x69, 0xa8, 0x22, 0x6b, 0x68, 0x17, 0x16, 0x65, 0x06, 0x51, 0xf8, 0x55,
	0x64, 0xb2, 0x2d, 0x31, 0xc9, 0xce, 0xff, 0x4b, 0xe2, 0xc6, 0xdc, 0x6b, 0xc6, 0x0d, 0xf5, 0x01,
	0xac, 0xc6, 0x76, 0x92, 0xdd, 0x09, 0xcf, 0x40, 0x55, 0x6e, 0x07, 0x07, 0xd2, 0x7e, 0xb4, 0x7f,
	0x2c, 0xc1, 0x5a, 0xde, 0x26, 0x6e, 0x20, 0xdc, 0xfc, 0x08, 0x36, 0x32, 0xf2, 0xf0, 0x05, 0x51,
	0x63, 0x68, 0x8e, 0x84, 0x79, 0xec, 0xe7, 0x28, 0x15, 0x73, 0x71, 0xff, 0x20, 0x15, 0x5f, 0x0c,
	0x3b, 0x36, 0x47, 0x07, 0x6e, 0xe8, 0x4f, 0xf4, 0x35, 0x5a, 0x38, 0xd8, 0x3d, 0x87, 0xcd, 0x4b,
	0xa6, 0x31, 0x6f, 0x67, 0x85, 0x06, 0xaf, 0x45, 0xd8, 0x5f, 0xf5, 0x3b, 0x50, 0xbd, 0x30, 0x9d,
	0x88, 0x8a, 0x7c, 0x62, 0x67, 0x96, 0xe5, 0xc6, 0xa4, 0x74, 0x8e, 0xfe, 0x51, 0xe9, 0xd7, 0x15,
	0xed, 0xdf, 0x15, 0xf8, 0x06, 0x0f, 0xd6, 0xbd, 0xf4, 0x7a, 0xf5, 0x0d, 0xf5, 0x32, 0x5e, 0xa9,
	0x1f, 0x70, 0x1b, 0x00, 0xb3, 0x7b, 0xde, 0x47, 0xe6, 0x7d, 0x80, 0x06, 0x42, 0xb0, 0xf1, 0x73,
	0x69, 0x07, 0xe0, 0xdf, 0x14, 0x50, 0x0f, 0xfa, 0xfd, 0xdc, 0x8e, 0x58, 0x2c, 0x4e, 0x02, 0x51,
	0x7c, 0xd5, 0x83, 0x72, 0xac, 0xeb, 0x0b, 0x22, 0x0e, 0xc5, 0x60, 0xf5, 0x9b, 0xb0, 0x80, 0x9a,
	0xc7, 0x5a, 0xc8, 0xb1, 0x87, 0x76, 0x28, 0x4e, 0x1f, 0x76, 0xcc, 0xeb, 0x0c, 0x7a, 0xc4, 0x80,
	0x72, 0xa6, 0x5c, 0x9e, 0x95, 0x29, 0x57, 0x32, 0x99, 0x72, 0x07, 0x6a, 0xa2, 0xf8, 0xc6, 0x5e,
	0x5f, 0x43, 0x8f, 0x3f, 0xd9, 0xc8, 0x4b, 0x4a, 0xcf, 0xa9, 0xcb, 0x62, 0x79, 0x79, 0xb7, 0xaa,
	0xc7, 0x9f, 0xda, 0x3f, 0x2b, 0xa0, 0xf6, 0xfb, 0x47, 0xf9, 0x0d, 0x6d, 0x43, 0xf3, 0xd4, 0xf3,
	0x5f, 0x9a, 0x3e, 0x61, 0x99, 0xb7, 0x48, 0x9b, 0x41, 0x80, 0x7a, 0x04, 0x2f, 0xf3, 0x06, 0xa6,
	0x75, 0x9e, 0x60, 0x88, 0xa4, 0x39, 0x86, 0x31, 0x94, 0x0f, 0x61, 0x2d, 0xbd, 0xcc, 0x33, 0x64,
	0x72, 0x7c, 0x3f, 0xcb, 0xc9, 0xc5, 0xde, 0xa3, 0x94, 0xee, 0xaf, 0xc2, 0xba, 0x34, 0x29, 0xb3,
	0x04, 0xdf, 0xec, 0x4a, 0x32, 0xeb, 0x61, 0xba, 0x96, 0xf6, 0xc7, 0x25, 0xb8, 0x3d, 0xc3, 0xd8,
	0x6e, 0xc0, 0x6b, 0x3f, 0x81, 0x3a, 0x53, 0x99, 0x94, 0x59, 0xdf, 0x99, 0xce, 0x07, 0xf2, 0x0b,
	0xb3, 0xc3, 0x06, 0x65, 0xf9, 0x09, 0xd4, 0x09, 0x71, 0xf8, 0xec, 0x72, 0xe1, 0xec, 0x69, 0x05,
	0xe8, 0x35, 0x42, 0xf8, 0x0d, 0x57, 0x2e, 0x1d, 0xa9, 0xbc, 0x42, 0x3a, 0xa2, 0xfd, 0x8f, 0x02,
	0xeb, 0x69, 0xbe, 0xf4, 0x70, 0xb2, 0xef, 0x9c, 0xfd, 0xb2, 0x7b, 0xdf, 0x7d, 0x58, 0x16, 0xc3,
	0xa2, 0x7b, 0x65, 0x88, 0xb7, 0x0c, 0x2c, 0x6d, 0x59, 0xe2, 0x78, 0x62, 0x04, 0x2b, 0xf6, 0xf4,
	0xdc, 0xa9, 0x22, 0x0a, 0x3f, 0x77, 0xb4, 0x1f, 0x01, 0xf0, 0xcd, 0x06, 0x91, 0x93, 0x39, 0xf9,
	0xc5, 0x73, 0x04, 0xee, 0x94, 0xc9, 0x00, 0x16, 0x9d, 0xc9, 0x80, 0x39, 0x96, 0xb8, 0xe4, 0xed,
	0x4e, 0xcc, 0xbd, 0xf8, 0xea, 0x93, 0x11, 0xcd, 0x6d, 0xa2, 0x82, 0x07, 0x5a, 0xba, 0x09, 0xed,
	0xc7, 0x25, 0xe8, 0x4c, 0xcb, 0xfd, 0x06, 0x0c, 0x51, 0xda, 0x48, 0x49, 0xce, 0x2f, 0xe5, 0x8d,
	0x94, 0xd3, 0x01, 0x73, 0xfc, 0x3a, 0xe6, 0xc3, 0xe6, 0x9a, 0xce, 0x99, 0xc7, 0x0e, 0xa9, 0xc8,
	0x09, 0x45, 0x29, 0xb5, 0x31, 0x75, 0x41, 0x13, 0x8b, 0x59, 0x07, 0x33, 0xf9, 0xaf, 0x3d, 0x83,
	0x46, 0x5a, 0x4d, 0x5e, 0x72, 0xdd, 0x99, 0xea, 0xaf, 0x24, 0xe7, 0x0d, 0x69, 0xc2, 0xca, 0x2f,
	0xa4, 0xc4, 0x97, 0xf6, 0x47, 0x71, 0x5f, 0xfc, 0xa0, 0xdf, 0xbf, 0x91, 0xb3, 0xe4, 0x7b, 0xb0,
	0x90, 0x96, 0xcd, 0x72, 0x96, 0x96, 0x9f, 0x9d, 0x16, 0xce, 0xf3, 0x2f, 0xe2, 0xbf, 0x98, 0x99,
	0xfd, 0x97, 0x02, 0x35, 0xc1, 0x4d, 0x6e, 0xa3, 0x4a, 0xee, 0x5e, 0x37, 0x35, 0x35, 0x59, 0x43,
	0x92, 0x4e, 0xcb, 0x19, 0x9d, 0xae, 0x40, 0xd5, 0x12, 0xf6, 0xc5, 0xc0, 0xfc, 0x23, 0x57, 0x15,
	0x54, 0xf3, 0x7d, 0xc3, 0x54, 0x9e, 0x73, 0xb2, 0x3c, 0xa5, 0x4b, 0x42, 0x9e, 0xf3, 0x24, 0x97,
	0x84, 0x2c, 0xbb, 0xf3, 0x7d, 0xcf, 0x37, 0xe2, 0xab, 0x42, 0xde, 0x5a, 0x6b, 0x21, 0xf0, 0x58,
	0x5c, 0x5e, 0xff, 0xb8, 0x04, 0x2b, 0x59, 0xb1, 0xdf, 0x80, 0x31, 0x3f, 0x83, 0x56, 0x1c, 0x55,
	0xa5, 0xf4, 0xe7, 0xc3, 0x22, 0xb9, 0xe7, 0x96, 0xbd, 0x2f, 0x3c, 0x2b, 0x49, 0x78, 0x80, 0x26,
	0x80, 0xee, 0x33, 0x58, 0xc8, 0x0d, 0x17, 0x24, 0x36, 0xef, 0x67, 0x13, 0x9b, 0xb5, 0x69, 0x87,
	0xc0, 0xf5, 0xa4, 0x74, 0xc6, 0x87, 0x6d, 0xa6, 0xee, 0x63, 0x71, 0x1d, 0xaa, 0x47, 0x0e, 0x7d,
	0x38, 0xd1, 0xd1, 0x2a, 0x5f, 0xdb, 0x08, 0x53, 0x6b, 0x2f, 0xc9, 0xe5, 0x99, 0xf6, 0xb5, 0x02,
	0xdf, 0x28, 0x5e, 0xf0, 0x06, 0xc4, 0x7f, 0x0c, 0xea, 0x30, 0xbe, 0xf8, 0xc5, 0xee, 0xa1, 0x64,
	0xfc, 0xdb, 0xf9, 0x7e, 0x89, 0xc4, 0x04, 0x93, 0x82, 0xbe, 0x18, 0x4f, 0x8d, 0x21, 0xac, 0x36,
	0x5f, 0xcc, 0xa3, 0x4d, 0x35, 0xb9, 0x73, 0x25, 0xec, 0x3e, 0xb4, 0x86, 0xe6, 0x18, 0x7b, 0x7b,
	0x8c, 0x03, 0x71, 0x2f, 0x78, 0xd9, 0xe2, 0x78, 0x79, 0x07, 0x43, 0x73, 0xdc, 0x23, 0x21, 0xfb,
	0x46, 0x12, 0xb6, 0x9b, 0x92, 0x28, 0x5f, 0x97, 0x84, 0xed, 0x0a, 0x12, 0xda, 0x18, 0xde, 0x96,
	0x35, 0xfb, 0xd8, 0xa7, 0x93, 0x9e, 0xe7, 0x9e, 0xda, 0x67, 0x6f, 0x5c, 0xbf, 0xff, 0xa4, 0x80,
	0x76, 0xd9, 0xb2, 0x37, 0xa0, 0xe5, 0x2f, 0x61, 0x23, 0xd1, 0xf2, 0x99, 0x4f, 0x27, 0x86, 0x85,
	0x6b, 0xc8, 0xca, 0x7e, 0x7b, 0x86, 0xb0, 0x52, 0x8e, 0x50, 0xe5, 0x6b, 0x31, 0x9d, 0x2c, 0x5c,
	0xfb, 0x69, 0x09, 0xd6, 0x8a, 0xa7, 0x5c, 0xa5, 0xfe, 0x2d, 0x80, 0x11, 0xf5, 0x2d, 0xea, 0x86,
	0xfc, 0x89, 0x02, 0xde, 0xd9, 0xa4, 0x10, 0xf5, 0x2d, 0x68, 0xc7, 0xaf, 0xba, 0x82, 0xb4, 0xd5,
	0x35, 0xaf, 0xb7, 0xc4, 0xdb, 0xae, 0x00, 0x17, 0xf9, 0x10, 0xd6, 0xa6, 0xfa, 0x20, 0x1c, 0xbb,
	0x82, 0xd8, 0xcb, 0xb9, 0x66, 0x08, 0x4e, 0x7a, 0x00, 0xab, 0xf9, 0x37, 0x98, 0xe9, 0x25, 0xf9,
	0xbc, 0xae, 0x66, 0x1f, 0x62, 0xc6, 0x65, 0x70, 0xfc, 0x30, 0x8c, 0xa3, 0xce, 0x71, 0x66, 0x62,
	0x20, 0x22, 0xdd, 0x83, 0xe5, 0xd3, 0xe4, 0x39, 0x85, 0x1b, 0x0d, 0x05, 0xdf, 0x35, 0x44, 0x5d,
	0x3c, 0x15, 0x62, 0xfa, 0x34, 0x1a, 0x22, 0xef, 0xda, 0x08, 0x6e, 0xb3, 0xdf, 0xfd, 0x87, 0x27,
	0x34, 0x08, 0xff, 0x4f, 0x42, 0xca, 0x5f, 0x2b, 0xb0, 0x35, 0x6b, 0xc9, 0x1b, 0x30, 0xb7, 0x27,
	0xb0, 0x64, 0x0e, 0x8c, 0x90, 0x06, 0xe1, 0x54, 0x4c, 0xc9, 0xb7, 0x7b, 0x52, 0x0e, 0xd0, 0xbc,
	0xda, 0xe6, 0x40, 0xfe, 0xd6, 0xfe, 0x52, 0x81, 0x76, 0x16, 0xe5, 0x2a, 0x73, 0xda, 0x86, 0xa6,
	0x69, 0x85, 0xf6, 0x05, 0xe5, 0x59, 0x99, 0xb0, 0x27, 0x0e, 0xc2, 0xdc, 0xf2, 0xfb, 0xb0, 0x92,
	0x61, 0x8e, 0xd0, 0xd0, 0xb4, 0x9d, 0x00, 0xad, 0xaa, 0xa0, 0xab, 0x9d, 0x2c, 0xde, 0xe7, 0x78,
	0xfa, 0x52, 0xca, 0xa2, 0x00, 0x69, 0x4f, 0x60, 0x69, 0x0a, 0x2f, 0xdb, 0x28, 0xe6, 0x0f, 0x08,
	0xd2, 0xd7, 0x73, 0x6b, 0x30, 0x87, 0xff, 0xf9, 0xf3, 0xda, 0x79, 0x5d, 0x7c, 0x69, 0x3f, 0x29,
	0xc3, 0x12, 0xef, 0x80, 0xfd, 0xff, 0x79, 0xfb, 0x9a, 0x7b, 0xd5, 0x5a, 0x7b, 0x8d, 0x57, 0xad,
	0xf5, 0x57, 0x79, 0xd5, 0x9a, 0xbf, 0xa6, 0x6b, 0x4c, 0xbf, 0x24, 0xcd, 0x3f, 0x52, 0x84, 0xe9,
	0x47, 0x8a, 0xc9, 0xd3, 0xc3, 0xe6, 0xb5, 0x9e, 0x1e, 0xfe, 0x24, 0x7e, 0xe1, 0x30, 0x7d, 0x79,
	0xf0, 0xda, 0xde, 0xda, 0x2b, 0x7c, 0x9d, 0xb1, 0x53, 0xd8, 0x20, 0x9d, 0x55, 0x59, 0x69, 0xff,
	0xa9, 0x00, 0xe0, 0x75, 0xd4, 0x95, 0x6f, 0x0c, 0xa5, 0x6c, 0xb0, 0x94, 0xcd, 0x06, 0xa5, 0x27,
	0x63, 0xe5, 0xec, 0x93, 0xb1, 0x4d, 0x68, 0xe0, 0xe5, 0x57, 0xd2, 0xdc, 0x56, 0xf4, 0x3a, 0x02,
	0x58, 0xaa, 0x9a, 0x0e, 0x9a, 0x63, 0xcc, 0x49, 0x93, 0x41, 0x7e, 0x55, 0x48, 0x4f, 0x4f, 0xf1,
	0xed, 0x08, 0xd3, 0x04, 0xbf, 0x39, 0x01, 0x0e, 0x42, 0x45, 0x30, 0x84, 0xf1, 0xc8, 0xf6, 0x85,
	0xe3, 0xd6, 0x04, 0x02, 0x82, 0xb0, 0x9e, 0xfa, 0xab, 0x92, 0x78, 0xdc, 0x50, 0x24, 0xfa, 0x1b,
	0x88, 0x5a, 0x36, 0x2c, 0x70, 0xf6, 0xf9, 0xeb, 0x8b, 0xd9, 0xbd, 0xb8, 0x2b, 0x98, 0xb8, 0x9f,
	0x8a, 0x3f, 0x49, 0x4d, 0xe7, 0x89, 0x0c, 0xeb, 0xfe, 0x2e, 0xa8, 0xd3, 0x48, 0x05, 0x09, 0xea,
	0x07, 0xd9, 0x04, 0x75, 0xea, 0xd5, 0x56, 0x42, 0x43, 0xce, 0x51, 0x7f, 0xae, 0xc0, 0xc6, 0x63,
	0x1a, 0x3e, 0x32, 0x1d, 0x67, 0x60, 0x5a, 0xe7, 0xc9, 0x3d, 0xc9, 0x9b, 0x39, 0x4b, 0xb2, 0x9d,
	0x32, 0x16, 0x66, 0xa4, 0x4e, 0x99, 0xba, 0x0b, 0x8b, 0xc4, 0x9c, 0x04, 0x46, 0xe8, 0x19, 0x84,
	0x3a, 0xf6, 0x05, 0xf5, 0x27, 0xe2, 0xcd, 0x54, 0x9b, 0xc1, 0x4f, 0xbc, 0xbe, 0x80, 0x4e, 0xb9,
	0x6a, 0x75, 0xda, 0x55, 0x8b, 0xab, 0x1a, 0xed, 0x2b, 0x05, 0xba, 0x45, 0xfb, 0xbd, 0xf9, 0x4a,
	0x7b, 0x7e, 0x56, 0xa5, 0x3d, 0x1f, 0xd7, 0x71, 0x7b, 0xff, 0xd2, 0x82, 0x8d, 0x23, 0xcb, 0x0b,
	0x7a, 0x24, 0x94, 0x9a, 0x39, 0x4f, 0xa9, 0x7f, 0x61, 0x5b, 0x54, 0xb5, 0x60, 0xe1, 0xa1, 0x19,
	0x5a, 0xcf, 0x1f, 0xd3, 0x50, 0xbc, 0xd0, 0x51, 0xb5, 0x22, 0xe3, 0xca, 0xbe, 0x2c, 0xea, 0xbe,
	0x7b, 0x35, 0x4e, 0xbc, 0x5d, 0xed, 0x96, 0x3a, 0xc6, 0x7b, 0x8c, 0xe9, 0x17, 0x40, 0xea, 0xbd,
	0x4b, 0xc8, 0x4c, 0xbf, 0xd4, 0xea, 0xde, 0xbf, 0x2e, 0x7a, 0xb2, 0xb2, 0x09, 0x8b, 0x8f, 0x69,
	0x98, 0x3c, 0xae, 0xc1, 0xe3, 0xfa, 0xad, 0xc2, 0x4a, 0x2e, 0xf7, 0xe6, 0xa7, 0xfb, 0xf6, 0x15,
	0x58, 0xc9, 0x12, 0x5f, 0x02, 0xa4, 0x3d, 0x95, 0x57, 0xdd, 0xd1, 0x9d, 0x82, 0xa6, 0x7a, 0xd6,
	0x5a, 0xb4, 0x5b, 0xaa, 0x01, 0xed, 0x6c, 0xb3, 0x7d, 0x6a, 0x0b, 0x85, 0xb7, 0x44, 0x53, 0x5b,
	0x28, 0xee, 0xd8, 0x6b, 0xb7, 0xd4, 0x90, 0xdf, 0x33, 0x4d, 0x35, 0x29, 0xd5, 0xf7, 0x0a, 0xd9,
	0x2b, 0xee, 0x9b, 0x77, 0xdf, 0xbf, 0x1e, 0x72, 0xb2, 0x2a, 0xe5, 0xba, 0x91, 0x9b, 0x51, 0xea,
	0x37, 0x67, 0xca, 0x23, 0xd3, 0x25, 0xec, 0xbe, 0x73, 0x25, 0x5e, 0xb2, 0xcc, 0x0f, 0x60, 0x09,
	0x2d, 0x5c, 0x2e, 0xd8, 0x8b, 0x6d, 0x3c, 0xdb, 0xbb, 0xe9, 0xde, 0xbd, 0x46, 0xc5, 0xaf, 0xdd,
	0x52, 0x3d, 0xd8, 0xcc, 0x79, 0xd0, 0x23, 0xcf, 0x3f, 0x0c, 0xe9, 0x10, 0x4f, 0xe9, 0x37, 0xe0,
	0x4d, 0x13, 0xe8, 0xcc, 0xaa, 0xf8, 0xd5, 0xbc, 0x87, 0x5c, 0xd1, 0x1a, 0xe8, 0xbe, 0x77, 0x49,
	0x41, 0x9a, 0x4f, 0xc0, 0xb5, 0x5b, 0xea, 0x4b, 0x58, 0x2b, 0x4e, 0xd2, 0xd5, 0xf7, 0x0b, 0x16,
	0x9e, 0x59, 0x3e, 0x74, 0xef, 0x5d, 0x13, 0x3b, 0x59, 0xf8, 0x0f, 0x45, 0x79, 0x30, 0xbb, 0x2a,
	0x55, 0xbf, 0x7d, 0xc9, 0xd6, 0x67, 0xd6, 0xce, 0xdd, 0x07, 0x57, 0x16, 0x99, 0x05, 0xdc, 0xfc,
	0x81, 0x02, 0x6f, 0x31, 0x8b, 0xeb, 0x9f, 0x4c, 0x9f, 0xb9, 0x8f, 0x3c, 0x9f, 0x19, 0xe0, 0xb1,
	0x47, 0xa8, 0x53, 0x1c, 0x0d, 0x66, 0xe6, 0x69, 0xc5, 0xf1, 0x6d, 0xf6, 0xb1, 0xae, 0xdd, 0x52,
	0xcf, 0xf1, 0xe1, 0x41, 0xee, 0xa0, 0x51, 0x77, 0xa7, 0xbd, 0xa3, 0xf8, 0xec, 0xed, 0x7e, 0xeb,
	0x1a, 0x98, 0xf1, 0x62, 0xff, 0x1b, 0x00, 0x00, 0xff, 0xff, 0x89, 0x39, 0x55, 0xd2, 0x94, 0x38,
	0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosCdtCalculationServiceClient is the client API for LcosCdtCalculationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosCdtCalculationServiceClient interface {
	// 批量获取cdt信息
	BatchGetCdtInfo(ctx context.Context, in *QueryCdtInfoRequest, opts ...grpc.CallOption) (*QueryCdtInfoRequestResponse, error)
	GetCdtInfoForTracking(ctx context.Context, in *QueryCdtInfoForTrackingRequest, opts ...grpc.CallOption) (*QueryCdtInfoForTrackingResponse, error)
	GetEventCodeList(ctx context.Context, in *QueryEventCodeRequest, opts ...grpc.CallOption) (*QueryEventCodeResponse, error)
	// SPLN-23295 return extra info for calculation
	GetEddInfo(ctx context.Context, in *QueryCdtInfoForTrackingRequest, opts ...grpc.CallOption) (*GetEddInfoResponse, error)
	// SPLN-24285 calculate edd with extension
	GetExtendedEDD(ctx context.Context, in *GetExtendedEDDRequest, opts ...grpc.CallOption) (*GetExtendedEDDResponse, error)
	// SPLN-29072 return edd calculation info, including edd info, ddl info
	GetEddCalculationInfo(ctx context.Context, in *GetEddCalculationInfoRequest, opts ...grpc.CallOption) (*GetEddCalculationInfoResponse, error)
	// SPLN-33284 通过algo模型计算edd
	GetEddInfoByAlgo(ctx context.Context, in *GetEddInfoByAlgoRequest, opts ...grpc.CallOption) (*GetEddInfoByAlgoResponse, error)
	// SPLN-30606 get edd by sls tn, service name: sls-timeservice-offline-{cid}
	BatchQueryEDDInfo(ctx context.Context, in *QueryEDDInfoRequest, opts ...grpc.CallOption) (*QueryEDDInfoResponse, error)
	BatchGetCdtInfoForItemScene(ctx context.Context, in *QueryCdtInfoRequest, opts ...grpc.CallOption) (*QueryCdtInfoRequestResponse, error)
	// SPLN-31544 get Aggregate rule by region
	ListMChannelRuleByRegion(ctx context.Context, in *ListMChannelRuleByRegionRequest, opts ...grpc.CallOption) (*MChannelRuleByRegionResponse, error)
	ListABTestRuleByRegion(ctx context.Context, in *ListABTestRuleByRegionRequest, opts ...grpc.CallOption) (*ListABTestRuleByRegionResponse, error)
	// SPLN-31544 get Aggregate channel grey rule by region
	ListMChannelGreyConfigByRegion(ctx context.Context, in *ListMChannelGreyConfigByRegionRequest, opts ...grpc.CallOption) (*MChannelGreyConfigByRegionResponse, error)
	// SPLN-33865 增加Algo Model获取修正规则的逻辑
	// SPLN-34535 新增对于历史修正规则的查询逻辑
	GetEDTManualManipulationForAlgoModel(ctx context.Context, in *QueryManualManipulationRequest, opts ...grpc.CallOption) (*QueryManualManipulationResponse, error)
	GetFallbackEddInfo(ctx context.Context, in *GetFallbackEddInfoRequest, opts ...grpc.CallOption) (*GetFallbackEddInfoResponse, error)
}

type lcosCdtCalculationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosCdtCalculationServiceClient(cc grpc.ClientConnInterface) LcosCdtCalculationServiceClient {
	return &lcosCdtCalculationServiceClient{cc}
}

func (c *lcosCdtCalculationServiceClient) BatchGetCdtInfo(ctx context.Context, in *QueryCdtInfoRequest, opts ...grpc.CallOption) (*QueryCdtInfoRequestResponse, error) {
	out := new(QueryCdtInfoRequestResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/BatchGetCdtInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) GetCdtInfoForTracking(ctx context.Context, in *QueryCdtInfoForTrackingRequest, opts ...grpc.CallOption) (*QueryCdtInfoForTrackingResponse, error) {
	out := new(QueryCdtInfoForTrackingResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/GetCdtInfoForTracking", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) GetEventCodeList(ctx context.Context, in *QueryEventCodeRequest, opts ...grpc.CallOption) (*QueryEventCodeResponse, error) {
	out := new(QueryEventCodeResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/GetEventCodeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) GetEddInfo(ctx context.Context, in *QueryCdtInfoForTrackingRequest, opts ...grpc.CallOption) (*GetEddInfoResponse, error) {
	out := new(GetEddInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/GetEddInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) GetExtendedEDD(ctx context.Context, in *GetExtendedEDDRequest, opts ...grpc.CallOption) (*GetExtendedEDDResponse, error) {
	out := new(GetExtendedEDDResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/GetExtendedEDD", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) GetEddCalculationInfo(ctx context.Context, in *GetEddCalculationInfoRequest, opts ...grpc.CallOption) (*GetEddCalculationInfoResponse, error) {
	out := new(GetEddCalculationInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/GetEddCalculationInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) GetEddInfoByAlgo(ctx context.Context, in *GetEddInfoByAlgoRequest, opts ...grpc.CallOption) (*GetEddInfoByAlgoResponse, error) {
	out := new(GetEddInfoByAlgoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/GetEddInfoByAlgo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) BatchQueryEDDInfo(ctx context.Context, in *QueryEDDInfoRequest, opts ...grpc.CallOption) (*QueryEDDInfoResponse, error) {
	out := new(QueryEDDInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/BatchQueryEDDInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) BatchGetCdtInfoForItemScene(ctx context.Context, in *QueryCdtInfoRequest, opts ...grpc.CallOption) (*QueryCdtInfoRequestResponse, error) {
	out := new(QueryCdtInfoRequestResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/BatchGetCdtInfoForItemScene", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) ListMChannelRuleByRegion(ctx context.Context, in *ListMChannelRuleByRegionRequest, opts ...grpc.CallOption) (*MChannelRuleByRegionResponse, error) {
	out := new(MChannelRuleByRegionResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/ListMChannelRuleByRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) ListABTestRuleByRegion(ctx context.Context, in *ListABTestRuleByRegionRequest, opts ...grpc.CallOption) (*ListABTestRuleByRegionResponse, error) {
	out := new(ListABTestRuleByRegionResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/ListABTestRuleByRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) ListMChannelGreyConfigByRegion(ctx context.Context, in *ListMChannelGreyConfigByRegionRequest, opts ...grpc.CallOption) (*MChannelGreyConfigByRegionResponse, error) {
	out := new(MChannelGreyConfigByRegionResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/ListMChannelGreyConfigByRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) GetEDTManualManipulationForAlgoModel(ctx context.Context, in *QueryManualManipulationRequest, opts ...grpc.CallOption) (*QueryManualManipulationResponse, error) {
	out := new(QueryManualManipulationResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/GetEDTManualManipulationForAlgoModel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosCdtCalculationServiceClient) GetFallbackEddInfo(ctx context.Context, in *GetFallbackEddInfoRequest, opts ...grpc.CallOption) (*GetFallbackEddInfoResponse, error) {
	out := new(GetFallbackEddInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosCdtCalculationService/GetFallbackEddInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosCdtCalculationServiceServer is the server API for LcosCdtCalculationService service.
type LcosCdtCalculationServiceServer interface {
	// 批量获取cdt信息
	BatchGetCdtInfo(context.Context, *QueryCdtInfoRequest) (*QueryCdtInfoRequestResponse, error)
	GetCdtInfoForTracking(context.Context, *QueryCdtInfoForTrackingRequest) (*QueryCdtInfoForTrackingResponse, error)
	GetEventCodeList(context.Context, *QueryEventCodeRequest) (*QueryEventCodeResponse, error)
	// SPLN-23295 return extra info for calculation
	GetEddInfo(context.Context, *QueryCdtInfoForTrackingRequest) (*GetEddInfoResponse, error)
	// SPLN-24285 calculate edd with extension
	GetExtendedEDD(context.Context, *GetExtendedEDDRequest) (*GetExtendedEDDResponse, error)
	// SPLN-29072 return edd calculation info, including edd info, ddl info
	GetEddCalculationInfo(context.Context, *GetEddCalculationInfoRequest) (*GetEddCalculationInfoResponse, error)
	// SPLN-33284 通过algo模型计算edd
	GetEddInfoByAlgo(context.Context, *GetEddInfoByAlgoRequest) (*GetEddInfoByAlgoResponse, error)
	// SPLN-30606 get edd by sls tn, service name: sls-timeservice-offline-{cid}
	BatchQueryEDDInfo(context.Context, *QueryEDDInfoRequest) (*QueryEDDInfoResponse, error)
	BatchGetCdtInfoForItemScene(context.Context, *QueryCdtInfoRequest) (*QueryCdtInfoRequestResponse, error)
	// SPLN-31544 get Aggregate rule by region
	ListMChannelRuleByRegion(context.Context, *ListMChannelRuleByRegionRequest) (*MChannelRuleByRegionResponse, error)
	ListABTestRuleByRegion(context.Context, *ListABTestRuleByRegionRequest) (*ListABTestRuleByRegionResponse, error)
	// SPLN-31544 get Aggregate channel grey rule by region
	ListMChannelGreyConfigByRegion(context.Context, *ListMChannelGreyConfigByRegionRequest) (*MChannelGreyConfigByRegionResponse, error)
	// SPLN-33865 增加Algo Model获取修正规则的逻辑
	// SPLN-34535 新增对于历史修正规则的查询逻辑
	GetEDTManualManipulationForAlgoModel(context.Context, *QueryManualManipulationRequest) (*QueryManualManipulationResponse, error)
	GetFallbackEddInfo(context.Context, *GetFallbackEddInfoRequest) (*GetFallbackEddInfoResponse, error)
}

// UnimplementedLcosCdtCalculationServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLcosCdtCalculationServiceServer struct {
}

func (*UnimplementedLcosCdtCalculationServiceServer) BatchGetCdtInfo(ctx context.Context, req *QueryCdtInfoRequest) (*QueryCdtInfoRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetCdtInfo not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) GetCdtInfoForTracking(ctx context.Context, req *QueryCdtInfoForTrackingRequest) (*QueryCdtInfoForTrackingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCdtInfoForTracking not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) GetEventCodeList(ctx context.Context, req *QueryEventCodeRequest) (*QueryEventCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEventCodeList not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) GetEddInfo(ctx context.Context, req *QueryCdtInfoForTrackingRequest) (*GetEddInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEddInfo not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) GetExtendedEDD(ctx context.Context, req *GetExtendedEDDRequest) (*GetExtendedEDDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExtendedEDD not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) GetEddCalculationInfo(ctx context.Context, req *GetEddCalculationInfoRequest) (*GetEddCalculationInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEddCalculationInfo not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) GetEddInfoByAlgo(ctx context.Context, req *GetEddInfoByAlgoRequest) (*GetEddInfoByAlgoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEddInfoByAlgo not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) BatchQueryEDDInfo(ctx context.Context, req *QueryEDDInfoRequest) (*QueryEDDInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchQueryEDDInfo not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) BatchGetCdtInfoForItemScene(ctx context.Context, req *QueryCdtInfoRequest) (*QueryCdtInfoRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetCdtInfoForItemScene not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) ListMChannelRuleByRegion(ctx context.Context, req *ListMChannelRuleByRegionRequest) (*MChannelRuleByRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMChannelRuleByRegion not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) ListABTestRuleByRegion(ctx context.Context, req *ListABTestRuleByRegionRequest) (*ListABTestRuleByRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListABTestRuleByRegion not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) ListMChannelGreyConfigByRegion(ctx context.Context, req *ListMChannelGreyConfigByRegionRequest) (*MChannelGreyConfigByRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMChannelGreyConfigByRegion not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) GetEDTManualManipulationForAlgoModel(ctx context.Context, req *QueryManualManipulationRequest) (*QueryManualManipulationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEDTManualManipulationForAlgoModel not implemented")
}
func (*UnimplementedLcosCdtCalculationServiceServer) GetFallbackEddInfo(ctx context.Context, req *GetFallbackEddInfoRequest) (*GetFallbackEddInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFallbackEddInfo not implemented")
}

func RegisterLcosCdtCalculationServiceServer(s *grpc.Server, srv LcosCdtCalculationServiceServer) {
	s.RegisterService(&_LcosCdtCalculationService_serviceDesc, srv)
}

func _LcosCdtCalculationService_BatchGetCdtInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCdtInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).BatchGetCdtInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/BatchGetCdtInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).BatchGetCdtInfo(ctx, req.(*QueryCdtInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_GetCdtInfoForTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCdtInfoForTrackingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).GetCdtInfoForTracking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/GetCdtInfoForTracking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).GetCdtInfoForTracking(ctx, req.(*QueryCdtInfoForTrackingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_GetEventCodeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryEventCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).GetEventCodeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/GetEventCodeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).GetEventCodeList(ctx, req.(*QueryEventCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_GetEddInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCdtInfoForTrackingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).GetEddInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/GetEddInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).GetEddInfo(ctx, req.(*QueryCdtInfoForTrackingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_GetExtendedEDD_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtendedEDDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).GetExtendedEDD(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/GetExtendedEDD",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).GetExtendedEDD(ctx, req.(*GetExtendedEDDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_GetEddCalculationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEddCalculationInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).GetEddCalculationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/GetEddCalculationInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).GetEddCalculationInfo(ctx, req.(*GetEddCalculationInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_GetEddInfoByAlgo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEddInfoByAlgoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).GetEddInfoByAlgo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/GetEddInfoByAlgo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).GetEddInfoByAlgo(ctx, req.(*GetEddInfoByAlgoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_BatchQueryEDDInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryEDDInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).BatchQueryEDDInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/BatchQueryEDDInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).BatchQueryEDDInfo(ctx, req.(*QueryEDDInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_BatchGetCdtInfoForItemScene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCdtInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).BatchGetCdtInfoForItemScene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/BatchGetCdtInfoForItemScene",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).BatchGetCdtInfoForItemScene(ctx, req.(*QueryCdtInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_ListMChannelRuleByRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMChannelRuleByRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).ListMChannelRuleByRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/ListMChannelRuleByRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).ListMChannelRuleByRegion(ctx, req.(*ListMChannelRuleByRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_ListABTestRuleByRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListABTestRuleByRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).ListABTestRuleByRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/ListABTestRuleByRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).ListABTestRuleByRegion(ctx, req.(*ListABTestRuleByRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_ListMChannelGreyConfigByRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMChannelGreyConfigByRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).ListMChannelGreyConfigByRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/ListMChannelGreyConfigByRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).ListMChannelGreyConfigByRegion(ctx, req.(*ListMChannelGreyConfigByRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_GetEDTManualManipulationForAlgoModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryManualManipulationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).GetEDTManualManipulationForAlgoModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/GetEDTManualManipulationForAlgoModel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).GetEDTManualManipulationForAlgoModel(ctx, req.(*QueryManualManipulationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosCdtCalculationService_GetFallbackEddInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFallbackEddInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosCdtCalculationServiceServer).GetFallbackEddInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosCdtCalculationService/GetFallbackEddInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosCdtCalculationServiceServer).GetFallbackEddInfo(ctx, req.(*GetFallbackEddInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosCdtCalculationService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosCdtCalculationService",
	HandlerType: (*LcosCdtCalculationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetCdtInfo",
			Handler:    _LcosCdtCalculationService_BatchGetCdtInfo_Handler,
		},
		{
			MethodName: "GetCdtInfoForTracking",
			Handler:    _LcosCdtCalculationService_GetCdtInfoForTracking_Handler,
		},
		{
			MethodName: "GetEventCodeList",
			Handler:    _LcosCdtCalculationService_GetEventCodeList_Handler,
		},
		{
			MethodName: "GetEddInfo",
			Handler:    _LcosCdtCalculationService_GetEddInfo_Handler,
		},
		{
			MethodName: "GetExtendedEDD",
			Handler:    _LcosCdtCalculationService_GetExtendedEDD_Handler,
		},
		{
			MethodName: "GetEddCalculationInfo",
			Handler:    _LcosCdtCalculationService_GetEddCalculationInfo_Handler,
		},
		{
			MethodName: "GetEddInfoByAlgo",
			Handler:    _LcosCdtCalculationService_GetEddInfoByAlgo_Handler,
		},
		{
			MethodName: "BatchQueryEDDInfo",
			Handler:    _LcosCdtCalculationService_BatchQueryEDDInfo_Handler,
		},
		{
			MethodName: "BatchGetCdtInfoForItemScene",
			Handler:    _LcosCdtCalculationService_BatchGetCdtInfoForItemScene_Handler,
		},
		{
			MethodName: "ListMChannelRuleByRegion",
			Handler:    _LcosCdtCalculationService_ListMChannelRuleByRegion_Handler,
		},
		{
			MethodName: "ListABTestRuleByRegion",
			Handler:    _LcosCdtCalculationService_ListABTestRuleByRegion_Handler,
		},
		{
			MethodName: "ListMChannelGreyConfigByRegion",
			Handler:    _LcosCdtCalculationService_ListMChannelGreyConfigByRegion_Handler,
		},
		{
			MethodName: "GetEDTManualManipulationForAlgoModel",
			Handler:    _LcosCdtCalculationService_GetEDTManualManipulationForAlgoModel_Handler,
		},
		{
			MethodName: "GetFallbackEddInfo",
			Handler:    _LcosCdtCalculationService_GetFallbackEddInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cdt_calculation.proto",
}
