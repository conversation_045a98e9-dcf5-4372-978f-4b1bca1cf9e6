// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_tw_store.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ConvenienceStore struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	StoreId              *string  `protobuf:"bytes,2,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	NewStoreId           *string  `protobuf:"bytes,3,opt,name=new_store_id,json=newStoreId" json:"new_store_id,omitempty"`
	Name                 *string  `protobuf:"bytes,4,opt,name=name" json:"name,omitempty"`
	Address              *string  `protobuf:"bytes,5,opt,name=address" json:"address,omitempty"`
	StoreType            *uint32  `protobuf:"varint,6,opt,name=store_type,json=storeType" json:"store_type,omitempty"`
	CloseDate            *string  `protobuf:"bytes,7,opt,name=close_date,json=closeDate" json:"close_date,omitempty"`
	EndDate              *string  `protobuf:"bytes,8,opt,name=end_date,json=endDate" json:"end_date,omitempty"`
	Status               *uint32  `protobuf:"varint,9,opt,name=status" json:"status,omitempty"`
	DistrictId           *uint64  `protobuf:"varint,10,opt,name=district_id,json=districtId" json:"district_id,omitempty"`
	IsAllConsistency     *uint32  `protobuf:"varint,11,opt,name=is_all_consistency,json=isAllConsistency" json:"is_all_consistency,omitempty"`
	UpdateDate           *string  `protobuf:"bytes,12,opt,name=update_date,json=updateDate" json:"update_date,omitempty"`
	Phone                *string  `protobuf:"bytes,13,opt,name=phone" json:"phone,omitempty"`
	PostalCode           *string  `protobuf:"bytes,14,opt,name=postal_code,json=postalCode" json:"postal_code,omitempty"`
	IsVirtual            *uint32  `protobuf:"varint,15,opt,name=is_virtual,json=isVirtual" json:"is_virtual,omitempty"`
	City                 *string  `protobuf:"bytes,16,opt,name=city" json:"city,omitempty"`
	Area                 *string  `protobuf:"bytes,17,opt,name=area" json:"area,omitempty"`
	District             *string  `protobuf:"bytes,18,opt,name=district" json:"district,omitempty"`
	Ctime                *uint32  `protobuf:"varint,19,opt,name=ctime" json:"ctime,omitempty"`
	Mtime                *uint32  `protobuf:"varint,20,opt,name=mtime" json:"mtime,omitempty"`
	DcroNo               *string  `protobuf:"bytes,21,opt,name=dcro_no,json=dcroNo" json:"dcro_no,omitempty"`
	RsNo                 *string  `protobuf:"bytes,22,opt,name=rs_no,json=rsNo" json:"rs_no,omitempty"`
	ShipType             *string  `protobuf:"bytes,23,opt,name=ship_type,json=shipType" json:"ship_type,omitempty"`
	PathNo               *string  `protobuf:"bytes,24,opt,name=path_no,json=pathNo" json:"path_no,omitempty"`
	AisleNo              *string  `protobuf:"bytes,25,opt,name=aisle_no,json=aisleNo" json:"aisle_no,omitempty"`
	GridNo               *string  `protobuf:"bytes,26,opt,name=grid_no,json=gridNo" json:"grid_no,omitempty"`
	MidType              *string  `protobuf:"bytes,27,opt,name=mid_type,json=midType" json:"mid_type,omitempty"`
	OkArea               *string  `protobuf:"bytes,28,opt,name=ok_area,json=okArea" json:"ok_area,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConvenienceStore) Reset()         { *m = ConvenienceStore{} }
func (m *ConvenienceStore) String() string { return proto.CompactTextString(m) }
func (*ConvenienceStore) ProtoMessage()    {}
func (*ConvenienceStore) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{0}
}

func (m *ConvenienceStore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConvenienceStore.Unmarshal(m, b)
}
func (m *ConvenienceStore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConvenienceStore.Marshal(b, m, deterministic)
}
func (m *ConvenienceStore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConvenienceStore.Merge(m, src)
}
func (m *ConvenienceStore) XXX_Size() int {
	return xxx_messageInfo_ConvenienceStore.Size(m)
}
func (m *ConvenienceStore) XXX_DiscardUnknown() {
	xxx_messageInfo_ConvenienceStore.DiscardUnknown(m)
}

var xxx_messageInfo_ConvenienceStore proto.InternalMessageInfo

func (m *ConvenienceStore) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *ConvenienceStore) GetStoreId() string {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return ""
}

func (m *ConvenienceStore) GetNewStoreId() string {
	if m != nil && m.NewStoreId != nil {
		return *m.NewStoreId
	}
	return ""
}

func (m *ConvenienceStore) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *ConvenienceStore) GetAddress() string {
	if m != nil && m.Address != nil {
		return *m.Address
	}
	return ""
}

func (m *ConvenienceStore) GetStoreType() uint32 {
	if m != nil && m.StoreType != nil {
		return *m.StoreType
	}
	return 0
}

func (m *ConvenienceStore) GetCloseDate() string {
	if m != nil && m.CloseDate != nil {
		return *m.CloseDate
	}
	return ""
}

func (m *ConvenienceStore) GetEndDate() string {
	if m != nil && m.EndDate != nil {
		return *m.EndDate
	}
	return ""
}

func (m *ConvenienceStore) GetStatus() uint32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *ConvenienceStore) GetDistrictId() uint64 {
	if m != nil && m.DistrictId != nil {
		return *m.DistrictId
	}
	return 0
}

func (m *ConvenienceStore) GetIsAllConsistency() uint32 {
	if m != nil && m.IsAllConsistency != nil {
		return *m.IsAllConsistency
	}
	return 0
}

func (m *ConvenienceStore) GetUpdateDate() string {
	if m != nil && m.UpdateDate != nil {
		return *m.UpdateDate
	}
	return ""
}

func (m *ConvenienceStore) GetPhone() string {
	if m != nil && m.Phone != nil {
		return *m.Phone
	}
	return ""
}

func (m *ConvenienceStore) GetPostalCode() string {
	if m != nil && m.PostalCode != nil {
		return *m.PostalCode
	}
	return ""
}

func (m *ConvenienceStore) GetIsVirtual() uint32 {
	if m != nil && m.IsVirtual != nil {
		return *m.IsVirtual
	}
	return 0
}

func (m *ConvenienceStore) GetCity() string {
	if m != nil && m.City != nil {
		return *m.City
	}
	return ""
}

func (m *ConvenienceStore) GetArea() string {
	if m != nil && m.Area != nil {
		return *m.Area
	}
	return ""
}

func (m *ConvenienceStore) GetDistrict() string {
	if m != nil && m.District != nil {
		return *m.District
	}
	return ""
}

func (m *ConvenienceStore) GetCtime() uint32 {
	if m != nil && m.Ctime != nil {
		return *m.Ctime
	}
	return 0
}

func (m *ConvenienceStore) GetMtime() uint32 {
	if m != nil && m.Mtime != nil {
		return *m.Mtime
	}
	return 0
}

func (m *ConvenienceStore) GetDcroNo() string {
	if m != nil && m.DcroNo != nil {
		return *m.DcroNo
	}
	return ""
}

func (m *ConvenienceStore) GetRsNo() string {
	if m != nil && m.RsNo != nil {
		return *m.RsNo
	}
	return ""
}

func (m *ConvenienceStore) GetShipType() string {
	if m != nil && m.ShipType != nil {
		return *m.ShipType
	}
	return ""
}

func (m *ConvenienceStore) GetPathNo() string {
	if m != nil && m.PathNo != nil {
		return *m.PathNo
	}
	return ""
}

func (m *ConvenienceStore) GetAisleNo() string {
	if m != nil && m.AisleNo != nil {
		return *m.AisleNo
	}
	return ""
}

func (m *ConvenienceStore) GetGridNo() string {
	if m != nil && m.GridNo != nil {
		return *m.GridNo
	}
	return ""
}

func (m *ConvenienceStore) GetMidType() string {
	if m != nil && m.MidType != nil {
		return *m.MidType
	}
	return ""
}

func (m *ConvenienceStore) GetOkArea() string {
	if m != nil && m.OkArea != nil {
		return *m.OkArea
	}
	return ""
}

type SingleGetStoreByStoreID struct {
	UniqueId             *string  `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	StoreType            *uint32  `protobuf:"varint,2,opt,name=store_type,json=storeType" json:"store_type,omitempty"`
	StoreId              *string  `protobuf:"bytes,3,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	Status               *uint32  `protobuf:"varint,4,opt,name=status" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingleGetStoreByStoreID) Reset()         { *m = SingleGetStoreByStoreID{} }
func (m *SingleGetStoreByStoreID) String() string { return proto.CompactTextString(m) }
func (*SingleGetStoreByStoreID) ProtoMessage()    {}
func (*SingleGetStoreByStoreID) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{1}
}

func (m *SingleGetStoreByStoreID) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleGetStoreByStoreID.Unmarshal(m, b)
}
func (m *SingleGetStoreByStoreID) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleGetStoreByStoreID.Marshal(b, m, deterministic)
}
func (m *SingleGetStoreByStoreID) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleGetStoreByStoreID.Merge(m, src)
}
func (m *SingleGetStoreByStoreID) XXX_Size() int {
	return xxx_messageInfo_SingleGetStoreByStoreID.Size(m)
}
func (m *SingleGetStoreByStoreID) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleGetStoreByStoreID.DiscardUnknown(m)
}

var xxx_messageInfo_SingleGetStoreByStoreID proto.InternalMessageInfo

func (m *SingleGetStoreByStoreID) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SingleGetStoreByStoreID) GetStoreType() uint32 {
	if m != nil && m.StoreType != nil {
		return *m.StoreType
	}
	return 0
}

func (m *SingleGetStoreByStoreID) GetStoreId() string {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return ""
}

func (m *SingleGetStoreByStoreID) GetStatus() uint32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

type BatchGetStoreByStoreIDRequest struct {
	ReqHeader            *ReqHeader                 `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	RequestList          []*SingleGetStoreByStoreID `protobuf:"bytes,2,rep,name=request_list,json=requestList" json:"request_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BatchGetStoreByStoreIDRequest) Reset()         { *m = BatchGetStoreByStoreIDRequest{} }
func (m *BatchGetStoreByStoreIDRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetStoreByStoreIDRequest) ProtoMessage()    {}
func (*BatchGetStoreByStoreIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{2}
}

func (m *BatchGetStoreByStoreIDRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStoreByStoreIDRequest.Unmarshal(m, b)
}
func (m *BatchGetStoreByStoreIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStoreByStoreIDRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetStoreByStoreIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStoreByStoreIDRequest.Merge(m, src)
}
func (m *BatchGetStoreByStoreIDRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetStoreByStoreIDRequest.Size(m)
}
func (m *BatchGetStoreByStoreIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStoreByStoreIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStoreByStoreIDRequest proto.InternalMessageInfo

func (m *BatchGetStoreByStoreIDRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetStoreByStoreIDRequest) GetRequestList() []*SingleGetStoreByStoreID {
	if m != nil {
		return m.RequestList
	}
	return nil
}

type BatchGetStoreByAddressIDRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	RequestList          []string   `protobuf:"bytes,2,rep,name=request_list,json=requestList" json:"request_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetStoreByAddressIDRequest) Reset()         { *m = BatchGetStoreByAddressIDRequest{} }
func (m *BatchGetStoreByAddressIDRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetStoreByAddressIDRequest) ProtoMessage()    {}
func (*BatchGetStoreByAddressIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{3}
}

func (m *BatchGetStoreByAddressIDRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStoreByAddressIDRequest.Unmarshal(m, b)
}
func (m *BatchGetStoreByAddressIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStoreByAddressIDRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetStoreByAddressIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStoreByAddressIDRequest.Merge(m, src)
}
func (m *BatchGetStoreByAddressIDRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetStoreByAddressIDRequest.Size(m)
}
func (m *BatchGetStoreByAddressIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStoreByAddressIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStoreByAddressIDRequest proto.InternalMessageInfo

func (m *BatchGetStoreByAddressIDRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetStoreByAddressIDRequest) GetRequestList() []string {
	if m != nil {
		return m.RequestList
	}
	return nil
}

type BatchGetStoreByStoreIDResponse struct {
	RespHeader           *RespHeader                  `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ResponseDict         map[string]*ConvenienceStore `protobuf:"bytes,2,rep,name=response_dict,json=responseDict" json:"response_dict,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchGetStoreByStoreIDResponse) Reset()         { *m = BatchGetStoreByStoreIDResponse{} }
func (m *BatchGetStoreByStoreIDResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetStoreByStoreIDResponse) ProtoMessage()    {}
func (*BatchGetStoreByStoreIDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{4}
}

func (m *BatchGetStoreByStoreIDResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStoreByStoreIDResponse.Unmarshal(m, b)
}
func (m *BatchGetStoreByStoreIDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStoreByStoreIDResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetStoreByStoreIDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStoreByStoreIDResponse.Merge(m, src)
}
func (m *BatchGetStoreByStoreIDResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetStoreByStoreIDResponse.Size(m)
}
func (m *BatchGetStoreByStoreIDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStoreByStoreIDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStoreByStoreIDResponse proto.InternalMessageInfo

func (m *BatchGetStoreByStoreIDResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetStoreByStoreIDResponse) GetResponseDict() map[string]*ConvenienceStore {
	if m != nil {
		return m.ResponseDict
	}
	return nil
}

type SingleGetStoreByNewStoreIdAndType struct {
	UniqueId             *string  `protobuf:"bytes,1,req,name=unique_id,json=uniqueId" json:"unique_id,omitempty"`
	NewStoreId           *string  `protobuf:"bytes,2,opt,name=new_store_id,json=newStoreId" json:"new_store_id,omitempty"`
	StoreType            *uint32  `protobuf:"varint,3,opt,name=store_type,json=storeType" json:"store_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingleGetStoreByNewStoreIdAndType) Reset()         { *m = SingleGetStoreByNewStoreIdAndType{} }
func (m *SingleGetStoreByNewStoreIdAndType) String() string { return proto.CompactTextString(m) }
func (*SingleGetStoreByNewStoreIdAndType) ProtoMessage()    {}
func (*SingleGetStoreByNewStoreIdAndType) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{5}
}

func (m *SingleGetStoreByNewStoreIdAndType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleGetStoreByNewStoreIdAndType.Unmarshal(m, b)
}
func (m *SingleGetStoreByNewStoreIdAndType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleGetStoreByNewStoreIdAndType.Marshal(b, m, deterministic)
}
func (m *SingleGetStoreByNewStoreIdAndType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleGetStoreByNewStoreIdAndType.Merge(m, src)
}
func (m *SingleGetStoreByNewStoreIdAndType) XXX_Size() int {
	return xxx_messageInfo_SingleGetStoreByNewStoreIdAndType.Size(m)
}
func (m *SingleGetStoreByNewStoreIdAndType) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleGetStoreByNewStoreIdAndType.DiscardUnknown(m)
}

var xxx_messageInfo_SingleGetStoreByNewStoreIdAndType proto.InternalMessageInfo

func (m *SingleGetStoreByNewStoreIdAndType) GetUniqueId() string {
	if m != nil && m.UniqueId != nil {
		return *m.UniqueId
	}
	return ""
}

func (m *SingleGetStoreByNewStoreIdAndType) GetNewStoreId() string {
	if m != nil && m.NewStoreId != nil {
		return *m.NewStoreId
	}
	return ""
}

func (m *SingleGetStoreByNewStoreIdAndType) GetStoreType() uint32 {
	if m != nil && m.StoreType != nil {
		return *m.StoreType
	}
	return 0
}

type BatchGetStoreByNewStoreIdAndTypeRequest struct {
	ReqHeader            *ReqHeader                           `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	RequestList          []*SingleGetStoreByNewStoreIdAndType `protobuf:"bytes,2,rep,name=request_list,json=requestList" json:"request_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *BatchGetStoreByNewStoreIdAndTypeRequest) Reset() {
	*m = BatchGetStoreByNewStoreIdAndTypeRequest{}
}
func (m *BatchGetStoreByNewStoreIdAndTypeRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetStoreByNewStoreIdAndTypeRequest) ProtoMessage()    {}
func (*BatchGetStoreByNewStoreIdAndTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{6}
}

func (m *BatchGetStoreByNewStoreIdAndTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeRequest.Unmarshal(m, b)
}
func (m *BatchGetStoreByNewStoreIdAndTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetStoreByNewStoreIdAndTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeRequest.Merge(m, src)
}
func (m *BatchGetStoreByNewStoreIdAndTypeRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeRequest.Size(m)
}
func (m *BatchGetStoreByNewStoreIdAndTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeRequest proto.InternalMessageInfo

func (m *BatchGetStoreByNewStoreIdAndTypeRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetStoreByNewStoreIdAndTypeRequest) GetRequestList() []*SingleGetStoreByNewStoreIdAndType {
	if m != nil {
		return m.RequestList
	}
	return nil
}

type BatchGetStoreByNewStoreIdAndTypeResponse struct {
	RespHeader           *RespHeader                  `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ResponseDict         map[string]*ConvenienceStore `protobuf:"bytes,2,rep,name=response_dict,json=responseDict" json:"response_dict,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchGetStoreByNewStoreIdAndTypeResponse) Reset() {
	*m = BatchGetStoreByNewStoreIdAndTypeResponse{}
}
func (m *BatchGetStoreByNewStoreIdAndTypeResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetStoreByNewStoreIdAndTypeResponse) ProtoMessage()    {}
func (*BatchGetStoreByNewStoreIdAndTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{7}
}

func (m *BatchGetStoreByNewStoreIdAndTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeResponse.Unmarshal(m, b)
}
func (m *BatchGetStoreByNewStoreIdAndTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetStoreByNewStoreIdAndTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeResponse.Merge(m, src)
}
func (m *BatchGetStoreByNewStoreIdAndTypeResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeResponse.Size(m)
}
func (m *BatchGetStoreByNewStoreIdAndTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStoreByNewStoreIdAndTypeResponse proto.InternalMessageInfo

func (m *BatchGetStoreByNewStoreIdAndTypeResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetStoreByNewStoreIdAndTypeResponse) GetResponseDict() map[string]*ConvenienceStore {
	if m != nil {
		return m.ResponseDict
	}
	return nil
}

type SyncStoreByTypeRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	StoreType            *uint32    `protobuf:"varint,2,req,name=store_type,json=storeType" json:"store_type,omitempty"`
	RemoteFilePath       *string    `protobuf:"bytes,3,req,name=remote_file_path,json=remoteFilePath" json:"remote_file_path,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SyncStoreByTypeRequest) Reset()         { *m = SyncStoreByTypeRequest{} }
func (m *SyncStoreByTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SyncStoreByTypeRequest) ProtoMessage()    {}
func (*SyncStoreByTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{8}
}

func (m *SyncStoreByTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncStoreByTypeRequest.Unmarshal(m, b)
}
func (m *SyncStoreByTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncStoreByTypeRequest.Marshal(b, m, deterministic)
}
func (m *SyncStoreByTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncStoreByTypeRequest.Merge(m, src)
}
func (m *SyncStoreByTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SyncStoreByTypeRequest.Size(m)
}
func (m *SyncStoreByTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncStoreByTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncStoreByTypeRequest proto.InternalMessageInfo

func (m *SyncStoreByTypeRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *SyncStoreByTypeRequest) GetStoreType() uint32 {
	if m != nil && m.StoreType != nil {
		return *m.StoreType
	}
	return 0
}

func (m *SyncStoreByTypeRequest) GetRemoteFilePath() string {
	if m != nil && m.RemoteFilePath != nil {
		return *m.RemoteFilePath
	}
	return ""
}

type SyncStoreByTypeResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SyncStoreByTypeResponse) Reset()         { *m = SyncStoreByTypeResponse{} }
func (m *SyncStoreByTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SyncStoreByTypeResponse) ProtoMessage()    {}
func (*SyncStoreByTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d5edc3311953dfdc, []int{9}
}

func (m *SyncStoreByTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncStoreByTypeResponse.Unmarshal(m, b)
}
func (m *SyncStoreByTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncStoreByTypeResponse.Marshal(b, m, deterministic)
}
func (m *SyncStoreByTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncStoreByTypeResponse.Merge(m, src)
}
func (m *SyncStoreByTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SyncStoreByTypeResponse.Size(m)
}
func (m *SyncStoreByTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncStoreByTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SyncStoreByTypeResponse proto.InternalMessageInfo

func (m *SyncStoreByTypeResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func init() {
	proto.RegisterType((*ConvenienceStore)(nil), "lcos_protobuf.ConvenienceStore")
	proto.RegisterType((*SingleGetStoreByStoreID)(nil), "lcos_protobuf.SingleGetStoreByStoreID")
	proto.RegisterType((*BatchGetStoreByStoreIDRequest)(nil), "lcos_protobuf.BatchGetStoreByStoreIDRequest")
	proto.RegisterType((*BatchGetStoreByAddressIDRequest)(nil), "lcos_protobuf.BatchGetStoreByAddressIDRequest")
	proto.RegisterType((*BatchGetStoreByStoreIDResponse)(nil), "lcos_protobuf.BatchGetStoreByStoreIDResponse")
	proto.RegisterMapType((map[string]*ConvenienceStore)(nil), "lcos_protobuf.BatchGetStoreByStoreIDResponse.ResponseDictEntry")
	proto.RegisterType((*SingleGetStoreByNewStoreIdAndType)(nil), "lcos_protobuf.SingleGetStoreByNewStoreIdAndType")
	proto.RegisterType((*BatchGetStoreByNewStoreIdAndTypeRequest)(nil), "lcos_protobuf.BatchGetStoreByNewStoreIdAndTypeRequest")
	proto.RegisterType((*BatchGetStoreByNewStoreIdAndTypeResponse)(nil), "lcos_protobuf.BatchGetStoreByNewStoreIdAndTypeResponse")
	proto.RegisterMapType((map[string]*ConvenienceStore)(nil), "lcos_protobuf.BatchGetStoreByNewStoreIdAndTypeResponse.ResponseDictEntry")
	proto.RegisterType((*SyncStoreByTypeRequest)(nil), "lcos_protobuf.SyncStoreByTypeRequest")
	proto.RegisterType((*SyncStoreByTypeResponse)(nil), "lcos_protobuf.SyncStoreByTypeResponse")
}

func init() {
	proto.RegisterFile("lcos_tw_store.proto", fileDescriptor_d5edc3311953dfdc)
}

var fileDescriptor_d5edc3311953dfdc = []byte{
	// 983 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x56, 0xdf, 0x6f, 0x1b, 0x45,
	0x10, 0xee, 0x9d, 0xe3, 0x38, 0x1e, 0xe7, 0x87, 0xbb, 0x29, 0xf1, 0xc6, 0xa5, 0xc4, 0xb5, 0x44,
	0xb1, 0x50, 0xb1, 0x50, 0x24, 0x28, 0xea, 0x0b, 0x4a, 0x13, 0x7e, 0x58, 0x42, 0x16, 0x3a, 0x17,
	0xfa, 0x78, 0x5c, 0x6e, 0xa7, 0xf5, 0x2a, 0xe7, 0xdb, 0xcb, 0xee, 0x3a, 0x95, 0x85, 0x40, 0xf0,
	0x86, 0xc4, 0x33, 0x42, 0xe2, 0x15, 0xfe, 0x07, 0xf8, 0xf3, 0xd0, 0xee, 0x9e, 0xd3, 0xf8, 0x6c,
	0x27, 0x84, 0x06, 0x89, 0xb7, 0x9d, 0x6f, 0x76, 0x66, 0x3e, 0xcf, 0x7e, 0x33, 0x67, 0xd8, 0x4e,
	0x62, 0xa1, 0x42, 0xfd, 0x32, 0x54, 0x5a, 0x48, 0xec, 0x66, 0x52, 0x68, 0x41, 0x36, 0x2c, 0x68,
	0xcf, 0xc7, 0xe3, 0xe7, 0xcd, 0x2d, 0x6b, 0x1e, 0x47, 0x2a, 0xf7, 0xb7, 0x7f, 0x58, 0x85, 0xfa,
	0xa1, 0x48, 0xcf, 0x30, 0xe5, 0x98, 0xc6, 0x38, 0x30, 0xa1, 0x64, 0x13, 0x7c, 0xce, 0xa8, 0xd7,
	0xf2, 0x3a, 0x2b, 0x81, 0xcf, 0x19, 0xd9, 0x85, 0x35, 0x9b, 0x33, 0xe4, 0x8c, 0xfa, 0x2d, 0xaf,
	0x53, 0x0d, 0x2a, 0xd6, 0xee, 0x31, 0xd2, 0x82, 0xf5, 0x14, 0xf3, 0x92, 0xc6, 0x5d, 0xb2, 0x6e,
	0x48, 0xf1, 0xe5, 0x20, 0xbf, 0x41, 0x60, 0x25, 0x8d, 0x46, 0x48, 0x57, 0xac, 0xc7, 0x9e, 0x09,
	0x85, 0x4a, 0xc4, 0x98, 0x44, 0xa5, 0x68, 0xd9, 0xe5, 0xcb, 0x4d, 0x72, 0x0f, 0xc0, 0xe5, 0xd2,
	0x93, 0x0c, 0xe9, 0x6a, 0xcb, 0xeb, 0x6c, 0x04, 0x55, 0x8b, 0x3c, 0x9d, 0x64, 0x68, 0xdc, 0x71,
	0x22, 0x14, 0x86, 0x2c, 0xd2, 0x48, 0x2b, 0x36, 0xb6, 0x6a, 0x91, 0xa3, 0x48, 0xa3, 0x21, 0x8a,
	0x29, 0x73, 0xce, 0x35, 0x97, 0x18, 0x53, 0x66, 0x5d, 0x3b, 0xb0, 0xaa, 0x74, 0xa4, 0xc7, 0x8a,
	0x56, 0x6d, 0xd2, 0xdc, 0x22, 0x7b, 0x50, 0x63, 0x5c, 0x69, 0xc9, 0x63, 0x6d, 0xf8, 0x83, 0xfd,
	0xd1, 0x30, 0x85, 0x7a, 0x8c, 0x3c, 0x04, 0xc2, 0x55, 0x18, 0x25, 0x49, 0x18, 0x8b, 0x54, 0x71,
	0xa5, 0x31, 0x8d, 0x27, 0xb4, 0x66, 0x93, 0xd4, 0xb9, 0x3a, 0x48, 0x92, 0xc3, 0x57, 0xb8, 0x49,
	0x37, 0xce, 0x4c, 0x7d, 0x47, 0x62, 0xdd, 0xb5, 0xc3, 0x41, 0x96, 0xc7, 0x1d, 0x28, 0x67, 0x43,
	0x91, 0x22, 0xdd, 0xb0, 0x2e, 0x67, 0x98, 0xb0, 0x4c, 0x28, 0x1d, 0x99, 0x22, 0x0c, 0xe9, 0xa6,
	0x0b, 0x73, 0xd0, 0xa1, 0x60, 0xf6, 0x87, 0x73, 0x15, 0x9e, 0x71, 0xa9, 0xc7, 0x51, 0x42, 0xb7,
	0x5c, 0x5f, 0xb8, 0xfa, 0xda, 0x01, 0xa6, 0xc9, 0x31, 0xd7, 0x13, 0x5a, 0x77, 0x4d, 0x36, 0x67,
	0x83, 0x45, 0x12, 0x23, 0x7a, 0xdb, 0x61, 0xe6, 0x4c, 0x9a, 0xb0, 0x36, 0xfd, 0x69, 0x94, 0x58,
	0xfc, 0xdc, 0x36, 0xcc, 0x62, 0xcd, 0x47, 0x48, 0xb7, 0x6d, 0x76, 0x67, 0x18, 0x74, 0x64, 0xd1,
	0x3b, 0x0e, 0xb5, 0x06, 0x69, 0x40, 0x85, 0xc5, 0x52, 0x84, 0xa9, 0xa0, 0x6f, 0xd8, 0x34, 0xab,
	0xc6, 0xec, 0x0b, 0xb2, 0x0d, 0x65, 0xa9, 0x0c, 0xbc, 0xe3, 0xaa, 0x4a, 0xd5, 0x17, 0xe4, 0x2e,
	0x54, 0xd5, 0x90, 0x67, 0xee, 0x4d, 0x1b, 0xae, 0xac, 0x01, 0xec, 0x93, 0x36, 0xa0, 0x92, 0x45,
	0x7a, 0x68, 0x62, 0xa8, 0x4b, 0x65, 0xcc, 0xbe, 0x30, 0x8f, 0x19, 0x71, 0x95, 0xa0, 0xf1, 0xec,
	0xe6, 0x2a, 0x31, 0x76, 0x5f, 0x98, 0x98, 0x17, 0x92, 0x33, 0xe3, 0x69, 0xba, 0x18, 0x63, 0xba,
	0x98, 0x11, 0x67, 0xae, 0xd0, 0x5d, 0x17, 0x33, 0xe2, 0x6c, 0x5a, 0x47, 0x9c, 0x84, 0xb6, 0x23,
	0x6f, 0xba, 0x18, 0x71, 0x72, 0x20, 0x31, 0x6a, 0xff, 0xe4, 0x41, 0x63, 0xc0, 0xd3, 0x17, 0x09,
	0x7e, 0x86, 0xda, 0xaa, 0xf6, 0xc9, 0xc4, 0x89, 0xf7, 0xc8, 0x30, 0x1f, 0xa7, 0xfc, 0x74, 0x6c,
	0xb5, 0xed, 0xb5, 0x7c, 0xc3, 0xdc, 0x01, 0x3d, 0x56, 0xd0, 0xaa, 0x5f, 0xd4, 0xea, 0xc5, 0xa9,
	0x29, 0xcd, 0x4e, 0xcd, 0x2b, 0x31, 0xae, 0x5c, 0x14, 0x63, 0xfb, 0x77, 0x0f, 0xee, 0x3d, 0x89,
	0x74, 0x3c, 0x9c, 0x63, 0x12, 0xe0, 0xe9, 0x18, 0x95, 0x26, 0x8f, 0x00, 0x24, 0x9e, 0x86, 0x43,
	0x8c, 0x18, 0x4a, 0xcb, 0xa8, 0xb6, 0x4f, 0xbb, 0x33, 0x43, 0xde, 0x0d, 0xf0, 0xf4, 0x73, 0xeb,
	0x0f, 0xaa, 0x72, 0x7a, 0x24, 0x3d, 0x58, 0x97, 0x2e, 0x47, 0x98, 0x70, 0xa5, 0xa9, 0xdf, 0x2a,
	0x75, 0x6a, 0xfb, 0x0f, 0x0a, 0xa1, 0x4b, 0xfa, 0x10, 0xd4, 0xf2, 0xd8, 0x2f, 0xb8, 0xd2, 0xed,
	0xef, 0x60, 0xaf, 0x40, 0xf2, 0xc0, 0x4d, 0xef, 0x0d, 0xd0, 0xbc, 0xbf, 0x80, 0x66, 0x75, 0xb6,
	0xfc, 0x1f, 0x3e, 0xbc, 0xb5, 0xac, 0x49, 0x2a, 0x13, 0xa9, 0x42, 0xf2, 0x18, 0x6a, 0x12, 0x55,
	0x36, 0x5b, 0x7f, 0x77, 0xae, 0xbe, 0xca, 0x72, 0x02, 0x20, 0xcf, 0xcf, 0x84, 0xc1, 0x86, 0xcc,
	0xf3, 0x84, 0xcc, 0xcc, 0x89, 0xeb, 0xd4, 0xc7, 0x85, 0xe8, 0xcb, 0x19, 0x74, 0xa7, 0x87, 0x23,
	0x1e, 0xeb, 0x4f, 0x52, 0x2d, 0x27, 0xc1, 0xba, 0xbc, 0x00, 0x35, 0xbf, 0x81, 0xdb, 0x73, 0x57,
	0x48, 0x1d, 0x4a, 0x27, 0x38, 0xb1, 0x8b, 0xb7, 0x1a, 0x98, 0x23, 0xf9, 0x00, 0xca, 0x67, 0x51,
	0x32, 0x76, 0xea, 0xaa, 0xed, 0xef, 0x15, 0x48, 0x14, 0x37, 0x77, 0xe0, 0x6e, 0x3f, 0xf6, 0x3f,
	0xf2, 0xda, 0x3f, 0x7a, 0x70, 0xbf, 0xf8, 0x9c, 0xfd, 0xf3, 0xb5, 0x7c, 0x90, 0xba, 0xa9, 0xb8,
	0x54, 0xe0, 0xc5, 0xe5, 0xee, 0xcf, 0x2d, 0xf7, 0xd9, 0x11, 0x28, 0x15, 0x46, 0xa0, 0xfd, 0xa7,
	0x07, 0xef, 0x14, 0x1a, 0x35, 0x47, 0xe1, 0xb5, 0x25, 0x33, 0x58, 0xa8, 0xec, 0xf7, 0xaf, 0x50,
	0xf6, 0x3c, 0x8f, 0x19, 0x91, 0xfd, 0xe5, 0x43, 0xe7, 0x6a, 0xe6, 0x37, 0x20, 0xb7, 0x74, 0xb1,
	0xdc, 0x7a, 0x97, 0xcb, 0x6d, 0x29, 0x97, 0xff, 0x81, 0xf0, 0x7e, 0xf3, 0x60, 0x67, 0x30, 0x49,
	0xe3, 0x9c, 0xea, 0x8d, 0xbc, 0x71, 0x71, 0xd5, 0xfa, 0xb3, 0xab, 0xb6, 0x03, 0x75, 0x89, 0x23,
	0xa1, 0x31, 0x7c, 0xce, 0x13, 0x0c, 0xcd, 0x07, 0x84, 0x96, 0xac, 0x98, 0x37, 0x1d, 0xfe, 0x29,
	0x4f, 0xf0, 0xcb, 0x48, 0x0f, 0xdb, 0x5f, 0x41, 0x63, 0x8e, 0xdb, 0xeb, 0xbf, 0xe2, 0xfe, 0xaf,
	0x65, 0xd8, 0x7c, 0xfa, 0xcc, 0x66, 0x1d, 0xa0, 0x3c, 0xe3, 0x31, 0x92, 0xef, 0x97, 0xad, 0xf2,
	0xe9, 0xe8, 0x3d, 0xfc, 0x87, 0x1b, 0xc5, 0xb6, 0xae, 0xf9, 0xde, 0xb5, 0xf6, 0x4f, 0xfb, 0x16,
	0xf9, 0xd9, 0x83, 0x77, 0x17, 0x5e, 0x9a, 0x4a, 0xe6, 0x19, 0xd7, 0x43, 0x31, 0xd6, 0x03, 0xf7,
	0x3f, 0xe8, 0x3f, 0x66, 0xf3, 0x8b, 0x07, 0xad, 0xab, 0x34, 0x4c, 0x3e, 0xbc, 0xb6, 0xe8, 0x1d,
	0x9b, 0x47, 0xff, 0x72, 0x58, 0xda, 0xb7, 0xc8, 0xb7, 0x40, 0x97, 0x7d, 0xcb, 0x48, 0xf7, 0xf2,
	0xb4, 0xc5, 0x8f, 0xde, 0xf5, 0x9b, 0x72, 0x0c, 0x5b, 0x05, 0x31, 0x92, 0xb7, 0x8b, 0x6b, 0x6b,
	0xe1, 0x20, 0x35, 0x1f, 0x5c, 0x75, 0x6d, 0x5a, 0xe3, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0xcb,
	0x6b, 0x0e, 0xe6, 0x16, 0x0c, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// TWStoreServiceClient is the client API for TWStoreService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TWStoreServiceClient interface {
	BatchGetStoreByStoreIDAndType(ctx context.Context, in *BatchGetStoreByStoreIDRequest, opts ...grpc.CallOption) (*BatchGetStoreByStoreIDResponse, error)
	BatchGetStoreByStoreIdAndTypeWithoutStatus(ctx context.Context, in *BatchGetStoreByStoreIDRequest, opts ...grpc.CallOption) (*BatchGetStoreByStoreIDResponse, error)
	BatchGetStoreByNewStoreIdAndType(ctx context.Context, in *BatchGetStoreByNewStoreIdAndTypeRequest, opts ...grpc.CallOption) (*BatchGetStoreByNewStoreIdAndTypeResponse, error)
	BatchGetStoreByAddressID(ctx context.Context, in *BatchGetStoreByAddressIDRequest, opts ...grpc.CallOption) (*BatchGetStoreByStoreIDResponse, error)
	SyncStoreByType(ctx context.Context, in *SyncStoreByTypeRequest, opts ...grpc.CallOption) (*SyncStoreByTypeResponse, error)
}

type tWStoreServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTWStoreServiceClient(cc grpc.ClientConnInterface) TWStoreServiceClient {
	return &tWStoreServiceClient{cc}
}

func (c *tWStoreServiceClient) BatchGetStoreByStoreIDAndType(ctx context.Context, in *BatchGetStoreByStoreIDRequest, opts ...grpc.CallOption) (*BatchGetStoreByStoreIDResponse, error) {
	out := new(BatchGetStoreByStoreIDResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.TWStoreService/BatchGetStoreByStoreIDAndType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tWStoreServiceClient) BatchGetStoreByStoreIdAndTypeWithoutStatus(ctx context.Context, in *BatchGetStoreByStoreIDRequest, opts ...grpc.CallOption) (*BatchGetStoreByStoreIDResponse, error) {
	out := new(BatchGetStoreByStoreIDResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.TWStoreService/BatchGetStoreByStoreIdAndTypeWithoutStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tWStoreServiceClient) BatchGetStoreByNewStoreIdAndType(ctx context.Context, in *BatchGetStoreByNewStoreIdAndTypeRequest, opts ...grpc.CallOption) (*BatchGetStoreByNewStoreIdAndTypeResponse, error) {
	out := new(BatchGetStoreByNewStoreIdAndTypeResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.TWStoreService/BatchGetStoreByNewStoreIdAndType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tWStoreServiceClient) BatchGetStoreByAddressID(ctx context.Context, in *BatchGetStoreByAddressIDRequest, opts ...grpc.CallOption) (*BatchGetStoreByStoreIDResponse, error) {
	out := new(BatchGetStoreByStoreIDResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.TWStoreService/BatchGetStoreByAddressID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tWStoreServiceClient) SyncStoreByType(ctx context.Context, in *SyncStoreByTypeRequest, opts ...grpc.CallOption) (*SyncStoreByTypeResponse, error) {
	out := new(SyncStoreByTypeResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.TWStoreService/SyncStoreByType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TWStoreServiceServer is the server API for TWStoreService service.
type TWStoreServiceServer interface {
	BatchGetStoreByStoreIDAndType(context.Context, *BatchGetStoreByStoreIDRequest) (*BatchGetStoreByStoreIDResponse, error)
	BatchGetStoreByStoreIdAndTypeWithoutStatus(context.Context, *BatchGetStoreByStoreIDRequest) (*BatchGetStoreByStoreIDResponse, error)
	BatchGetStoreByNewStoreIdAndType(context.Context, *BatchGetStoreByNewStoreIdAndTypeRequest) (*BatchGetStoreByNewStoreIdAndTypeResponse, error)
	BatchGetStoreByAddressID(context.Context, *BatchGetStoreByAddressIDRequest) (*BatchGetStoreByStoreIDResponse, error)
	SyncStoreByType(context.Context, *SyncStoreByTypeRequest) (*SyncStoreByTypeResponse, error)
}

// UnimplementedTWStoreServiceServer can be embedded to have forward compatible implementations.
type UnimplementedTWStoreServiceServer struct {
}

func (*UnimplementedTWStoreServiceServer) BatchGetStoreByStoreIDAndType(ctx context.Context, req *BatchGetStoreByStoreIDRequest) (*BatchGetStoreByStoreIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetStoreByStoreIDAndType not implemented")
}
func (*UnimplementedTWStoreServiceServer) BatchGetStoreByStoreIdAndTypeWithoutStatus(ctx context.Context, req *BatchGetStoreByStoreIDRequest) (*BatchGetStoreByStoreIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetStoreByStoreIdAndTypeWithoutStatus not implemented")
}
func (*UnimplementedTWStoreServiceServer) BatchGetStoreByNewStoreIdAndType(ctx context.Context, req *BatchGetStoreByNewStoreIdAndTypeRequest) (*BatchGetStoreByNewStoreIdAndTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetStoreByNewStoreIdAndType not implemented")
}
func (*UnimplementedTWStoreServiceServer) BatchGetStoreByAddressID(ctx context.Context, req *BatchGetStoreByAddressIDRequest) (*BatchGetStoreByStoreIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetStoreByAddressID not implemented")
}
func (*UnimplementedTWStoreServiceServer) SyncStoreByType(ctx context.Context, req *SyncStoreByTypeRequest) (*SyncStoreByTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncStoreByType not implemented")
}

func RegisterTWStoreServiceServer(s *grpc.Server, srv TWStoreServiceServer) {
	s.RegisterService(&_TWStoreService_serviceDesc, srv)
}

func _TWStoreService_BatchGetStoreByStoreIDAndType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetStoreByStoreIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TWStoreServiceServer).BatchGetStoreByStoreIDAndType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.TWStoreService/BatchGetStoreByStoreIDAndType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TWStoreServiceServer).BatchGetStoreByStoreIDAndType(ctx, req.(*BatchGetStoreByStoreIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TWStoreService_BatchGetStoreByStoreIdAndTypeWithoutStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetStoreByStoreIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TWStoreServiceServer).BatchGetStoreByStoreIdAndTypeWithoutStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.TWStoreService/BatchGetStoreByStoreIdAndTypeWithoutStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TWStoreServiceServer).BatchGetStoreByStoreIdAndTypeWithoutStatus(ctx, req.(*BatchGetStoreByStoreIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TWStoreService_BatchGetStoreByNewStoreIdAndType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetStoreByNewStoreIdAndTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TWStoreServiceServer).BatchGetStoreByNewStoreIdAndType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.TWStoreService/BatchGetStoreByNewStoreIdAndType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TWStoreServiceServer).BatchGetStoreByNewStoreIdAndType(ctx, req.(*BatchGetStoreByNewStoreIdAndTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TWStoreService_BatchGetStoreByAddressID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetStoreByAddressIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TWStoreServiceServer).BatchGetStoreByAddressID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.TWStoreService/BatchGetStoreByAddressID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TWStoreServiceServer).BatchGetStoreByAddressID(ctx, req.(*BatchGetStoreByAddressIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TWStoreService_SyncStoreByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncStoreByTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TWStoreServiceServer).SyncStoreByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.TWStoreService/SyncStoreByType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TWStoreServiceServer).SyncStoreByType(ctx, req.(*SyncStoreByTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _TWStoreService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.TWStoreService",
	HandlerType: (*TWStoreServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetStoreByStoreIDAndType",
			Handler:    _TWStoreService_BatchGetStoreByStoreIDAndType_Handler,
		},
		{
			MethodName: "BatchGetStoreByStoreIdAndTypeWithoutStatus",
			Handler:    _TWStoreService_BatchGetStoreByStoreIdAndTypeWithoutStatus_Handler,
		},
		{
			MethodName: "BatchGetStoreByNewStoreIdAndType",
			Handler:    _TWStoreService_BatchGetStoreByNewStoreIdAndType_Handler,
		},
		{
			MethodName: "BatchGetStoreByAddressID",
			Handler:    _TWStoreService_BatchGetStoreByAddressID_Handler,
		},
		{
			MethodName: "SyncStoreByType",
			Handler:    _TWStoreService_SyncStoreByType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_tw_store.proto",
}
