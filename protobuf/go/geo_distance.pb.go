// Code generated by protoc-gen-go. DO NOT EDIT.
// source: geo_distance.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ListAllGeoClientConfigRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ListAllGeoClientConfigRequest) Reset()         { *m = ListAllGeoClientConfigRequest{} }
func (m *ListAllGeoClientConfigRequest) String() string { return proto.CompactTextString(m) }
func (*ListAllGeoClientConfigRequest) ProtoMessage()    {}
func (*ListAllGeoClientConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a379b80c2305e35, []int{0}
}

func (m *ListAllGeoClientConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListAllGeoClientConfigRequest.Unmarshal(m, b)
}
func (m *ListAllGeoClientConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListAllGeoClientConfigRequest.Marshal(b, m, deterministic)
}
func (m *ListAllGeoClientConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListAllGeoClientConfigRequest.Merge(m, src)
}
func (m *ListAllGeoClientConfigRequest) XXX_Size() int {
	return xxx_messageInfo_ListAllGeoClientConfigRequest.Size(m)
}
func (m *ListAllGeoClientConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListAllGeoClientConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListAllGeoClientConfigRequest proto.InternalMessageInfo

func (m *ListAllGeoClientConfigRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

type ListAllGeoClientConfigResponse struct {
	RespHeader           *RespHeader        `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ClientList           []*GeoClientConfig `protobuf:"bytes,2,rep,name=client_list,json=clientList" json:"client_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ListAllGeoClientConfigResponse) Reset()         { *m = ListAllGeoClientConfigResponse{} }
func (m *ListAllGeoClientConfigResponse) String() string { return proto.CompactTextString(m) }
func (*ListAllGeoClientConfigResponse) ProtoMessage()    {}
func (*ListAllGeoClientConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a379b80c2305e35, []int{1}
}

func (m *ListAllGeoClientConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListAllGeoClientConfigResponse.Unmarshal(m, b)
}
func (m *ListAllGeoClientConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListAllGeoClientConfigResponse.Marshal(b, m, deterministic)
}
func (m *ListAllGeoClientConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListAllGeoClientConfigResponse.Merge(m, src)
}
func (m *ListAllGeoClientConfigResponse) XXX_Size() int {
	return xxx_messageInfo_ListAllGeoClientConfigResponse.Size(m)
}
func (m *ListAllGeoClientConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListAllGeoClientConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListAllGeoClientConfigResponse proto.InternalMessageInfo

func (m *ListAllGeoClientConfigResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *ListAllGeoClientConfigResponse) GetClientList() []*GeoClientConfig {
	if m != nil {
		return m.ClientList
	}
	return nil
}

type GeoClientConfig struct {
	Project              *string  `protobuf:"bytes,1,req,name=project" json:"project,omitempty"`
	Scenario             *string  `protobuf:"bytes,2,req,name=scenario" json:"scenario,omitempty"`
	GeoUser              *string  `protobuf:"bytes,3,req,name=geo_user,json=geoUser" json:"geo_user,omitempty"`
	GeoProject           *string  `protobuf:"bytes,4,req,name=geo_project,json=geoProject" json:"geo_project,omitempty"`
	GeoClientKey         *string  `protobuf:"bytes,5,req,name=geo_client_key,json=geoClientKey" json:"geo_client_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GeoClientConfig) Reset()         { *m = GeoClientConfig{} }
func (m *GeoClientConfig) String() string { return proto.CompactTextString(m) }
func (*GeoClientConfig) ProtoMessage()    {}
func (*GeoClientConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a379b80c2305e35, []int{2}
}

func (m *GeoClientConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeoClientConfig.Unmarshal(m, b)
}
func (m *GeoClientConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeoClientConfig.Marshal(b, m, deterministic)
}
func (m *GeoClientConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeoClientConfig.Merge(m, src)
}
func (m *GeoClientConfig) XXX_Size() int {
	return xxx_messageInfo_GeoClientConfig.Size(m)
}
func (m *GeoClientConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_GeoClientConfig.DiscardUnknown(m)
}

var xxx_messageInfo_GeoClientConfig proto.InternalMessageInfo

func (m *GeoClientConfig) GetProject() string {
	if m != nil && m.Project != nil {
		return *m.Project
	}
	return ""
}

func (m *GeoClientConfig) GetScenario() string {
	if m != nil && m.Scenario != nil {
		return *m.Scenario
	}
	return ""
}

func (m *GeoClientConfig) GetGeoUser() string {
	if m != nil && m.GeoUser != nil {
		return *m.GeoUser
	}
	return ""
}

func (m *GeoClientConfig) GetGeoProject() string {
	if m != nil && m.GeoProject != nil {
		return *m.GeoProject
	}
	return ""
}

func (m *GeoClientConfig) GetGeoClientKey() string {
	if m != nil && m.GeoClientKey != nil {
		return *m.GeoClientKey
	}
	return ""
}

type ListAllGeoDistanceConfigRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ListAllGeoDistanceConfigRequest) Reset()         { *m = ListAllGeoDistanceConfigRequest{} }
func (m *ListAllGeoDistanceConfigRequest) String() string { return proto.CompactTextString(m) }
func (*ListAllGeoDistanceConfigRequest) ProtoMessage()    {}
func (*ListAllGeoDistanceConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a379b80c2305e35, []int{3}
}

func (m *ListAllGeoDistanceConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListAllGeoDistanceConfigRequest.Unmarshal(m, b)
}
func (m *ListAllGeoDistanceConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListAllGeoDistanceConfigRequest.Marshal(b, m, deterministic)
}
func (m *ListAllGeoDistanceConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListAllGeoDistanceConfigRequest.Merge(m, src)
}
func (m *ListAllGeoDistanceConfigRequest) XXX_Size() int {
	return xxx_messageInfo_ListAllGeoDistanceConfigRequest.Size(m)
}
func (m *ListAllGeoDistanceConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListAllGeoDistanceConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListAllGeoDistanceConfigRequest proto.InternalMessageInfo

func (m *ListAllGeoDistanceConfigRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

type ListAllGeoDistanceConfigResponse struct {
	RespHeader           *RespHeader                 `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ProductList          []*ProductGeoDistanceConfig `protobuf:"bytes,2,rep,name=product_list,json=productList" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *ListAllGeoDistanceConfigResponse) Reset()         { *m = ListAllGeoDistanceConfigResponse{} }
func (m *ListAllGeoDistanceConfigResponse) String() string { return proto.CompactTextString(m) }
func (*ListAllGeoDistanceConfigResponse) ProtoMessage()    {}
func (*ListAllGeoDistanceConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a379b80c2305e35, []int{4}
}

func (m *ListAllGeoDistanceConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListAllGeoDistanceConfigResponse.Unmarshal(m, b)
}
func (m *ListAllGeoDistanceConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListAllGeoDistanceConfigResponse.Marshal(b, m, deterministic)
}
func (m *ListAllGeoDistanceConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListAllGeoDistanceConfigResponse.Merge(m, src)
}
func (m *ListAllGeoDistanceConfigResponse) XXX_Size() int {
	return xxx_messageInfo_ListAllGeoDistanceConfigResponse.Size(m)
}
func (m *ListAllGeoDistanceConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListAllGeoDistanceConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListAllGeoDistanceConfigResponse proto.InternalMessageInfo

func (m *ListAllGeoDistanceConfigResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *ListAllGeoDistanceConfigResponse) GetProductList() []*ProductGeoDistanceConfig {
	if m != nil {
		return m.ProductList
	}
	return nil
}

type ProductGeoDistanceConfig struct {
	ProductId            *string  `protobuf:"bytes,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	GeoDistanceMode      *string  `protobuf:"bytes,2,req,name=geo_distance_mode,json=geoDistanceMode" json:"geo_distance_mode,omitempty"`
	GeoDistanceAvoid     []string `protobuf:"bytes,3,rep,name=geo_distance_avoid,json=geoDistanceAvoid" json:"geo_distance_avoid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductGeoDistanceConfig) Reset()         { *m = ProductGeoDistanceConfig{} }
func (m *ProductGeoDistanceConfig) String() string { return proto.CompactTextString(m) }
func (*ProductGeoDistanceConfig) ProtoMessage()    {}
func (*ProductGeoDistanceConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_1a379b80c2305e35, []int{5}
}

func (m *ProductGeoDistanceConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductGeoDistanceConfig.Unmarshal(m, b)
}
func (m *ProductGeoDistanceConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductGeoDistanceConfig.Marshal(b, m, deterministic)
}
func (m *ProductGeoDistanceConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductGeoDistanceConfig.Merge(m, src)
}
func (m *ProductGeoDistanceConfig) XXX_Size() int {
	return xxx_messageInfo_ProductGeoDistanceConfig.Size(m)
}
func (m *ProductGeoDistanceConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductGeoDistanceConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ProductGeoDistanceConfig proto.InternalMessageInfo

func (m *ProductGeoDistanceConfig) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *ProductGeoDistanceConfig) GetGeoDistanceMode() string {
	if m != nil && m.GeoDistanceMode != nil {
		return *m.GeoDistanceMode
	}
	return ""
}

func (m *ProductGeoDistanceConfig) GetGeoDistanceAvoid() []string {
	if m != nil {
		return m.GeoDistanceAvoid
	}
	return nil
}

func init() {
	proto.RegisterType((*ListAllGeoClientConfigRequest)(nil), "lcos_protobuf.ListAllGeoClientConfigRequest")
	proto.RegisterType((*ListAllGeoClientConfigResponse)(nil), "lcos_protobuf.ListAllGeoClientConfigResponse")
	proto.RegisterType((*GeoClientConfig)(nil), "lcos_protobuf.GeoClientConfig")
	proto.RegisterType((*ListAllGeoDistanceConfigRequest)(nil), "lcos_protobuf.ListAllGeoDistanceConfigRequest")
	proto.RegisterType((*ListAllGeoDistanceConfigResponse)(nil), "lcos_protobuf.ListAllGeoDistanceConfigResponse")
	proto.RegisterType((*ProductGeoDistanceConfig)(nil), "lcos_protobuf.ProductGeoDistanceConfig")
}

func init() {
	proto.RegisterFile("geo_distance.proto", fileDescriptor_1a379b80c2305e35)
}

var fileDescriptor_1a379b80c2305e35 = []byte{
	// 457 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x53, 0xdf, 0x6b, 0xd4, 0x40,
	0x10, 0x36, 0x77, 0x8a, 0xbd, 0x49, 0xf5, 0x74, 0x1e, 0x64, 0x7b, 0xd2, 0xf6, 0x08, 0x82, 0x87,
	0xd4, 0x13, 0xee, 0x45, 0xf0, 0x45, 0x4a, 0x05, 0x7f, 0x43, 0x89, 0x08, 0xe2, 0x4b, 0x48, 0x77,
	0xa7, 0x71, 0x35, 0x66, 0x73, 0xbb, 0x9b, 0x4a, 0x1f, 0xfc, 0x2b, 0xc4, 0x47, 0xff, 0x00, 0xfd,
	0x2b, 0x25, 0x7b, 0x9b, 0xf6, 0x2e, 0x6d, 0x4a, 0x1f, 0xfa, 0x36, 0xb3, 0xdf, 0x37, 0xdf, 0x4c,
	0xe6, 0xcb, 0x00, 0x66, 0xa4, 0x12, 0x21, 0x8d, 0x4d, 0x0b, 0x4e, 0xd3, 0x52, 0x2b, 0xab, 0xf0,
	0x56, 0xce, 0x95, 0x49, 0x5c, 0x7c, 0x50, 0x1d, 0x8e, 0x86, 0x2e, 0x3d, 0x48, 0x8d, 0xc7, 0xa3,
	0x4f, 0xb0, 0xf9, 0x4e, 0x1a, 0xbb, 0x9b, 0xe7, 0x2f, 0x49, 0xed, 0xe5, 0x92, 0x0a, 0xbb, 0xa7,
	0x8a, 0x43, 0x99, 0xc5, 0x34, 0xaf, 0xc8, 0x58, 0x7c, 0x0a, 0xa0, 0x69, 0x9e, 0x7c, 0xa1, 0x54,
	0x90, 0x66, 0xc1, 0xb8, 0x37, 0x09, 0x67, 0x6c, 0xba, 0xa2, 0x3a, 0x8d, 0x69, 0xfe, 0xca, 0xe1,
	0xf1, 0x40, 0x37, 0x61, 0xf4, 0x27, 0x80, 0xad, 0x2e, 0x69, 0x53, 0xaa, 0xc2, 0x10, 0x3e, 0x83,
	0x50, 0x93, 0x29, 0x57, 0xc5, 0x37, 0xce, 0x88, 0x9b, 0xd2, 0xab, 0x83, 0x3e, 0x89, 0xf1, 0x39,
	0x84, 0xdc, 0x69, 0x26, 0xb9, 0x34, 0x96, 0xf5, 0xc6, 0xfd, 0x49, 0x38, 0xdb, 0x6a, 0xd5, 0xb6,
	0x1b, 0xc3, 0xa2, 0xa4, 0x1e, 0x2b, 0xfa, 0x1b, 0xc0, 0xb0, 0x85, 0x23, 0x83, 0x9b, 0xa5, 0x56,
	0x5f, 0x89, 0x5b, 0x37, 0xcc, 0x20, 0x6e, 0x52, 0x1c, 0xc1, 0x9a, 0xe1, 0x54, 0xa4, 0x5a, 0x2a,
	0xd6, 0x73, 0xd0, 0x49, 0x8e, 0x1b, 0xb0, 0x56, 0x6f, 0xbe, 0x32, 0xa4, 0x59, 0x7f, 0x51, 0x96,
	0x91, 0xfa, 0x68, 0x48, 0xe3, 0x36, 0x84, 0x35, 0xd4, 0x88, 0x5e, 0x77, 0x28, 0x64, 0xa4, 0xf6,
	0xbd, 0xee, 0x03, 0xb8, 0x5d, 0x13, 0xfc, 0xa7, 0x7c, 0xa3, 0x63, 0x76, 0xc3, 0x71, 0xd6, 0xb3,
	0x66, 0xb4, 0xb7, 0x74, 0x1c, 0x7d, 0x86, 0xed, 0xd3, 0x55, 0xbe, 0xf0, 0x0e, 0x5f, 0x91, 0x4f,
	0xff, 0x02, 0x18, 0x77, 0x8b, 0x5f, 0x81, 0x53, 0x6f, 0x60, 0xbd, 0xd4, 0x4a, 0x54, 0x7c, 0xc5,
	0xaa, 0x87, 0xad, 0xe2, 0xfd, 0x05, 0xe5, 0xec, 0x08, 0xa1, 0x2f, 0x76, 0xa6, 0xfd, 0x0a, 0x80,
	0x75, 0x31, 0x71, 0x13, 0xa0, 0x69, 0x24, 0x85, 0x37, 0x70, 0xe0, 0x5f, 0x5e, 0x0b, 0x7c, 0x04,
	0x77, 0x97, 0x0f, 0x24, 0xf9, 0xae, 0x04, 0x79, 0x2f, 0x87, 0xd9, 0xa9, 0xd8, 0x7b, 0x25, 0x08,
	0x77, 0x56, 0x8f, 0x29, 0x49, 0x8f, 0x94, 0x14, 0xac, 0x3f, 0xee, 0x4f, 0x06, 0xf1, 0x9d, 0x25,
	0xf2, 0x6e, 0xfd, 0x3e, 0xfb, 0xdd, 0x83, 0xfb, 0xcb, 0xe3, 0xa4, 0x39, 0xaf, 0xf2, 0xd4, 0xd2,
	0x07, 0xd2, 0x47, 0x92, 0x13, 0xfe, 0x80, 0x7b, 0xe7, 0x5f, 0x02, 0xee, 0xb4, 0xb6, 0x70, 0xe1,
	0x2d, 0x8e, 0x1e, 0x5f, 0x92, 0xbd, 0x30, 0x2d, 0xba, 0x86, 0x3f, 0x81, 0x75, 0x59, 0x8b, 0xd3,
	0x4e, 0xb1, 0x73, 0x7f, 0xb0, 0xd1, 0x93, 0x4b, 0xf3, 0x9b, 0xf6, 0xff, 0x03, 0x00, 0x00, 0xff,
	0xff, 0x97, 0x90, 0x9b, 0x73, 0x91, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// GeoDistanceCalculateServiceClient is the client API for GeoDistanceCalculateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GeoDistanceCalculateServiceClient interface {
	ListAllGeoClientConfig(ctx context.Context, in *ListAllGeoClientConfigRequest, opts ...grpc.CallOption) (*ListAllGeoClientConfigResponse, error)
	ListAllGeoDistanceConfig(ctx context.Context, in *ListAllGeoDistanceConfigRequest, opts ...grpc.CallOption) (*ListAllGeoDistanceConfigResponse, error)
}

type geoDistanceCalculateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGeoDistanceCalculateServiceClient(cc grpc.ClientConnInterface) GeoDistanceCalculateServiceClient {
	return &geoDistanceCalculateServiceClient{cc}
}

func (c *geoDistanceCalculateServiceClient) ListAllGeoClientConfig(ctx context.Context, in *ListAllGeoClientConfigRequest, opts ...grpc.CallOption) (*ListAllGeoClientConfigResponse, error) {
	out := new(ListAllGeoClientConfigResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.GeoDistanceCalculateService/ListAllGeoClientConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *geoDistanceCalculateServiceClient) ListAllGeoDistanceConfig(ctx context.Context, in *ListAllGeoDistanceConfigRequest, opts ...grpc.CallOption) (*ListAllGeoDistanceConfigResponse, error) {
	out := new(ListAllGeoDistanceConfigResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.GeoDistanceCalculateService/ListAllGeoDistanceConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GeoDistanceCalculateServiceServer is the server API for GeoDistanceCalculateService service.
type GeoDistanceCalculateServiceServer interface {
	ListAllGeoClientConfig(context.Context, *ListAllGeoClientConfigRequest) (*ListAllGeoClientConfigResponse, error)
	ListAllGeoDistanceConfig(context.Context, *ListAllGeoDistanceConfigRequest) (*ListAllGeoDistanceConfigResponse, error)
}

// UnimplementedGeoDistanceCalculateServiceServer can be embedded to have forward compatible implementations.
type UnimplementedGeoDistanceCalculateServiceServer struct {
}

func (*UnimplementedGeoDistanceCalculateServiceServer) ListAllGeoClientConfig(ctx context.Context, req *ListAllGeoClientConfigRequest) (*ListAllGeoClientConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAllGeoClientConfig not implemented")
}
func (*UnimplementedGeoDistanceCalculateServiceServer) ListAllGeoDistanceConfig(ctx context.Context, req *ListAllGeoDistanceConfigRequest) (*ListAllGeoDistanceConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAllGeoDistanceConfig not implemented")
}

func RegisterGeoDistanceCalculateServiceServer(s *grpc.Server, srv GeoDistanceCalculateServiceServer) {
	s.RegisterService(&_GeoDistanceCalculateService_serviceDesc, srv)
}

func _GeoDistanceCalculateService_ListAllGeoClientConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAllGeoClientConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GeoDistanceCalculateServiceServer).ListAllGeoClientConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.GeoDistanceCalculateService/ListAllGeoClientConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GeoDistanceCalculateServiceServer).ListAllGeoClientConfig(ctx, req.(*ListAllGeoClientConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GeoDistanceCalculateService_ListAllGeoDistanceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAllGeoDistanceConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GeoDistanceCalculateServiceServer).ListAllGeoDistanceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.GeoDistanceCalculateService/ListAllGeoDistanceConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GeoDistanceCalculateServiceServer).ListAllGeoDistanceConfig(ctx, req.(*ListAllGeoDistanceConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _GeoDistanceCalculateService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.GeoDistanceCalculateService",
	HandlerType: (*GeoDistanceCalculateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAllGeoClientConfig",
			Handler:    _GeoDistanceCalculateService_ListAllGeoClientConfig_Handler,
		},
		{
			MethodName: "ListAllGeoDistanceConfig",
			Handler:    _GeoDistanceCalculateService_ListAllGeoDistanceConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "geo_distance.proto",
}
