// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_pis.proto

package lcos_protobuf

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PisReqHeader struct {
	RequestId            *string  `protobuf:"bytes,1,req,name=request_id,json=requestId" json:"request_id,omitempty"`
	RequestIp            *string  `protobuf:"bytes,2,req,name=request_ip,json=requestIp" json:"request_ip,omitempty"`
	Timestamp            *uint32  `protobuf:"varint,3,req,name=timestamp" json:"timestamp,omitempty"`
	ClientId             *string  `protobuf:"bytes,4,req,name=client_id,json=clientId" json:"client_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PisReqHeader) Reset()         { *m = PisReqHeader{} }
func (m *PisReqHeader) String() string { return proto.CompactTextString(m) }
func (*PisReqHeader) ProtoMessage()    {}
func (*PisReqHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_2103a35ea7836b5b, []int{0}
}

func (m *PisReqHeader) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PisReqHeader.Unmarshal(m, b)
}
func (m *PisReqHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PisReqHeader.Marshal(b, m, deterministic)
}
func (m *PisReqHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PisReqHeader.Merge(m, src)
}
func (m *PisReqHeader) XXX_Size() int {
	return xxx_messageInfo_PisReqHeader.Size(m)
}
func (m *PisReqHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_PisReqHeader.DiscardUnknown(m)
}

var xxx_messageInfo_PisReqHeader proto.InternalMessageInfo

func (m *PisReqHeader) GetRequestId() string {
	if m != nil && m.RequestId != nil {
		return *m.RequestId
	}
	return ""
}

func (m *PisReqHeader) GetRequestIp() string {
	if m != nil && m.RequestIp != nil {
		return *m.RequestIp
	}
	return ""
}

func (m *PisReqHeader) GetTimestamp() uint32 {
	if m != nil && m.Timestamp != nil {
		return *m.Timestamp
	}
	return 0
}

func (m *PisReqHeader) GetClientId() string {
	if m != nil && m.ClientId != nil {
		return *m.ClientId
	}
	return ""
}

type PisRespHeader struct {
	Retcode              *int32   `protobuf:"varint,1,req,name=retcode" json:"retcode,omitempty"`
	Message              *string  `protobuf:"bytes,2,req,name=message" json:"message,omitempty"`
	RequestId            *string  `protobuf:"bytes,3,req,name=request_id,json=requestId" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PisRespHeader) Reset()         { *m = PisRespHeader{} }
func (m *PisRespHeader) String() string { return proto.CompactTextString(m) }
func (*PisRespHeader) ProtoMessage()    {}
func (*PisRespHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_2103a35ea7836b5b, []int{1}
}

func (m *PisRespHeader) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PisRespHeader.Unmarshal(m, b)
}
func (m *PisRespHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PisRespHeader.Marshal(b, m, deterministic)
}
func (m *PisRespHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PisRespHeader.Merge(m, src)
}
func (m *PisRespHeader) XXX_Size() int {
	return xxx_messageInfo_PisRespHeader.Size(m)
}
func (m *PisRespHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_PisRespHeader.DiscardUnknown(m)
}

var xxx_messageInfo_PisRespHeader proto.InternalMessageInfo

func (m *PisRespHeader) GetRetcode() int32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *PisRespHeader) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

func (m *PisRespHeader) GetRequestId() string {
	if m != nil && m.RequestId != nil {
		return *m.RequestId
	}
	return ""
}

type DistributionRequest struct {
	ReqHeader             *PisReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	DistributionProcessId *string       `protobuf:"bytes,2,req,name=distribution_process_id,json=distributionProcessId" json:"distribution_process_id,omitempty"`
	DistributionId        *string       `protobuf:"bytes,3,req,name=distribution_id,json=distributionId" json:"distribution_id,omitempty"`
	ResourceId            *uint32       `protobuf:"varint,4,req,name=resource_id,json=resourceId" json:"resource_id,omitempty"`
	BusinessData          *string       `protobuf:"bytes,5,req,name=business_data,json=businessData" json:"business_data,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}      `json:"-"`
	XXX_unrecognized      []byte        `json:"-"`
	XXX_sizecache         int32         `json:"-"`
}

func (m *DistributionRequest) Reset()         { *m = DistributionRequest{} }
func (m *DistributionRequest) String() string { return proto.CompactTextString(m) }
func (*DistributionRequest) ProtoMessage()    {}
func (*DistributionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2103a35ea7836b5b, []int{2}
}

func (m *DistributionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DistributionRequest.Unmarshal(m, b)
}
func (m *DistributionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DistributionRequest.Marshal(b, m, deterministic)
}
func (m *DistributionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DistributionRequest.Merge(m, src)
}
func (m *DistributionRequest) XXX_Size() int {
	return xxx_messageInfo_DistributionRequest.Size(m)
}
func (m *DistributionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DistributionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DistributionRequest proto.InternalMessageInfo

func (m *DistributionRequest) GetReqHeader() *PisReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *DistributionRequest) GetDistributionProcessId() string {
	if m != nil && m.DistributionProcessId != nil {
		return *m.DistributionProcessId
	}
	return ""
}

func (m *DistributionRequest) GetDistributionId() string {
	if m != nil && m.DistributionId != nil {
		return *m.DistributionId
	}
	return ""
}

func (m *DistributionRequest) GetResourceId() uint32 {
	if m != nil && m.ResourceId != nil {
		return *m.ResourceId
	}
	return 0
}

func (m *DistributionRequest) GetBusinessData() string {
	if m != nil && m.BusinessData != nil {
		return *m.BusinessData
	}
	return ""
}

type DistributionResponse struct {
	RespHeader           *PisRespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	Data                 *string        `protobuf:"bytes,2,opt,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *DistributionResponse) Reset()         { *m = DistributionResponse{} }
func (m *DistributionResponse) String() string { return proto.CompactTextString(m) }
func (*DistributionResponse) ProtoMessage()    {}
func (*DistributionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2103a35ea7836b5b, []int{3}
}

func (m *DistributionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DistributionResponse.Unmarshal(m, b)
}
func (m *DistributionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DistributionResponse.Marshal(b, m, deterministic)
}
func (m *DistributionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DistributionResponse.Merge(m, src)
}
func (m *DistributionResponse) XXX_Size() int {
	return xxx_messageInfo_DistributionResponse.Size(m)
}
func (m *DistributionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DistributionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DistributionResponse proto.InternalMessageInfo

func (m *DistributionResponse) GetRespHeader() *PisRespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *DistributionResponse) GetData() string {
	if m != nil && m.Data != nil {
		return *m.Data
	}
	return ""
}

func init() {
	proto.RegisterType((*PisReqHeader)(nil), "lcos_protobuf.PisReqHeader")
	proto.RegisterType((*PisRespHeader)(nil), "lcos_protobuf.PisRespHeader")
	proto.RegisterType((*DistributionRequest)(nil), "lcos_protobuf.DistributionRequest")
	proto.RegisterType((*DistributionResponse)(nil), "lcos_protobuf.DistributionResponse")
}

func init() {
	proto.RegisterFile("lcos_pis.proto", fileDescriptor_2103a35ea7836b5b)
}

var fileDescriptor_2103a35ea7836b5b = []byte{
	// 332 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x91, 0xb1, 0x4e, 0xfb, 0x30,
	0x10, 0xc6, 0x95, 0xb4, 0xd5, 0xff, 0x9f, 0x6b, 0x53, 0x24, 0x03, 0x22, 0x52, 0x8b, 0xa8, 0xc2,
	0x40, 0xa7, 0x0e, 0x0c, 0x0c, 0x48, 0x6c, 0x1d, 0xc8, 0x56, 0xe5, 0x05, 0x2a, 0x27, 0x3e, 0xc0,
	0x52, 0x1b, 0xbb, 0x3e, 0xe7, 0x19, 0x78, 0x65, 0x46, 0x14, 0x27, 0x86, 0xa4, 0x62, 0xf3, 0x7d,
	0x9f, 0xee, 0xbe, 0xdf, 0x9d, 0x61, 0x7e, 0x28, 0x15, 0xed, 0xb5, 0xa4, 0x8d, 0x36, 0xca, 0x2a,
	0x16, 0xb7, 0x75, 0xf3, 0x2e, 0xea, 0xb7, 0xf4, 0x33, 0x80, 0xd9, 0x4e, 0x52, 0x8e, 0xa7, 0x57,
	0xe4, 0x02, 0x0d, 0xbb, 0x05, 0x30, 0x78, 0xaa, 0x91, 0xec, 0x5e, 0x8a, 0x24, 0x58, 0x85, 0xeb,
	0x28, 0x8f, 0x3a, 0x25, 0x13, 0x03, 0x5b, 0x27, 0xe1, 0xd0, 0xd6, 0x6c, 0x09, 0x91, 0x95, 0x47,
	0x24, 0xcb, 0x8f, 0x3a, 0x19, 0xad, 0xc2, 0x75, 0x9c, 0xff, 0x0a, 0x6c, 0x01, 0x51, 0x79, 0x90,
	0x58, 0xb9, 0xd1, 0x63, 0xd7, 0xfb, 0xbf, 0x15, 0x32, 0x91, 0x16, 0x10, 0x3b, 0x10, 0xd2, 0x1d,
	0x49, 0x02, 0xff, 0x0c, 0xda, 0x52, 0x09, 0x74, 0x18, 0x93, 0xdc, 0x97, 0x8d, 0x73, 0x44, 0x22,
	0xfe, 0x8e, 0x1d, 0x81, 0x2f, 0xcf, 0xe8, 0x47, 0x67, 0xf4, 0xe9, 0x57, 0x00, 0x97, 0x5b, 0x49,
	0xd6, 0xc8, 0xa2, 0xb6, 0x52, 0x55, 0x79, 0xeb, 0xb0, 0x67, 0xd7, 0xb6, 0xff, 0x70, 0xc1, 0x2e,
	0x6d, 0xfa, 0xb8, 0xd8, 0x0c, 0x2e, 0xb5, 0xe9, 0x5f, 0xc9, 0xcd, 0xec, 0x30, 0x9f, 0xe0, 0x46,
	0xf4, 0x46, 0x36, 0x0d, 0x25, 0x12, 0x35, 0xf9, 0x2d, 0xdc, 0x75, 0xdf, 0xde, 0xb5, 0x6e, 0x26,
	0xd8, 0x03, 0x5c, 0x0c, 0xfa, 0x7e, 0x78, 0xe7, 0x7d, 0x39, 0x13, 0xec, 0x0e, 0xa6, 0x06, 0x49,
	0xd5, 0xa6, 0x44, 0x7f, 0xb7, 0x38, 0x07, 0x2f, 0x65, 0x82, 0xdd, 0x43, 0x5c, 0xd4, 0x24, 0xab,
	0x26, 0x55, 0x70, 0xcb, 0x93, 0x89, 0x9b, 0x33, 0xf3, 0xe2, 0x96, 0x5b, 0x9e, 0x4a, 0xb8, 0x1a,
	0x6e, 0x4e, 0x5a, 0x55, 0x84, 0xec, 0xc5, 0x4d, 0xd7, 0xc3, 0xdd, 0x97, 0x7f, 0xed, 0xee, 0x3f,
	0xc6, 0x65, 0xfb, 0x4f, 0x62, 0x30, 0x76, 0x91, 0xe1, 0x2a, 0x58, 0x47, 0xb9, 0x7b, 0x7f, 0x07,
	0x00, 0x00, 0xff, 0xff, 0xd2, 0xbb, 0x81, 0x28, 0x73, 0x02, 0x00, 0x00,
}
