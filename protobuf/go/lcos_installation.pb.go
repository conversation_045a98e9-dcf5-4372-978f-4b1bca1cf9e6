// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_installation.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type BatchGetInstallationDataRequest struct {
	ReqHeader            *ReqHeader          `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	InstallationItems    []*InstallationItem `protobuf:"bytes,2,rep,name=installation_items,json=installationItems" json:"installation_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetInstallationDataRequest) Reset()         { *m = BatchGetInstallationDataRequest{} }
func (m *BatchGetInstallationDataRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetInstallationDataRequest) ProtoMessage()    {}
func (*BatchGetInstallationDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4b8ca7a24f39246c, []int{0}
}

func (m *BatchGetInstallationDataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetInstallationDataRequest.Unmarshal(m, b)
}
func (m *BatchGetInstallationDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetInstallationDataRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetInstallationDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetInstallationDataRequest.Merge(m, src)
}
func (m *BatchGetInstallationDataRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetInstallationDataRequest.Size(m)
}
func (m *BatchGetInstallationDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetInstallationDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetInstallationDataRequest proto.InternalMessageInfo

func (m *BatchGetInstallationDataRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetInstallationDataRequest) GetInstallationItems() []*InstallationItem {
	if m != nil {
		return m.InstallationItems
	}
	return nil
}

type InstallationDateResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	DateItems            []*DateItem `protobuf:"bytes,2,rep,name=date_items,json=dateItems" json:"date_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *InstallationDateResponse) Reset()         { *m = InstallationDateResponse{} }
func (m *InstallationDateResponse) String() string { return proto.CompactTextString(m) }
func (*InstallationDateResponse) ProtoMessage()    {}
func (*InstallationDateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4b8ca7a24f39246c, []int{1}
}

func (m *InstallationDateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InstallationDateResponse.Unmarshal(m, b)
}
func (m *InstallationDateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InstallationDateResponse.Marshal(b, m, deterministic)
}
func (m *InstallationDateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InstallationDateResponse.Merge(m, src)
}
func (m *InstallationDateResponse) XXX_Size() int {
	return xxx_messageInfo_InstallationDateResponse.Size(m)
}
func (m *InstallationDateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InstallationDateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InstallationDateResponse proto.InternalMessageInfo

func (m *InstallationDateResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *InstallationDateResponse) GetDateItems() []*DateItem {
	if m != nil {
		return m.DateItems
	}
	return nil
}

type InstallationItem struct {
	SlsTn                *string  `protobuf:"bytes,1,req,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	Scenario             *uint32  `protobuf:"varint,2,req,name=scenario" json:"scenario,omitempty"`
	Edd                  *uint32  `protobuf:"varint,3,opt,name=edd" json:"edd,omitempty"`
	Edt                  *uint32  `protobuf:"varint,4,opt,name=edt" json:"edt,omitempty"`
	LineId               *string  `protobuf:"bytes,5,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	ProductId            *int32   `protobuf:"varint,6,req,name=product_id,json=productId" json:"product_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InstallationItem) Reset()         { *m = InstallationItem{} }
func (m *InstallationItem) String() string { return proto.CompactTextString(m) }
func (*InstallationItem) ProtoMessage()    {}
func (*InstallationItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_4b8ca7a24f39246c, []int{2}
}

func (m *InstallationItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InstallationItem.Unmarshal(m, b)
}
func (m *InstallationItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InstallationItem.Marshal(b, m, deterministic)
}
func (m *InstallationItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InstallationItem.Merge(m, src)
}
func (m *InstallationItem) XXX_Size() int {
	return xxx_messageInfo_InstallationItem.Size(m)
}
func (m *InstallationItem) XXX_DiscardUnknown() {
	xxx_messageInfo_InstallationItem.DiscardUnknown(m)
}

var xxx_messageInfo_InstallationItem proto.InternalMessageInfo

func (m *InstallationItem) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

func (m *InstallationItem) GetScenario() uint32 {
	if m != nil && m.Scenario != nil {
		return *m.Scenario
	}
	return 0
}

func (m *InstallationItem) GetEdd() uint32 {
	if m != nil && m.Edd != nil {
		return *m.Edd
	}
	return 0
}

func (m *InstallationItem) GetEdt() uint32 {
	if m != nil && m.Edt != nil {
		return *m.Edt
	}
	return 0
}

func (m *InstallationItem) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *InstallationItem) GetProductId() int32 {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return 0
}

type DateItem struct {
	SlsTn                *string       `protobuf:"bytes,1,req,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	AvailableDays        []*InstallDay `protobuf:"bytes,2,rep,name=available_days,json=availableDays" json:"available_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DateItem) Reset()         { *m = DateItem{} }
func (m *DateItem) String() string { return proto.CompactTextString(m) }
func (*DateItem) ProtoMessage()    {}
func (*DateItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_4b8ca7a24f39246c, []int{3}
}

func (m *DateItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DateItem.Unmarshal(m, b)
}
func (m *DateItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DateItem.Marshal(b, m, deterministic)
}
func (m *DateItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DateItem.Merge(m, src)
}
func (m *DateItem) XXX_Size() int {
	return xxx_messageInfo_DateItem.Size(m)
}
func (m *DateItem) XXX_DiscardUnknown() {
	xxx_messageInfo_DateItem.DiscardUnknown(m)
}

var xxx_messageInfo_DateItem proto.InternalMessageInfo

func (m *DateItem) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

func (m *DateItem) GetAvailableDays() []*InstallDay {
	if m != nil {
		return m.AvailableDays
	}
	return nil
}

type InstallDay struct {
	TimestampDay         *uint32        `protobuf:"varint,1,req,name=timestamp_day,json=timestampDay" json:"timestamp_day,omitempty"`
	Timeslots            []*InstallSlot `protobuf:"bytes,2,rep,name=timeslots" json:"timeslots,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *InstallDay) Reset()         { *m = InstallDay{} }
func (m *InstallDay) String() string { return proto.CompactTextString(m) }
func (*InstallDay) ProtoMessage()    {}
func (*InstallDay) Descriptor() ([]byte, []int) {
	return fileDescriptor_4b8ca7a24f39246c, []int{4}
}

func (m *InstallDay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InstallDay.Unmarshal(m, b)
}
func (m *InstallDay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InstallDay.Marshal(b, m, deterministic)
}
func (m *InstallDay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InstallDay.Merge(m, src)
}
func (m *InstallDay) XXX_Size() int {
	return xxx_messageInfo_InstallDay.Size(m)
}
func (m *InstallDay) XXX_DiscardUnknown() {
	xxx_messageInfo_InstallDay.DiscardUnknown(m)
}

var xxx_messageInfo_InstallDay proto.InternalMessageInfo

func (m *InstallDay) GetTimestampDay() uint32 {
	if m != nil && m.TimestampDay != nil {
		return *m.TimestampDay
	}
	return 0
}

func (m *InstallDay) GetTimeslots() []*InstallSlot {
	if m != nil {
		return m.Timeslots
	}
	return nil
}

type InstallSlot struct {
	StartTime            *uint32  `protobuf:"varint,1,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime              *uint32  `protobuf:"varint,2,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InstallSlot) Reset()         { *m = InstallSlot{} }
func (m *InstallSlot) String() string { return proto.CompactTextString(m) }
func (*InstallSlot) ProtoMessage()    {}
func (*InstallSlot) Descriptor() ([]byte, []int) {
	return fileDescriptor_4b8ca7a24f39246c, []int{5}
}

func (m *InstallSlot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InstallSlot.Unmarshal(m, b)
}
func (m *InstallSlot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InstallSlot.Marshal(b, m, deterministic)
}
func (m *InstallSlot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InstallSlot.Merge(m, src)
}
func (m *InstallSlot) XXX_Size() int {
	return xxx_messageInfo_InstallSlot.Size(m)
}
func (m *InstallSlot) XXX_DiscardUnknown() {
	xxx_messageInfo_InstallSlot.DiscardUnknown(m)
}

var xxx_messageInfo_InstallSlot proto.InternalMessageInfo

func (m *InstallSlot) GetStartTime() uint32 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *InstallSlot) GetEndTime() uint32 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func init() {
	proto.RegisterType((*BatchGetInstallationDataRequest)(nil), "lcos_protobuf.BatchGetInstallationDataRequest")
	proto.RegisterType((*InstallationDateResponse)(nil), "lcos_protobuf.InstallationDateResponse")
	proto.RegisterType((*InstallationItem)(nil), "lcos_protobuf.InstallationItem")
	proto.RegisterType((*DateItem)(nil), "lcos_protobuf.DateItem")
	proto.RegisterType((*InstallDay)(nil), "lcos_protobuf.InstallDay")
	proto.RegisterType((*InstallSlot)(nil), "lcos_protobuf.InstallSlot")
}

func init() {
	proto.RegisterFile("lcos_installation.proto", fileDescriptor_4b8ca7a24f39246c)
}

var fileDescriptor_4b8ca7a24f39246c = []byte{
	// 470 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x93, 0xcf, 0x6e, 0xd3, 0x40,
	0x10, 0xc6, 0xb1, 0x43, 0xd2, 0x78, 0x82, 0xa1, 0xac, 0x84, 0xe2, 0x44, 0x42, 0x8d, 0xcc, 0x81,
	0x9c, 0x72, 0xc8, 0x01, 0x10, 0x27, 0x84, 0x22, 0x95, 0x48, 0x88, 0xc3, 0xb6, 0x77, 0x6b, 0xe3,
	0x1d, 0xd4, 0x15, 0x8e, 0xed, 0xec, 0x4c, 0x2a, 0xe5, 0x25, 0x10, 0xaf, 0xc0, 0x95, 0xa7, 0x44,
	0xde, 0xc4, 0xa9, 0x63, 0x5a, 0x7a, 0x9b, 0xf9, 0xbe, 0xf9, 0xf3, 0xb3, 0x77, 0x17, 0x86, 0x59,
	0x5a, 0x50, 0x62, 0x72, 0x62, 0x95, 0x65, 0x8a, 0x4d, 0x91, 0xcf, 0x4a, 0x5b, 0x70, 0x21, 0x42,
	0x67, 0xb8, 0x78, 0xb5, 0xfd, 0x3e, 0x7e, 0xe1, 0xd2, 0x95, 0x22, 0xdc, 0xfb, 0xf1, 0x1f, 0x0f,
	0x2e, 0x3e, 0x2b, 0x4e, 0x6f, 0x2e, 0x91, 0x97, 0x8d, 0xf6, 0x85, 0x62, 0x25, 0x71, 0xb3, 0x45,
	0x62, 0xf1, 0x1e, 0xc0, 0xe2, 0x26, 0xb9, 0x41, 0xa5, 0xd1, 0x46, 0xde, 0xc4, 0x9f, 0x0e, 0xe6,
	0xd1, 0xec, 0x64, 0xf0, 0x4c, 0xe2, 0xe6, 0x8b, 0xf3, 0x65, 0x60, 0xeb, 0x50, 0x7c, 0x03, 0xd1,
	0x44, 0x4a, 0x0c, 0xe3, 0x9a, 0x22, 0x7f, 0xd2, 0x99, 0x0e, 0xe6, 0x17, 0xad, 0x01, 0xcd, 0xe5,
	0x4b, 0xc6, 0xb5, 0x7c, 0x69, 0x5a, 0x0a, 0xc5, 0x3f, 0x3d, 0x88, 0x5a, 0x90, 0x28, 0x91, 0xca,
	0x22, 0x27, 0x14, 0x1f, 0x61, 0x60, 0x91, 0xca, 0x53, 0xcc, 0xd1, 0x3f, 0x98, 0x54, 0x1e, 0x38,
	0xc1, 0x1e, 0x63, 0xf1, 0x0e, 0x40, 0x2b, 0xc6, 0x13, 0xc0, 0x61, 0xab, 0xb5, 0x5a, 0xe6, 0xc0,
	0x02, 0x7d, 0x88, 0x28, 0xfe, 0xed, 0xc1, 0x79, 0x1b, 0x5c, 0xbc, 0x82, 0x1e, 0x65, 0x94, 0x70,
	0xee, 0x18, 0x02, 0xd9, 0xa5, 0x8c, 0xae, 0x73, 0x31, 0x86, 0x3e, 0xa5, 0x98, 0x2b, 0x6b, 0x8a,
	0xc8, 0x9f, 0xf8, 0xd3, 0x50, 0x1e, 0x73, 0x71, 0x0e, 0x1d, 0xd4, 0x3a, 0xea, 0x4c, 0xbc, 0x69,
	0x28, 0xab, 0x70, 0xaf, 0x70, 0xf4, 0xb4, 0x56, 0x58, 0x0c, 0xe1, 0x2c, 0x33, 0x39, 0x26, 0x46,
	0x47, 0x5d, 0x37, 0xb7, 0x57, 0xa5, 0x4b, 0x2d, 0x5e, 0x03, 0x94, 0xb6, 0xd0, 0xdb, 0x94, 0x2b,
	0xaf, 0x37, 0xf1, 0xa7, 0x5d, 0x19, 0x1c, 0x94, 0xa5, 0x8e, 0x53, 0xe8, 0xd7, 0xe8, 0x0f, 0xa1,
	0x7d, 0x82, 0xe7, 0xea, 0x56, 0x99, 0x4c, 0xad, 0x32, 0x4c, 0xb4, 0xda, 0xd5, 0xbf, 0x60, 0x74,
	0xff, 0x19, 0x2d, 0xd4, 0x4e, 0x86, 0xc7, 0x86, 0x85, 0xda, 0x51, 0xfc, 0x03, 0xe0, 0xce, 0x14,
	0x6f, 0x20, 0x64, 0xb3, 0x46, 0x62, 0xb5, 0x2e, 0xab, 0x79, 0x6e, 0x5b, 0x28, 0x9f, 0x1d, 0xc5,
	0xaa, 0xe8, 0x03, 0x04, 0x2e, 0xcf, 0x0a, 0xae, 0xf7, 0x8d, 0xef, 0xdf, 0x77, 0x95, 0x15, 0x2c,
	0xef, 0x8a, 0xe3, 0x4b, 0x18, 0x34, 0x9c, 0xea, 0xfb, 0x89, 0x95, 0xe5, 0xa4, 0xaa, 0x88, 0x3c,
	0xf7, 0xc7, 0x02, 0xa7, 0x5c, 0x9b, 0x35, 0x8a, 0x11, 0xf4, 0x31, 0xd7, 0x7b, 0xd3, 0x77, 0xe6,
	0x19, 0xe6, 0xba, 0xb2, 0xe6, 0xbf, 0x3c, 0x18, 0x7e, 0x4d, 0x0b, 0x6a, 0x1e, 0xe1, 0x15, 0xda,
	0x5b, 0x93, 0xa2, 0xd8, 0x42, 0xf4, 0xc0, 0xbb, 0x40, 0x31, 0x6b, 0x71, 0x3e, 0xf2, 0x80, 0xc6,
	0x6f, 0xff, 0x73, 0xd7, 0x9b, 0x77, 0x38, 0x7e, 0xf2, 0x37, 0x00, 0x00, 0xff, 0xff, 0x55, 0xbb,
	0x51, 0xcf, 0xc9, 0x03, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosInstallationServiceClient is the client API for LcosInstallationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosInstallationServiceClient interface {
	BatchGetInstallationDate(ctx context.Context, in *BatchGetInstallationDataRequest, opts ...grpc.CallOption) (*InstallationDateResponse, error)
}

type lcosInstallationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosInstallationServiceClient(cc grpc.ClientConnInterface) LcosInstallationServiceClient {
	return &lcosInstallationServiceClient{cc}
}

func (c *lcosInstallationServiceClient) BatchGetInstallationDate(ctx context.Context, in *BatchGetInstallationDataRequest, opts ...grpc.CallOption) (*InstallationDateResponse, error) {
	out := new(InstallationDateResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosInstallationService/BatchGetInstallationDate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosInstallationServiceServer is the server API for LcosInstallationService service.
type LcosInstallationServiceServer interface {
	BatchGetInstallationDate(context.Context, *BatchGetInstallationDataRequest) (*InstallationDateResponse, error)
}

// UnimplementedLcosInstallationServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLcosInstallationServiceServer struct {
}

func (*UnimplementedLcosInstallationServiceServer) BatchGetInstallationDate(ctx context.Context, req *BatchGetInstallationDataRequest) (*InstallationDateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetInstallationDate not implemented")
}

func RegisterLcosInstallationServiceServer(s *grpc.Server, srv LcosInstallationServiceServer) {
	s.RegisterService(&_LcosInstallationService_serviceDesc, srv)
}

func _LcosInstallationService_BatchGetInstallationDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetInstallationDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosInstallationServiceServer).BatchGetInstallationDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosInstallationService/BatchGetInstallationDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosInstallationServiceServer).BatchGetInstallationDate(ctx, req.(*BatchGetInstallationDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosInstallationService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosInstallationService",
	HandlerType: (*LcosInstallationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetInstallationDate",
			Handler:    _LcosInstallationService_BatchGetInstallationDate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_installation.proto",
}
