// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_delivery_instruction.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// --- 获取ProductID维度可用的delivery_instruction ---
type BatchGetDeliveryInstructionByProductIDRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductId            []string   `protobuf:"bytes,2,rep,name=product_id,json=productId" json:"product_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetDeliveryInstructionByProductIDRequest) Reset() {
	*m = BatchGetDeliveryInstructionByProductIDRequest{}
}
func (m *BatchGetDeliveryInstructionByProductIDRequest) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetDeliveryInstructionByProductIDRequest) ProtoMessage() {}
func (*BatchGetDeliveryInstructionByProductIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{0}
}

func (m *BatchGetDeliveryInstructionByProductIDRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetDeliveryInstructionByProductIDRequest.Unmarshal(m, b)
}
func (m *BatchGetDeliveryInstructionByProductIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetDeliveryInstructionByProductIDRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetDeliveryInstructionByProductIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetDeliveryInstructionByProductIDRequest.Merge(m, src)
}
func (m *BatchGetDeliveryInstructionByProductIDRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetDeliveryInstructionByProductIDRequest.Size(m)
}
func (m *BatchGetDeliveryInstructionByProductIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetDeliveryInstructionByProductIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetDeliveryInstructionByProductIDRequest proto.InternalMessageInfo

func (m *BatchGetDeliveryInstructionByProductIDRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetDeliveryInstructionByProductIDRequest) GetProductId() []string {
	if m != nil {
		return m.ProductId
	}
	return nil
}

type DeliveryInstructionByProductIDResponse struct {
	RespHeader                  *RespHeader                    `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	DeliveryInstructionRespList []*DeliveryInstructionRespList `protobuf:"bytes,2,rep,name=delivery_instruction_resp_list,json=deliveryInstructionRespList" json:"delivery_instruction_resp_list,omitempty"`
	XXX_NoUnkeyedLiteral        struct{}                       `json:"-"`
	XXX_unrecognized            []byte                         `json:"-"`
	XXX_sizecache               int32                          `json:"-"`
}

func (m *DeliveryInstructionByProductIDResponse) Reset() {
	*m = DeliveryInstructionByProductIDResponse{}
}
func (m *DeliveryInstructionByProductIDResponse) String() string { return proto.CompactTextString(m) }
func (*DeliveryInstructionByProductIDResponse) ProtoMessage()    {}
func (*DeliveryInstructionByProductIDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{1}
}

func (m *DeliveryInstructionByProductIDResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryInstructionByProductIDResponse.Unmarshal(m, b)
}
func (m *DeliveryInstructionByProductIDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryInstructionByProductIDResponse.Marshal(b, m, deterministic)
}
func (m *DeliveryInstructionByProductIDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryInstructionByProductIDResponse.Merge(m, src)
}
func (m *DeliveryInstructionByProductIDResponse) XXX_Size() int {
	return xxx_messageInfo_DeliveryInstructionByProductIDResponse.Size(m)
}
func (m *DeliveryInstructionByProductIDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryInstructionByProductIDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryInstructionByProductIDResponse proto.InternalMessageInfo

func (m *DeliveryInstructionByProductIDResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *DeliveryInstructionByProductIDResponse) GetDeliveryInstructionRespList() []*DeliveryInstructionRespList {
	if m != nil {
		return m.DeliveryInstructionRespList
	}
	return nil
}

// --- 获取Region维度可用的delivery_instruction 分页---
type ListDeliveryInstructionByRegionRequest struct {
	ReqHeader              *ReqHeader                   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	DeliveryInstructionReq *DeliveryInstructioReqByPage `protobuf:"bytes,2,req,name=delivery_instruction_req,json=deliveryInstructionReq" json:"delivery_instruction_req,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                     `json:"-"`
	XXX_unrecognized       []byte                       `json:"-"`
	XXX_sizecache          int32                        `json:"-"`
}

func (m *ListDeliveryInstructionByRegionRequest) Reset() {
	*m = ListDeliveryInstructionByRegionRequest{}
}
func (m *ListDeliveryInstructionByRegionRequest) String() string { return proto.CompactTextString(m) }
func (*ListDeliveryInstructionByRegionRequest) ProtoMessage()    {}
func (*ListDeliveryInstructionByRegionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{2}
}

func (m *ListDeliveryInstructionByRegionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListDeliveryInstructionByRegionRequest.Unmarshal(m, b)
}
func (m *ListDeliveryInstructionByRegionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListDeliveryInstructionByRegionRequest.Marshal(b, m, deterministic)
}
func (m *ListDeliveryInstructionByRegionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListDeliveryInstructionByRegionRequest.Merge(m, src)
}
func (m *ListDeliveryInstructionByRegionRequest) XXX_Size() int {
	return xxx_messageInfo_ListDeliveryInstructionByRegionRequest.Size(m)
}
func (m *ListDeliveryInstructionByRegionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListDeliveryInstructionByRegionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListDeliveryInstructionByRegionRequest proto.InternalMessageInfo

func (m *ListDeliveryInstructionByRegionRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *ListDeliveryInstructionByRegionRequest) GetDeliveryInstructionReq() *DeliveryInstructioReqByPage {
	if m != nil {
		return m.DeliveryInstructionReq
	}
	return nil
}

type DeliveryInstructioReqByPage struct {
	Pageno               *uint32  `protobuf:"varint,1,req,name=pageno" json:"pageno,omitempty"`
	Count                *uint32  `protobuf:"varint,2,req,name=count" json:"count,omitempty"`
	Region               *string  `protobuf:"bytes,3,req,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeliveryInstructioReqByPage) Reset()         { *m = DeliveryInstructioReqByPage{} }
func (m *DeliveryInstructioReqByPage) String() string { return proto.CompactTextString(m) }
func (*DeliveryInstructioReqByPage) ProtoMessage()    {}
func (*DeliveryInstructioReqByPage) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{3}
}

func (m *DeliveryInstructioReqByPage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryInstructioReqByPage.Unmarshal(m, b)
}
func (m *DeliveryInstructioReqByPage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryInstructioReqByPage.Marshal(b, m, deterministic)
}
func (m *DeliveryInstructioReqByPage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryInstructioReqByPage.Merge(m, src)
}
func (m *DeliveryInstructioReqByPage) XXX_Size() int {
	return xxx_messageInfo_DeliveryInstructioReqByPage.Size(m)
}
func (m *DeliveryInstructioReqByPage) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryInstructioReqByPage.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryInstructioReqByPage proto.InternalMessageInfo

func (m *DeliveryInstructioReqByPage) GetPageno() uint32 {
	if m != nil && m.Pageno != nil {
		return *m.Pageno
	}
	return 0
}

func (m *DeliveryInstructioReqByPage) GetCount() uint32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *DeliveryInstructioReqByPage) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type DeliveryInstructionByRegionResponse struct {
	RespHeader              *RespHeader              `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	DeliveryInstructionResp *DeliveryInstructionResp `protobuf:"bytes,2,req,name=delivery_instruction_resp,json=deliveryInstructionResp" json:"delivery_instruction_resp,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                 `json:"-"`
	XXX_unrecognized        []byte                   `json:"-"`
	XXX_sizecache           int32                    `json:"-"`
}

func (m *DeliveryInstructionByRegionResponse) Reset()         { *m = DeliveryInstructionByRegionResponse{} }
func (m *DeliveryInstructionByRegionResponse) String() string { return proto.CompactTextString(m) }
func (*DeliveryInstructionByRegionResponse) ProtoMessage()    {}
func (*DeliveryInstructionByRegionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{4}
}

func (m *DeliveryInstructionByRegionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryInstructionByRegionResponse.Unmarshal(m, b)
}
func (m *DeliveryInstructionByRegionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryInstructionByRegionResponse.Marshal(b, m, deterministic)
}
func (m *DeliveryInstructionByRegionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryInstructionByRegionResponse.Merge(m, src)
}
func (m *DeliveryInstructionByRegionResponse) XXX_Size() int {
	return xxx_messageInfo_DeliveryInstructionByRegionResponse.Size(m)
}
func (m *DeliveryInstructionByRegionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryInstructionByRegionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryInstructionByRegionResponse proto.InternalMessageInfo

func (m *DeliveryInstructionByRegionResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *DeliveryInstructionByRegionResponse) GetDeliveryInstructionResp() *DeliveryInstructionResp {
	if m != nil {
		return m.DeliveryInstructionResp
	}
	return nil
}

type DeliveryInstructionResp struct {
	PageNo                  *uint32                        `protobuf:"varint,1,req,name=page_no,json=pageNo" json:"page_no,omitempty"`
	Count                   *uint32                        `protobuf:"varint,2,req,name=count" json:"count,omitempty"`
	Total                   *uint32                        `protobuf:"varint,3,req,name=total" json:"total,omitempty"`
	DeliveryInstructionList []*DeliveryInstructionRespList `protobuf:"bytes,4,rep,name=delivery_instruction_list,json=deliveryInstructionList" json:"delivery_instruction_list,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                       `json:"-"`
	XXX_unrecognized        []byte                         `json:"-"`
	XXX_sizecache           int32                          `json:"-"`
}

func (m *DeliveryInstructionResp) Reset()         { *m = DeliveryInstructionResp{} }
func (m *DeliveryInstructionResp) String() string { return proto.CompactTextString(m) }
func (*DeliveryInstructionResp) ProtoMessage()    {}
func (*DeliveryInstructionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{5}
}

func (m *DeliveryInstructionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryInstructionResp.Unmarshal(m, b)
}
func (m *DeliveryInstructionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryInstructionResp.Marshal(b, m, deterministic)
}
func (m *DeliveryInstructionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryInstructionResp.Merge(m, src)
}
func (m *DeliveryInstructionResp) XXX_Size() int {
	return xxx_messageInfo_DeliveryInstructionResp.Size(m)
}
func (m *DeliveryInstructionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryInstructionResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryInstructionResp proto.InternalMessageInfo

func (m *DeliveryInstructionResp) GetPageNo() uint32 {
	if m != nil && m.PageNo != nil {
		return *m.PageNo
	}
	return 0
}

func (m *DeliveryInstructionResp) GetCount() uint32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *DeliveryInstructionResp) GetTotal() uint32 {
	if m != nil && m.Total != nil {
		return *m.Total
	}
	return 0
}

func (m *DeliveryInstructionResp) GetDeliveryInstructionList() []*DeliveryInstructionRespList {
	if m != nil {
		return m.DeliveryInstructionList
	}
	return nil
}

type DeliveryInstructionRespList struct {
	ProductId                    *string                         `protobuf:"bytes,1,req,name=product_id,json=productId" json:"product_id,omitempty"`
	AvailableDeliveryInstruction []*AvailableDeliveryInstruction `protobuf:"bytes,2,rep,name=available_delivery_instruction,json=availableDeliveryInstruction" json:"available_delivery_instruction,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}                        `json:"-"`
	XXX_unrecognized             []byte                          `json:"-"`
	XXX_sizecache                int32                           `json:"-"`
}

func (m *DeliveryInstructionRespList) Reset()         { *m = DeliveryInstructionRespList{} }
func (m *DeliveryInstructionRespList) String() string { return proto.CompactTextString(m) }
func (*DeliveryInstructionRespList) ProtoMessage()    {}
func (*DeliveryInstructionRespList) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{6}
}

func (m *DeliveryInstructionRespList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryInstructionRespList.Unmarshal(m, b)
}
func (m *DeliveryInstructionRespList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryInstructionRespList.Marshal(b, m, deterministic)
}
func (m *DeliveryInstructionRespList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryInstructionRespList.Merge(m, src)
}
func (m *DeliveryInstructionRespList) XXX_Size() int {
	return xxx_messageInfo_DeliveryInstructionRespList.Size(m)
}
func (m *DeliveryInstructionRespList) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryInstructionRespList.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryInstructionRespList proto.InternalMessageInfo

func (m *DeliveryInstructionRespList) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *DeliveryInstructionRespList) GetAvailableDeliveryInstruction() []*AvailableDeliveryInstruction {
	if m != nil {
		return m.AvailableDeliveryInstruction
	}
	return nil
}

type AvailableDeliveryInstruction struct {
	Category                *uint32  `protobuf:"varint,1,req,name=category" json:"category,omitempty"`
	DeliveryInstructionInfo []uint32 `protobuf:"varint,2,rep,name=delivery_instruction_info,json=deliveryInstructionInfo" json:"delivery_instruction_info,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *AvailableDeliveryInstruction) Reset()         { *m = AvailableDeliveryInstruction{} }
func (m *AvailableDeliveryInstruction) String() string { return proto.CompactTextString(m) }
func (*AvailableDeliveryInstruction) ProtoMessage()    {}
func (*AvailableDeliveryInstruction) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{7}
}

func (m *AvailableDeliveryInstruction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AvailableDeliveryInstruction.Unmarshal(m, b)
}
func (m *AvailableDeliveryInstruction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AvailableDeliveryInstruction.Marshal(b, m, deterministic)
}
func (m *AvailableDeliveryInstruction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AvailableDeliveryInstruction.Merge(m, src)
}
func (m *AvailableDeliveryInstruction) XXX_Size() int {
	return xxx_messageInfo_AvailableDeliveryInstruction.Size(m)
}
func (m *AvailableDeliveryInstruction) XXX_DiscardUnknown() {
	xxx_messageInfo_AvailableDeliveryInstruction.DiscardUnknown(m)
}

var xxx_messageInfo_AvailableDeliveryInstruction proto.InternalMessageInfo

func (m *AvailableDeliveryInstruction) GetCategory() uint32 {
	if m != nil && m.Category != nil {
		return *m.Category
	}
	return 0
}

func (m *AvailableDeliveryInstruction) GetDeliveryInstructionInfo() []uint32 {
	if m != nil {
		return m.DeliveryInstructionInfo
	}
	return nil
}

// --- 获取SlsTn维度可用的delivery_instruction ---
type ListDeliveryInstructionBySlsTnRequest struct {
	ReqHeader                         *ReqHeader                       `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	DeliveryInstructionBySlstnReqList []*DeliveryInstructionReqBySlsTn `protobuf:"bytes,2,rep,name=delivery_instruction_by_slstn_req_list,json=deliveryInstructionBySlstnReqList" json:"delivery_instruction_by_slstn_req_list,omitempty"`
	XXX_NoUnkeyedLiteral              struct{}                         `json:"-"`
	XXX_unrecognized                  []byte                           `json:"-"`
	XXX_sizecache                     int32                            `json:"-"`
}

func (m *ListDeliveryInstructionBySlsTnRequest) Reset()         { *m = ListDeliveryInstructionBySlsTnRequest{} }
func (m *ListDeliveryInstructionBySlsTnRequest) String() string { return proto.CompactTextString(m) }
func (*ListDeliveryInstructionBySlsTnRequest) ProtoMessage()    {}
func (*ListDeliveryInstructionBySlsTnRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{8}
}

func (m *ListDeliveryInstructionBySlsTnRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListDeliveryInstructionBySlsTnRequest.Unmarshal(m, b)
}
func (m *ListDeliveryInstructionBySlsTnRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListDeliveryInstructionBySlsTnRequest.Marshal(b, m, deterministic)
}
func (m *ListDeliveryInstructionBySlsTnRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListDeliveryInstructionBySlsTnRequest.Merge(m, src)
}
func (m *ListDeliveryInstructionBySlsTnRequest) XXX_Size() int {
	return xxx_messageInfo_ListDeliveryInstructionBySlsTnRequest.Size(m)
}
func (m *ListDeliveryInstructionBySlsTnRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListDeliveryInstructionBySlsTnRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListDeliveryInstructionBySlsTnRequest proto.InternalMessageInfo

func (m *ListDeliveryInstructionBySlsTnRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *ListDeliveryInstructionBySlsTnRequest) GetDeliveryInstructionBySlstnReqList() []*DeliveryInstructionReqBySlsTn {
	if m != nil {
		return m.DeliveryInstructionBySlstnReqList
	}
	return nil
}

type DeliveryInstructionReqBySlsTn struct {
	SlsTn                *string       `protobuf:"bytes,1,req,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	Country              *string       `protobuf:"bytes,2,req,name=country" json:"country,omitempty"`
	LineId               *string       `protobuf:"bytes,3,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	OtpFlag              *uint32       `protobuf:"varint,4,req,name=otp_flag,json=otpFlag" json:"otp_flag,omitempty"`
	PaymentMethod        *uint32       `protobuf:"varint,5,req,name=payment_method,json=paymentMethod" json:"payment_method,omitempty"`
	BuyerAddress         *BuyerAddress `protobuf:"bytes,6,req,name=buyer_address,json=buyerAddress" json:"buyer_address,omitempty"`
	SloStatus            *uint32       `protobuf:"varint,7,req,name=slo_status,json=sloStatus" json:"slo_status,omitempty"`
	Edd                  *uint32       `protobuf:"varint,8,opt,name=edd" json:"edd,omitempty"`
	Fcodes               []string      `protobuf:"bytes,9,rep,name=fcodes" json:"fcodes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DeliveryInstructionReqBySlsTn) Reset()         { *m = DeliveryInstructionReqBySlsTn{} }
func (m *DeliveryInstructionReqBySlsTn) String() string { return proto.CompactTextString(m) }
func (*DeliveryInstructionReqBySlsTn) ProtoMessage()    {}
func (*DeliveryInstructionReqBySlsTn) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{9}
}

func (m *DeliveryInstructionReqBySlsTn) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryInstructionReqBySlsTn.Unmarshal(m, b)
}
func (m *DeliveryInstructionReqBySlsTn) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryInstructionReqBySlsTn.Marshal(b, m, deterministic)
}
func (m *DeliveryInstructionReqBySlsTn) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryInstructionReqBySlsTn.Merge(m, src)
}
func (m *DeliveryInstructionReqBySlsTn) XXX_Size() int {
	return xxx_messageInfo_DeliveryInstructionReqBySlsTn.Size(m)
}
func (m *DeliveryInstructionReqBySlsTn) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryInstructionReqBySlsTn.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryInstructionReqBySlsTn proto.InternalMessageInfo

func (m *DeliveryInstructionReqBySlsTn) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

func (m *DeliveryInstructionReqBySlsTn) GetCountry() string {
	if m != nil && m.Country != nil {
		return *m.Country
	}
	return ""
}

func (m *DeliveryInstructionReqBySlsTn) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *DeliveryInstructionReqBySlsTn) GetOtpFlag() uint32 {
	if m != nil && m.OtpFlag != nil {
		return *m.OtpFlag
	}
	return 0
}

func (m *DeliveryInstructionReqBySlsTn) GetPaymentMethod() uint32 {
	if m != nil && m.PaymentMethod != nil {
		return *m.PaymentMethod
	}
	return 0
}

func (m *DeliveryInstructionReqBySlsTn) GetBuyerAddress() *BuyerAddress {
	if m != nil {
		return m.BuyerAddress
	}
	return nil
}

func (m *DeliveryInstructionReqBySlsTn) GetSloStatus() uint32 {
	if m != nil && m.SloStatus != nil {
		return *m.SloStatus
	}
	return 0
}

func (m *DeliveryInstructionReqBySlsTn) GetEdd() uint32 {
	if m != nil && m.Edd != nil {
		return *m.Edd
	}
	return 0
}

func (m *DeliveryInstructionReqBySlsTn) GetFcodes() []string {
	if m != nil {
		return m.Fcodes
	}
	return nil
}

type BuyerAddress struct {
	DeliverDistrictId    *uint32  `protobuf:"varint,1,opt,name=deliver_district_id,json=deliverDistrictId" json:"deliver_district_id,omitempty"`
	DeliverPostalCode    *string  `protobuf:"bytes,2,opt,name=deliver_postal_code,json=deliverPostalCode" json:"deliver_postal_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyerAddress) Reset()         { *m = BuyerAddress{} }
func (m *BuyerAddress) String() string { return proto.CompactTextString(m) }
func (*BuyerAddress) ProtoMessage()    {}
func (*BuyerAddress) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{10}
}

func (m *BuyerAddress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyerAddress.Unmarshal(m, b)
}
func (m *BuyerAddress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyerAddress.Marshal(b, m, deterministic)
}
func (m *BuyerAddress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyerAddress.Merge(m, src)
}
func (m *BuyerAddress) XXX_Size() int {
	return xxx_messageInfo_BuyerAddress.Size(m)
}
func (m *BuyerAddress) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyerAddress.DiscardUnknown(m)
}

var xxx_messageInfo_BuyerAddress proto.InternalMessageInfo

func (m *BuyerAddress) GetDeliverDistrictId() uint32 {
	if m != nil && m.DeliverDistrictId != nil {
		return *m.DeliverDistrictId
	}
	return 0
}

func (m *BuyerAddress) GetDeliverPostalCode() string {
	if m != nil && m.DeliverPostalCode != nil {
		return *m.DeliverPostalCode
	}
	return ""
}

type DeliveryInstructionBySlsTnResponse struct {
	RespHeader                         *RespHeader                             `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	DeliveryInstructionBySlstnRespList []*SingleDeliveryInstructionBySlsTnResp `protobuf:"bytes,2,rep,name=delivery_instruction_by_slstn_resp_list,json=deliveryInstructionBySlstnRespList" json:"delivery_instruction_by_slstn_resp_list,omitempty"`
	XXX_NoUnkeyedLiteral               struct{}                                `json:"-"`
	XXX_unrecognized                   []byte                                  `json:"-"`
	XXX_sizecache                      int32                                   `json:"-"`
}

func (m *DeliveryInstructionBySlsTnResponse) Reset()         { *m = DeliveryInstructionBySlsTnResponse{} }
func (m *DeliveryInstructionBySlsTnResponse) String() string { return proto.CompactTextString(m) }
func (*DeliveryInstructionBySlsTnResponse) ProtoMessage()    {}
func (*DeliveryInstructionBySlsTnResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{11}
}

func (m *DeliveryInstructionBySlsTnResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliveryInstructionBySlsTnResponse.Unmarshal(m, b)
}
func (m *DeliveryInstructionBySlsTnResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliveryInstructionBySlsTnResponse.Marshal(b, m, deterministic)
}
func (m *DeliveryInstructionBySlsTnResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliveryInstructionBySlsTnResponse.Merge(m, src)
}
func (m *DeliveryInstructionBySlsTnResponse) XXX_Size() int {
	return xxx_messageInfo_DeliveryInstructionBySlsTnResponse.Size(m)
}
func (m *DeliveryInstructionBySlsTnResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliveryInstructionBySlsTnResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeliveryInstructionBySlsTnResponse proto.InternalMessageInfo

func (m *DeliveryInstructionBySlsTnResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *DeliveryInstructionBySlsTnResponse) GetDeliveryInstructionBySlstnRespList() []*SingleDeliveryInstructionBySlsTnResp {
	if m != nil {
		return m.DeliveryInstructionBySlstnRespList
	}
	return nil
}

type SingleDeliveryInstructionBySlsTnResp struct {
	SlsTn                         *string                        `protobuf:"bytes,1,req,name=sls_tn,json=slsTn" json:"sls_tn,omitempty"`
	LineId                        *string                        `protobuf:"bytes,2,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	AvailableDeliveryInstructions *AvailableDeliveryInstructions `protobuf:"bytes,3,opt,name=available_delivery_instructions,json=availableDeliveryInstructions" json:"available_delivery_instructions,omitempty"`
	Retcode                       *uint32                        `protobuf:"varint,4,req,name=retcode" json:"retcode,omitempty"`
	Message                       *string                        `protobuf:"bytes,5,req,name=message" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral          struct{}                       `json:"-"`
	XXX_unrecognized              []byte                         `json:"-"`
	XXX_sizecache                 int32                          `json:"-"`
}

func (m *SingleDeliveryInstructionBySlsTnResp) Reset()         { *m = SingleDeliveryInstructionBySlsTnResp{} }
func (m *SingleDeliveryInstructionBySlsTnResp) String() string { return proto.CompactTextString(m) }
func (*SingleDeliveryInstructionBySlsTnResp) ProtoMessage()    {}
func (*SingleDeliveryInstructionBySlsTnResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{12}
}

func (m *SingleDeliveryInstructionBySlsTnResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleDeliveryInstructionBySlsTnResp.Unmarshal(m, b)
}
func (m *SingleDeliveryInstructionBySlsTnResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleDeliveryInstructionBySlsTnResp.Marshal(b, m, deterministic)
}
func (m *SingleDeliveryInstructionBySlsTnResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleDeliveryInstructionBySlsTnResp.Merge(m, src)
}
func (m *SingleDeliveryInstructionBySlsTnResp) XXX_Size() int {
	return xxx_messageInfo_SingleDeliveryInstructionBySlsTnResp.Size(m)
}
func (m *SingleDeliveryInstructionBySlsTnResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleDeliveryInstructionBySlsTnResp.DiscardUnknown(m)
}

var xxx_messageInfo_SingleDeliveryInstructionBySlsTnResp proto.InternalMessageInfo

func (m *SingleDeliveryInstructionBySlsTnResp) GetSlsTn() string {
	if m != nil && m.SlsTn != nil {
		return *m.SlsTn
	}
	return ""
}

func (m *SingleDeliveryInstructionBySlsTnResp) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *SingleDeliveryInstructionBySlsTnResp) GetAvailableDeliveryInstructions() *AvailableDeliveryInstructions {
	if m != nil {
		return m.AvailableDeliveryInstructions
	}
	return nil
}

func (m *SingleDeliveryInstructionBySlsTnResp) GetRetcode() uint32 {
	if m != nil && m.Retcode != nil {
		return *m.Retcode
	}
	return 0
}

func (m *SingleDeliveryInstructionBySlsTnResp) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

type AvailableDeliveryInstructions struct {
	DeliveryInstructions []uint32 `protobuf:"varint,1,rep,name=delivery_instructions,json=deliveryInstructions" json:"delivery_instructions,omitempty"`
	LogisticSupport      []uint32 `protobuf:"varint,2,rep,name=logistic_support,json=logisticSupport" json:"logistic_support,omitempty"`
	ContactMethod        []uint32 `protobuf:"varint,3,rep,name=contact_method,json=contactMethod" json:"contact_method,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AvailableDeliveryInstructions) Reset()         { *m = AvailableDeliveryInstructions{} }
func (m *AvailableDeliveryInstructions) String() string { return proto.CompactTextString(m) }
func (*AvailableDeliveryInstructions) ProtoMessage()    {}
func (*AvailableDeliveryInstructions) Descriptor() ([]byte, []int) {
	return fileDescriptor_b882d1bd5146d966, []int{13}
}

func (m *AvailableDeliveryInstructions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AvailableDeliveryInstructions.Unmarshal(m, b)
}
func (m *AvailableDeliveryInstructions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AvailableDeliveryInstructions.Marshal(b, m, deterministic)
}
func (m *AvailableDeliveryInstructions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AvailableDeliveryInstructions.Merge(m, src)
}
func (m *AvailableDeliveryInstructions) XXX_Size() int {
	return xxx_messageInfo_AvailableDeliveryInstructions.Size(m)
}
func (m *AvailableDeliveryInstructions) XXX_DiscardUnknown() {
	xxx_messageInfo_AvailableDeliveryInstructions.DiscardUnknown(m)
}

var xxx_messageInfo_AvailableDeliveryInstructions proto.InternalMessageInfo

func (m *AvailableDeliveryInstructions) GetDeliveryInstructions() []uint32 {
	if m != nil {
		return m.DeliveryInstructions
	}
	return nil
}

func (m *AvailableDeliveryInstructions) GetLogisticSupport() []uint32 {
	if m != nil {
		return m.LogisticSupport
	}
	return nil
}

func (m *AvailableDeliveryInstructions) GetContactMethod() []uint32 {
	if m != nil {
		return m.ContactMethod
	}
	return nil
}

func init() {
	proto.RegisterType((*BatchGetDeliveryInstructionByProductIDRequest)(nil), "lcos_protobuf.BatchGetDeliveryInstructionByProductIDRequest")
	proto.RegisterType((*DeliveryInstructionByProductIDResponse)(nil), "lcos_protobuf.DeliveryInstructionByProductIDResponse")
	proto.RegisterType((*ListDeliveryInstructionByRegionRequest)(nil), "lcos_protobuf.ListDeliveryInstructionByRegionRequest")
	proto.RegisterType((*DeliveryInstructioReqByPage)(nil), "lcos_protobuf.DeliveryInstructioReqByPage")
	proto.RegisterType((*DeliveryInstructionByRegionResponse)(nil), "lcos_protobuf.DeliveryInstructionByRegionResponse")
	proto.RegisterType((*DeliveryInstructionResp)(nil), "lcos_protobuf.DeliveryInstructionResp")
	proto.RegisterType((*DeliveryInstructionRespList)(nil), "lcos_protobuf.DeliveryInstructionRespList")
	proto.RegisterType((*AvailableDeliveryInstruction)(nil), "lcos_protobuf.AvailableDeliveryInstruction")
	proto.RegisterType((*ListDeliveryInstructionBySlsTnRequest)(nil), "lcos_protobuf.ListDeliveryInstructionBySlsTnRequest")
	proto.RegisterType((*DeliveryInstructionReqBySlsTn)(nil), "lcos_protobuf.DeliveryInstructionReqBySlsTn")
	proto.RegisterType((*BuyerAddress)(nil), "lcos_protobuf.BuyerAddress")
	proto.RegisterType((*DeliveryInstructionBySlsTnResponse)(nil), "lcos_protobuf.DeliveryInstructionBySlsTnResponse")
	proto.RegisterType((*SingleDeliveryInstructionBySlsTnResp)(nil), "lcos_protobuf.SingleDeliveryInstructionBySlsTnResp")
	proto.RegisterType((*AvailableDeliveryInstructions)(nil), "lcos_protobuf.AvailableDeliveryInstructions")
}

func init() {
	proto.RegisterFile("lcos_delivery_instruction.proto", fileDescriptor_b882d1bd5146d966)
}

var fileDescriptor_b882d1bd5146d966 = []byte{
	// 977 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0x4f, 0x6f, 0x23, 0x35,
	0x14, 0xaf, 0x93, 0xed, 0x9f, 0xbc, 0xee, 0xb0, 0x8b, 0xe9, 0x6e, 0xa7, 0xe9, 0xb6, 0x0d, 0x03,
	0x1b, 0xc2, 0xbf, 0x4a, 0x74, 0x41, 0x48, 0x2b, 0x0e, 0x6c, 0xa9, 0x80, 0x48, 0x0b, 0xaa, 0x1c,
	0xee, 0x23, 0x67, 0xec, 0x4c, 0x47, 0x9a, 0x8e, 0x27, 0xb6, 0x53, 0x29, 0x27, 0xb8, 0xc1, 0x09,
	0x3e, 0x02, 0x37, 0xae, 0xdc, 0xf8, 0x06, 0xc0, 0x27, 0xe0, 0x03, 0x70, 0xe0, 0xc4, 0x97, 0x40,
	0x76, 0x9c, 0x6c, 0x12, 0x66, 0x26, 0x59, 0xed, 0xde, 0xfc, 0xfe, 0xf8, 0xf9, 0xf7, 0x9e, 0x7f,
	0xef, 0xd9, 0x70, 0x92, 0x46, 0x42, 0x85, 0x8c, 0xa7, 0xc9, 0x0d, 0x97, 0xe3, 0x30, 0xc9, 0x94,
	0x96, 0xa3, 0x48, 0x27, 0x22, 0x3b, 0xcd, 0xa5, 0xd0, 0x02, 0x7b, 0xd6, 0xc1, 0xae, 0xfb, 0xa3,
	0x41, 0xf3, 0x8e, 0x15, 0xfb, 0x54, 0xf1, 0x89, 0x3d, 0xf8, 0x1e, 0xc1, 0xfb, 0xe7, 0x54, 0x47,
	0x57, 0x5f, 0x70, 0x7d, 0xe1, 0xc2, 0x74, 0x9f, 0x45, 0x39, 0x1f, 0x5f, 0x4a, 0xc1, 0x46, 0x91,
	0xee, 0x5e, 0x10, 0x3e, 0x1c, 0x71, 0xa5, 0xf1, 0xc7, 0x00, 0x92, 0x0f, 0xc3, 0x2b, 0x4e, 0x19,
	0x97, 0x3e, 0x6a, 0xd5, 0x3a, 0xbb, 0x67, 0xfe, 0xe9, 0xc2, 0x31, 0xa7, 0x84, 0x0f, 0xbf, 0xb4,
	0x76, 0xd2, 0x90, 0xd3, 0x25, 0x3e, 0x02, 0xc8, 0x27, 0xc1, 0xc2, 0x84, 0xf9, 0xb5, 0x56, 0xbd,
	0xd3, 0x20, 0x0d, 0xa7, 0xe9, 0xb2, 0xe0, 0x2f, 0x04, 0xed, 0x55, 0x08, 0x54, 0x2e, 0x32, 0xc5,
	0xf1, 0x63, 0xd8, 0x95, 0x5c, 0xe5, 0x8b, 0x18, 0x0e, 0xfe, 0x87, 0x41, 0xe5, 0x0e, 0x04, 0xc8,
	0xd9, 0x1a, 0x0b, 0x38, 0x2e, 0x2a, 0x57, 0x68, 0x03, 0xa6, 0x89, 0xd2, 0x16, 0xd9, 0xee, 0xd9,
	0x3b, 0x4b, 0xe1, 0x0a, 0xa0, 0x99, 0x13, 0x9e, 0x26, 0x4a, 0x93, 0x43, 0x56, 0x6e, 0x0c, 0xfe,
	0x44, 0xd0, 0x36, 0x8b, 0xc2, 0xdc, 0x08, 0x8f, 0xad, 0xef, 0x0b, 0x96, 0x96, 0x81, 0x5f, 0x92,
	0xd4, 0xd0, 0xaf, 0xd9, 0x30, 0xab, 0xd3, 0x21, 0x7c, 0x78, 0x3e, 0xbe, 0xa4, 0x31, 0x27, 0xf7,
	0x0b, 0xd3, 0x19, 0x06, 0x11, 0x1c, 0x56, 0x6c, 0xc3, 0xf7, 0x61, 0x2b, 0xa7, 0x31, 0xcf, 0x84,
	0x45, 0xee, 0x11, 0x27, 0xe1, 0x3d, 0xd8, 0x8c, 0xc4, 0x28, 0xd3, 0x16, 0x89, 0x47, 0x26, 0x82,
	0xf1, 0x96, 0x36, 0x79, 0xbf, 0xde, 0xaa, 0x75, 0x1a, 0xc4, 0x49, 0xc1, 0xef, 0x08, 0xde, 0xa8,
	0x2c, 0xd5, 0x4b, 0xe0, 0x40, 0x1f, 0x0e, 0x4a, 0x39, 0xe0, 0xea, 0xd5, 0x5e, 0xef, 0xfa, 0xc9,
	0x7e, 0xc9, 0xd5, 0x07, 0x7f, 0x20, 0xd8, 0x2f, 0xd9, 0x84, 0xf7, 0x61, 0xdb, 0xd4, 0x26, 0x5c,
	0x2c, 0xd5, 0xd7, 0x65, 0xa5, 0xda, 0x83, 0x4d, 0x2d, 0x34, 0x4d, 0x6d, 0xa5, 0x3c, 0x32, 0x11,
	0xf0, 0xa0, 0x24, 0x09, 0xcb, 0xe1, 0x5b, 0xcf, 0xcd, 0xe1, 0xa2, 0x44, 0x2c, 0x7f, 0x7f, 0x45,
	0x45, 0xd7, 0x3e, 0xdb, 0xb8, 0xd4, 0xd6, 0xc8, 0x5e, 0xe6, 0xb3, 0xb6, 0xc6, 0x43, 0x38, 0xa6,
	0x37, 0x34, 0x49, 0x69, 0x3f, 0xe5, 0x85, 0x83, 0xca, 0xf5, 0xdb, 0xbb, 0x4b, 0x58, 0x9f, 0x4c,
	0x37, 0x15, 0x9d, 0xfd, 0x80, 0x56, 0x58, 0x83, 0x1b, 0x78, 0x50, 0xb5, 0x1b, 0x37, 0x61, 0x27,
	0xa2, 0x9a, 0xc7, 0x42, 0x8e, 0x5d, 0xfd, 0x67, 0x32, 0x7e, 0x5c, 0x52, 0xd5, 0x24, 0x1b, 0x08,
	0x8b, 0xd4, 0x2b, 0xac, 0x54, 0x37, 0x1b, 0x88, 0xe0, 0x6f, 0x04, 0x0f, 0x4b, 0x3b, 0xbd, 0x97,
	0xaa, 0x6f, 0x5e, 0xbc, 0xd1, 0xbf, 0x85, 0x76, 0x21, 0xbc, 0xfe, 0x38, 0x54, 0xa9, 0xd2, 0xb6,
	0xe3, 0xe7, 0xa7, 0xd8, 0x7b, 0xeb, 0x30, 0x60, 0x38, 0x45, 0xf7, 0x3a, 0x2b, 0x41, 0xae, 0x8d,
	0x97, 0x65, 0xc3, 0x6f, 0x35, 0x38, 0xaa, 0x0c, 0x82, 0xef, 0xc1, 0x96, 0x4a, 0x55, 0xa8, 0x33,
	0xc7, 0x85, 0x4d, 0x65, 0xd5, 0x3e, 0x6c, 0x5b, 0x36, 0xcb, 0xb1, 0x25, 0x77, 0x83, 0x4c, 0x45,
	0xd3, 0x0d, 0x69, 0x92, 0x71, 0xc3, 0x1e, 0x37, 0x0a, 0x8c, 0xd8, 0x65, 0xf8, 0x00, 0x76, 0x84,
	0xce, 0xc3, 0x41, 0x4a, 0x63, 0xff, 0x96, 0xbd, 0xa7, 0x6d, 0xa1, 0xf3, 0xcf, 0x53, 0x1a, 0xe3,
	0x87, 0xf0, 0x4a, 0x4e, 0xc7, 0xd7, 0x3c, 0xd3, 0xe1, 0x35, 0xd7, 0x57, 0x82, 0xf9, 0x9b, 0xd6,
	0xc1, 0x73, 0xda, 0xaf, 0xac, 0x12, 0x7f, 0x0a, 0x5e, 0x7f, 0x34, 0xe6, 0x32, 0xa4, 0x8c, 0x49,
	0xae, 0x94, 0xbf, 0x65, 0x4b, 0x7d, 0xb8, 0x54, 0x95, 0x73, 0xe3, 0xf3, 0x64, 0xe2, 0x42, 0x6e,
	0xf7, 0xe7, 0x24, 0xc3, 0x6e, 0x95, 0x8a, 0x50, 0x69, 0xaa, 0x47, 0xca, 0xdf, 0xb6, 0x87, 0x34,
	0x54, 0x2a, 0x7a, 0x56, 0x81, 0xef, 0x42, 0x9d, 0x33, 0xe6, 0xef, 0xb4, 0x50, 0xc7, 0x23, 0x66,
	0x69, 0xe6, 0xda, 0x20, 0x12, 0x8c, 0x2b, 0xbf, 0x61, 0x5f, 0x38, 0x27, 0x05, 0x19, 0xdc, 0x9e,
	0x3f, 0x06, 0x9f, 0xc2, 0x6b, 0xae, 0xda, 0x21, 0x4b, 0x94, 0x96, 0xc9, 0xb4, 0x7f, 0x4c, 0xa4,
	0x57, 0x9d, 0xe9, 0xc2, 0x59, 0xba, 0x6c, 0xde, 0x3f, 0x17, 0x4a, 0xd3, 0x34, 0x34, 0x71, 0xfd,
	0x5a, 0x0b, 0x75, 0x1a, 0x33, 0xff, 0x4b, 0x6b, 0xf9, 0x4c, 0x30, 0x1e, 0xfc, 0x8b, 0x20, 0xa8,
	0x22, 0xe2, 0x4b, 0x18, 0xa3, 0x3f, 0x20, 0x78, 0x6b, 0x15, 0x1b, 0x17, 0x1f, 0xd5, 0x47, 0x4b,
	0x81, 0x7b, 0x49, 0x16, 0x17, 0xf6, 0xe8, 0x1c, 0x4c, 0x12, 0x54, 0xb1, 0xd2, 0x3d, 0xb2, 0xdf,
	0xd5, 0xe0, 0xcd, 0x75, 0x82, 0x95, 0xb1, 0x73, 0x8e, 0x83, 0xb5, 0x05, 0x0e, 0x6a, 0x38, 0xa9,
	0x1e, 0x5f, 0xca, 0xaf, 0xb7, 0x50, 0x41, 0xa7, 0x55, 0x4d, 0x20, 0x45, 0x8e, 0xaa, 0x06, 0x98,
	0x32, 0xcd, 0x22, 0xb9, 0xb6, 0x17, 0xec, 0x88, 0xef, 0x44, 0x63, 0xb9, 0xe6, 0x4a, 0xd1, 0x98,
	0x5b, 0xc6, 0x37, 0xc8, 0x54, 0x0c, 0x7e, 0x41, 0x70, 0x54, 0x79, 0x28, 0x7e, 0x04, 0xf7, 0x8a,
	0x33, 0x40, 0x76, 0xae, 0xed, 0xb1, 0xa2, 0x4d, 0x6f, 0xc3, 0xdd, 0x54, 0xc4, 0x89, 0xd2, 0x49,
	0x14, 0xaa, 0x51, 0x9e, 0x0b, 0xa9, 0xdd, 0x1c, 0xbc, 0x33, 0xd5, 0xf7, 0x26, 0x6a, 0xd3, 0x94,
	0x91, 0xc8, 0x34, 0x8d, 0x66, 0x4d, 0x59, 0xb7, 0x8e, 0x9e, 0xd3, 0x4e, 0x9a, 0xf2, 0xec, 0x9f,
	0x3a, 0x1c, 0x3f, 0x8d, 0x84, 0x2a, 0xc0, 0xd8, 0xe3, 0xf2, 0x26, 0x89, 0x38, 0xfe, 0x19, 0x41,
	0x7b, 0xbd, 0x5f, 0x29, 0xfe, 0x64, 0xb9, 0x97, 0x9f, 0xe7, 0x33, 0xdb, 0xfc, 0x68, 0xf5, 0x7c,
	0x2c, 0xf8, 0x80, 0x06, 0x1b, 0xf8, 0x47, 0x04, 0x27, 0x2b, 0x7e, 0x75, 0x78, 0x39, 0xf8, 0x7a,
	0xbf, 0xc0, 0xe6, 0xd9, 0x3a, 0x98, 0x16, 0x7f, 0x43, 0xc1, 0x06, 0xfe, 0x09, 0x41, 0x50, 0x99,
	0xfb, 0x64, 0x3a, 0x7f, 0xb8, 0x2e, 0xa6, 0xf9, 0xf7, 0xaa, 0xf9, 0xc1, 0x3a, 0x90, 0x16, 0x06,
	0x4b, 0xb0, 0xf1, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xcd, 0x99, 0xe9, 0x96, 0x9c, 0x0c, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosDeliveryInstructionServiceClient is the client API for LcosDeliveryInstructionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosDeliveryInstructionServiceClient interface {
	BatchGetDeliveryInstructionByProductID(ctx context.Context, in *BatchGetDeliveryInstructionByProductIDRequest, opts ...grpc.CallOption) (*DeliveryInstructionByProductIDResponse, error)
	ListDeliveryInstructionByRegion(ctx context.Context, in *ListDeliveryInstructionByRegionRequest, opts ...grpc.CallOption) (*DeliveryInstructionByRegionResponse, error)
	BatchGetDeliveryInstructionBySlsTn(ctx context.Context, in *ListDeliveryInstructionBySlsTnRequest, opts ...grpc.CallOption) (*DeliveryInstructionBySlsTnResponse, error)
}

type lcosDeliveryInstructionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosDeliveryInstructionServiceClient(cc grpc.ClientConnInterface) LcosDeliveryInstructionServiceClient {
	return &lcosDeliveryInstructionServiceClient{cc}
}

func (c *lcosDeliveryInstructionServiceClient) BatchGetDeliveryInstructionByProductID(ctx context.Context, in *BatchGetDeliveryInstructionByProductIDRequest, opts ...grpc.CallOption) (*DeliveryInstructionByProductIDResponse, error) {
	out := new(DeliveryInstructionByProductIDResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosDeliveryInstructionService/BatchGetDeliveryInstructionByProductID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosDeliveryInstructionServiceClient) ListDeliveryInstructionByRegion(ctx context.Context, in *ListDeliveryInstructionByRegionRequest, opts ...grpc.CallOption) (*DeliveryInstructionByRegionResponse, error) {
	out := new(DeliveryInstructionByRegionResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosDeliveryInstructionService/ListDeliveryInstructionByRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosDeliveryInstructionServiceClient) BatchGetDeliveryInstructionBySlsTn(ctx context.Context, in *ListDeliveryInstructionBySlsTnRequest, opts ...grpc.CallOption) (*DeliveryInstructionBySlsTnResponse, error) {
	out := new(DeliveryInstructionBySlsTnResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosDeliveryInstructionService/BatchGetDeliveryInstructionBySlsTn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosDeliveryInstructionServiceServer is the server API for LcosDeliveryInstructionService service.
type LcosDeliveryInstructionServiceServer interface {
	BatchGetDeliveryInstructionByProductID(context.Context, *BatchGetDeliveryInstructionByProductIDRequest) (*DeliveryInstructionByProductIDResponse, error)
	ListDeliveryInstructionByRegion(context.Context, *ListDeliveryInstructionByRegionRequest) (*DeliveryInstructionByRegionResponse, error)
	BatchGetDeliveryInstructionBySlsTn(context.Context, *ListDeliveryInstructionBySlsTnRequest) (*DeliveryInstructionBySlsTnResponse, error)
}

// UnimplementedLcosDeliveryInstructionServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLcosDeliveryInstructionServiceServer struct {
}

func (*UnimplementedLcosDeliveryInstructionServiceServer) BatchGetDeliveryInstructionByProductID(ctx context.Context, req *BatchGetDeliveryInstructionByProductIDRequest) (*DeliveryInstructionByProductIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetDeliveryInstructionByProductID not implemented")
}
func (*UnimplementedLcosDeliveryInstructionServiceServer) ListDeliveryInstructionByRegion(ctx context.Context, req *ListDeliveryInstructionByRegionRequest) (*DeliveryInstructionByRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDeliveryInstructionByRegion not implemented")
}
func (*UnimplementedLcosDeliveryInstructionServiceServer) BatchGetDeliveryInstructionBySlsTn(ctx context.Context, req *ListDeliveryInstructionBySlsTnRequest) (*DeliveryInstructionBySlsTnResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetDeliveryInstructionBySlsTn not implemented")
}

func RegisterLcosDeliveryInstructionServiceServer(s *grpc.Server, srv LcosDeliveryInstructionServiceServer) {
	s.RegisterService(&_LcosDeliveryInstructionService_serviceDesc, srv)
}

func _LcosDeliveryInstructionService_BatchGetDeliveryInstructionByProductID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetDeliveryInstructionByProductIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosDeliveryInstructionServiceServer).BatchGetDeliveryInstructionByProductID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosDeliveryInstructionService/BatchGetDeliveryInstructionByProductID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosDeliveryInstructionServiceServer).BatchGetDeliveryInstructionByProductID(ctx, req.(*BatchGetDeliveryInstructionByProductIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosDeliveryInstructionService_ListDeliveryInstructionByRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDeliveryInstructionByRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosDeliveryInstructionServiceServer).ListDeliveryInstructionByRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosDeliveryInstructionService/ListDeliveryInstructionByRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosDeliveryInstructionServiceServer).ListDeliveryInstructionByRegion(ctx, req.(*ListDeliveryInstructionByRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosDeliveryInstructionService_BatchGetDeliveryInstructionBySlsTn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDeliveryInstructionBySlsTnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosDeliveryInstructionServiceServer).BatchGetDeliveryInstructionBySlsTn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosDeliveryInstructionService/BatchGetDeliveryInstructionBySlsTn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosDeliveryInstructionServiceServer).BatchGetDeliveryInstructionBySlsTn(ctx, req.(*ListDeliveryInstructionBySlsTnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosDeliveryInstructionService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosDeliveryInstructionService",
	HandlerType: (*LcosDeliveryInstructionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetDeliveryInstructionByProductID",
			Handler:    _LcosDeliveryInstructionService_BatchGetDeliveryInstructionByProductID_Handler,
		},
		{
			MethodName: "ListDeliveryInstructionByRegion",
			Handler:    _LcosDeliveryInstructionService_ListDeliveryInstructionByRegion_Handler,
		},
		{
			MethodName: "BatchGetDeliveryInstructionBySlsTn",
			Handler:    _LcosDeliveryInstructionService_BatchGetDeliveryInstructionBySlsTn_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_delivery_instruction.proto",
}
