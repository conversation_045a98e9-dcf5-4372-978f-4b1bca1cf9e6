// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_health.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PingBody struct {
	Str                  *string  `protobuf:"bytes,1,req,name=str" json:"str,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PingBody) Reset()         { *m = PingBody{} }
func (m *PingBody) String() string { return proto.CompactTextString(m) }
func (*PingBody) ProtoMessage()    {}
func (*PingBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_87465535e212897b, []int{0}
}

func (m *PingBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PingBody.Unmarshal(m, b)
}
func (m *PingBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PingBody.Marshal(b, m, deterministic)
}
func (m *PingBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PingBody.Merge(m, src)
}
func (m *PingBody) XXX_Size() int {
	return xxx_messageInfo_PingBody.Size(m)
}
func (m *PingBody) XXX_DiscardUnknown() {
	xxx_messageInfo_PingBody.DiscardUnknown(m)
}

var xxx_messageInfo_PingBody proto.InternalMessageInfo

func (m *PingBody) GetStr() string {
	if m != nil && m.Str != nil {
		return *m.Str
	}
	return ""
}

func init() {
	proto.RegisterType((*PingBody)(nil), "lcos_protobuf.PingBody")
}

func init() {
	proto.RegisterFile("lcos_health.proto", fileDescriptor_87465535e212897b)
}

var fileDescriptor_87465535e212897b = []byte{
	// 106 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0xcc, 0x49, 0xce, 0x2f,
	0x8e, 0xcf, 0x48, 0x4d, 0xcc, 0x29, 0xc9, 0xd0, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0xe2, 0x05,
	0x0b, 0x81, 0xd9, 0x49, 0xa5, 0x69, 0x4a, 0x32, 0x5c, 0x1c, 0x01, 0x99, 0x79, 0xe9, 0x4e, 0xf9,
	0x29, 0x95, 0x42, 0x02, 0x5c, 0xcc, 0xc5, 0x25, 0x45, 0x12, 0x8c, 0x0a, 0x4c, 0x1a, 0x9c, 0x41,
	0x20, 0xa6, 0x91, 0x0b, 0x17, 0x9b, 0x07, 0x58, 0xb3, 0x90, 0x15, 0x17, 0x0b, 0x48, 0x9d, 0x90,
	0xb8, 0x1e, 0x8a, 0x7e, 0x3d, 0x98, 0x66, 0x29, 0x5c, 0x12, 0x4a, 0x0c, 0x80, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xf1, 0x92, 0xaa, 0xcd, 0x86, 0x00, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// HealthClient is the client API for Health service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type HealthClient interface {
	Ping(ctx context.Context, in *PingBody, opts ...grpc.CallOption) (*PingBody, error)
}

type healthClient struct {
	cc grpc.ClientConnInterface
}

func NewHealthClient(cc grpc.ClientConnInterface) HealthClient {
	return &healthClient{cc}
}

func (c *healthClient) Ping(ctx context.Context, in *PingBody, opts ...grpc.CallOption) (*PingBody, error) {
	out := new(PingBody)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.Health/Ping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HealthServer is the server API for Health service.
type HealthServer interface {
	Ping(context.Context, *PingBody) (*PingBody, error)
}

// UnimplementedHealthServer can be embedded to have forward compatible implementations.
type UnimplementedHealthServer struct {
}

func (*UnimplementedHealthServer) Ping(ctx context.Context, req *PingBody) (*PingBody, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}

func RegisterHealthServer(s *grpc.Server, srv HealthServer) {
	s.RegisterService(&_Health_serviceDesc, srv)
}

func _Health_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingBody)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.Health/Ping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthServer).Ping(ctx, req.(*PingBody))
	}
	return interceptor(ctx, in, info, handler)
}

var _Health_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.Health",
	HandlerType: (*HealthServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _Health_Ping_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_health.proto",
}
