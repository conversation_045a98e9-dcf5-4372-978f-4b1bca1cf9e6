// Code generated by protoc-gen-go. DO NOT EDIT.
// source: lcos_serviceable.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// --- 获取线服务范围请求体 ---
type CheckLineServiceableAreaRequest struct {
	ReqHeader            *ReqHeader                    `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BaseInfo             *CheckServiceableAreaBaseInfo `protobuf:"bytes,2,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	PickupInfo           *LocationInfo                 `protobuf:"bytes,3,opt,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	DeliverInfo          *LocationInfo                 `protobuf:"bytes,4,opt,name=deliver_info,json=deliverInfo" json:"deliver_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *CheckLineServiceableAreaRequest) Reset()         { *m = CheckLineServiceableAreaRequest{} }
func (m *CheckLineServiceableAreaRequest) String() string { return proto.CompactTextString(m) }
func (*CheckLineServiceableAreaRequest) ProtoMessage()    {}
func (*CheckLineServiceableAreaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{0}
}

func (m *CheckLineServiceableAreaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLineServiceableAreaRequest.Unmarshal(m, b)
}
func (m *CheckLineServiceableAreaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLineServiceableAreaRequest.Marshal(b, m, deterministic)
}
func (m *CheckLineServiceableAreaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLineServiceableAreaRequest.Merge(m, src)
}
func (m *CheckLineServiceableAreaRequest) XXX_Size() int {
	return xxx_messageInfo_CheckLineServiceableAreaRequest.Size(m)
}
func (m *CheckLineServiceableAreaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLineServiceableAreaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLineServiceableAreaRequest proto.InternalMessageInfo

func (m *CheckLineServiceableAreaRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *CheckLineServiceableAreaRequest) GetBaseInfo() *CheckServiceableAreaBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *CheckLineServiceableAreaRequest) GetPickupInfo() *LocationInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

func (m *CheckLineServiceableAreaRequest) GetDeliverInfo() *LocationInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

// --- v2版本 获取线服务范围请求体 ---
type CheckLineServiceableAreaRequest2 struct {
	ReqHeader            *ReqHeader                     `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BaseInfo             *CheckServiceableAreaBaseInfo2 `protobuf:"bytes,2,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	PickupInfo           *LocationInfo                  `protobuf:"bytes,3,opt,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	DeliverInfo          *LocationInfo                  `protobuf:"bytes,4,opt,name=deliver_info,json=deliverInfo" json:"deliver_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *CheckLineServiceableAreaRequest2) Reset()         { *m = CheckLineServiceableAreaRequest2{} }
func (m *CheckLineServiceableAreaRequest2) String() string { return proto.CompactTextString(m) }
func (*CheckLineServiceableAreaRequest2) ProtoMessage()    {}
func (*CheckLineServiceableAreaRequest2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{1}
}

func (m *CheckLineServiceableAreaRequest2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLineServiceableAreaRequest2.Unmarshal(m, b)
}
func (m *CheckLineServiceableAreaRequest2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLineServiceableAreaRequest2.Marshal(b, m, deterministic)
}
func (m *CheckLineServiceableAreaRequest2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLineServiceableAreaRequest2.Merge(m, src)
}
func (m *CheckLineServiceableAreaRequest2) XXX_Size() int {
	return xxx_messageInfo_CheckLineServiceableAreaRequest2.Size(m)
}
func (m *CheckLineServiceableAreaRequest2) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLineServiceableAreaRequest2.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLineServiceableAreaRequest2 proto.InternalMessageInfo

func (m *CheckLineServiceableAreaRequest2) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *CheckLineServiceableAreaRequest2) GetBaseInfo() *CheckServiceableAreaBaseInfo2 {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *CheckLineServiceableAreaRequest2) GetPickupInfo() *LocationInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

func (m *CheckLineServiceableAreaRequest2) GetDeliverInfo() *LocationInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

// --- 获取线服务范围响应体 ---
type CheckLineServiceableAreaResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CheckLineServiceableAreaResponse) Reset()         { *m = CheckLineServiceableAreaResponse{} }
func (m *CheckLineServiceableAreaResponse) String() string { return proto.CompactTextString(m) }
func (*CheckLineServiceableAreaResponse) ProtoMessage()    {}
func (*CheckLineServiceableAreaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{2}
}

func (m *CheckLineServiceableAreaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLineServiceableAreaResponse.Unmarshal(m, b)
}
func (m *CheckLineServiceableAreaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLineServiceableAreaResponse.Marshal(b, m, deterministic)
}
func (m *CheckLineServiceableAreaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLineServiceableAreaResponse.Merge(m, src)
}
func (m *CheckLineServiceableAreaResponse) XXX_Size() int {
	return xxx_messageInfo_CheckLineServiceableAreaResponse.Size(m)
}
func (m *CheckLineServiceableAreaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLineServiceableAreaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLineServiceableAreaResponse proto.InternalMessageInfo

func (m *CheckLineServiceableAreaResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

// --- deprecated v1版本base_info，包含场景参数 ---
type CheckServiceableAreaBaseInfo struct {
	LineId               *string  `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	IsCheckOperation     *uint32  `protobuf:"varint,2,req,name=is_check_operation,json=isCheckOperation" json:"is_check_operation,omitempty"`
	IsCheckBasic         *uint32  `protobuf:"varint,3,req,name=is_check_basic,json=isCheckBasic" json:"is_check_basic,omitempty"`
	PaymentMethod        *uint32  `protobuf:"varint,4,req,name=payment_method,json=paymentMethod" json:"payment_method,omitempty"`
	CollectType          *uint32  `protobuf:"varint,5,opt,name=collect_type,json=collectType" json:"collect_type,omitempty"`
	DeliverType          *uint32  `protobuf:"varint,6,opt,name=deliver_type,json=deliverType" json:"deliver_type,omitempty"`
	Scenario             *uint32  `protobuf:"varint,7,req,name=scenario" json:"scenario,omitempty"`
	SkipZoneRoute        *uint32  `protobuf:"varint,8,opt,name=skip_zone_route,json=skipZoneRoute" json:"skip_zone_route,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckServiceableAreaBaseInfo) Reset()         { *m = CheckServiceableAreaBaseInfo{} }
func (m *CheckServiceableAreaBaseInfo) String() string { return proto.CompactTextString(m) }
func (*CheckServiceableAreaBaseInfo) ProtoMessage()    {}
func (*CheckServiceableAreaBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{3}
}

func (m *CheckServiceableAreaBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckServiceableAreaBaseInfo.Unmarshal(m, b)
}
func (m *CheckServiceableAreaBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckServiceableAreaBaseInfo.Marshal(b, m, deterministic)
}
func (m *CheckServiceableAreaBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckServiceableAreaBaseInfo.Merge(m, src)
}
func (m *CheckServiceableAreaBaseInfo) XXX_Size() int {
	return xxx_messageInfo_CheckServiceableAreaBaseInfo.Size(m)
}
func (m *CheckServiceableAreaBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckServiceableAreaBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CheckServiceableAreaBaseInfo proto.InternalMessageInfo

func (m *CheckServiceableAreaBaseInfo) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *CheckServiceableAreaBaseInfo) GetIsCheckOperation() uint32 {
	if m != nil && m.IsCheckOperation != nil {
		return *m.IsCheckOperation
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo) GetIsCheckBasic() uint32 {
	if m != nil && m.IsCheckBasic != nil {
		return *m.IsCheckBasic
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo) GetPaymentMethod() uint32 {
	if m != nil && m.PaymentMethod != nil {
		return *m.PaymentMethod
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo) GetCollectType() uint32 {
	if m != nil && m.CollectType != nil {
		return *m.CollectType
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo) GetDeliverType() uint32 {
	if m != nil && m.DeliverType != nil {
		return *m.DeliverType
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo) GetScenario() uint32 {
	if m != nil && m.Scenario != nil {
		return *m.Scenario
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo) GetSkipZoneRoute() uint32 {
	if m != nil && m.SkipZoneRoute != nil {
		return *m.SkipZoneRoute
	}
	return 0
}

// --- v2版本base_info ---
type CheckServiceableAreaBaseInfo2 struct {
	LineId               *string  `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	IsCheckOperation     *uint32  `protobuf:"varint,2,req,name=is_check_operation,json=isCheckOperation" json:"is_check_operation,omitempty"`
	IsCheckBasic         *uint32  `protobuf:"varint,3,req,name=is_check_basic,json=isCheckBasic" json:"is_check_basic,omitempty"`
	PaymentMethod        *uint32  `protobuf:"varint,4,req,name=payment_method,json=paymentMethod" json:"payment_method,omitempty"`
	CollectType          *uint32  `protobuf:"varint,5,opt,name=collect_type,json=collectType" json:"collect_type,omitempty"`
	DeliverType          *uint32  `protobuf:"varint,6,opt,name=deliver_type,json=deliverType" json:"deliver_type,omitempty"`
	CheckSender          *uint32  `protobuf:"varint,7,req,name=check_sender,json=checkSender" json:"check_sender,omitempty"`
	SenderCheckLevel     *uint32  `protobuf:"varint,8,opt,name=sender_check_level,json=senderCheckLevel" json:"sender_check_level,omitempty"`
	CheckReceiver        *uint32  `protobuf:"varint,9,req,name=check_receiver,json=checkReceiver" json:"check_receiver,omitempty"`
	ReceiverCheckLevel   *uint32  `protobuf:"varint,10,opt,name=receiver_check_level,json=receiverCheckLevel" json:"receiver_check_level,omitempty"`
	SkipPostcode         *uint32  `protobuf:"varint,11,opt,name=skip_postcode,json=skipPostcode" json:"skip_postcode,omitempty"`
	SkipZoneRoute        *uint32  `protobuf:"varint,12,opt,name=skip_zone_route,json=skipZoneRoute" json:"skip_zone_route,omitempty"`
	SkipElectricFence    *bool    `protobuf:"varint,13,opt,name=skip_electric_fence,json=skipElectricFence" json:"skip_electric_fence,omitempty"`
	UseElectricFence     *bool    `protobuf:"varint,14,opt,name=use_electric_fence,json=useElectricFence" json:"use_electric_fence,omitempty"`
	CheckPredefinedRoute *bool    `protobuf:"varint,15,opt,name=check_predefined_route,json=checkPredefinedRoute" json:"check_predefined_route,omitempty"`
	PredefinedRouteCodes []string `protobuf:"bytes,16,rep,name=predefined_route_codes,json=predefinedRouteCodes" json:"predefined_route_codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckServiceableAreaBaseInfo2) Reset()         { *m = CheckServiceableAreaBaseInfo2{} }
func (m *CheckServiceableAreaBaseInfo2) String() string { return proto.CompactTextString(m) }
func (*CheckServiceableAreaBaseInfo2) ProtoMessage()    {}
func (*CheckServiceableAreaBaseInfo2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{4}
}

func (m *CheckServiceableAreaBaseInfo2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckServiceableAreaBaseInfo2.Unmarshal(m, b)
}
func (m *CheckServiceableAreaBaseInfo2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckServiceableAreaBaseInfo2.Marshal(b, m, deterministic)
}
func (m *CheckServiceableAreaBaseInfo2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckServiceableAreaBaseInfo2.Merge(m, src)
}
func (m *CheckServiceableAreaBaseInfo2) XXX_Size() int {
	return xxx_messageInfo_CheckServiceableAreaBaseInfo2.Size(m)
}
func (m *CheckServiceableAreaBaseInfo2) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckServiceableAreaBaseInfo2.DiscardUnknown(m)
}

var xxx_messageInfo_CheckServiceableAreaBaseInfo2 proto.InternalMessageInfo

func (m *CheckServiceableAreaBaseInfo2) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *CheckServiceableAreaBaseInfo2) GetIsCheckOperation() uint32 {
	if m != nil && m.IsCheckOperation != nil {
		return *m.IsCheckOperation
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetIsCheckBasic() uint32 {
	if m != nil && m.IsCheckBasic != nil {
		return *m.IsCheckBasic
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetPaymentMethod() uint32 {
	if m != nil && m.PaymentMethod != nil {
		return *m.PaymentMethod
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetCollectType() uint32 {
	if m != nil && m.CollectType != nil {
		return *m.CollectType
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetDeliverType() uint32 {
	if m != nil && m.DeliverType != nil {
		return *m.DeliverType
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetCheckSender() uint32 {
	if m != nil && m.CheckSender != nil {
		return *m.CheckSender
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetSenderCheckLevel() uint32 {
	if m != nil && m.SenderCheckLevel != nil {
		return *m.SenderCheckLevel
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetCheckReceiver() uint32 {
	if m != nil && m.CheckReceiver != nil {
		return *m.CheckReceiver
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetReceiverCheckLevel() uint32 {
	if m != nil && m.ReceiverCheckLevel != nil {
		return *m.ReceiverCheckLevel
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetSkipPostcode() uint32 {
	if m != nil && m.SkipPostcode != nil {
		return *m.SkipPostcode
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetSkipZoneRoute() uint32 {
	if m != nil && m.SkipZoneRoute != nil {
		return *m.SkipZoneRoute
	}
	return 0
}

func (m *CheckServiceableAreaBaseInfo2) GetSkipElectricFence() bool {
	if m != nil && m.SkipElectricFence != nil {
		return *m.SkipElectricFence
	}
	return false
}

func (m *CheckServiceableAreaBaseInfo2) GetUseElectricFence() bool {
	if m != nil && m.UseElectricFence != nil {
		return *m.UseElectricFence
	}
	return false
}

func (m *CheckServiceableAreaBaseInfo2) GetCheckPredefinedRoute() bool {
	if m != nil && m.CheckPredefinedRoute != nil {
		return *m.CheckPredefinedRoute
	}
	return false
}

func (m *CheckServiceableAreaBaseInfo2) GetPredefinedRouteCodes() []string {
	if m != nil {
		return m.PredefinedRouteCodes
	}
	return nil
}

// --- 批量获取线服务范围请求体 ---
type BatchCheckLineServiceableAreaRequest struct {
	ReqHeader            *ReqHeader                           `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	CheckReqList         []*SingleCheckServiceableAreaRequest `protobuf:"bytes,2,rep,name=check_req_list,json=checkReqList" json:"check_req_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *BatchCheckLineServiceableAreaRequest) Reset()         { *m = BatchCheckLineServiceableAreaRequest{} }
func (m *BatchCheckLineServiceableAreaRequest) String() string { return proto.CompactTextString(m) }
func (*BatchCheckLineServiceableAreaRequest) ProtoMessage()    {}
func (*BatchCheckLineServiceableAreaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{5}
}

func (m *BatchCheckLineServiceableAreaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckLineServiceableAreaRequest.Unmarshal(m, b)
}
func (m *BatchCheckLineServiceableAreaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckLineServiceableAreaRequest.Marshal(b, m, deterministic)
}
func (m *BatchCheckLineServiceableAreaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckLineServiceableAreaRequest.Merge(m, src)
}
func (m *BatchCheckLineServiceableAreaRequest) XXX_Size() int {
	return xxx_messageInfo_BatchCheckLineServiceableAreaRequest.Size(m)
}
func (m *BatchCheckLineServiceableAreaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckLineServiceableAreaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckLineServiceableAreaRequest proto.InternalMessageInfo

func (m *BatchCheckLineServiceableAreaRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckLineServiceableAreaRequest) GetCheckReqList() []*SingleCheckServiceableAreaRequest {
	if m != nil {
		return m.CheckReqList
	}
	return nil
}

// --- 多批量获取线服务范围请求体 ---
type MultipleBatchCheckLineServiceableAreaRequest struct {
	ReqHeader            *ReqHeader                                   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	OrderReqList         []*OrderBatchCheckLineServiceableAreaRequest `protobuf:"bytes,2,rep,name=order_req_list,json=orderReqList" json:"order_req_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *MultipleBatchCheckLineServiceableAreaRequest) Reset() {
	*m = MultipleBatchCheckLineServiceableAreaRequest{}
}
func (m *MultipleBatchCheckLineServiceableAreaRequest) String() string {
	return proto.CompactTextString(m)
}
func (*MultipleBatchCheckLineServiceableAreaRequest) ProtoMessage() {}
func (*MultipleBatchCheckLineServiceableAreaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{6}
}

func (m *MultipleBatchCheckLineServiceableAreaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultipleBatchCheckLineServiceableAreaRequest.Unmarshal(m, b)
}
func (m *MultipleBatchCheckLineServiceableAreaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultipleBatchCheckLineServiceableAreaRequest.Marshal(b, m, deterministic)
}
func (m *MultipleBatchCheckLineServiceableAreaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultipleBatchCheckLineServiceableAreaRequest.Merge(m, src)
}
func (m *MultipleBatchCheckLineServiceableAreaRequest) XXX_Size() int {
	return xxx_messageInfo_MultipleBatchCheckLineServiceableAreaRequest.Size(m)
}
func (m *MultipleBatchCheckLineServiceableAreaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MultipleBatchCheckLineServiceableAreaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MultipleBatchCheckLineServiceableAreaRequest proto.InternalMessageInfo

func (m *MultipleBatchCheckLineServiceableAreaRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *MultipleBatchCheckLineServiceableAreaRequest) GetOrderReqList() []*OrderBatchCheckLineServiceableAreaRequest {
	if m != nil {
		return m.OrderReqList
	}
	return nil
}

// --- 多批量获取线服务范围请求体 ---
type OrderBatchCheckLineServiceableAreaRequest struct {
	CheckReqList         []*SingleCheckServiceableAreaRequest `protobuf:"bytes,1,rep,name=check_req_list,json=checkReqList" json:"check_req_list,omitempty"`
	ReqNo                *string                              `protobuf:"bytes,2,req,name=req_no,json=reqNo" json:"req_no,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *OrderBatchCheckLineServiceableAreaRequest) Reset() {
	*m = OrderBatchCheckLineServiceableAreaRequest{}
}
func (m *OrderBatchCheckLineServiceableAreaRequest) String() string {
	return proto.CompactTextString(m)
}
func (*OrderBatchCheckLineServiceableAreaRequest) ProtoMessage() {}
func (*OrderBatchCheckLineServiceableAreaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{7}
}

func (m *OrderBatchCheckLineServiceableAreaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderBatchCheckLineServiceableAreaRequest.Unmarshal(m, b)
}
func (m *OrderBatchCheckLineServiceableAreaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderBatchCheckLineServiceableAreaRequest.Marshal(b, m, deterministic)
}
func (m *OrderBatchCheckLineServiceableAreaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderBatchCheckLineServiceableAreaRequest.Merge(m, src)
}
func (m *OrderBatchCheckLineServiceableAreaRequest) XXX_Size() int {
	return xxx_messageInfo_OrderBatchCheckLineServiceableAreaRequest.Size(m)
}
func (m *OrderBatchCheckLineServiceableAreaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderBatchCheckLineServiceableAreaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderBatchCheckLineServiceableAreaRequest proto.InternalMessageInfo

func (m *OrderBatchCheckLineServiceableAreaRequest) GetCheckReqList() []*SingleCheckServiceableAreaRequest {
	if m != nil {
		return m.CheckReqList
	}
	return nil
}

func (m *OrderBatchCheckLineServiceableAreaRequest) GetReqNo() string {
	if m != nil && m.ReqNo != nil {
		return *m.ReqNo
	}
	return ""
}

// --- 批量获取线服务范围的single request ---
type SingleCheckServiceableAreaRequest struct {
	BaseInfo             *CheckServiceableAreaBaseInfo2 `protobuf:"bytes,1,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	PickupInfo           *LocationInfo                  `protobuf:"bytes,2,opt,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	DeliverInfo          *LocationInfo                  `protobuf:"bytes,3,opt,name=deliver_info,json=deliverInfo" json:"deliver_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *SingleCheckServiceableAreaRequest) Reset()         { *m = SingleCheckServiceableAreaRequest{} }
func (m *SingleCheckServiceableAreaRequest) String() string { return proto.CompactTextString(m) }
func (*SingleCheckServiceableAreaRequest) ProtoMessage()    {}
func (*SingleCheckServiceableAreaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{8}
}

func (m *SingleCheckServiceableAreaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleCheckServiceableAreaRequest.Unmarshal(m, b)
}
func (m *SingleCheckServiceableAreaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleCheckServiceableAreaRequest.Marshal(b, m, deterministic)
}
func (m *SingleCheckServiceableAreaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleCheckServiceableAreaRequest.Merge(m, src)
}
func (m *SingleCheckServiceableAreaRequest) XXX_Size() int {
	return xxx_messageInfo_SingleCheckServiceableAreaRequest.Size(m)
}
func (m *SingleCheckServiceableAreaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleCheckServiceableAreaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SingleCheckServiceableAreaRequest proto.InternalMessageInfo

func (m *SingleCheckServiceableAreaRequest) GetBaseInfo() *CheckServiceableAreaBaseInfo2 {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *SingleCheckServiceableAreaRequest) GetPickupInfo() *LocationInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

func (m *SingleCheckServiceableAreaRequest) GetDeliverInfo() *LocationInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

// --- 批量获取线服务范围的响应结果 ---
type BatchCheckLineServiceableAreaResponse struct {
	RespHeader           *RespHeader                       `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	CheckRespList        []*SingleCheckServiceableResponse `protobuf:"bytes,2,rep,name=check_resp_list,json=checkRespList" json:"check_resp_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *BatchCheckLineServiceableAreaResponse) Reset()         { *m = BatchCheckLineServiceableAreaResponse{} }
func (m *BatchCheckLineServiceableAreaResponse) String() string { return proto.CompactTextString(m) }
func (*BatchCheckLineServiceableAreaResponse) ProtoMessage()    {}
func (*BatchCheckLineServiceableAreaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{9}
}

func (m *BatchCheckLineServiceableAreaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckLineServiceableAreaResponse.Unmarshal(m, b)
}
func (m *BatchCheckLineServiceableAreaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckLineServiceableAreaResponse.Marshal(b, m, deterministic)
}
func (m *BatchCheckLineServiceableAreaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckLineServiceableAreaResponse.Merge(m, src)
}
func (m *BatchCheckLineServiceableAreaResponse) XXX_Size() int {
	return xxx_messageInfo_BatchCheckLineServiceableAreaResponse.Size(m)
}
func (m *BatchCheckLineServiceableAreaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckLineServiceableAreaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckLineServiceableAreaResponse proto.InternalMessageInfo

func (m *BatchCheckLineServiceableAreaResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckLineServiceableAreaResponse) GetCheckRespList() []*SingleCheckServiceableResponse {
	if m != nil {
		return m.CheckRespList
	}
	return nil
}

// --- 批量获取线服务范围的响应结果 ---
type MultipleBatchCheckLineServiceableAreaResponse struct {
	RespHeader           *RespHeader                                       `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	OrderRespMap         map[string]*BatchCheckLineServiceableAreaResponse `protobuf:"bytes,2,rep,name=order_resp_map,json=orderRespMap" json:"order_resp_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *MultipleBatchCheckLineServiceableAreaResponse) Reset() {
	*m = MultipleBatchCheckLineServiceableAreaResponse{}
}
func (m *MultipleBatchCheckLineServiceableAreaResponse) String() string {
	return proto.CompactTextString(m)
}
func (*MultipleBatchCheckLineServiceableAreaResponse) ProtoMessage() {}
func (*MultipleBatchCheckLineServiceableAreaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{10}
}

func (m *MultipleBatchCheckLineServiceableAreaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultipleBatchCheckLineServiceableAreaResponse.Unmarshal(m, b)
}
func (m *MultipleBatchCheckLineServiceableAreaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultipleBatchCheckLineServiceableAreaResponse.Marshal(b, m, deterministic)
}
func (m *MultipleBatchCheckLineServiceableAreaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultipleBatchCheckLineServiceableAreaResponse.Merge(m, src)
}
func (m *MultipleBatchCheckLineServiceableAreaResponse) XXX_Size() int {
	return xxx_messageInfo_MultipleBatchCheckLineServiceableAreaResponse.Size(m)
}
func (m *MultipleBatchCheckLineServiceableAreaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MultipleBatchCheckLineServiceableAreaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MultipleBatchCheckLineServiceableAreaResponse proto.InternalMessageInfo

func (m *MultipleBatchCheckLineServiceableAreaResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *MultipleBatchCheckLineServiceableAreaResponse) GetOrderRespMap() map[string]*BatchCheckLineServiceableAreaResponse {
	if m != nil {
		return m.OrderRespMap
	}
	return nil
}

type SingleCheckServiceableResponse struct {
	LineId               *string  `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	ItemRetCode          *int32   `protobuf:"varint,2,req,name=item_ret_code,json=itemRetCode" json:"item_ret_code,omitempty"`
	ItemRetMsg           *string  `protobuf:"bytes,3,req,name=item_ret_msg,json=itemRetMsg" json:"item_ret_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingleCheckServiceableResponse) Reset()         { *m = SingleCheckServiceableResponse{} }
func (m *SingleCheckServiceableResponse) String() string { return proto.CompactTextString(m) }
func (*SingleCheckServiceableResponse) ProtoMessage()    {}
func (*SingleCheckServiceableResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{11}
}

func (m *SingleCheckServiceableResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleCheckServiceableResponse.Unmarshal(m, b)
}
func (m *SingleCheckServiceableResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleCheckServiceableResponse.Marshal(b, m, deterministic)
}
func (m *SingleCheckServiceableResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleCheckServiceableResponse.Merge(m, src)
}
func (m *SingleCheckServiceableResponse) XXX_Size() int {
	return xxx_messageInfo_SingleCheckServiceableResponse.Size(m)
}
func (m *SingleCheckServiceableResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleCheckServiceableResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SingleCheckServiceableResponse proto.InternalMessageInfo

func (m *SingleCheckServiceableResponse) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *SingleCheckServiceableResponse) GetItemRetCode() int32 {
	if m != nil && m.ItemRetCode != nil {
		return *m.ItemRetCode
	}
	return 0
}

func (m *SingleCheckServiceableResponse) GetItemRetMsg() string {
	if m != nil && m.ItemRetMsg != nil {
		return *m.ItemRetMsg
	}
	return ""
}

type GetServiceableInfoBase struct {
	LineId               *string  `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	IsCheckOperation     *uint32  `protobuf:"varint,2,req,name=is_check_operation,json=isCheckOperation" json:"is_check_operation,omitempty"`
	IsCheckBasic         *uint32  `protobuf:"varint,3,req,name=is_check_basic,json=isCheckBasic" json:"is_check_basic,omitempty"`
	CollectType          *uint32  `protobuf:"varint,4,opt,name=collect_type,json=collectType" json:"collect_type,omitempty"`
	DeliverType          *uint32  `protobuf:"varint,5,opt,name=deliver_type,json=deliverType" json:"deliver_type,omitempty"`
	Scenario             *uint32  `protobuf:"varint,6,req,name=scenario" json:"scenario,omitempty"`
	SkipZoneRoute        *uint32  `protobuf:"varint,7,opt,name=skip_zone_route,json=skipZoneRoute" json:"skip_zone_route,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetServiceableInfoBase) Reset()         { *m = GetServiceableInfoBase{} }
func (m *GetServiceableInfoBase) String() string { return proto.CompactTextString(m) }
func (*GetServiceableInfoBase) ProtoMessage()    {}
func (*GetServiceableInfoBase) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{12}
}

func (m *GetServiceableInfoBase) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetServiceableInfoBase.Unmarshal(m, b)
}
func (m *GetServiceableInfoBase) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetServiceableInfoBase.Marshal(b, m, deterministic)
}
func (m *GetServiceableInfoBase) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetServiceableInfoBase.Merge(m, src)
}
func (m *GetServiceableInfoBase) XXX_Size() int {
	return xxx_messageInfo_GetServiceableInfoBase.Size(m)
}
func (m *GetServiceableInfoBase) XXX_DiscardUnknown() {
	xxx_messageInfo_GetServiceableInfoBase.DiscardUnknown(m)
}

var xxx_messageInfo_GetServiceableInfoBase proto.InternalMessageInfo

func (m *GetServiceableInfoBase) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *GetServiceableInfoBase) GetIsCheckOperation() uint32 {
	if m != nil && m.IsCheckOperation != nil {
		return *m.IsCheckOperation
	}
	return 0
}

func (m *GetServiceableInfoBase) GetIsCheckBasic() uint32 {
	if m != nil && m.IsCheckBasic != nil {
		return *m.IsCheckBasic
	}
	return 0
}

func (m *GetServiceableInfoBase) GetCollectType() uint32 {
	if m != nil && m.CollectType != nil {
		return *m.CollectType
	}
	return 0
}

func (m *GetServiceableInfoBase) GetDeliverType() uint32 {
	if m != nil && m.DeliverType != nil {
		return *m.DeliverType
	}
	return 0
}

func (m *GetServiceableInfoBase) GetScenario() uint32 {
	if m != nil && m.Scenario != nil {
		return *m.Scenario
	}
	return 0
}

func (m *GetServiceableInfoBase) GetSkipZoneRoute() uint32 {
	if m != nil && m.SkipZoneRoute != nil {
		return *m.SkipZoneRoute
	}
	return 0
}

type GetServiceableInfoBase2 struct {
	LineId               *string  `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	IsCheckOperation     *uint32  `protobuf:"varint,2,req,name=is_check_operation,json=isCheckOperation" json:"is_check_operation,omitempty"`
	IsCheckBasic         *uint32  `protobuf:"varint,3,req,name=is_check_basic,json=isCheckBasic" json:"is_check_basic,omitempty"`
	CollectType          *uint32  `protobuf:"varint,4,opt,name=collect_type,json=collectType" json:"collect_type,omitempty"`
	DeliverType          *uint32  `protobuf:"varint,5,opt,name=deliver_type,json=deliverType" json:"deliver_type,omitempty"`
	CheckSender          *uint32  `protobuf:"varint,6,req,name=check_sender,json=checkSender" json:"check_sender,omitempty"`
	SenderCheckLevel     *uint32  `protobuf:"varint,7,opt,name=sender_check_level,json=senderCheckLevel" json:"sender_check_level,omitempty"`
	CheckReceiver        *uint32  `protobuf:"varint,8,req,name=check_receiver,json=checkReceiver" json:"check_receiver,omitempty"`
	ReceiverCheckLevel   *uint32  `protobuf:"varint,9,opt,name=receiver_check_level,json=receiverCheckLevel" json:"receiver_check_level,omitempty"`
	SkipPostcode         *uint32  `protobuf:"varint,10,opt,name=skip_postcode,json=skipPostcode" json:"skip_postcode,omitempty"`
	SkipZoneRoute        *uint32  `protobuf:"varint,11,opt,name=skip_zone_route,json=skipZoneRoute" json:"skip_zone_route,omitempty"`
	SkipElectricFence    *bool    `protobuf:"varint,12,opt,name=skip_electric_fence,json=skipElectricFence" json:"skip_electric_fence,omitempty"`
	UseElectricFence     *bool    `protobuf:"varint,13,opt,name=use_electric_fence,json=useElectricFence" json:"use_electric_fence,omitempty"`
	IsCheckTradeIn       *uint32  `protobuf:"varint,14,opt,name=is_check_trade_in,json=isCheckTradeIn" json:"is_check_trade_in,omitempty"`
	CheckPredefinedRoute *bool    `protobuf:"varint,15,opt,name=check_predefined_route,json=checkPredefinedRoute" json:"check_predefined_route,omitempty"`
	PredefinedRouteCodes []string `protobuf:"bytes,16,rep,name=predefined_route_codes,json=predefinedRouteCodes" json:"predefined_route_codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetServiceableInfoBase2) Reset()         { *m = GetServiceableInfoBase2{} }
func (m *GetServiceableInfoBase2) String() string { return proto.CompactTextString(m) }
func (*GetServiceableInfoBase2) ProtoMessage()    {}
func (*GetServiceableInfoBase2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{13}
}

func (m *GetServiceableInfoBase2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetServiceableInfoBase2.Unmarshal(m, b)
}
func (m *GetServiceableInfoBase2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetServiceableInfoBase2.Marshal(b, m, deterministic)
}
func (m *GetServiceableInfoBase2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetServiceableInfoBase2.Merge(m, src)
}
func (m *GetServiceableInfoBase2) XXX_Size() int {
	return xxx_messageInfo_GetServiceableInfoBase2.Size(m)
}
func (m *GetServiceableInfoBase2) XXX_DiscardUnknown() {
	xxx_messageInfo_GetServiceableInfoBase2.DiscardUnknown(m)
}

var xxx_messageInfo_GetServiceableInfoBase2 proto.InternalMessageInfo

func (m *GetServiceableInfoBase2) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *GetServiceableInfoBase2) GetIsCheckOperation() uint32 {
	if m != nil && m.IsCheckOperation != nil {
		return *m.IsCheckOperation
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetIsCheckBasic() uint32 {
	if m != nil && m.IsCheckBasic != nil {
		return *m.IsCheckBasic
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetCollectType() uint32 {
	if m != nil && m.CollectType != nil {
		return *m.CollectType
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetDeliverType() uint32 {
	if m != nil && m.DeliverType != nil {
		return *m.DeliverType
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetCheckSender() uint32 {
	if m != nil && m.CheckSender != nil {
		return *m.CheckSender
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetSenderCheckLevel() uint32 {
	if m != nil && m.SenderCheckLevel != nil {
		return *m.SenderCheckLevel
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetCheckReceiver() uint32 {
	if m != nil && m.CheckReceiver != nil {
		return *m.CheckReceiver
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetReceiverCheckLevel() uint32 {
	if m != nil && m.ReceiverCheckLevel != nil {
		return *m.ReceiverCheckLevel
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetSkipPostcode() uint32 {
	if m != nil && m.SkipPostcode != nil {
		return *m.SkipPostcode
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetSkipZoneRoute() uint32 {
	if m != nil && m.SkipZoneRoute != nil {
		return *m.SkipZoneRoute
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetSkipElectricFence() bool {
	if m != nil && m.SkipElectricFence != nil {
		return *m.SkipElectricFence
	}
	return false
}

func (m *GetServiceableInfoBase2) GetUseElectricFence() bool {
	if m != nil && m.UseElectricFence != nil {
		return *m.UseElectricFence
	}
	return false
}

func (m *GetServiceableInfoBase2) GetIsCheckTradeIn() uint32 {
	if m != nil && m.IsCheckTradeIn != nil {
		return *m.IsCheckTradeIn
	}
	return 0
}

func (m *GetServiceableInfoBase2) GetCheckPredefinedRoute() bool {
	if m != nil && m.CheckPredefinedRoute != nil {
		return *m.CheckPredefinedRoute
	}
	return false
}

func (m *GetServiceableInfoBase2) GetPredefinedRouteCodes() []string {
	if m != nil {
		return m.PredefinedRouteCodes
	}
	return nil
}

type GetLineServiceableInfoRequest struct {
	ReqHeader            *ReqHeader              `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BaseInfo             *GetServiceableInfoBase `protobuf:"bytes,2,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	PickupInfo           *LocationInfo           `protobuf:"bytes,3,opt,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	DeliverInfo          *LocationInfo           `protobuf:"bytes,4,opt,name=deliver_info,json=deliverInfo" json:"deliver_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetLineServiceableInfoRequest) Reset()         { *m = GetLineServiceableInfoRequest{} }
func (m *GetLineServiceableInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetLineServiceableInfoRequest) ProtoMessage()    {}
func (*GetLineServiceableInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{14}
}

func (m *GetLineServiceableInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLineServiceableInfoRequest.Unmarshal(m, b)
}
func (m *GetLineServiceableInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLineServiceableInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetLineServiceableInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLineServiceableInfoRequest.Merge(m, src)
}
func (m *GetLineServiceableInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetLineServiceableInfoRequest.Size(m)
}
func (m *GetLineServiceableInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLineServiceableInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLineServiceableInfoRequest proto.InternalMessageInfo

func (m *GetLineServiceableInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetLineServiceableInfoRequest) GetBaseInfo() *GetServiceableInfoBase {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *GetLineServiceableInfoRequest) GetPickupInfo() *LocationInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

func (m *GetLineServiceableInfoRequest) GetDeliverInfo() *LocationInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

type GetLineServiceableInfoRequest2 struct {
	ReqHeader            *ReqHeader               `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	BaseInfo             *GetServiceableInfoBase2 `protobuf:"bytes,2,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	PickupInfo           *LocationInfo            `protobuf:"bytes,3,opt,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	DeliverInfo          *LocationInfo            `protobuf:"bytes,4,opt,name=deliver_info,json=deliverInfo" json:"deliver_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetLineServiceableInfoRequest2) Reset()         { *m = GetLineServiceableInfoRequest2{} }
func (m *GetLineServiceableInfoRequest2) String() string { return proto.CompactTextString(m) }
func (*GetLineServiceableInfoRequest2) ProtoMessage()    {}
func (*GetLineServiceableInfoRequest2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{15}
}

func (m *GetLineServiceableInfoRequest2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLineServiceableInfoRequest2.Unmarshal(m, b)
}
func (m *GetLineServiceableInfoRequest2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLineServiceableInfoRequest2.Marshal(b, m, deterministic)
}
func (m *GetLineServiceableInfoRequest2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLineServiceableInfoRequest2.Merge(m, src)
}
func (m *GetLineServiceableInfoRequest2) XXX_Size() int {
	return xxx_messageInfo_GetLineServiceableInfoRequest2.Size(m)
}
func (m *GetLineServiceableInfoRequest2) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLineServiceableInfoRequest2.DiscardUnknown(m)
}

var xxx_messageInfo_GetLineServiceableInfoRequest2 proto.InternalMessageInfo

func (m *GetLineServiceableInfoRequest2) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetLineServiceableInfoRequest2) GetBaseInfo() *GetServiceableInfoBase2 {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *GetLineServiceableInfoRequest2) GetPickupInfo() *LocationInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

func (m *GetLineServiceableInfoRequest2) GetDeliverInfo() *LocationInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

type GetLineServiceableInfoResponse struct {
	RespHeader           *RespHeader          `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ServiceableInfo      *ServiceableAreaInfo `protobuf:"bytes,2,req,name=serviceable_info,json=serviceableInfo" json:"serviceable_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetLineServiceableInfoResponse) Reset()         { *m = GetLineServiceableInfoResponse{} }
func (m *GetLineServiceableInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetLineServiceableInfoResponse) ProtoMessage()    {}
func (*GetLineServiceableInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{16}
}

func (m *GetLineServiceableInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLineServiceableInfoResponse.Unmarshal(m, b)
}
func (m *GetLineServiceableInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLineServiceableInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetLineServiceableInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLineServiceableInfoResponse.Merge(m, src)
}
func (m *GetLineServiceableInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetLineServiceableInfoResponse.Size(m)
}
func (m *GetLineServiceableInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLineServiceableInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLineServiceableInfoResponse proto.InternalMessageInfo

func (m *GetLineServiceableInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetLineServiceableInfoResponse) GetServiceableInfo() *ServiceableAreaInfo {
	if m != nil {
		return m.ServiceableInfo
	}
	return nil
}

type ServiceableAreaInfo struct {
	CanPickup            *uint32  `protobuf:"varint,1,req,name=can_pickup,json=canPickup" json:"can_pickup,omitempty"`
	CanCodPickup         *uint32  `protobuf:"varint,2,req,name=can_cod_pickup,json=canCodPickup" json:"can_cod_pickup,omitempty"`
	CanDeliver           *uint32  `protobuf:"varint,3,req,name=can_deliver,json=canDeliver" json:"can_deliver,omitempty"`
	CanCodDeliver        *uint32  `protobuf:"varint,4,req,name=can_cod_deliver,json=canCodDeliver" json:"can_cod_deliver,omitempty"`
	PickupInEFence       *uint32  `protobuf:"varint,5,opt,name=pickup_in_e_fence,json=pickupInEFence" json:"pickup_in_e_fence,omitempty"`
	DeliverInEFence      *uint32  `protobuf:"varint,6,opt,name=deliver_in_e_fence,json=deliverInEFence" json:"deliver_in_e_fence,omitempty"`
	CanTradeIn           *uint32  `protobuf:"varint,7,opt,name=can_trade_in,json=canTradeIn" json:"can_trade_in,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServiceableAreaInfo) Reset()         { *m = ServiceableAreaInfo{} }
func (m *ServiceableAreaInfo) String() string { return proto.CompactTextString(m) }
func (*ServiceableAreaInfo) ProtoMessage()    {}
func (*ServiceableAreaInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{17}
}

func (m *ServiceableAreaInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceableAreaInfo.Unmarshal(m, b)
}
func (m *ServiceableAreaInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceableAreaInfo.Marshal(b, m, deterministic)
}
func (m *ServiceableAreaInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceableAreaInfo.Merge(m, src)
}
func (m *ServiceableAreaInfo) XXX_Size() int {
	return xxx_messageInfo_ServiceableAreaInfo.Size(m)
}
func (m *ServiceableAreaInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceableAreaInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceableAreaInfo proto.InternalMessageInfo

func (m *ServiceableAreaInfo) GetCanPickup() uint32 {
	if m != nil && m.CanPickup != nil {
		return *m.CanPickup
	}
	return 0
}

func (m *ServiceableAreaInfo) GetCanCodPickup() uint32 {
	if m != nil && m.CanCodPickup != nil {
		return *m.CanCodPickup
	}
	return 0
}

func (m *ServiceableAreaInfo) GetCanDeliver() uint32 {
	if m != nil && m.CanDeliver != nil {
		return *m.CanDeliver
	}
	return 0
}

func (m *ServiceableAreaInfo) GetCanCodDeliver() uint32 {
	if m != nil && m.CanCodDeliver != nil {
		return *m.CanCodDeliver
	}
	return 0
}

func (m *ServiceableAreaInfo) GetPickupInEFence() uint32 {
	if m != nil && m.PickupInEFence != nil {
		return *m.PickupInEFence
	}
	return 0
}

func (m *ServiceableAreaInfo) GetDeliverInEFence() uint32 {
	if m != nil && m.DeliverInEFence != nil {
		return *m.DeliverInEFence
	}
	return 0
}

func (m *ServiceableAreaInfo) GetCanTradeIn() uint32 {
	if m != nil && m.CanTradeIn != nil {
		return *m.CanTradeIn
	}
	return 0
}

type SingleGetServiceableRequest struct {
	BaseInfo             *GetServiceableInfoBase `protobuf:"bytes,1,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	PickupInfo           *LocationInfo           `protobuf:"bytes,2,opt,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	DeliverInfo          *LocationInfo           `protobuf:"bytes,3,opt,name=deliver_info,json=deliverInfo" json:"deliver_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingleGetServiceableRequest) Reset()         { *m = SingleGetServiceableRequest{} }
func (m *SingleGetServiceableRequest) String() string { return proto.CompactTextString(m) }
func (*SingleGetServiceableRequest) ProtoMessage()    {}
func (*SingleGetServiceableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{18}
}

func (m *SingleGetServiceableRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleGetServiceableRequest.Unmarshal(m, b)
}
func (m *SingleGetServiceableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleGetServiceableRequest.Marshal(b, m, deterministic)
}
func (m *SingleGetServiceableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleGetServiceableRequest.Merge(m, src)
}
func (m *SingleGetServiceableRequest) XXX_Size() int {
	return xxx_messageInfo_SingleGetServiceableRequest.Size(m)
}
func (m *SingleGetServiceableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleGetServiceableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SingleGetServiceableRequest proto.InternalMessageInfo

func (m *SingleGetServiceableRequest) GetBaseInfo() *GetServiceableInfoBase {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *SingleGetServiceableRequest) GetPickupInfo() *LocationInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

func (m *SingleGetServiceableRequest) GetDeliverInfo() *LocationInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

type SingleGetServiceableRequest2 struct {
	BaseInfo             *GetServiceableInfoBase2 `protobuf:"bytes,1,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	PickupInfo           *LocationInfo            `protobuf:"bytes,2,opt,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	DeliverInfo          *LocationInfo            `protobuf:"bytes,3,opt,name=deliver_info,json=deliverInfo" json:"deliver_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *SingleGetServiceableRequest2) Reset()         { *m = SingleGetServiceableRequest2{} }
func (m *SingleGetServiceableRequest2) String() string { return proto.CompactTextString(m) }
func (*SingleGetServiceableRequest2) ProtoMessage()    {}
func (*SingleGetServiceableRequest2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{19}
}

func (m *SingleGetServiceableRequest2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleGetServiceableRequest2.Unmarshal(m, b)
}
func (m *SingleGetServiceableRequest2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleGetServiceableRequest2.Marshal(b, m, deterministic)
}
func (m *SingleGetServiceableRequest2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleGetServiceableRequest2.Merge(m, src)
}
func (m *SingleGetServiceableRequest2) XXX_Size() int {
	return xxx_messageInfo_SingleGetServiceableRequest2.Size(m)
}
func (m *SingleGetServiceableRequest2) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleGetServiceableRequest2.DiscardUnknown(m)
}

var xxx_messageInfo_SingleGetServiceableRequest2 proto.InternalMessageInfo

func (m *SingleGetServiceableRequest2) GetBaseInfo() *GetServiceableInfoBase2 {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *SingleGetServiceableRequest2) GetPickupInfo() *LocationInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

func (m *SingleGetServiceableRequest2) GetDeliverInfo() *LocationInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

type SingleGetServiceableResponse struct {
	LineId               *string              `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	ServiceableInfo      *ServiceableAreaInfo `protobuf:"bytes,2,req,name=serviceable_info,json=serviceableInfo" json:"serviceable_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SingleGetServiceableResponse) Reset()         { *m = SingleGetServiceableResponse{} }
func (m *SingleGetServiceableResponse) String() string { return proto.CompactTextString(m) }
func (*SingleGetServiceableResponse) ProtoMessage()    {}
func (*SingleGetServiceableResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{20}
}

func (m *SingleGetServiceableResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleGetServiceableResponse.Unmarshal(m, b)
}
func (m *SingleGetServiceableResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleGetServiceableResponse.Marshal(b, m, deterministic)
}
func (m *SingleGetServiceableResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleGetServiceableResponse.Merge(m, src)
}
func (m *SingleGetServiceableResponse) XXX_Size() int {
	return xxx_messageInfo_SingleGetServiceableResponse.Size(m)
}
func (m *SingleGetServiceableResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleGetServiceableResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SingleGetServiceableResponse proto.InternalMessageInfo

func (m *SingleGetServiceableResponse) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *SingleGetServiceableResponse) GetServiceableInfo() *ServiceableAreaInfo {
	if m != nil {
		return m.ServiceableInfo
	}
	return nil
}

type SingleGetServiceableResponse2 struct {
	LineId               *string              `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	ServiceableInfo      *ServiceableAreaInfo `protobuf:"bytes,2,req,name=serviceable_info,json=serviceableInfo" json:"serviceable_info,omitempty"`
	ItemCode             *int32               `protobuf:"varint,3,opt,name=item_code,json=itemCode" json:"item_code,omitempty"`
	Message              *string              `protobuf:"bytes,4,opt,name=message" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SingleGetServiceableResponse2) Reset()         { *m = SingleGetServiceableResponse2{} }
func (m *SingleGetServiceableResponse2) String() string { return proto.CompactTextString(m) }
func (*SingleGetServiceableResponse2) ProtoMessage()    {}
func (*SingleGetServiceableResponse2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{21}
}

func (m *SingleGetServiceableResponse2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleGetServiceableResponse2.Unmarshal(m, b)
}
func (m *SingleGetServiceableResponse2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleGetServiceableResponse2.Marshal(b, m, deterministic)
}
func (m *SingleGetServiceableResponse2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleGetServiceableResponse2.Merge(m, src)
}
func (m *SingleGetServiceableResponse2) XXX_Size() int {
	return xxx_messageInfo_SingleGetServiceableResponse2.Size(m)
}
func (m *SingleGetServiceableResponse2) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleGetServiceableResponse2.DiscardUnknown(m)
}

var xxx_messageInfo_SingleGetServiceableResponse2 proto.InternalMessageInfo

func (m *SingleGetServiceableResponse2) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *SingleGetServiceableResponse2) GetServiceableInfo() *ServiceableAreaInfo {
	if m != nil {
		return m.ServiceableInfo
	}
	return nil
}

func (m *SingleGetServiceableResponse2) GetItemCode() int32 {
	if m != nil && m.ItemCode != nil {
		return *m.ItemCode
	}
	return 0
}

func (m *SingleGetServiceableResponse2) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

type BatchGetLineServiceableInfoRequest struct {
	ReqHeader            *ReqHeader                     `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ServiceableReqList   []*SingleGetServiceableRequest `protobuf:"bytes,2,rep,name=serviceable_req_list,json=serviceableReqList" json:"serviceable_req_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *BatchGetLineServiceableInfoRequest) Reset()         { *m = BatchGetLineServiceableInfoRequest{} }
func (m *BatchGetLineServiceableInfoRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetLineServiceableInfoRequest) ProtoMessage()    {}
func (*BatchGetLineServiceableInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{22}
}

func (m *BatchGetLineServiceableInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLineServiceableInfoRequest.Unmarshal(m, b)
}
func (m *BatchGetLineServiceableInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLineServiceableInfoRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetLineServiceableInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLineServiceableInfoRequest.Merge(m, src)
}
func (m *BatchGetLineServiceableInfoRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetLineServiceableInfoRequest.Size(m)
}
func (m *BatchGetLineServiceableInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLineServiceableInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLineServiceableInfoRequest proto.InternalMessageInfo

func (m *BatchGetLineServiceableInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetLineServiceableInfoRequest) GetServiceableReqList() []*SingleGetServiceableRequest {
	if m != nil {
		return m.ServiceableReqList
	}
	return nil
}

type BatchGetLineServiceableInfoRequest2 struct {
	ReqHeader            *ReqHeader                      `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ServiceableReqList   []*SingleGetServiceableRequest2 `protobuf:"bytes,2,rep,name=serviceable_req_list,json=serviceableReqList" json:"serviceable_req_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *BatchGetLineServiceableInfoRequest2) Reset()         { *m = BatchGetLineServiceableInfoRequest2{} }
func (m *BatchGetLineServiceableInfoRequest2) String() string { return proto.CompactTextString(m) }
func (*BatchGetLineServiceableInfoRequest2) ProtoMessage()    {}
func (*BatchGetLineServiceableInfoRequest2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{23}
}

func (m *BatchGetLineServiceableInfoRequest2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLineServiceableInfoRequest2.Unmarshal(m, b)
}
func (m *BatchGetLineServiceableInfoRequest2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLineServiceableInfoRequest2.Marshal(b, m, deterministic)
}
func (m *BatchGetLineServiceableInfoRequest2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLineServiceableInfoRequest2.Merge(m, src)
}
func (m *BatchGetLineServiceableInfoRequest2) XXX_Size() int {
	return xxx_messageInfo_BatchGetLineServiceableInfoRequest2.Size(m)
}
func (m *BatchGetLineServiceableInfoRequest2) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLineServiceableInfoRequest2.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLineServiceableInfoRequest2 proto.InternalMessageInfo

func (m *BatchGetLineServiceableInfoRequest2) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetLineServiceableInfoRequest2) GetServiceableReqList() []*SingleGetServiceableRequest2 {
	if m != nil {
		return m.ServiceableReqList
	}
	return nil
}

type BatchGetLineServiceableInfoResponse struct {
	RespHeader           *RespHeader                     `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ServiceableRespList  []*SingleGetServiceableResponse `protobuf:"bytes,2,rep,name=serviceable_resp_list,json=serviceableRespList" json:"serviceable_resp_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *BatchGetLineServiceableInfoResponse) Reset()         { *m = BatchGetLineServiceableInfoResponse{} }
func (m *BatchGetLineServiceableInfoResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetLineServiceableInfoResponse) ProtoMessage()    {}
func (*BatchGetLineServiceableInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{24}
}

func (m *BatchGetLineServiceableInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLineServiceableInfoResponse.Unmarshal(m, b)
}
func (m *BatchGetLineServiceableInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLineServiceableInfoResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetLineServiceableInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLineServiceableInfoResponse.Merge(m, src)
}
func (m *BatchGetLineServiceableInfoResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetLineServiceableInfoResponse.Size(m)
}
func (m *BatchGetLineServiceableInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLineServiceableInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLineServiceableInfoResponse proto.InternalMessageInfo

func (m *BatchGetLineServiceableInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetLineServiceableInfoResponse) GetServiceableRespList() []*SingleGetServiceableResponse {
	if m != nil {
		return m.ServiceableRespList
	}
	return nil
}

type BatchGetLineServiceableInfoResponse2 struct {
	RespHeader           *RespHeader                      `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	ServiceableRespList  []*SingleGetServiceableResponse2 `protobuf:"bytes,2,rep,name=serviceable_resp_list,json=serviceableRespList" json:"serviceable_resp_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *BatchGetLineServiceableInfoResponse2) Reset()         { *m = BatchGetLineServiceableInfoResponse2{} }
func (m *BatchGetLineServiceableInfoResponse2) String() string { return proto.CompactTextString(m) }
func (*BatchGetLineServiceableInfoResponse2) ProtoMessage()    {}
func (*BatchGetLineServiceableInfoResponse2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{25}
}

func (m *BatchGetLineServiceableInfoResponse2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLineServiceableInfoResponse2.Unmarshal(m, b)
}
func (m *BatchGetLineServiceableInfoResponse2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLineServiceableInfoResponse2.Marshal(b, m, deterministic)
}
func (m *BatchGetLineServiceableInfoResponse2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLineServiceableInfoResponse2.Merge(m, src)
}
func (m *BatchGetLineServiceableInfoResponse2) XXX_Size() int {
	return xxx_messageInfo_BatchGetLineServiceableInfoResponse2.Size(m)
}
func (m *BatchGetLineServiceableInfoResponse2) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLineServiceableInfoResponse2.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLineServiceableInfoResponse2 proto.InternalMessageInfo

func (m *BatchGetLineServiceableInfoResponse2) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetLineServiceableInfoResponse2) GetServiceableRespList() []*SingleGetServiceableResponse2 {
	if m != nil {
		return m.ServiceableRespList
	}
	return nil
}

type GetLineCollectDeliverAbilityRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineId               *string    `protobuf:"bytes,2,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetLineCollectDeliverAbilityRequest) Reset()         { *m = GetLineCollectDeliverAbilityRequest{} }
func (m *GetLineCollectDeliverAbilityRequest) String() string { return proto.CompactTextString(m) }
func (*GetLineCollectDeliverAbilityRequest) ProtoMessage()    {}
func (*GetLineCollectDeliverAbilityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{26}
}

func (m *GetLineCollectDeliverAbilityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLineCollectDeliverAbilityRequest.Unmarshal(m, b)
}
func (m *GetLineCollectDeliverAbilityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLineCollectDeliverAbilityRequest.Marshal(b, m, deterministic)
}
func (m *GetLineCollectDeliverAbilityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLineCollectDeliverAbilityRequest.Merge(m, src)
}
func (m *GetLineCollectDeliverAbilityRequest) XXX_Size() int {
	return xxx_messageInfo_GetLineCollectDeliverAbilityRequest.Size(m)
}
func (m *GetLineCollectDeliverAbilityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLineCollectDeliverAbilityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLineCollectDeliverAbilityRequest proto.InternalMessageInfo

func (m *GetLineCollectDeliverAbilityRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *GetLineCollectDeliverAbilityRequest) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

type GetLineCollectDeliverAbilityResponse struct {
	RespHeader           *RespHeader  `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	AbilityInfo          *AbilityInfo `protobuf:"bytes,2,opt,name=ability_info,json=abilityInfo" json:"ability_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetLineCollectDeliverAbilityResponse) Reset()         { *m = GetLineCollectDeliverAbilityResponse{} }
func (m *GetLineCollectDeliverAbilityResponse) String() string { return proto.CompactTextString(m) }
func (*GetLineCollectDeliverAbilityResponse) ProtoMessage()    {}
func (*GetLineCollectDeliverAbilityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{27}
}

func (m *GetLineCollectDeliverAbilityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLineCollectDeliverAbilityResponse.Unmarshal(m, b)
}
func (m *GetLineCollectDeliverAbilityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLineCollectDeliverAbilityResponse.Marshal(b, m, deterministic)
}
func (m *GetLineCollectDeliverAbilityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLineCollectDeliverAbilityResponse.Merge(m, src)
}
func (m *GetLineCollectDeliverAbilityResponse) XXX_Size() int {
	return xxx_messageInfo_GetLineCollectDeliverAbilityResponse.Size(m)
}
func (m *GetLineCollectDeliverAbilityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLineCollectDeliverAbilityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLineCollectDeliverAbilityResponse proto.InternalMessageInfo

func (m *GetLineCollectDeliverAbilityResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *GetLineCollectDeliverAbilityResponse) GetAbilityInfo() *AbilityInfo {
	if m != nil {
		return m.AbilityInfo
	}
	return nil
}

type BatchGetLineCollectDeliverAbilityRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineId               []string   `protobuf:"bytes,2,rep,name=line_id,json=lineId" json:"line_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetLineCollectDeliverAbilityRequest) Reset() {
	*m = BatchGetLineCollectDeliverAbilityRequest{}
}
func (m *BatchGetLineCollectDeliverAbilityRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetLineCollectDeliverAbilityRequest) ProtoMessage()    {}
func (*BatchGetLineCollectDeliverAbilityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{28}
}

func (m *BatchGetLineCollectDeliverAbilityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLineCollectDeliverAbilityRequest.Unmarshal(m, b)
}
func (m *BatchGetLineCollectDeliverAbilityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLineCollectDeliverAbilityRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetLineCollectDeliverAbilityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLineCollectDeliverAbilityRequest.Merge(m, src)
}
func (m *BatchGetLineCollectDeliverAbilityRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetLineCollectDeliverAbilityRequest.Size(m)
}
func (m *BatchGetLineCollectDeliverAbilityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLineCollectDeliverAbilityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLineCollectDeliverAbilityRequest proto.InternalMessageInfo

func (m *BatchGetLineCollectDeliverAbilityRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetLineCollectDeliverAbilityRequest) GetLineId() []string {
	if m != nil {
		return m.LineId
	}
	return nil
}

type BatchGetLineCollectDeliverAbilityResponse struct {
	RespHeader           *RespHeader    `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	AbilityInfoList      []*AbilityInfo `protobuf:"bytes,2,rep,name=ability_info_list,json=abilityInfoList" json:"ability_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchGetLineCollectDeliverAbilityResponse) Reset() {
	*m = BatchGetLineCollectDeliverAbilityResponse{}
}
func (m *BatchGetLineCollectDeliverAbilityResponse) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetLineCollectDeliverAbilityResponse) ProtoMessage() {}
func (*BatchGetLineCollectDeliverAbilityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{29}
}

func (m *BatchGetLineCollectDeliverAbilityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLineCollectDeliverAbilityResponse.Unmarshal(m, b)
}
func (m *BatchGetLineCollectDeliverAbilityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLineCollectDeliverAbilityResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetLineCollectDeliverAbilityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLineCollectDeliverAbilityResponse.Merge(m, src)
}
func (m *BatchGetLineCollectDeliverAbilityResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetLineCollectDeliverAbilityResponse.Size(m)
}
func (m *BatchGetLineCollectDeliverAbilityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLineCollectDeliverAbilityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLineCollectDeliverAbilityResponse proto.InternalMessageInfo

func (m *BatchGetLineCollectDeliverAbilityResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetLineCollectDeliverAbilityResponse) GetAbilityInfoList() []*AbilityInfo {
	if m != nil {
		return m.AbilityInfoList
	}
	return nil
}

type AbilityInfo struct {
	LineId                    *string      `protobuf:"bytes,1,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	CollectType               *CollectType `protobuf:"bytes,2,req,name=collect_type,json=collectType" json:"collect_type,omitempty"`
	DeliverType               *DeliverType `protobuf:"bytes,3,req,name=deliver_type,json=deliverType" json:"deliver_type,omitempty"`
	MaxCapacity               *MaxCapacity `protobuf:"bytes,4,req,name=max_capacity,json=maxCapacity" json:"max_capacity,omitempty"`
	DistanceLimitationChecked *uint32      `protobuf:"varint,5,req,name=distance_limitation_checked,json=distanceLimitationChecked" json:"distance_limitation_checked,omitempty"`
	MinimumDistanceOperator   *uint32      `protobuf:"varint,6,req,name=minimum_distance_operator,json=minimumDistanceOperator" json:"minimum_distance_operator,omitempty"`
	MinimumDistance           *uint32      `protobuf:"varint,7,req,name=minimum_distance,json=minimumDistance" json:"minimum_distance,omitempty"`
	MaximumDistanceOperator   *uint32      `protobuf:"varint,8,req,name=maximum_distance_operator,json=maximumDistanceOperator" json:"maximum_distance_operator,omitempty"`
	MaximumDistance           *uint32      `protobuf:"varint,9,req,name=maximum_distance,json=maximumDistance" json:"maximum_distance,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}     `json:"-"`
	XXX_unrecognized          []byte       `json:"-"`
	XXX_sizecache             int32        `json:"-"`
}

func (m *AbilityInfo) Reset()         { *m = AbilityInfo{} }
func (m *AbilityInfo) String() string { return proto.CompactTextString(m) }
func (*AbilityInfo) ProtoMessage()    {}
func (*AbilityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{30}
}

func (m *AbilityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AbilityInfo.Unmarshal(m, b)
}
func (m *AbilityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AbilityInfo.Marshal(b, m, deterministic)
}
func (m *AbilityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AbilityInfo.Merge(m, src)
}
func (m *AbilityInfo) XXX_Size() int {
	return xxx_messageInfo_AbilityInfo.Size(m)
}
func (m *AbilityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AbilityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AbilityInfo proto.InternalMessageInfo

func (m *AbilityInfo) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *AbilityInfo) GetCollectType() *CollectType {
	if m != nil {
		return m.CollectType
	}
	return nil
}

func (m *AbilityInfo) GetDeliverType() *DeliverType {
	if m != nil {
		return m.DeliverType
	}
	return nil
}

func (m *AbilityInfo) GetMaxCapacity() *MaxCapacity {
	if m != nil {
		return m.MaxCapacity
	}
	return nil
}

func (m *AbilityInfo) GetDistanceLimitationChecked() uint32 {
	if m != nil && m.DistanceLimitationChecked != nil {
		return *m.DistanceLimitationChecked
	}
	return 0
}

func (m *AbilityInfo) GetMinimumDistanceOperator() uint32 {
	if m != nil && m.MinimumDistanceOperator != nil {
		return *m.MinimumDistanceOperator
	}
	return 0
}

func (m *AbilityInfo) GetMinimumDistance() uint32 {
	if m != nil && m.MinimumDistance != nil {
		return *m.MinimumDistance
	}
	return 0
}

func (m *AbilityInfo) GetMaximumDistanceOperator() uint32 {
	if m != nil && m.MaximumDistanceOperator != nil {
		return *m.MaximumDistanceOperator
	}
	return 0
}

func (m *AbilityInfo) GetMaximumDistance() uint32 {
	if m != nil && m.MaximumDistance != nil {
		return *m.MaximumDistance
	}
	return 0
}

type CollectType struct {
	Pickup               *uint32  `protobuf:"varint,1,req,name=pickup" json:"pickup,omitempty"`
	Dropoff              *uint32  `protobuf:"varint,2,req,name=dropoff" json:"dropoff,omitempty"`
	B2C                  *uint32  `protobuf:"varint,3,req,name=b2c" json:"b2c,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CollectType) Reset()         { *m = CollectType{} }
func (m *CollectType) String() string { return proto.CompactTextString(m) }
func (*CollectType) ProtoMessage()    {}
func (*CollectType) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{31}
}

func (m *CollectType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CollectType.Unmarshal(m, b)
}
func (m *CollectType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CollectType.Marshal(b, m, deterministic)
}
func (m *CollectType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CollectType.Merge(m, src)
}
func (m *CollectType) XXX_Size() int {
	return xxx_messageInfo_CollectType.Size(m)
}
func (m *CollectType) XXX_DiscardUnknown() {
	xxx_messageInfo_CollectType.DiscardUnknown(m)
}

var xxx_messageInfo_CollectType proto.InternalMessageInfo

func (m *CollectType) GetPickup() uint32 {
	if m != nil && m.Pickup != nil {
		return *m.Pickup
	}
	return 0
}

func (m *CollectType) GetDropoff() uint32 {
	if m != nil && m.Dropoff != nil {
		return *m.Dropoff
	}
	return 0
}

func (m *CollectType) GetB2C() uint32 {
	if m != nil && m.B2C != nil {
		return *m.B2C
	}
	return 0
}

type DeliverType struct {
	ToHome               *uint32  `protobuf:"varint,1,req,name=to_home,json=toHome" json:"to_home,omitempty"`
	ToSite               *uint32  `protobuf:"varint,2,req,name=to_site,json=toSite" json:"to_site,omitempty"`
	ToWms                *uint32  `protobuf:"varint,3,opt,name=to_wms,json=toWms" json:"to_wms,omitempty"`
	To_3Pl               *uint32  `protobuf:"varint,4,opt,name=to_3pl,json=to3pl" json:"to_3pl,omitempty"`
	ToBranch             *uint32  `protobuf:"varint,5,opt,name=to_branch,json=toBranch" json:"to_branch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeliverType) Reset()         { *m = DeliverType{} }
func (m *DeliverType) String() string { return proto.CompactTextString(m) }
func (*DeliverType) ProtoMessage()    {}
func (*DeliverType) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{32}
}

func (m *DeliverType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeliverType.Unmarshal(m, b)
}
func (m *DeliverType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeliverType.Marshal(b, m, deterministic)
}
func (m *DeliverType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeliverType.Merge(m, src)
}
func (m *DeliverType) XXX_Size() int {
	return xxx_messageInfo_DeliverType.Size(m)
}
func (m *DeliverType) XXX_DiscardUnknown() {
	xxx_messageInfo_DeliverType.DiscardUnknown(m)
}

var xxx_messageInfo_DeliverType proto.InternalMessageInfo

func (m *DeliverType) GetToHome() uint32 {
	if m != nil && m.ToHome != nil {
		return *m.ToHome
	}
	return 0
}

func (m *DeliverType) GetToSite() uint32 {
	if m != nil && m.ToSite != nil {
		return *m.ToSite
	}
	return 0
}

func (m *DeliverType) GetToWms() uint32 {
	if m != nil && m.ToWms != nil {
		return *m.ToWms
	}
	return 0
}

func (m *DeliverType) GetTo_3Pl() uint32 {
	if m != nil && m.To_3Pl != nil {
		return *m.To_3Pl
	}
	return 0
}

func (m *DeliverType) GetToBranch() uint32 {
	if m != nil && m.ToBranch != nil {
		return *m.ToBranch
	}
	return 0
}

type MaxCapacity struct {
	Pickup               *uint32  `protobuf:"varint,1,req,name=pickup" json:"pickup,omitempty"`
	Dropoff              *uint32  `protobuf:"varint,2,req,name=dropoff" json:"dropoff,omitempty"`
	B2C                  *uint32  `protobuf:"varint,3,req,name=b2c" json:"b2c,omitempty"`
	ToHome               *uint32  `protobuf:"varint,4,req,name=to_home,json=toHome" json:"to_home,omitempty"`
	ToSite               *uint32  `protobuf:"varint,5,req,name=to_site,json=toSite" json:"to_site,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MaxCapacity) Reset()         { *m = MaxCapacity{} }
func (m *MaxCapacity) String() string { return proto.CompactTextString(m) }
func (*MaxCapacity) ProtoMessage()    {}
func (*MaxCapacity) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{33}
}

func (m *MaxCapacity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaxCapacity.Unmarshal(m, b)
}
func (m *MaxCapacity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaxCapacity.Marshal(b, m, deterministic)
}
func (m *MaxCapacity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaxCapacity.Merge(m, src)
}
func (m *MaxCapacity) XXX_Size() int {
	return xxx_messageInfo_MaxCapacity.Size(m)
}
func (m *MaxCapacity) XXX_DiscardUnknown() {
	xxx_messageInfo_MaxCapacity.DiscardUnknown(m)
}

var xxx_messageInfo_MaxCapacity proto.InternalMessageInfo

func (m *MaxCapacity) GetPickup() uint32 {
	if m != nil && m.Pickup != nil {
		return *m.Pickup
	}
	return 0
}

func (m *MaxCapacity) GetDropoff() uint32 {
	if m != nil && m.Dropoff != nil {
		return *m.Dropoff
	}
	return 0
}

func (m *MaxCapacity) GetB2C() uint32 {
	if m != nil && m.B2C != nil {
		return *m.B2C
	}
	return 0
}

func (m *MaxCapacity) GetToHome() uint32 {
	if m != nil && m.ToHome != nil {
		return *m.ToHome
	}
	return 0
}

func (m *MaxCapacity) GetToSite() uint32 {
	if m != nil && m.ToSite != nil {
		return *m.ToSite
	}
	return 0
}

type CheckFmServiceableAreaRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LineId               *string    `protobuf:"bytes,2,req,name=line_id,json=lineId" json:"line_id,omitempty"`
	CollectType          *uint32    `protobuf:"varint,3,req,name=collect_type,json=collectType" json:"collect_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CheckFmServiceableAreaRequest) Reset()         { *m = CheckFmServiceableAreaRequest{} }
func (m *CheckFmServiceableAreaRequest) String() string { return proto.CompactTextString(m) }
func (*CheckFmServiceableAreaRequest) ProtoMessage()    {}
func (*CheckFmServiceableAreaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{34}
}

func (m *CheckFmServiceableAreaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFmServiceableAreaRequest.Unmarshal(m, b)
}
func (m *CheckFmServiceableAreaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFmServiceableAreaRequest.Marshal(b, m, deterministic)
}
func (m *CheckFmServiceableAreaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFmServiceableAreaRequest.Merge(m, src)
}
func (m *CheckFmServiceableAreaRequest) XXX_Size() int {
	return xxx_messageInfo_CheckFmServiceableAreaRequest.Size(m)
}
func (m *CheckFmServiceableAreaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFmServiceableAreaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFmServiceableAreaRequest proto.InternalMessageInfo

func (m *CheckFmServiceableAreaRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *CheckFmServiceableAreaRequest) GetLineId() string {
	if m != nil && m.LineId != nil {
		return *m.LineId
	}
	return ""
}

func (m *CheckFmServiceableAreaRequest) GetCollectType() uint32 {
	if m != nil && m.CollectType != nil {
		return *m.CollectType
	}
	return 0
}

type CheckFmServiceableAreaResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CheckFmServiceableAreaResponse) Reset()         { *m = CheckFmServiceableAreaResponse{} }
func (m *CheckFmServiceableAreaResponse) String() string { return proto.CompactTextString(m) }
func (*CheckFmServiceableAreaResponse) ProtoMessage()    {}
func (*CheckFmServiceableAreaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{35}
}

func (m *CheckFmServiceableAreaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFmServiceableAreaResponse.Unmarshal(m, b)
}
func (m *CheckFmServiceableAreaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFmServiceableAreaResponse.Marshal(b, m, deterministic)
}
func (m *CheckFmServiceableAreaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFmServiceableAreaResponse.Merge(m, src)
}
func (m *CheckFmServiceableAreaResponse) XXX_Size() int {
	return xxx_messageInfo_CheckFmServiceableAreaResponse.Size(m)
}
func (m *CheckFmServiceableAreaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFmServiceableAreaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFmServiceableAreaResponse proto.InternalMessageInfo

func (m *CheckFmServiceableAreaResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

// --- 多批量获取线服务范围请求体 ---
type BatchCheckLineServiceableAreaRequest2 struct {
	ReqHeader            *ReqHeader                   `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	OrderReqList         []*CheckLinesServiceableArea `protobuf:"bytes,2,rep,name=order_req_list,json=orderReqList" json:"order_req_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchCheckLineServiceableAreaRequest2) Reset()         { *m = BatchCheckLineServiceableAreaRequest2{} }
func (m *BatchCheckLineServiceableAreaRequest2) String() string { return proto.CompactTextString(m) }
func (*BatchCheckLineServiceableAreaRequest2) ProtoMessage()    {}
func (*BatchCheckLineServiceableAreaRequest2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{36}
}

func (m *BatchCheckLineServiceableAreaRequest2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckLineServiceableAreaRequest2.Unmarshal(m, b)
}
func (m *BatchCheckLineServiceableAreaRequest2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckLineServiceableAreaRequest2.Marshal(b, m, deterministic)
}
func (m *BatchCheckLineServiceableAreaRequest2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckLineServiceableAreaRequest2.Merge(m, src)
}
func (m *BatchCheckLineServiceableAreaRequest2) XXX_Size() int {
	return xxx_messageInfo_BatchCheckLineServiceableAreaRequest2.Size(m)
}
func (m *BatchCheckLineServiceableAreaRequest2) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckLineServiceableAreaRequest2.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckLineServiceableAreaRequest2 proto.InternalMessageInfo

func (m *BatchCheckLineServiceableAreaRequest2) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchCheckLineServiceableAreaRequest2) GetOrderReqList() []*CheckLinesServiceableArea {
	if m != nil {
		return m.OrderReqList
	}
	return nil
}

// --- 校验线服务范围请求体 ---
type CheckLinesServiceableArea struct {
	BaseInfo             []*CheckServiceableAreaBaseInfo2 `protobuf:"bytes,1,rep,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	PickupInfo           *LocationInfo                    `protobuf:"bytes,2,opt,name=pickup_info,json=pickupInfo" json:"pickup_info,omitempty"`
	DeliverInfo          *LocationInfo                    `protobuf:"bytes,3,opt,name=deliver_info,json=deliverInfo" json:"deliver_info,omitempty"`
	ReqNo                *string                          `protobuf:"bytes,4,req,name=req_no,json=reqNo" json:"req_no,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *CheckLinesServiceableArea) Reset()         { *m = CheckLinesServiceableArea{} }
func (m *CheckLinesServiceableArea) String() string { return proto.CompactTextString(m) }
func (*CheckLinesServiceableArea) ProtoMessage()    {}
func (*CheckLinesServiceableArea) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{37}
}

func (m *CheckLinesServiceableArea) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLinesServiceableArea.Unmarshal(m, b)
}
func (m *CheckLinesServiceableArea) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLinesServiceableArea.Marshal(b, m, deterministic)
}
func (m *CheckLinesServiceableArea) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLinesServiceableArea.Merge(m, src)
}
func (m *CheckLinesServiceableArea) XXX_Size() int {
	return xxx_messageInfo_CheckLinesServiceableArea.Size(m)
}
func (m *CheckLinesServiceableArea) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLinesServiceableArea.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLinesServiceableArea proto.InternalMessageInfo

func (m *CheckLinesServiceableArea) GetBaseInfo() []*CheckServiceableAreaBaseInfo2 {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *CheckLinesServiceableArea) GetPickupInfo() *LocationInfo {
	if m != nil {
		return m.PickupInfo
	}
	return nil
}

func (m *CheckLinesServiceableArea) GetDeliverInfo() *LocationInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

func (m *CheckLinesServiceableArea) GetReqNo() string {
	if m != nil && m.ReqNo != nil {
		return *m.ReqNo
	}
	return ""
}

// --- 批量获取线服务范围的响应结果 ---
type BatchCheckLineServiceableAreaResponse2 struct {
	RespHeader           *RespHeader                                       `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	OrderRespMap         map[string]*BatchCheckLineServiceableAreaResponse `protobuf:"bytes,2,rep,name=order_resp_map,json=orderRespMap" json:"order_resp_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *BatchCheckLineServiceableAreaResponse2) Reset() {
	*m = BatchCheckLineServiceableAreaResponse2{}
}
func (m *BatchCheckLineServiceableAreaResponse2) String() string { return proto.CompactTextString(m) }
func (*BatchCheckLineServiceableAreaResponse2) ProtoMessage()    {}
func (*BatchCheckLineServiceableAreaResponse2) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{38}
}

func (m *BatchCheckLineServiceableAreaResponse2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckLineServiceableAreaResponse2.Unmarshal(m, b)
}
func (m *BatchCheckLineServiceableAreaResponse2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckLineServiceableAreaResponse2.Marshal(b, m, deterministic)
}
func (m *BatchCheckLineServiceableAreaResponse2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckLineServiceableAreaResponse2.Merge(m, src)
}
func (m *BatchCheckLineServiceableAreaResponse2) XXX_Size() int {
	return xxx_messageInfo_BatchCheckLineServiceableAreaResponse2.Size(m)
}
func (m *BatchCheckLineServiceableAreaResponse2) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckLineServiceableAreaResponse2.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckLineServiceableAreaResponse2 proto.InternalMessageInfo

func (m *BatchCheckLineServiceableAreaResponse2) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchCheckLineServiceableAreaResponse2) GetOrderRespMap() map[string]*BatchCheckLineServiceableAreaResponse {
	if m != nil {
		return m.OrderRespMap
	}
	return nil
}

type BatchGetLaneServiceableRuleRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	LaneCode             []string   `protobuf:"bytes,2,rep,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetLaneServiceableRuleRequest) Reset()         { *m = BatchGetLaneServiceableRuleRequest{} }
func (m *BatchGetLaneServiceableRuleRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetLaneServiceableRuleRequest) ProtoMessage()    {}
func (*BatchGetLaneServiceableRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{39}
}

func (m *BatchGetLaneServiceableRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLaneServiceableRuleRequest.Unmarshal(m, b)
}
func (m *BatchGetLaneServiceableRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLaneServiceableRuleRequest.Marshal(b, m, deterministic)
}
func (m *BatchGetLaneServiceableRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLaneServiceableRuleRequest.Merge(m, src)
}
func (m *BatchGetLaneServiceableRuleRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetLaneServiceableRuleRequest.Size(m)
}
func (m *BatchGetLaneServiceableRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLaneServiceableRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLaneServiceableRuleRequest proto.InternalMessageInfo

func (m *BatchGetLaneServiceableRuleRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *BatchGetLaneServiceableRuleRequest) GetLaneCode() []string {
	if m != nil {
		return m.LaneCode
	}
	return nil
}

type BatchGetLaneServiceableRuleResponse struct {
	RespHeader           *RespHeader        `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	LaneServiceableRule  []*ServiceableRule `protobuf:"bytes,2,rep,name=lane_serviceable_rule,json=laneServiceableRule" json:"lane_serviceable_rule,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetLaneServiceableRuleResponse) Reset()         { *m = BatchGetLaneServiceableRuleResponse{} }
func (m *BatchGetLaneServiceableRuleResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetLaneServiceableRuleResponse) ProtoMessage()    {}
func (*BatchGetLaneServiceableRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{40}
}

func (m *BatchGetLaneServiceableRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLaneServiceableRuleResponse.Unmarshal(m, b)
}
func (m *BatchGetLaneServiceableRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLaneServiceableRuleResponse.Marshal(b, m, deterministic)
}
func (m *BatchGetLaneServiceableRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLaneServiceableRuleResponse.Merge(m, src)
}
func (m *BatchGetLaneServiceableRuleResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetLaneServiceableRuleResponse.Size(m)
}
func (m *BatchGetLaneServiceableRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLaneServiceableRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLaneServiceableRuleResponse proto.InternalMessageInfo

func (m *BatchGetLaneServiceableRuleResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func (m *BatchGetLaneServiceableRuleResponse) GetLaneServiceableRule() []*ServiceableRule {
	if m != nil {
		return m.LaneServiceableRule
	}
	return nil
}

type ServiceableRule struct {
	LaneCode             *string       `protobuf:"bytes,1,req,name=lane_code,json=laneCode" json:"lane_code,omitempty"`
	RuleDetail           []*RuleDetail `protobuf:"bytes,2,rep,name=rule_detail,json=ruleDetail" json:"rule_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ServiceableRule) Reset()         { *m = ServiceableRule{} }
func (m *ServiceableRule) String() string { return proto.CompactTextString(m) }
func (*ServiceableRule) ProtoMessage()    {}
func (*ServiceableRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{41}
}

func (m *ServiceableRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceableRule.Unmarshal(m, b)
}
func (m *ServiceableRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceableRule.Marshal(b, m, deterministic)
}
func (m *ServiceableRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceableRule.Merge(m, src)
}
func (m *ServiceableRule) XXX_Size() int {
	return xxx_messageInfo_ServiceableRule.Size(m)
}
func (m *ServiceableRule) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceableRule.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceableRule proto.InternalMessageInfo

func (m *ServiceableRule) GetLaneCode() string {
	if m != nil && m.LaneCode != nil {
		return *m.LaneCode
	}
	return ""
}

func (m *ServiceableRule) GetRuleDetail() []*RuleDetail {
	if m != nil {
		return m.RuleDetail
	}
	return nil
}

type RuleDetail struct {
	Sequence                      *int32   `protobuf:"varint,1,req,name=sequence" json:"sequence,omitempty"`
	ResourceType                  *int32   `protobuf:"varint,2,req,name=resource_type,json=resourceType" json:"resource_type,omitempty"`
	ResourceSubType               *int32   `protobuf:"varint,3,req,name=resource_sub_type,json=resourceSubType" json:"resource_sub_type,omitempty"`
	SenderUseLps                  *int32   `protobuf:"varint,4,opt,name=sender_use_lps,json=senderUseLps" json:"sender_use_lps,omitempty"`
	ReceiverUseLps                *int32   `protobuf:"varint,5,opt,name=receiver_use_lps,json=receiverUseLps" json:"receiver_use_lps,omitempty"`
	CheckSender                   *int32   `protobuf:"varint,6,opt,name=check_sender,json=checkSender" json:"check_sender,omitempty"`
	CheckReceiver                 *int32   `protobuf:"varint,7,opt,name=check_receiver,json=checkReceiver" json:"check_receiver,omitempty"`
	PreResourceSaCheckFlag        *int32   `protobuf:"varint,8,opt,name=pre_resource_sa_check_flag,json=preResourceSaCheckFlag" json:"pre_resource_sa_check_flag,omitempty"`
	NextResourceSaCheckFlag       *int32   `protobuf:"varint,9,opt,name=next_resource_sa_check_flag,json=nextResourceSaCheckFlag" json:"next_resource_sa_check_flag,omitempty"`
	SiteSortingFlag               *int32   `protobuf:"varint,10,opt,name=site_sorting_flag,json=siteSortingFlag" json:"site_sorting_flag,omitempty"`
	SortingAsPreDeliverFlag       *int32   `protobuf:"varint,11,opt,name=sorting_as_pre_deliver_flag,json=sortingAsPreDeliverFlag" json:"sorting_as_pre_deliver_flag,omitempty"`
	SiteSortingPickupAddressFlag  *int32   `protobuf:"varint,12,opt,name=site_sorting_pickup_address_flag,json=siteSortingPickupAddressFlag" json:"site_sorting_pickup_address_flag,omitempty"`
	SortingAsNextPickupFlag       *int32   `protobuf:"varint,13,opt,name=sorting_as_next_pickup_flag,json=sortingAsNextPickupFlag" json:"sorting_as_next_pickup_flag,omitempty"`
	SiteSortingDeliverAddressFlag *int32   `protobuf:"varint,14,opt,name=site_sorting_deliver_address_flag,json=siteSortingDeliverAddressFlag" json:"site_sorting_deliver_address_flag,omitempty"`
	CheckSenderAddrType           *int32   `protobuf:"varint,15,opt,name=check_sender_addr_type,json=checkSenderAddrType" json:"check_sender_addr_type,omitempty"`
	CheckReceiverAddrType         *int32   `protobuf:"varint,16,opt,name=check_receiver_addr_type,json=checkReceiverAddrType" json:"check_receiver_addr_type,omitempty"`
	XXX_NoUnkeyedLiteral          struct{} `json:"-"`
	XXX_unrecognized              []byte   `json:"-"`
	XXX_sizecache                 int32    `json:"-"`
}

func (m *RuleDetail) Reset()         { *m = RuleDetail{} }
func (m *RuleDetail) String() string { return proto.CompactTextString(m) }
func (*RuleDetail) ProtoMessage()    {}
func (*RuleDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{42}
}

func (m *RuleDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RuleDetail.Unmarshal(m, b)
}
func (m *RuleDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RuleDetail.Marshal(b, m, deterministic)
}
func (m *RuleDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuleDetail.Merge(m, src)
}
func (m *RuleDetail) XXX_Size() int {
	return xxx_messageInfo_RuleDetail.Size(m)
}
func (m *RuleDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_RuleDetail.DiscardUnknown(m)
}

var xxx_messageInfo_RuleDetail proto.InternalMessageInfo

func (m *RuleDetail) GetSequence() int32 {
	if m != nil && m.Sequence != nil {
		return *m.Sequence
	}
	return 0
}

func (m *RuleDetail) GetResourceType() int32 {
	if m != nil && m.ResourceType != nil {
		return *m.ResourceType
	}
	return 0
}

func (m *RuleDetail) GetResourceSubType() int32 {
	if m != nil && m.ResourceSubType != nil {
		return *m.ResourceSubType
	}
	return 0
}

func (m *RuleDetail) GetSenderUseLps() int32 {
	if m != nil && m.SenderUseLps != nil {
		return *m.SenderUseLps
	}
	return 0
}

func (m *RuleDetail) GetReceiverUseLps() int32 {
	if m != nil && m.ReceiverUseLps != nil {
		return *m.ReceiverUseLps
	}
	return 0
}

func (m *RuleDetail) GetCheckSender() int32 {
	if m != nil && m.CheckSender != nil {
		return *m.CheckSender
	}
	return 0
}

func (m *RuleDetail) GetCheckReceiver() int32 {
	if m != nil && m.CheckReceiver != nil {
		return *m.CheckReceiver
	}
	return 0
}

func (m *RuleDetail) GetPreResourceSaCheckFlag() int32 {
	if m != nil && m.PreResourceSaCheckFlag != nil {
		return *m.PreResourceSaCheckFlag
	}
	return 0
}

func (m *RuleDetail) GetNextResourceSaCheckFlag() int32 {
	if m != nil && m.NextResourceSaCheckFlag != nil {
		return *m.NextResourceSaCheckFlag
	}
	return 0
}

func (m *RuleDetail) GetSiteSortingFlag() int32 {
	if m != nil && m.SiteSortingFlag != nil {
		return *m.SiteSortingFlag
	}
	return 0
}

func (m *RuleDetail) GetSortingAsPreDeliverFlag() int32 {
	if m != nil && m.SortingAsPreDeliverFlag != nil {
		return *m.SortingAsPreDeliverFlag
	}
	return 0
}

func (m *RuleDetail) GetSiteSortingPickupAddressFlag() int32 {
	if m != nil && m.SiteSortingPickupAddressFlag != nil {
		return *m.SiteSortingPickupAddressFlag
	}
	return 0
}

func (m *RuleDetail) GetSortingAsNextPickupFlag() int32 {
	if m != nil && m.SortingAsNextPickupFlag != nil {
		return *m.SortingAsNextPickupFlag
	}
	return 0
}

func (m *RuleDetail) GetSiteSortingDeliverAddressFlag() int32 {
	if m != nil && m.SiteSortingDeliverAddressFlag != nil {
		return *m.SiteSortingDeliverAddressFlag
	}
	return 0
}

func (m *RuleDetail) GetCheckSenderAddrType() int32 {
	if m != nil && m.CheckSenderAddrType != nil {
		return *m.CheckSenderAddrType
	}
	return 0
}

func (m *RuleDetail) GetCheckReceiverAddrType() int32 {
	if m != nil && m.CheckReceiverAddrType != nil {
		return *m.CheckReceiverAddrType
	}
	return 0
}

type NotifyZoneInfoRequest struct {
	ReqHeader            *ReqHeader  `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ZoneList             []*ZoneInfo `protobuf:"bytes,2,rep,name=zone_list,json=zoneList" json:"zone_list,omitempty"`
	Mode                 *int32      `protobuf:"varint,3,req,name=mode" json:"mode,omitempty"`
	ForceHandle          *int32      `protobuf:"varint,4,opt,name=force_handle,json=forceHandle" json:"force_handle,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *NotifyZoneInfoRequest) Reset()         { *m = NotifyZoneInfoRequest{} }
func (m *NotifyZoneInfoRequest) String() string { return proto.CompactTextString(m) }
func (*NotifyZoneInfoRequest) ProtoMessage()    {}
func (*NotifyZoneInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{43}
}

func (m *NotifyZoneInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyZoneInfoRequest.Unmarshal(m, b)
}
func (m *NotifyZoneInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyZoneInfoRequest.Marshal(b, m, deterministic)
}
func (m *NotifyZoneInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyZoneInfoRequest.Merge(m, src)
}
func (m *NotifyZoneInfoRequest) XXX_Size() int {
	return xxx_messageInfo_NotifyZoneInfoRequest.Size(m)
}
func (m *NotifyZoneInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyZoneInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyZoneInfoRequest proto.InternalMessageInfo

func (m *NotifyZoneInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *NotifyZoneInfoRequest) GetZoneList() []*ZoneInfo {
	if m != nil {
		return m.ZoneList
	}
	return nil
}

func (m *NotifyZoneInfoRequest) GetMode() int32 {
	if m != nil && m.Mode != nil {
		return *m.Mode
	}
	return 0
}

func (m *NotifyZoneInfoRequest) GetForceHandle() int32 {
	if m != nil && m.ForceHandle != nil {
		return *m.ForceHandle
	}
	return 0
}

type NotifyZoneInfoResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *NotifyZoneInfoResponse) Reset()         { *m = NotifyZoneInfoResponse{} }
func (m *NotifyZoneInfoResponse) String() string { return proto.CompactTextString(m) }
func (*NotifyZoneInfoResponse) ProtoMessage()    {}
func (*NotifyZoneInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{44}
}

func (m *NotifyZoneInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyZoneInfoResponse.Unmarshal(m, b)
}
func (m *NotifyZoneInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyZoneInfoResponse.Marshal(b, m, deterministic)
}
func (m *NotifyZoneInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyZoneInfoResponse.Merge(m, src)
}
func (m *NotifyZoneInfoResponse) XXX_Size() int {
	return xxx_messageInfo_NotifyZoneInfoResponse.Size(m)
}
func (m *NotifyZoneInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyZoneInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyZoneInfoResponse proto.InternalMessageInfo

func (m *NotifyZoneInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

type ZoneInfo struct {
	VersionId            *int64   `protobuf:"varint,1,req,name=version_id,json=versionId" json:"version_id,omitempty"`
	ZoneStatus           *int32   `protobuf:"varint,2,req,name=zone_status,json=zoneStatus" json:"zone_status,omitempty"`
	Operator             *string  `protobuf:"bytes,3,opt,name=operator" json:"operator,omitempty"`
	ZoneId               *string  `protobuf:"bytes,4,opt,name=zone_id,json=zoneId" json:"zone_id,omitempty"`
	ZoneName             *string  `protobuf:"bytes,5,opt,name=zone_name,json=zoneName" json:"zone_name,omitempty"`
	StationId            *string  `protobuf:"bytes,6,opt,name=station_id,json=stationId" json:"station_id,omitempty"`
	StationType          *int32   `protobuf:"varint,7,opt,name=station_type,json=stationType" json:"station_type,omitempty"`
	StationName          *string  `protobuf:"bytes,8,opt,name=station_name,json=stationName" json:"station_name,omitempty"`
	Geometry             *string  `protobuf:"bytes,9,opt,name=geometry" json:"geometry,omitempty"`
	ZoneArea             *float64 `protobuf:"fixed64,10,opt,name=zone_area,json=zoneArea" json:"zone_area,omitempty"`
	LayerId              *string  `protobuf:"bytes,11,opt,name=layer_id,json=layerId" json:"layer_id,omitempty"`
	LayerName            *string  `protobuf:"bytes,12,opt,name=layer_name,json=layerName" json:"layer_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ZoneInfo) Reset()         { *m = ZoneInfo{} }
func (m *ZoneInfo) String() string { return proto.CompactTextString(m) }
func (*ZoneInfo) ProtoMessage()    {}
func (*ZoneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{45}
}

func (m *ZoneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZoneInfo.Unmarshal(m, b)
}
func (m *ZoneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZoneInfo.Marshal(b, m, deterministic)
}
func (m *ZoneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZoneInfo.Merge(m, src)
}
func (m *ZoneInfo) XXX_Size() int {
	return xxx_messageInfo_ZoneInfo.Size(m)
}
func (m *ZoneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ZoneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ZoneInfo proto.InternalMessageInfo

func (m *ZoneInfo) GetVersionId() int64 {
	if m != nil && m.VersionId != nil {
		return *m.VersionId
	}
	return 0
}

func (m *ZoneInfo) GetZoneStatus() int32 {
	if m != nil && m.ZoneStatus != nil {
		return *m.ZoneStatus
	}
	return 0
}

func (m *ZoneInfo) GetOperator() string {
	if m != nil && m.Operator != nil {
		return *m.Operator
	}
	return ""
}

func (m *ZoneInfo) GetZoneId() string {
	if m != nil && m.ZoneId != nil {
		return *m.ZoneId
	}
	return ""
}

func (m *ZoneInfo) GetZoneName() string {
	if m != nil && m.ZoneName != nil {
		return *m.ZoneName
	}
	return ""
}

func (m *ZoneInfo) GetStationId() string {
	if m != nil && m.StationId != nil {
		return *m.StationId
	}
	return ""
}

func (m *ZoneInfo) GetStationType() int32 {
	if m != nil && m.StationType != nil {
		return *m.StationType
	}
	return 0
}

func (m *ZoneInfo) GetStationName() string {
	if m != nil && m.StationName != nil {
		return *m.StationName
	}
	return ""
}

func (m *ZoneInfo) GetGeometry() string {
	if m != nil && m.Geometry != nil {
		return *m.Geometry
	}
	return ""
}

func (m *ZoneInfo) GetZoneArea() float64 {
	if m != nil && m.ZoneArea != nil {
		return *m.ZoneArea
	}
	return 0
}

func (m *ZoneInfo) GetLayerId() string {
	if m != nil && m.LayerId != nil {
		return *m.LayerId
	}
	return ""
}

func (m *ZoneInfo) GetLayerName() string {
	if m != nil && m.LayerName != nil {
		return *m.LayerName
	}
	return ""
}

// 电子围栏polygon数据刷新
type RefreshZoneInfoRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	VersionId            *string    `protobuf:"bytes,2,req,name=version_id,json=versionId" json:"version_id,omitempty"`
	OriVersionId         *string    `protobuf:"bytes,3,req,name=ori_version_id,json=oriVersionId" json:"ori_version_id,omitempty"`
	ZoneId               *string    `protobuf:"bytes,4,opt,name=zone_id,json=zoneId" json:"zone_id,omitempty"`
	Operator             *string    `protobuf:"bytes,5,opt,name=operator" json:"operator,omitempty"`
	ZoneName             *string    `protobuf:"bytes,6,opt,name=zone_name,json=zoneName" json:"zone_name,omitempty"`
	StationId            *string    `protobuf:"bytes,7,opt,name=station_id,json=stationId" json:"station_id,omitempty"`
	StationType          *int32     `protobuf:"varint,8,opt,name=station_type,json=stationType" json:"station_type,omitempty"`
	StationName          *string    `protobuf:"bytes,9,opt,name=station_name,json=stationName" json:"station_name,omitempty"`
	Geometry             *string    `protobuf:"bytes,10,opt,name=geometry" json:"geometry,omitempty"`
	ZoneArea             *float64   `protobuf:"fixed64,11,opt,name=zone_area,json=zoneArea" json:"zone_area,omitempty"`
	LayerId              *string    `protobuf:"bytes,12,opt,name=layer_id,json=layerId" json:"layer_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *RefreshZoneInfoRequest) Reset()         { *m = RefreshZoneInfoRequest{} }
func (m *RefreshZoneInfoRequest) String() string { return proto.CompactTextString(m) }
func (*RefreshZoneInfoRequest) ProtoMessage()    {}
func (*RefreshZoneInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{46}
}

func (m *RefreshZoneInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshZoneInfoRequest.Unmarshal(m, b)
}
func (m *RefreshZoneInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshZoneInfoRequest.Marshal(b, m, deterministic)
}
func (m *RefreshZoneInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshZoneInfoRequest.Merge(m, src)
}
func (m *RefreshZoneInfoRequest) XXX_Size() int {
	return xxx_messageInfo_RefreshZoneInfoRequest.Size(m)
}
func (m *RefreshZoneInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshZoneInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshZoneInfoRequest proto.InternalMessageInfo

func (m *RefreshZoneInfoRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *RefreshZoneInfoRequest) GetVersionId() string {
	if m != nil && m.VersionId != nil {
		return *m.VersionId
	}
	return ""
}

func (m *RefreshZoneInfoRequest) GetOriVersionId() string {
	if m != nil && m.OriVersionId != nil {
		return *m.OriVersionId
	}
	return ""
}

func (m *RefreshZoneInfoRequest) GetZoneId() string {
	if m != nil && m.ZoneId != nil {
		return *m.ZoneId
	}
	return ""
}

func (m *RefreshZoneInfoRequest) GetOperator() string {
	if m != nil && m.Operator != nil {
		return *m.Operator
	}
	return ""
}

func (m *RefreshZoneInfoRequest) GetZoneName() string {
	if m != nil && m.ZoneName != nil {
		return *m.ZoneName
	}
	return ""
}

func (m *RefreshZoneInfoRequest) GetStationId() string {
	if m != nil && m.StationId != nil {
		return *m.StationId
	}
	return ""
}

func (m *RefreshZoneInfoRequest) GetStationType() int32 {
	if m != nil && m.StationType != nil {
		return *m.StationType
	}
	return 0
}

func (m *RefreshZoneInfoRequest) GetStationName() string {
	if m != nil && m.StationName != nil {
		return *m.StationName
	}
	return ""
}

func (m *RefreshZoneInfoRequest) GetGeometry() string {
	if m != nil && m.Geometry != nil {
		return *m.Geometry
	}
	return ""
}

func (m *RefreshZoneInfoRequest) GetZoneArea() float64 {
	if m != nil && m.ZoneArea != nil {
		return *m.ZoneArea
	}
	return 0
}

func (m *RefreshZoneInfoRequest) GetLayerId() string {
	if m != nil && m.LayerId != nil {
		return *m.LayerId
	}
	return ""
}

type RefreshZoneInfoResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *RefreshZoneInfoResponse) Reset()         { *m = RefreshZoneInfoResponse{} }
func (m *RefreshZoneInfoResponse) String() string { return proto.CompactTextString(m) }
func (*RefreshZoneInfoResponse) ProtoMessage()    {}
func (*RefreshZoneInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_de016ae38234b7d0, []int{47}
}

func (m *RefreshZoneInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshZoneInfoResponse.Unmarshal(m, b)
}
func (m *RefreshZoneInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshZoneInfoResponse.Marshal(b, m, deterministic)
}
func (m *RefreshZoneInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshZoneInfoResponse.Merge(m, src)
}
func (m *RefreshZoneInfoResponse) XXX_Size() int {
	return xxx_messageInfo_RefreshZoneInfoResponse.Size(m)
}
func (m *RefreshZoneInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshZoneInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshZoneInfoResponse proto.InternalMessageInfo

func (m *RefreshZoneInfoResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func init() {
	proto.RegisterType((*CheckLineServiceableAreaRequest)(nil), "lcos_protobuf.CheckLineServiceableAreaRequest")
	proto.RegisterType((*CheckLineServiceableAreaRequest2)(nil), "lcos_protobuf.CheckLineServiceableAreaRequest2")
	proto.RegisterType((*CheckLineServiceableAreaResponse)(nil), "lcos_protobuf.CheckLineServiceableAreaResponse")
	proto.RegisterType((*CheckServiceableAreaBaseInfo)(nil), "lcos_protobuf.CheckServiceableAreaBaseInfo")
	proto.RegisterType((*CheckServiceableAreaBaseInfo2)(nil), "lcos_protobuf.CheckServiceableAreaBaseInfo2")
	proto.RegisterType((*BatchCheckLineServiceableAreaRequest)(nil), "lcos_protobuf.BatchCheckLineServiceableAreaRequest")
	proto.RegisterType((*MultipleBatchCheckLineServiceableAreaRequest)(nil), "lcos_protobuf.MultipleBatchCheckLineServiceableAreaRequest")
	proto.RegisterType((*OrderBatchCheckLineServiceableAreaRequest)(nil), "lcos_protobuf.OrderBatchCheckLineServiceableAreaRequest")
	proto.RegisterType((*SingleCheckServiceableAreaRequest)(nil), "lcos_protobuf.SingleCheckServiceableAreaRequest")
	proto.RegisterType((*BatchCheckLineServiceableAreaResponse)(nil), "lcos_protobuf.BatchCheckLineServiceableAreaResponse")
	proto.RegisterType((*MultipleBatchCheckLineServiceableAreaResponse)(nil), "lcos_protobuf.MultipleBatchCheckLineServiceableAreaResponse")
	proto.RegisterMapType((map[string]*BatchCheckLineServiceableAreaResponse)(nil), "lcos_protobuf.MultipleBatchCheckLineServiceableAreaResponse.OrderRespMapEntry")
	proto.RegisterType((*SingleCheckServiceableResponse)(nil), "lcos_protobuf.SingleCheckServiceableResponse")
	proto.RegisterType((*GetServiceableInfoBase)(nil), "lcos_protobuf.GetServiceableInfoBase")
	proto.RegisterType((*GetServiceableInfoBase2)(nil), "lcos_protobuf.GetServiceableInfoBase2")
	proto.RegisterType((*GetLineServiceableInfoRequest)(nil), "lcos_protobuf.GetLineServiceableInfoRequest")
	proto.RegisterType((*GetLineServiceableInfoRequest2)(nil), "lcos_protobuf.GetLineServiceableInfoRequest2")
	proto.RegisterType((*GetLineServiceableInfoResponse)(nil), "lcos_protobuf.GetLineServiceableInfoResponse")
	proto.RegisterType((*ServiceableAreaInfo)(nil), "lcos_protobuf.ServiceableAreaInfo")
	proto.RegisterType((*SingleGetServiceableRequest)(nil), "lcos_protobuf.SingleGetServiceableRequest")
	proto.RegisterType((*SingleGetServiceableRequest2)(nil), "lcos_protobuf.SingleGetServiceableRequest2")
	proto.RegisterType((*SingleGetServiceableResponse)(nil), "lcos_protobuf.SingleGetServiceableResponse")
	proto.RegisterType((*SingleGetServiceableResponse2)(nil), "lcos_protobuf.SingleGetServiceableResponse2")
	proto.RegisterType((*BatchGetLineServiceableInfoRequest)(nil), "lcos_protobuf.BatchGetLineServiceableInfoRequest")
	proto.RegisterType((*BatchGetLineServiceableInfoRequest2)(nil), "lcos_protobuf.BatchGetLineServiceableInfoRequest2")
	proto.RegisterType((*BatchGetLineServiceableInfoResponse)(nil), "lcos_protobuf.BatchGetLineServiceableInfoResponse")
	proto.RegisterType((*BatchGetLineServiceableInfoResponse2)(nil), "lcos_protobuf.BatchGetLineServiceableInfoResponse2")
	proto.RegisterType((*GetLineCollectDeliverAbilityRequest)(nil), "lcos_protobuf.GetLineCollectDeliverAbilityRequest")
	proto.RegisterType((*GetLineCollectDeliverAbilityResponse)(nil), "lcos_protobuf.GetLineCollectDeliverAbilityResponse")
	proto.RegisterType((*BatchGetLineCollectDeliverAbilityRequest)(nil), "lcos_protobuf.BatchGetLineCollectDeliverAbilityRequest")
	proto.RegisterType((*BatchGetLineCollectDeliverAbilityResponse)(nil), "lcos_protobuf.BatchGetLineCollectDeliverAbilityResponse")
	proto.RegisterType((*AbilityInfo)(nil), "lcos_protobuf.AbilityInfo")
	proto.RegisterType((*CollectType)(nil), "lcos_protobuf.CollectType")
	proto.RegisterType((*DeliverType)(nil), "lcos_protobuf.DeliverType")
	proto.RegisterType((*MaxCapacity)(nil), "lcos_protobuf.MaxCapacity")
	proto.RegisterType((*CheckFmServiceableAreaRequest)(nil), "lcos_protobuf.CheckFmServiceableAreaRequest")
	proto.RegisterType((*CheckFmServiceableAreaResponse)(nil), "lcos_protobuf.CheckFmServiceableAreaResponse")
	proto.RegisterType((*BatchCheckLineServiceableAreaRequest2)(nil), "lcos_protobuf.BatchCheckLineServiceableAreaRequest2")
	proto.RegisterType((*CheckLinesServiceableArea)(nil), "lcos_protobuf.CheckLinesServiceableArea")
	proto.RegisterType((*BatchCheckLineServiceableAreaResponse2)(nil), "lcos_protobuf.BatchCheckLineServiceableAreaResponse2")
	proto.RegisterMapType((map[string]*BatchCheckLineServiceableAreaResponse)(nil), "lcos_protobuf.BatchCheckLineServiceableAreaResponse2.OrderRespMapEntry")
	proto.RegisterType((*BatchGetLaneServiceableRuleRequest)(nil), "lcos_protobuf.BatchGetLaneServiceableRuleRequest")
	proto.RegisterType((*BatchGetLaneServiceableRuleResponse)(nil), "lcos_protobuf.BatchGetLaneServiceableRuleResponse")
	proto.RegisterType((*ServiceableRule)(nil), "lcos_protobuf.ServiceableRule")
	proto.RegisterType((*RuleDetail)(nil), "lcos_protobuf.RuleDetail")
	proto.RegisterType((*NotifyZoneInfoRequest)(nil), "lcos_protobuf.NotifyZoneInfoRequest")
	proto.RegisterType((*NotifyZoneInfoResponse)(nil), "lcos_protobuf.NotifyZoneInfoResponse")
	proto.RegisterType((*ZoneInfo)(nil), "lcos_protobuf.ZoneInfo")
	proto.RegisterType((*RefreshZoneInfoRequest)(nil), "lcos_protobuf.RefreshZoneInfoRequest")
	proto.RegisterType((*RefreshZoneInfoResponse)(nil), "lcos_protobuf.RefreshZoneInfoResponse")
}

func init() {
	proto.RegisterFile("lcos_serviceable.proto", fileDescriptor_de016ae38234b7d0)
}

var fileDescriptor_de016ae38234b7d0 = []byte{
	// 2894 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x1a, 0xcf, 0x8f, 0xdc, 0x56,
	0x39, 0x9e, 0xc9, 0xec, 0xce, 0x7c, 0x33, 0xb3, 0xb3, 0xfb, 0x92, 0x6c, 0x26, 0xbb, 0xd9, 0x74,
	0xe3, 0xfc, 0xd0, 0xa6, 0x4d, 0xd2, 0x32, 0x09, 0x6a, 0x15, 0x9a, 0x4a, 0xc9, 0xa6, 0x69, 0x16,
	0x25, 0x69, 0x70, 0xda, 0x22, 0x55, 0xa5, 0xc6, 0xeb, 0x79, 0xbb, 0x6b, 0xe2, 0xb1, 0x1d, 0x3f,
	0x4f, 0x9a, 0xad, 0x40, 0x3d, 0x20, 0x55, 0x02, 0x21, 0xf5, 0xc2, 0x01, 0x55, 0x70, 0x28, 0xea,
	0x8d, 0x03, 0x5c, 0x2a, 0x50, 0xa1, 0x1c, 0x10, 0x12, 0x12, 0xff, 0x00, 0xdc, 0x10, 0x77, 0x2e,
	0x48, 0xdc, 0xb8, 0xa0, 0xf7, 0xbd, 0x67, 0x8f, 0xed, 0xb1, 0x3d, 0x33, 0xbb, 0xd3, 0x28, 0xe5,
	0xe6, 0xf7, 0xbd, 0xef, 0xf7, 0xf7, 0x7e, 0x7c, 0xdf, 0xf7, 0x0c, 0x8b, 0xb6, 0xe9, 0x32, 0x9d,
	0x51, 0xff, 0x91, 0x65, 0x52, 0x63, 0xd3, 0xa6, 0x17, 0x3d, 0xdf, 0x0d, 0x5c, 0xd2, 0x44, 0x38,
	0x7e, 0x6f, 0xf6, 0xb7, 0x96, 0x5a, 0x38, 0xdc, 0x34, 0x98, 0x9c, 0x57, 0x7f, 0x59, 0x82, 0x67,
	0xd6, 0x77, 0xa8, 0xf9, 0xe0, 0xb6, 0xe5, 0xd0, 0xfb, 0x03, 0xf2, 0x6b, 0x3e, 0x35, 0x34, 0xfa,
	0xb0, 0x4f, 0x59, 0x40, 0x5e, 0x04, 0xf0, 0xe9, 0x43, 0x7d, 0x87, 0x1a, 0x5d, 0xea, 0xb7, 0x95,
	0xd5, 0xd2, 0x5a, 0xbd, 0xd3, 0xbe, 0x98, 0x60, 0x7c, 0x51, 0xa3, 0x0f, 0x6f, 0xe1, 0xbc, 0x56,
	0xf3, 0xc3, 0x4f, 0x72, 0x0b, 0x6a, 0x5c, 0x94, 0x6e, 0x39, 0x5b, 0x6e, 0xbb, 0x84, 0x74, 0xcf,
	0xa5, 0xe8, 0x50, 0x76, 0x4a, 0xee, 0x75, 0x83, 0xd1, 0x0d, 0x67, 0xcb, 0xd5, 0xaa, 0x9b, 0xf2,
	0x8b, 0xbc, 0x0c, 0x75, 0xcf, 0x32, 0x1f, 0xf4, 0x3d, 0xc1, 0xab, 0xbc, 0xaa, 0xac, 0xd5, 0x3b,
	0xcb, 0x29, 0x5e, 0xb7, 0x5d, 0xd3, 0x08, 0x2c, 0xd7, 0x41, 0x5a, 0x10, 0xf8, 0x48, 0xfd, 0x0a,
	0x34, 0xba, 0xd4, 0xb6, 0x1e, 0x51, 0x5f, 0x90, 0x1f, 0x1c, 0x4d, 0x5e, 0x97, 0x04, 0x7c, 0xa0,
	0x7e, 0x5a, 0x82, 0xd5, 0x11, 0x4e, 0xea, 0xec, 0xdd, 0x4b, 0x1b, 0xc3, 0x5e, 0x3a, 0x3f, 0x81,
	0x97, 0x3a, 0x4f, 0x8d, 0x9b, 0xde, 0x2d, 0xf2, 0x12, 0xf3, 0x5c, 0x87, 0x51, 0x72, 0x05, 0xea,
	0x3e, 0x65, 0x5e, 0xd2, 0x4d, 0xc7, 0x86, 0xdc, 0xc4, 0x3c, 0xe9, 0x27, 0xf0, 0xa3, 0x6f, 0xf5,
	0x0f, 0x25, 0x38, 0x5e, 0xe4, 0x09, 0x72, 0x14, 0x66, 0x6d, 0xcb, 0xa1, 0xba, 0xd5, 0x45, 0xc6,
	0x35, 0x6d, 0x86, 0x0f, 0x37, 0xba, 0xe4, 0x3c, 0x10, 0x8b, 0xe9, 0x26, 0xa7, 0xd5, 0x5d, 0x8f,
	0xfa, 0x68, 0x00, 0xfa, 0xba, 0xa9, 0xcd, 0x5b, 0x0c, 0x99, 0xbe, 0x1e, 0xc2, 0xc9, 0x69, 0x98,
	0x8b, 0xb0, 0x37, 0x0d, 0x66, 0x99, 0xed, 0x32, 0x62, 0x36, 0x24, 0xe6, 0x75, 0x0e, 0x23, 0x67,
	0x60, 0xce, 0x33, 0x76, 0x7b, 0xd4, 0x09, 0xf4, 0x1e, 0x0d, 0x76, 0xdc, 0x6e, 0xfb, 0x20, 0x62,
	0x35, 0x25, 0xf4, 0x0e, 0x02, 0xc9, 0x49, 0x68, 0x98, 0xae, 0x6d, 0x53, 0x33, 0xd0, 0x83, 0x5d,
	0x8f, 0xb6, 0x2b, 0xab, 0xca, 0x5a, 0x53, 0xab, 0x4b, 0xd8, 0x1b, 0xbb, 0x1e, 0xe5, 0x28, 0xa1,
	0xdf, 0x11, 0x65, 0x46, 0xa0, 0x48, 0x18, 0xa2, 0x2c, 0x41, 0x95, 0x99, 0xd4, 0x31, 0x7c, 0xcb,
	0x6d, 0xcf, 0xa2, 0x98, 0x68, 0x4c, 0xce, 0x42, 0x8b, 0x3d, 0xb0, 0x3c, 0xfd, 0x7d, 0xd7, 0xa1,
	0xba, 0xef, 0xf6, 0x03, 0xda, 0xae, 0x22, 0x87, 0x26, 0x07, 0xbf, 0xed, 0x3a, 0x54, 0xe3, 0x40,
	0xf5, 0xf3, 0x0a, 0xac, 0x14, 0x2e, 0xa4, 0xff, 0x63, 0xff, 0x71, 0x2e, 0xa8, 0x0f, 0xa3, 0x0e,
	0x5f, 0x77, 0xc2, 0x87, 0x75, 0x53, 0xb8, 0x83, 0x83, 0xb8, 0x8d, 0x62, 0x52, 0x6a, 0x6e, 0xd3,
	0x47, 0xd4, 0x96, 0x9e, 0x9c, 0x17, 0x33, 0x62, 0x75, 0x73, 0x38, 0xd7, 0x5e, 0xa0, 0xf9, 0xd4,
	0xa4, 0x5c, 0x4c, 0xbb, 0x26, 0xb4, 0x47, 0xa8, 0x26, 0x81, 0xe4, 0x05, 0x38, 0x1c, 0x22, 0x24,
	0xd8, 0x02, 0xb2, 0x25, 0xe1, 0x5c, 0x8c, 0xf1, 0x29, 0xc0, 0xb0, 0xe9, 0x9e, 0xcb, 0x02, 0xd3,
	0xed, 0xd2, 0x76, 0x1d, 0x51, 0x1b, 0x1c, 0x78, 0x4f, 0xc2, 0xb2, 0x42, 0xde, 0xc8, 0x08, 0x39,
	0xb9, 0x08, 0x87, 0x10, 0x8f, 0x72, 0x5f, 0xf9, 0x96, 0xa9, 0x6f, 0x51, 0xc7, 0xa4, 0xed, 0xe6,
	0xaa, 0xb2, 0x56, 0xd5, 0x16, 0xf8, 0xd4, 0xab, 0x72, 0xe6, 0x26, 0x9f, 0xe0, 0x3e, 0xe8, 0x33,
	0x9a, 0x46, 0x9f, 0x43, 0xf4, 0xf9, 0x3e, 0xa3, 0x49, 0xec, 0xcb, 0xb0, 0x28, 0x6c, 0xf2, 0x7c,
	0xda, 0xa5, 0x5b, 0x96, 0x43, 0xbb, 0x52, 0x99, 0x16, 0x52, 0x1c, 0xc6, 0xd9, 0x7b, 0xd1, 0xa4,
	0xd0, 0xe9, 0x32, 0x2c, 0xa6, 0xf1, 0x75, 0x6e, 0x14, 0x6b, 0xcf, 0xaf, 0x96, 0xd7, 0x6a, 0xda,
	0x61, 0x2f, 0x49, 0xb0, 0xce, 0xe7, 0xd4, 0xdf, 0x2a, 0x70, 0xfa, 0xba, 0x11, 0x98, 0x3b, 0x5f,
	0xda, 0x65, 0xf5, 0xd6, 0x20, 0xa2, 0x0f, 0x75, 0xdb, 0x62, 0x41, 0xbb, 0xb4, 0x5a, 0x5e, 0xab,
	0x77, 0x5e, 0x48, 0x11, 0xdf, 0xb7, 0x9c, 0x6d, 0x9b, 0x66, 0x6d, 0x24, 0xa9, 0x82, 0xd6, 0x90,
	0x6b, 0xe0, 0xe1, 0x6d, 0x8b, 0x05, 0xea, 0x5f, 0x14, 0x38, 0x7f, 0xa7, 0x6f, 0x07, 0x96, 0x67,
	0xd3, 0x2f, 0xd7, 0x82, 0x77, 0x61, 0xce, 0xf5, 0xf9, 0x02, 0x4e, 0x59, 0xf0, 0x52, 0x8a, 0xf8,
	0x75, 0x8e, 0x34, 0x8e, 0x2a, 0x5a, 0x03, 0xf9, 0x85, 0x96, 0x7c, 0xac, 0xc0, 0xb9, 0xb1, 0x69,
	0x33, 0xfc, 0xa9, 0x4c, 0xc3, 0x9f, 0xe4, 0x08, 0xcc, 0x70, 0x8e, 0x8e, 0xb8, 0x2b, 0x6b, 0x5a,
	0xc5, 0xa7, 0x0f, 0xef, 0xba, 0xea, 0xbf, 0x14, 0x38, 0x39, 0x92, 0x55, 0xf2, 0xae, 0x55, 0xa6,
	0x79, 0xd7, 0x96, 0xf6, 0x77, 0xd7, 0x96, 0x27, 0xbc, 0x6b, 0x3f, 0x57, 0xe0, 0xcc, 0x88, 0x30,
	0xec, 0xff, 0xc6, 0x25, 0x6f, 0x42, 0x2b, 0x8c, 0x21, 0xf3, 0xe2, 0x4b, 0xea, 0xc2, 0x58, 0x41,
	0x0c, 0x75, 0x88, 0x4e, 0x45, 0xe6, 0xe1, 0x42, 0xfa, 0x7b, 0x09, 0x2e, 0x8c, 0xb9, 0x25, 0xa6,
	0x60, 0x44, 0x30, 0xd8, 0x16, 0xcc, 0xd3, 0x7b, 0x86, 0x27, 0x6d, 0xb8, 0x9b, 0x22, 0x9f, 0x48,
	0x23, 0xb1, 0x89, 0xf8, 0xe8, 0x8e, 0xe1, 0xbd, 0xea, 0x04, 0xfe, 0x6e, 0xb4, 0x59, 0x10, 0xb4,
	0xd4, 0x87, 0x85, 0x21, 0x14, 0x32, 0x0f, 0xe5, 0x07, 0x74, 0xb7, 0xad, 0xac, 0x2a, 0x6b, 0x35,
	0x8d, 0x7f, 0x92, 0x6f, 0x42, 0xe5, 0x91, 0x61, 0xf7, 0xa9, 0x5c, 0x3f, 0x97, 0x53, 0x3a, 0x8d,
	0xa5, 0x8b, 0x26, 0x58, 0x5c, 0x29, 0xbd, 0xa4, 0xa8, 0x1f, 0xc0, 0x89, 0xe2, 0x58, 0xe4, 0x5f,
	0xf2, 0x2a, 0x34, 0xad, 0x80, 0xf6, 0x74, 0x9f, 0x06, 0x78, 0x20, 0xe3, 0xfe, 0xaa, 0x68, 0x75,
	0x0e, 0xd4, 0x68, 0xc0, 0xcf, 0x61, 0xb2, 0x0a, 0x8d, 0x08, 0xa7, 0xc7, 0xb6, 0xf1, 0x62, 0xaf,
	0x69, 0x20, 0x51, 0xee, 0xb0, 0x6d, 0xf5, 0xa3, 0x12, 0x2c, 0xbe, 0x46, 0x83, 0x98, 0x64, 0xbe,
	0x5e, 0xf9, 0x06, 0x7a, 0xb2, 0xe9, 0x45, 0x3a, 0x6f, 0x38, 0x38, 0x3a, 0x6f, 0xa8, 0x14, 0xe7,
	0x5d, 0x33, 0xa3, 0xf3, 0xae, 0xd9, 0xac, 0xbc, 0xeb, 0x77, 0x15, 0x38, 0x9a, 0xed, 0x91, 0xce,
	0x57, 0xd1, 0x25, 0xe9, 0x54, 0x6a, 0x66, 0xdc, 0x54, 0x6a, 0x76, 0xec, 0x54, 0xaa, 0x3a, 0x49,
	0x2a, 0x55, 0x1b, 0x3f, 0x95, 0x82, 0xf1, 0x52, 0xa9, 0xfa, 0x04, 0xa9, 0x54, 0x63, 0xb2, 0x54,
	0xaa, 0x99, 0x93, 0x4a, 0x9d, 0x83, 0x85, 0x28, 0x80, 0x81, 0x6f, 0x74, 0xf9, 0x0d, 0x85, 0x79,
	0x57, 0x53, 0x9b, 0x93, 0x31, 0x7c, 0x83, 0x83, 0x37, 0x9c, 0x27, 0x9a, 0x75, 0x7d, 0x5c, 0x82,
	0x95, 0xd7, 0x68, 0x90, 0x3a, 0x7b, 0xf0, 0x36, 0xda, 0x6f, 0xb2, 0x72, 0x7d, 0xb8, 0xea, 0x3d,
	0x93, 0xa2, 0xcb, 0xde, 0x34, 0x4f, 0x4d, 0xb9, 0xfb, 0xf3, 0x12, 0x9c, 0x28, 0x74, 0xce, 0x3e,
	0x7a, 0x02, 0xeb, 0xc3, 0xde, 0x39, 0x3b, 0x96, 0x77, 0x9e, 0x9e, 0x6e, 0xc0, 0xaf, 0x94, 0x7c,
	0xf7, 0x4c, 0xe1, 0x56, 0xbf, 0x03, 0xf3, 0xb1, 0x6e, 0x57, 0xdc, 0x51, 0x6a, 0x3a, 0x37, 0x49,
	0xde, 0x9a, 0xa8, 0x41, 0x8b, 0x25, 0x55, 0x52, 0x7f, 0x51, 0x82, 0x43, 0x19, 0x88, 0x64, 0x05,
	0xc0, 0x34, 0x1c, 0x5d, 0xf8, 0x05, 0x35, 0x6c, 0x6a, 0x35, 0xd3, 0x70, 0xee, 0x21, 0x80, 0x1f,
	0xbc, 0x7c, 0xda, 0x74, 0xbb, 0x21, 0x8a, 0x38, 0xa2, 0x1b, 0xa6, 0xe1, 0xac, 0xbb, 0x5d, 0x89,
	0xf5, 0x0c, 0xd4, 0x39, 0x96, 0xf4, 0x8e, 0x3c, 0x9b, 0x39, 0xdf, 0x1b, 0x02, 0xc2, 0x0f, 0xa1,
	0x90, 0x4d, 0x88, 0x24, 0x8b, 0x61, 0xc1, 0x27, 0xc4, 0x3b, 0x07, 0x0b, 0x51, 0x44, 0x75, 0x2a,
	0xcf, 0x14, 0x71, 0x46, 0xcf, 0x85, 0xa1, 0x7b, 0x55, 0x9c, 0x28, 0xcf, 0x01, 0x19, 0x84, 0x2f,
	0xc2, 0x15, 0xa5, 0x71, 0x2b, 0x8a, 0x93, 0x44, 0x5e, 0x05, 0xae, 0xf0, 0xe0, 0xe4, 0x11, 0x47,
	0x35, 0xd7, 0x50, 0x9e, 0x3a, 0xea, 0x3f, 0x14, 0x58, 0x16, 0x89, 0x45, 0x72, 0xdd, 0x85, 0xe7,
	0xc0, 0xf5, 0xe1, 0xc4, 0x7a, 0xbf, 0xdb, 0xf9, 0x09, 0x67, 0xd4, 0xff, 0x54, 0xe0, 0x78, 0x81,
	0x85, 0x9d, 0xe4, 0x9e, 0x54, 0xa6, 0xb3, 0x27, 0x9f, 0xb0, 0x8d, 0x1f, 0xe6, 0xda, 0x38, 0x2a,
	0x39, 0x9c, 0xf2, 0x76, 0xfb, 0xa3, 0x02, 0x2b, 0x45, 0x8a, 0x74, 0x9e, 0x94, 0x26, 0x64, 0x19,
	0x6a, 0x98, 0xd1, 0x62, 0x82, 0xc0, 0xfd, 0x59, 0xd1, 0xaa, 0x1c, 0x80, 0xe9, 0x6e, 0x1b, 0x66,
	0x7b, 0x94, 0x31, 0x63, 0x5b, 0x24, 0x4b, 0x35, 0x2d, 0x1c, 0xaa, 0xbf, 0x57, 0x40, 0xc5, 0xe4,
	0xfc, 0x4b, 0xba, 0x1e, 0xdf, 0x81, 0xc3, 0x71, 0x2b, 0x53, 0x15, 0xfd, 0xb3, 0x99, 0xe5, 0x57,
	0xe6, 0xba, 0xd5, 0x08, 0x4b, 0xc0, 0xb0, 0x00, 0xfb, 0x42, 0x81, 0x53, 0xa3, 0xb5, 0xdf, 0xc7,
	0xfd, 0xf5, 0x9d, 0x42, 0xf5, 0x9f, 0x1b, 0x5f, 0xfd, 0x4e, 0xa6, 0xfe, 0x7f, 0x1a, 0xa5, 0xff,
	0x14, 0x2e, 0x18, 0x1d, 0x8e, 0x24, 0x4d, 0x48, 0x56, 0xc0, 0xe3, 0xd9, 0x20, 0x0b, 0xb4, 0x43,
	0x2c, 0x09, 0x44, 0x23, 0xfe, 0x1c, 0xb6, 0xb4, 0x8a, 0x8d, 0xe8, 0xec, 0xcb, 0x8a, 0xef, 0x16,
	0x5b, 0x71, 0x7e, 0x02, 0x2b, 0x3a, 0xd9, 0x66, 0xbc, 0x07, 0xa7, 0xa4, 0x01, 0xeb, 0xa2, 0x90,
	0x90, 0x97, 0xd5, 0xb5, 0x4d, 0xcb, 0xb6, 0x82, 0xdd, 0x7d, 0xef, 0x84, 0xd8, 0x41, 0x50, 0x8a,
	0x1f, 0x04, 0xea, 0x27, 0x0a, 0x9c, 0x2e, 0x96, 0x3c, 0x85, 0x55, 0x70, 0x15, 0x1a, 0x86, 0x60,
	0x17, 0x3f, 0xb0, 0x97, 0x52, 0xc4, 0x52, 0xa2, 0x38, 0x70, 0x8d, 0xc1, 0x40, 0xfd, 0x3e, 0xac,
	0xc5, 0x43, 0xfc, 0x04, 0x3c, 0x54, 0x8e, 0x79, 0xe8, 0xd7, 0x0a, 0x9c, 0x1b, 0x43, 0xfc, 0x14,
	0xdc, 0x74, 0x13, 0x16, 0xe2, 0x6e, 0x8a, 0x2f, 0xb1, 0x22, 0x5f, 0xb5, 0x62, 0xbe, 0xc2, 0xc5,
	0xf4, 0xef, 0x32, 0xd4, 0x63, 0x08, 0xf9, 0xb7, 0xc0, 0xd5, 0x54, 0x2d, 0x2b, 0x6e, 0x80, 0xb4,
	0xac, 0xf5, 0x41, 0x69, 0x9b, 0xac, 0x73, 0xaf, 0xa6, 0xea, 0xdc, 0x72, 0x26, 0xf9, 0x8d, 0x41,
	0xd9, 0x9b, 0xac, 0x81, 0xaf, 0x42, 0xa3, 0x67, 0x3c, 0xd6, 0x4d, 0xc3, 0x33, 0x4c, 0x2b, 0xd8,
	0xc5, 0x64, 0x6d, 0x98, 0xfc, 0x8e, 0xf1, 0x78, 0x5d, 0x62, 0x68, 0xf5, 0xde, 0x60, 0x40, 0x5e,
	0x81, 0xe5, 0xae, 0xc5, 0x02, 0xc3, 0x31, 0xa9, 0x6e, 0x5b, 0x3d, 0x2b, 0xc0, 0xeb, 0x5a, 0x94,
	0x7f, 0xb4, 0xdb, 0xae, 0x60, 0xea, 0x77, 0x2c, 0x44, 0xb9, 0x1d, 0x61, 0xac, 0x0b, 0x04, 0x72,
	0x05, 0x8e, 0xf5, 0x2c, 0xc7, 0xea, 0xf5, 0x7b, 0x7a, 0xc4, 0x47, 0x34, 0x09, 0xdc, 0xb0, 0x1e,
	0x3f, 0x2a, 0x11, 0x6e, 0xc8, 0xf9, 0xd7, 0xe5, 0x34, 0x39, 0x07, 0xf3, 0x69, 0x5a, 0xf9, 0x1a,
	0xd2, 0x4a, 0x91, 0xa0, 0x18, 0xe3, 0x71, 0x8e, 0x98, 0xaa, 0x14, 0x23, 0x10, 0x32, 0xc5, 0xa4,
	0x68, 0xe5, 0x0b, 0x49, 0x2b, 0x45, 0xa2, 0x7e, 0x0b, 0xea, 0xb1, 0x38, 0x91, 0x45, 0x98, 0x49,
	0x64, 0xdb, 0x72, 0xc4, 0xef, 0xe2, 0xae, 0xef, 0x7a, 0xee, 0xd6, 0x96, 0xcc, 0xb1, 0xc3, 0x21,
	0x99, 0x87, 0xf2, 0x66, 0x27, 0x6c, 0x79, 0xf0, 0x4f, 0xf5, 0xc7, 0x0a, 0xd4, 0x63, 0xc1, 0xe3,
	0xcb, 0x28, 0x70, 0xf5, 0x1d, 0xb7, 0x47, 0x43, 0xa6, 0x81, 0x7b, 0xcb, 0xed, 0x85, 0x13, 0xcc,
	0x0a, 0xa8, 0x64, 0x3a, 0x13, 0xb8, 0xf7, 0xad, 0x80, 0x92, 0x23, 0x30, 0x13, 0xb8, 0xfa, 0x7b,
	0x3d, 0x86, 0x39, 0x41, 0x53, 0xab, 0x04, 0xee, 0xb7, 0x7b, 0x4c, 0x82, 0x2f, 0x79, 0xb6, 0x6c,
	0x9e, 0x54, 0x02, 0xf7, 0x92, 0x67, 0xf3, 0x24, 0x22, 0x70, 0xf5, 0x4d, 0xdf, 0x70, 0xcc, 0x1d,
	0x99, 0x8f, 0x57, 0x03, 0xf7, 0x3a, 0x8e, 0xd5, 0x1f, 0x2a, 0x50, 0x8f, 0x2d, 0x85, 0x69, 0x18,
	0x18, 0x37, 0xe8, 0x60, 0x9e, 0x41, 0x95, 0xb8, 0x41, 0xea, 0x4f, 0x15, 0xf9, 0xfa, 0x77, 0xb3,
	0x37, 0xed, 0x77, 0x87, 0xbc, 0x13, 0x7a, 0xa8, 0xe1, 0x54, 0x96, 0xad, 0xa2, 0x41, 0xb4, 0xd5,
	0x77, 0xe0, 0x44, 0x9e, 0x56, 0x53, 0x78, 0x31, 0xfe, 0xcd, 0xe8, 0x2e, 0xf9, 0x7e, 0x33, 0x9d,
	0xbb, 0x39, 0x8f, 0x2e, 0x6b, 0x59, 0xcf, 0x0a, 0x5c, 0x03, 0x96, 0x56, 0x21, 0xf9, 0xc8, 0xf2,
	0x5f, 0x05, 0x8e, 0xe5, 0xe2, 0xa6, 0xdf, 0x2f, 0xca, 0x5f, 0xd5, 0xf7, 0x8b, 0xd8, 0x2b, 0xce,
	0xc1, 0xf8, 0x2b, 0xce, 0x5f, 0x4b, 0x70, 0x76, 0xac, 0x9e, 0xf7, 0xfe, 0xb2, 0xa2, 0x5e, 0xce,
	0x93, 0xc0, 0x6b, 0x7b, 0x69, 0xbf, 0x77, 0x9e, 0xd6, 0xb7, 0x80, 0xf7, 0x63, 0x25, 0x8a, 0x91,
	0xa0, 0xd0, 0xfa, 0x83, 0xca, 0x7d, 0xcf, 0x2b, 0x7f, 0x19, 0x6a, 0xb6, 0xe1, 0xd0, 0xf0, 0xad,
	0x80, 0x27, 0x1e, 0x55, 0x0e, 0xe0, 0x95, 0x93, 0xfa, 0x59, 0x3c, 0x43, 0xcf, 0x12, 0x3e, 0x85,
	0xa4, 0x43, 0x83, 0x23, 0xa8, 0x40, 0x22, 0xc1, 0xed, 0xdb, 0x54, 0x06, 0xf3, 0x44, 0x7e, 0x39,
	0x88, 0x2a, 0x1c, 0xb2, 0x87, 0xf5, 0x52, 0xbf, 0x07, 0xad, 0x14, 0x28, 0x69, 0xa7, 0xc8, 0x42,
	0x22, 0x3b, 0x51, 0xff, 0xbe, 0x4d, 0xf5, 0x2e, 0x0d, 0x0c, 0xcb, 0x96, 0x92, 0x87, 0xf4, 0xef,
	0xdb, 0xf4, 0x06, 0x22, 0x68, 0xe0, 0x47, 0xdf, 0xea, 0x67, 0x33, 0x00, 0x83, 0x29, 0x7c, 0x6b,
	0xe0, 0x31, 0xe1, 0x57, 0xa5, 0x82, 0x4f, 0x2f, 0xd1, 0x98, 0x9c, 0x82, 0xa6, 0x4f, 0x99, 0xdb,
	0xf7, 0x4d, 0x3a, 0xc8, 0x77, 0x2a, 0x5a, 0x23, 0x04, 0xe2, 0x2d, 0xf7, 0x2c, 0x2c, 0x44, 0x48,
	0xac, 0xbf, 0x39, 0x38, 0x73, 0x2b, 0x5a, 0x2b, 0x9c, 0xb8, 0xdf, 0xdf, 0x44, 0xdc, 0xd3, 0x30,
	0x27, 0x5b, 0xf4, 0x7d, 0x46, 0x75, 0xdb, 0x63, 0x78, 0xa1, 0x55, 0xb4, 0x86, 0x80, 0xbe, 0xc9,
	0xe8, 0x6d, 0x8f, 0x91, 0x35, 0x98, 0x8f, 0x7a, 0xee, 0x21, 0x5e, 0x05, 0xf1, 0xe6, 0x42, 0xb8,
	0xc4, 0x1c, 0x7e, 0x15, 0xe0, 0x58, 0x89, 0x57, 0x81, 0xe1, 0x3e, 0xff, 0x2c, 0x22, 0xa5, 0xfa,
	0xfc, 0x57, 0x60, 0xc9, 0xf3, 0xb1, 0x52, 0x91, 0x96, 0x18, 0xb2, 0x2f, 0xbe, 0x65, 0x1b, 0xdb,
	0xf8, 0x3f, 0x46, 0x45, 0x5b, 0xf4, 0x7c, 0xbe, 0x84, 0x84, 0x45, 0x86, 0xb8, 0x40, 0x6c, 0x63,
	0x9b, 0xbc, 0x0c, 0xcb, 0x0e, 0x7d, 0x1c, 0xe4, 0x11, 0xd7, 0x90, 0xf8, 0x28, 0x47, 0xc9, 0xa2,
	0x7e, 0x16, 0x16, 0xf8, 0xc5, 0xa9, 0x33, 0xd7, 0x0f, 0x2c, 0x67, 0x5b, 0xd0, 0x00, 0xd2, 0xb4,
	0xf8, 0xc4, 0x7d, 0x01, 0x0f, 0x25, 0x85, 0x68, 0x06, 0x8f, 0x34, 0x0d, 0x1b, 0x77, 0x82, 0xaa,
	0x2e, 0x24, 0x49, 0x94, 0x6b, 0xec, 0x9e, 0x4f, 0x65, 0x3e, 0x82, 0xd4, 0x37, 0x61, 0x35, 0x21,
	0x49, 0x1e, 0xc4, 0x46, 0xb7, 0xeb, 0x53, 0xc6, 0x04, 0x8b, 0x06, 0xb2, 0x38, 0x1e, 0x13, 0x2c,
	0xba, 0x89, 0xd7, 0x04, 0x52, 0x86, 0x16, 0x68, 0xba, 0x64, 0x85, 0x2c, 0x9a, 0x29, 0x2d, 0xee,
	0xd2, 0xc7, 0x81, 0x60, 0x82, 0xd4, 0xb7, 0xe0, 0x64, 0x42, 0x8b, 0xd0, 0x82, 0x84, 0x1a, 0x73,
	0xc8, 0x63, 0x25, 0xa6, 0x46, 0x58, 0x3e, 0xc4, 0xf4, 0xb8, 0x14, 0xbe, 0x49, 0xc8, 0x35, 0xc5,
	0x39, 0x88, 0xe5, 0xd7, 0x42, 0xf2, 0x43, 0xb1, 0x75, 0xc0, 0xe9, 0x70, 0x09, 0xbe, 0x08, 0xed,
	0xe4, 0x7a, 0x88, 0x91, 0xcd, 0x23, 0xd9, 0x91, 0xc4, 0xca, 0x08, 0x09, 0xd5, 0x2f, 0x14, 0x38,
	0x72, 0xd7, 0x0d, 0xac, 0xad, 0xdd, 0xb7, 0x5d, 0x67, 0x3a, 0xed, 0x96, 0xcb, 0x50, 0xc3, 0x07,
	0xa0, 0xd8, 0x05, 0x7e, 0x34, 0x45, 0x17, 0xc9, 0xaa, 0x72, 0x4c, 0xfc, 0x15, 0x81, 0xc0, 0xc1,
	0x9e, 0x68, 0x1b, 0xf1, 0x3d, 0x86, 0xdf, 0x7c, 0x23, 0x6c, 0xb9, 0x7c, 0xe9, 0xed, 0x18, 0x4e,
	0xd7, 0xa6, 0x72, 0x5b, 0xd5, 0x11, 0x76, 0x0b, 0x41, 0xea, 0x1b, 0xb0, 0x98, 0x56, 0x7f, 0x0a,
	0xb9, 0xce, 0x7f, 0x4a, 0x50, 0x0d, 0x19, 0x92, 0x15, 0x80, 0x47, 0xd4, 0x67, 0xbc, 0xaa, 0x90,
	0xa5, 0x53, 0x59, 0xab, 0x49, 0xc8, 0x46, 0x97, 0x3c, 0x03, 0x75, 0x34, 0x97, 0x05, 0x46, 0xd0,
	0x67, 0xf2, 0x30, 0x01, 0x0e, 0xba, 0x8f, 0x10, 0x7e, 0x16, 0x45, 0x99, 0x7e, 0x19, 0x6f, 0xa8,
	0x68, 0xcc, 0xd3, 0x3d, 0x24, 0xb6, 0xba, 0xb2, 0x29, 0x36, 0xc3, 0x87, 0x1b, 0x5d, 0x7e, 0x50,
	0xe2, 0x84, 0x63, 0xf4, 0x44, 0x57, 0xba, 0x26, 0x7c, 0x75, 0xd7, 0xe8, 0x51, 0xae, 0x11, 0x93,
	0x75, 0x8e, 0xd5, 0xc5, 0xe3, 0xa1, 0xa6, 0xd5, 0x24, 0x44, 0xa4, 0x8a, 0xe1, 0x34, 0x2e, 0x00,
	0x71, 0x34, 0xd4, 0x25, 0x2c, 0x7c, 0x78, 0x0c, 0x51, 0x50, 0x42, 0x15, 0x79, 0x84, 0x28, 0x28,
	0x64, 0x09, 0xaa, 0xdb, 0xd4, 0xed, 0xd1, 0xc0, 0xdf, 0xc5, 0xcd, 0x5e, 0xd3, 0xa2, 0x71, 0xa4,
	0x9d, 0xe1, 0x53, 0x03, 0x77, 0xb5, 0x22, 0xb4, 0xc3, 0xbc, 0xea, 0x18, 0x54, 0x6d, 0x63, 0x97,
	0x27, 0x33, 0x5d, 0xdc, 0xbb, 0x35, 0x6d, 0x16, 0xc7, 0x1b, 0x5d, 0xae, 0xb8, 0x98, 0x42, 0xa1,
	0x0d, 0xa1, 0x38, 0x42, 0xb8, 0x48, 0xf5, 0x93, 0x32, 0x2c, 0x6a, 0x74, 0xcb, 0xa7, 0x6c, 0x67,
	0x6a, 0xab, 0x31, 0x19, 0x3d, 0x91, 0x53, 0xc7, 0xa2, 0x77, 0x9a, 0x67, 0x2f, 0x96, 0x1e, 0x43,
	0x11, 0xcf, 0xf0, 0x0d, 0xd7, 0xb7, 0xde, 0x8a, 0xb0, 0x72, 0xc3, 0x14, 0x8f, 0x6d, 0x25, 0x15,
	0xdb, 0x44, 0x08, 0x67, 0x0a, 0x43, 0x38, 0x3b, 0x2a, 0x84, 0xd5, 0xd1, 0x21, 0xac, 0x15, 0x87,
	0x10, 0x8a, 0x42, 0x58, 0x2f, 0x08, 0x61, 0x23, 0x11, 0x42, 0xf5, 0x4d, 0x38, 0x3a, 0x14, 0xa2,
	0xfd, 0xef, 0xb8, 0xce, 0xdf, 0x5a, 0xb0, 0x78, 0xdb, 0x74, 0xe3, 0x49, 0xba, 0xfc, 0x24, 0x3f,
	0x80, 0x76, 0x5e, 0xa6, 0x46, 0x2e, 0xe6, 0x55, 0x06, 0xd9, 0xb5, 0xc9, 0xd2, 0xf3, 0x63, 0xe3,
	0x0b, 0x9b, 0xd4, 0x03, 0xe4, 0x83, 0x58, 0x0d, 0x91, 0xc2, 0xea, 0x90, 0xe7, 0x27, 0x93, 0xdf,
	0xd9, 0x8b, 0x02, 0x3f, 0x51, 0x60, 0xa5, 0x30, 0x5f, 0x25, 0x97, 0x26, 0xcb, 0x6e, 0x85, 0x2b,
	0xf6, 0x94, 0x12, 0xab, 0x07, 0xc8, 0xa7, 0x0a, 0x9c, 0x19, 0xeb, 0xf7, 0x1e, 0xf2, 0x8d, 0xbd,
	0xfd, 0x14, 0x24, 0xd4, 0x7b, 0x79, 0x3f, 0x7f, 0x14, 0xa9, 0x07, 0xc8, 0x7b, 0xf8, 0xeb, 0x4c,
	0x96, 0x5a, 0xe7, 0x87, 0x1f, 0x9a, 0xf2, 0xfb, 0xf6, 0x4b, 0x17, 0xc6, 0xc4, 0x8e, 0x04, 0xef,
	0xe2, 0x1f, 0x2a, 0x99, 0xab, 0xe5, 0xc2, 0x24, 0x92, 0x3b, 0x93, 0x8b, 0xfe, 0x50, 0x81, 0xe5,
	0x9c, 0x2e, 0x38, 0x5a, 0xfe, 0xb5, 0xac, 0x90, 0x17, 0x9b, 0xdf, 0x99, 0x84, 0x24, 0x52, 0xe4,
	0x47, 0x0a, 0x1c, 0x2f, 0x50, 0xa4, 0x43, 0x3a, 0x13, 0x6b, 0xd2, 0x59, 0xba, 0x34, 0xb9, 0x2a,
	0x1d, 0xa9, 0x4b, 0x51, 0xcf, 0x76, 0x48, 0x97, 0x31, 0xfa, 0xcb, 0x43, 0xba, 0x8c, 0xd3, 0x14,
	0x56, 0x0f, 0x90, 0x9f, 0x29, 0x70, 0x72, 0x64, 0x13, 0x99, 0xbc, 0x58, 0x60, 0x68, 0xa1, 0x56,
	0x2f, 0x4d, 0x4e, 0x18, 0xdf, 0x2f, 0xd9, 0xcd, 0x23, 0x92, 0xd9, 0x14, 0xc9, 0xeb, 0x7c, 0x0d,
	0x2d, 0xda, 0xe2, 0x8e, 0x94, 0x7a, 0x80, 0x7c, 0xa4, 0xc0, 0x89, 0xc2, 0x4d, 0xdd, 0x21, 0x97,
	0xf7, 0x70, 0xbe, 0x75, 0x96, 0xbe, 0xbe, 0xa7, 0x06, 0x44, 0x7a, 0x1b, 0x0d, 0xd7, 0xb5, 0xf9,
	0xdb, 0x28, 0xb7, 0x31, 0x90, 0xbf, 0x8d, 0xf2, 0xcb, 0x79, 0xf5, 0x00, 0xd1, 0x61, 0x2e, 0x99,
	0xdc, 0x92, 0xd3, 0x29, 0x3e, 0x99, 0xa9, 0xfb, 0xd2, 0x99, 0x11, 0x58, 0x91, 0x80, 0x4d, 0x68,
	0xa5, 0x2e, 0x73, 0x72, 0x66, 0xe8, 0xbe, 0xce, 0xca, 0xc7, 0x96, 0xce, 0x8e, 0x42, 0x0b, 0x65,
	0xfc, 0x2f, 0x00, 0x00, 0xff, 0xff, 0x94, 0x08, 0xaf, 0xaf, 0x4e, 0x35, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LcosServiceableServiceClient is the client API for LcosServiceableService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LcosServiceableServiceClient interface {
	CheckLineServiceableArea(ctx context.Context, in *CheckLineServiceableAreaRequest, opts ...grpc.CallOption) (*CheckLineServiceableAreaResponse, error)
	CheckLineServiceableArea2(ctx context.Context, in *CheckLineServiceableAreaRequest2, opts ...grpc.CallOption) (*CheckLineServiceableAreaResponse, error)
	BatchCheckLineServiceableArea(ctx context.Context, in *BatchCheckLineServiceableAreaRequest, opts ...grpc.CallOption) (*BatchCheckLineServiceableAreaResponse, error)
	MultipleBatchCheckLineServiceableArea(ctx context.Context, in *MultipleBatchCheckLineServiceableAreaRequest, opts ...grpc.CallOption) (*MultipleBatchCheckLineServiceableAreaResponse, error)
	GetLineServiceableArea(ctx context.Context, in *GetLineServiceableInfoRequest, opts ...grpc.CallOption) (*GetLineServiceableInfoResponse, error)
	GetLineServiceableArea2(ctx context.Context, in *GetLineServiceableInfoRequest2, opts ...grpc.CallOption) (*GetLineServiceableInfoResponse, error)
	BatchGetLineServiceableArea(ctx context.Context, in *BatchGetLineServiceableInfoRequest, opts ...grpc.CallOption) (*BatchGetLineServiceableInfoResponse, error)
	BatchGetLineServiceableArea2(ctx context.Context, in *BatchGetLineServiceableInfoRequest2, opts ...grpc.CallOption) (*BatchGetLineServiceableInfoResponse2, error)
	GetLineCollectDeliverAbility(ctx context.Context, in *GetLineCollectDeliverAbilityRequest, opts ...grpc.CallOption) (*GetLineCollectDeliverAbilityResponse, error)
	BatchGetLineCollectDeliverAbility(ctx context.Context, in *BatchGetLineCollectDeliverAbilityRequest, opts ...grpc.CallOption) (*BatchGetLineCollectDeliverAbilityResponse, error)
	CheckFmServiceableArea(ctx context.Context, in *CheckFmServiceableAreaRequest, opts ...grpc.CallOption) (*CheckFmServiceableAreaResponse, error)
	BatchCheckLineServiceableArea2(ctx context.Context, in *BatchCheckLineServiceableAreaRequest2, opts ...grpc.CallOption) (*BatchCheckLineServiceableAreaResponse2, error)
	BatchGetLaneServiceableRule(ctx context.Context, in *BatchGetLaneServiceableRuleRequest, opts ...grpc.CallOption) (*BatchGetLaneServiceableRuleResponse, error)
	NotifyZoneInfo(ctx context.Context, in *NotifyZoneInfoRequest, opts ...grpc.CallOption) (*NotifyZoneInfoResponse, error)
	RefreshZoneInfo(ctx context.Context, in *RefreshZoneInfoRequest, opts ...grpc.CallOption) (*RefreshZoneInfoResponse, error)
}

type lcosServiceableServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLcosServiceableServiceClient(cc grpc.ClientConnInterface) LcosServiceableServiceClient {
	return &lcosServiceableServiceClient{cc}
}

func (c *lcosServiceableServiceClient) CheckLineServiceableArea(ctx context.Context, in *CheckLineServiceableAreaRequest, opts ...grpc.CallOption) (*CheckLineServiceableAreaResponse, error) {
	out := new(CheckLineServiceableAreaResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/CheckLineServiceableArea", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) CheckLineServiceableArea2(ctx context.Context, in *CheckLineServiceableAreaRequest2, opts ...grpc.CallOption) (*CheckLineServiceableAreaResponse, error) {
	out := new(CheckLineServiceableAreaResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/CheckLineServiceableArea2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) BatchCheckLineServiceableArea(ctx context.Context, in *BatchCheckLineServiceableAreaRequest, opts ...grpc.CallOption) (*BatchCheckLineServiceableAreaResponse, error) {
	out := new(BatchCheckLineServiceableAreaResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/BatchCheckLineServiceableArea", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) MultipleBatchCheckLineServiceableArea(ctx context.Context, in *MultipleBatchCheckLineServiceableAreaRequest, opts ...grpc.CallOption) (*MultipleBatchCheckLineServiceableAreaResponse, error) {
	out := new(MultipleBatchCheckLineServiceableAreaResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/MultipleBatchCheckLineServiceableArea", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) GetLineServiceableArea(ctx context.Context, in *GetLineServiceableInfoRequest, opts ...grpc.CallOption) (*GetLineServiceableInfoResponse, error) {
	out := new(GetLineServiceableInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/GetLineServiceableArea", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) GetLineServiceableArea2(ctx context.Context, in *GetLineServiceableInfoRequest2, opts ...grpc.CallOption) (*GetLineServiceableInfoResponse, error) {
	out := new(GetLineServiceableInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/GetLineServiceableArea2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) BatchGetLineServiceableArea(ctx context.Context, in *BatchGetLineServiceableInfoRequest, opts ...grpc.CallOption) (*BatchGetLineServiceableInfoResponse, error) {
	out := new(BatchGetLineServiceableInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/BatchGetLineServiceableArea", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) BatchGetLineServiceableArea2(ctx context.Context, in *BatchGetLineServiceableInfoRequest2, opts ...grpc.CallOption) (*BatchGetLineServiceableInfoResponse2, error) {
	out := new(BatchGetLineServiceableInfoResponse2)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/BatchGetLineServiceableArea2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) GetLineCollectDeliverAbility(ctx context.Context, in *GetLineCollectDeliverAbilityRequest, opts ...grpc.CallOption) (*GetLineCollectDeliverAbilityResponse, error) {
	out := new(GetLineCollectDeliverAbilityResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/GetLineCollectDeliverAbility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) BatchGetLineCollectDeliverAbility(ctx context.Context, in *BatchGetLineCollectDeliverAbilityRequest, opts ...grpc.CallOption) (*BatchGetLineCollectDeliverAbilityResponse, error) {
	out := new(BatchGetLineCollectDeliverAbilityResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/BatchGetLineCollectDeliverAbility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) CheckFmServiceableArea(ctx context.Context, in *CheckFmServiceableAreaRequest, opts ...grpc.CallOption) (*CheckFmServiceableAreaResponse, error) {
	out := new(CheckFmServiceableAreaResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/CheckFmServiceableArea", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) BatchCheckLineServiceableArea2(ctx context.Context, in *BatchCheckLineServiceableAreaRequest2, opts ...grpc.CallOption) (*BatchCheckLineServiceableAreaResponse2, error) {
	out := new(BatchCheckLineServiceableAreaResponse2)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/BatchCheckLineServiceableArea2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) BatchGetLaneServiceableRule(ctx context.Context, in *BatchGetLaneServiceableRuleRequest, opts ...grpc.CallOption) (*BatchGetLaneServiceableRuleResponse, error) {
	out := new(BatchGetLaneServiceableRuleResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/BatchGetLaneServiceableRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) NotifyZoneInfo(ctx context.Context, in *NotifyZoneInfoRequest, opts ...grpc.CallOption) (*NotifyZoneInfoResponse, error) {
	out := new(NotifyZoneInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/NotifyZoneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lcosServiceableServiceClient) RefreshZoneInfo(ctx context.Context, in *RefreshZoneInfoRequest, opts ...grpc.CallOption) (*RefreshZoneInfoResponse, error) {
	out := new(RefreshZoneInfoResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.LcosServiceableService/RefreshZoneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LcosServiceableServiceServer is the server API for LcosServiceableService service.
type LcosServiceableServiceServer interface {
	CheckLineServiceableArea(context.Context, *CheckLineServiceableAreaRequest) (*CheckLineServiceableAreaResponse, error)
	CheckLineServiceableArea2(context.Context, *CheckLineServiceableAreaRequest2) (*CheckLineServiceableAreaResponse, error)
	BatchCheckLineServiceableArea(context.Context, *BatchCheckLineServiceableAreaRequest) (*BatchCheckLineServiceableAreaResponse, error)
	MultipleBatchCheckLineServiceableArea(context.Context, *MultipleBatchCheckLineServiceableAreaRequest) (*MultipleBatchCheckLineServiceableAreaResponse, error)
	GetLineServiceableArea(context.Context, *GetLineServiceableInfoRequest) (*GetLineServiceableInfoResponse, error)
	GetLineServiceableArea2(context.Context, *GetLineServiceableInfoRequest2) (*GetLineServiceableInfoResponse, error)
	BatchGetLineServiceableArea(context.Context, *BatchGetLineServiceableInfoRequest) (*BatchGetLineServiceableInfoResponse, error)
	BatchGetLineServiceableArea2(context.Context, *BatchGetLineServiceableInfoRequest2) (*BatchGetLineServiceableInfoResponse2, error)
	GetLineCollectDeliverAbility(context.Context, *GetLineCollectDeliverAbilityRequest) (*GetLineCollectDeliverAbilityResponse, error)
	BatchGetLineCollectDeliverAbility(context.Context, *BatchGetLineCollectDeliverAbilityRequest) (*BatchGetLineCollectDeliverAbilityResponse, error)
	CheckFmServiceableArea(context.Context, *CheckFmServiceableAreaRequest) (*CheckFmServiceableAreaResponse, error)
	BatchCheckLineServiceableArea2(context.Context, *BatchCheckLineServiceableAreaRequest2) (*BatchCheckLineServiceableAreaResponse2, error)
	BatchGetLaneServiceableRule(context.Context, *BatchGetLaneServiceableRuleRequest) (*BatchGetLaneServiceableRuleResponse, error)
	NotifyZoneInfo(context.Context, *NotifyZoneInfoRequest) (*NotifyZoneInfoResponse, error)
	RefreshZoneInfo(context.Context, *RefreshZoneInfoRequest) (*RefreshZoneInfoResponse, error)
}

// UnimplementedLcosServiceableServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLcosServiceableServiceServer struct {
}

func (*UnimplementedLcosServiceableServiceServer) CheckLineServiceableArea(ctx context.Context, req *CheckLineServiceableAreaRequest) (*CheckLineServiceableAreaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLineServiceableArea not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) CheckLineServiceableArea2(ctx context.Context, req *CheckLineServiceableAreaRequest2) (*CheckLineServiceableAreaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLineServiceableArea2 not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) BatchCheckLineServiceableArea(ctx context.Context, req *BatchCheckLineServiceableAreaRequest) (*BatchCheckLineServiceableAreaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckLineServiceableArea not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) MultipleBatchCheckLineServiceableArea(ctx context.Context, req *MultipleBatchCheckLineServiceableAreaRequest) (*MultipleBatchCheckLineServiceableAreaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MultipleBatchCheckLineServiceableArea not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) GetLineServiceableArea(ctx context.Context, req *GetLineServiceableInfoRequest) (*GetLineServiceableInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLineServiceableArea not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) GetLineServiceableArea2(ctx context.Context, req *GetLineServiceableInfoRequest2) (*GetLineServiceableInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLineServiceableArea2 not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) BatchGetLineServiceableArea(ctx context.Context, req *BatchGetLineServiceableInfoRequest) (*BatchGetLineServiceableInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetLineServiceableArea not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) BatchGetLineServiceableArea2(ctx context.Context, req *BatchGetLineServiceableInfoRequest2) (*BatchGetLineServiceableInfoResponse2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetLineServiceableArea2 not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) GetLineCollectDeliverAbility(ctx context.Context, req *GetLineCollectDeliverAbilityRequest) (*GetLineCollectDeliverAbilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLineCollectDeliverAbility not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) BatchGetLineCollectDeliverAbility(ctx context.Context, req *BatchGetLineCollectDeliverAbilityRequest) (*BatchGetLineCollectDeliverAbilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetLineCollectDeliverAbility not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) CheckFmServiceableArea(ctx context.Context, req *CheckFmServiceableAreaRequest) (*CheckFmServiceableAreaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckFmServiceableArea not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) BatchCheckLineServiceableArea2(ctx context.Context, req *BatchCheckLineServiceableAreaRequest2) (*BatchCheckLineServiceableAreaResponse2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckLineServiceableArea2 not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) BatchGetLaneServiceableRule(ctx context.Context, req *BatchGetLaneServiceableRuleRequest) (*BatchGetLaneServiceableRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetLaneServiceableRule not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) NotifyZoneInfo(ctx context.Context, req *NotifyZoneInfoRequest) (*NotifyZoneInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyZoneInfo not implemented")
}
func (*UnimplementedLcosServiceableServiceServer) RefreshZoneInfo(ctx context.Context, req *RefreshZoneInfoRequest) (*RefreshZoneInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshZoneInfo not implemented")
}

func RegisterLcosServiceableServiceServer(s *grpc.Server, srv LcosServiceableServiceServer) {
	s.RegisterService(&_LcosServiceableService_serviceDesc, srv)
}

func _LcosServiceableService_CheckLineServiceableArea_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLineServiceableAreaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).CheckLineServiceableArea(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/CheckLineServiceableArea",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).CheckLineServiceableArea(ctx, req.(*CheckLineServiceableAreaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_CheckLineServiceableArea2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLineServiceableAreaRequest2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).CheckLineServiceableArea2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/CheckLineServiceableArea2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).CheckLineServiceableArea2(ctx, req.(*CheckLineServiceableAreaRequest2))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_BatchCheckLineServiceableArea_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckLineServiceableAreaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).BatchCheckLineServiceableArea(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/BatchCheckLineServiceableArea",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).BatchCheckLineServiceableArea(ctx, req.(*BatchCheckLineServiceableAreaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_MultipleBatchCheckLineServiceableArea_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MultipleBatchCheckLineServiceableAreaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).MultipleBatchCheckLineServiceableArea(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/MultipleBatchCheckLineServiceableArea",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).MultipleBatchCheckLineServiceableArea(ctx, req.(*MultipleBatchCheckLineServiceableAreaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_GetLineServiceableArea_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLineServiceableInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).GetLineServiceableArea(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/GetLineServiceableArea",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).GetLineServiceableArea(ctx, req.(*GetLineServiceableInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_GetLineServiceableArea2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLineServiceableInfoRequest2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).GetLineServiceableArea2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/GetLineServiceableArea2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).GetLineServiceableArea2(ctx, req.(*GetLineServiceableInfoRequest2))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_BatchGetLineServiceableArea_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetLineServiceableInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).BatchGetLineServiceableArea(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/BatchGetLineServiceableArea",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).BatchGetLineServiceableArea(ctx, req.(*BatchGetLineServiceableInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_BatchGetLineServiceableArea2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetLineServiceableInfoRequest2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).BatchGetLineServiceableArea2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/BatchGetLineServiceableArea2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).BatchGetLineServiceableArea2(ctx, req.(*BatchGetLineServiceableInfoRequest2))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_GetLineCollectDeliverAbility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLineCollectDeliverAbilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).GetLineCollectDeliverAbility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/GetLineCollectDeliverAbility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).GetLineCollectDeliverAbility(ctx, req.(*GetLineCollectDeliverAbilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_BatchGetLineCollectDeliverAbility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetLineCollectDeliverAbilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).BatchGetLineCollectDeliverAbility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/BatchGetLineCollectDeliverAbility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).BatchGetLineCollectDeliverAbility(ctx, req.(*BatchGetLineCollectDeliverAbilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_CheckFmServiceableArea_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFmServiceableAreaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).CheckFmServiceableArea(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/CheckFmServiceableArea",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).CheckFmServiceableArea(ctx, req.(*CheckFmServiceableAreaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_BatchCheckLineServiceableArea2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckLineServiceableAreaRequest2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).BatchCheckLineServiceableArea2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/BatchCheckLineServiceableArea2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).BatchCheckLineServiceableArea2(ctx, req.(*BatchCheckLineServiceableAreaRequest2))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_BatchGetLaneServiceableRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetLaneServiceableRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).BatchGetLaneServiceableRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/BatchGetLaneServiceableRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).BatchGetLaneServiceableRule(ctx, req.(*BatchGetLaneServiceableRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_NotifyZoneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyZoneInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).NotifyZoneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/NotifyZoneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).NotifyZoneInfo(ctx, req.(*NotifyZoneInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LcosServiceableService_RefreshZoneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshZoneInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LcosServiceableServiceServer).RefreshZoneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.LcosServiceableService/RefreshZoneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LcosServiceableServiceServer).RefreshZoneInfo(ctx, req.(*RefreshZoneInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LcosServiceableService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.LcosServiceableService",
	HandlerType: (*LcosServiceableServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckLineServiceableArea",
			Handler:    _LcosServiceableService_CheckLineServiceableArea_Handler,
		},
		{
			MethodName: "CheckLineServiceableArea2",
			Handler:    _LcosServiceableService_CheckLineServiceableArea2_Handler,
		},
		{
			MethodName: "BatchCheckLineServiceableArea",
			Handler:    _LcosServiceableService_BatchCheckLineServiceableArea_Handler,
		},
		{
			MethodName: "MultipleBatchCheckLineServiceableArea",
			Handler:    _LcosServiceableService_MultipleBatchCheckLineServiceableArea_Handler,
		},
		{
			MethodName: "GetLineServiceableArea",
			Handler:    _LcosServiceableService_GetLineServiceableArea_Handler,
		},
		{
			MethodName: "GetLineServiceableArea2",
			Handler:    _LcosServiceableService_GetLineServiceableArea2_Handler,
		},
		{
			MethodName: "BatchGetLineServiceableArea",
			Handler:    _LcosServiceableService_BatchGetLineServiceableArea_Handler,
		},
		{
			MethodName: "BatchGetLineServiceableArea2",
			Handler:    _LcosServiceableService_BatchGetLineServiceableArea2_Handler,
		},
		{
			MethodName: "GetLineCollectDeliverAbility",
			Handler:    _LcosServiceableService_GetLineCollectDeliverAbility_Handler,
		},
		{
			MethodName: "BatchGetLineCollectDeliverAbility",
			Handler:    _LcosServiceableService_BatchGetLineCollectDeliverAbility_Handler,
		},
		{
			MethodName: "CheckFmServiceableArea",
			Handler:    _LcosServiceableService_CheckFmServiceableArea_Handler,
		},
		{
			MethodName: "BatchCheckLineServiceableArea2",
			Handler:    _LcosServiceableService_BatchCheckLineServiceableArea2_Handler,
		},
		{
			MethodName: "BatchGetLaneServiceableRule",
			Handler:    _LcosServiceableService_BatchGetLaneServiceableRule_Handler,
		},
		{
			MethodName: "NotifyZoneInfo",
			Handler:    _LcosServiceableService_NotifyZoneInfo_Handler,
		},
		{
			MethodName: "RefreshZoneInfo",
			Handler:    _LcosServiceableService_RefreshZoneInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lcos_serviceable.proto",
}
