// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sync_item_cdt.proto

package lcos_protobuf

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type SyncAutoUpdateDataRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	RuleId               *uint64    `protobuf:"varint,2,opt,name=rule_id,json=ruleId" json:"rule_id,omitempty"`
	Region               *string    `protobuf:"bytes,3,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SyncAutoUpdateDataRequest) Reset()         { *m = SyncAutoUpdateDataRequest{} }
func (m *SyncAutoUpdateDataRequest) String() string { return proto.CompactTextString(m) }
func (*SyncAutoUpdateDataRequest) ProtoMessage()    {}
func (*SyncAutoUpdateDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bd8c4d4f7852f970, []int{0}
}

func (m *SyncAutoUpdateDataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncAutoUpdateDataRequest.Unmarshal(m, b)
}
func (m *SyncAutoUpdateDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncAutoUpdateDataRequest.Marshal(b, m, deterministic)
}
func (m *SyncAutoUpdateDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncAutoUpdateDataRequest.Merge(m, src)
}
func (m *SyncAutoUpdateDataRequest) XXX_Size() int {
	return xxx_messageInfo_SyncAutoUpdateDataRequest.Size(m)
}
func (m *SyncAutoUpdateDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncAutoUpdateDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncAutoUpdateDataRequest proto.InternalMessageInfo

func (m *SyncAutoUpdateDataRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *SyncAutoUpdateDataRequest) GetRuleId() uint64 {
	if m != nil && m.RuleId != nil {
		return *m.RuleId
	}
	return 0
}

func (m *SyncAutoUpdateDataRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type SyncAutoUpdateDataResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SyncAutoUpdateDataResponse) Reset()         { *m = SyncAutoUpdateDataResponse{} }
func (m *SyncAutoUpdateDataResponse) String() string { return proto.CompactTextString(m) }
func (*SyncAutoUpdateDataResponse) ProtoMessage()    {}
func (*SyncAutoUpdateDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bd8c4d4f7852f970, []int{1}
}

func (m *SyncAutoUpdateDataResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncAutoUpdateDataResponse.Unmarshal(m, b)
}
func (m *SyncAutoUpdateDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncAutoUpdateDataResponse.Marshal(b, m, deterministic)
}
func (m *SyncAutoUpdateDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncAutoUpdateDataResponse.Merge(m, src)
}
func (m *SyncAutoUpdateDataResponse) XXX_Size() int {
	return xxx_messageInfo_SyncAutoUpdateDataResponse.Size(m)
}
func (m *SyncAutoUpdateDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncAutoUpdateDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SyncAutoUpdateDataResponse proto.InternalMessageInfo

func (m *SyncAutoUpdateDataResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

type SyncManualUpdateDataRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	ProductId            *string    `protobuf:"bytes,2,opt,name=product_id,json=productId" json:"product_id,omitempty"`
	Region               *string    `protobuf:"bytes,3,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SyncManualUpdateDataRequest) Reset()         { *m = SyncManualUpdateDataRequest{} }
func (m *SyncManualUpdateDataRequest) String() string { return proto.CompactTextString(m) }
func (*SyncManualUpdateDataRequest) ProtoMessage()    {}
func (*SyncManualUpdateDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bd8c4d4f7852f970, []int{2}
}

func (m *SyncManualUpdateDataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncManualUpdateDataRequest.Unmarshal(m, b)
}
func (m *SyncManualUpdateDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncManualUpdateDataRequest.Marshal(b, m, deterministic)
}
func (m *SyncManualUpdateDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncManualUpdateDataRequest.Merge(m, src)
}
func (m *SyncManualUpdateDataRequest) XXX_Size() int {
	return xxx_messageInfo_SyncManualUpdateDataRequest.Size(m)
}
func (m *SyncManualUpdateDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncManualUpdateDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncManualUpdateDataRequest proto.InternalMessageInfo

func (m *SyncManualUpdateDataRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *SyncManualUpdateDataRequest) GetProductId() string {
	if m != nil && m.ProductId != nil {
		return *m.ProductId
	}
	return ""
}

func (m *SyncManualUpdateDataRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type SyncManualUpdateDataResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SyncManualUpdateDataResponse) Reset()         { *m = SyncManualUpdateDataResponse{} }
func (m *SyncManualUpdateDataResponse) String() string { return proto.CompactTextString(m) }
func (*SyncManualUpdateDataResponse) ProtoMessage()    {}
func (*SyncManualUpdateDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bd8c4d4f7852f970, []int{3}
}

func (m *SyncManualUpdateDataResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncManualUpdateDataResponse.Unmarshal(m, b)
}
func (m *SyncManualUpdateDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncManualUpdateDataResponse.Marshal(b, m, deterministic)
}
func (m *SyncManualUpdateDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncManualUpdateDataResponse.Merge(m, src)
}
func (m *SyncManualUpdateDataResponse) XXX_Size() int {
	return xxx_messageInfo_SyncManualUpdateDataResponse.Size(m)
}
func (m *SyncManualUpdateDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncManualUpdateDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SyncManualUpdateDataResponse proto.InternalMessageInfo

func (m *SyncManualUpdateDataResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

type SyncAutoVolumeDataRequest struct {
	ReqHeader            *ReqHeader `protobuf:"bytes,1,req,name=req_header,json=reqHeader" json:"req_header,omitempty"`
	RuleId               *uint64    `protobuf:"varint,2,opt,name=rule_id,json=ruleId" json:"rule_id,omitempty"`
	Region               *string    `protobuf:"bytes,3,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SyncAutoVolumeDataRequest) Reset()         { *m = SyncAutoVolumeDataRequest{} }
func (m *SyncAutoVolumeDataRequest) String() string { return proto.CompactTextString(m) }
func (*SyncAutoVolumeDataRequest) ProtoMessage()    {}
func (*SyncAutoVolumeDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_bd8c4d4f7852f970, []int{4}
}

func (m *SyncAutoVolumeDataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncAutoVolumeDataRequest.Unmarshal(m, b)
}
func (m *SyncAutoVolumeDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncAutoVolumeDataRequest.Marshal(b, m, deterministic)
}
func (m *SyncAutoVolumeDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncAutoVolumeDataRequest.Merge(m, src)
}
func (m *SyncAutoVolumeDataRequest) XXX_Size() int {
	return xxx_messageInfo_SyncAutoVolumeDataRequest.Size(m)
}
func (m *SyncAutoVolumeDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncAutoVolumeDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncAutoVolumeDataRequest proto.InternalMessageInfo

func (m *SyncAutoVolumeDataRequest) GetReqHeader() *ReqHeader {
	if m != nil {
		return m.ReqHeader
	}
	return nil
}

func (m *SyncAutoVolumeDataRequest) GetRuleId() uint64 {
	if m != nil && m.RuleId != nil {
		return *m.RuleId
	}
	return 0
}

func (m *SyncAutoVolumeDataRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type SyncAutoVolumeDataResponse struct {
	RespHeader           *RespHeader `protobuf:"bytes,1,req,name=resp_header,json=respHeader" json:"resp_header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SyncAutoVolumeDataResponse) Reset()         { *m = SyncAutoVolumeDataResponse{} }
func (m *SyncAutoVolumeDataResponse) String() string { return proto.CompactTextString(m) }
func (*SyncAutoVolumeDataResponse) ProtoMessage()    {}
func (*SyncAutoVolumeDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_bd8c4d4f7852f970, []int{5}
}

func (m *SyncAutoVolumeDataResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncAutoVolumeDataResponse.Unmarshal(m, b)
}
func (m *SyncAutoVolumeDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncAutoVolumeDataResponse.Marshal(b, m, deterministic)
}
func (m *SyncAutoVolumeDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncAutoVolumeDataResponse.Merge(m, src)
}
func (m *SyncAutoVolumeDataResponse) XXX_Size() int {
	return xxx_messageInfo_SyncAutoVolumeDataResponse.Size(m)
}
func (m *SyncAutoVolumeDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncAutoVolumeDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SyncAutoVolumeDataResponse proto.InternalMessageInfo

func (m *SyncAutoVolumeDataResponse) GetRespHeader() *RespHeader {
	if m != nil {
		return m.RespHeader
	}
	return nil
}

func init() {
	proto.RegisterType((*SyncAutoUpdateDataRequest)(nil), "lcos_protobuf.SyncAutoUpdateDataRequest")
	proto.RegisterType((*SyncAutoUpdateDataResponse)(nil), "lcos_protobuf.SyncAutoUpdateDataResponse")
	proto.RegisterType((*SyncManualUpdateDataRequest)(nil), "lcos_protobuf.SyncManualUpdateDataRequest")
	proto.RegisterType((*SyncManualUpdateDataResponse)(nil), "lcos_protobuf.SyncManualUpdateDataResponse")
	proto.RegisterType((*SyncAutoVolumeDataRequest)(nil), "lcos_protobuf.SyncAutoVolumeDataRequest")
	proto.RegisterType((*SyncAutoVolumeDataResponse)(nil), "lcos_protobuf.SyncAutoVolumeDataResponse")
}

func init() {
	proto.RegisterFile("sync_item_cdt.proto", fileDescriptor_bd8c4d4f7852f970)
}

var fileDescriptor_bd8c4d4f7852f970 = []byte{
	// 343 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x92, 0x4f, 0x4b, 0xf3, 0x40,
	0x10, 0xc6, 0x9b, 0xbe, 0x2f, 0x95, 0x4c, 0x11, 0x61, 0x15, 0x4d, 0xa3, 0x42, 0xc9, 0x29, 0x2a,
	0xf4, 0xd0, 0x8b, 0xe0, 0x4d, 0xf4, 0x60, 0x0f, 0x5e, 0x52, 0x14, 0xf1, 0x12, 0xb6, 0xbb, 0xa3,
	0x06, 0xd3, 0xec, 0x76, 0xff, 0x08, 0xfd, 0x00, 0x1e, 0xfd, 0x8c, 0x7e, 0x15, 0x69, 0x9b, 0x6a,
	0x6b, 0x12, 0x29, 0x58, 0xf0, 0x36, 0xb3, 0x3b, 0x3c, 0x3c, 0xbf, 0x79, 0x06, 0xb6, 0xf5, 0x38,
	0x63, 0x71, 0x62, 0x70, 0x18, 0x33, 0x6e, 0x3a, 0x52, 0x09, 0x23, 0xc8, 0x66, 0xca, 0x84, 0x8e,
	0xa7, 0xf5, 0xc0, 0x3e, 0xf8, 0x5b, 0xd3, 0x76, 0x40, 0x35, 0xce, 0xfe, 0x83, 0x57, 0x07, 0x5a,
	0xfd, 0x71, 0xc6, 0xce, 0xad, 0x11, 0x37, 0x92, 0x53, 0x83, 0x97, 0xd4, 0xd0, 0x08, 0x47, 0x16,
	0xb5, 0x21, 0xa7, 0x00, 0x0a, 0x47, 0xf1, 0x13, 0x52, 0x8e, 0xca, 0x73, 0xda, 0xf5, 0xb0, 0xd9,
	0xf5, 0x3a, 0x4b, 0x92, 0x9d, 0x08, 0x47, 0x57, 0xd3, 0xff, 0xc8, 0x55, 0xf3, 0x92, 0xec, 0xc1,
	0x86, 0xb2, 0x29, 0xc6, 0x09, 0xf7, 0xea, 0x6d, 0x27, 0xfc, 0x1f, 0x35, 0x26, 0x6d, 0x8f, 0x93,
	0x5d, 0x68, 0x28, 0x7c, 0x4c, 0x44, 0xe6, 0xfd, 0x6b, 0x3b, 0xa1, 0x1b, 0xe5, 0x5d, 0x70, 0x07,
	0x7e, 0x99, 0x0d, 0x2d, 0x45, 0xa6, 0x91, 0x9c, 0x41, 0x53, 0xa1, 0x96, 0xcb, 0x46, 0x5a, 0x05,
	0x23, 0x5a, 0xe6, 0x4e, 0x40, 0x7d, 0xd6, 0xc1, 0x9b, 0x03, 0xfb, 0x13, 0xe9, 0x6b, 0x9a, 0x59,
	0x9a, 0xae, 0x91, 0xf1, 0x10, 0x40, 0x2a, 0xc1, 0x2d, 0x33, 0x73, 0x4c, 0x37, 0x72, 0xf3, 0x97,
	0x1f, 0x48, 0xef, 0xe1, 0xa0, 0xdc, 0xce, 0x1a, 0x58, 0x17, 0xd3, 0xbc, 0x15, 0xa9, 0x1d, 0xfe,
	0x7d, 0x9a, 0x8b, 0x36, 0x7e, 0x4f, 0xd8, 0x7d, 0xaf, 0x03, 0x99, 0x48, 0xf7, 0x0c, 0x0e, 0x2f,
	0xb8, 0xe9, 0xa3, 0x7a, 0x49, 0x18, 0x92, 0xe7, 0xd9, 0xeb, 0xf2, 0xf9, 0x90, 0xf0, 0x9b, 0x66,
	0xe5, 0xa1, 0xfb, 0x47, 0x2b, 0x4c, 0xce, 0xdc, 0x07, 0x35, 0x22, 0x60, 0xa7, 0x2c, 0x41, 0x72,
	0x5c, 0x22, 0x52, 0x71, 0x75, 0xfe, 0xc9, 0x4a, 0xb3, 0xf9, 0xc2, 0x16, 0xe8, 0xbe, 0xd6, 0x59,
	0x49, 0x57, 0x08, 0xbe, 0x92, 0xae, 0x98, 0x4d, 0x50, 0xfb, 0x08, 0x00, 0x00, 0xff, 0xff, 0x34,
	0xf9, 0xab, 0xbd, 0x47, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// SyncItemCdtServiceClient is the client API for SyncItemCdtService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SyncItemCdtServiceClient interface {
	SyncAutoUpdateData(ctx context.Context, in *SyncAutoUpdateDataRequest, opts ...grpc.CallOption) (*SyncAutoUpdateDataResponse, error)
	SyncManualUpdateData(ctx context.Context, in *SyncManualUpdateDataRequest, opts ...grpc.CallOption) (*SyncManualUpdateDataResponse, error)
	SyncAutoVolumeData(ctx context.Context, in *SyncAutoVolumeDataRequest, opts ...grpc.CallOption) (*SyncAutoVolumeDataResponse, error)
}

type syncItemCdtServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSyncItemCdtServiceClient(cc grpc.ClientConnInterface) SyncItemCdtServiceClient {
	return &syncItemCdtServiceClient{cc}
}

func (c *syncItemCdtServiceClient) SyncAutoUpdateData(ctx context.Context, in *SyncAutoUpdateDataRequest, opts ...grpc.CallOption) (*SyncAutoUpdateDataResponse, error) {
	out := new(SyncAutoUpdateDataResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.SyncItemCdtService/SyncAutoUpdateData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncItemCdtServiceClient) SyncManualUpdateData(ctx context.Context, in *SyncManualUpdateDataRequest, opts ...grpc.CallOption) (*SyncManualUpdateDataResponse, error) {
	out := new(SyncManualUpdateDataResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.SyncItemCdtService/SyncManualUpdateData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncItemCdtServiceClient) SyncAutoVolumeData(ctx context.Context, in *SyncAutoVolumeDataRequest, opts ...grpc.CallOption) (*SyncAutoVolumeDataResponse, error) {
	out := new(SyncAutoVolumeDataResponse)
	err := c.cc.Invoke(ctx, "/lcos_protobuf.SyncItemCdtService/SyncAutoVolumeData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SyncItemCdtServiceServer is the server API for SyncItemCdtService service.
type SyncItemCdtServiceServer interface {
	SyncAutoUpdateData(context.Context, *SyncAutoUpdateDataRequest) (*SyncAutoUpdateDataResponse, error)
	SyncManualUpdateData(context.Context, *SyncManualUpdateDataRequest) (*SyncManualUpdateDataResponse, error)
	SyncAutoVolumeData(context.Context, *SyncAutoVolumeDataRequest) (*SyncAutoVolumeDataResponse, error)
}

// UnimplementedSyncItemCdtServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSyncItemCdtServiceServer struct {
}

func (*UnimplementedSyncItemCdtServiceServer) SyncAutoUpdateData(ctx context.Context, req *SyncAutoUpdateDataRequest) (*SyncAutoUpdateDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncAutoUpdateData not implemented")
}
func (*UnimplementedSyncItemCdtServiceServer) SyncManualUpdateData(ctx context.Context, req *SyncManualUpdateDataRequest) (*SyncManualUpdateDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncManualUpdateData not implemented")
}
func (*UnimplementedSyncItemCdtServiceServer) SyncAutoVolumeData(ctx context.Context, req *SyncAutoVolumeDataRequest) (*SyncAutoVolumeDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncAutoVolumeData not implemented")
}

func RegisterSyncItemCdtServiceServer(s *grpc.Server, srv SyncItemCdtServiceServer) {
	s.RegisterService(&_SyncItemCdtService_serviceDesc, srv)
}

func _SyncItemCdtService_SyncAutoUpdateData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncAutoUpdateDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncItemCdtServiceServer).SyncAutoUpdateData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.SyncItemCdtService/SyncAutoUpdateData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncItemCdtServiceServer).SyncAutoUpdateData(ctx, req.(*SyncAutoUpdateDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncItemCdtService_SyncManualUpdateData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncManualUpdateDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncItemCdtServiceServer).SyncManualUpdateData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.SyncItemCdtService/SyncManualUpdateData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncItemCdtServiceServer).SyncManualUpdateData(ctx, req.(*SyncManualUpdateDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncItemCdtService_SyncAutoVolumeData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncAutoVolumeDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncItemCdtServiceServer).SyncAutoVolumeData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lcos_protobuf.SyncItemCdtService/SyncAutoVolumeData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncItemCdtServiceServer).SyncAutoVolumeData(ctx, req.(*SyncAutoVolumeDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SyncItemCdtService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lcos_protobuf.SyncItemCdtService",
	HandlerType: (*SyncItemCdtServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncAutoUpdateData",
			Handler:    _SyncItemCdtService_SyncAutoUpdateData_Handler,
		},
		{
			MethodName: "SyncManualUpdateData",
			Handler:    _SyncItemCdtService_SyncManualUpdateData_Handler,
		},
		{
			MethodName: "SyncAutoVolumeData",
			Handler:    _SyncItemCdtService_SyncAutoVolumeData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sync_item_cdt.proto",
}
