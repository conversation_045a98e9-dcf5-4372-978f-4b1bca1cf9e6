syntax = "proto2";

package lcos_protobuf;

message CDTAutoUpdateCalculateDataTabLite {
  required uint64 auto_update_rule_id = 1;
  required string product_id = 2;
  required uint32 update_event = 3;
  required uint32 is_lm = 4;
  required string three_pl_unique_key = 5;
  required string origin_region = 6;
  required int32 origin_location_id = 7;
  required string destination_region = 8;
  required int32 destination_location_id = 9;
  required int32 destination_cep_initial = 10;
  required int32 destination_cep_final = 11;
  required string destination_postcode = 12;
  required double lead_time_min = 13;
  required double lead_time_max = 14;
  required double cb_lm_lead_time_max = 15;
  required uint32 cdt_version = 16;
  required double ddl_forward_cdt = 17;
  required double ddl_backward_cdt = 18;
  required string cdt_extra_data = 19;
}

message CdtManualUpdateLocationDataTabLite {
  required uint32 is_lm = 1;
  required uint32 is_site_line = 2;
  required int32 location_level = 3;
  required int32 origin_location_id = 4;
  required int32 destination_location_id = 5;
  required string product_id = 6;
  required double lead_time_min = 7;
  required double lead_time_max = 8;
  required double cb_lm_lead_time_max = 9;
  required string region = 10;
  required string extra_data = 11;
  required string override_type = 12;
  required uint32 object_type = 13;
  required uint32 update_event = 14;
  required string cdt_extra_data = 15;
}

message CdtManualUpdatePostcodeDataTabLite {
  required uint32 is_lm = 1;
  required uint32 is_site_line = 2;
  required int32 location_level = 3;
  required int32 origin_location_id = 4;
  required string destination_postcode = 5;
  required string product_id = 6;
  required double lead_time_min = 7;
  required double lead_time_max = 8;
  required double cb_lm_lead_time_max = 9;
  required string region = 10;
  required string extra_data = 11;
  required string override_type = 12;
  required uint32 object_type = 13;
  required uint32 update_event = 14;
  required string cdt_extra_data = 15;
}