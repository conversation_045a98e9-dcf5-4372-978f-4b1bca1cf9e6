syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service LcosTPLIDLineIDRefService {
  // 通过three pl id或者line id获取到refs
  rpc QueryThreePLIDLineIDRefs(QueryThreePLIDLineIDRefRequest) returns (QueryThreePLIDLineIDRefResponse) {}
}

message QueryThreePLIDLineIDRefRequest {
  required ReqHeader  req_header = 1;
  optional string     line_id = 2;
  optional uint32     three_pl_id = 3;
}

message ThreePLIDLineIDRef {
  optional uint64 id = 1;
  optional uint32 three_pl_id = 2;
  optional string lcs_channel_id = 3;
  optional string three_pl_name = 4;
  optional uint32 three_pl_type = 5;
  optional uint32 three_pl_sub_type = 6;
  optional uint32 channel_type = 7;
  optional string line_id = 8;
  optional string line_name = 9;
  optional string lane_code = 10;
  optional uint32 cross_border_type = 11;
  optional uint32 is_virtual = 12;
  optional uint32 shipping_channel_id = 13;
  optional string shipping_channel_name = 14;
  optional uint32 progress = 15;
  optional string region = 16;
}

message QueryThreePLIDLineIDRefResponse {
  required RespHeader     resp_header = 1;
  repeated ThreePLIDLineIDRef three_pl_id_line_id_refs = 2;
}