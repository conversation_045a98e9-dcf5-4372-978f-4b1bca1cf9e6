syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service LcosDeliveryInstructionService {
  rpc BatchGetDeliveryInstructionByProductID(
      BatchGetDeliveryInstructionByProductIDRequest)
      returns (DeliveryInstructionByProductIDResponse) {}
  rpc ListDeliveryInstructionByRegion(ListDeliveryInstructionByRegionRequest)
      returns (DeliveryInstructionByRegionResponse) {}
  rpc BatchGetDeliveryInstructionBySlsTn(ListDeliveryInstructionBySlsTnRequest)
      returns (DeliveryInstructionBySlsTnResponse) {}
}

// --- 获取ProductID维度可用的delivery_instruction ---
message BatchGetDeliveryInstructionByProductIDRequest {
  required ReqHeader req_header = 1;
  repeated string product_id = 2;
}

message DeliveryInstructionByProductIDResponse {
  required RespHeader resp_header = 1;
  repeated DeliveryInstructionRespList delivery_instruction_resp_list = 2;
}

// --- 获取Region维度可用的delivery_instruction 分页---
message ListDeliveryInstructionByRegionRequest {
  required ReqHeader req_header = 1;
  required DeliveryInstructioReqByPage delivery_instruction_req = 2;
}

message DeliveryInstructioReqByPage {
  required uint32 pageno = 1;
  required uint32 count = 2;
  required string region = 3;
}

message DeliveryInstructionByRegionResponse {
  required RespHeader resp_header = 1;
  required DeliveryInstructionResp delivery_instruction_resp = 2;
}

message DeliveryInstructionResp {
  required uint32 page_no = 1;
  required uint32 count = 2;
  required uint32 total = 3;
  repeated DeliveryInstructionRespList delivery_instruction_list = 4;
}

message DeliveryInstructionRespList {
  required string product_id = 1;
  repeated AvailableDeliveryInstruction available_delivery_instruction = 2;
}

message AvailableDeliveryInstruction {
  required uint32 category =
      1;  // 1-Delivery Method，2-Logistic Support，3-Contact Method
  repeated uint32 delivery_instruction_info =
      2;  // 1-authorize_to_Leave, 2-do_not_call, 4-expedite
}

// --- 获取SlsTn维度可用的delivery_instruction ---
message ListDeliveryInstructionBySlsTnRequest {
  required ReqHeader req_header = 1;
  repeated DeliveryInstructionReqBySlsTn
      delivery_instruction_by_slstn_req_list = 2;
}

message DeliveryInstructionReqBySlsTn {
  required string sls_tn = 1;
  required string country = 2;
  required string line_id = 3;
  required uint32 otp_flag = 4;        // 1 - otp; 2 - non-otp;
  required uint32 payment_method = 5;  // 1 - cod; 2 - non-cod;
  required BuyerAddress buyer_address = 6;
  required uint32 slo_status = 7;
  optional uint32 edd = 8;
  repeated string fcodes = 9;
}

message BuyerAddress {
  optional uint32 deliver_district_id = 1;
  optional string deliver_postal_code = 2;
}

message DeliveryInstructionBySlsTnResponse {
  required RespHeader resp_header = 1;
  repeated SingleDeliveryInstructionBySlsTnResp
      delivery_instruction_by_slstn_resp_list = 2;
}

message SingleDeliveryInstructionBySlsTnResp {
  required string sls_tn = 1;
  required string line_id = 2;
  optional AvailableDeliveryInstructions available_delivery_instructions = 3;
  required uint32 retcode = 4;
  required string message = 5;
}

message AvailableDeliveryInstructions {
  repeated uint32 delivery_instructions =
      1;  // 1-authorize_to_Leave, 2-deliver_in_person
  repeated uint32 logistic_support = 2;  // 1-expedite
  repeated uint32 contact_method = 3;    // 1-contact_by_phone, 2-do_not_call
}
