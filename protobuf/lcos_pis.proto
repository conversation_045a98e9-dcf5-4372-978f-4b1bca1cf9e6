syntax = "proto2";

package lcos_protobuf;

message PisReqHeader {
  required string request_id      = 1;  // id
  required string request_ip      = 2;  // ip
  required uint32 timestamp       = 3;  // 时间戳
  required string client_id       = 4;  // 请求服务id
}

message PisRespHeader {
  required int32 retcode     = 1;  // 返回码
  required string message    = 2;  // 提示信息
  required string request_id = 3;

}

message DistributionRequest {
  required PisReqHeader  req_header = 1; //公共请求头
  required string distribution_process_id = 2;
  required string distribution_id = 3;
  required uint32 resource_id = 4;
  required string business_data = 5;
}

message DistributionResponse {
  required PisRespHeader resp_header = 1;
  optional string data = 2;
}

