syntax = "proto2";

package lcos_protobuf;


service CacheApiService {
  // 清理缓存
  rpc CleanCache(CacheApiRequest) returns (SuccessBody) {}
  // 查询缓存
  rpc GetCache(CacheApiRequest) returns (SuccessBody) {}
  // 重新加载本地缓存
  rpc RefreshLocalCache(CacheApiRequest) returns (SuccessBody) {}
}


message CacheApiRequest{
  required string namespace = 1; // local cache缓存命名空间
}


message SuccessBody {
  required string result = 1;  // 响应result
}
