syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service LcosInstallationService {
  rpc BatchGetInstallationDate(BatchGetInstallationDataRequest) returns (InstallationDateResponse) {}
}

message BatchGetInstallationDataRequest {
  required ReqHeader  req_header = 1;
  repeated InstallationItem     installation_items = 2;
}

message InstallationDateResponse {
  required RespHeader                  resp_header = 1;
  repeated DateItem date_items = 2;
}

message InstallationItem {
  required string sls_tn = 1;
  required uint32 scenario = 2; // 1 use edd, 2 use edt, 3 use default(edd + x else edt + x)
  optional uint32 edd = 3;
  optional uint32 edt = 4;
  required string line_id = 5;
  required int32 product_id = 6;
}

message DateItem {
  required string sls_tn = 1;
  repeated InstallDay available_days = 2;
}

message InstallDay {
  required uint32 timestamp_day = 1;
  repeated InstallSlot timeslots = 2;
}

message InstallSlot {
  optional uint32 start_time = 1;
  optional uint32 end_time = 2;
}
