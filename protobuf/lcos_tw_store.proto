syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service TWStoreService {
  rpc BatchGetStoreByStoreIDAndType(BatchGetStoreByStoreIDRequest) returns (BatchGetStoreByStoreIDResponse) {}
  rpc BatchGetStoreByStoreIdAndTypeWithoutStatus(BatchGetStoreByStoreIDRequest) returns (BatchGetStoreByStoreIDResponse) {}
  rpc BatchGetStoreByNewStoreIdAndType(BatchGetStoreByNewStoreIdAndTypeRequest) returns (BatchGetStoreByNewStoreIdAndTypeResponse) {}
  rpc BatchGetStoreByAddressID(BatchGetStoreByAddressIDRequest) returns (BatchGetStoreByStoreIDResponse) {}
  rpc SyncStoreByType(SyncStoreByTypeRequest) returns (SyncStoreByTypeResponse) {}
}

message ConvenienceStore {
  optional uint64        id = 1;
  optional string        store_id = 2;
  optional string        new_store_id = 3;
  optional string        name = 4;
  optional string        address = 5;
  optional uint32        store_type = 6;
  optional string        close_date = 7;
  optional string        end_date = 8;
  optional uint32        status = 9;
  optional uint64        district_id = 10;
  optional uint32        is_all_consistency = 11;
  optional string        update_date = 12;
  optional string        phone = 13;
  optional string        postal_code = 14;
  optional uint32        is_virtual = 15;
  optional string        city = 16;
  optional string        area = 17;
  optional string        district = 18;
  optional uint32        ctime = 19;
  optional uint32        mtime = 20;
  optional string        dcro_no = 21;
  optional string        rs_no = 22;
  optional string        ship_type = 23;
  optional string        path_no = 24;
  optional string        aisle_no = 25;
  optional string        grid_no = 26;
  optional string        mid_type = 27;
  optional string        ok_area = 28;
}

message SingleGetStoreByStoreID {
  required string unique_id = 1;    // 必填，且不为空
  optional uint32 store_type = 2;
  optional string store_id = 3;
  optional uint32 status = 4;
}

message BatchGetStoreByStoreIDRequest {
  required ReqHeader                 req_header = 1;
  repeated SingleGetStoreByStoreID   request_list = 2;
}

message BatchGetStoreByAddressIDRequest {
  required ReqHeader    req_header = 1;
  repeated string       request_list = 2;   // address_id的列表，需要转为string
}

message  BatchGetStoreByStoreIDResponse {
  required RespHeader                 resp_header = 1;
  map<string, ConvenienceStore>       response_dict = 2;  // key为unique id或者address id
}

message SingleGetStoreByNewStoreIdAndType {
  required string unique_id = 1;    // 必填，且不为空
  optional string new_store_id = 2;
  optional uint32 store_type = 3;
}

message BatchGetStoreByNewStoreIdAndTypeRequest {
  required ReqHeader                         req_header = 1;
  repeated SingleGetStoreByNewStoreIdAndType request_list = 2;
}

message BatchGetStoreByNewStoreIdAndTypeResponse {
  required RespHeader           resp_header = 1;
  map<string, ConvenienceStore> response_dict = 2;
}

message SyncStoreByTypeRequest {
  required ReqHeader    req_header = 1;
  required uint32       store_type = 2;
  required string       remote_file_path = 3;
}

message SyncStoreByTypeResponse {
  required RespHeader                 resp_header = 1;
}

