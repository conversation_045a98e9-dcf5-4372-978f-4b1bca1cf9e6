syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service LcosSiteServiceableAreaService {
  // 批量获取site serviceable area信息
  rpc BatchGetActualSiteIDList(BatchGetActualSiteIDListRequest) returns (BatchGetActualSiteIDListResponse) {} // 获取event code list
}

message SingleGetActualSiteIDList {
  required string     unique_id = 1;  // 必填且不能为空，标识单个请求体
  required string     site_id = 2;   // 虚拟点id
  optional uint32     state_location_id = 3;   // 层级地址
  optional uint32     city_location_id = 4;
  optional uint32     district_location_id = 5;
  optional uint32     street_location_id = 6;
  optional string     zipcode = 7;  // zipcode信息
  optional uint32     skip_postcode = 8;   // SPLN-21100 skip_postcode=1时，并且site conf=enable时，且为postcode或者cep range，则返回改site下配置的所有actual point id
  optional uint32     skip_check = 9;   // SPLN-21773 skip_postcode字段将被废弃，未来将使用skip_check。skip_check=1时，并且site conf=enable时，则返回改site下配置的所有actual point id
}

message BatchGetActualSiteIDListRequest {
  required ReqHeader  req_header = 1;
  repeated SingleGetActualSiteIDList  site_list = 2;
}

message ActualSiteList {
  repeated string actual_site_id_list = 1;
}

message BatchGetActualSiteIDListResponse {
  required RespHeader  resp_header = 1;
  map<string, ActualSiteList> actual_site_map = 2;  // 以unique id为key，值为实际点列表
}