syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service SyncItemCdtService {
    rpc SyncAutoUpdateData(SyncAutoUpdateDataRequest) returns (SyncAutoUpdateDataResponse) {};
    rpc SyncManualUpdateData(SyncManualUpdateDataRequest) returns (SyncManualUpdateDataResponse);
    rpc SyncAutoVolumeData(SyncAutoVolumeDataRequest) returns (SyncAutoVolumeDataResponse) {};
}

message SyncAutoUpdateDataRequest {
    required ReqHeader req_header = 1;
    optional uint64 rule_id = 2;
    optional string region = 3;
}

message SyncAutoUpdateDataResponse {
    required RespHeader resp_header = 1;
}

message SyncManualUpdateDataRequest {
    required ReqHeader req_header = 1;
    optional string product_id = 2;
    optional string region = 3;
}

message SyncManualUpdateDataResponse {
    required RespHeader resp_header = 1;
}

message SyncAutoVolumeDataRequest {
    required ReqHeader req_header = 1;
    optional uint64 rule_id = 2;
    optional string region = 3;
}

message SyncAutoVolumeDataResponse {
    required RespHeader resp_header = 1;
}