syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service LcosServiceableService {
  rpc CheckLineServiceableArea(CheckLineServiceableAreaRequest) returns (CheckLineServiceableAreaResponse) {}
  rpc CheckLineServiceableArea2(CheckLineServiceableAreaRequest2) returns (CheckLineServiceableAreaResponse) {}
  rpc BatchCheckLineServiceableArea(BatchCheckLineServiceableAreaRequest) returns (BatchCheckLineServiceableAreaResponse) {}
  rpc MultipleBatchCheckLineServiceableArea(MultipleBatchCheckLineServiceableAreaRequest) returns (MultipleBatchCheckLineServiceableAreaResponse) {}
  rpc GetLineServiceableArea(GetLineServiceableInfoRequest) returns (GetLineServiceableInfoResponse) {}
  rpc GetLineServiceableArea2(GetLineServiceableInfoRequest2) returns (GetLineServiceableInfoResponse) {}
  rpc BatchGetLineServiceableArea(BatchGetLineServiceableInfoRequest) returns (BatchGetLineServiceableInfoResponse) {}
  rpc BatchGetLineServiceableArea2(BatchGetLineServiceableInfoRequest2) returns (BatchGetLineServiceableInfoResponse2) {}
  rpc GetLineCollectDeliverAbility(GetLineCollectDeliverAbilityRequest) returns (GetLineCollectDeliverAbilityResponse) {}
  rpc BatchGetLineCollectDeliverAbility(BatchGetLineCollectDeliverAbilityRequest) returns (BatchGetLineCollectDeliverAbilityResponse) {}
  rpc CheckFmServiceableArea(CheckFmServiceableAreaRequest) returns (CheckFmServiceableAreaResponse) {}
  rpc BatchCheckLineServiceableArea2(BatchCheckLineServiceableAreaRequest2) returns (BatchCheckLineServiceableAreaResponse2) {}
  rpc BatchGetLaneServiceableRule(BatchGetLaneServiceableRuleRequest) returns (BatchGetLaneServiceableRuleResponse) {}
  rpc NotifyZoneInfo(NotifyZoneInfoRequest) returns (NotifyZoneInfoResponse) {}
  rpc RefreshZoneInfo(RefreshZoneInfoRequest) returns (RefreshZoneInfoResponse) {}
}

// --- 获取线服务范围请求体 ---
message CheckLineServiceableAreaRequest {
  required ReqHeader                            req_header                  = 1;
  required CheckServiceableAreaBaseInfo         base_info                   = 2;
  optional LocationInfo                         pickup_info                 = 3;
  optional LocationInfo                         deliver_info                = 4;
}

// --- v2版本 获取线服务范围请求体 ---
message CheckLineServiceableAreaRequest2 {
  required ReqHeader                            req_header                  = 1;
  required CheckServiceableAreaBaseInfo2        base_info                   = 2;
  optional LocationInfo                         pickup_info                 = 3;
  optional LocationInfo                         deliver_info                = 4;
}

// --- 获取线服务范围响应体 ---
message CheckLineServiceableAreaResponse {
  required RespHeader                     resp_header         = 1;
}

// --- deprecated v1版本base_info，包含场景参数 ---
message CheckServiceableAreaBaseInfo {
  required string     line_id                = 1;
  required uint32     is_check_operation     = 2;
  required uint32     is_check_basic         = 3;
  required uint32     payment_method         = 4;
  optional uint32     collect_type           = 5;
  optional uint32     deliver_type           = 6;
  required uint32     scenario               = 7;
  optional uint32     skip_zone_route        = 8;
}

// --- v2版本base_info ---
message CheckServiceableAreaBaseInfo2 {
  required string     line_id                = 1;
  required uint32     is_check_operation     = 2;
  required uint32     is_check_basic         = 3;
  required uint32     payment_method         = 4;
  optional uint32     collect_type           = 5;
  optional uint32     deliver_type           = 6;
  required uint32     check_sender           = 7;
  optional uint32     sender_check_level     = 8;
  required uint32     check_receiver         = 9;
  optional uint32     receiver_check_level   = 10;
  optional uint32     skip_postcode          = 11;
  optional uint32     skip_zone_route        = 12;
  optional bool       skip_electric_fence    = 13; // 是否跳过电子围栏校验【废弃】
  optional bool       use_electric_fence     = 14; // 是否启用电子围栏校验
  optional bool       check_predefined_route = 15;
  repeated string     predefined_route_codes = 16;
}

// --- 批量获取线服务范围请求体 ---
message BatchCheckLineServiceableAreaRequest {
  required ReqHeader                            req_header                  = 1;
  repeated SingleCheckServiceableAreaRequest    check_req_list              = 2;
}
// --- 多批量获取线服务范围请求体 ---
message MultipleBatchCheckLineServiceableAreaRequest {
  required ReqHeader                            req_header                  = 1;
  repeated OrderBatchCheckLineServiceableAreaRequest  order_req_list        = 2;
}
// --- 多批量获取线服务范围请求体 ---
message OrderBatchCheckLineServiceableAreaRequest {
  repeated SingleCheckServiceableAreaRequest    check_req_list              = 1;
  required string                               req_no                      = 2;
}


// --- 批量获取线服务范围的single request ---
message SingleCheckServiceableAreaRequest {
  required CheckServiceableAreaBaseInfo2        base_info                   = 1;
  optional LocationInfo                         pickup_info                 = 2;
  optional LocationInfo                         deliver_info                = 3;
}

// --- 批量获取线服务范围的响应结果 ---
message BatchCheckLineServiceableAreaResponse {
  required RespHeader                           resp_header                 = 1;
  repeated SingleCheckServiceableResponse       check_resp_list             = 2;
}

// --- 批量获取线服务范围的响应结果 ---
message MultipleBatchCheckLineServiceableAreaResponse {
  required RespHeader                           resp_header                 = 1;
  map<string, BatchCheckLineServiceableAreaResponse>       order_resp_map = 2;
}

message SingleCheckServiceableResponse {
  required string                               line_id                     = 1;
  required int32                                item_ret_code               = 2;
  required string                               item_ret_msg                = 3;
}

message GetServiceableInfoBase {
  required string     line_id                = 1;
  required uint32     is_check_operation     = 2;
  required uint32     is_check_basic         = 3;
  optional uint32     collect_type           = 4;
  optional uint32     deliver_type           = 5;
  required uint32     scenario               = 6;
  optional uint32     skip_zone_route        = 7;
}

message GetServiceableInfoBase2 {
  required string     line_id                = 1;
  required uint32     is_check_operation     = 2;
  required uint32     is_check_basic         = 3;
  optional uint32     collect_type           = 4;
  optional uint32     deliver_type           = 5;
  required uint32     check_sender           = 6;
  optional uint32     sender_check_level     = 7;
  required uint32     check_receiver         = 8;
  optional uint32     receiver_check_level   = 9;
  optional uint32     skip_postcode          = 10;
  optional uint32     skip_zone_route        = 11;
  optional bool       skip_electric_fence    = 12; // 是否跳过电子围栏校验【废弃】
  optional bool       use_electric_fence     = 13; // 是否启用电子围栏校验
  optional uint32     is_check_trade_in      = 14;
  optional bool       check_predefined_route = 15;
  repeated string     predefined_route_codes = 16;
}

message GetLineServiceableInfoRequest {
  required ReqHeader                            req_header                  = 1;
  required GetServiceableInfoBase               base_info                   = 2;
  optional LocationInfo                         pickup_info                 = 3;
  optional LocationInfo                         deliver_info                = 4;
}

message GetLineServiceableInfoRequest2 {
  required ReqHeader                            req_header                  = 1;
  required GetServiceableInfoBase2              base_info                   = 2;
  optional LocationInfo                         pickup_info                 = 3;
  optional LocationInfo                         deliver_info                = 4;
}

message GetLineServiceableInfoResponse {
  required RespHeader                           resp_header                 = 1;
  required ServiceableAreaInfo                  serviceable_info            = 2;
}

message ServiceableAreaInfo {
  required uint32       can_pickup                = 1;
  required uint32       can_cod_pickup            = 2;
  required uint32       can_deliver               = 3;
  required uint32       can_cod_deliver           = 4;
  optional uint32 pickup_in_e_fence = 5;
  optional uint32 deliver_in_e_fence = 6;
  optional uint32 can_trade_in = 7;
}

message SingleGetServiceableRequest {
  required GetServiceableInfoBase               base_info                   = 1;
  optional LocationInfo                         pickup_info                 = 2;
  optional LocationInfo                         deliver_info                = 3;
}

message SingleGetServiceableRequest2 {
  required GetServiceableInfoBase2              base_info                   = 1;
  optional LocationInfo                         pickup_info                 = 2;
  optional LocationInfo                         deliver_info                = 3;
}

message SingleGetServiceableResponse {
  required string                               line_id                     = 1;
  required ServiceableAreaInfo                  serviceable_info            = 2;
}

message SingleGetServiceableResponse2 {
  required string                               line_id                     = 1;
  required ServiceableAreaInfo                  serviceable_info            = 2;
  optional int32                                item_code                   = 3;
  optional string                               message                     = 4;
}

message BatchGetLineServiceableInfoRequest {
  required ReqHeader                            req_header                  = 1;
  repeated SingleGetServiceableRequest          serviceable_req_list        = 2;
}

message BatchGetLineServiceableInfoRequest2 {
  required ReqHeader                            req_header                  = 1;
  repeated SingleGetServiceableRequest2         serviceable_req_list        = 2;
}

message BatchGetLineServiceableInfoResponse {
  required RespHeader                           resp_header                 = 1;
  repeated SingleGetServiceableResponse         serviceable_resp_list       = 2;
}

message BatchGetLineServiceableInfoResponse2 {
  required RespHeader                           resp_header                 = 1;
  repeated SingleGetServiceableResponse2        serviceable_resp_list       = 2;
}

message GetLineCollectDeliverAbilityRequest {
  required ReqHeader                            req_header                  = 1;
  required string                               line_id                     = 2;
}

message GetLineCollectDeliverAbilityResponse {
  required RespHeader                           resp_header                 = 1;
  optional AbilityInfo                          ability_info                = 2;
}

message BatchGetLineCollectDeliverAbilityRequest {
  required ReqHeader                            req_header                  = 1;
  repeated string                               line_id                     = 2;
}
message BatchGetLineCollectDeliverAbilityResponse {
  required RespHeader                           resp_header                 = 1;
  repeated AbilityInfo                          ability_info_list           = 2;
}


message AbilityInfo {
  required string                               line_id                       = 1;
  required CollectType                          collect_type                  = 2;
  required DeliverType                          deliver_type                  = 3;
  required MaxCapacity                          max_capacity                  = 4;
  required uint32                               distance_limitation_checked   = 5;
  required uint32                               minimum_distance_operator     = 6;
  required uint32                               minimum_distance              = 7;
  required uint32                               maximum_distance_operator     = 8;
  required uint32                               maximum_distance              = 9;
}

message CollectType {
  required uint32                               pickup                      = 1;
  required uint32                               dropoff                     = 2;
  required uint32                               b2c                         = 3;
}


message DeliverType {
  required uint32                               to_home                     = 1;
  required uint32                               to_site                     = 2;
  optional uint32                               to_wms                      = 3;
  optional uint32                               to_3pl                      = 4;
  optional uint32                               to_branch                   = 5;
}

message MaxCapacity {
  required uint32                               pickup                      = 1;
  required uint32                               dropoff                     = 2;
  required uint32                               b2c                         = 3;
  required uint32                               to_home                     = 4;
  required uint32                               to_site                     = 5;
}

message CheckFmServiceableAreaRequest {
  required ReqHeader req_header = 1;
  required string line_id = 2;
  required uint32 collect_type = 3;
}

message CheckFmServiceableAreaResponse {
  required RespHeader resp_header = 1;
}

// --- 多批量获取线服务范围请求体 ---
message BatchCheckLineServiceableAreaRequest2 {
  required ReqHeader                            req_header                  = 1;
  repeated CheckLinesServiceableArea  order_req_list        = 2;
}

// --- 校验线服务范围请求体 ---
message CheckLinesServiceableArea {
  repeated CheckServiceableAreaBaseInfo2        base_info                   = 1;
  optional LocationInfo                         pickup_info                 = 2;
  optional LocationInfo                         deliver_info                = 3;
  required string                               req_no                      = 4;
}

// --- 批量获取线服务范围的响应结果 ---
message BatchCheckLineServiceableAreaResponse2 {
  required RespHeader                           resp_header                 = 1;
  map<string, BatchCheckLineServiceableAreaResponse>       order_resp_map = 2;
}

message BatchGetLaneServiceableRuleRequest {
    required ReqHeader req_header = 1;
    repeated string lane_code = 2;
}

message BatchGetLaneServiceableRuleResponse {
    required RespHeader  resp_header = 1;
    repeated ServiceableRule lane_serviceable_rule = 2;
}

message ServiceableRule {
    required string lane_code = 1;
    repeated RuleDetail rule_detail = 2;
}

message RuleDetail {
    required int32 sequence = 1;
    required int32 resource_type = 2; // 1 - site, 2 - line
    required int32 resource_sub_type = 3; // resource sub type
    optional int32 sender_use_lps = 4; // 1 - ignore check_sender return value
    optional int32 receiver_use_lps = 5; // 1 - ignore check_receiver return value
    optional int32 check_sender = 6; // 1 - yes; 0 - no;
    optional int32 check_receiver = 7; // 1 - yes; 0 - no;
    optional int32 pre_resource_sa_check_flag = 8; // 1 - yes; 0 - no
    optional int32 next_resource_sa_check_flag = 9; // 1 - yes; 0 - no
    optional int32 site_sorting_flag = 10; // 1 - on; 0 - off
    optional int32 sorting_as_pre_deliver_flag = 11; // 1 - yes; 0 - no
    optional int32 site_sorting_pickup_address_flag = 12; // 1 - use deliver address; 2 - use pickup address
    optional int32 sorting_as_next_pickup_flag = 13; // 1 - yes; 0 - no
    optional int32 site_sorting_deliver_address_flag = 14; // 1 - use deliver address; 2 - use pickup address
  optional int32 check_sender_addr_type = 15;
  optional int32 check_receiver_addr_type = 16;
}


message NotifyZoneInfoRequest {
  required ReqHeader req_header = 1;
  repeated ZoneInfo zone_list = 2;
  required int32 mode = 3;  // 0: 发生变更时增量推送，同一批数据会事务处理; 1: 定时全量推送，用于最终一致性兜底
  optional int32 force_handle = 4;  // 当这个值传入1时，强制处理这次请求，用于测试及数据异常时重新触发任务
}

message NotifyZoneInfoResponse {
  required RespHeader  resp_header = 1;
}

message ZoneInfo {
  required int64 version_id = 1;
  required int32 zone_status = 2;
  optional string operator = 3;
  optional string zone_id = 4;
  optional string zone_name = 5;
  optional string station_id = 6;
  optional int32 station_type = 7;
  optional string station_name = 8;
  optional string geometry = 9;
  optional double zone_area = 10;
  optional string layer_id = 11;
  optional string layer_name = 12;
}

// 电子围栏polygon数据刷新
message RefreshZoneInfoRequest {
  required ReqHeader req_header = 1;
  required string version_id = 2;
  required string ori_version_id = 3;
  optional string zone_id = 4;
  optional string operator = 5;
  optional string zone_name = 6;
  optional string station_id = 7;
  optional int32 station_type = 8;
  optional string station_name = 9;
  optional string geometry = 10;
  optional double zone_area = 11;
  optional string layer_id = 12;
}

message RefreshZoneInfoResponse {
  required RespHeader  resp_header = 1;
}