syntax = "proto2";

package lcos_protobuf;

import "lcos_base.proto";

service GeoDistanceCalculateService {
  rpc ListAllGeoClientConfig(ListAllGeoClientConfigRequest) returns (ListAllGeoClientConfigResponse) {};
  rpc ListAllGeoDistanceConfig(ListAllGeoDistanceConfigRequest) returns (ListAllGeoDistanceConfigResponse) {};
}

message ListAllGeoClientConfigRequest {
  required ReqHeader req_header = 1;
}

message ListAllGeoClientConfigResponse {
  required RespHeader resp_header = 1;
  repeated GeoClientConfig client_list = 2;
}

message GeoClientConfig {
  required string project = 1;
  required string scenario = 2;
  required string geo_user = 3;
  required string geo_project = 4;
  required string geo_client_key = 5;
}

message ListAllGeoDistanceConfigRequest {
  required ReqHeader req_header = 1;
}

message ListAllGeoDistanceConfigResponse {
  required RespHeader resp_header = 1;
  repeated ProductGeoDistanceConfig product_list = 2;
}

message ProductGeoDistanceConfig {
  required string product_id = 1;
  required string geo_distance_mode = 2;
  repeated string geo_distance_avoid = 3;
}
