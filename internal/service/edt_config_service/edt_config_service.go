package edt_config_service

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/edt_config_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_config"
)

type EdtConfigService interface {
	CreateProductEdtConfig(c utils.LCOSContext, request *edt_config_protocol.CreateProductEdtConfigRequest) *lcos_error.LCOSError
	UpdateProductEdtConfig(c utils.LCOSContext, request *edt_config_protocol.UpdateProductEdtConfigRequest) *lcos_error.LCOSError
	DeleteProductEdtConfig(c utils.LCOSContext, id uint64) *lcos_error.LCOSError

	GetProductEdtConfig(c utils.LCOSContext, id uint64) (*edt_config_protocol.ProductEdtConfigData, *lcos_error.LCOSError)
	ListProductEdtConfig(c utils.LCOSContext, request *edt_config_protocol.ListProductEdtConfigRequest) (*edt_config_protocol.ListProductEdtConfigResponse, *lcos_error.LCOSError)
}

type edtConfigService struct {
	EdtConfigDao edt_config.EdtConfigDao
}

func NewEdtConfigService(edtConfigDao edt_config.EdtConfigDao) *edtConfigService {
	return &edtConfigService{
		EdtConfigDao: edtConfigDao,
	}
}

func (e *edtConfigService) CreateProductEdtConfig(c utils.LCOSContext, request *edt_config_protocol.CreateProductEdtConfigRequest) *lcos_error.LCOSError {
	now := utils.GetTimestamp(c)
	region := c.GetCountry()
	email := c.GetUserEmail()
	model := &edt_config.ProductEdtConfigTab{
		Region:         region,
		ProductId:      request.ProductId,
		CutoffTimeType: request.CutoffTimeType,
		CutoffTime:     request.CutoffTime,
		Operator:       email,
		Ctime:          int64(now),
		Mtime:          int64(now),
	}

	if err := verifyProductEdtConfig(model); err != nil {
		return err
	}

	return e.EdtConfigDao.CreateProductEdtConfig(c, model)
}

func (e *edtConfigService) UpdateProductEdtConfig(c utils.LCOSContext, request *edt_config_protocol.UpdateProductEdtConfigRequest) *lcos_error.LCOSError {
	now := utils.GetTimestamp(c)
	region := c.GetCountry()
	email := c.GetUserEmail()
	model := &edt_config.ProductEdtConfigTab{
		Id:             request.Id,
		Region:         region,
		CutoffTimeType: request.CutoffTimeType,
		CutoffTime:     request.CutoffTime,
		Operator:       email,
		Mtime:          int64(now),
	}

	if err := verifyProductEdtConfig(model); err != nil {
		return err
	}

	return e.EdtConfigDao.UpdateProductEdtConfig(c, model)
}

func (e *edtConfigService) DeleteProductEdtConfig(c utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return e.EdtConfigDao.DeleteProductEdtConfig(c, id)
}

func (e *edtConfigService) GetProductEdtConfig(c utils.LCOSContext, id uint64) (*edt_config_protocol.ProductEdtConfigData, *lcos_error.LCOSError) {
	edtConfig, err := e.EdtConfigDao.GetProductEdtConfig(c, id)
	if err != nil {
		return nil, err
	}
	return convertProductEdtConfigTab(edtConfig), nil
}

func (e *edtConfigService) ListProductEdtConfig(c utils.LCOSContext, request *edt_config_protocol.ListProductEdtConfigRequest) (*edt_config_protocol.ListProductEdtConfigResponse, *lcos_error.LCOSError) {
	queryMap := make(map[string]interface{})
	region := c.GetCountry()
	queryMap["region"] = region
	if request.ProductId != nil {
		queryMap["product_id"] = *request.ProductId
	}
	total, models, err := e.EdtConfigDao.ListProductEdtConfig(c, request.Page, request.Count, queryMap)
	if err != nil {
		return nil, err
	}
	resp := &edt_config_protocol.ListProductEdtConfigResponse{
		Total: total,
	}
	for _, model := range models {
		resp.Data = append(resp.Data, convertProductEdtConfigTab(model))
	}
	return resp, nil
}

func convertProductEdtConfigTab(model *edt_config.ProductEdtConfigTab) *edt_config_protocol.ProductEdtConfigData {
	return &edt_config_protocol.ProductEdtConfigData{
		Id:             model.Id,
		Region:         model.Region,
		ProductId:      model.ProductId,
		CutoffTimeType: model.CutoffTimeType,
		CutoffTime:     model.CutoffTime,
		Operator:       model.Operator,
		Ctime:          model.Ctime,
		Mtime:          model.Mtime,
	}
}

func verifyProductEdtConfig(c *edt_config.ProductEdtConfigTab) *lcos_error.LCOSError {
	if c == nil {
		return nil
	}
	if c.CutoffTime < 0 {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "cutoff time smaller than 0")
	}
	if c.CutoffTimeType == edt_config.ProductCutoffType && c.CutoffTime == 0 {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "cutoff time should be set for product level")
	}
	return nil
}
