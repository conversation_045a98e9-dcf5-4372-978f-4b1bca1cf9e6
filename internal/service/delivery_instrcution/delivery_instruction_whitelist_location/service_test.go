package delivery_instruction_whitelist_location

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
)

func TestMain(m *testing.M) {
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv()))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		fmt.Println(err.Error())
		return
	}

	c, err := cf.InitConfig()
	if err != nil {
		fmt.Println(fmt.Sprintf("getConfig %v", err))
		return
	}

	if err := startup.InitLibs(c); err != nil {
		fmt.Println(fmt.Sprintf("InitLibs Error: %v", err))
		return
	}
	os.Exit(m.Run())
}

func Test_deliveryInstructionWhitelistLocationService_UploadDeliveryInstructionWhitelistLocation(t *testing.T) {
	type fields struct {
		deliveryInstructionWhitelistLocationDao delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationDaoInterface
		deliveryInstructionWhitelistCepRangeDao delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeDaoInterface
	}
	type args struct {
		ctx     utils.LCOSContext
		request *whitelist.UploadDeliveryInstructionWhitelistLocationRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		{
			name: "overlap=0, override=0",
			fields: fields{
				deliveryInstructionWhitelistLocationDao: delivery_instruction_whitelist_location.NewDeliveryInstructionWhitelistLocationDAO(),
				deliveryInstructionWhitelistCepRangeDao: delivery_instruction_whitelist_cep_range.NewDeliveryInstructionWhitelistCepRangeDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				//ctx: ctx,
				request: &whitelist.UploadDeliveryInstructionWhitelistLocationRequest{
					FileUrl: "https://cdn.edge.test.shopee.com/shopee_slsopsrecon_sg_test/ssc-ops/20230909/7ad42fca9eb0ea82b56fb5fd3f44defe/location_import.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Expires=86400&X-Amz-Credential=60034057%2F20230909%2Fdefault%2Fs3%2Faws4_request&X-Amz-SignedHeaders=host&X-Amz-Date=20230909T131833Z&X-Amz-Signature=9ff42cf671b83a60775049eaea3d0a935d0ba21ca3b63cfbd964c22eb6b1168e",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &deliveryInstructionWhitelistLocationService{
				deliveryInstructionWhitelistLocationDao: tt.fields.deliveryInstructionWhitelistLocationDao,
				deliveryInstructionWhitelistCepRangeDao: tt.fields.deliveryInstructionWhitelistCepRangeDao,
			}
			if got := s.UploadDeliveryInstructionWhitelistLocation(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("deliveryInstructionWhitelistLocationService.UploadDeliveryInstructionWhitelistLocation() = %v, want %v", got, tt.want)
			}
		})
	}
}
