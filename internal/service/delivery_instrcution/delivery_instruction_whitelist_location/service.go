package delivery_instruction_whitelist_location

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	delivery_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/delivery_instruction"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"os"
	"strconv"
	"strings"
	"time"
)

type DeliveryInstructionWhitelistLocationInterface interface {
	ListDeliveryInstructionWhitelistLocation(ctx utils.LCOSContext, request *whitelist.ListDeliveryInstructionWhitelistLocation) (map[string]interface{}, *lcos_error.LCOSError)
	DeleteDeliveryInstructionWhitelistLocation(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError
	UploadDeliveryInstructionWhitelistLocation(ctx utils.LCOSContext, request *whitelist.UploadDeliveryInstructionWhitelistLocationRequest) *lcos_error.LCOSError
	ListAllDeliveryInstructionWhitelistLocation(ctx utils.LCOSContext, request *whitelist.ExportDeliveryInstructionWhitelistLocation) ([]*delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab, *lcos_error.LCOSError)
}

type deliveryInstructionWhitelistLocationService struct {
	deliveryInstructionWhitelistLocationDao delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationDaoInterface
	deliveryInstructionWhitelistCepRangeDao delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeDaoInterface
}

func (d *deliveryInstructionWhitelistLocationService) ListDeliveryInstructionWhitelistLocation(ctx utils.LCOSContext, request *whitelist.ListDeliveryInstructionWhitelistLocation) (map[string]interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())

	var page uint32 = 1
	var count uint32 = 10
	if request.PageNo != nil {
		page = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}
	queryMap, err := utils.Struct2map(request)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	queryMap["region"] = region

	models, total, lcosErr := d.deliveryInstructionWhitelistLocationDao.ListDeliveryInstructionWhitelistLocationsPaging(ctx, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	return map[string]interface{}{
		"count":  count,
		"pageno": page,
		"total":  total,
		"list":   models,
	}, nil
}

func (d *deliveryInstructionWhitelistLocationService) DeleteDeliveryInstructionWhitelistLocation(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return d.deliveryInstructionWhitelistLocationDao.DeleteDeliveryInstructionWhitelistLocation(ctx, id)
}

func (d *deliveryInstructionWhitelistLocationService) UploadDeliveryInstructionWhitelistLocation(ctx utils.LCOSContext, request *whitelist.UploadDeliveryInstructionWhitelistLocationRequest) *lcos_error.LCOSError {
	region := ctx.GetCountry()
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	addLocationModelsMap := make(map[string][]*delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab)
	deleteLocationModelsMap := make(map[string][]*delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab)
	lineIDMap := make(map[string]bool)
	var lineIDList []string
	var allRows [][]string
	var errList []*lcos_error.LCOSError
	var errMessage string

	lineIdCheckErrLineMap := make(map[int]bool)
	categoryErrLineMap := make(map[int]bool)
	dupConfiguredErrLineMap := make(map[string]bool)
	mandatoryErrLineMap := make(map[int]bool)
	actionCodeErrLineMap := make(map[int]bool)
	lineMatchErrMap := make(map[int]bool)

	dupLocationErrLineMap := make(map[int]bool)
	invalidLocationErrLineMap := make(map[int]bool)
	nonExistingLocationErrLineMap := make(map[int]bool)

	for rows.Next() {
		lineNum++
		//遍历每一行
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, delivery_constant.MaxLocationColumn) {
			continue
		}
		//校验必填项是否都填写
		if errString := CheckDILocationRowData(row, region, lineNum, mandatoryErrLineMap, categoryErrLineMap, actionCodeErrLineMap, lineMatchErrMap); len(errString) != 0 {
			errMessage += errString
			continue
		}

		// 将line id去重存在在列表中，方便批量查询
		rowLineId := row[0]
		//添加行号
		row = append(row, strconv.Itoa(lineNum))
		row[6] = strconv.Itoa(lineNum)
		if _, ok := lineIDMap[rowLineId]; !ok {
			lineIDList = append(lineIDList, rowLineId)
			lineIDMap[rowLineId] = true
		}
		allRows = append(allRows, row)
	}
	if len(lineIDList) == 0 {
		if appendErrMsg := common.AppendErrMsg(mandatoryErrLineMap, delivery_constant.MandatoryErrMessage); appendErrMsg != "" {
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
		}
		if appendErrMsg := common.AppendErrMsg(categoryErrLineMap, delivery_constant.CategoryErrMessage); appendErrMsg != "" {
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
		}
		if appendErrMsg := common.AppendErrMsg(actionCodeErrLineMap, delivery_constant.ActionCodeErrMessage); appendErrMsg != "" {
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
		}
		if appendErrMsg := common.AppendErrMsg(lineMatchErrMap, delivery_constant.LineMatchErrMessage); appendErrMsg != "" {
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
		}
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromListWithSpace(errList))
	}

	//请求lls 获取所有的line信息
	lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIDList)
	if lcosErr != nil {
		return lcosErr
	}
	if lineInfoMap == nil {
		lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	}
	// 请求lls获取所有的草稿态lineDraft信息 更新lineinfoMap
	lineDraftMap, lcosErr := lls_service.BatchGetLineDraftsMap(ctx, lineIDList)
	if lcosErr != nil {
		return lcosErr
	}
	for k, v := range lineDraftMap {
		lineInfoMap[k] = v
	}

	// 检查line_id +category  是否有配置cepRange
	// 获取line_id cepRange的信息，并且存为map
	lineIDCepRangeConfMap := make(map[string]bool) // lineID -> line cepRange bool
	lineIDCepRangeConfs, lcosErr := d.deliveryInstructionWhitelistCepRangeDao.ListAllDeliveryInstructionWhitelistCepRanges(ctx, map[string]interface{}{"line_id in": lineIDList})
	if lcosErr != nil {
		return lcosErr
	}
	for _, singleLineIDCepRangeConf := range lineIDCepRangeConfs {
		uniqKey := fmt.Sprintf("%s:%d", singleLineIDCepRangeConf.LineId, singleLineIDCepRangeConf.Category)
		lineIDCepRangeConfMap[uniqKey] = true
	}

	//解析每一行数据到add 、del Locations
	for _, rowData := range allRows {
		tmpErrList := d.parseSingleDeliveryInstructionWhitelistLocation(ctx, region, rowData, addLocationModelsMap, deleteLocationModelsMap, lineInfoMap, lineIdCheckErrLineMap, invalidLocationErrLineMap)
		if tmpErrList != nil {
			errList = append(errList, tmpErrList...)
		}
		//校验是否有cepRange配置 Line id+Category
		uniqKey := fmt.Sprintf("%s:%s", rowData[0], rowData[1])
		if exist, ok := lineIDCepRangeConfMap[uniqKey]; ok && exist {
			dupConfiguredErrLineMap[rowData[6]] = true
		}
	}

	// 获取已有的lineID -> location conf的信息，并且存为map
	lineIDLocationConfs, lcosErr := d.deliveryInstructionWhitelistLocationDao.ListAllDeliveryInstructionWhitelistLocations(ctx, map[string]interface{}{"line_id in": lineIDList})
	if lcosErr != nil {
		return lcosErr
	}
	nowLineIDLocationMap := make(map[string]bool)
	for _, singleLineIDLocationConf := range lineIDLocationConfs {
		uniqKey := fmt.Sprintf("%s:%d:%d", singleLineIDLocationConf.LineId, singleLineIDLocationConf.Category, singleLineIDLocationConf.LocationId)
		nowLineIDLocationMap[uniqKey] = true
	}

	//检查本次上传是否有新增重复地址 相对已有配置、与本次上传配置
	addMap := make(map[string]bool)
	for _, locationList := range addLocationModelsMap {
		for _, loc := range locationList {
			duplicateFlag := false
			uniqKey := fmt.Sprintf("%s:%d:%d", loc.LineId, loc.Category, loc.LocationId)
			if exist, ok := nowLineIDLocationMap[uniqKey]; ok && exist {
				duplicateFlag = true
				errMsg := fmt.Sprintf("Duplicate with db location in row %d, please re-confirm.|line_id=%s, category=%d, location_id=%d", loc.RowId, loc.LineId, loc.Category, loc.LocationId)
				logger.CtxLogErrorf(ctx, errMsg)
			}

			if exist, ok := addMap[uniqKey]; ok && exist {
				duplicateFlag = true
				errMsg := fmt.Sprintf("Duplicate with now add location in row %d, please re-confirm.|line_id=%s, category=%d, location_id=%d", loc.RowId, loc.LineId, loc.Category, loc.LocationId)
				logger.CtxLogErrorf(ctx, errMsg)
			} else {
				addMap[uniqKey] = true
			}

			if duplicateFlag {
				dupLocationErrLineMap[loc.RowId] = true
			}
		}
	}

	//检查本次上传是否有删除重复地址
	deleteMap := make(map[string]bool)
	for _, deleteLocationModel := range deleteLocationModelsMap {
		for _, loc := range deleteLocationModel {
			uniqKey := fmt.Sprintf("%s:%d:%d", loc.LineId, loc.Category, loc.LocationId)
			if exist, ok := deleteMap[uniqKey]; ok && exist {
				//不可重复删除
				dupLocationErrLineMap[loc.RowId] = true
			} else {
				//本次删除
				deleteMap[uniqKey] = true
			}

			//只可删除现有已配置的或本次新加的
			_, existNow := nowLineIDLocationMap[uniqKey] //in db
			_, existNewAdd := addMap[uniqKey]            //in add
			if !existNow && !existNewAdd {
				nonExistingLocationErrLineMap[loc.RowId] = true
			}
		}
	}

	if appendErrMsg := common.AppendErrMsg(lineIdCheckErrLineMap, delivery_constant.LineIdCheckErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(categoryErrLineMap, delivery_constant.CategoryErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendStringErrMsg(dupConfiguredErrLineMap, delivery_constant.DupConfiguredErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(mandatoryErrLineMap, delivery_constant.MandatoryErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(actionCodeErrLineMap, delivery_constant.ActionCodeErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(lineMatchErrMap, delivery_constant.LineMatchErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}

	if appendErrMsg := common.AppendErrMsg(dupLocationErrLineMap, delivery_constant.DupLocationErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(invalidLocationErrLineMap, delivery_constant.InvalidLocationErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(nonExistingLocationErrLineMap, delivery_constant.NonExistingLocationErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromListWithSpace(errList))
	}

	//开启事务，更新DB
	fc := func() *lcos_error.LCOSError {
		for _, addLocationModels := range addLocationModelsMap {
			if err := d.deliveryInstructionWhitelistLocationDao.BatchCreateDeliveryInstructionWhitelistLocation(ctx, addLocationModels); err != nil {
				return err
			}
		}
		for _, deleteLocationModels := range deleteLocationModelsMap {
			if err := d.deliveryInstructionWhitelistLocationDao.BatchDeleteDeliveryInstructionWhitelistLocation(ctx, deleteLocationModels); err != nil {
				return err
			}
		}
		return nil
	}
	return ctx.Transaction(fc)
}

func (d *deliveryInstructionWhitelistLocationService) ListAllDeliveryInstructionWhitelistLocation(ctx utils.LCOSContext, request *whitelist.ExportDeliveryInstructionWhitelistLocation) ([]*delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	queryMap, err := utils.Struct2map(request)
	queryMap["region"] = region
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	models, lcosErr := d.deliveryInstructionWhitelistLocationDao.ListAllDeliveryInstructionWhitelistLocations(ctx, queryMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return models, nil
}

func (d *deliveryInstructionWhitelistLocationService) parseSingleDeliveryInstructionWhitelistLocation(ctx utils.LCOSContext, region string, rowData []string,
	addLocationModelsMap map[string][]*delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab,
	deleteLocationModelsMap map[string][]*delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab,
	lineInfoMap map[string]*llspb.GetLineInfoResponseData, lineIdCheckErrLineMap, invalidLocationErrLineMap map[int]bool) []*lcos_error.LCOSError {
	var errList []*lcos_error.LCOSError
	//校验lineid是否存在
	rowLineId := rowData[0]
	rowIdAdd, err := strconv.Atoi(rowData[6])
	if err != nil {
		errMsg := fmt.Sprintf("strconv Row %d err,err = %s", rowIdAdd, err.Error())
		logger.CtxLogErrorf(ctx, errMsg)
	}
	operator := ctx.GetUserEmail()
	var errMsgAns string

	// 检查lineid是否存在进行校验
	if _, ok := lineInfoMap[rowLineId]; !ok {
		lineIdCheckErrLineMap[rowIdAdd] = true
		errMsgAns += fmt.Sprintf("[Row %d] Invalid line id.", rowIdAdd)
	}

	rowData[1] = strings.ToUpper(rowData[1])
	var category int
	switch rowData[1] {
	case delivery_constant.DeliveryMethodNameUpper:
		category = delivery_constant.DeliveryMethod
	case delivery_constant.LogisticSupportNameUpper:
		category = delivery_constant.LogisticSupport
	case delivery_constant.ContactMethodNameUpper:
		category = delivery_constant.ContactMethod
	default:
		errMsg := fmt.Sprintf("Invalid category, please re-confirm.in Row %d,category = %s", rowIdAdd, rowData[1])
		logger.CtxLogErrorf(ctx, errMsg)
	}
	//categoty转为枚举值
	rowData[1] = strconv.Itoa(category)

	var state, city, district, street string
	state = rowData[2]
	city = rowData[3]
	district = rowData[4]
	street = ""
	actionCode := rowData[5]

	locationInfo, lcosErr := address_service.LocationServer.GetLocationInfoByName(ctx, region, state, city, district, street)
	if lcosErr != nil {
		if lcosErr.RetCode == lcos_error.CdtAddressNotFoundErrorCode {
			invalidLocationErrLineMap[rowIdAdd] = true
			errMsg := fmt.Sprintf("get location err,err = %s", lcosErr.Msg)
			logger.CtxLogErrorf(ctx, errMsg)
		} else {
			errList = append(errList, lcosErr)
		}
	} else {
		//根据同一行的地址信息查询,找到location id对应的district id
		var locationID int
		if locationInfo.DistrictLocationId != 0 {
			locationID = locationInfo.DistrictLocationId
		}
		if locationID == 0 {
			//找不到对应的locationID
			errMsg := fmt.Sprintf("location is not valid|region=%s, state=%s, city=%s, district=%s, street=%s", region, state, city, district, street)
			logger.CtxLogErrorf(ctx, errMsg)
			invalidLocationErrLineMap[rowIdAdd] = true
			errMsgAns += fmt.Sprintf("[Row %d] Invalid location in row %d.", rowIdAdd, rowIdAdd)
		}
		if len(errMsgAns) != 0 {
			return errList
		}

		//考虑找不到地址时，不添加数据，这里保留errMsgAns记录错误信息
		deliveryInstructionLocationModel := &delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab{
			LineId:     rowLineId,
			Category:   int8(category),
			Region:     region,
			LocationId: locationID,
			State:      locationInfo.State,
			City:       locationInfo.City,
			District:   locationInfo.District,
			Operator:   operator,
			RowId:      rowIdAdd,
		}

		if actionCode == delivery_constant.AddAction {
			addLocationModelsMap[rowLineId] = append(addLocationModelsMap[rowLineId], deliveryInstructionLocationModel)
		} else {
			deleteLocationModelsMap[rowLineId] = append(deleteLocationModelsMap[rowLineId], deliveryInstructionLocationModel)
		}
	}
	return errList
}

func NewDeliveryInstructionWhitelistLocationService(deliveryInstructionWhitelistLocationDao delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationDaoInterface,
	deliveryInstructionWhitelistCepRangeDao delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeDaoInterface) *deliveryInstructionWhitelistLocationService {
	return &deliveryInstructionWhitelistLocationService{
		deliveryInstructionWhitelistLocationDao: deliveryInstructionWhitelistLocationDao,
		deliveryInstructionWhitelistCepRangeDao: deliveryInstructionWhitelistCepRangeDao,
	}
}

var _ DeliveryInstructionWhitelistLocationInterface = (*deliveryInstructionWhitelistLocationService)(nil)
