package common

import (
	"fmt"
	"strconv"
	"strings"
)

func GetCombinedDigits(num int8) []int8 {
	digits := make([]int8, 0)
	for num > 0 {
		digit := num & -num // 获取最低位的数字
		digits = append(digits, int8(digit))
		num -= digit
	}
	return digits
}

func AppendErrMsg(lineMap map[int]bool, typeErrMsg string) string {
	if len(lineMap) == 0 {
		return ""
	}

	var lineList []string
	for lineId := range lineMap {
		lineList = append(lineList, strconv.Itoa(lineId))
	}
	errMsg := fmt.Sprintf(typeErrMsg, strings.Join(lineList, ","))
	return errMsg
}

func AppendStringErrMsg(lineMap map[string]bool, typeErrMsg string) string {
	if len(lineMap) == 0 {
		return ""
	}

	var lineList []string
	for lineId := range lineMap {
		lineList = append(lineList, lineId)
	}
	errMsg := fmt.Sprintf(typeErrMsg, strings.Join(lineList, ","))
	return errMsg
}

func IsBlankRow(rowData []string, maxColumn int) bool {
	for i := 0; i < maxColumn; i++ {
		if len(strings.TrimSpace(rowData[i])) > 0 {
			return false
		}
	}
	return true
}
