package delivery_instruction_whitelist_cep_range

import (
	"fmt"
	delivery_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/delivery_instruction"
	"strconv"
	"strings"
)

func CheckDICepRangeRowData(rowData []string, region string, lineNum int, mandatoryErrLineMap, categoryErrLineMap, actionCodeErrLineMap, lineMatchErrMap map[int]bool) string {
	//Line id	Category	initial	  final	     Action Code
	var errString string
	missingFlag := false

	if len(rowData) < delivery_constant.MaxCepRangeColumn {
		missingFlag = true
	} else {
		for i := 0; i < delivery_constant.MaxCepRangeColumn; i++ {
			rowData[i] = strings.TrimSpace(rowData[i])
			if rowData[i] == "" {
				missingFlag = true
				break
			}
		}
	}

	if missingFlag {
		mandatoryErrLineMap[lineNum] = true
		errString += fmt.Sprintf("[Row %d] Mandatory data is missing.", lineNum)
	}

	if len(rowData) >= 1 {
		//lineId没有包含该country
		rowLineId := strings.TrimSpace(rowData[0])
		rowLineId = strings.ToUpper(rowLineId)
		rowData[0] = rowLineId
		if !strings.Contains(rowLineId, region) {
			lineMatchErrMap[lineNum] = true
			errString += fmt.Sprintf("[Row %d] The line id does not belong to the current region.", lineNum)
		}
	}

	if len(rowData) >= 2 {
		category := strings.TrimSpace(rowData[1])
		category = strings.ToUpper(category)
		if !(category == delivery_constant.DeliveryMethodNameUpper || category == delivery_constant.LogisticSupportNameUpper || category == delivery_constant.ContactMethodNameUpper) {
			categoryErrLineMap[lineNum] = true
			errString += fmt.Sprintf("[Row %d] Invalid category.", lineNum)
		}
	}

	if len(rowData) >= delivery_constant.MaxCepRangeColumn {
		if actionCode, err := strconv.Atoi(rowData[4]); err != nil || !(actionCode == 1 || actionCode == -1) {
			actionCodeErrLineMap[lineNum] = true
			errString += fmt.Sprintf("[Row %d] Invalid action code.", lineNum)
		}
	}

	return errString
}
