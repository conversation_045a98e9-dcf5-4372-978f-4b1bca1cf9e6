package delivery_instruction_whitelist_cep_range

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	delivery_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/delivery_instruction"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/intervaltree"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

type DeliveryInstructionWhitelistCepRangeInterface interface {
	ListDeliveryInstructionWhitelistCepRange(ctx utils.LCOSContext, request *whitelist.ListDeliveryInstructionWhitelistCepRange) (map[string]interface{}, *lcos_error.LCOSError)
	DeleteDeliveryInstructionWhitelistCepRange(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError
	UploadDeliveryInstructionWhitelistCepRange(ctx utils.LCOSContext, request *whitelist.UploadDeliveryInstructionWhitelistCepRangeRequest) *lcos_error.LCOSError
	ListAllDeliveryInstructionWhitelistCepRange(ctx utils.LCOSContext, request *whitelist.ExportDeliveryInstructionWhitelistCepRange) ([]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab, *lcos_error.LCOSError)
}

type deliveryInstructionWhitelistCepRangeService struct {
	deliveryInstructionWhitelistCepRangeDao delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeDaoInterface
	deliveryInstructionWhitelistLocationDao delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationDaoInterface
}

func (d *deliveryInstructionWhitelistCepRangeService) ListDeliveryInstructionWhitelistCepRange(ctx utils.LCOSContext, request *whitelist.ListDeliveryInstructionWhitelistCepRange) (map[string]interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())

	var page uint32 = 1
	var count uint32 = 10
	if request.PageNo != nil {
		page = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}
	queryMap, err := utils.Struct2map(request)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	// cep code需要转为or的sql模式
	if request.CepCode != nil && *request.CepCode != 0 {
		queryMap["initial <="] = *request.CepCode
		queryMap["final >="] = *request.CepCode
		delete(queryMap, "cep_code")
	}
	queryMap["region"] = region

	models, total, lcosErr := d.deliveryInstructionWhitelistCepRangeDao.ListDeliveryInstructionWhitelistCepRangesPaging(ctx, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	return map[string]interface{}{
		"count":  count,
		"pageno": page,
		"total":  total,
		"list":   models,
	}, nil

}

func (d *deliveryInstructionWhitelistCepRangeService) DeleteDeliveryInstructionWhitelistCepRange(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return d.deliveryInstructionWhitelistCepRangeDao.DeleteDeliveryInstructionWhitelistCepRange(ctx, id)
}

func (d *deliveryInstructionWhitelistCepRangeService) UploadDeliveryInstructionWhitelistCepRange(ctx utils.LCOSContext, request *whitelist.UploadDeliveryInstructionWhitelistCepRangeRequest) *lcos_error.LCOSError {
	region := ctx.GetCountry()
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	var errList []*lcos_error.LCOSError
	addCepRangeModelsMap := make(map[string][]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab)
	deleteCepRangeModelsMap := make(map[string][]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab)
	lineIDMap := make(map[string]bool)
	var lineIDList []string
	var allRows [][]string
	var errMessage string

	lineIdCheckErrLineMap := make(map[int]bool)
	categoryErrLineMap := make(map[int]bool)
	dupConfiguredErrLineMap := make(map[string]bool)
	mandatoryErrLineMap := make(map[int]bool)
	actionCodeErrLineMap := make(map[int]bool)
	lineMatchErrMap := make(map[int]bool)

	newlyAddedOverlapErrLineMap := make(map[int]bool)
	tobeDeletedOverlapErrLineMap := make(map[int]bool)
	nonExistingErrLineMap := make(map[int]bool)
	cepCodeUploadErrLineMap := make(map[int]bool)

	for rows.Next() {
		lineNum++
		//遍历每一行
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, delivery_constant.MaxCepRangeColumn) {
			continue
		}
		//校验必填项是否都填写
		if errString := CheckDICepRangeRowData(row, region, lineNum, mandatoryErrLineMap, categoryErrLineMap, actionCodeErrLineMap, lineMatchErrMap); len(errString) != 0 {
			errMessage += errString
			continue
		}

		// 将line id去重存在在列表中，方便批量查询
		rowLineId := row[0]
		//添加行号
		row = append(row, strconv.Itoa(lineNum))
		row[5] = strconv.Itoa(lineNum)
		if _, ok := lineIDMap[rowLineId]; !ok {
			lineIDList = append(lineIDList, rowLineId)
			lineIDMap[rowLineId] = true
		}
		allRows = append(allRows, row)
	}

	if len(lineIDList) == 0 {
		if appendErrMsg := common.AppendErrMsg(mandatoryErrLineMap, delivery_constant.MandatoryErrMessage); appendErrMsg != "" {
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
		}
		if appendErrMsg := common.AppendErrMsg(categoryErrLineMap, delivery_constant.CategoryErrMessage); appendErrMsg != "" {
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
		}
		if appendErrMsg := common.AppendErrMsg(actionCodeErrLineMap, delivery_constant.ActionCodeErrMessage); appendErrMsg != "" {
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
		}
		if appendErrMsg := common.AppendErrMsg(lineMatchErrMap, delivery_constant.LineMatchErrMessage); appendErrMsg != "" {
			errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
		}
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromListWithSpace(errList))
	}

	//前置数据准备
	//1.1 请求lls 获取所有的line信息 line id -> line info
	lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIDList)
	if lcosErr != nil {
		return lcosErr
	}
	if lineInfoMap == nil {
		lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	}
	// 请求lls获取所有的草稿态lineDraft信息 更新lineinfoMap
	lineDraftMap, lcosErr := lls_service.BatchGetLineDraftsMap(ctx, lineIDList)
	if lcosErr != nil {
		return lcosErr
	}
	for k, v := range lineDraftMap {
		lineInfoMap[k] = v
	}

	// 1.2 检查line_id +category 是否有配置location
	// 获取line_id location的信息，并且存为map
	lineIDLocationConfMap := make(map[string]bool) // lineID -> line location conf
	lineIDLocationConfs, lcosErr := d.deliveryInstructionWhitelistLocationDao.ListAllDeliveryInstructionWhitelistLocations(ctx, map[string]interface{}{"line_id in": lineIDList})
	if lcosErr != nil {
		return lcosErr
	}
	for _, singleLineIDLocationConf := range lineIDLocationConfs {
		uniqKey := fmt.Sprintf("%s:%d", singleLineIDLocationConf.LineId, singleLineIDLocationConf.Category)
		lineIDLocationConfMap[uniqKey] = true
	}

	//2. 解析每一行数据到add 、del CepRanges
	for _, rowData := range allRows {
		tmpErrList := d.parseSingleDeliveryInstructionWhitelistCepRange(ctx, region, rowData, addCepRangeModelsMap, deleteCepRangeModelsMap, lineInfoMap, lineIdCheckErrLineMap, cepCodeUploadErrLineMap)
		if tmpErrList != nil {
			errList = append(errList, tmpErrList...)
		}
		//校验是否有location配置
		uniqKey := fmt.Sprintf("%s:%s", rowData[0], rowData[1])
		if exist, ok := lineIDLocationConfMap[uniqKey]; ok && exist {
			dupConfiguredErrLineMap[rowData[5]] = true
			continue
		}
	}

	// 3.1 分别校验上传的新增数据、删除数据是否自重叠
	addConditionMap := make(map[string]*intervaltree.IntervalTree)
	delConditionMap := make(map[string]*intervaltree.IntervalTree)
	for _, addCepRangeList := range addCepRangeModelsMap {
		for _, cepRangeInfo := range addCepRangeList {
			conditionKey := fmt.Sprintf("%s:%d", cepRangeInfo.LineId, cepRangeInfo.Category)
			if tree, ok := addConditionMap[conditionKey]; !ok {
				tree := intervaltree.New()
				_ = tree.Insert(cepRangeInfo.Initial, cepRangeInfo.Final)
				addConditionMap[conditionKey] = tree
			} else {
				if tree.Contains(cepRangeInfo.Final) || tree.Contains(cepRangeInfo.Initial) {
					newlyAddedOverlapErrLineMap[cepRangeInfo.RowId] = true
				} else {
					err := tree.Insert(cepRangeInfo.Initial, cepRangeInfo.Final)
					if err != nil {
						newlyAddedOverlapErrLineMap[cepRangeInfo.RowId] = true
						errMsg := fmt.Sprintf("tree insert Cep-Range overlap in row %d, please re-confirm.err = %v", cepRangeInfo.RowId, err.Error())
						logger.CtxLogErrorf(ctx, errMsg)
					}
				}
			}
		}
	}

	for _, deleteCepRangeList := range deleteCepRangeModelsMap {
		for _, cepRangeInfo := range deleteCepRangeList {
			conditionKey := fmt.Sprintf("%s:%d", cepRangeInfo.LineId, cepRangeInfo.Category)
			if tree, ok := delConditionMap[conditionKey]; !ok {
				tree := intervaltree.New()
				_ = tree.Insert(cepRangeInfo.Initial, cepRangeInfo.Final)
				delConditionMap[conditionKey] = tree
			} else {
				if tree.Contains(cepRangeInfo.Final) || tree.Contains(cepRangeInfo.Initial) {
					tobeDeletedOverlapErrLineMap[cepRangeInfo.RowId] = true
				} else {
					err := tree.Insert(cepRangeInfo.Initial, cepRangeInfo.Final)
					if err != nil {
						tobeDeletedOverlapErrLineMap[cepRangeInfo.RowId] = true
						errMsg := fmt.Sprintf("[Row %d] Overlap of data to be deleted.err is %v", cepRangeInfo.RowId, err.Error())
						logger.CtxLogErrorf(ctx, errMsg)
					}
				}
			}
		}
	}

	// 3.2 分别校验上传的新增数据、删除数据是否和已有数据有重叠
	// 3.2.1 获取line_id cepRange的信息，并且存为map
	lineIDCepRangeConfs, lcosErr := d.deliveryInstructionWhitelistCepRangeDao.ListAllDeliveryInstructionWhitelistCepRanges(ctx, map[string]interface{}{"line_id in": lineIDList})
	if lcosErr != nil {
		return lcosErr
	}
	nowLineIDCepRangeMap := make(map[string][]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab)
	for _, singleLineIDCepRangeConf := range lineIDCepRangeConfs {
		uniqKey := fmt.Sprintf("%s:%d", singleLineIDCepRangeConf.LineId, singleLineIDCepRangeConf.Category)
		if _, ok := nowLineIDCepRangeMap[uniqKey]; !ok {
			nowLineIDCepRangeMap[uniqKey] = []*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab{}
		}
		nowLineIDCepRangeMap[uniqKey] = append(nowLineIDCepRangeMap[uniqKey], singleLineIDCepRangeConf)
	}

	// 3.2.2 对每组区间进行排序
	for key := range nowLineIDCepRangeMap {
		sort.SliceStable(nowLineIDCepRangeMap[key], func(i, j int) bool {
			return nowLineIDCepRangeMap[key][i].Initial < nowLineIDCepRangeMap[key][j].Initial
		})
	}

	// 3.2.3 校验每个新增区间是否和已有区间重叠
	for _, addCepRangeList := range addCepRangeModelsMap {
		for _, addCepRange := range addCepRangeList {
			key := fmt.Sprintf("%s:%d", addCepRange.LineId, addCepRange.Category)
			if overlapped, _ := d.checkSingleCepRangeLineOverlap(ctx, addCepRange, nowLineIDCepRangeMap[key]); overlapped {
				newlyAddedOverlapErrLineMap[addCepRange.RowId] = true
			}
		}
	}

	// 3.2.4 校验每个删除区间是否和已有区间重叠
	for _, delCepRangeList := range deleteCepRangeModelsMap {
		for _, delCepRange := range delCepRangeList {
			key := fmt.Sprintf("%s:%d", delCepRange.LineId, delCepRange.Category)
			exitNew := d.checkSingleCepRangeLineSame(ctx, delCepRange, addCepRangeModelsMap[delCepRange.LineId])
			exitDB := d.checkSingleCepRangeLineSame(ctx, delCepRange, nowLineIDCepRangeMap[key])
			if !exitNew && !exitDB {
				nonExistingErrLineMap[delCepRange.RowId] = true
				// 对于需要删除的不存在的区间，检查是否有overlap (和已有区间 及本次新增区间）
				overlappedNew := d.checkSingleCepRangeLineOverlapWithoutSort(ctx, delCepRange, addCepRangeModelsMap[delCepRange.LineId])
				overlappedDB, _ := d.checkSingleCepRangeLineOverlap(ctx, delCepRange, nowLineIDCepRangeMap[key])
				if overlappedNew || overlappedDB {
					tobeDeletedOverlapErrLineMap[delCepRange.RowId] = true
				}
			}
		}
	}

	if appendErrMsg := common.AppendErrMsg(lineIdCheckErrLineMap, delivery_constant.LineIdCheckErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(categoryErrLineMap, delivery_constant.CategoryErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendStringErrMsg(dupConfiguredErrLineMap, delivery_constant.DupConfiguredErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(mandatoryErrLineMap, delivery_constant.MandatoryErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(actionCodeErrLineMap, delivery_constant.ActionCodeErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(lineMatchErrMap, delivery_constant.LineMatchErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}

	if appendErrMsg := common.AppendErrMsg(newlyAddedOverlapErrLineMap, delivery_constant.AddedDataErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(tobeDeletedOverlapErrLineMap, delivery_constant.ToBeDeletedErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(nonExistingErrLineMap, delivery_constant.NonExistingErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}
	if appendErrMsg := common.AppendErrMsg(cepCodeUploadErrLineMap, delivery_constant.CepRangeUploadErrMessage); appendErrMsg != "" {
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, appendErrMsg))
	}

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromListWithSpace(errList))
	}
	//开启事务，更新DB
	fc := func() *lcos_error.LCOSError {
		for _, addCepRangeModels := range addCepRangeModelsMap {
			if err := d.deliveryInstructionWhitelistCepRangeDao.BatchCreateDeliveryInstructionWhitelistCepRange(ctx, addCepRangeModels); err != nil {
				return err
			}
		}
		for _, deleteCepRangeModels := range deleteCepRangeModelsMap {
			if err := d.deliveryInstructionWhitelistCepRangeDao.BatchDeleteDeliveryInstructionWhitelistCepRange(ctx, deleteCepRangeModels); err != nil {
				return err
			}
		}
		return nil
	}
	return ctx.Transaction(fc)
}

func (d *deliveryInstructionWhitelistCepRangeService) checkSingleCepRangeLineOverlap(ctx utils.LCOSContext, singleCepRangeWhiteList *delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab, cepRangeWhiteLists []*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab) (bool, int) {
	startIndex := 0
	endIndex := len(cepRangeWhiteLists) - 1
	for startIndex <= endIndex {
		mid := (startIndex + endIndex) / 2
		// 有重叠
		if cepRangeWhiteLists[mid].Initial <= singleCepRangeWhiteList.Final && singleCepRangeWhiteList.Initial <= cepRangeWhiteLists[mid].Final {
			return true, mid
		} else {
			if singleCepRangeWhiteList.Final < cepRangeWhiteLists[mid].Initial {
				endIndex = mid - 1
			} else {
				startIndex = mid + 1
			}
		}
	}
	return false, -1
}

func (d *deliveryInstructionWhitelistCepRangeService) checkSingleCepRangeLineOverlapWithoutSort(ctx utils.LCOSContext, singleCepRangeWhiteList *delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab,
	cepRangeWhiteLists []*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab) bool {
	for _, cepRangeWhiteList := range cepRangeWhiteLists {
		if cepRangeWhiteList.Category != singleCepRangeWhiteList.Category {
			continue
		}
		if cepRangeWhiteList.Initial <= singleCepRangeWhiteList.Final && singleCepRangeWhiteList.Initial <= cepRangeWhiteList.Final {
			return true
		}
	}
	return false
}

func (d *deliveryInstructionWhitelistCepRangeService) checkSingleCepRangeLineSame(ctx utils.LCOSContext, singleCepRangeWhiteList *delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab,
	cepRangeWhiteLists []*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab) bool {
	for _, cepRangeWhiteList := range cepRangeWhiteLists {
		if cepRangeWhiteList.Category != singleCepRangeWhiteList.Category {
			continue
		}
		if cepRangeWhiteList.Initial == singleCepRangeWhiteList.Initial && cepRangeWhiteList.Final == singleCepRangeWhiteList.Final {
			return true
		}
	}
	return false
}

func (d *deliveryInstructionWhitelistCepRangeService) ListAllDeliveryInstructionWhitelistCepRange(ctx utils.LCOSContext, request *whitelist.ExportDeliveryInstructionWhitelistCepRange) ([]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	queryMap, err := utils.Struct2map(request)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	// cep code需要转为or的sql模式
	if request.CepCode != nil && *request.CepCode != 0 {
		queryMap["initial <="] = *request.CepCode
		queryMap["final >="] = *request.CepCode
		delete(queryMap, "cep_code")
	}
	queryMap["region"] = region

	models, lcosErr := d.deliveryInstructionWhitelistCepRangeDao.ListAllDeliveryInstructionWhitelistCepRanges(ctx, queryMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return models, nil
}

func (d *deliveryInstructionWhitelistCepRangeService) parseSingleDeliveryInstructionWhitelistCepRange(ctx utils.LCOSContext, region string, rowData []string,
	addCepRangeModelsMap map[string][]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab,
	deleteCepRangeModelsMap map[string][]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab,
	lineInfoMap map[string]*llspb.GetLineInfoResponseData, lineIdCheckErrLineMap, cepCodeUploadErrLineMap map[int]bool) []*lcos_error.LCOSError {
	var errList []*lcos_error.LCOSError
	//校验lineid是否存在
	rowLineId := rowData[0]
	rowIdAdd, err := strconv.Atoi(rowData[5])
	if err != nil {
		errMsg := fmt.Sprintf("strconv Row %d err,err = %s", rowIdAdd, err.Error())
		logger.CtxLogErrorf(ctx, errMsg)
	}
	operator := ctx.GetUserEmail()

	// 检查lineid是否存在进行校验
	if _, ok := lineInfoMap[rowLineId]; !ok {
		lineIdCheckErrLineMap[rowIdAdd] = true
	}

	rowData[1] = strings.ToUpper(rowData[1])
	var category int
	switch rowData[1] {
	case delivery_constant.DeliveryMethodNameUpper:
		category = delivery_constant.DeliveryMethod
	case delivery_constant.LogisticSupportNameUpper:
		category = delivery_constant.LogisticSupport
	case delivery_constant.ContactMethodNameUpper:
		category = delivery_constant.ContactMethod
	default:
		errMsg := fmt.Sprintf("Invalid category, please re-confirm.in Row %d，category= %s", rowIdAdd, rowData[1])
		logger.CtxLogErrorf(ctx, errMsg)
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg))
	}

	rowData[1] = strconv.Itoa(category)
	actionCode := rowData[4]
	initial, err := strconv.ParseUint(rowData[2], 10, 64)
	if err != nil {
		cepCodeUploadErrLineMap[rowIdAdd] = true
		errMsg := fmt.Sprintf("convert initial err,in Row %d，initial= %s，err= %s", rowIdAdd, rowData[2], err.Error())
		logger.CtxLogErrorf(ctx, errMsg)
	}
	final, err := strconv.ParseUint(rowData[3], 10, 64)
	if err != nil {
		cepCodeUploadErrLineMap[rowIdAdd] = true
		errMsg := fmt.Sprintf("convert initial err,in Row %d，final= %s，err= %s", rowIdAdd, rowData[3], err.Error())
		logger.CtxLogErrorf(ctx, errMsg)
	}
	if len(errList) != 0 {
		return errList
	}

	deliveryInstructionCepRangeModel := &delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab{
		LineId:   rowLineId,
		Region:   region,
		Category: int8(category),
		Initial:  initial,
		Final:    final,
		Operator: operator,
		RowId:    rowIdAdd,
	}
	if actionCode == delivery_constant.AddAction {
		addCepRangeModelsMap[rowLineId] = append(addCepRangeModelsMap[rowLineId], deliveryInstructionCepRangeModel)
	} else {
		deleteCepRangeModelsMap[rowLineId] = append(deleteCepRangeModelsMap[rowLineId], deliveryInstructionCepRangeModel)
	}
	return errList
}

func NewDeliveryInstructionWhitelistCepRangeService(deliveryInstructionWhitelistCepRangeDao delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeDaoInterface,
	deliveryInstructionWhitelistLocationDao delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationDaoInterface) *deliveryInstructionWhitelistCepRangeService {
	return &deliveryInstructionWhitelistCepRangeService{
		deliveryInstructionWhitelistCepRangeDao: deliveryInstructionWhitelistCepRangeDao,
		deliveryInstructionWhitelistLocationDao: deliveryInstructionWhitelistLocationDao,
	}
}

var _ DeliveryInstructionWhitelistCepRangeInterface = (*deliveryInstructionWhitelistCepRangeService)(nil)
