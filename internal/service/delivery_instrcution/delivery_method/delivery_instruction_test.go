package delivery_method

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	delivery_method2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/delivery_method"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf_detail"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"testing"
)

func TestGetDetailModel(t *testing.T) {

	deliveryMethodInstance := deliveryMethod{}
	tests := []struct {
		name     string
		request  *delivery_method2.EditDeliveryInstructionRequest
		expected []*delivery_conf_detail.DeliveryInstructionBasicConfDetailTab
	}{
		{
			name: "beforeEdd",
			request: &delivery_method2.EditDeliveryInstructionRequest{
				Category:           2,
				Scenario:           2,
				EnableStatus:       1,
				WhitelistToggle:    0,
				AvailableSloStatus: []int{1000, 1020},
				LmLineIds:          []string{"LID03"},
				AppliedOptions: []*delivery_method2.ApplliedOption{
					{
						AppliedOption: 1,
						BeforeEddDays: 5,
					},
				},
				AvailableFCodeStatus: []string{"F510"},
			},
			expected: []*delivery_conf_detail.DeliveryInstructionBasicConfDetailTab{
				{
					ResourceID:         "LID03",
					AvailableSloStatus: "1000,1020",
					AppliedOption:      1,
					BeforeEddDays:      5,
					AvailableFCodes:    "F510",
				},
			},
		},
		{
			name: "NoFCodes",
			request: &delivery_method2.EditDeliveryInstructionRequest{
				Category:           2,
				Scenario:           2,
				EnableStatus:       1,
				WhitelistToggle:    0,
				AvailableSloStatus: []int{},
				LmLineIds:          []string{"LID03"},
				AppliedOptions: []*delivery_method2.ApplliedOption{
					{
						AppliedOption: 1,
						BeforeEddDays: 5,
					},
				},
				AvailableFCodeStatus: []string{},
			},
			expected: []*delivery_conf_detail.DeliveryInstructionBasicConfDetailTab{
				{
					ResourceID:         "LID03",
					AvailableSloStatus: "",
					AppliedOption:      1,
					BeforeEddDays:      5,
					AvailableFCodes:    "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			details, err := deliveryMethodInstance.getDetailModel(&utils.HttpContext{}, tt.request)
			assert.Nil(t, err)
			assert.Equal(t, tt.expected, details)
		})
	}
}

func TestCheckCategory(t *testing.T) {
	tests := []struct {
		name       string
		slsTnInfo  *pb.DeliveryInstructionReqBySlsTn
		dMCategory *delivery_conf.DeliveryCategory
		expected   *pb.AvailableDeliveryInstruction
	}{
		{
			name: "preEddSloStatus",
			slsTnInfo: &pb.DeliveryInstructionReqBySlsTn{
				SlsTn:     proto.String("test1"),
				Country:   proto.String("TC"),
				LineId:    proto.String("line1"),
				SloStatus: proto.Uint32(1000),
				Edd:       proto.Uint32(uint32(recorder.Now(context.Background()).Unix())),
				FCodes:    []string{"A500"},
			},
			dMCategory: &delivery_conf.DeliveryCategory{
				SloStatusArray: map[string]bool{
					"1000": true,
				},
				CategoryNameEnum: 2,
				FCodeArray: map[string]bool{
					"B500": true,
				},
				Options: []delivery_conf.Option{
					{
						OptionNameEnum: 1,
						BeforeEddDays:  5,
					},
				},
			},
			expected: &pb.AvailableDeliveryInstruction{
				Category:                proto.Uint32(2),
				DeliveryInstructionInfo: []uint32{1},
			},
		},
		{
			name: "preEddFCode",
			slsTnInfo: &pb.DeliveryInstructionReqBySlsTn{
				SlsTn:     proto.String("test1"),
				Country:   proto.String("TC"),
				LineId:    proto.String("line1"),
				SloStatus: proto.Uint32(1000),
				Edd:       proto.Uint32(uint32(recorder.Now(context.Background()).Unix())),
				FCodes:    []string{"A500"},
			},
			dMCategory: &delivery_conf.DeliveryCategory{
				SloStatusArray: map[string]bool{
					"8000": true,
				},
				CategoryNameEnum: 2,
				FCodeArray: map[string]bool{
					"A500": true,
				},
				Options: []delivery_conf.Option{
					{
						OptionNameEnum: 1,
						BeforeEddDays:  5,
					},
				},
			},
			expected: &pb.AvailableDeliveryInstruction{
				Category:                proto.Uint32(2),
				DeliveryInstructionInfo: []uint32{1},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			instructions, err := checkCategory(utils.NewCommonCtx(context.Background()), tt.slsTnInfo, tt.dMCategory)
			assert.Nil(t, err)
			assert.Equal(t, tt.expected, instructions)
		})
	}
}
