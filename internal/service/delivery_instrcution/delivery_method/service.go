package delivery_method

import (
	"encoding/gob"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	delivery_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/delivery_instruction"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	delivery_method2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/delivery_method"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_conf_detail"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/delivery_instruction/delivery_instruction_whitelist_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/common"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"strconv"
	"strings"
)

//service 层写逻辑
type DeliveryMethodInterface interface {
	CreateDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.EditDeliveryInstructionRequest) *lcos_error.LCOSError
	EditDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.EditDeliveryInstructionRequest) *lcos_error.LCOSError

	ListDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.ListDeliveryInstructionRequest) (interface{}, *lcos_error.LCOSError)
	DetailDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.RuleIdDeliveryInstructionRequest) (interface{}, *lcos_error.LCOSError)
	DeleteDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.RuleIdDeliveryInstructionRequest) *lcos_error.LCOSError
	OptionListDeliveryInstructionConf(ctx utils.LCOSContext) (interface{}, *lcos_error.LCOSError)

	//for grpc
	GetDeliveryInstructionInfoByProductIdsInOnCache(ctx utils.LCOSContext, productIds []string) ([]*pb.DeliveryInstructionRespList, *lcos_error.LCOSError)
	GetDeliveryInstructionInfoByRegionInOnCache(ctx utils.LCOSContext, pageNo uint32, count uint32, region string) (uint32, []*pb.DeliveryInstructionRespList, *lcos_error.LCOSError)
	GetDeliveryInstructionInfoByLineId(ctx utils.LCOSContext, slsTnInfo *pb.DeliveryInstructionReqBySlsTn) (*pb.AvailableDeliveryInstructions, *lcos_error.LCOSError)
}

type deliveryMethod struct {
	deliveryInstructionBasicConfDAO          delivery_conf.DeliveryInstructionBasicConfTabDAO
	deliveryInstructionBasicConfDetailTabDAO delivery_conf_detail.DeliveryInstructionBasicConfDetailTabDAO
	deliveryInstructionWhitelistCepRangeDao  delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeDaoInterface
	deliveryInstructionWhitelistLocationDao  delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationDaoInterface
}

func init() {
	gob.Register([]*delivery_conf.ProductIdOptionConf{})
}

func (e *deliveryMethod) CreateDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.EditDeliveryInstructionRequest) *lcos_error.LCOSError {
	//对已有的product_id或lm_line_id校验
	if request.Scenario == delivery_constant.AfterShipment && request.Category == delivery_constant.LogisticSupport && len(request.AppliedOptions) > 0 && request.AppliedOptions[0].BeforeEddDays <= 0 && !checkExceedEddDays(request.AppliedOptions[0].ExceedEddDays) {
		return &lcos_error.LCOSError{
			Msg:     "at least one of exceed edd or before edd should be greater than 0",
			RetCode: lcos_error.SchemaParamsErrorCode,
		}
	}
	err := e.checkResourceIds(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "check resource_id in db|err=%v", err.Msg)
		return err
	}

	err = e.checkWhitelistToggle(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "check WhitelistToggle err|err=%v", err.Msg)
		return err
	}

	confModel, err := e.getConfModel(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get conf model err|err=%v", err.Msg)
		return err
	}

	detailModels, err := e.getDetailModel(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get detail models err|err=%v", err.Msg)
		return err
	}

	//开启事务
	fn := func() *lcos_error.LCOSError {
		//1.写入basic_conf表
		var basic *delivery_conf.DeliveryInstructionBasicConfTab
		var err *lcos_error.LCOSError
		if basic, err = e.deliveryInstructionBasicConfDAO.CreateBasicConf(ctx, confModel); err != nil {
			logger.CtxLogErrorf(ctx, "write basic_conf tab err|err=%v", err.Msg)
			return err
		}

		//填充到detailmodel中
		for _, item := range detailModels {
			item.RuleId = basic.Id
		}
		//2.写入basic_conf_detail表
		if err := e.deliveryInstructionBasicConfDetailTabDAO.BatchCreateBasicConfDetail(ctx, detailModels); err != nil {
			logger.CtxLogErrorf(ctx, "write basic_conf_detail err|err=%v", err.Msg)
			return err
		}
		return nil
	}
	return ctx.Transaction(fn)
}

func (e *deliveryMethod) getConfModel(ctx utils.LCOSContext, request *delivery_method2.EditDeliveryInstructionRequest) (*delivery_conf.DeliveryInstructionBasicConfTab, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	operator := ctx.GetUserEmail()

	confModel := &delivery_conf.DeliveryInstructionBasicConfTab{
		Region:          region,
		Category:        request.Category,
		Scenario:        request.Scenario,
		EnableStatus:    request.EnableStatus,
		Operator:        operator,
		WhitelistToggle: request.WhitelistToggle,
	}
	return confModel, nil
}

func (e *deliveryMethod) checkWhitelistToggle(ctx utils.LCOSContext, request *delivery_method2.EditDeliveryInstructionRequest) *lcos_error.LCOSError {
	if request.Scenario == delivery_constant.CheckoutPage || request.WhitelistToggle == delivery_constant.Disable {
		return nil
	}
	//var errList []*lcos_error.LCOSError
	var errLineIdList []string
	var errString string
	for _, lineId := range request.LmLineIds {
		var queryMap = make(map[string]interface{})
		queryMap["line_id"] = lineId
		queryMap["category"] = request.Category
		lineIDLocationConfs, lcosErr := e.deliveryInstructionWhitelistLocationDao.ListAllDeliveryInstructionWhitelistLocations(ctx, queryMap)
		if lcosErr != nil {
			return lcosErr
		}
		lineIDCepRangeConfs, lcosErr := e.deliveryInstructionWhitelistCepRangeDao.ListAllDeliveryInstructionWhitelistCepRanges(ctx, queryMap)
		if lcosErr != nil {
			return lcosErr
		}

		//check location and ceprange
		if len(lineIDLocationConfs) == 0 && len(lineIDCepRangeConfs) == 0 {
			errLineIdList = append(errLineIdList, lineId)
		}
	}
	if len(errLineIdList) != 0 {
		errString = strings.Join(errLineIdList, ",")
		errString += delivery_constant.WhitelistCheckErrMessage
		return lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, errString)
	}
	return nil
}

func (e *deliveryMethod) checkResourceIds(ctx utils.LCOSContext, request *delivery_method2.EditDeliveryInstructionRequest) *lcos_error.LCOSError {
	//var resourceIDMap = make(map[string]int)
	models, err := e.deliveryInstructionBasicConfDAO.ListAllBasicConf(ctx)
	if err != nil {
		return err
	}
	detailModels, err := e.deliveryInstructionBasicConfDetailTabDAO.ListAllBasicConfDetail(ctx)
	if err != nil {
		return err
	}

	//拼接当前的 scenario+Category+resourceId
	nowModelIdMap := make(map[uint64]string)
	searchResourceIDMap := make(map[string]bool)
	for _, model := range models {
		nowModelIdMap[model.Id] = strconv.FormatInt(int64(model.Scenario), 10) + "-" + strconv.FormatInt(int64(model.Category), 10)
	}
	for _, detailModel := range detailModels {
		if scenarioAndCategory, ok := nowModelIdMap[detailModel.RuleId]; ok {
			resourceIDs := strings.Split(detailModel.ResourceID, ",") //product_ids
			for _, resourceID := range resourceIDs {
				key := scenarioAndCategory + resourceID
				searchResourceIDMap[key] = true
			}
		}
	}

	hasConfigedIds := []string{}
	if request.Scenario == delivery_constant.AfterShipment { //lm_line_id
		if len(request.LmLineIds) == 0 {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "after shipment but not request lm_line_ids")
		}
		for _, lmLineId := range request.LmLineIds {
			key := strconv.FormatInt(int64(request.Scenario), 10) + "-" + strconv.FormatInt(int64(request.Category), 10) + lmLineId
			if _, ok := searchResourceIDMap[key]; ok {
				hasConfigedIds = append(hasConfigedIds, lmLineId)
			}
		}
	} else {
		if len(request.ProductIds) == 0 {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "checkout but not request product_ids")
		}
		for _, productId := range request.ProductIds {
			key := strconv.FormatInt(int64(request.Scenario), 10) + "-" + strconv.FormatInt(int64(request.Category), 10) + productId
			if _, ok := searchResourceIDMap[key]; ok {
				hasConfigedIds = append(hasConfigedIds, productId)
			}
		}
	}
	if len(hasConfigedIds) != 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.DeliveryPrefixErrorCode, "%s have existing configuration. Please re-confirm.", strings.Join(hasConfigedIds, ","))
	}
	//没找到
	return nil
}

func (e *deliveryMethod) getDetailModel(ctx utils.LCOSContext, request *delivery_method2.EditDeliveryInstructionRequest) ([]*delivery_conf_detail.DeliveryInstructionBasicConfDetailTab, *lcos_error.LCOSError) {
	if len(request.AppliedOptions) == 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "no applied options")
	}
	detailModels := make([]*delivery_conf_detail.DeliveryInstructionBasicConfDetailTab, 0, len(request.AppliedOptions))

	var resourceIDs string
	if request.Scenario == delivery_constant.AfterShipment { //lm_line_id
		if len(request.LmLineIds) == 0 {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "after shipment but not config lm_line_ids")
		}
		if len(request.LmLineIds) > delivery_constant.MaxNum {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "after shipment but lm_line_ids large max 50")
		}
		resourceIDs = strings.Join(request.LmLineIds, ",")
		var availableSloStatus string
		if request.AvailableSloStatus != nil && len(request.AvailableSloStatus) > 0 {
			strArray := make([]string, len(request.AvailableSloStatus))
			if len(request.AvailableSloStatus) > delivery_constant.MaxNum {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "checkout but available_slo_status large max 50")
			}
			for i, num := range request.AvailableSloStatus {
				strArray[i] = fmt.Sprint(num)
			}
			availableSloStatus = strings.Join(strArray, ",")
		}
		var availableFCodes string
		if request.AvailableFCodeStatus != nil && len(request.AvailableFCodeStatus) > 0 {
			if len(request.AvailableFCodeStatus) > delivery_constant.MaxNum {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "checkout but available_f_code large max 50")
			}
			availableFCodes = strings.Join(request.AvailableFCodeStatus, ",")
		}
		for _, item := range request.AppliedOptions {
			otpSum := int8(0)
			for _, otp := range item.OTPFlags {
				otpSum |= otp
			}
			codSum := int8(0)
			for _, cod := range item.CODFlags {
				codSum |= cod
			}
			detailModels = append(detailModels, &delivery_conf_detail.DeliveryInstructionBasicConfDetailTab{
				ResourceID:         resourceIDs,
				AppliedOption:      item.AppliedOption,
				OTPFlag:            otpSum,
				CODFlag:            codSum,
				ExceedEddDays:      item.ExceedEddDays,
				BeforeEddDays:      item.BeforeEddDays,
				AvailableFCodes:    availableFCodes,
				AvailableSloStatus: availableSloStatus,
			})
		}
	} else {
		if len(request.ProductIds) == 0 {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "checkout but not config product_ids")
		}
		if len(request.ProductIds) > delivery_constant.MaxNum {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "checkout but lm_line_ids large max 50")
		}
		resourceIDs = strings.Join(request.ProductIds, ",") //product_ids
		for _, item := range request.AppliedOptions {
			detailModels = append(detailModels, &delivery_conf_detail.DeliveryInstructionBasicConfDetailTab{
				ResourceID:    resourceIDs,
				AppliedOption: item.AppliedOption,
			})
		}
	}
	return detailModels, nil
}

func (e *deliveryMethod) EditDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.EditDeliveryInstructionRequest) *lcos_error.LCOSError {
	if request.RuleId == 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "request must send RuleId, when edit")
	}
	err := e.checkWhitelistToggle(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "check WhitelistToggle err|err=%v", err.Msg)
		return err
	}

	confModel, err := e.getConfModel(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get conf model err|err=%v", err.Msg) //err打在上边
		return err
	}

	//detailModels := []*delivery_conf_detail.DeliveryInstructionBasicConfDetailTab{}
	detailModels, err := e.getDetailModel(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get detail models err|err=%v", err.Msg)
		return err
	}

	//开启事务
	fn := func() *lcos_error.LCOSError {
		//1.删除 basic_conf_detail 表
		detailConds := map[string]interface{}{
			"rule_id": request.RuleId,
		}
		if err := e.deliveryInstructionBasicConfDetailTabDAO.DeleteBasicConfDetailByCond(ctx, detailConds); err != nil {
			return err
		}

		//2.删除 basic_conf 表
		if err := e.deliveryInstructionBasicConfDAO.DeleteBasicConfByID(ctx, request.RuleId); err != nil {
			return err
		}

		//删除后新建时，查找现有的是否有存在
		resourceIdErr := e.checkResourceIds(ctx, request)
		if resourceIdErr != nil {
			logger.CtxLogErrorf(ctx, "edit existing resource_id in db err|err=%v", resourceIdErr.Msg)
			return resourceIdErr
		}

		//3.新增 basic_conf表
		var basic *delivery_conf.DeliveryInstructionBasicConfTab
		var err *lcos_error.LCOSError
		if basic, err = e.deliveryInstructionBasicConfDAO.CreateBasicConf(ctx, confModel); err != nil {
			return err
		}

		//填充到detailmodel中
		for _, item := range detailModels {
			item.RuleId = basic.Id
		}
		//4.新增 basic_conf_detail表
		if err := e.deliveryInstructionBasicConfDetailTabDAO.BatchCreateBasicConfDetail(ctx, detailModels); err != nil {
			return err
		}
		return nil
	}
	return ctx.Transaction(fn)
}

func (e *deliveryMethod) ListDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.ListDeliveryInstructionRequest) (interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	var pageNo uint32 = 1
	var pageCount uint32 = 10
	if request.PageNo != 0 {
		pageNo = request.PageNo
	}
	if request.Count != 0 {
		pageCount = request.Count
	}
	queryMap := map[string]interface{}{
		"region": region,
	}
	if request.Category != nil {
		queryMap["category"] = *request.Category
	}
	if request.Scenario != nil {
		queryMap["scenario"] = *request.Scenario
	}
	if request.EnableStatus != nil {
		queryMap["enable_status"] = *request.EnableStatus
	}
	if request.AppliedOption != nil {
		details, detailErr := e.deliveryInstructionBasicConfDetailTabDAO.SearchBasicConfDetail(ctx, map[string]interface{}{"applied_option in": request.AppliedOption})
		if detailErr != nil {
			logger.CtxLogErrorf(ctx, "search applied_option in db err|err=%v", detailErr.Msg)
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "find detail err for option %d, when list", request.AppliedOption)
		}
		var searchID []uint64
		for _, detail := range details {
			searchID = append(searchID, detail.RuleId)
		}
		queryMap["id in"] = searchID
	}
	//直接查找basic_conf表
	models, total, lcosErr := e.deliveryInstructionBasicConfDAO.ListBasicConfByParamsPaging(ctx, queryMap, pageNo, pageCount)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "search basic_conf db err|err=%v", lcosErr.Msg)
		return nil, lcosErr
	}
	ModelToList := []delivery_method2.DeliveryList{}
	for _, item := range models {
		details, detailErr := e.deliveryInstructionBasicConfDetailTabDAO.SearchBasicConfDetail(ctx, map[string]interface{}{"rule_id": item.Id})
		if detailErr != nil {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find detail for rule %d, when list", item.Id)
		}
		var AppliedOption []int8
		if len(details) == 0 {
			logger.CtxLogErrorf(ctx, "search zero detail for rule_id=%v", item.Id)
			continue
		}
		for _, detail := range details {
			AppliedOption = append(AppliedOption, detail.AppliedOption)
		}
		Model := delivery_method2.DeliveryList{
			RuleId:        item.Id,
			Category:      item.Category,
			Scenario:      item.Scenario,
			AppliedOption: AppliedOption,
			Operator:      item.Operator,
			EnableStatus:  item.EnableStatus,
			Mtime:         item.MTime,
		}
		if item.Scenario == delivery_constant.CheckoutPage {
			Model.ProductIds = strings.Split(details[0].ResourceID, ",")
		} else if item.Scenario == delivery_constant.AfterShipment {
			Model.LmLineIds = strings.Split(details[0].ResourceID, ",") //lm_line_ids
		}
		ModelToList = append(ModelToList, Model)
	}
	return delivery_method2.ListDeliveryInstructionRsp{
		PageNo: pageNo,
		Count:  pageCount,
		Total:  total,
		List:   ModelToList,
	}, nil
}

func (e *deliveryMethod) DetailDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.RuleIdDeliveryInstructionRequest) (interface{}, *lcos_error.LCOSError) {
	details, detailErr := e.deliveryInstructionBasicConfDetailTabDAO.SearchBasicConfDetail(ctx, map[string]interface{}{"rule_id": request.RuleId})
	if detailErr != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find detail for rule %d, when get detail", request.RuleId)
	}
	if len(details) == 0 {
		return nil, nil
	}
	confs, err := e.deliveryInstructionBasicConfDAO.SearchBasicConf(ctx, map[string]interface{}{"id": request.RuleId})
	if err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find conf for rule %d, when get detail", request.RuleId)
	}
	if len(confs) == 0 {
		return nil, nil
	}
	conf := confs[0]
	AppliedOptions := make([]*delivery_method2.ApplliedOption, 0, len(details))
	for _, detail := range details {
		AppliedOptions = append(AppliedOptions, &delivery_method2.ApplliedOption{
			AppliedOption: detail.AppliedOption,
			OTPFlags:      common.GetCombinedDigits(detail.OTPFlag),
			CODFlags:      common.GetCombinedDigits(detail.CODFlag),
			ExceedEddDays: detail.ExceedEddDays,
			BeforeEddDays: detail.BeforeEddDays,
		})
	}

	optionList := delivery_method2.EditDeliveryInstructionRequest{
		Category:        conf.Category,
		Scenario:        conf.Scenario,
		AppliedOptions:  AppliedOptions,
		EnableStatus:    conf.EnableStatus,
		WhitelistToggle: conf.WhitelistToggle,
		Operator:        conf.Operator,
		RuleId:          conf.Id,
		Mtime:           conf.MTime,
	}
	if conf.Scenario == delivery_constant.CheckoutPage {
		optionList.ProductIds = strings.Split(details[0].ResourceID, ",") //product_ids
	} else if conf.Scenario == delivery_constant.AfterShipment {
		optionList.LmLineIds = strings.Split(details[0].ResourceID, ",") //lm_line_ids
	}

	sloArray := strings.Split(details[0].AvailableSloStatus, ",")
	optionList.AvailableSloStatus = make([]int, 0)
	for _, status := range sloArray {
		if status == "" {
			continue
		}
		num, err := strconv.ParseUint(status, 10, 32)
		if err != nil {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Error converting %d status to uint32: %v", request.RuleId, err)
		}
		optionList.AvailableSloStatus = append(optionList.AvailableSloStatus, int(num))
	}
	fCodeArray := strings.Split(details[0].AvailableFCodes, ",")
	optionList.AvailableFCodeStatus = make([]string, 0, len(fCodeArray))
	for _, fCode := range fCodeArray {
		if fCode == "" {
			continue
		}
		optionList.AvailableFCodeStatus = append(optionList.AvailableFCodeStatus, fCode)
	}
	return optionList, nil
}

func (e *deliveryMethod) DeleteDeliveryMethod(ctx utils.LCOSContext, request *delivery_method2.RuleIdDeliveryInstructionRequest) *lcos_error.LCOSError {
	//校验是否有这个rule
	confs, err := e.deliveryInstructionBasicConfDAO.SearchBasicConf(ctx, map[string]interface{}{"id": request.RuleId})
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "find conf for rule %d err, when delete", request.RuleId)
	}
	if len(confs) == 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find conf for rule %d, when delete", request.RuleId)
	}

	//开启事务
	fn := func() *lcos_error.LCOSError {
		//1.删除 basic_conf_detail 表
		detailConds := map[string]interface{}{
			"rule_id": request.RuleId,
		}
		if err := e.deliveryInstructionBasicConfDetailTabDAO.DeleteBasicConfDetailByCond(ctx, detailConds); err != nil {
			return err
		}

		//2.删除 basic_conf 表
		if err := e.deliveryInstructionBasicConfDAO.DeleteBasicConfByID(ctx, *request.RuleId); err != nil {
			return err
		}
		return nil
	}
	return ctx.Transaction(fn)
}

func (e *deliveryMethod) OptionListDeliveryInstructionConf(ctx utils.LCOSContext) (interface{}, *lcos_error.LCOSError) {
	//直接返回内容
	optionList := delivery_method2.DeliveryInstructionOptionList{}
	optionList.OptionLists = make([]delivery_method2.OptionList, 0, delivery_constant.DeliveryMethod)
	optionList.OptionLists = append(optionList.OptionLists, delivery_method2.OptionList{
		Category:         delivery_constant.DeliveryMethod,
		AvliableScenario: []int8{delivery_constant.CheckoutPage, delivery_constant.AfterShipment},
		AvliableOption:   []int8{delivery_constant.AuthorizeToLeave, delivery_constant.DeliverInPerson, delivery_constant.LeaveAtReception},
	})

	optionList.OptionLists = append(optionList.OptionLists, delivery_method2.OptionList{
		Category:         delivery_constant.LogisticSupport,
		AvliableScenario: []int8{delivery_constant.AfterShipment},
		AvliableOption:   []int8{delivery_constant.Expedite},
	})

	optionList.OptionLists = append(optionList.OptionLists, delivery_method2.OptionList{
		Category:         delivery_constant.ContactMethod,
		AvliableScenario: []int8{delivery_constant.AfterShipment},
		AvliableOption:   []int8{delivery_constant.DoNotCall, delivery_constant.ContactByPhone},
	})
	return optionList, nil
}

func (e *deliveryMethod) GetDeliveryInstructionInfoByProductIdsInOnCache(ctx utils.LCOSContext, productIds []string) ([]*pb.DeliveryInstructionRespList, *lcos_error.LCOSError) {
	deliveryInstructionRespList := make([]*pb.DeliveryInstructionRespList, 0, len(productIds))
	queryExecutor := localcache.NewLocalCacheQueryExecutor()

	for _, productId := range productIds {
		cacheKey := delivery_conf.GenDeliveryInstructionCacheKey(delivery_constant.Product, productId, delivery_constant.CheckoutPage)
		val, dbErr := queryExecutor.Find(ctx, constant.DeliveryInstructionBasicConfAndDetailNamespace, cacheKey)
		if dbErr == nil {
			productIdCategoryConfs, ok := val.([]*delivery_conf.DeliveryCategory)
			if !ok {
				errMsg := fmt.Sprintf("converting available option failed|productId=%s", productId)
				return nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, errMsg)
			}
			availables := make([]*pb.AvailableDeliveryInstruction, 0, len(productIdCategoryConfs))
			for _, productIdCategoryConf := range productIdCategoryConfs {
				deliveryInstructionInfo := []uint32{}
				for _, option := range productIdCategoryConf.Options {
					deliveryInstructionInfo = append(deliveryInstructionInfo, uint32(option.OptionNameEnum))
				}
				availables = append(availables, &pb.AvailableDeliveryInstruction{
					Category:                utils.NewUint32(uint32(productIdCategoryConf.CategoryNameEnum)),
					DeliveryInstructionInfo: deliveryInstructionInfo,
				})
			}
			deliveryInstructionRespList = append(deliveryInstructionRespList, &pb.DeliveryInstructionRespList{
				ProductId:                    utils.NewString(productId),
				AvailableDeliveryInstruction: availables,
			})
		} else {
			if errors.Is(dbErr, localcache.ErrKeyNotFound) {
				deliveryInstructionRespList = append(deliveryInstructionRespList, &pb.DeliveryInstructionRespList{
					ProductId:                    utils.NewString(productId),
					AvailableDeliveryInstruction: nil,
				})
			} else {
				return nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, dbErr.Error())
			}
		}
	}
	return deliveryInstructionRespList, nil
}

func (e *deliveryMethod) GetDeliveryInstructionInfoByRegionInOnCache(ctx utils.LCOSContext, pageNo uint32, count uint32, region string) (uint32, []*pb.DeliveryInstructionRespList, *lcos_error.LCOSError) {
	queryExecutor := localcache.NewLocalCacheQueryExecutor()
	//region -> []OptionList  map[string][]*delivery_conf.OptionList
	cacheKey := delivery_conf.GenDeliveryInstructionCacheKey(delivery_constant.Region, region, delivery_constant.CheckoutPage)
	val, dbErr := queryExecutor.Find(ctx, constant.DeliveryInstructionBasicConfAndDetailNamespace, cacheKey)
	if dbErr == localcache.ErrKeyNotFound {
		return 0, nil, nil
	}
	if dbErr != nil {
		return 0, nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, dbErr.Error())
	}

	productIdOptionConfs := val.([]*delivery_conf.ProductIdOptionConf)
	totalCount := uint32(len(productIdOptionConfs))
	startIndex := (pageNo - 1) * count
	endIndex := startIndex + count

	// 检查边界
	if startIndex >= totalCount {
		return totalCount, nil, nil
	}
	if endIndex > totalCount {
		endIndex = totalCount
	}

	deliveryInstructionRespList := make([]*pb.DeliveryInstructionRespList, 0, totalCount)
	for i := startIndex; i < endIndex; i++ {
		categoryConfs := productIdOptionConfs[i].Categorys
		availables := make([]*pb.AvailableDeliveryInstruction, 0, len(productIdOptionConfs[i].Categorys))
		for _, categoryConf := range categoryConfs {
			deliveryInstructionInfo := []uint32{}
			for _, option := range categoryConf.Options {
				deliveryInstructionInfo = append(deliveryInstructionInfo, uint32(option.OptionNameEnum))
			}
			availables = append(availables, &pb.AvailableDeliveryInstruction{
				Category:                utils.NewUint32(uint32(categoryConf.CategoryNameEnum)),
				DeliveryInstructionInfo: deliveryInstructionInfo,
			})
		}

		deliveryInstructionRespList = append(deliveryInstructionRespList, &pb.DeliveryInstructionRespList{
			ProductId:                    utils.NewString(productIdOptionConfs[i].ProductId),
			AvailableDeliveryInstruction: availables,
		})
	}
	return totalCount, deliveryInstructionRespList, nil
}

func (e *deliveryMethod) GetDeliveryInstructionInfoByLineId(ctx utils.LCOSContext, slsTnInfo *pb.DeliveryInstructionReqBySlsTn) (*pb.AvailableDeliveryInstructions, *lcos_error.LCOSError) {
	availables := make([]*pb.AvailableDeliveryInstruction, 0)
	queryExecutor := localcache.NewLocalCacheQueryExecutor()
	ans := &pb.AvailableDeliveryInstructions{}

	cacheKey := delivery_conf.GenDeliveryInstructionCacheKey(delivery_constant.Line, slsTnInfo.GetLineId(), delivery_constant.AfterShipment)
	//从localcache获取lineid+category 的配置
	val, dbErr := queryExecutor.Find(ctx, constant.DeliveryInstructionBasicConfAndDetailNamespace, cacheKey)
	if dbErr != nil {
		if errors.Is(dbErr, localcache.ErrKeyNotFound) {
			return ans, nil
		} else {
			//localcache报错 返回错误
			return nil, lcos_error.NewLCOSError(lcos_error.LocalCacheReadWriteErrorCode, dbErr.Error())
		}
	}

	lineIdCategoryConfs, ok := val.([]*delivery_conf.DeliveryCategory)
	if !ok {
		errMsg := fmt.Sprintf("converting lineId available option failed|slstn=%s,lineId=%s", slsTnInfo.GetSlsTn(), slsTnInfo.GetLineId())
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}
	for _, lineIdCategoryConf := range lineIdCategoryConfs {
		available, err := checkCategory(ctx, slsTnInfo, lineIdCategoryConf)
		if err != nil {
			//category报错 直接返回
			return nil, err
		}
		if available != nil {
			availables = append(availables, available)
		}
	}

	for _, available := range availables {
		switch available.GetCategory() {
		case delivery_constant.DeliveryMethod:
			ans.DeliveryInstructions = available.GetDeliveryInstructionInfo()
		case delivery_constant.LogisticSupport:
			ans.LogisticSupport = available.GetDeliveryInstructionInfo()
		case delivery_constant.ContactMethod:
			ans.ContactMethod = available.GetDeliveryInstructionInfo()
		}
	}

	return ans, nil
}

func checkLocationAndCepRange(ctx utils.LCOSContext, slsTnInfo *pb.DeliveryInstructionReqBySlsTn, dMCategory *delivery_conf.DeliveryCategory) (bool, *lcos_error.LCOSError) {
	queryExecutor := localcache.NewLocalCacheQueryExecutor()
	whitelistCheck := false
	//whitelist check
	locationCheck := false
	cepRangeCheck := false
	var err error

	//location和ceprange 都要校验一遍
	locationInfo, lcosErr := address_service.LocationServer.GetLocationInfoById(ctx, int(slsTnInfo.GetBuyerAddress().GetDeliverDistrictId()))
	if lcosErr != nil {
		// Notice 千万不能return,解析不到地址时仍希望进行其他option的校验，且输出错误日志
		logger.CtxLogErrorf(ctx, "parse buyer address location id:[%d] error:%v", slsTnInfo.GetBuyerAddress().GetDeliverDistrictId(), lcosErr.Msg)
	} else {
		locationKey := delivery_instruction_whitelist_location.GenLineDeliveryInstructionLocationCacheKey(slsTnInfo.GetLineId(), dMCategory.CategoryNameEnum, locationInfo.DistrictLocationId)
		locationConfVal, locationDbErr := queryExecutor.Find(ctx, constant.DeliveryInstructionWhitelistLocationNamespace, locationKey)
		if locationDbErr == nil {
			//正常处理逻辑
			locationConfs, ok := locationConfVal.(*delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab)
			if !ok {
				errMsg := fmt.Sprintf("converting whitelist location conf failed|sls_tn=[%s], req_location_id=[%d], district_id=[%d], error=[%s]", slsTnInfo.GetSlsTn(), slsTnInfo.GetBuyerAddress().GetDeliverDistrictId(), locationInfo.DistrictLocationId, locationDbErr.Error())
				return whitelistCheck, lcos_error.NewLCOSError(lcos_error.FindDeliveryCategoryFailed, errMsg)

			}
			locationCheck = validLocation(locationInfo.DistrictLocationId, locationConfs)
			if !locationCheck {
				logger.CtxLogErrorf(ctx, "delivery category[%d],location_id not belong to input location id|sls_tn=[%s], req_location_id=[%d],district_id=[%s]", dMCategory.CategoryNameEnum, slsTnInfo.GetSlsTn(), slsTnInfo.GetBuyerAddress().GetDeliverDistrictId(), locationInfo.DistrictLocationId)
			}
		} else {
			if errors.Is(locationDbErr, localcache.ErrKeyNotFound) {
				//没找到 跳过 没有这一条locationId option
				logger.CtxLogErrorf(ctx, "location whitelist conf not found|sls_tn=[%s], location_id=[%d], district_id=[%d]", slsTnInfo.GetSlsTn(), slsTnInfo.GetBuyerAddress().GetDeliverDistrictId(), locationInfo.DistrictLocationId)
			} else {
				return whitelistCheck, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, locationDbErr.Error())
			}
		}
	}

	cepRangeKey := delivery_instruction_whitelist_cep_range.GenLineDeliveryInstructionCepRangeCacheKey(slsTnInfo.GetLineId(), dMCategory.CategoryNameEnum)
	cepRangeConfVal, cepRangeDbErr := queryExecutor.Find(ctx, constant.DeliveryInstructionWhitelistCepRangeNamespace, cepRangeKey)
	if cepRangeDbErr == nil {
		//list    可以配置多条cep range
		cepRangeConfs, ok := cepRangeConfVal.([]*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab)
		if !ok {
			errMsg := fmt.Sprintf("converting whitelist postcode conf failed|sls_tn=[%s], postcode=[%s], error=[%s]", slsTnInfo.GetSlsTn(), slsTnInfo.GetBuyerAddress().GetDeliverPostalCode(), err.Error())
			return whitelistCheck, lcos_error.NewLCOSError(lcos_error.FindDeliveryCategoryFailed, errMsg)
		}
		cepRangeCheck, err = validCepRange(slsTnInfo.GetBuyerAddress().GetDeliverPostalCode(), cepRangeConfs)
		if err != nil {
			logger.CtxLogErrorf(ctx, "postcode is not valid|sls_tn=[%s], postcode=[%s], error=[%s]", slsTnInfo.GetSlsTn(), slsTnInfo.GetBuyerAddress().GetDeliverPostalCode(), err.Error())
			return whitelistCheck, lcos_error.NewLCOSError(lcos_error.FindDeliveryCategoryFailed, err.Error())
		}
		if !cepRangeCheck {
			logger.CtxLogErrorf(ctx, "postcode not belong to input cep range list|sls_tn=[%s], postcode=[%s]", slsTnInfo.GetSlsTn(), slsTnInfo.GetBuyerAddress().GetDeliverPostalCode())
		}
	} else {
		if errors.Is(cepRangeDbErr, localcache.ErrKeyNotFound) {
			//没找到 跳过 没有这一条option
			logger.CtxLogErrorf(ctx, "cepRange whitelist conf not found|sls_tn=[%s], postcode=[%s]", slsTnInfo.GetSlsTn(), slsTnInfo.GetBuyerAddress().GetDeliverPostalCode())
		} else {
			return whitelistCheck, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, cepRangeDbErr.Error())
		}
	}

	if locationCheck || cepRangeCheck {
		whitelistCheck = true
	}
	logger.CtxLogErrorf(ctx, "whitelist check result is: locationCheck=[%t], cepRangeCheck=[%t]|sls_tn=[%s]", locationCheck, cepRangeCheck, slsTnInfo.GetSlsTn())
	return whitelistCheck, nil
}

func checkCategory(ctx utils.LCOSContext, slsTnInfo *pb.DeliveryInstructionReqBySlsTn, dMCategory *delivery_conf.DeliveryCategory) (*pb.AvailableDeliveryInstruction, *lcos_error.LCOSError) {
	availableInfo := []uint32{}
	//slo status check
	_, ok := dMCategory.SloStatusArray[strconv.FormatUint(uint64(slsTnInfo.GetSloStatus()), 10)]
	if !ok {
		logger.CtxLogErrorf(ctx, "delivery category[%d],find lineId available slo status zero|sls_tn=%s,lineId=%s,slo_status=%d", dMCategory.CategoryNameEnum, slsTnInfo.GetSlsTn(), slsTnInfo.GetLineId(), slsTnInfo.GetSloStatus())
	}
	fCodeContains := false
	for _, fCode := range slsTnInfo.GetFCodes() {
		if _, ok := dMCategory.FCodeArray[fCode]; ok {
			fCodeContains = true
			break
		}
	}
	if !fCodeContains {
		logger.CtxLogErrorf(ctx, "delivery category[%d],find lineId available slo status zero|sls_tn=%s,lineId=%s,slo_status=%d", dMCategory.CategoryNameEnum, slsTnInfo.GetSlsTn(), slsTnInfo.GetLineId(), slsTnInfo.GetSloStatus())
	}
	if !ok && !fCodeContains {
		return nil, nil
	}
	var err *lcos_error.LCOSError
	//whitelist check
	whitelistCheck := false
	if dMCategory.WhitelistToggle == delivery_constant.Disable {
		//开关关闭 默认配置校验通过
		whitelistCheck = true
	} else {
		whitelistCheck, err = checkLocationAndCepRange(ctx, slsTnInfo, dMCategory)
		if err != nil {
			return nil, err
		}
	}

	if !whitelistCheck {
		return nil, nil
	}

	switch dMCategory.CategoryNameEnum {
	case delivery_constant.DeliveryMethod, delivery_constant.ContactMethod:
		{
			var errMsg string
			if slsTnInfo.GetOtpFlag() != delivery_constant.Flag && slsTnInfo.GetOtpFlag() != delivery_constant.NotFlag {
				errMsg += "invalid otp_flag "
			}
			if slsTnInfo.GetPaymentMethod() != delivery_constant.Flag && slsTnInfo.GetPaymentMethod() != delivery_constant.NotFlag {
				errMsg += "invalid payment_method "
			}
			if errMsg != "" {
				return nil, lcos_error.NewLCOSError(lcos_error.FindDeliveryCategoryFailed, errMsg)
			}

			for _, dmOption := range dMCategory.Options {
				//cod & otp check
				if uint32(dmOption.OTPFlag)&slsTnInfo.GetOtpFlag() != slsTnInfo.GetOtpFlag() {
					//多个option 当前option校验不通过，需跳过，check 下一个option
					continue
				}
				if uint32(dmOption.CODFlag)&slsTnInfo.GetPaymentMethod() != slsTnInfo.GetPaymentMethod() {
					//同上
					continue
				}
				availableInfo = append(availableInfo, uint32(dmOption.OptionNameEnum))
			}
		}
	case delivery_constant.LogisticSupport:
		{
			for _, dmOption := range dMCategory.Options {
				if slsTnInfo.Edd == nil {
					continue
				}
				//edd check, now > request + conf  当前时间超过配置时间，可以催促派送
				currentTime := recorder.Now(ctx).Unix()
				edd := int64(slsTnInfo.GetEdd())
				reqTime := edd + int64(dmOption.ExceedEddDays*delivery_constant.SecondByDay)
				beforeEdd := edd - int64(dmOption.BeforeEddDays*delivery_constant.SecondByDay)
				if (reqTime >= currentTime && currentTime > edd) || (currentTime < beforeEdd) {
					logger.CtxLogErrorf(ctx, "delivery req time not exceed current time|reqTime=[%d], currentTime=[%d]", reqTime, currentTime)
					continue
				}
				availableInfo = append(availableInfo, uint32(dmOption.OptionNameEnum))
			}
		}
	}

	if len(availableInfo) == 0 {
		return nil, nil
	}
	return &pb.AvailableDeliveryInstruction{
		Category:                utils.NewUint32(uint32(dMCategory.CategoryNameEnum)),
		DeliveryInstructionInfo: availableInfo,
	}, nil
}

func validLocation(districtId int, locationConf *delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationTab) bool {
	return locationConf.LocationId == districtId
}

func validCepRange(postcode string, cepRangeList []*delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeTab) (bool, error) {
	if len(cepRangeList) == 0 {
		return false, nil
	}
	postcode = utils.ValidPostcode(postcode)
	postcodeInt, err := strconv.Atoi(postcode)
	if err != nil {
		return false, err
	}
	for _, cepRange := range cepRangeList {
		if uint64(postcodeInt) >= cepRange.Initial && uint64(postcodeInt) <= cepRange.Final {
			return true, nil
		}
	}
	return false, nil
}

func NewDeliveryMethod(deliveryInstructionBasicConfDAO delivery_conf.DeliveryInstructionBasicConfTabDAO,
	deliveryInstructionBasicConfDetailTabDAO delivery_conf_detail.DeliveryInstructionBasicConfDetailTabDAO,
	deliveryInstructionWhitelistCepRangeDao delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeDaoInterface,
	deliveryInstructionWhitelistLocationDao delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationDaoInterface) *deliveryMethod {
	return &deliveryMethod{
		deliveryInstructionBasicConfDAO:          deliveryInstructionBasicConfDAO,
		deliveryInstructionBasicConfDetailTabDAO: deliveryInstructionBasicConfDetailTabDAO,
		deliveryInstructionWhitelistCepRangeDao:  deliveryInstructionWhitelistCepRangeDao,
		deliveryInstructionWhitelistLocationDao:  deliveryInstructionWhitelistLocationDao,
	}
}

var _ DeliveryMethodInterface = (*deliveryMethod)(nil)

func checkExceedEddDays(exceedEddDays int) bool {
	return exceedEddDays >= 0 && exceedEddDays != delivery_constant.InvalidExceedEddDays
}
