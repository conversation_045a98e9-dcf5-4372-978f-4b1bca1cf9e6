package site_serviceable_area_postcode

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	site_serviceable_area "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	site_serviceable_area_postcode "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

/*
* @Author: lei.guo
* @Date: 2021/11/18 12:12 上午
* @Name：site_serviceable_area
* @Description:
 */

type SiteServiceableAreaPostcodeInterface interface {
	CreateSiteServiceableAreaPostcode(ctx utils.LCOSContext, request *basic_serviceable.CreateSiteServiceableAreaPostcode) *lcos_error.LCOSError
	DeleteSiteServiceableAreaPostcode(ctx utils.LCOSContext, id uint64, siteID string) *lcos_error.LCOSError
	ListSiteServiceableAreaPostcode(ctx utils.LCOSContext, request *basic_serviceable.ListSiteServiceableAreaPostcode) (map[string]interface{}, *lcos_error.LCOSError)
	ListAllSiteServiceableAreaPostcode(ctx utils.LCOSContext, request *basic_serviceable.ExportSiteServiceableAreaPostcode) ([]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab, *lcos_error.LCOSError)
	UploadSiteServiceableAreaPostcode(ctx utils.LCOSContext, request *basic_serviceable.UploadSiteServiceableAreaPostcodeRequest) *lcos_error.LCOSError
}

type siteServiceableAreaPostcodeService struct {
	siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
	siteServiceableAreaPostcodeDao  site_serviceable_area_postcode.SiteServiceableAreaPostcodeDaoInterface
}

func generateUniqueKey(siteID, postcode string) string {
	return fmt.Sprintf("site_id=%s, postcode=%s", siteID, postcode)
}

func (s *siteServiceableAreaPostcodeService) ListAllSiteServiceableAreaPostcode(ctx utils.LCOSContext, request *basic_serviceable.ExportSiteServiceableAreaPostcode) ([]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab, *lcos_error.LCOSError) {
	queryMap, err := utils.Struct2map(request)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	models, lcosErr := s.siteServiceableAreaPostcodeDao.ListAllSiteServiceableAreaPostcodes(ctx, *request.SiteID, queryMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return models, nil
}

// 检查是否overlap的函数key为 site_id+postcode->actual_point_id->LogisticSiteBasicServiceablePostcodeTab
func (s *siteServiceableAreaPostcodeService) checkOverlap(addedPostcodes, deletedPostcodes, postcodesFromDB map[string]map[string]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab, siteBasicConf *site_serviceable_area.LogisticSiteBasicServiceableConfTab, deleteModelsMap map[string][]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab) *lcos_error.LCOSError {
	// 先剔除需要删除的数据
	for key, locationMap := range addedPostcodes {
		for actualPointID := range locationMap {
			if item, ok1 := deletedPostcodes[key]; ok1 {
				if _, ok2 := item[actualPointID]; ok2 {
					// addedPostcode中的内容
					delete(addedPostcodes[key], actualPointID)
				}
			}
		}
	}

	for key, locationMap := range postcodesFromDB {
		for actualPointID := range locationMap {
			if item, ok1 := deletedPostcodes[key]; ok1 {
				if _, ok2 := item[actualPointID]; ok2 {
					// addedPostcode中的内容
					delete(postcodesFromDB[key], actualPointID)
				}
			}
		}
	}

	// 比较重叠数据
	for key, postcodeMap := range addedPostcodes {
		// 上传的数据本身存在overlap
		if len(postcodeMap) > 1 {
			errMsg := fmt.Sprintf("uploaded site serviceable area postcode overlap|site_id-postcode:%s", key)
			logger.LogError(errMsg)
			return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaLocationOverlapErrorCode, errMsg)
		}
		for _, actualPointStruct := range postcodeMap {
			if dbPostcodeMap, ok1 := postcodesFromDB[key]; ok1 {
				for _, dbActualPointStruct := range dbPostcodeMap {
					if actualPointStruct.ActualPointId != dbActualPointStruct.ActualPointId {
						if siteBasicConf.Override == constant.DISABLED {
							// override == 0, cause err as usual
							errMsg := fmt.Sprintf("uploaded site serviceable area postcodes overlap|%s, uploaded_postcodes:[%s], db_postcodes:[%s]", key, actualPointStruct.ActualPointId, dbActualPointStruct.ActualPointId)
							return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaLocationOverlapErrorCode, errMsg)
						} else {
							// override == 1, more delete need
							deleteModelsMap[siteBasicConf.SiteId] = append(deleteModelsMap[siteBasicConf.SiteId], dbActualPointStruct)
						}
					}
				}
			}
		}
	}
	return nil
}

func (s *siteServiceableAreaPostcodeService) parseSingleBasicServiceablePostcode(ctx utils.LCOSContext, rowData []string, lineNumber int, addPostcodeModelsMap map[string][]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab, deletePostcodeModelsMap map[string][]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab, siteInfoMap map[string]*site_serviceable_area.LogisticSiteBasicServiceableConfTab) *lcos_error.LCOSError {
	rowSiteId := rowData[0]

	// 检查site基础配置
	if _, ok := siteInfoMap[rowSiteId]; !ok {
		return lcos_error.NewLCOSError(lcos_error.NotFoundSiteServiceableAreaBasicConfErrorCode, fmt.Sprintf("cannot find site serviceable area basic conf|site_id=%v, line_num=%d", rowSiteId, lineNumber))
	}

	siteBasicConf := siteInfoMap[rowSiteId]

	if siteBasicConf.SsaType != constant.POSTCODE {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("ssa type of basic conf is not postcode|site_id=%v, line_num=%d", rowSiteId, lineNumber))

	}

	actualPointID := rowData[1]
	region := rowData[2]
	postcode := rowData[3]
	actionCode := rowData[4]
	siteServiceableAreaPostcodeModel := &site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{
		SiteId:        rowSiteId,
		ActualPointId: actualPointID,
		Region:        region,
		Postcode:      postcode,
	}

	if actionCode == "1" {
		addPostcodeModelsMap[rowSiteId] = append(addPostcodeModelsMap[rowSiteId], siteServiceableAreaPostcodeModel)
	} else {
		deletePostcodeModelsMap[rowSiteId] = append(deletePostcodeModelsMap[rowSiteId], siteServiceableAreaPostcodeModel)
	}

	return nil
}

func (s *siteServiceableAreaPostcodeService) UploadSiteServiceableAreaPostcode(ctx utils.LCOSContext, request *basic_serviceable.UploadSiteServiceableAreaPostcodeRequest) *lcos_error.LCOSError {
	region := ctx.GetCountry()
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//self-test code
	//region = "VN"
	//file, _ := excelize.OpenFile("/Users/<USER>/Desktop/actual_point_postcode.xlsx")
	//rows, _ := file.Rows("Sheet1")

	addPostcodeModelsMap := make(map[string][]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab)
	deletePostcodeModelsMap := make(map[string][]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab)
	siteIDMap := make(map[string]bool)                                                          // 用于去重
	var siteIDList []string                                                                     // 用于批量获取数据库中的site basic conf
	siteBasicConfMap := map[string]*site_serviceable_area.LogisticSiteBasicServiceableConfTab{} // siteID -> basic conf
	siteActualPointMap := map[string][]string{}                                                 // 生成site id actual point列表的map，用于请求lls
	siteActualPointExistMap := map[string]bool{}                                                // 用于去重
	var errList []*lcos_error.LCOSError
	var allRows [][]string

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}
		if err := CheckSSAPostcodeRowData(row, region, lineNum); err != nil {
			return err
		}

		// 将site id去重存在在列表中，方便批量查询
		rowSiteID := row[0]
		if _, ok := siteIDMap[rowSiteID]; !ok {
			siteIDList = append(siteIDList, rowSiteID)
			siteIDMap[rowSiteID] = true
		}

		rowActualPoint := row[1]

		// 存入key
		key := generateUniqueKey(rowSiteID, rowActualPoint)
		if _, ok := siteActualPointExistMap[key]; !ok {
			siteActualPointMap[rowSiteID] = append(siteActualPointMap[rowSiteID], rowActualPoint)
			siteActualPointExistMap[key] = true
		}

		allRows = append(allRows, row)
	}
	if len(siteIDList) == 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "site list cannot be empty")
	}

	// 检查actualPoint是否都有效
	lcosErr := common.ValidActualPoints(ctx, siteActualPointMap)
	if lcosErr != nil {
		return lcosErr
	}

	// 获取site basic conf的信息，并且存为map
	siteBasicConfs, lcosErr := s.siteServiceableAreaBasicConfDao.ListAllSiteServiceableAreaBasicConfs(ctx, map[string]interface{}{"site_id in": siteIDList})
	if lcosErr != nil {
		return lcosErr
	}
	for _, singleSiteBasicConf := range siteBasicConfs {
		siteBasicConfMap[singleSiteBasicConf.SiteId] = singleSiteBasicConf
	}

	for index, rowData := range allRows {
		tmpErrList := s.parseSingleBasicServiceablePostcode(ctx, rowData, index+1, addPostcodeModelsMap, deletePostcodeModelsMap, siteBasicConfMap)
		if tmpErrList != nil {
			errList = append(errList, tmpErrList)
		}
	}

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}

	// file overlap check, feat: SPLN-21958 site sa override
	conditionMap := make(map[string]bool)
	for _, postcodeList := range addPostcodeModelsMap {
		for _, postcode := range postcodeList {
			uniqKey := fmt.Sprintf("%s:%s:%s", postcode.SiteId, postcode.ActualPointId, postcode.Postcode)
			if exist, ok := conditionMap[uniqKey]; ok && exist {
				errMsg := fmt.Sprintf("Site Location overlap in file|site_id=%s, actual_point_id=%s, postcode=%s", postcode.SiteId, postcode.ActualPointId, postcode.Postcode)
				logger.CtxLogErrorf(ctx, errMsg)
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
			} else {
				conditionMap[uniqKey] = true
			}
		}
	}
	for _, postcodeList := range deletePostcodeModelsMap {
		for _, postcode := range postcodeList {
			uniqKey := fmt.Sprintf("%s:%s:%s", postcode.SiteId, postcode.ActualPointId, postcode.Postcode)
			if exist, ok := conditionMap[uniqKey]; ok && exist {
				errMsg := fmt.Sprintf("Site Location overlap in file|site_id=%s, actual_point_id=%s, postcode=%s", postcode.SiteId, postcode.ActualPointId, postcode.Postcode)
				logger.CtxLogErrorf(ctx, errMsg)
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
			} else {
				conditionMap[uniqKey] = true
			}
		}
	}

	// 校验overlap
	// 将addPostcodeModels和deletePostcodeModels存为 site_id+postcode->actual_point_id->postcode的map
	addedPostcodesForOverlap := map[string]map[string]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{}
	deletedPostcodesForOverlap := map[string]map[string]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{}
	for _, postcodes := range addPostcodeModelsMap {
		for _, postcode := range postcodes {
			key := generateUniqueKey(postcode.SiteId, postcode.Postcode)
			if _, ok := addedPostcodesForOverlap[key]; !ok {
				addedPostcodesForOverlap[key] = map[string]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{}
			}
			addedPostcodesForOverlap[key][postcode.ActualPointId] = postcode
		}
	}
	for _, postcodes := range deletePostcodeModelsMap {
		for _, postcode := range postcodes {
			key := generateUniqueKey(postcode.SiteId, postcode.Postcode)
			if _, ok := deletePostcodeModelsMap[key]; !ok {
				deletedPostcodesForOverlap[key] = map[string]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{}
			}
			deletedPostcodesForOverlap[key][postcode.ActualPointId] = postcode
		}
	}
	for siteID, siteBasicConf := range siteBasicConfMap {
		if siteBasicConf.AllowOverlap == constant.FALSE {
			// 查询数据库，获取所有site_id的服务范围数据
			dbPostcodes, lcosErr := s.siteServiceableAreaPostcodeDao.ListAllSiteServiceableAreaPostcodes(ctx, siteID, map[string]interface{}{"site_id": siteID})
			if lcosErr != nil {
				return lcosErr
			}
			// 将postcode存为校验overlap需要的格式
			postcodesFromDB := map[string]map[string]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{}
			for _, dbPostcode := range dbPostcodes {
				key := generateUniqueKey(dbPostcode.SiteId, dbPostcode.Postcode)
				if _, ok := postcodesFromDB[key]; !ok {
					postcodesFromDB[key] = map[string]*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{}
				}
				postcodesFromDB[key][dbPostcode.ActualPointId] = dbPostcode
			}
			if lcosErr := s.checkOverlap(addedPostcodesForOverlap, deletedPostcodesForOverlap, postcodesFromDB, siteBasicConf, deletePostcodeModelsMap); lcosErr != nil {
				return lcosErr
			}
		}
	}

	fc := func() *lcos_error.LCOSError {
		for siteID, addLocationModels := range addPostcodeModelsMap {
			//if err := s.lineBasicServiceableLocationDAO.BatchDeleteBasicServiceableLocation(ctx, lineId, addLocationModels); err != nil {
			//	return err
			//}
			if err := s.siteServiceableAreaPostcodeDao.BatchCreateSiteServiceableAreaPostcodeOnDuplicate(ctx, addLocationModels, siteID); err != nil {
				return err
			}
		}
		for siteID, deleteLocationModels := range deletePostcodeModelsMap {
			if err := s.siteServiceableAreaPostcodeDao.BatchDeleteSiteServiceableAreaPostcode(ctx, deleteLocationModels, siteID); err != nil {
				return err
			}
		}

		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(siteIDList, ","))
	return nil
}

func (s *siteServiceableAreaPostcodeService) CreateSiteServiceableAreaPostcode(ctx utils.LCOSContext, request *basic_serviceable.CreateSiteServiceableAreaPostcode) *lcos_error.LCOSError {
	if lcosErr := common.CheckSiteAndActualPointValid(ctx, request.SiteID, request.ActualPointID); lcosErr != nil {
		return lcosErr
	}
	// 先获取基础配置，获取不到则报错
	siteConfs, lcosErr := s.siteServiceableAreaBasicConfDao.ListAllSiteServiceableAreaBasicConfs(ctx, map[string]interface{}{"site_id": request.SiteID})
	if lcosErr != nil {
		errMsg := fmt.Sprintf("db read error|err=%s, site_id=%s", lcosErr.Msg, request.SiteID)
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, errMsg)
	}
	if len(siteConfs) == 0 {
		errMsg := fmt.Sprintf("cannot find site serviceable area basic conf|site_id=%s", request.SiteID)
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSError(lcos_error.NotFoundSiteServiceableAreaBasicConfErrorCode, errMsg)
	}
	siteConf := siteConfs[0]

	postcode := request.Postcode

	// 当前的conf必须为postcode
	if siteConf.SsaType != constant.POSTCODE {
		return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaLocationLevelNotValidErrorCode, "Site Serviceable Area Type not match, cannot create Postcode type")
	}

	// 检查是否重叠
	if siteConf.AllowOverlap == constant.FALSE {
		sitePostcodes, lcosErr := s.siteServiceableAreaPostcodeDao.ListAllSiteServiceableAreaPostcodes(ctx, request.SiteID, map[string]interface{}{"postcode": postcode, "site_id": request.SiteID})
		if lcosErr != nil {
			return lcosErr
		}
		if siteConf.Override == constant.DISABLED {
			// override == 0, cause err as usual
			if len(sitePostcodes) > 0 {
				errMsg := fmt.Sprintf("postcode overlap|request:%v, db:%v", request, sitePostcodes[0])
				return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaPostcodeOverlapErrorCode, errMsg)
			}
		} else {
			// override == 1, need to delete those in db, then insert new one
			err := s.siteServiceableAreaPostcodeDao.BatchDeleteSiteServiceableAreaPostcode(ctx, sitePostcodes, request.SiteID)
			if err != nil {
				return err
			}
		}
	}

	// 入库
	sitePostcodeModel := &site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{
		SiteId:        request.SiteID,
		ActualPointId: request.ActualPointID,
		Region:        siteConf.Region,
		Postcode:      postcode,
	}
	return s.siteServiceableAreaPostcodeDao.BatchCreateSiteServiceableAreaPostcode(ctx, []*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab{sitePostcodeModel}, request.SiteID)
}

func (s *siteServiceableAreaPostcodeService) DeleteSiteServiceableAreaPostcode(ctx utils.LCOSContext, id uint64, siteID string) *lcos_error.LCOSError {
	return s.siteServiceableAreaPostcodeDao.DeleteSiteServiceableAreaPostcode(ctx, id, siteID)
}

func (s *siteServiceableAreaPostcodeService) ListSiteServiceableAreaPostcode(ctx utils.LCOSContext, request *basic_serviceable.ListSiteServiceableAreaPostcode) (map[string]interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())

	var page uint32 = 1
	var count uint32 = 10
	if request.PageNo != nil {
		page = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}
	queryMap, err := utils.Struct2map(request)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	models, total, lcosErr := s.siteServiceableAreaPostcodeDao.ListSiteServiceableAreaPostcodesPaging(ctx, request.SiteID, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 查询lls，获取site name和actual point name，并且填充model
	if len(models) > 0 {
		var actualPointList []string
		var siteIDList []string
		var actualPointListInfoMap map[string]*lls_service.ActualSiteInfo
		var siteInfoMap map[string]*lls_service.SiteBaseInfo
		for _, model := range models {
			siteIDList = append(siteIDList, model.SiteId)
			if model.ActualPointId != "" {
				actualPointList = append(actualPointList, model.ActualPointId)
			}
		}
		// 存在真实点的时候查询真实点的name信息
		if len(actualPointList) > 0 {
			actualPointListInfoMap, lcosErr = lls_service.GetActualPointInfos(ctx, actualPointList)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}

		// 查询虚拟点的name信息
		if len(siteIDList) > 0 {
			siteInfoMap, lcosErr = lls_service.GetAllSiteInfo(ctx, region)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}

		for i := 0; i < len(models); i++ {
			if infoItem, ok1 := actualPointListInfoMap[models[i].ActualPointId]; ok1 {
				models[i].ActualPointName = infoItem.ActualPointName
				models[i].SiteName = infoItem.SiteName
			}
			if siteItem, ok2 := siteInfoMap[models[i].SiteId]; ok2 {
				models[i].SiteName = siteItem.SiteName
			}
		}
	}

	return map[string]interface{}{
		"count":  count,
		"pageno": page,
		"total":  total,
		"list":   models,
	}, nil
}

func NewSiteServiceableAreaPostcodeService(siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface, siteServiceableAreaPostcodeDao site_serviceable_area_postcode.SiteServiceableAreaPostcodeDaoInterface) *siteServiceableAreaPostcodeService {
	return &siteServiceableAreaPostcodeService{
		siteServiceableAreaBasicConfDao: siteServiceableAreaBasicConfDao,
		siteServiceableAreaPostcodeDao:  siteServiceableAreaPostcodeDao,
	}
}

var _ SiteServiceableAreaPostcodeInterface = (*siteServiceableAreaPostcodeService)(nil)
