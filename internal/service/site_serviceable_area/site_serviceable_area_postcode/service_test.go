package site_serviceable_area_postcode

import (
	"context"
	"fmt"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	site_serviceable_area "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	site_serviceable_area_postcode "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
)

func TestMain(m *testing.M) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		fmt.Println(err.Error())
		return
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		fmt.Println(fmt.Sprintf("getConfig %v", err))
		return
	}

	if err := startup.InitLibs(c); err != nil {
		fmt.Println(fmt.Sprintf("InitLibs Error: %v", err))
		return
	}
	os.Exit(m.Run())
}

func Test_siteServiceableAreaPostcodeService_UploadSiteServiceableAreaPostcode(t *testing.T) {
	type fields struct {
		siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
		siteServiceableAreaPostcodeDao  site_serviceable_area_postcode.SiteServiceableAreaPostcodeDaoInterface
	}
	type args struct {
		ctx     utils.LCOSContext
		request *basic_serviceable.UploadSiteServiceableAreaPostcodeRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "overlap=0, override=1",
			fields: fields{
				siteServiceableAreaBasicConfDao: site_serviceable_area.NewSiteServiceableAreaBasicConfDAO(),
				siteServiceableAreaPostcodeDao:  site_serviceable_area_postcode.NewSiteServiceableAreaPostcodeDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &basic_serviceable.UploadSiteServiceableAreaPostcodeRequest{
					FileUrl: "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &siteServiceableAreaPostcodeService{
				siteServiceableAreaBasicConfDao: tt.fields.siteServiceableAreaBasicConfDao,
				siteServiceableAreaPostcodeDao:  tt.fields.siteServiceableAreaPostcodeDao,
			}
			if got := s.UploadSiteServiceableAreaPostcode(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("siteServiceableAreaPostcodeService.UploadSiteServiceableAreaPostcode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_siteServiceableAreaPostcodeService_CreateSiteServiceableAreaPostcode(t *testing.T) {
	type fields struct {
		siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
		siteServiceableAreaPostcodeDao  site_serviceable_area_postcode.SiteServiceableAreaPostcodeDaoInterface
	}
	type args struct {
		ctx     utils.LCOSContext
		request *basic_serviceable.CreateSiteServiceableAreaPostcode
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "overlap=0, override=0",
			fields: fields{
				siteServiceableAreaBasicConfDao: site_serviceable_area.NewSiteServiceableAreaBasicConfDAO(),
				siteServiceableAreaPostcodeDao:  site_serviceable_area_postcode.NewSiteServiceableAreaPostcodeDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &basic_serviceable.CreateSiteServiceableAreaPostcode{
					SiteID:        "SVN4004",
					ActualPointID: "SVN4004-AP003",
					Postcode:      "23223",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &siteServiceableAreaPostcodeService{
				siteServiceableAreaBasicConfDao: tt.fields.siteServiceableAreaBasicConfDao,
				siteServiceableAreaPostcodeDao:  tt.fields.siteServiceableAreaPostcodeDao,
			}
			if got := s.CreateSiteServiceableAreaPostcode(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("siteServiceableAreaPostcodeService.CreateSiteServiceableAreaPostcode() = %v, want %v", got, tt.want)
			}
		})
	}
}
