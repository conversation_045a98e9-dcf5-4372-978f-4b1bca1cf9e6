package site_serviceable_area_postcode

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"strconv"
	"strings"
)

func CheckSSAPostcodeRowData(rowData []string, region string, lineNum int) *lcos_error.LCOSError {
	if len(rowData) < 5 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", rowData length not enough: "+strconv.Itoa(len(rowData)))
	}

	rowCountry := strings.TrimSpace(rowData[2])
	if rowCountry != region {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", region mismatch: "+rowCountry)
	}
	postcode := strings.TrimSpace(rowData[3])
	if len(postcode) == 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", postcode is not valid")
	}

	if actionCode, err := strconv.Atoi(rowData[4]); err != nil || !(actionCode == 1 || actionCode == -1) {
		errMsg := fmt.Sprintf("action code is not valid|action_code=%d", actionCode)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}

	return nil
}
