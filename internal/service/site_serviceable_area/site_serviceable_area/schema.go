package site_serviceable_area

type GetSiteServiceableAreaBasicRequest struct {
	SiteID             string `form:"site_id" json:"site_id" validate:"required"`
	StateLocationID    int    `form:"state_location_id" json:"state_location_id"`
	CityLocationID     int    `form:"city_location_id" json:"city_location_id"`
	DistrictLocationID int    `form:"district_location_id" json:"district_location_id"`
	StreetLocationID   int    `form:"street_location_id" json:"street_location_id"`
	Zipcode            string `form:"zipcode" json:"zipcode"`
	SkipPostcode       int    `form:"skip_postcode" json:"skip_postcode"`
	SkipCheck          int    `form:"skip_check" json:"skip_check"`
}
