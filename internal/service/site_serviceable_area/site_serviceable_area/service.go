package site_serviceable_area

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	Logger "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	site_serviceable_area "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	site_serviceable_area3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	site_serviceable_area2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	site_serviceable_area_postcode "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"strconv"
	"strings"
)

/*
* @Author: lei.guo
* @Date: 2021/11/18 12:12 上午
* @Name：site_serviceable_area
* @Description:
 */

const (
	getApByAddress = "0"
	getFallbackAp  = "1"
	getDefaultAp   = "2"
	getApError     = "3"
)

type SiteServiceableAreaInterface interface {
	GetSiteServiceableAreaList(ctx utils.LCOSContext, request *GetSiteServiceableAreaBasicRequest) ([]string, *lcos_error.LCOSError)
}

type siteServiceableAreaService struct {
	siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
	siteServiceableAreaLocationDao  site_serviceable_area2.SiteServiceableAreaLocationDaoInterface
	siteServiceableAreaPostcodeDao  site_serviceable_area_postcode.SiteServiceableAreaPostcodeDaoInterface
	siteServiceableAreaCepRangeDao  site_serviceable_area3.SiteServiceableAreaCepRangeDaoInterface
}

// GetSiteServiceableAreaList 获取点服务范围实际点（包含地址无法匹配的兜底逻辑）
func (s *siteServiceableAreaService) GetSiteServiceableAreaList(ctx utils.LCOSContext, request *GetSiteServiceableAreaBasicRequest) ([]string, *lcos_error.LCOSError) {
	reportLabels := map[string]string{
		"site_id":    request.SiteID,
		"query_type": getApByAddress,
	}

	// 1. 获取点服务范围基础配置
	basicConf, err := s.siteServiceableAreaBasicConfDao.GetSiteServiceableAreaBasicConfBySiteIDUsingCache(ctx, request.SiteID)
	if err != nil {
		reportLabels["query_type"] = getApError
		_ = metrics.CounterIncr(constant.MetricSiteServiceableCheck, reportLabels)
		Logger.OpsLogSiteBasicServiceableAreaData(ctx,
			Logger.SiteBasicServiceableAreaParams{SiteId: request.SiteID},
			Logger.SiteBasicServiceableAreaResult{Error: err.Msg},
		)
		return nil, err
	}
	Logger.OpsLogSiteBasicServiceableAreaData(ctx,
		Logger.SiteBasicServiceableAreaParams{Region: basicConf.Region, SiteId: basicConf.SiteId},
		Logger.SiteBasicServiceableAreaResult{SiteServiceableAreaCheck: basicConf.SsaCheck == constant.TRUE, DefaultActualPointList: basicConf.GetDefaultActualPointList()},
	)

	// 2. 查找实际点
	var actualPointList []string
	if basicConf.SsaCheck == constant.FALSE {
		// 2.1 如果基础配置不校验，则返回默认实际点
		actualPointList = basicConf.GetDefaultActualPointList()
		reportLabels["query_type"] = getDefaultAp
	} else {
		// 2.2 如果基础配置校验，则按照地址匹配实际点
		var getApErr *lcos_error.LCOSError
		actualPointList, getApErr = s.getActualPointsWithRequest(ctx, basicConf.SsaType, basicConf.LocationLevel, request)
		if getApErr != nil || len(actualPointList) == 0 {
			// 2.3 没有匹配到实际点的兜底逻辑
			if basicConf.AllowFallback == constant.TRUE {
				// 2.3.1 如果允许兜底，则返回兜底实际点
				actualPointList = append(actualPointList, basicConf.FallbackActualPoint)
				logger.CtxLogInfof(ctx, "use fallback actual point|req=%+v, ap_list=%v", request, actualPointList)
				reportLabels["query_type"] = getFallbackAp
			} else {
				// 2.3.2 如果不允许兜底，则返回error
				reportLabels["query_type"] = getApError
				_ = metrics.CounterIncr(constant.MetricSiteServiceableCheck, reportLabels)
				return nil, getApErr
			}
		}
	}

	// 3. 过滤实际点，只返回在lls有效的实际点
	validActualPointList, err := s.filterActualPoints(ctx, actualPointList)
	if err != nil {
		reportLabels["query_type"] = getApError
		_ = metrics.CounterIncr(constant.MetricSiteServiceableCheck, reportLabels)
		return nil, lcos_error.NewLCOSError(err.RetCode, fmt.Sprintf("filter actual points error|ap_list=%v, cause=%s", actualPointList, err.Msg))
	}
	_ = metrics.CounterIncr(constant.MetricSiteServiceableCheck, reportLabels)
	return validActualPointList, nil
}

// getActualPointsWithRequest 通过请求的地址信息匹配实际点
func (s *siteServiceableAreaService) getActualPointsWithRequest(ctx utils.LCOSContext, ssaType uint8, locationLevel uint8, request *GetSiteServiceableAreaBasicRequest) ([]string, *lcos_error.LCOSError) {
	var actualPointList []string
	var getApErr *lcos_error.LCOSError
	switch ssaType {
	case constant.LOCATION:
		// 根据基础配置的location校验层级从请求获中取对应的locationId
		var locationID int
		switch locationLevel {
		case constant.STATE:
			locationID = request.StateLocationID
		case constant.CITY:
			locationID = request.CityLocationID
		case constant.DISTRICT:
			locationID = request.DistrictLocationID
		case constant.STREET:
			locationID = request.StreetLocationID
		}
		var locations []*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab
		if uint8(request.SkipCheck) == constant.TRUE {
			// 如果请求skip check，则返回所有实际点
			locations, getApErr = s.siteServiceableAreaLocationDao.GetSiteServiceableAreaByLocationUsingCache(ctx, request.SiteID, constant.AllActualPointIDListLocation)
		} else {
			// 按照locationId匹配实际点
			locations, getApErr = s.siteServiceableAreaLocationDao.GetSiteServiceableAreaByLocationUsingCache(ctx, request.SiteID, locationID)
		}
		if getApErr != nil {
			Logger.OpsLogAPLocationServiceableAreaData(ctx,
				Logger.ApLocationServiceableAreaParams{SiteId: request.SiteID, LocationId: locationID},
				Logger.ApServiceableAreaResult{Error: getApErr.Msg},
			)
			return nil, getApErr
		}
		for _, location := range locations {
			actualPointList = append(actualPointList, location.ActualPointId)
		}
		Logger.OpsLogAPLocationServiceableAreaData(ctx,
			Logger.ApLocationServiceableAreaParams{SiteId: request.SiteID, LocationId: locationID},
			Logger.ApServiceableAreaResult{ActualPointList: actualPointList},
		)
	case constant.POSTCODE:
		var postcodes []*site_serviceable_area_postcode.LogisticSiteBasicServiceablePostcodeTab
		if uint8(request.SkipCheck) == constant.TRUE || uint8(request.SkipPostcode) == constant.TRUE {
			// 如果请求skip check或者skip postcode，则返回所有实际点
			postcodes, getApErr = s.siteServiceableAreaPostcodeDao.GetSiteServiceableAreaByPostcodeUsingCache(ctx, request.SiteID, constant.AllActualPointIDList)
		} else {
			postcodes, getApErr = s.siteServiceableAreaPostcodeDao.GetSiteServiceableAreaByPostcodeUsingCache(ctx, request.SiteID, request.Zipcode)
		}
		if getApErr != nil {
			Logger.OpsLogAPPostcodeServiceableAreaData(ctx,
				Logger.ApPostcodeServiceableAreaParams{SiteId: request.SiteID, Postcode: request.Zipcode},
				Logger.ApServiceableAreaResult{Error: getApErr.Msg},
			)
			return nil, getApErr
		}
		for _, singlePostcode := range postcodes {
			actualPointList = append(actualPointList, singlePostcode.ActualPointId)
		}
		Logger.OpsLogAPPostcodeServiceableAreaData(ctx,
			Logger.ApPostcodeServiceableAreaParams{SiteId: request.SiteID, Postcode: request.Zipcode},
			Logger.ApServiceableAreaResult{ActualPointList: actualPointList},
		)
	case constant.CEPRANGE:
		var cepRangeList []*site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab
		if uint8(request.SkipCheck) == constant.TRUE || uint8(request.SkipPostcode) == constant.TRUE {
			// 如果请求skip check或者skip postcode，则返回所有实际点
			cepRangeList, getApErr = s.siteServiceableAreaCepRangeDao.GetAllActualPointIDListBySiteIDUsingCache(ctx, request.SiteID)
		} else {
			// 处理zipcode，替换掉中划线
			zipCode := strings.ReplaceAll(request.Zipcode, "-", "")
			zipCodeInt, err := strconv.Atoi(zipCode)
			if err != nil {
				return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
			}
			cepRangeList, getApErr = s.siteServiceableAreaCepRangeDao.GetSiteServiceableAreaByPostcodeUsingCache(ctx, request.SiteID, zipCodeInt)
		}
		if getApErr != nil {
			Logger.OpsLogAPCEPServiceableAreaData(ctx,
				Logger.ApCEPServiceableAreaParams{SiteId: request.SiteID, CepCode: request.Zipcode},
				Logger.ApServiceableAreaResult{Error: getApErr.Msg},
			)
			return nil, getApErr
		}
		for _, singlePostcode := range cepRangeList {
			actualPointList = append(actualPointList, singlePostcode.ActualPointId)
		}
		Logger.OpsLogAPCEPServiceableAreaData(ctx,
			Logger.ApCEPServiceableAreaParams{SiteId: request.SiteID, CepCode: request.Zipcode},
			Logger.ApServiceableAreaResult{ActualPointList: actualPointList},
		)
	}
	return actualPointList, nil
}

// filterActualPoints 过滤有效的实际点
func (s *siteServiceableAreaService) filterActualPoints(ctx utils.LCOSContext, actualPointIdList []string) ([]string, *lcos_error.LCOSError) {
	if len(actualPointIdList) == 0 {
		return actualPointIdList, nil
	}
	actualPointInfoMap, err := lls_service.GetActualPointsMapWithGreySwitch(ctx, actualPointIdList)
	if err != nil {
		return nil, err
	}
	validActualPointIdList := make([]string, 0, len(actualPointIdList))
	for _, actualPointId := range actualPointIdList {
		if actualPointInfo, ok := actualPointInfoMap[actualPointId]; ok && actualPointInfo.GetRetcode() == 0 {
			validActualPointIdList = append(validActualPointIdList, actualPointId)
		} else {
			logger.CtxLogInfof(ctx, "filter actual point|ap_id=%s", actualPointId)
		}
	}
	return validActualPointIdList, nil
}

func NewSiteServiceableAreaService(siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface,
	siteServiceableAreaLocationDao site_serviceable_area2.SiteServiceableAreaLocationDaoInterface,
	siteServiceableAreaPostcodeDao site_serviceable_area_postcode.SiteServiceableAreaPostcodeDaoInterface,
	siteServiceableAreaCepRangeDao site_serviceable_area3.SiteServiceableAreaCepRangeDaoInterface) *siteServiceableAreaService {
	return &siteServiceableAreaService{
		siteServiceableAreaBasicConfDao: siteServiceableAreaBasicConfDao,
		siteServiceableAreaLocationDao:  siteServiceableAreaLocationDao,
		siteServiceableAreaPostcodeDao:  siteServiceableAreaPostcodeDao,
		siteServiceableAreaCepRangeDao:  siteServiceableAreaCepRangeDao,
	}
}

var _ SiteServiceableAreaInterface = (*siteServiceableAreaService)(nil)
