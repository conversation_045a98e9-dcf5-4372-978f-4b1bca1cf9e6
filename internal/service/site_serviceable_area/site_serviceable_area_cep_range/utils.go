package site_serviceable_area_cep_range

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"strconv"
	"strings"
)

func CheckSSACepRangeRowData(rowData []string, region string, lineNum int) *lcos_error.LCOSError {
	if len(rowData) < 6 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", rowData length not enough: "+strconv.Itoa(len(rowData)))
	}

	rowCountry := strings.TrimSpace(rowData[2])
	if rowCountry != region {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", region mismatch: "+rowCountry)
	}
	rowActionCode := strings.TrimSpace(rowData[5])
	var cepInitial, cepFinal int
	var err error
	if cepInitial, err = strconv.Atoi(rowData[3]); err != nil {
		errMsg := fmt.Sprintf("cep initial is not valid|line_num=%d", lineNum)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}
	if cepFinal, err = strconv.Atoi(rowData[4]); err != nil {
		errMsg := fmt.Sprintf("cep final is not valid|line_num=%d", lineNum)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}

	if cepInitial > cepFinal {
		errMsg := fmt.Sprintf("cep initial cannot larger than cep final|line_nume=%d", lineNum)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}
	if !(rowActionCode == "1" || rowActionCode == "-1") {
		errMsg := fmt.Sprintf("Please fill in Actioncode (1 or -1)|line_nume=%d", lineNum)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}

	return nil
}
