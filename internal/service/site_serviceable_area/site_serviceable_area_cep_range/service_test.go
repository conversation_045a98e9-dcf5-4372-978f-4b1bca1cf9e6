package site_serviceable_area_cep_range

import (
	"context"
	"fmt"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	site_serviceable_area "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	site_serviceable_area3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
)

func TestMain(m *testing.M) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		fmt.Println(err.Error())
		return
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		fmt.Println(fmt.Sprintf("getConfig %v", err))
		return
	}

	if err := startup.InitLibs(c); err != nil {
		fmt.Println(fmt.Sprintf("InitLibs Error: %v", err))
		return
	}
	os.Exit(m.Run())
}

func Test_siteServiceableAreaCepRangeService_UploadSiteServiceableAreaCepRange(t *testing.T) {
	type fields struct {
		siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
		siteServiceableAreaCepRangeDao  site_serviceable_area3.SiteServiceableAreaCepRangeDaoInterface
	}
	type args struct {
		ctx      utils.LCOSContext
		request  *basic_serviceable.UploadSiteServiceableAreaCepRangeRequest
		filePath string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "upload site cep range",
			fields: fields{
				siteServiceableAreaBasicConfDao: site_serviceable_area.NewSiteServiceableAreaBasicConfDAO(),
				siteServiceableAreaCepRangeDao:  site_serviceable_area3.NewSiteServiceableAreaCepRangeDAO(),
			},
			args: args{
				ctx:     utils.NewCommonCtx(context.Background()),
				request: &basic_serviceable.UploadSiteServiceableAreaCepRangeRequest{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &siteServiceableAreaCepRangeService{
				siteServiceableAreaBasicConfDao: tt.fields.siteServiceableAreaBasicConfDao,
				siteServiceableAreaCepRangeDao:  tt.fields.siteServiceableAreaCepRangeDao,
			}
			if got := s.UploadSiteServiceableAreaCepRange(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("siteServiceableAreaCepRangeService.UploadSiteServiceableAreaCepRange() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_siteServiceableAreaCepRangeService_CreateSiteServiceableAreaCepRange(t *testing.T) {
	type fields struct {
		siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
		siteServiceableAreaCepRangeDao  site_serviceable_area3.SiteServiceableAreaCepRangeDaoInterface
	}
	type args struct {
		ctx     utils.LCOSContext
		request *basic_serviceable.CreateSiteServiceableAreaCepRange
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "overlap=0, override=0",
			fields: fields{
				siteServiceableAreaBasicConfDao: site_serviceable_area.NewSiteServiceableAreaBasicConfDAO(),
				siteServiceableAreaCepRangeDao:  site_serviceable_area3.NewSiteServiceableAreaCepRangeDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &basic_serviceable.CreateSiteServiceableAreaCepRange{
					SiteID:        "SVN4004",
					ActualPointID: "SVN4004-AP003",
					CepInitial:    10,
					CepFinal:      100,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &siteServiceableAreaCepRangeService{
				siteServiceableAreaBasicConfDao: tt.fields.siteServiceableAreaBasicConfDao,
				siteServiceableAreaCepRangeDao:  tt.fields.siteServiceableAreaCepRangeDao,
			}
			if got := s.CreateSiteServiceableAreaCepRange(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("siteServiceableAreaCepRangeService.CreateSiteServiceableAreaCepRange() = %v, want %v", got, tt.want)
			}
		})
	}
}
