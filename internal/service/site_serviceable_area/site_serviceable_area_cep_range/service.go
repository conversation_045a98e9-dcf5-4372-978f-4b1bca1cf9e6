package site_serviceable_area_cep_range

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/intervaltree"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	site_serviceable_area "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	site_serviceable_area3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

/*
* @Author: lei.guo
* @Date: 2021/11/18 12:12 上午
* @Name：site_serviceable_area
* @Description:
 */

type SiteServiceableAreaCepRangeInterface interface {
	CreateSiteServiceableAreaCepRange(ctx utils.LCOSContext, request *basic_serviceable.CreateSiteServiceableAreaCepRange) *lcos_error.LCOSError
	DeleteSiteServiceableAreaCepRange(ctx utils.LCOSContext, id uint64, siteID string) *lcos_error.LCOSError
	ListSiteServiceableAreaCepRange(ctx utils.LCOSContext, request *basic_serviceable.ListSiteServiceableAreaCepRange) (map[string]interface{}, *lcos_error.LCOSError)
	ListAllSiteServiceableAreaCepRange(ctx utils.LCOSContext, request *basic_serviceable.ExportSiteServiceableAreaCepRange) ([]*site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab, *lcos_error.LCOSError)
	UploadSiteServiceableAreaCepRange(ctx utils.LCOSContext, request *basic_serviceable.UploadSiteServiceableAreaCepRangeRequest) *lcos_error.LCOSError
}

type siteServiceableAreaCepRangeService struct {
	siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
	siteServiceableAreaCepRangeDao  site_serviceable_area3.SiteServiceableAreaCepRangeDaoInterface
}

func (s *siteServiceableAreaCepRangeService) parseSingleBasicServiceableCepRange(ctx utils.LCOSContext, rowData []string, lineNumber int, addCepRangeModelsMap map[string][]*site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab, delCepRangeModelsMap map[string][]*site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab, siteInfoMap map[string]*site_serviceable_area.LogisticSiteBasicServiceableConfTab) *lcos_error.LCOSError {
	rowSiteId := rowData[0]
	rowActionCode := strings.Trim(rowData[5], " ")
	actualPointID := rowData[1]
	region := rowData[2]
	cepInitial := rowData[3]
	cepFinal := rowData[4]
	cepInitialInt, err1 := strconv.Atoi(cepInitial)
	cepFinalInt, err2 := strconv.Atoi(cepFinal)
	if err1 != nil || err2 != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cep range is not valid|site_id=%v, line_num=%d", rowSiteId, lineNumber))
	}
	if rowActionCode == "-1" {
		siteServiceableAreaCepRangeModel := &site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab{
			SiteId:        rowSiteId,
			ActualPointId: actualPointID,
			Region:        region,
			CepInitial:    cepInitialInt,
			CepFinal:      cepFinalInt,
		}
		delCepRangeModelsMap[rowSiteId] = append(delCepRangeModelsMap[rowSiteId], siteServiceableAreaCepRangeModel)
	} else {
		// 检查site基础配置
		if _, ok := siteInfoMap[rowSiteId]; !ok {
			return lcos_error.NewLCOSError(lcos_error.NotFoundSiteServiceableAreaBasicConfErrorCode, fmt.Sprintf("cannot find site serviceable area basic conf|site_id=%v, line_num=%d", rowSiteId, lineNumber))
		}

		siteBasicConf := siteInfoMap[rowSiteId]

		if siteBasicConf.SsaType != constant.CEPRANGE {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("ssa type of basic conf is not cep range|site_id=%v, line_num=%d", rowSiteId, lineNumber))
		}

		siteServiceableAreaCepRangeModel := &site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab{
			SiteId:        rowSiteId,
			ActualPointId: actualPointID,
			Region:        region,
			CepInitial:    cepInitialInt,
			CepFinal:      cepFinalInt,
		}
		addCepRangeModelsMap[rowSiteId] = append(addCepRangeModelsMap[rowSiteId], siteServiceableAreaCepRangeModel)
	}
	return nil
}

func (s *siteServiceableAreaCepRangeService) ListAllSiteServiceableAreaCepRange(ctx utils.LCOSContext, request *basic_serviceable.ExportSiteServiceableAreaCepRange) ([]*site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab, *lcos_error.LCOSError) {
	queryMap, err := utils.Struct2map(request)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	// cep code需要转为or的sql模式
	if request.CepCode != nil && *request.CepCode != 0 {
		queryMap["cep_initial <="] = *request.CepCode
		queryMap["cep_final >="] = *request.CepCode
		delete(queryMap, "cep_code")
	}
	models, lcosErr := s.siteServiceableAreaCepRangeDao.ListAllSiteServiceableAreaCepRanges(ctx, *request.SiteID, queryMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return models, nil
}

func (s *siteServiceableAreaCepRangeService) UploadSiteServiceableAreaCepRange(ctx utils.LCOSContext, request *basic_serviceable.UploadSiteServiceableAreaCepRangeRequest) *lcos_error.LCOSError {
	region := ctx.GetCountry()
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	// self-test code
	//region = "VN"
	//file, _ := excelize.OpenFile("/Users/<USER>/Desktop/actual_point_cep_range.xlsx")
	//rows, _ := file.Rows("Sheet1")

	addCepRangeModelsMap := make(map[string][]*site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab)
	delCepRangeModelsMap := make(map[string][]*site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab)
	addSiteIDMap := make(map[string]bool) // 用于去重
	delSiteIDMap := make(map[string]bool)
	delSiteKeyMap := make(map[string]bool)
	overrideCepRangeKeyMap := make(map[string]bool)

	var addSiteIDList []string // 用于批量获取数据库中的site basic conf
	var delSiteIDList []string
	siteBasicConfMap := map[string]*site_serviceable_area.LogisticSiteBasicServiceableConfTab{} // siteID -> basic conf
	siteActualPointMap := map[string][]string{}                                                 // 生成site id actual point列表的map，用于请求lls
	siteActualPointExistMap := map[string]bool{}                                                // 用于去重
	var errList []*lcos_error.LCOSError
	var allRows [][]string

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}

		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}
		if len(row) > 6 {
			row = row[:6]
			if serviceable_util.IsBlankRow(row) {
				continue
			}
		}
		if len(row) < 6 {
			if serviceable_util.IsBlankRow(row) {
				continue
			}
			if len(row) == 5 {
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "please fill in Actioncode (1 or -1)")
			}
			return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "column num is less than 6")
		}

		row = row[:6]
		if serviceable_util.IsBlankRow(row) {
			continue
		}
		if err := CheckSSACepRangeRowData(row, region, lineNum); err != nil {
			return err
		}

		rowActionCode := row[5]
		rowSiteID := row[0]
		// do basic check only when action code == 1
		if rowActionCode == "1" {
			// 将site id去重存在在列表中，方便批量查询
			if _, ok := addSiteIDMap[rowSiteID]; !ok {
				addSiteIDList = append(addSiteIDList, rowSiteID)
				addSiteIDMap[rowSiteID] = true
			}

			rowActualPoint := row[1]

			// 存入key
			key := fmt.Sprintf("site_id:%s, actual_point_id:%s", rowSiteID, rowActualPoint)
			if _, ok := siteActualPointExistMap[key]; !ok {
				siteActualPointMap[rowSiteID] = append(siteActualPointMap[rowSiteID], rowActualPoint)
				siteActualPointExistMap[key] = true
			}
		} else if rowActionCode == "-1" {
			if _, ok := delSiteIDMap[rowSiteID]; !ok {
				delSiteIDList = append(delSiteIDList, rowSiteID)
				delSiteIDMap[rowSiteID] = true
			}
		}
		allRows = append(allRows, row)
	}

	// 改成全部都为空才报错
	if len(addSiteIDList) == 0 && len(delSiteIDList) == 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "site list cannot be empty")
	}

	// 检查actualPoint是否都有效
	lcosErr := common.ValidActualPoints(ctx, siteActualPointMap)
	if lcosErr != nil {
		return lcosErr
	}

	// 获取site basic conf的信息，并且存为map
	siteBasicConfs, lcosErr := s.siteServiceableAreaBasicConfDao.ListAllSiteServiceableAreaBasicConfs(ctx, map[string]interface{}{"site_id in": addSiteIDList})
	if lcosErr != nil {
		return lcosErr
	}
	for _, singleSiteBasicConf := range siteBasicConfs {
		siteBasicConfMap[singleSiteBasicConf.SiteId] = singleSiteBasicConf
	}

	for index, rowData := range allRows {
		tmpErrList := s.parseSingleBasicServiceableCepRange(ctx, rowData, index+1, addCepRangeModelsMap, delCepRangeModelsMap, siteBasicConfMap)
		if tmpErrList != nil {
			errList = append(errList, tmpErrList)
		}
	}

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}

	// check whether there is overlap in file for the same AP
	addConditionMap := make(map[string]*intervaltree.IntervalTree)
	delConditionMap := make(map[string]*intervaltree.IntervalTree)
	for siteID, cepRangeList := range addCepRangeModelsMap {
		for _, cepRangeInfo := range cepRangeList {
			conditionKey := fmt.Sprintf("%s:%s", siteID, cepRangeInfo.ActualPointId)
			initial := uint64(cepRangeInfo.CepInitial)
			final := uint64(cepRangeInfo.CepFinal)
			if tree, ok := addConditionMap[conditionKey]; !ok {
				tree := intervaltree.New()
				_ = tree.Insert(initial, final)
				addConditionMap[conditionKey] = tree
			} else {
				if tree.Contains(final) || tree.Contains(initial) {
					errMsg := fmt.Sprintf("Cep range overlap in upload file |  [%d, %d], site_id=%s, actual_point=%s, actioncode=1", initial, final, cepRangeInfo.SiteId, cepRangeInfo.ActualPointId)
					logger.CtxLogErrorf(ctx, errMsg)
					return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
				} else {
					err := tree.Insert(initial, final)
					if err != nil {
						errMsg := fmt.Sprintf("Cep range overlap in upload file |  [%d, %d], site_id=%s, actual_point=%s, actioncode=1", initial, final, cepRangeInfo.SiteId, cepRangeInfo.ActualPointId)
						logger.CtxLogErrorf(ctx, errMsg)
						return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
					}
				}
			}
		}
	}

	for siteID, cepRangeList := range delCepRangeModelsMap {
		for _, cepRangeInfo := range cepRangeList {
			conditionKey := fmt.Sprintf("%s:%s", siteID, cepRangeInfo.ActualPointId)
			initial := uint64(cepRangeInfo.CepInitial)
			final := uint64(cepRangeInfo.CepFinal)
			if tree, ok := delConditionMap[conditionKey]; !ok {
				tree := intervaltree.New()
				_ = tree.Insert(initial, final)
				delConditionMap[conditionKey] = tree
			} else {
				if tree.Contains(final) || tree.Contains(initial) {
					errMsg := fmt.Sprintf("Cep range overlap in upload file | [%d, %d], site_id=%s, actual_point=%s, actioncode=-1", initial, final, cepRangeInfo.SiteId, cepRangeInfo.ActualPointId)
					logger.CtxLogErrorf(ctx, errMsg)
					return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
				} else {
					err := tree.Insert(initial, final)
					if err != nil {
						errMsg := fmt.Sprintf("Cep range overlap in upload file | [%d, %d], site_id=%s, actual_point=%s, actioncode=-1", initial, final, cepRangeInfo.SiteId, cepRangeInfo.ActualPointId)
						logger.CtxLogErrorf(ctx, errMsg)
						return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
					}
				}
			}
		}
	}
	// 校验delete是否重复
	for _, siteID := range delSiteIDList {
		if delCepRangeList, ok := delCepRangeModelsMap[siteID]; ok {
			dbCepRangeMap := make(map[string]bool)
			// 查询数据库，获取所有site_id的服务范围数据
			dbCepRanges, lcosErr := s.siteServiceableAreaCepRangeDao.ListAllSiteServiceableAreaCepRanges(ctx, siteID, map[string]interface{}{"site_id": siteID})
			if lcosErr != nil {
				return lcosErr
			}
			for _, cepRangeInfo := range dbCepRanges {
				cepKey := fmt.Sprintf("%s:%s:%d:%d", cepRangeInfo.SiteId, cepRangeInfo.ActualPointId, cepRangeInfo.CepInitial, cepRangeInfo.CepFinal)
				dbCepRangeMap[cepKey] = true
			}
			for _, delInfo := range delCepRangeList {
				key := fmt.Sprintf("%s:%s:%d:%d", delInfo.SiteId, delInfo.ActualPointId, delInfo.CepInitial, delInfo.CepFinal)
				if _, ok := dbCepRangeMap[key]; !ok {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("Cannot find to delete, Initial and Final should be same with db | [%d, %d], site_id=%s, actual_point=%s", delInfo.CepInitial, delInfo.CepFinal, delInfo.SiteId, delInfo.ActualPointId))
				}
				// 确定会删除的条目，对siteId-actual_point-initial-final的key做标记，在校验插入重复时不校验
				delSiteKeyMap[key] = true
			}
		}
	}
	// 校验insert overlap
	for siteID, siteBasicConf := range siteBasicConfMap {
		if siteBasicConf.AllowOverlap == constant.FALSE {
			// 使用范围查找树查询是否重叠
			tree := intervaltree.New()
			if importedCepRangeList, ok := addCepRangeModelsMap[siteID]; ok {
				// 先检查传入的数据是否重叠
				for _, importedCepRange := range importedCepRangeList {
					if siteBasicConf.Override == constant.ENABLED {
						key := fmt.Sprintf("%d:%d", importedCepRange.CepInitial, importedCepRange.CepFinal)
						overrideCepRangeKeyMap[key] = true
					}
					if tree.Contains(uint64(importedCepRange.CepInitial)) || tree.Contains(uint64(importedCepRange.CepFinal)) {
						errMsg := fmt.Sprintf("Cep range in upload file overlap with db | db ceprange [%d, %d], site_id=%s, actual_point=%s", importedCepRange.CepInitial, importedCepRange.CepFinal, importedCepRange.SiteId, importedCepRange.ActualPointId)
						logger.CtxLogErrorf(ctx, errMsg)
						return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
					} else {
						err1 := tree.Insert(uint64(importedCepRange.CepInitial), uint64(importedCepRange.CepFinal))
						if err1 != nil {
							errMsg := fmt.Sprintf("Cep range in upload file overlap with db | db ceprange [%d, %d], site_id=%s, actual_point=%s", importedCepRange.CepInitial, importedCepRange.CepFinal, importedCepRange.SiteId, importedCepRange.ActualPointId)
							logger.CtxLogErrorf(ctx, errMsg)
							return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
						}
					}
				}
				// 查询数据库，获取所有site_id的服务范围数据
				dbCepRanges, lcosErr := s.siteServiceableAreaCepRangeDao.ListAllSiteServiceableAreaCepRanges(ctx, siteID, map[string]interface{}{"site_id": siteID})
				if lcosErr != nil {
					return lcosErr
				}
				for _, dbCepRange := range dbCepRanges {
					key := fmt.Sprintf("%s:%s:%d:%d", dbCepRange.SiteId, dbCepRange.ActualPointId, dbCepRange.CepInitial, dbCepRange.CepFinal)
					overrideKey := fmt.Sprintf("%d:%d", dbCepRange.CepInitial, dbCepRange.CepFinal)
					if exist, ok := delSiteKeyMap[key]; ok && exist {
						// 需要删除的db条目不再校验
						continue
					}
					_, canOverride := overrideCepRangeKeyMap[overrideKey]
					if siteBasicConf.Override == constant.ENABLED && canOverride {
						// override == 1, need initial & final both equal as well.
						delCepRangeModelsMap[siteID] = append(delCepRangeModelsMap[siteID], dbCepRange)
						continue
					}
					if tree.Contains(uint64(dbCepRange.CepInitial)) || tree.Contains(uint64(dbCepRange.CepFinal)) {
						// override == 0 or cannot override, check duplicate as usual.
						errMsg := fmt.Sprintf("Cep range in upload file overlap with db | db ceprange [%d, %d], site_id=%s, actual_point=%s ", dbCepRange.CepInitial, dbCepRange.CepFinal, dbCepRange.SiteId, dbCepRange.ActualPointId)
						logger.CtxLogErrorf(ctx, errMsg)
						return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
					} else {
						err1 := tree.Insert(uint64(dbCepRange.CepInitial), uint64(dbCepRange.CepFinal))
						if err1 != nil {
							errMsg := fmt.Sprintf("Cep range in upload file overlap with db | db ceprange [%d, %d], site_id=%s, actual_point=%s", dbCepRange.CepInitial, dbCepRange.CepFinal, dbCepRange.SiteId, dbCepRange.ActualPointId)
							logger.CtxLogErrorf(ctx, errMsg)
							return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
						}
					}

				}
			}

		} else {
			// 做一个map，使得每个actual point都可以校验各自是否重叠
			actualPointIntervalTree := map[string]*intervaltree.IntervalTree{}
			if importedCepRangeList, ok := addCepRangeModelsMap[siteID]; ok {
				for _, importedCepRange := range importedCepRangeList {
					if _, ok := actualPointIntervalTree[importedCepRange.ActualPointId]; !ok {
						actualPointIntervalTree[importedCepRange.ActualPointId] = intervaltree.New()
					}
					tree := actualPointIntervalTree[importedCepRange.ActualPointId]
					if tree.Contains(uint64(importedCepRange.CepInitial)) || tree.Contains(uint64(importedCepRange.CepFinal)) {
						errMsg := fmt.Sprintf("imported cep range overlap, cep_initial:%d, cep_final:%d, site_id=%s, actual_point=%s", importedCepRange.CepInitial, importedCepRange.CepFinal, importedCepRange.SiteId, importedCepRange.ActualPointId)
						logger.CtxLogErrorf(ctx, errMsg)
						return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
					} else {
						err1 := tree.Insert(uint64(importedCepRange.CepInitial), uint64(importedCepRange.CepFinal))
						if err1 != nil {
							errMsg := fmt.Sprintf("imported cep range overlap, cep_initial:%d, cep_final:%d, site_id=%s, actual_point=%s", importedCepRange.CepInitial, importedCepRange.CepFinal, importedCepRange.SiteId, importedCepRange.ActualPointId)
							logger.CtxLogErrorf(ctx, errMsg)
							return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
						}
					}
				}
				// 查询数据库，获取所有site_id的服务范围数据
				dbCepRanges, lcosErr := s.siteServiceableAreaCepRangeDao.ListAllSiteServiceableAreaCepRanges(ctx, siteID, map[string]interface{}{"site_id": siteID})
				if lcosErr != nil {
					return lcosErr
				}
				for _, dbCepRange := range dbCepRanges {
					if _, ok := actualPointIntervalTree[dbCepRange.ActualPointId]; !ok {
						continue
					}
					key := fmt.Sprintf("%s:%s:%d:%d", dbCepRange.SiteId, dbCepRange.ActualPointId, dbCepRange.CepInitial, dbCepRange.CepFinal)
					if exist, ok := delSiteKeyMap[key]; ok && exist {
						// 需要删除的db条目不再校验
						continue
					}
					tree := actualPointIntervalTree[dbCepRange.ActualPointId]
					if tree.Contains(uint64(dbCepRange.CepInitial)) || tree.Contains(uint64(dbCepRange.CepFinal)) {
						errMsg := fmt.Sprintf("import cep range overlap with db cep range, cep_initial:%d, cep_final:%d, site_id=%s, actual_point=%s", dbCepRange.CepInitial, dbCepRange.CepFinal, dbCepRange.SiteId, dbCepRange.ActualPointId)
						logger.CtxLogErrorf(ctx, errMsg)
						return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
					} else {
						err1 := tree.Insert(uint64(dbCepRange.CepInitial), uint64(dbCepRange.CepFinal))
						if err1 != nil {
							errMsg := fmt.Sprintf("imported cep range overlap with db cep range, cep_initial:%d, cep_final:%d, site_id=%s, actual_point=%s", dbCepRange.CepInitial, dbCepRange.CepFinal, dbCepRange.SiteId, dbCepRange.ActualPointId)
							logger.CtxLogErrorf(ctx, errMsg)
							return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
						}
					}
				}
			}

		}
	}
	for siteID, delCepRangeModel := range delCepRangeModelsMap {
		if err := s.siteServiceableAreaCepRangeDao.BatchDeleteSiteServiceableAreaCepRangeModel(ctx, delCepRangeModel, siteID, constant.DBMAXBATCHDELNUM); err != nil {
			return err
		}
	}

	fc := func() *lcos_error.LCOSError {
		for siteID, addCepRangeModel := range addCepRangeModelsMap {
			//if err := s.lineBasicServiceableLocationDAO.BatchDeleteBasicServiceableLocation(ctx, lineId, addLocationModels); err != nil {
			//	return err
			//}
			if err := s.siteServiceableAreaCepRangeDao.BatchCreateSiteServiceableAreaCepRangeOnDuplicateUpdate(ctx, addCepRangeModel, siteID); err != nil {
				return err
			}
		}
		return nil
	}
	lcosErr = ctx.Transaction(fc)
	if lcosErr != nil {
		return lcosErr
	}
	// 当delete - create都完成以后，手动刷新缓存
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.SiteServiceableAreaCepRangeTabNamespace)
	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(addSiteIDList, ",")+","+strings.Join(delSiteIDList, ","))
	return nil
}

func (s *siteServiceableAreaCepRangeService) CreateSiteServiceableAreaCepRange(ctx utils.LCOSContext, request *basic_serviceable.CreateSiteServiceableAreaCepRange) *lcos_error.LCOSError {
	var lcosErr *lcos_error.LCOSError

	if lcosErr = common.CheckSiteAndActualPointValid(ctx, request.SiteID, request.ActualPointID); lcosErr != nil {
		return lcosErr
	}

	// 先获取基础配置，获取不到则报错
	siteConfs, lcosErr := s.siteServiceableAreaBasicConfDao.ListAllSiteServiceableAreaBasicConfs(ctx, map[string]interface{}{"site_id": request.SiteID})
	if lcosErr != nil {
		errMsg := fmt.Sprintf("db read error|error=%s, site_id=%s", lcosErr.Msg, request.SiteID)
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, errMsg)
	}
	if len(siteConfs) == 0 {
		errMsg := fmt.Sprintf("cannot find site serviceable area basic conf|site_id=%s", request.SiteID)
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSError(lcos_error.NotFoundSiteServiceableAreaBasicConfErrorCode, errMsg)
	}
	siteConf := siteConfs[0]

	cepInitial := request.CepInitial
	cepFinal := request.CepFinal

	if cepInitial > cepFinal {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cep initial cannot larger than cep final")
	}

	// 当前的conf必须为cep range
	if siteConf.SsaType != constant.CEPRANGE {
		return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaLocationLevelNotValidErrorCode, "Site Serviceable Area Type not match, cannot create Cep Range type")
	}

	// 检查是否重叠
	var siteCepRanges []*site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab
	if siteConf.AllowOverlap == constant.FALSE {
		siteCepRanges, lcosErr = s.siteServiceableAreaCepRangeDao.ListAllSiteServiceableAreaCepRanges(ctx, request.SiteID, map[string]interface{}{"cep_initial <=": cepFinal, "cep_final >=": cepInitial, "site_id": request.SiteID})
	} else {
		// allow overlap为true时，同一个actual point id不能overlap
		siteCepRanges, lcosErr = s.siteServiceableAreaCepRangeDao.ListAllSiteServiceableAreaCepRanges(ctx, request.SiteID, map[string]interface{}{"cep_initial <=": cepFinal, "cep_final >=": cepInitial, "site_id": request.SiteID, "actual_point_id": request.ActualPointID})
	}
	if lcosErr != nil {
		return lcosErr
	}
	if siteConf.AllowOverlap == constant.FALSE {
		// if override enabled, need to kick out old data in database.
		// only if initial & final both equal can override
		canOverride := false
		if siteConf.Override == constant.ENABLED {
			for _, dbCepRange := range siteCepRanges {
				if dbCepRange.CepInitial == cepInitial && dbCepRange.CepFinal == cepFinal {
					canOverride = true
					err := s.siteServiceableAreaCepRangeDao.DeleteSiteServiceableAreaCepRange(ctx, dbCepRange.Id, request.SiteID)
					if err != nil {
						return err
					}
				}
			}
		}
		if !canOverride && len(siteCepRanges) > 0 {
			errMsg := fmt.Sprintf("cep range overlap|request:%v, db:%v", request, siteCepRanges[0])
			return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
		}
	} else {
		if len(siteCepRanges) > 0 {
			errMsg := fmt.Sprintf("cep range overlap|request:%v, db:%v", request, siteCepRanges[0])
			return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaCepRangeOverlapErrorCode, errMsg)
		}
	}

	// 入库
	siteCepRangeModel := &site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab{
		SiteId:        request.SiteID,
		ActualPointId: request.ActualPointID,
		Region:        siteConf.Region,
		CepFinal:      cepFinal,
		CepInitial:    cepInitial,
	}
	err := s.siteServiceableAreaCepRangeDao.BatchCreateSiteServiceableAreaCepRange(ctx, []*site_serviceable_area3.LogisticSiteBasicServiceableCepRangeTab{siteCepRangeModel}, request.SiteID)
	if err != nil {
		return err
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.SiteServiceableAreaCepRangeTabNamespace)
	return nil
}

func (s *siteServiceableAreaCepRangeService) DeleteSiteServiceableAreaCepRange(ctx utils.LCOSContext, id uint64, siteID string) *lcos_error.LCOSError {
	err := s.siteServiceableAreaCepRangeDao.DeleteSiteServiceableAreaCepRange(ctx, id, siteID)
	if err != nil {
		return err
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.SiteServiceableAreaCepRangeTabNamespace)
	return nil
}

func (s *siteServiceableAreaCepRangeService) ListSiteServiceableAreaCepRange(ctx utils.LCOSContext, request *basic_serviceable.ListSiteServiceableAreaCepRange) (map[string]interface{}, *lcos_error.LCOSError) {

	region := strings.ToUpper(ctx.GetCountry())

	var page uint32 = 1
	var count uint32 = 10
	if request.PageNo != nil {
		page = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}
	queryMap, err := utils.Struct2map(request)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	// cep code需要转为or的sql模式
	if request.CepCode != nil && *request.CepCode != 0 {
		queryMap["cep_initial <="] = *request.CepCode
		queryMap["cep_final >="] = *request.CepCode
		delete(queryMap, "cep_code")
	}
	models, total, lcosErr := s.siteServiceableAreaCepRangeDao.ListSiteServiceableAreaCepRangesPaging(ctx, request.SiteID, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 查询lls，获取site name和actual point name，并且填充model
	if len(models) > 0 {
		var actualPointList []string
		var siteIDList []string
		var actualPointListInfoMap map[string]*lls_service.ActualSiteInfo
		var siteInfoMap map[string]*lls_service.SiteBaseInfo
		for _, model := range models {
			siteIDList = append(siteIDList, model.SiteId)
			if model.ActualPointId != "" {
				actualPointList = append(actualPointList, model.ActualPointId)
			}
		}
		// 存在真实点的时候查询真实点的name信息
		if len(actualPointList) > 0 {
			actualPointListInfoMap, lcosErr = lls_service.GetActualPointInfos(ctx, actualPointList)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}

		// 查询虚拟点的name信息
		if len(siteIDList) > 0 {
			siteInfoMap, lcosErr = lls_service.GetAllSiteInfo(ctx, region)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}

		for i := 0; i < len(models); i++ {
			if infoItem, ok1 := actualPointListInfoMap[models[i].ActualPointId]; ok1 {
				models[i].ActualPointName = infoItem.ActualPointName
				models[i].SiteName = infoItem.SiteName
			}
			if siteItem, ok2 := siteInfoMap[models[i].SiteId]; ok2 {
				models[i].SiteName = siteItem.SiteName
			}
		}
	}

	return map[string]interface{}{
		"count":  count,
		"pageno": page,
		"total":  total,
		"list":   models,
	}, nil
}

func NewSiteServiceableAreaCepRangeService(siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface, siteServiceableAreaCepRangeDao site_serviceable_area3.SiteServiceableAreaCepRangeDaoInterface) *siteServiceableAreaCepRangeService {
	return &siteServiceableAreaCepRangeService{
		siteServiceableAreaBasicConfDao: siteServiceableAreaBasicConfDao,
		siteServiceableAreaCepRangeDao:  siteServiceableAreaCepRangeDao,
	}
}

var _ SiteServiceableAreaCepRangeInterface = (*siteServiceableAreaCepRangeService)(nil)
