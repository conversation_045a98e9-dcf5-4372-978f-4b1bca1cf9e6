package common

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
)

func ValidActualPoints(ctx utils.LCOSContext, siteActualPointMap map[string][]string) *lcos_error.LCOSError {
	for siteID, actualPointList := range siteActualPointMap {
		actualPointStructMap, lcosErr := lls_service.GetActualPointInfos(ctx, actualPointList)
		if lcosErr != nil {
			return lcosErr
		}
		// 批量检查actual point是否存在
		for _, actualPointStruct := range actualPointStructMap {
			actualPointStructMap[actualPointStruct.ActualPointID] = actualPointStruct
		}

		// 检查
		for _, actualPoint := range actualPointList {
			if _, ok := actualPointStructMap[actualPoint]; !ok {
				errMsg := fmt.Sprintf("cannot find actual point|site_id=%s, actual_point_id=%s", siteID, actualPoint)
				logger.LogInfo(errMsg)
				return lcos_error.NewLCOSError(lcos_error.NotFoundActualPointErrorCode, errMsg)
			}
		}
	}
	return nil
}

// CheckSiteAndActualPointValid 校验actual point id和site匹配
func CheckSiteAndActualPointValid(ctx utils.LCOSContext, siteID, actualPointID string) *lcos_error.LCOSError {
	infoMap, lcosErr := lls_service.GetActualPointInfos(ctx, []string{actualPointID})
	if lcosErr != nil {
		return lcosErr
	}
	if infoItem, ok := infoMap[actualPointID]; !ok || infoItem.SiteID != siteID {
		return lcos_error.NewLCOSError(lcos_error.ActualPointNotValidErrorCode, fmt.Sprintf("actual point is not valid|actual_point=%s, site_id=%s", actualPointID, siteID))
	}
	return nil
}

// BatchCheckSiteAndActualPointValid 批量校验实际点是否存在且是否属于指定的点
func BatchCheckSiteAndActualPointValid(ctx utils.LCOSContext, siteId string, apIdList []string) *lcos_error.LCOSError {
	apInfoMap, err := lls_service.GetActualPointInfos(ctx, apIdList)
	if err != nil {
		return err
	}
	for _, apId := range apIdList {
		apInfo, ok := apInfoMap[apId]
		if !ok {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "actual point %s not found", apId)
		}
		if apInfo.SiteID != siteId {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "actual point %s not belong to site %s", apId, siteId)
		}
	}
	return nil
}
