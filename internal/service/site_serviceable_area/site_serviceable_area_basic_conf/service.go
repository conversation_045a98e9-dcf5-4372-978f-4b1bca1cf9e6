package site_serviceable_area_basic_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	site_serviceable_area "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"strings"
)

/*
* @Author: lei.guo
* @Date: 2021/11/18 12:12 上午
* @Name：site_serviceable_area
* @Description:
 */

type SiteServiceableAreaBasicConfInterface interface {
	CreateSiteServiceableAreaBasicConf(ctx utils.LCOSContext, request *basic_serviceable.CreateSiteServiceableAreaBasicConf) *lcos_error.LCOSError
	UpdateSiteServiceableAreaBasicConf(ctx utils.LCOSContext, request *basic_serviceable.UpdateSiteServiceableAreaBasicConf) *lcos_error.LCOSError
	DeleteSiteServiceableAreaBasicConf(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError
	ListSiteServiceableAreaBasicConf(ctx utils.LCOSContext, request *basic_serviceable.ListSiteServiceableAreaBasicConf) (map[string]interface{}, *lcos_error.LCOSError)
}

type siteServiceableAreaBasicConfService struct {
	siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
}

func validSsaTypeAndLocationLevel(ssaType, locationLevel uint8) *lcos_error.LCOSError {
	if !utils.CheckInUint8(ssaType, []uint8{constant.LOCATION, constant.POSTCODE, constant.CEPRANGE}) {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "site sa type is invalid")
	}
	if ssaType == constant.LOCATION && !(locationLevel == constant.STATE || locationLevel == constant.CITY || locationLevel == constant.DISTRICT || locationLevel == constant.STREET) {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "location level has to be state/city/district/street when ssa_type=location")
	}
	return nil
}

// 基本的参数校验
func validSiteServiceableAreaBasicConf(ctx utils.LCOSContext, siteId string, ssaCheck, ssaType, locationLevel uint8, defaultApList []string, allowFallback uint8, fallbackAp string) *lcos_error.LCOSError {
	if ssaCheck == constant.FALSE {
		// 不校验点服务范围时，不要求sa type必传，但如果传了的话则需要校验
		if ssaType != 0 {
			if err := validSsaTypeAndLocationLevel(ssaType, locationLevel); err != nil {
				return err
			}
		}

		// 不校验点服务范围时，必须配置有效的默认实际点
		if len(defaultApList) == 0 {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "default actual points is required when site sa check is disabled")
		}
		if err := common.BatchCheckSiteAndActualPointValid(ctx, siteId, defaultApList); err != nil {
			return err
		}
	} else {
		// 校验点服务范围时，需要校验sa type和location level
		if err := validSsaTypeAndLocationLevel(ssaType, locationLevel); err != nil {
			return err
		}

		// 校验点服务范围时，如果允许兜底，那么兜底实际点不允许为空，且需要有效
		if allowFallback == constant.TRUE {
			if len(fallbackAp) == 0 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "fallback actual point cannot be empty when allow fallback")
			}
			if err := common.BatchCheckSiteAndActualPointValid(ctx, siteId, []string{fallbackAp}); err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *siteServiceableAreaBasicConfService) CreateSiteServiceableAreaBasicConf(ctx utils.LCOSContext, request *basic_serviceable.CreateSiteServiceableAreaBasicConf) *lcos_error.LCOSError {
	region := request.Region
	if len(region) == 0 {
		region = strings.ToUpper(ctx.GetCountry())
	}
	// 1. 校验site id是否合法且存在
	if err := utils.CheckSiteValid(request.SiteID, region); err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	if siteInfo, err := lls_service.GetSiteInfo(ctx, request.SiteID); err != nil || siteInfo == nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "site %s not found", request.SiteID)
	}
	// 2. 校验site sa基础配置字段
	if err := validSiteServiceableAreaBasicConf(ctx, request.SiteID, request.SsaCheck, request.SsaType, request.LocationLevel, request.DefaultActualPointList, request.AllowFallback, request.FallbackActualPoint); err != nil {
		return err
	}
	model := &site_serviceable_area.LogisticSiteBasicServiceableConfTab{
		SiteId:              request.SiteID,
		Region:              region,
		SsaType:             request.SsaType,
		LocationLevel:       request.LocationLevel,
		AllowOverlap:        request.AllowOverlap,
		SsaCheck:            request.SsaCheck,
		Override:            request.Override,
		DefaultActualPoint:  strings.Join(request.DefaultActualPointList, ","),
		AllowFallback:       request.AllowFallback,
		FallbackActualPoint: request.FallbackActualPoint,
	}
	return s.siteServiceableAreaBasicConfDao.BatchCreateSiteServiceableAreaBasicConf(ctx, []*site_serviceable_area.LogisticSiteBasicServiceableConfTab{model})
}

func (s *siteServiceableAreaBasicConfService) UpdateSiteServiceableAreaBasicConf(ctx utils.LCOSContext, request *basic_serviceable.UpdateSiteServiceableAreaBasicConf) *lcos_error.LCOSError {
	basicConf, err := s.siteServiceableAreaBasicConfDao.GetSiteServiceableAreabasicConfById(ctx, request.ID)
	if err != nil {
		return err
	}
	if err := validSiteServiceableAreaBasicConf(ctx, basicConf.SiteId, request.SsaCheck, request.SsaType, request.LocationLevel, request.DefaultActualPointList, request.AllowFallback, request.FallbackActualPoint); err != nil {
		return err
	}

	updateData, convertErr := utils.Struct2map(request)
	if convertErr != nil {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, convertErr.Error())
	}
	updateData["default_actual_point"] = strings.Join(request.DefaultActualPointList, ",")
	return s.siteServiceableAreaBasicConfDao.UpdateSiteServiceableAreaBasicConf(ctx, updateData, request.ID)
}

func (s *siteServiceableAreaBasicConfService) DeleteSiteServiceableAreaBasicConf(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return s.siteServiceableAreaBasicConfDao.DeleteSiteServiceableAreaBasicConf(ctx, id)
}

func (s *siteServiceableAreaBasicConfService) ListSiteServiceableAreaBasicConf(ctx utils.LCOSContext, request *basic_serviceable.ListSiteServiceableAreaBasicConf) (map[string]interface{}, *lcos_error.LCOSError) {
	var page uint32 = 1
	var count uint32 = 10
	if request.PageNo != nil {
		page = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}
	queryMap, err := utils.Struct2map(request)

	region := request.Region
	if len(region) == 0 {
		region = strings.ToUpper(ctx.GetCountry())
	}
	if region != "" {
		queryMap["region"] = region
	}

	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	models, total, lcosErr := s.siteServiceableAreaBasicConfDao.ListSiteServiceableAreaBasicConfsPaging(ctx, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 查询lls，获取site name和actual point name，并且填充model
	if len(models) > 0 {
		var actualPointList []string
		actualPointListMap := map[string]bool{} // 用于去重actual point
		var siteIDList []string
		var actualPointListInfoMap map[string]*lls_service.ActualSiteInfo
		var siteInfoMap map[string]*lls_service.SiteBaseInfo
		for _, model := range models {
			siteIDList = append(siteIDList, model.SiteId)
			for _, actualPoint := range model.GetDefaultActualPointList() {
				if _, ok := actualPointListMap[actualPoint]; !ok {
					actualPointList = append(actualPointList, actualPoint)
					actualPointListMap[actualPoint] = true
				}
			}
			if len(model.FallbackActualPoint) != 0 {
				if _, ok := actualPointListMap[model.FallbackActualPoint]; !ok {
					actualPointList = append(actualPointList, model.FallbackActualPoint)
				}
			}
		}
		// 存在真实点的时候查询真实点的name信息
		if len(actualPointList) > 0 {
			actualPointListInfoMap, lcosErr = lls_service.GetActualPointInfos(ctx, actualPointList)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}

		// 查询虚拟点的name信息
		if len(siteIDList) > 0 {
			siteInfoMap, lcosErr = lls_service.GetAllSiteInfo(ctx, region)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}

		for i := 0; i < len(models); i++ {
			if siteItem, ok2 := siteInfoMap[models[i].SiteId]; ok2 {
				models[i].SiteName = siteItem.SiteName
			}

			if models[i].DefaultActualPoint != "" {
				// 封装actual point的json信息
				actualPointStructList := []*basic_serviceable.ActualPointInfo{}
				for _, actualPoint := range models[i].GetDefaultActualPointList() {
					if _, ok := actualPointListInfoMap[actualPoint]; ok {
						actualPointStructList = append(actualPointStructList, &basic_serviceable.ActualPointInfo{
							ActualPointID:   actualPoint,
							ActualPointName: actualPointListInfoMap[actualPoint].ActualPointName,
						})
					}
				}
				models[i].DefaultActualPointInfoList = actualPointStructList
			}

			// 填充兜底实际点信息
			if len(models[i].FallbackActualPoint) != 0 {
				if apInfo, ok := actualPointListInfoMap[models[i].FallbackActualPoint]; ok {
					models[i].FallbackActualPointInfo = &basic_serviceable.ActualPointInfo{
						ActualPointID:   apInfo.ActualPointID,
						ActualPointName: apInfo.ActualPointName,
					}
				}
			}
		}
	}

	return map[string]interface{}{
		"count":  count,
		"pageno": page,
		"total":  total,
		"list":   models,
	}, nil
}

func NewSiteServiceableAreaBasicConfService(siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface) *siteServiceableAreaBasicConfService {
	return &siteServiceableAreaBasicConfService{
		siteServiceableAreaBasicConfDao: siteServiceableAreaBasicConfDao,
	}
}

var _ SiteServiceableAreaBasicConfInterface = (*siteServiceableAreaBasicConfService)(nil)
