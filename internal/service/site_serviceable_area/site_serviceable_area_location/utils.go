package site_serviceable_area_location

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"strconv"
	"strings"
)

func CheckSSALocationRowData(rowData []string, region string, lineNum int) *lcos_error.LCOSError {
	if len(rowData) < 8 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", rowData length not enough: "+strconv.Itoa(len(rowData)))
	}

	rowCountry := strings.TrimSpace(rowData[2])
	if rowCountry != region {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", region mismatch: "+rowCountry)
	}

	if actionCode, err := strconv.Atoi(rowData[7]); err != nil || !(actionCode == 1 || actionCode == -1) {
		errMsg := fmt.Sprintf("please fill in Actioncode (1 or -1)|action_code=%d", actionCode)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}

	return nil
}
