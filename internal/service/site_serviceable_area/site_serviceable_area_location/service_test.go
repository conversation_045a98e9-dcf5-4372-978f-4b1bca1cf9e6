package site_serviceable_area_location

import (
	"context"
	"fmt"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	site_serviceable_area "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	site_serviceable_area2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
)

func TestMain(m *testing.M) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		fmt.Println(err.Error())
		return
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		fmt.Println(fmt.Sprintf("getConfig %v", err))
		return
	}

	if err := startup.InitLibs(c); err != nil {
		fmt.Println(fmt.Sprintf("InitLibs Error: %v", err))
		return
	}
	os.Exit(m.Run())
}

func Test_siteServiceableAreaLocationService_UploadSiteServiceableAreaLocation(t *testing.T) {
	type fields struct {
		siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
		siteServiceableAreaLocationDao  site_serviceable_area2.SiteServiceableAreaLocationDaoInterface
	}
	type args struct {
		ctx     utils.LCOSContext
		request *basic_serviceable.UploadSiteServiceableAreaLocationRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "overlap=0, override=0",
			fields: fields{
				siteServiceableAreaBasicConfDao: site_serviceable_area.NewSiteServiceableAreaBasicConfDAO(),
				siteServiceableAreaLocationDao:  site_serviceable_area2.NewSiteServiceableAreaLocationDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &basic_serviceable.UploadSiteServiceableAreaLocationRequest{
					FileUrl: "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &siteServiceableAreaLocationService{
				siteServiceableAreaBasicConfDao: tt.fields.siteServiceableAreaBasicConfDao,
				siteServiceableAreaLocationDao:  tt.fields.siteServiceableAreaLocationDao,
			}
			if got := s.UploadSiteServiceableAreaLocation(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("siteServiceableAreaLocationService.UploadSiteServiceableAreaLocation() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_siteServiceableAreaLocationService_CreateSiteServiceableAreaLocation(t *testing.T) {
	type fields struct {
		siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
		siteServiceableAreaLocationDao  site_serviceable_area2.SiteServiceableAreaLocationDaoInterface
	}
	type args struct {
		ctx     utils.LCOSContext
		request *basic_serviceable.CreateSiteServiceableAreaLocation
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "overlap=0, override=0",
			fields: fields{
				siteServiceableAreaBasicConfDao: site_serviceable_area.NewSiteServiceableAreaBasicConfDAO(),
				siteServiceableAreaLocationDao:  site_serviceable_area2.NewSiteServiceableAreaLocationDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &basic_serviceable.CreateSiteServiceableAreaLocation{
					SiteID:        "SVN4004",
					ActualPointID: "SVN4004-AP001",
					LocationLevel: 2,
					LocationIds:   []int{6000099},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &siteServiceableAreaLocationService{
				siteServiceableAreaBasicConfDao: tt.fields.siteServiceableAreaBasicConfDao,
				siteServiceableAreaLocationDao:  tt.fields.siteServiceableAreaLocationDao,
			}
			if got := s.CreateSiteServiceableAreaLocation(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("siteServiceableAreaLocationService.CreateSiteServiceableAreaLocation() = %v, want %v", got, tt.want)
			} else {
				t.Logf("siteServiceableAreaLocationService.CreateSiteServiceableAreaLocation() = %v", got)
			}
		})
	}
}
