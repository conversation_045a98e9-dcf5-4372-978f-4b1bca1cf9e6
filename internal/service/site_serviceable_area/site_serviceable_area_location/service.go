package site_serviceable_area_location

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seatalk"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strconv"

	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	site_serviceable_area "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	site_serviceable_area2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

/*
* @Author: lei.guo
* @Date: 2021/11/18 12:12 上午
* @Name：site_serviceable_area
* @Description:
 */

type SiteServiceableAreaLocationInterface interface {
	CreateSiteServiceableAreaLocation(ctx utils.LCOSContext, request *basic_serviceable.CreateSiteServiceableAreaLocation) *lcos_error.LCOSError
	DeleteSiteServiceableAreaLocation(ctx utils.LCOSContext, id uint64, siteID string) *lcos_error.LCOSError
	ListSiteServiceableAreaLocation(ctx utils.LCOSContext, request *basic_serviceable.ListSiteServiceableAreaLocation) (map[string]interface{}, *lcos_error.LCOSError)
	ListAllSiteServiceableAreaLocation(ctx utils.LCOSContext, request *basic_serviceable.ExportSiteServiceableAreaLocation) ([]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab, *lcos_error.LCOSError)
	UploadSiteServiceableAreaLocation(ctx utils.LCOSContext, request *basic_serviceable.UploadSiteServiceableAreaLocationRequest) *lcos_error.LCOSError

	SyncSiteSALocation(ctx utils.LCOSContext, region string) *lcos_error.LCOSError
}

type siteServiceableAreaLocationService struct {
	siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface
	siteServiceableAreaLocationDao  site_serviceable_area2.SiteServiceableAreaLocationDaoInterface
}

func generateUniqueKey(siteID string, locationID int) string {
	return fmt.Sprintf("site_id=%s, location_id=%d", siteID, locationID)
}

func (s *siteServiceableAreaLocationService) ListAllSiteServiceableAreaLocation(ctx utils.LCOSContext, request *basic_serviceable.ExportSiteServiceableAreaLocation) ([]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab, *lcos_error.LCOSError) {
	queryMap, err := utils.Struct2map(request)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	models, lcosErr := s.siteServiceableAreaLocationDao.ListAllSiteServiceableAreaLocations(ctx, *request.SiteID, queryMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return models, nil
}

// 检查是否overlap的函数key为 site_id+location_id->actual_point_id->LogisticSiteBasicServiceableLocationTab
func (s *siteServiceableAreaLocationService) checkOverlap(addedLocations, deletedLocations, locationsFromDB map[string]map[string]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab, siteBasicConf *site_serviceable_area.LogisticSiteBasicServiceableConfTab, deleteModelsMap map[string][]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab) *lcos_error.LCOSError {
	// 先剔除需要删除的数据
	for key, locationMap := range addedLocations {
		for actualPointID := range locationMap {
			if item, ok1 := deletedLocations[key]; ok1 {
				if _, ok2 := item[actualPointID]; ok2 {
					// addedLocations中的内容
					delete(addedLocations[key], actualPointID)
				}
			}
		}
	}

	for key, locationMap := range locationsFromDB {
		for actualPointID := range locationMap {
			if item, ok1 := deletedLocations[key]; ok1 {
				if _, ok2 := item[actualPointID]; ok2 {
					// addedLocations中的内容
					delete(locationsFromDB[key], actualPointID)
				}
			}
		}
	}

	// 比较重叠数据
	for key, locationMap := range addedLocations {
		// 1. check duplication action_code == 1 part in file
		// 上传的数据本身存在overlap
		if len(locationMap) > 1 {
			errMsg := fmt.Sprintf("uploaded site serviceable area locations overlap|site_id-location_id:%s", key)
			logger.LogError(errMsg)
			return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaLocationOverlapErrorCode, errMsg)
		}
		// 2. check duplication of action_code == 1 part in database
		for _, actualPointStruct := range locationMap {
			if dbLocationMap, ok1 := locationsFromDB[key]; ok1 {
				for _, dbActualPointStruct := range dbLocationMap {
					if actualPointStruct.ActualPointId != dbActualPointStruct.ActualPointId {
						if siteBasicConf.Override == constant.DISABLED {
							// override == 0, cause err as usual
							errMsg := fmt.Sprintf("uploaded site serviceable area locations overlap|%s, state=%s, city=%s, district=%s, street=%s, uploaded_location:[%s], db_location:[%s]", key, dbActualPointStruct.State, dbActualPointStruct.City, dbActualPointStruct.District, dbActualPointStruct.Street, actualPointStruct.ActualPointId, dbActualPointStruct.ActualPointId)
							return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaLocationOverlapErrorCode, errMsg)
						} else {
							// override == 1, more delete need
							deleteModelsMap[siteBasicConf.SiteId] = append(deleteModelsMap[siteBasicConf.SiteId], dbActualPointStruct)
						}
					}
				}
			}
		}
	}
	return nil
}

func isLocationLevelValid(state, city, district, street string, locationLevel uint8, lineNumber int) *lcos_error.LCOSError {
	switch locationLevel {
	case constant.STATE:
		if !(state != "" && city == "" && district == "" && street == "") {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("location level is not valid|lineNumber=%d", lineNumber))
		}
	case constant.CITY:
		if !(state != "" && city != "" && district == "" && street == "") {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("location level is not valid|lineNumber=%d", lineNumber))
		}
	case constant.DISTRICT:
		if !(state != "" && city != "" && district != "" && street == "") {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("location level is not valid|lineNumber=%d", lineNumber))
		}
	case constant.STREET:
		if !(state != "" && city != "" && district != "" && street != "") {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("location level is not valid|lineNumber=%d", lineNumber))
		}
	}
	return nil
}

func (s *siteServiceableAreaLocationService) parseSingleBasicServiceableLocation(ctx utils.LCOSContext, rowData []string, lineNumber int, addLocationModelsMap map[string][]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab, deleteLocationModelsMap map[string][]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab, siteInfoMap map[string]*site_serviceable_area.LogisticSiteBasicServiceableConfTab) *lcos_error.LCOSError {
	rowSiteId := rowData[0]

	// 检查site基础配置
	if _, ok := siteInfoMap[rowSiteId]; !ok {
		return lcos_error.NewLCOSError(lcos_error.NotFoundSiteServiceableAreaBasicConfErrorCode, fmt.Sprintf("cannot find site serviceable area basic conf|site_id=%v", rowSiteId))
	}

	siteBasicConf := siteInfoMap[rowSiteId]

	// 基础配置需要是location
	if siteBasicConf.SsaType != constant.LOCATION {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("ssa type of basic conf is not location|site_id=%v", rowSiteId))
	}

	actualPointID := rowData[1]

	var state, city, district, street string
	region := rowData[2]
	state = rowData[3]
	city = rowData[4]
	district = rowData[5]
	street = rowData[6]
	// 传入的地址是否有效
	if lcosErr := isLocationLevelValid(state, city, district, street, siteBasicConf.LocationLevel, lineNumber); lcosErr != nil {
		return lcosErr
	}
	actionCode := rowData[7]
	locationInfo, lcosErr := address_service.LocationServer.GetLocationInfoByName(ctx, region, state, city, district, street)
	if lcosErr != nil {
		return lcosErr
	} else {
		var locationID int
		// 根据基础层级信息获取location id
		switch siteBasicConf.LocationLevel {
		case constant.STATE:
			locationID = locationInfo.StateLocationId
		case constant.CITY:
			locationID = locationInfo.CityLocationId
		case constant.DISTRICT:
			locationID = locationInfo.DistrictLocationId
		case constant.STREET:
			locationID = locationInfo.StreetLocationId
		}
		if locationID == 0 {
			errMsg := fmt.Sprintf("location is not valid|region=%s, state=%s, city=%s, district=%s, street=%s", region, state, city, district, street)
			logger.LogInfo(errMsg)
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
		}
		siteServiceableAreaLocationModel := &site_serviceable_area2.LogisticSiteBasicServiceableLocationTab{
			SiteId:        rowSiteId,
			ActualPointId: actualPointID,
			Region:        region,
			LocationId:    locationID,
			State:         locationInfo.State,
			City:          locationInfo.City,
			District:      locationInfo.District,
			Street:        locationInfo.Street,
		}

		if actionCode == "1" {
			addLocationModelsMap[rowSiteId] = append(addLocationModelsMap[rowSiteId], siteServiceableAreaLocationModel)
		} else {
			deleteLocationModelsMap[rowSiteId] = append(deleteLocationModelsMap[rowSiteId], siteServiceableAreaLocationModel)
		}
	}
	return nil
}

func (s *siteServiceableAreaLocationService) UploadSiteServiceableAreaLocation(ctx utils.LCOSContext, request *basic_serviceable.UploadSiteServiceableAreaLocationRequest) *lcos_error.LCOSError {
	region := ctx.GetCountry()
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//self-test code
	//region = "VN"
	//file, _ := excelize.OpenFile("/Users/<USER>/Desktop/actual_point_location.xlsx")
	//rows, _ := file.Rows("Sheet1")

	addLocationModelsMap := make(map[string][]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab)
	deleteLocationModelsMap := make(map[string][]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab)
	siteIDMap := make(map[string]bool)                                                          // 用于去重
	var siteIDList []string                                                                     // 用于批量获取数据库中的site basic conf
	siteBasicConfMap := map[string]*site_serviceable_area.LogisticSiteBasicServiceableConfTab{} // siteID -> basic conf
	siteActualPointMap := map[string][]string{}                                                 // 生成site id actual point列表的map，用于请求lls
	siteActualPointExistMap := map[string]bool{}                                                // 用于去重
	var errList []*lcos_error.LCOSError
	var allRows [][]string

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}
		if err := CheckSSALocationRowData(row, region, lineNum); err != nil {
			return err
		}

		// 将site id去重存在在列表中，方便批量查询
		rowSiteID := row[0]
		if _, ok := siteIDMap[rowSiteID]; !ok {
			siteIDList = append(siteIDList, rowSiteID)
			siteIDMap[rowSiteID] = true
		}

		rowActualPoint := row[1]

		// 存入key
		key := fmt.Sprintf("site_id:%s, actual_point_id:%s", rowSiteID, rowActualPoint)
		if _, ok := siteActualPointExistMap[key]; !ok {
			siteActualPointMap[rowSiteID] = append(siteActualPointMap[rowSiteID], rowActualPoint)
			siteActualPointExistMap[key] = true
		}

		allRows = append(allRows, row)
	}
	if len(siteIDList) == 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "site list cannot be empty")
	}

	// 检查actualPoint是否都有效
	lcosErr := common.ValidActualPoints(ctx, siteActualPointMap)
	if lcosErr != nil {
		return lcosErr
	}

	// 获取site basic conf的信息，并且存为map
	siteBasicConfs, lcosErr := s.siteServiceableAreaBasicConfDao.ListAllSiteServiceableAreaBasicConfs(ctx, map[string]interface{}{"site_id in": siteIDList})
	if lcosErr != nil {
		return lcosErr
	}
	for _, singleSiteBasicConf := range siteBasicConfs {
		siteBasicConfMap[singleSiteBasicConf.SiteId] = singleSiteBasicConf
	}

	for index, rowData := range allRows {
		tmpErrList := s.parseSingleBasicServiceableLocation(ctx, rowData, index+1, addLocationModelsMap, deleteLocationModelsMap, siteBasicConfMap)
		if tmpErrList != nil {
			errList = append(errList, tmpErrList)
		}
	}

	if len(errList) != 0 {
		errMsg := lcos_error.GetMessageFromList(errList)
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}

	// file overlap check, feat: SPLN-21958 site sa override
	conditionMap := make(map[string]bool)
	for _, locationList := range addLocationModelsMap {
		for _, loc := range locationList {
			uniqKey := fmt.Sprintf("%s:%s:%d", loc.SiteId, loc.ActualPointId, loc.LocationId)
			if exist, ok := conditionMap[uniqKey]; ok && exist {
				errMsg := fmt.Sprintf("Site Location overlap in file|site_id=%s, actual_point_id=%s, location_id=%d", loc.SiteId, loc.ActualPointId, loc.LocationId)
				logger.CtxLogErrorf(ctx, errMsg)
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
			} else {
				conditionMap[uniqKey] = true
			}
		}
	}
	for _, locationList := range deleteLocationModelsMap {
		for _, loc := range locationList {
			uniqKey := fmt.Sprintf("%s:%s:%d", loc.SiteId, loc.ActualPointId, loc.LocationId)
			if exist, ok := conditionMap[uniqKey]; ok && exist {
				errMsg := fmt.Sprintf("Site Location overlap in file|site_id=%s, actual_point_id=%s, location_id=%d", loc.SiteId, loc.ActualPointId, loc.LocationId)
				logger.CtxLogErrorf(ctx, errMsg)
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
			} else {
				conditionMap[uniqKey] = true
			}
		}
	}

	// 校验overlap
	// 将addLocationModels和deleteLocationModels存为 site_id+location_id->actual_point_id->location的
	addedLocationsForOverlap := map[string]map[string]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab{}
	deletedLocationsForOverlap := map[string]map[string]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab{}
	for _, locations := range addLocationModelsMap {
		for _, location := range locations {
			key := generateUniqueKey(location.SiteId, location.LocationId)
			if _, ok := addedLocationsForOverlap[key]; !ok {
				addedLocationsForOverlap[key] = map[string]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab{}
			}
			addedLocationsForOverlap[key][location.ActualPointId] = location
		}
	}
	for _, locations := range deleteLocationModelsMap {
		for _, location := range locations {
			key := generateUniqueKey(location.SiteId, location.LocationId)
			if _, ok := deleteLocationModelsMap[key]; !ok {
				deletedLocationsForOverlap[key] = map[string]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab{}
			}
			deletedLocationsForOverlap[key][location.ActualPointId] = location
		}
	}

	// duplication check rule:
	// allowOverLap == 1, no check;
	// allowOverlap == 0, but override == 1, delete those in db, then insert those in file;
	// allowOverlap == 0, override == 0, return err while duplicated
	for siteID, siteBasicConf := range siteBasicConfMap {
		if siteBasicConf.AllowOverlap == constant.FALSE {
			// 查询数据库，获取所有site_id的服务范围数据
			dbLocations, lcosErr := s.siteServiceableAreaLocationDao.ListAllSiteServiceableAreaLocations(ctx, siteID, map[string]interface{}{"site_id": siteID})
			if lcosErr != nil {
				return lcosErr
			}
			// 将locations存为校验overlap需要的格式
			locationFromDB := map[string]map[string]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab{}
			for _, dbLocation := range dbLocations {
				key := generateUniqueKey(dbLocation.SiteId, dbLocation.LocationId)
				if _, ok := locationFromDB[key]; !ok {
					locationFromDB[key] = map[string]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab{}
				}
				locationFromDB[key][dbLocation.ActualPointId] = dbLocation
			}
			if lcosErr := s.checkOverlap(addedLocationsForOverlap, deletedLocationsForOverlap, locationFromDB, siteBasicConf, deleteLocationModelsMap); lcosErr != nil {
				return lcosErr
			}
		}
	}

	siteId := config.GetSiteIdServiceableLocationSiteIdConfig(ctx, region)
	_, ok1 := addLocationModelsMap[siteId]
	_, ok2 := deleteLocationModelsMap[siteId]

	if !ok1 && !ok2 {
		fc := func() *lcos_error.LCOSError {
			for siteID, addLocationModels := range addLocationModelsMap {
				//if err := s.lineBasicServiceableLocationDAO.BatchDeleteBasicServiceableLocation(ctx, lineId, addLocationModels); err != nil {
				//	return err
				//}
				if err := s.siteServiceableAreaLocationDao.BatchCreateSiteServiceableAreaLocationOnDuplicate(ctx, addLocationModels, siteID); err != nil {
					return err
				}
			}
			for siteID, deleteLocationModels := range deleteLocationModelsMap {
				if err := s.siteServiceableAreaLocationDao.BatchDeleteSiteServiceableAreaLocation(ctx, deleteLocationModels, siteID); err != nil {
					return err
				}
			}

			return nil
		}
		if err := ctx.Transaction(fc); err != nil {
			return err
		}
	} else {
		taskModel := &site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab{
			SiteId: siteId,
			Region: region,
		}
		var siteSALocationsNow []*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab

		fc := func() *lcos_error.LCOSError {
			//添加新版本记录 version
			err = s.siteServiceableAreaLocationDao.BatchCreateSiteServiceableAreaLocationHistory(ctx, []*site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab{taskModel})
			if err != nil {
				logger.CtxLogErrorf(ctx, "set data in history db error")
				return err
			}
			version := taskModel.Id

			//先删除业务上传的数据 再读所有的数据 最后写到新的版本中
			for siteID, addLocationModels := range addLocationModelsMap {
				if err := s.siteServiceableAreaLocationDao.BatchCreateSiteServiceableAreaLocationOnDuplicate(ctx, addLocationModels, siteID); err != nil {
					return err
				}
			}
			for siteID, deleteLocationModels := range deleteLocationModelsMap {
				if err := s.siteServiceableAreaLocationDao.BatchDeleteSiteServiceableAreaLocation(ctx, deleteLocationModels, siteID); err != nil {
					return err
				}
			}

			siteSALocationsNow, err = s.saveHistoryAndCacheOrderVersion(ctx, siteId, region, version)
			if err != nil {
				logger.CtxLogErrorf(ctx, "del site sa location data in db error,err = %s", err.Msg)
				return err
			}
			return nil
		}
		if err := ctx.Transaction(fc); err != nil {
			return err
		}
		startTime := utils.GetTimestamp(ctx)
		addNum, delNum, err := s.SyncNewAndDelOldSiteSALocationToMplRedis(ctx, siteId, region, taskModel, siteSALocationsNow)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.WriteMplRedisReport, siteId, constant.StatusError, err.Msg)
			syncSiteSALocationDataErrorNotify(ctx, startTime, err)
			logger.CtxLogErrorf(ctx, "update task status data in db error,err = %s", err.Msg)
			return err
		}
		updateData := map[string]interface{}{
			"history_status": constant.SyncSiteSASuccess,
		}
		err = s.siteServiceableAreaLocationDao.UpdateSiteServiceableAreaLocationHistory(ctx, updateData, taskModel.Id)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.WriteMplRedisReport, siteId, constant.StatusError, err.Msg)
			syncSiteSALocationDataErrorNotify(ctx, startTime, err)
			logger.CtxLogErrorf(ctx, "update task status data in db error")
			return err
		}
		_ = monitor.AwesomeReportEvent(ctx, constant.WriteMplRedisReport, siteId, constant.StatusSuccess, "")
		syncSingleSiteSALocationDataSuccessNotify(ctx, startTime, region, taskModel.Id, siteId, constant.UploadAction, addNum, delNum)
	}

	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(siteIDList, ","))
	return nil
}

func (s *siteServiceableAreaLocationService) CreateSiteServiceableAreaLocation(ctx utils.LCOSContext, request *basic_serviceable.CreateSiteServiceableAreaLocation) *lcos_error.LCOSError {
	if lcosErr := common.CheckSiteAndActualPointValid(ctx, request.SiteID, request.ActualPointID); lcosErr != nil {
		return lcosErr
	}

	if len(request.LocationIds) == 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "location list cannot be empty")
	}
	// 先获取基础配置，获取不到则报错
	siteConfs, lcosErr := s.siteServiceableAreaBasicConfDao.ListAllSiteServiceableAreaBasicConfs(ctx, map[string]interface{}{"site_id": request.SiteID})
	if lcosErr != nil {
		errMsg := fmt.Sprintf("db read error|error=%s, site_id=%s", lcosErr.Msg, request.SiteID)
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, errMsg)
	}
	if len(siteConfs) == 0 {
		errMsg := fmt.Sprintf("cannot find site serviceable area basic conf|site_id=%s", request.SiteID)
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSError(lcos_error.NotFoundSiteServiceableAreaBasicConfErrorCode, errMsg)
	}
	siteConf := siteConfs[0]

	// 当前的conf必须为location
	if siteConf.SsaType != constant.LOCATION {
		return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaLocationLevelNotValidErrorCode, "Site Serviceable Area Type not match, cannot create Location type")
	}

	// 校验location level
	if siteConf.LocationLevel != request.LocationLevel {
		errMsg := fmt.Sprintf("location level is not valid|siteBasicConf.LocationLevel=%d, request.LocationLevel=%d", siteConf.LocationLevel, request.LocationLevel)
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaLocationLevelNotValidErrorCode, errMsg)
	}

	// 检查是否重叠
	if siteConf.AllowOverlap == constant.FALSE {
		siteLocations, lcosErr := s.siteServiceableAreaLocationDao.ListAllSiteServiceableAreaLocations(ctx, request.SiteID, map[string]interface{}{"location_id in": request.LocationIds, "site_id": request.SiteID})
		if lcosErr != nil {
			return lcosErr
		}
		if siteConf.Override == constant.DISABLED {
			// override == 0, keep as usual
			if len(siteLocations) > 0 {
				errMsg := fmt.Sprintf("location overlap|request:%v, db:%v", request, siteLocations[0])
				return lcos_error.NewLCOSError(lcos_error.SiteServiceableAreaLocationOverlapErrorCode, errMsg)
			}
		} else {
			// override == 1, need to delete those in db, and insert new one.
			err := s.siteServiceableAreaLocationDao.BatchDeleteSiteServiceableAreaLocation(ctx, siteLocations, request.SiteID)
			if err != nil {
				return err
			}
		}

	}

	var siteLocationModels []*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab

	// 请求location server获取对应值
	for _, locationID := range request.LocationIds {
		// 获取
		locationInfo, lcosErr := address_service.LocationServer.GetLocationInfoById(ctx, locationID)
		if lcosErr != nil {
			return lcosErr
		}
		siteLocationModels = append(siteLocationModels, &site_serviceable_area2.LogisticSiteBasicServiceableLocationTab{
			SiteId:        request.SiteID,
			ActualPointId: request.ActualPointID,
			Region:        siteConf.Region,
			LocationId:    locationID,
			State:         locationInfo.State,
			City:          locationInfo.City,
			District:      locationInfo.District,
			Street:        locationInfo.Street,
		})

	}
	//spln-32923 upload to mpl codis
	region := strings.ToUpper(ctx.GetCountry())
	siteId := config.GetSiteIdServiceableLocationSiteIdConfig(ctx, region)
	var err *lcos_error.LCOSError
	if siteId != "" && siteId == request.SiteID {
		//1. 事务：写db history表 更新本次记录
		//   事务：更新业务数据
		//   事务：清空并重写业务version数据
		//2. 写codis
		//3. 更新db中写codis的状态

		taskModel := &site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab{
			SiteId: siteId,
			Region: region,
		}
		var siteSALocationsNow []*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab

		fc := func() *lcos_error.LCOSError {
			//添加新版本记录 version
			err = s.siteServiceableAreaLocationDao.BatchCreateSiteServiceableAreaLocationHistory(ctx, []*site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab{taskModel})
			if err != nil {
				logger.CtxLogErrorf(ctx, "create data in history db error,err = %s", err.Msg)
				return err
			}
			version := taskModel.Id

			//执行业务数据的更新
			err = s.siteServiceableAreaLocationDao.BatchCreateSiteServiceableAreaLocation(ctx, siteLocationModels, request.SiteID)
			if err != nil {
				logger.CtxLogErrorf(ctx, "add site sa location data in db error,err = %s", err.Msg)
				return err
			}

			//先删除业务上传的数据 再读所有的数据 最后写到新的版本中 返回带有lls的site location
			siteSALocationsNow, err = s.saveHistoryAndCacheOrderVersion(ctx, siteId, region, version)
			if err != nil {
				logger.CtxLogErrorf(ctx, "save site sa location history in db error,err = %s", err.Msg)
				return err
			}
			return nil
		}
		if err := ctx.Transaction(fc); err != nil {
			return err
		}

		startTime := utils.GetTimestamp(ctx)
		addNum, delNum, err := s.SyncNewAndDelOldSiteSALocationToMplRedis(ctx, siteId, region, taskModel, siteSALocationsNow)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.WriteMplRedisReport, siteId, constant.StatusError, err.Msg)
			syncSiteSALocationDataErrorNotify(ctx, startTime, err)
			logger.CtxLogErrorf(ctx, "update task status data in db error,err = %s", err.Msg)
			return err
		}

		updateData := map[string]interface{}{
			"history_status": constant.SyncSiteSASuccess,
		}
		err = s.siteServiceableAreaLocationDao.UpdateSiteServiceableAreaLocationHistory(ctx, updateData, taskModel.Id)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.WriteMplRedisReport, siteId, constant.StatusError, err.Msg)
			syncSiteSALocationDataErrorNotify(ctx, startTime, err)
			logger.CtxLogErrorf(ctx, "update task status data in db error,err = %s", err.Msg)
			return err
		}
		_ = monitor.AwesomeReportEvent(ctx, constant.WriteMplRedisReport, siteId, constant.StatusSuccess, "")
		syncSingleSiteSALocationDataSuccessNotify(ctx, startTime, region, taskModel.Id, siteId, constant.CreateAction, addNum, delNum)
	} else {
		err := s.siteServiceableAreaLocationDao.BatchCreateSiteServiceableAreaLocation(ctx, siteLocationModels, request.SiteID)
		if err != nil {
			return err
		}
	}
	return err
}

func (s *siteServiceableAreaLocationService) saveHistoryAndCacheOrderVersion(ctx utils.LCOSContext, siteID, region string, version uint64) ([]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab, *lcos_error.LCOSError) {
	//1. 获取siteid对应的最新的site location
	//2. 调用lls拼接3pl code
	//3. 清理version表，并保存拼接好的site location到对应市场的version db中
	siteSALocationsNow, err := s.siteServiceableAreaLocationDao.ListAllSiteServiceableAreaLocations(ctx, siteID, map[string]interface{}{"site_id": siteID})
	if err != nil {
		logger.CtxLogErrorf(ctx, "search site location db error,err = %s", err.Msg)
		return nil, err
	}
	err = getSocStationCodeFromLLS(ctx, siteSALocationsNow)
	if err != nil {
		logger.CtxLogErrorf(ctx, "request lls error,err = %s", err.Msg)
		return nil, err
	}
	siteLocationsToVersion := siteLocationsToVersionModel(siteSALocationsNow, version)

	err = s.siteServiceableAreaLocationDao.DelAllSiteServiceableAreaLocationCacheOrder(ctx, version, region, map[string]interface{}{"id >": 0})
	if err != nil {
		logger.CtxLogErrorf(ctx, "del site sa location data in db error,err = %s", err.Msg)
		return nil, err
	}

	err = s.siteServiceableAreaLocationDao.BatchCreateSiteServiceableAreaLocationCacheOrder(ctx, siteLocationsToVersion, version, region)
	if err != nil {
		logger.CtxLogErrorf(ctx, "create site sa location data in version db error,err = %s", err.Msg)
		return nil, err
	}
	return siteSALocationsNow, nil
}

func getSocStationCodeFromLLS(ctx utils.LCOSContext, siteSALocations []*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab) *lcos_error.LCOSError {
	var actualPointIDList []string
	for _, siteSALocation := range siteSALocations {
		//Get Actual Point 3pl name from lls
		actualPointIDList = append(actualPointIDList, siteSALocation.ActualPointId)
	}
	actualPointInfoMap, err := lls_service.GetActualPointsMapWithGreySwitch(ctx, actualPointIDList)
	if err != nil {
		return err
	}
	for _, siteSALocation := range siteSALocations {
		if actualPointInfo, ok := actualPointInfoMap[siteSALocation.ActualPointId]; ok {
			siteSALocation.SocStationCode = actualPointInfo.GetSocStationCode()
		}
	}
	return nil
}

func siteLocationsToVersionModel(siteLocationsToDel []*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab, version uint64) []*site_serviceable_area2.LogisticSiteBasicServiceableLocationCacheOrderTab {
	var siteLocationsToDelTwoModel []*site_serviceable_area2.LogisticSiteBasicServiceableLocationCacheOrderTab
	for _, siteLocation := range siteLocationsToDel {
		siteLocationToDelTwoModel := &site_serviceable_area2.LogisticSiteBasicServiceableLocationCacheOrderTab{
			SiteId:          siteLocation.SiteId,
			SiteName:        siteLocation.SiteName,
			ActualPointId:   siteLocation.ActualPointId,
			ActualPointName: siteLocation.ActualPointName,
			Region:          siteLocation.Region,
			LocationId:      siteLocation.LocationId,
			State:           siteLocation.State,
			City:            siteLocation.City,
			District:        siteLocation.District,
			Street:          siteLocation.Street,
			SocStationCode:  siteLocation.SocStationCode,
			Version:         version,
		}
		siteLocationsToDelTwoModel = append(siteLocationsToDelTwoModel, siteLocationToDelTwoModel)
	}
	return siteLocationsToDelTwoModel
}

func (s *siteServiceableAreaLocationService) DeleteSiteServiceableAreaLocation(ctx utils.LCOSContext, id uint64, siteID string) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	siteId := config.GetSiteIdServiceableLocationSiteIdConfig(ctx, region)
	var err *lcos_error.LCOSError

	if siteId != "" && siteId == siteID {
		//1.新建history
		//2.执行删除site id操作
		//3.删除当前表内容 将新数据写入当前表
		//查当前的location + lls name  保存到version表

		taskModel := &site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab{
			SiteId: siteID,
			Region: region,
		}
		var siteSALocationsNow []*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab

		fc := func() *lcos_error.LCOSError {
			//添加新版本记录 version
			err = s.siteServiceableAreaLocationDao.BatchCreateSiteServiceableAreaLocationHistory(ctx, []*site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab{taskModel})
			if err != nil {
				logger.CtxLogErrorf(ctx, "create data in history db error,err = %s", err.Msg)
				return err
			}
			version := taskModel.Id

			//先删除业务上传的数据 再读所有的数据 最后写到新的版本中
			err = s.siteServiceableAreaLocationDao.DeleteSiteServiceableAreaLocation(ctx, id, siteID)
			if err != nil {
				logger.CtxLogErrorf(ctx, "del site sa location data in db error,err = %s", err.Msg)
				return err
			}

			siteSALocationsNow, err = s.saveHistoryAndCacheOrderVersion(ctx, siteID, region, version)
			if err != nil {
				logger.CtxLogErrorf(ctx, "del site sa location data in db error,err = %s", err.Msg)
				return err
			}
			return nil
		}
		if err := ctx.Transaction(fc); err != nil {
			return err
		}

		startTime := utils.GetTimestamp(ctx)
		addNum, delNum, err := s.SyncNewAndDelOldSiteSALocationToMplRedis(ctx, siteID, region, taskModel, siteSALocationsNow)
		if err != nil {
			_ = monitor.AwesomeReportEvent(ctx, constant.WriteMplRedisReport, siteId, constant.StatusError, err.Msg)
			syncSiteSALocationDataErrorNotify(ctx, startTime, err)
			logger.CtxLogErrorf(ctx, "sync site sa data to mpl  error,err = %s", err.Msg)
			return err
		}

		updateData := map[string]interface{}{
			"history_status": constant.SyncSiteSASuccess,
		}
		err = s.siteServiceableAreaLocationDao.UpdateSiteServiceableAreaLocationHistory(ctx, updateData, taskModel.Id)
		if err != nil {
			syncSiteSALocationDataErrorNotify(ctx, startTime, err)
			_ = monitor.AwesomeReportEvent(ctx, constant.WriteMplRedisReport, siteId, constant.StatusError, err.Msg)
			logger.CtxLogErrorf(ctx, "update task status data in db error,err = %s", err.Msg)
			return err
		}
		_ = monitor.AwesomeReportEvent(ctx, constant.WriteMplRedisReport, siteId, constant.StatusSuccess, "")
		syncSingleSiteSALocationDataSuccessNotify(ctx, startTime, region, taskModel.Id, siteId, constant.DeleteAction, addNum, delNum)
	} else {
		//not mpl cache order site sa, old flow
		err = s.siteServiceableAreaLocationDao.DeleteSiteServiceableAreaLocation(ctx, id, siteID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "del data in db error,err = %s", err.Msg)
			return err
		}
	}

	return nil
}

func (s *siteServiceableAreaLocationService) SyncNewAndDelOldSiteSALocationToMplRedis(ctx utils.LCOSContext, siteID, region string, taskModel *site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab, siteSALocationsNow []*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab) (int, int, *lcos_error.LCOSError) {
	//写新数据入到codis中
	success, err := s.siteServiceableAreaLocationDao.SyncSiteSALocationToMplRedis(ctx, taskModel, siteSALocationsNow)
	if err != nil {
		logger.CtxLogErrorf(ctx, "sync new data to mpl codis error,err = %s", err.Msg)
		return 0, 0, err
	}
	logger.CtxLogInfof(ctx, "num of sync new site data to mpl is[%d]", success)

	//删除旧数据
	del, err := s.SyncDelOldSiteSALocationToMplRedis(ctx, siteID, region, taskModel)
	if err != nil {
		logger.CtxLogErrorf(ctx, "del site sa location data in db error,err = %s", err.Msg)
		return 0, 0, err
	}

	return success, del, nil
}

func (s *siteServiceableAreaLocationService) SyncDelOldSiteSALocationToMplRedis(ctx utils.LCOSContext, siteID, region string, taskModel *site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab) (int, *lcos_error.LCOSError) {
	//删除旧数据
	historys, lcosErr := s.siteServiceableAreaLocationDao.ListAllSiteServiceableAreaHistoryByParam(ctx, map[string]interface{}{"site_id": siteID, "id <": taskModel.Id})
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "search db for history error,err = %s", lcosErr.Msg)
		return 0, lcosErr
	}

	var delTaskModel *site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab
	if len(historys) > 0 {
		delTaskModel = historys[0]
	} else {
		//无历史记录 说明是第一次更新 需要删除保存的0版本数据
		delTaskModel = &site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab{
			Id:     0,
			Region: region,
		}
	}
	siteLocationsToDel, err := s.siteServiceableAreaLocationDao.ListAllSiteServiceableCacheOrderAreaLocations(ctx, delTaskModel.Id, region, map[string]interface{}{"site_id": siteID})
	if err != nil {
		logger.CtxLogErrorf(ctx, "list site sa location data in db error,err = %s", err.Msg)
		return 0, err
	}
	del, err := s.siteServiceableAreaLocationDao.SyncDelSiteSALocationToMplRedis(ctx, delTaskModel, siteLocationsToDel)
	if err != nil {
		logger.CtxLogErrorf(ctx, "del site sa in codis error,err = %s", err.Msg)
		return 0, err
	}
	logger.CtxLogInfof(ctx, "num of del site as is[%d]", del)
	return del, nil
}

func (s *siteServiceableAreaLocationService) updateHistoryDB(ctx utils.LCOSContext, taskModel *site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab, taskStatus int) *lcos_error.LCOSError {
	updateData := map[string]interface{}{
		"history_status": taskStatus,
	}
	err := s.siteServiceableAreaLocationDao.UpdateSiteServiceableAreaLocationHistory(ctx, updateData, taskModel.Id)
	if err != nil {
		logger.CtxLogErrorf(ctx, "update task status data in db error")
		return err
	}
	return nil
}

func (s *siteServiceableAreaLocationService) getNewestSiteSALocation(ctx utils.LCOSContext, siteID string, taskModel *site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab) ([]*site_serviceable_area2.LogisticSiteBasicServiceableLocationTab, *lcos_error.LCOSError) {
	siteSALocations, lcosErr := s.siteServiceableAreaLocationDao.ListAllSiteServiceableAreaLocations(ctx, siteID, map[string]interface{}{"site_id": siteID})
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "search db error,err = %s", lcosErr.Msg)
		return nil, lcosErr
	}

	err := getSocStationCodeFromLLS(ctx, siteSALocations)
	if err != nil {
		logger.CtxLogErrorf(ctx, "request lls error,err = %s", err.Msg)
		return nil, err
	}
	return siteSALocations, nil
}

func (s *siteServiceableAreaLocationService) saveNewestSiteSALocation(ctx utils.LCOSContext, taskModel *site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab, siteLocationsToVersion []*site_serviceable_area2.LogisticSiteBasicServiceableLocationCacheOrderTab) *lcos_error.LCOSError {
	err := s.siteServiceableAreaLocationDao.DelAllSiteServiceableAreaLocationCacheOrder(ctx, taskModel.Id, taskModel.Region, map[string]interface{}{"id >": 0})
	if err != nil {
		logger.CtxLogErrorf(ctx, "del site sa location data in db error,err = %s", err.Msg)
		return err
	}
	err = s.siteServiceableAreaLocationDao.BatchCreateSiteServiceableAreaLocationCacheOrder(ctx, siteLocationsToVersion, taskModel.Id, taskModel.Region)
	if err != nil {
		logger.CtxLogErrorf(ctx, "create site sa location data in version db error,err = %s", err.Msg)
		return err
	}
	return nil
}

func (s *siteServiceableAreaLocationService) ListSiteServiceableAreaLocation(ctx utils.LCOSContext, request *basic_serviceable.ListSiteServiceableAreaLocation) (map[string]interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())

	var page uint32 = 1
	var count uint32 = 10
	if request.PageNo != nil {
		page = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}
	queryMap, err := utils.Struct2map(request)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	models, total, lcosErr := s.siteServiceableAreaLocationDao.ListSiteServiceableAreaLocationsPaging(ctx, request.SiteID, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 查询lls，获取site name和actual point name，并且填充model
	if len(models) > 0 {
		var actualPointList []string
		var siteIDList []string
		var actualPointListInfoMap map[string]*lls_service.ActualSiteInfo
		var siteInfoMap map[string]*lls_service.SiteBaseInfo
		for _, model := range models {
			siteIDList = append(siteIDList, model.SiteId)
			if model.ActualPointId != "" {
				actualPointList = append(actualPointList, model.ActualPointId)
			}
		}
		// 存在真实点的时候查询真实点的name信息
		if len(actualPointList) > 0 {
			actualPointListInfoMap, lcosErr = lls_service.GetActualPointInfos(ctx, actualPointList)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}

		// 查询虚拟点的name信息
		if len(siteIDList) > 0 {
			siteInfoMap, lcosErr = lls_service.GetAllSiteInfo(ctx, region)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}

		for i := 0; i < len(models); i++ {
			if infoItem, ok1 := actualPointListInfoMap[models[i].ActualPointId]; ok1 {
				models[i].ActualPointName = infoItem.ActualPointName
				models[i].SiteName = infoItem.SiteName
			}
			if siteItem, ok2 := siteInfoMap[models[i].SiteId]; ok2 {
				models[i].SiteName = siteItem.SiteName
			}
		}
	}

	return map[string]interface{}{
		"count":  count,
		"pageno": page,
		"total":  total,
		"list":   models,
	}, nil
}

func (s *siteServiceableAreaLocationService) SyncSiteSALocation(ctx utils.LCOSContext, region string) *lcos_error.LCOSError {
	startTime := utils.GetTimestamp(ctx)
	taskName := fmt.Sprintf("sync_site_sa_location_to_mpl:%s:%s", utils.FormatTimestamp(startTime, constant.DateAndTimeFormat), region)

	siteIdMap := make(map[string]string)
	if region != "" {
		siteId := config.GetSiteIdServiceableLocationSiteIdConfig(ctx, region)
		if siteId != "" {
			siteIdMap[region] = siteId
		}
	} else {
		siteIdMap = config.GetALLRegionSiteIdServiceableLocationSiteIdConfig(ctx)
	}
	if len(siteIdMap) == 0 {
		logger.CtxLogInfof(ctx, "no site id need sync to mpl")
		_ = monitor.AwesomeReportEvent(ctx, taskName, "empty", constant.StatusError, "no site id need sync to mpl")
		return nil
	}

	var errList []*lcos_error.LCOSError
	var total, totalDelNum int
	var failedSiteIdList []string
	for itemRegion, siteId := range siteIdMap {
		logger.CtxLogInfof(ctx, "ready to sync site sa location data to mpl codis|site_id=%s", siteId)

		startTime = utils.GetTimestamp(ctx)
		historys, lcosErr := s.siteServiceableAreaLocationDao.ListAllSiteServiceableAreaHistoryByParam(ctx, map[string]interface{}{"site_id": siteId})
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "search db error")
			failedSiteIdList = append(failedSiteIdList, siteId)
			errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncMPLRedisError, "sync site sa location data failed, get db error|site_id=%s, cause=%s", siteId, lcosErr.Msg))
			_ = monitor.AwesomeReportEvent(ctx, taskName, siteId, constant.StatusError, lcosErr.Msg)
			continue
		}
		var historyModel *site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab
		var success, delNum int
		if len(historys) == 0 {
			//无修改历史 同步最新数据
			//并保存数据到0 version中
			historyModel = &site_serviceable_area2.LogisticSiteBasicServiceableLocationHistoryTab{Id: 0, Region: itemRegion}
			newestSiteSALocations, lcosErr := s.getNewestSiteSALocation(ctx, siteId, historyModel)
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "get Newest Site SA Location error,err = %s", lcosErr.Msg)
				failedSiteIdList = append(failedSiteIdList, siteId)
				errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncMPLRedisError, "get site sa location data failed, get error|site_id=%s, cause=%s", siteId, lcosErr.Msg))
				_ = monitor.AwesomeReportEvent(ctx, taskName, siteId, constant.StatusError, lcosErr.Msg)
				continue
			}
			if len(newestSiteSALocations) == 0 {
				continue
			}

			siteLocationsToVersion := siteLocationsToVersionModel(newestSiteSALocations, historyModel.Id)
			lcosErr = s.saveNewestSiteSALocation(ctx, historyModel, siteLocationsToVersion)
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "save Newest Site SA Location error,err = %s", lcosErr.Msg)
				failedSiteIdList = append(failedSiteIdList, siteId)
				errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncMPLRedisError, "save new sa location data failed, get error|site_id=%s, cause=%s", siteId, lcosErr.Msg))
				_ = monitor.AwesomeReportEvent(ctx, taskName, siteId, constant.StatusError, lcosErr.Msg)
				continue
			}

			//写新数据 //写codis 版本
			success, lcosErr = s.siteServiceableAreaLocationDao.SyncSiteSALocationToMplRedis(ctx, historyModel, newestSiteSALocations)
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "sync Newest Site SA Location error,err = %s", lcosErr.Msg)
				failedSiteIdList = append(failedSiteIdList, siteId)
				errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncMPLRedisError, "sync site sa location data failed, get error|site_id=%s, cause=%s", siteId, lcosErr.Msg))
				_ = monitor.AwesomeReportEvent(ctx, taskName, siteId, constant.StatusError, lcosErr.Msg)
				continue
			}

		} else {
			historyModel = historys[0]
			siteLocations, err := s.getNewestSiteSALocation(ctx, siteId, historyModel)
			if err != nil {
				logger.CtxLogErrorf(ctx, "get Newest Site SA Location error,err = %s,version = %d", err.Msg, historyModel.Id)
				failedSiteIdList = append(failedSiteIdList, siteId)
				errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncMPLRedisError, "get site sa location data failed, get error|site_id=%s, cause=%s", siteId, err.Msg))
				_ = monitor.AwesomeReportEvent(ctx, taskName, siteId, constant.StatusError, err.Msg)
				continue
			}
			if len(siteLocations) == 0 {
				continue
			}

			success, err = s.siteServiceableAreaLocationDao.SyncSiteSALocationToMplRedis(ctx, historyModel, siteLocations)
			if err != nil {
				logger.CtxLogErrorf(ctx, "sync new data to mpl codis error,err = %s", err.Msg)
				failedSiteIdList = append(failedSiteIdList, siteId)
				errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncMPLRedisError, "sync new data to mpl codis error, get error|site_id=%s, cause=%s", siteId, err.Msg))
				_ = monitor.AwesomeReportEvent(ctx, taskName, siteId, constant.StatusError, err.Msg)
				continue
			}

			if historyModel.HistoryStatus != constant.SyncSiteSASuccess {
				//上一次执行失败的任务 重试删除
				delNum, err = s.SyncDelOldSiteSALocationToMplRedis(ctx, siteId, itemRegion, historyModel)
				if err != nil {
					logger.CtxLogErrorf(ctx, "del old data in codis error,err = %s", err.Msg)
					failedSiteIdList = append(failedSiteIdList, siteId)
					errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncMPLRedisDelError, "sync del site sa location data failed, get error|site_id=%s, cause=%s", siteId, err.Msg))
					_ = monitor.AwesomeReportEvent(ctx, taskName, siteId, constant.StatusError, err.Msg)
					continue
				}

				taskStatus := constant.SyncSiteSASuccess
				err = s.updateHistoryDB(ctx, historyModel, taskStatus)
				if err != nil {
					logger.CtxLogErrorf(ctx, "update task status data in db error,err = %s", err.Msg)
					failedSiteIdList = append(failedSiteIdList, siteId)
					errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncMPLRedisDelError, "sync del site sa location data failed, get error|site_id=%s, cause=%s", siteId, err.Msg))
					_ = monitor.AwesomeReportEvent(ctx, taskName, siteId, constant.StatusError, err.Msg)
					continue
				}
				totalDelNum += delNum
			}
		}
		_ = monitor.AwesomeReportEvent(ctx, taskName, siteId, constant.StatusSuccess, strconv.Itoa(success))
		total += success
	}
	_ = monitor.AwesomeReportEvent(ctx, taskName, "done", constant.StatusSuccess, strconv.Itoa(total))
	syncSiteSALocationDataSuccessNotify(ctx, startTime, total, totalDelNum, siteIdMap, failedSiteIdList)

	if len(errList) != 0 {
		errMsg := lcos_error.GetMessageFromListWithSep(errList, "\n")
		return lcos_error.NewLCOSError(lcos_error.SyncMPLRedisError, errMsg)
	}
	return nil
}

func syncSingleSiteSALocationDataSuccessNotify(ctx utils.LCOSContext, startTime uint32, region string, version uint64, siteId, action string, addNum, delNum int) {
	message := fmt.Sprintf(
		serviceable_constant.SyncSiteSALocationSuccessMessage,
		utils.GetEnv(ctx),
		utils.FormatTimestamp(startTime, constant.DateAndTimeFormat),
		utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
		region,
		version,
		siteId,
		action,
		addNum,
		delNum,
	)
	cfg := config.GetMutableConf(ctx).ItemCardSceneConfig.SeatalkNotifyConfig
	_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, cfg.AtAll)
}

func syncSiteSALocationDataErrorNotify(ctx utils.LCOSContext, startTime uint32, err *lcos_error.LCOSError) {
	if err == nil {
		return
	}
	message := fmt.Sprintf(
		serviceable_constant.SyncSiteSALocationDataFailedMessage,
		utils.GetEnv(ctx),
		utils.FormatTimestamp(startTime, constant.DateAndTimeFormat),
		utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
		err.Msg,
	)
	cfg := config.GetMutableConf(ctx).ItemCardSceneConfig.SeatalkNotifyConfig
	_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, cfg.AtAll)
}

func syncSiteSALocationDataSuccessNotify(ctx utils.LCOSContext, startTime uint32, total, delNum int, siteIdMap map[string]string, failedSiteIdList []string) {
	message := fmt.Sprintf(
		serviceable_constant.SyncSiteSALocationNotifyMessage,
		utils.GetEnv(ctx),
		utils.FormatTimestamp(startTime, constant.DateAndTimeFormat),
		utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
		len(siteIdMap),
		strings.Join(failedSiteIdList, ","),
		total,
		delNum,
	)
	cfg := config.GetMutableConf(ctx).ItemCardSceneConfig.SeatalkNotifyConfig
	_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, cfg.AtAll)
}

func NewSiteServiceableAreaLocationService(siteServiceableAreaBasicConfDao site_serviceable_area.SiteServiceableAreaBasicConfDAOInterface, siteServiceableAreaLocationDao site_serviceable_area2.SiteServiceableAreaLocationDaoInterface) *siteServiceableAreaLocationService {
	return &siteServiceableAreaLocationService{
		siteServiceableAreaBasicConfDao: siteServiceableAreaBasicConfDao,
		siteServiceableAreaLocationDao:  siteServiceableAreaLocationDao,
	}
}

var _ SiteServiceableAreaLocationInterface = (*siteServiceableAreaLocationService)(nil)
