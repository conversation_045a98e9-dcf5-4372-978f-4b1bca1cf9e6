package sls_opa_service

import (
	"context"
	"log"
	"reflect"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_opa"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
)

func TestSlsOpaService_sendEmail(t *testing.T) {
	type args struct {
		ctx       context.Context
		res       []*sls_opa.AuditLogTab
		operators []string
	}
	tests := []struct {
		name    string
		s       *slsOpaService
		args    args
		wantErr bool
	}{
		{
			name: "default",
			s:    &slsOpaService{},
			args: args{
				ctx: context.TODO(),
				res: []*sls_opa.AuditLogTab{
					{
						Country: "ID",
						Project: "sls",
						Path:    "/api/v2/sls/forward/lcos/product/admin/cdt_management/manual_update_rule/export/",
						Body:    "{\"action_code\":4,\"id\":1246,\"product_id\":\"80088\",\"product_name\":\"80088 Shopee Express Standard LPS\"}",
						Email:   "<EMAIL>",
						Ctime:   1727594158,
					},
					{
						Country: "ID",
						Project: "sls",
						Path:    "/api/v2/sls/forward/lcos/product/admin/cdt_management/manual_update_rule/export/",
						Body:    "{\"effective_date\":1727542800,\"expiration_date\":1727715599,\"is_lm\":0,\"is_site_line\":1,\"mode\":2,\"object_type\":0,\"product_infos\":[{\"cb_type\":0,\"integrated_type\":1,\"masking_type\":0,\"product_id\":\"80088\",\"product_name\":\"80088 Shopee Express Standard LPS\"}],\"route_file_name\":\"edd_add3_-_special_for_NH.xlsx\",\"route_file_url\":\"https://proxy.uss.s3.test.shopee.io/shopee_slsopsrecon_sg_test/ssc-ops/20240929/608651778701017df20c00c19ecab4c5/edd_add3_-_special_for_NH.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256\\u0026X-Amz-Credential=60034057%2F20240929%2Fdefault%2Fs3%2Faws4_request\\u0026X-Amz-Date=20240929T071538Z\\u0026X-Amz-Expires=604800\\u0026X-Amz-SignedHeaders=host\\u0026X-Amz-Signature=ab26dc90a1294d7bd18a0b966f1e654be7ed52897917d4025058acf5359d4fbe\",\"task_name\":\"sdfsdf\"}",
						Email:   "<EMAIL>",
						Ctime:   1727594145,
					},
				},
				operators: []string{"<EMAIL>"},
			},
			wantErr: false,
		},
		{
			name: "empty test",
			s:    &slsOpaService{},
			args: args{
				ctx:       context.TODO(),
				res:       []*sls_opa.AuditLogTab{},
				operators: []string{"<EMAIL>"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &slsOpaService{}
			if err := s.createExcelAndSendEmail(tt.args.ctx, tt.args.res, tt.args.operators); (err != nil) != tt.wantErr {
				t.Errorf("slsOpaService.sendEmail() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_slsOpaService_ScanAuditWriteLogSendEmail(t *testing.T) {
	ctx := utils.NewCommonCtx(context.Background())
	cnf := &config.Config{
		DBSlsOpaConfig: config.DBRWConfig{
			Read: config.DBConfig{
				DSN: "sz_sc_test2:6vJaoy5HHiPP0ms_sUnO@tcp(master.1ae45da9755c4b76.mysql.cloud.test.shopee.io:6606)/shopee_sls_ops_biz_db",
			},
		},
	}

	config.SetConfig(cnf)

	if err := startup.InitSlsOpaDBLibs(cnf); err != nil {
		log.Fatalf("InitSlsOpaDBLibs Error: %v", err)
	}

	type fields struct {
		slsOpaDao sls_opa.SlsOpaDAO
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		wantLcosErr *lcos_error.LCOSError
	}{
		{
			name: "default",
			fields: fields{
				slsOpaDao: sls_opa.NewOpadayDao(),
			},
			args: args{
				ctx: ctx,
			},
			wantLcosErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &slsOpaService{
				slsOpaDao: tt.fields.slsOpaDao,
			}
			if gotLcosErr := s.ScanAuditWriteLogSendEmail(tt.args.ctx); !reflect.DeepEqual(gotLcosErr, tt.wantLcosErr) {
				t.Errorf("slsOpaService.ScanAuditWriteLogSendEmail() = %v, want %v", gotLcosErr, tt.wantLcosErr)
			}
		})
	}
}
