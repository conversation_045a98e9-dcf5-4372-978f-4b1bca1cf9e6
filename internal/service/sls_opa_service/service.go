package sls_opa_service

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_opa"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/emailhelper"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/tidwall/gjson"
)

type SlsOpaService interface {
	// 扫描昨日audit_log, 获取指定write内容, 发送邮件
	ScanAuditWriteLogSendEmail(context.Context) (err *lcos_error.LCOSError)
}

var _ SlsOpaService = (*slsOpaService)(nil)

type slsOpaService struct {
	slsOpaDao sls_opa.SlsOpaDAO
}

func NewSlsOpaService(slsOpaDao sls_opa.SlsOpaDAO) *slsOpaService {
	return &slsOpaService{
		slsOpaDao: slsOpaDao,
	}
}

func (s *slsOpaService) ScanAuditWriteLogSendEmail(ctx context.Context) (lcosErr *lcos_error.LCOSError) {
	resData, lcosErr := s.slsOpaDao.GetAllAuditWriteLog(utils.NewCommonCtx(ctx))
	if lcosErr != nil {
		return lcosErr
	}

	logger.CtxLogInfof(ctx, "ScanAuditWriteLogSendEmail total: %v", len(resData))
	if len(resData) == 0 {
		return nil
	}

	return s.createExcelAndSendEmail(ctx, resData, config.UnmarshalArray(config.GetMutableConf(ctx).SlsOpsEmails))
}

func (s *slsOpaService) createExcelAndSendEmail(ctx context.Context, res []*sls_opa.AuditLogTab, operators []string) *lcos_error.LCOSError {
	if len(res) == 0 || len(operators) == 0 {
		return nil
	}

	// parse into excel
	fname := "EDD and EDT Configuration Change Details.xlsx"
	sheetName := "Sheet1"

	f := excelize.NewFile()
	f.SetActiveSheet(f.NewSheet(sheetName))

	titles := []string{
		"Region",
		"Operation Type",
		"Task Details",
		"File Uploaded",
		"Operator Email",
		"Operation Time",
		"Product ID - Product Seller Side Name",
	}

	for i, title := range titles {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		if err := f.SetCellValue(sheetName, cell, title); err != nil {
			logger.CtxLogErrorf(ctx, "xlsx set title cell: %v", err)
		}
	}

	for i, v := range res {
		operationType := v.Path

		projectName := fmt.Sprintf("%s-%s", gjson.Get(v.Body, "product_id").String(), gjson.Get(v.Body, "product_name"))
		if projectName == "-" {
			projectName = ""
		}

		s3Urls := []string{}
		gjson.Parse(v.Body).ForEach(func(key, value gjson.Result) bool {
			if value.Type == gjson.String && strings.Contains(value.String(), "X-Amz-SignedHeaders=") {
				s3Urls = append(s3Urls, value.String())
			}
			return true
		})

		_ = f.SetCellValue(sheetName, fmt.Sprintf("A%d", i+2), v.Country)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("B%d", i+2), operationType)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("C%d", i+2), v.Body)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("D%d", i+2), strings.Join(s3Urls, "\n"))
		_ = f.SetCellValue(sheetName, fmt.Sprintf("E%d", i+2), v.Email)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("F%d", i+2), time.Unix(int64(v.Ctime), 0).Format("2006-01-02 15:04:05"))
		_ = f.SetCellValue(sheetName, fmt.Sprintf("G%d", i+2), projectName)
	}

	subject := "[Attention Needed] EDD/EDT Configuration is changed"
	body := `<html>Hi teams,<br><br>Please take note that following configurations have changes. <br></html>`

	if err := f.SaveAs(fname); err != nil {
		logger.CtxLogErrorf(ctx, "sls opa email save: %v", err)
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "sls opa audit email template error")
	}
	defer os.Remove(fname) // ignore error. 兜底删除

	sErr := emailhelper.SendEmailV2(body, subject, "text/html", fname, operators, nil, nil)
	if sErr != nil {
		logger.CtxLogErrorf(ctx, "send email error: %v", sErr)
	}

	return nil
}
