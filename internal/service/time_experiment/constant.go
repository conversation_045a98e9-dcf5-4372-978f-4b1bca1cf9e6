package time_experiment

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/time_experiment_repo"

type DestinationLocationMode int8

const (
	RouteDefineTypeRouteMode    = 1
	RouteDefineTypeCepMode      = 2
	RouteDefineTypePostcodeMode = 3
)

const (
	StatusDisable = 0
	StatusEnable  = 1
)

const (
	DefaultRouteGroupNameMaxLength     = 60
	DefaultSelectChanelMaxLength       = 50
	DefaultMinimumThresholdEDTMinValue = 0
	DefaultMinimumThresholdEDTMaxValue = 99

	DefaultEDTDeltaMinValue      = -99
	DefaultEDTDeltaMaxValue      = 99
	DefaultStrategyNameMaxLength = 60
)

type ParsedAbtestRouteGroupLocationRule struct {
	Row                 int                                                   `json:"row"`
	DestinationRegion   string                                                `json:"destination_region" `
	DestinationState    string                                                `json:"destination_state" `
	DestinationCity     string                                                `json:"destination_city" `
	DestinationDistrict string                                                `json:"destination_district" `
	OriginRegion        string                                                `json:"origin_region" `
	OriginState         string                                                `json:"origin_state" `
	OriginCity          string                                                `json:"origin_city" `
	OriginDistrict      string                                                `json:"origin_district"`
	CleanedData         *time_experiment_repo.AbtestRouteGroupLocationRuleTab `json:"-"` // used to store the cleaned data
	ErrorMessage        string                                                `json:"-"`
}

func (p *ParsedAbtestRouteGroupLocationRule) IsValid() {
	if !p.IsValidDestinationRegion() {
		p.ErrorMessage = "destination region is empty"
	}
}

func (p *ParsedAbtestRouteGroupLocationRule) IsValidDestinationRegion() bool {
	return p.DestinationRegion != ""
}
