package time_experiment

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/time_experiment_protocol"
	Logger "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/time_experiment_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"os"
	"strconv"
	"strings"
)

type TimeExperimentService interface {
	CreateABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupCreateRequest) *lcos_error.LCOSError
	ListABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupListRequest) (*time_experiment_protocol.RouteGroupListResponse, *lcos_error.LCOSError)
	ViewABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupViewRequest) (*time_experiment_protocol.RouteGroupViewResponse, *lcos_error.LCOSError)
	EditABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupEditRequest) *lcos_error.LCOSError
	DeleteABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupDeleteRequest) *lcos_error.LCOSError
	CreateEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyCreateRequest) *lcos_error.LCOSError
	ListEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyListRequest) (*time_experiment_protocol.EdtStrategyListResponse, *lcos_error.LCOSError)
	ViewEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyViewRequest) (*time_experiment_protocol.EdtStrategyViewResponse, *lcos_error.LCOSError)
	EditEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyEditRequest) *lcos_error.LCOSError
	DeleteEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyDeleteRequest) *lcos_error.LCOSError
}

type TimeExperimentServiceImpl struct {
	TimeExperimentRepo time_experiment_repo.TimeExperimentRepo
}

func NewTimeExperimentServiceImpl(timeExperimentRepo time_experiment_repo.TimeExperimentRepo) *TimeExperimentServiceImpl {
	return &TimeExperimentServiceImpl{
		TimeExperimentRepo: timeExperimentRepo,
	}
}
func (t *TimeExperimentServiceImpl) CreateABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupCreateRequest) *lcos_error.LCOSError {
	var (
		region         = strings.ToUpper(ctx.GetCountry())
		operator       = ctx.GetUserName()
		routeGroupRule time_experiment_repo.AbtestRouteGroupRuleTab
		requestID      = ctx.GetRequestId()
	)
	// 1.参数校验
	if err := validateRouteGroupCreateRequest(ctx, req); err != nil {
		Logger.CtxLogErrorf(ctx, "validate rute group create req fail,err:%s", err.Msg)
		return err
	}

	// 转化成db结构
	routeGroupRule = transformReqToRouteGroupRuleTab(region, operator, req)
	// 防止外层context done掉影响
	baseCtx := utils.NewLogContext(ctx, "|"+requestID)
	newCtx := utils.NewCommonCtx(baseCtx)
	routeGroupRouteLocationRules, tErr := transformReqToRouteGroupRouteLocationTab(newCtx, region, req)
	if tErr != nil {
		Logger.CtxLogErrorf(ctx, "transform req to route group route location tab fail,err:%s", tErr.Msg)
		return tErr
	}
	// 事务插入
	if err := t.createRouteGroupRule(ctx, routeGroupRule, routeGroupRouteLocationRules); err != nil {
		Logger.CtxLogDebugf(ctx, "create route group rule fail,err:%s", err.Msg)
		return err
	}

	return nil
}

func validateRouteGroupCreateRequest(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupCreateRequest) *lcos_error.LCOSError {
	if req == nil {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "RouteGroupCreateRequest is nil")
	}
	if req.RouteGroupName == "" {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "RouteGroupName is empty")
	}

	if len(req.RouteGroupName) > DefaultRouteGroupNameMaxLength {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("RouteGroupName length is too long ，max length must be less than %d,actual is %d", DefaultRouteGroupNameMaxLength, len(req.RouteGroupName)))
	}

	if len(req.SelectChannel) > DefaultSelectChanelMaxLength {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("SelectChannel length is too long ，max length must be less than %d,actual is %d", DefaultSelectChanelMaxLength, len(req.SelectChannel)))
	}
	// 本期只支持route
	if req.RouteDefineType != RouteDefineTypeRouteMode {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "RouteDefineType is not support")
	}

	if req.MinimumThresholdEdtMin < DefaultMinimumThresholdEDTMinValue || req.MinimumThresholdEdtMax > DefaultMinimumThresholdEDTMaxValue {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("MinimumThresholdEdtMin or MinimumThresholdEdtMax is out of range,MinimumThresholdEdtMin must be greater than %d,MinimumThresholdEdtMax must be less than %d", DefaultMinimumThresholdEDTMinValue, DefaultMinimumThresholdEDTMaxValue))
	}
	if req.MinimumThresholdEdtMax < DefaultMinimumThresholdEDTMinValue || req.MinimumThresholdEdtMax > DefaultMinimumThresholdEDTMaxValue {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("MinimumThresholdEdtMax or MinimumThresholdEdtMax is out of range,MinimumThresholdEdtMax must be greater than %d,MinimumThresholdEdtMax must be less than %d", DefaultMinimumThresholdEDTMinValue, DefaultMinimumThresholdEDTMaxValue))
	}

	return nil
}

func transformReqToRouteGroupRuleTab(region, operator string, req *time_experiment_protocol.RouteGroupCreateRequest) time_experiment_repo.AbtestRouteGroupRuleTab {
	var (
		selectChannelList string
		tab               = time_experiment_repo.AbtestRouteGroupRuleTab{}
	)

	if req == nil {
		return tab
	}

	selectChannelList = transferSelectChannelListToString(req.SelectChannel)

	return time_experiment_repo.AbtestRouteGroupRuleTab{
		Region:                 region,
		RouteGroupName:         req.RouteGroupName,
		SelectAllChannel:       req.SelectAllChannel,
		SelectChannelList:      selectChannelList,
		BuyerLocationFileName:  req.BuyerLocationFileName,
		BuyerLocationFileURL:   req.BuyerLocationFileURL,
		RouteDefineType:        RouteDefineTypeRouteMode,
		MinimumThresholdEdtMin: req.MinimumThresholdEdtMin,
		MinimumThresholdEdtMax: req.MinimumThresholdEdtMax,
		StatusId:               *req.Status,
		Operator:               operator,
	}
}

func transferSelectChannelListToString(selectChannelList []int) string {
	var (
		selectChannelStrList []string
		selectChannelStr     string
	)

	if len(selectChannelList) == 0 {
		return selectChannelStr
	}

	for _, channel := range selectChannelList {
		selectChannelStrList = append(selectChannelStrList, strconv.Itoa(channel))
	}
	selectChannelStr = strings.Join(selectChannelStrList, ",")
	return selectChannelStr
}

func transferSelectChannelListToSlice(selectChannelList string) ([]int, *lcos_error.LCOSError) {
	var (
		selectChannel []int
	)
	list := strings.Split(selectChannelList, ",")
	for _, v := range list {
		if strings.Trim(v, " ") == "" {
			continue
		}
		channel, err := strconv.Atoi(v)
		if err != nil {
			return selectChannel, lcos_error.NewLCOSError(lcos_error.DBReadErrorCode, "selectChannelList is error")
		}
		selectChannel = append(selectChannel, channel)
	}
	return selectChannel, nil
}

func transformReqToRouteGroupRouteLocationTab(ctx utils.LCOSContext, region string, req *time_experiment_protocol.RouteGroupCreateRequest) ([]time_experiment_repo.AbtestRouteGroupLocationRuleTab, *lcos_error.LCOSError) {
	var (
		s3Url = req.BuyerLocationFileURL
		tabs  []time_experiment_repo.AbtestRouteGroupLocationRuleTab
	)
	// 1. download from uss server
	filePath, lcosErr := serviceable_util.DownloadFileFromS3(ctx, s3Url)
	if lcosErr != nil {
		return nil, lcosErr
	}
	defer func(name string) {
		_ = os.Remove(name)
	}(filePath)
	logger.CtxLogInfof(ctx, "successfully download file url:[%s] from S3 service", s3Url)

	// 2. parse s3 file
	file, err1 := excelize.OpenFile(filePath)
	if err1 != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err1.Error())
	}
	dataList, rowErr := parseRouteGroupRouteLocationExcel(ctx, file)
	if rowErr != nil {
		return nil, rowErr
	}
	err := addRouteGroupRouteLocationRuleCleanData(ctx, dataList, region)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "addRouteGroupRouteLocationRuleCleanData fail,err:%s", err.Msg)
		return nil, err
	}

	for _, data := range dataList {
		tabs = append(tabs, *data.CleanedData)
	}

	return tabs, nil
}

func parseRouteGroupRouteLocationExcel(ctx utils.LCOSContext, file *excelize.File) ([]*ParsedAbtestRouteGroupLocationRule, *lcos_error.LCOSError) {
	var (
		uploadedData  []*ParsedAbtestRouteGroupLocationRule
		maxUploadSize = config.GetRouteGroupRuleMaxUploadSize(ctx)
	)
	sheet1 := file.WorkBook.Sheets.Sheet[0].Name

	rows, err := file.GetRows(sheet1)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("parsing file failed|error=%v", err.Error()))
	}

	// 遍历rows
	for index, row := range rows {
		// 跳过行首
		if index == 0 || serviceable_util.IsBlankRow(row) {
			continue
		}
		// 解析校验
		if index > maxUploadSize {
			return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("line num is %d the size of upload file should be not more than %d lines", index, maxUploadSize))
		}

		rowStruct := fillStructWithRowData(row)
		rowStruct.Row = index + 1 // row is equal to index + 1
		// 基础数据校验
		rowStruct.IsValid()

		// 解析过程中就报错
		if len(rowStruct.ErrorMessage) > 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("row num:[%d] error:[%s]", rowStruct.Row, rowStruct.ErrorMessage))
		}

		uploadedData = append(uploadedData, rowStruct)
	}

	// 如果解析出来的数据为空，表示失败
	if len(uploadedData) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cannot upload empty excel")
	}

	return uploadedData, nil
}

func addRouteGroupRouteLocationRuleCleanData(ctx utils.LCOSContext, parsedRouteRules []*ParsedAbtestRouteGroupLocationRule, region string) *lcos_error.LCOSError {
	for _, singleRule := range parsedRouteRules {
		if len(singleRule.ErrorMessage) == 0 {
			singleRule.CleanedData = &time_experiment_repo.AbtestRouteGroupLocationRuleTab{
				Region:                region,
				DestinationLocationID: 0,
				DestinationRegion:     strings.Trim(singleRule.DestinationRegion, " "),
				DestinationState:      strings.Trim(singleRule.DestinationState, " "),
				DestinationCity:       strings.Trim(singleRule.DestinationCity, " "),
				DestinationDistrict:   strings.Trim(singleRule.DestinationDistrict, " "),
				OriginRegion:          strings.Trim(singleRule.OriginRegion, " "),
				OriginState:           strings.Trim(singleRule.OriginState, " "),
				OriginCity:            strings.Trim(singleRule.OriginCity, " "),
				OriginDistrict:        strings.Trim(singleRule.OriginDistrict, " "),
				OriginLocationID:      0,
			}

			// sender
			// 支持sender只为region,也就是location id为0
			if singleRule.CleanedData.OriginState != "" {
				originLocationInfo, lcosErr := address_service.LocationServer.GetLocationInfoByName(ctx, singleRule.CleanedData.OriginRegion,
					singleRule.CleanedData.OriginState, singleRule.CleanedData.OriginCity, singleRule.CleanedData.OriginDistrict, "")
				if lcosErr != nil {
					errMsg := fmt.Sprintf("cannot get location info,|location=[%s|%s|%s|%s|%s], error=%s", singleRule.CleanedData.OriginRegion, singleRule.CleanedData.OriginState, singleRule.CleanedData.OriginCity, singleRule.CleanedData.OriginDistrict, "", lcosErr.Msg)
					logger.CtxLogErrorf(ctx, errMsg)
					singleRule.ErrorMessage = errMsg
					return lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, errMsg)
				}

				if originLocationInfo.StateLocationId != 0 {
					singleRule.CleanedData.OriginLocationID = originLocationInfo.StateLocationId
				}
				if originLocationInfo.CityLocationId != 0 {
					singleRule.CleanedData.OriginLocationID = originLocationInfo.CityLocationId
				}
				if originLocationInfo.DistrictLocationId != 0 {
					singleRule.CleanedData.OriginLocationID = originLocationInfo.DistrictLocationId
				}

			}

			// receiver
			// 支持receiver只为region,也就是location id为0
			if singleRule.CleanedData.DestinationState != "" {
				destinationLocationInfo, lcosErr := address_service.LocationServer.GetLocationInfoByName(ctx, singleRule.CleanedData.DestinationRegion,
					singleRule.CleanedData.DestinationState, singleRule.CleanedData.DestinationCity, singleRule.CleanedData.DestinationDistrict, "")
				if lcosErr != nil {
					errMsg := fmt.Sprintf("cannot get location info,|location=[%s|%s|%s|%s|%s], error=%s", singleRule.CleanedData.DestinationRegion, singleRule.CleanedData.DestinationState, singleRule.CleanedData.DestinationCity, singleRule.CleanedData.DestinationDistrict, "", lcosErr.Msg)
					logger.CtxLogErrorf(ctx, errMsg)
					singleRule.ErrorMessage = errMsg
					return lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, errMsg)
				}

				if destinationLocationInfo.StateLocationId != 0 {
					singleRule.CleanedData.DestinationLocationID = destinationLocationInfo.StateLocationId
				}
				if destinationLocationInfo.CityLocationId != 0 {
					singleRule.CleanedData.DestinationLocationID = destinationLocationInfo.CityLocationId
				}
				if destinationLocationInfo.DistrictLocationId != 0 {
					singleRule.CleanedData.DestinationLocationID = destinationLocationInfo.DistrictLocationId
				}

			}

		}
	}
	return nil
}

func (t *TimeExperimentServiceImpl) createRouteGroupRule(ctx utils.LCOSContext, routeGroupRule time_experiment_repo.AbtestRouteGroupRuleTab, routeLocationRuleList []time_experiment_repo.AbtestRouteGroupLocationRuleTab) *lcos_error.LCOSError {
	if len(routeLocationRuleList) == 0 {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "routeLocationRuleList is empty")
	}
	err := ctx.Transaction(func() *lcos_error.LCOSError {
		if rErr := t.TimeExperimentRepo.CreateOrUpdateRouteGroupRule(ctx, &routeGroupRule); rErr != nil {
			return rErr
		}
		// 需要插入绑定的rule id
		var dataList []time_experiment_repo.AbtestRouteGroupLocationRuleTab
		for _, tab := range routeLocationRuleList {
			tab.RouteGroupID = routeGroupRule.ID
			dataList = append(dataList, tab)
		}

		if lErr := t.TimeExperimentRepo.BatchCreateOrUpdateRouteLocationRule(ctx, dataList); lErr != nil {
			return lErr
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (t *TimeExperimentServiceImpl) ListABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupListRequest) (*time_experiment_protocol.RouteGroupListResponse, *lcos_error.LCOSError) {
	var (
		region        = strings.ToUpper(ctx.GetCountry())
		pageNo, count = utils.GenPageAndCount(req.PageNo, req.Count)
		condition     = make(map[string]interface{})
		resp          []time_experiment_protocol.RouteGroupData
	)

	// 1.参数校验
	condition["region"] = region

	// 2.db搜索
	tabs, total, err := t.TimeExperimentRepo.ListRouteGroupRuleByPage(ctx, condition, pageNo, count)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "ListRouteGroupRuleByPage fail,err:%s", err.Msg)
		return nil, err
	}

	// 3.转化成response
	resp = transferRouteGroupRuleDbToResponse(ctx, tabs)

	return &time_experiment_protocol.RouteGroupListResponse{
		PageNo: pageNo,
		Count:  count,
		Total:  total,
		List:   resp,
	}, nil
}

func transferRouteGroupRuleDbToResponse(ctx utils.LCOSContext, tabs []time_experiment_repo.AbtestRouteGroupRuleTab) []time_experiment_protocol.RouteGroupData {
	var (
		routeGroupDataList = make([]time_experiment_protocol.RouteGroupData, 0, len(tabs))
	)
	if len(tabs) == 0 {
		return routeGroupDataList
	}
	for _, tab := range tabs {
		selectChannel, err := transferSelectChannelListToSlice(tab.SelectChannelList)
		if err != nil {
			// 这里不return err 默认无错误
			Logger.CtxLogErrorf(ctx, "transferSelectChannelListToSlice fail,err:%s", err.Msg)

		}
		routeGroupDataList = append(routeGroupDataList, time_experiment_protocol.RouteGroupData{
			RouteGroupID:           tab.ID,
			RouteGroupName:         tab.RouteGroupName,
			SelectChannel:          selectChannel,
			SelectAllChannel:       tab.SelectAllChannel,
			Status:                 tab.StatusId,
			MinimumThresholdEdtMin: tab.MinimumThresholdEdtMin,
			MinimumThresholdEdtMax: tab.MinimumThresholdEdtMax,
			EffectiveTime:          tab.MTime,
			Operator:               tab.Operator,
		})
	}
	return routeGroupDataList
}

func (t *TimeExperimentServiceImpl) ViewABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupViewRequest) (*time_experiment_protocol.RouteGroupViewResponse, *lcos_error.LCOSError) {
	var (
		region       = strings.ToUpper(ctx.GetCountry())
		condition    = make(map[string]interface{})
		routeGroupId = req.RouteGroupId
	)
	// 1.参数校验
	if routeGroupId == 0 {
		Logger.CtxLogErrorf(ctx, "routeGroupId is empty,req:%v", req)
		return nil, lcos_error.NewLCOSError(lcos_error.ParamsError, "routeGroupId is empty")
	}

	// 1.参数校验
	condition["region"] = region
	condition["id"] = routeGroupId

	// 2.db搜索
	tab, err := t.TimeExperimentRepo.GetRouteGroupRule(ctx, condition)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "GetRouteGroupRule fail,err:%s,condition:%+v", err.Msg, condition)
		return nil, err
	}

	// 参数转换
	resp, tErr := transferRouteGroupRuleDbToViewResponse(tab)
	if tErr != nil {
		Logger.CtxLogErrorf(ctx, "transferRouteGroupRuleDbToViewResponse fail,err:%s,tab:%+v", tErr.Msg, tab)
		return nil, tErr
	}
	return &resp, nil
}

func transferRouteGroupRuleDbToViewResponse(tab time_experiment_repo.AbtestRouteGroupRuleTab) (time_experiment_protocol.RouteGroupViewResponse, *lcos_error.LCOSError) {
	var (
		resp = time_experiment_protocol.RouteGroupViewResponse{}
	)
	selectChannel, err := transferSelectChannelListToSlice(tab.SelectChannelList)
	if err != nil {
		return resp, err
	}

	return time_experiment_protocol.RouteGroupViewResponse{
		RouteGroupId:           tab.ID,
		RouteGroupName:         tab.RouteGroupName,
		RouteDefineType:        tab.RouteDefineType,
		SelectChannel:          selectChannel,
		SelectAllChannel:       tab.SelectAllChannel,
		BuyerLocationFileName:  tab.BuyerLocationFileName,
		BuyerLocationFileURL:   tab.BuyerLocationFileURL,
		MinimumThresholdEdtMin: tab.MinimumThresholdEdtMin,
		MinimumThresholdEdtMax: tab.MinimumThresholdEdtMax,
		Status:                 tab.StatusId,
	}, nil
}

func (t *TimeExperimentServiceImpl) EditABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupEditRequest) *lcos_error.LCOSError {
	// 1.参数校验
	if err := validateRouteGroupEditRequest(ctx, req); err != nil {
		Logger.CtxLogErrorf(ctx, "validate fail,err msg:%s", err.Msg)
		return err
	}

	var (
		region           = strings.ToUpper(ctx.GetCountry())
		operator         = ctx.GetUserName()
		routeGroupRuleId = req.RouteGroupId
		condition        = map[string]interface{}{
			"region": region,
			"id":     routeGroupRuleId,
		}
	)

	// 1.1 获取参数
	tab, err := t.TimeExperimentRepo.GetRouteGroupRule(ctx, condition)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "GetRouteGroupRule fail,err:%s,condition:%+v", err.Msg, condition)
		return err
	}

	// 2.转化成db结构
	data, tErr := transferRouteGroupEditRequestToDbStruct(ctx, req, tab)
	if tErr != nil {
		Logger.CtxLogErrorf(ctx, "transferRouteGroupEditRequestToDbStruct fail,err:%s,req:%+v", tErr.Msg, req)
		return tErr
	}
	data.Operator = operator

	// 3.数据更新
	err = t.TimeExperimentRepo.CreateOrUpdateRouteGroupRule(ctx, &data)

	return err
}

func validateRouteGroupEditRequest(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupEditRequest) *lcos_error.LCOSError {
	if req == nil {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "RouteGroupCreateRequest is nil")
	}
	if req.RouteGroupName != nil && *req.RouteGroupName == "" {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "RouteGroupName is empty")
	}

	if req.RouteGroupName != nil && len(*req.RouteGroupName) > DefaultRouteGroupNameMaxLength {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("RouteGroupName length is too long ，max length must be less than %d,actual is %d", DefaultRouteGroupNameMaxLength, len(*req.RouteGroupName)))
	}

	if req.SelectChannel != nil && len(*req.SelectChannel) > DefaultSelectChanelMaxLength {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("SelectChannel length is too long ，max length must be less than %d,actual is %d", DefaultSelectChanelMaxLength, len(*req.SelectChannel)))
	}
	// 本期只支持route

	if req.MinimumThresholdEdtMin != nil && (*req.MinimumThresholdEdtMin < DefaultMinimumThresholdEDTMinValue || *req.MinimumThresholdEdtMax > DefaultMinimumThresholdEDTMaxValue) {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("MinimumThresholdEdtMin or MinimumThresholdEdtMax is out of range,MinimumThresholdEdtMin must be greater than %d,MinimumThresholdEdtMin must be less than %d", DefaultMinimumThresholdEDTMinValue, DefaultMinimumThresholdEDTMaxValue))
	}
	if req.MinimumThresholdEdtMax != nil && (*req.MinimumThresholdEdtMax < DefaultMinimumThresholdEDTMinValue || *req.MinimumThresholdEdtMax > DefaultMinimumThresholdEDTMaxValue) {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("MinimumThresholdEdtMax or MinimumThresholdEdtMax is out of range,MinimumThresholdEdtMax must be greater than %d,MinimumThresholdEdtMax must be less than %d", DefaultMinimumThresholdEDTMinValue, DefaultMinimumThresholdEDTMaxValue))
	}

	return nil
}

func transferRouteGroupEditRequestToDbStruct(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupEditRequest,
	oldTab time_experiment_repo.AbtestRouteGroupRuleTab) (time_experiment_repo.AbtestRouteGroupRuleTab, *lcos_error.LCOSError) {
	var (
		tab                     = oldTab
		oldSelectChannelList, _ = transferSelectChannelListToSlice(oldTab.SelectChannelList)
		selectChannelMap        = make(map[int]int)
	)

	if req == nil {
		return tab, lcos_error.NewLCOSError(lcos_error.ParamsError, "req is empty")
	}
	if req.RouteGroupName != nil {
		tab.RouteGroupName = *req.RouteGroupName
	}
	if req.SelectAllChannel != nil {
		tab.SelectAllChannel = *req.SelectAllChannel
	}
	if req.Status != nil {
		tab.StatusId = *req.Status
	}
	if req.MinimumThresholdEdtMin != nil {
		tab.MinimumThresholdEdtMin = *req.MinimumThresholdEdtMin
	}
	if req.MinimumThresholdEdtMax != nil {
		tab.MinimumThresholdEdtMax = *req.MinimumThresholdEdtMax
	}

	// 判断 select channel 是否改变
	if req.SelectChannel != nil {
		if len(*req.SelectChannel) == 0 && len(oldSelectChannelList) == 0 {
			// 都为0的情况下，直接返回
			return tab, nil
		}
		if len(*req.SelectChannel) != len(oldSelectChannelList) {
			tab.SelectChannelList = transferSelectChannelListToString(*req.SelectChannel)
			return tab, nil
		}

		for _, channel := range oldSelectChannelList {
			selectChannelMap[channel] = channel
		}
		for _, channel := range *req.SelectChannel {
			if _, ok := selectChannelMap[channel]; !ok {
				tab.SelectChannelList = transferSelectChannelListToString(*req.SelectChannel)
				return tab, nil
			}
		}
	}
	return tab, nil

}

func (t *TimeExperimentServiceImpl) DeleteABTestRouteGroup(ctx *utils.HttpContext, req *time_experiment_protocol.RouteGroupDeleteRequest) *lcos_error.LCOSError {
	var (
		region              = strings.ToUpper(ctx.GetCountry())
		routeGroupRuleId    = req.RouteGroupId
		routeGroupCondition = map[string]interface{}{
			"region": region,
			"id":     routeGroupRuleId,
		}
		routeLocationRuleCondition = map[string]interface{}{
			"region":         region,
			"route_group_id": routeGroupRuleId,
		}
	)
	// 1.参数校验
	// 1.1 获取参数
	tab, err := t.TimeExperimentRepo.GetRouteGroupRule(ctx, routeGroupCondition)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "GetRouteGroupRule fail,err:%s,condition:%+v", err.Msg, routeGroupCondition)
		return err
	}
	// 1.2 如果不是disable状态，不允许删除
	if tab.StatusId != StatusDisable {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "route group status is not disable")
	}

	// 2.事务删除
	tErr := ctx.Transaction(func() *lcos_error.LCOSError {
		if dErr := t.TimeExperimentRepo.DeleteRouteGroupRule(ctx, routeGroupCondition); dErr != nil {
			Logger.CtxLogErrorf(ctx, "delete route group fail,err:%s", dErr.Msg)
			return dErr
		}
		if dErr := t.TimeExperimentRepo.DeleteRouteLocationRule(ctx, routeLocationRuleCondition); dErr != nil {
			Logger.CtxLogErrorf(ctx, "delete route group fail,err:%s", dErr.Msg)
			return dErr
		}
		return nil
	})
	if tErr != nil {
		Logger.CtxLogErrorf(ctx, "delete route group fail,err:%s", tErr.Msg)
		return tErr
	}

	return nil
}

func (t *TimeExperimentServiceImpl) CreateEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyCreateRequest) *lcos_error.LCOSError {
	var (
		region          = strings.ToUpper(ctx.GetCountry())
		operator        = ctx.GetUserName()
		edtStrategyRule time_experiment_repo.AbtestEdtStrategyRuleTab
	)
	// 1.参数校验
	if err := validateEdtStrategyRuleRequest(ctx, req); err != nil {
		Logger.CtxLogErrorf(ctx, "validate rute group create req fail,err:%s", err.Msg)
		return err
	}

	// 2.转化成db结构
	edtStrategyRule = transformReqToEdtStrategyRuleTab(region, operator, req)

	// 3.插入db
	if err := t.TimeExperimentRepo.CreateOrUpdateEdtStrategyRule(ctx, edtStrategyRule); err != nil {
		Logger.CtxLogDebugf(ctx, "create edt strategy rule fail,err:%s", err.Msg)
		return err
	}
	return nil
}

func validateEdtStrategyRuleRequest(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyCreateRequest) *lcos_error.LCOSError {
	if req == nil {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "EdtStrategyCreateRequest is nil")
	}
	if req.StrategyName == "" {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "StrategyName is empty")
	}

	if len(req.StrategyName) > DefaultStrategyNameMaxLength {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "Strategy Name is too long")
	}

	if req.ExperimentStrategy.EdtMinDelta < DefaultEDTDeltaMinValue || req.ExperimentStrategy.EdtMinDelta > DefaultEDTDeltaMaxValue {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "EdtMinDelta is out of range")
	}
	if req.ExperimentStrategy.EdtMaxDelta < DefaultEDTDeltaMinValue || req.ExperimentStrategy.EdtMaxDelta > DefaultEDTDeltaMaxValue {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "EdtMaxDelta is out of range")
	}
	return nil
}

func transformReqToEdtStrategyRuleTab(region, operator string, req *time_experiment_protocol.EdtStrategyCreateRequest) time_experiment_repo.AbtestEdtStrategyRuleTab {
	return time_experiment_repo.AbtestEdtStrategyRuleTab{
		Region:                     region,
		StrategyRuleName:           req.StrategyName,
		EdtMinDelta:                req.ExperimentStrategy.EdtMinDelta,
		EdtMaxDelta:                req.ExperimentStrategy.EdtMaxDelta,
		DeltaExtensionFilterToggle: req.DeltaExtensionFilterToggle,
		MinimumThresholdEdtCheck:   req.MinimumThresholdEdtCheck,
		EdtMinMaxValueCheck:        req.EdtMinMaxValueCheck,
		Operator:                   operator,
		StatusId:                   *req.Status,
	}
}

func (t *TimeExperimentServiceImpl) ListEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyListRequest) (*time_experiment_protocol.EdtStrategyListResponse, *lcos_error.LCOSError) {
	var (
		region        = strings.ToUpper(ctx.GetCountry())
		pageNo, count = utils.GenPageAndCount(req.PageNo, req.Count)
		condition     = make(map[string]interface{})
	)
	// 1 组装请求参数
	condition["region"] = region

	// 2 查询数据
	tabs, total, err := t.TimeExperimentRepo.LisEdtStrategyRuleByPage(ctx, condition, pageNo, count)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "list edt strategy rule fail,err:%s,condition:%v", err.Msg, condition)
		return nil, err
	}

	// 3 转化成返回
	return &time_experiment_protocol.EdtStrategyListResponse{
		PageNo: pageNo,
		Count:  count,
		Total:  total,
		List:   transferEdtStrategyRuleTabToResp(tabs),
	}, nil
}

func transferEdtStrategyRuleTabToResp(tabs []time_experiment_repo.AbtestEdtStrategyRuleTab) []time_experiment_protocol.EdtStrategyData {
	var (
		results []time_experiment_protocol.EdtStrategyData
	)

	for _, tab := range tabs {
		results = append(results, time_experiment_protocol.EdtStrategyData{
			StrategyId:   tab.ID,
			StrategyName: tab.StrategyRuleName,
			ExperimentStrategy: time_experiment_protocol.ExperimentStrategy{
				EdtMinDelta: tab.EdtMinDelta,
				EdtMaxDelta: tab.EdtMaxDelta,
			},
			Status:        tab.StatusId,
			EffectiveTime: tab.MTime,
			Operator:      tab.Operator,
		})
	}
	return results
}

func (t *TimeExperimentServiceImpl) ViewEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyViewRequest) (*time_experiment_protocol.EdtStrategyViewResponse, *lcos_error.LCOSError) {
	var (
		region     = strings.ToUpper(ctx.GetCountry())
		condition  = make(map[string]interface{})
		strategyId = req.StrategyId
	)
	// 1.参数校验
	if strategyId == 0 {
		Logger.CtxLogErrorf(ctx, "strategyId is empty,req:%v", req)
		return nil, lcos_error.NewLCOSError(lcos_error.ParamsError, "strategyId is empty")
	}

	// 1.参数校验
	condition["region"] = region
	condition["id"] = strategyId

	// 2.db搜索
	tab, err := t.TimeExperimentRepo.GetEdtStrategyRule(ctx, condition)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "GetEdtStrategyRule fail,err:%s,condition:%+v", err.Msg, condition)
		return nil, err
	}

	// 参数转换
	resp, tErr := transferEdtStrategyRuleDbToViewResponse(tab)
	if tErr != nil {
		Logger.CtxLogErrorf(ctx, "transferEdtStrategyRuleDbToViewResponse fail,err:%s,tab:%+v", tErr.Msg, tab)
		return nil, tErr
	}
	return &resp, nil
}

func transferEdtStrategyRuleDbToViewResponse(tab time_experiment_repo.AbtestEdtStrategyRuleTab) (time_experiment_protocol.EdtStrategyViewResponse, *lcos_error.LCOSError) {
	return time_experiment_protocol.EdtStrategyViewResponse{
		StrategyId:   tab.ID,
		StrategyName: tab.StrategyRuleName,
		ExperimentStrategy: time_experiment_protocol.ExperimentStrategy{
			EdtMinDelta: tab.EdtMinDelta,
			EdtMaxDelta: tab.EdtMaxDelta,
		},
		DeltaExtensionFilterToggle: tab.DeltaExtensionFilterToggle,
		MinimumThresholdEdtCheck:   tab.MinimumThresholdEdtCheck,
		EdtMinMaxValueCheck:        tab.EdtMinMaxValueCheck,
		Status:                     tab.StatusId,
	}, nil

}

func (t *TimeExperimentServiceImpl) EditEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyEditRequest) *lcos_error.LCOSError {
	// 1.参数校验
	if err := validateEdtStrategyRuleEditRequest(ctx, req); err != nil {
		Logger.CtxLogErrorf(ctx, "validate fail,err msg:%s", err.Msg)
		return err
	}

	var (
		region     = strings.ToUpper(ctx.GetCountry())
		operator   = ctx.GetUserName()
		strategyId = req.StrategyId
		condition  = map[string]interface{}{
			"region": region,
			"id":     strategyId,
		}
	)

	// 1.1 获取参数
	tab, err := t.TimeExperimentRepo.GetEdtStrategyRule(ctx, condition)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "GetEdtStrategyRule fail,err:%s,condition:%+v", err.Msg, condition)
		return err
	}

	// 2.转化成db结构
	data, tErr := transferEdtStrategyRuleRequestToDbStruct(ctx, req, tab)
	if tErr != nil {
		Logger.CtxLogErrorf(ctx, "transferEdtStrategyRuleRequestToDbStruct fail,err:%s,req:%+v", tErr.Msg, req)
		return tErr
	}
	data.Operator = operator

	// 3.数据更新
	err = t.TimeExperimentRepo.CreateOrUpdateEdtStrategyRule(ctx, data)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "CreateOrUpdateEdtStrategyRule fail,err:%s,data:%+v", err.Msg, data)
		return err
	}

	return nil
}

func validateEdtStrategyRuleEditRequest(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyEditRequest) *lcos_error.LCOSError {
	if req == nil {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "EdtStrategyCreateRequest is nil")
	}
	if req.StrategyName != nil && *req.StrategyName == "" {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "StrategyName is empty")
	}

	if req.StrategyName != nil && len(*req.StrategyName) > DefaultStrategyNameMaxLength {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "Strategy Name is too long")
	}

	if req.ExperimentStrategy != nil && (req.ExperimentStrategy.EdtMinDelta < DefaultEDTDeltaMinValue || req.ExperimentStrategy.EdtMinDelta > DefaultEDTDeltaMaxValue) {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "EdtMinDelta is out of range")
	}
	if req.ExperimentStrategy != nil && (req.ExperimentStrategy.EdtMaxDelta < DefaultEDTDeltaMinValue || req.ExperimentStrategy.EdtMaxDelta > DefaultEDTDeltaMaxValue) {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "EdtMaxDelta is out of range")
	}
	return nil
}

func transferEdtStrategyRuleRequestToDbStruct(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyEditRequest, oldTab time_experiment_repo.AbtestEdtStrategyRuleTab) (time_experiment_repo.AbtestEdtStrategyRuleTab, *lcos_error.LCOSError) {
	var (
		edtStrategyRuleTab = oldTab
	)

	if req.StrategyName != nil {
		edtStrategyRuleTab.StrategyRuleName = *req.StrategyName
	}

	if req.Status != nil {
		edtStrategyRuleTab.StatusId = *req.Status
	}
	if req.ExperimentStrategy != nil {
		edtStrategyRuleTab.EdtMinDelta = req.ExperimentStrategy.EdtMinDelta
		edtStrategyRuleTab.EdtMaxDelta = req.ExperimentStrategy.EdtMaxDelta
	}
	if req.DeltaExtensionFilterToggle != nil {
		edtStrategyRuleTab.DeltaExtensionFilterToggle = *req.DeltaExtensionFilterToggle
	}
	if req.MinimumThresholdEdtCheck != nil {
		edtStrategyRuleTab.MinimumThresholdEdtCheck = *req.MinimumThresholdEdtCheck
	}
	if req.EdtMinMaxValueCheck != nil {
		edtStrategyRuleTab.EdtMinMaxValueCheck = *req.EdtMinMaxValueCheck
	}
	return edtStrategyRuleTab, nil
}

func (t *TimeExperimentServiceImpl) DeleteEdtStrategyRule(ctx *utils.HttpContext, req *time_experiment_protocol.EdtStrategyDeleteRequest) *lcos_error.LCOSError {
	var (
		region     = strings.ToUpper(ctx.GetCountry())
		strategyId = req.StrategyId
		condition  = map[string]interface{}{
			"region": region,
			"id":     strategyId,
		}
	)
	// 1.参数校验
	// 1.1 获取参数
	tab, err := t.TimeExperimentRepo.GetEdtStrategyRule(ctx, condition)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "GetEdtStrategyRule fail,err:%s,condition:%+v", err.Msg, condition)
		return err
	}
	// 1.2 如果不是disable状态，不允许删除
	if tab.StatusId != StatusDisable {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "edt strategy status is not disable")
	}

	// 2.事务删除
	tErr := t.TimeExperimentRepo.DeleteEdtStrategyRule(ctx, condition)
	if tErr != nil {
		Logger.CtxLogErrorf(ctx, "delete route group fail,err:%s", tErr.Msg)
		return tErr
	}

	return nil
}
