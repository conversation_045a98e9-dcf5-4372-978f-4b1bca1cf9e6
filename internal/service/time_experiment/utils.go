package time_experiment

import (
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"strings"
)

var titles = []string{
	"origin_region",
	"origin_state",
	"origin_city",
	"origin_district",
	"destination_region",
	"destination_state",
	"destination_city",
	"destination_district",
	"destination_cep_range_initial",
	"destination_cep_range_final",
	"destination_postcode",
}

func fillStructWithRowData(row []string) *ParsedAbtestRouteGroupLocationRule {
	model := &ParsedAbtestRouteGroupLocationRule{}
	if len(row) == 0 {
		model.ErrorMessage = fmt.Sprintf("fields %s are required", strings.Join(titles[len(row):], ","))
		return model
	}

	tmpMap := map[string]interface{}{}
	for index, item := range row {
		if index < len(titles) {
			tmpMap[titles[index]] = item
		}
	}

	tmpResult, err := jsoniter.Marshal(tmpMap)
	if err != nil {
		model.ErrorMessage = err.Error()
	} else {
		err = jsoniter.Unmarshal(tmpResult, model)
		if err != nil {
			model.ErrorMessage = err.<PERSON>rror()
		}
	}
	return model
}
