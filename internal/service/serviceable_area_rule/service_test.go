package serviceable_area_rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/initialized_rule"
	initialized_model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/initialized_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
)

func TestServiceableAreaRuleService_CreateServiceableInitializedRule(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	type fields struct {
		initializedRuleDAO initialized_rule.InitializedRuleDAO
		effectiveRuleDAO   effective_rule.EffectiveRuleDAO
	}
	type args struct {
		ctx     utils.LCOSContext
		request *serviceable_area_rule.CreateInitializedRuleRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *initialized_model.ServiceableInitializedRuleTab
		want1  *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "test create IR",
			fields: fields{
				initializedRuleDAO: initialized_rule.NewInitializedRuleDAO(),
				effectiveRuleDAO:   effective_rule.NewEffectiveRuleDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &serviceable_area_rule.CreateInitializedRuleRequest{
					RuleName:         "unit-test 1",
					Range:            3,
					Region:           "TH",
					ModelName:        "BR 3-segments",
					FulfillmentModel: 111167,
					LaneConnection: []*dbhelper.LaneConnection{
						{
							Sequence: 1,
							Type:     2,
							SubType:  64,
						},
						{
							Sequence: 2,
							Type:     1,
							SubType:  8,
						},
						{
							Sequence: 3,
							Type:     2,
							SubType:  128,
						},
					},
					ServiceableRule: []*dbhelper.SARule{
						{
							Sequence:                 1,
							CheckCollectAddress:      1,
							CheckCollectAddressType:  1,
							CheckDeliveryAddressType: 1,
							CheckDeliveryAddress:     1,
							FiltrateMode:             0,
							ApFiltrate:               0,
							PointFiltrate:            0,
							OutboundRule:             0,
							InboundRule:              0,
						},
						{
							Sequence:                 2,
							CheckCollectAddress:      0,
							CheckCollectAddressType:  0,
							CheckDeliveryAddressType: 0,
							CheckDeliveryAddress:     0,
							FiltrateMode:             2,
							ApFiltrate:               1,
							PointFiltrate:            0,
							OutboundRule:             1,
							InboundRule:              2,
						},
						{
							Sequence:                 3,
							CheckCollectAddress:      2,
							CheckCollectAddressType:  2,
							CheckDeliveryAddressType: 2,
							CheckDeliveryAddress:     2,
							FiltrateMode:             0,
							ApFiltrate:               0,
							PointFiltrate:            0,
							OutboundRule:             0,
							InboundRule:              0,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ServiceableAreaRuleService{
				initializedRuleDAO: tt.fields.initializedRuleDAO,
				effectiveRuleDAO:   tt.fields.effectiveRuleDAO,
			}
			got, got1 := s.CreateServiceableInitializedRule(tt.args.ctx, tt.args.request)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ServiceableAreaRuleService.CreateServiceableInitializedRule() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("ServiceableAreaRuleService.CreateServiceableInitializedRule() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
