package serviceable_area_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	effective_model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/initialized_rule"
	initialized_model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/initialized_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"strings"
)

type ServiceableAreaRuleServiceInterface interface {
	// initialized rule
	CreateServiceableInitializedRule(ctx utils.LCOSContext, request *serviceable_area_rule.CreateInitializedRuleRequest) (*initialized_model.ServiceableInitializedRuleTab, *lcos_error.LCOSError)
	UpdateServiceableInitializedRule(ctx utils.LCOSContext, request *serviceable_area_rule.UpdateInitializedRuleRequest) (*initialized_model.ServiceableInitializedRuleTab, *lcos_error.LCOSError)
	GetServiceableInitializedRuleList(ctx utils.LCOSContext, request *serviceable_area_rule.ListInitializedRuleRequest) (*common.NewPageModel, *lcos_error.LCOSError)
	GetServiceableInitializedRule(ctx utils.LCOSContext, request *common_protocol.IDRequest) (*initialized_model.ServiceableInitializedRuleTab, *lcos_error.LCOSError)
	DeleteServiceableInitializedRule(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	DisableServiceableInitializedRule(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	CheckServiceableInitializedRuleExist(ctx utils.LCOSContext, request *serviceable_area_rule.CheckInitializedRuleExistRequest) (*serviceable_area_rule.CheckInitializedRuleExistResponse, *lcos_error.LCOSError)

	GetRuleIdList(ctx utils.LCOSContext) (*serviceable_area_rule.ListInitializedRuleIdResponse, *lcos_error.LCOSError)

	// effective rule
	UpdateServiceableEffectiveRule(ctx utils.LCOSContext, request *serviceable_area_rule.UpdateEffectiveRuleRequest) (*effective_model.ServiceableEffectiveRuleTab, *lcos_error.LCOSError)
	GetServiceableEffectiveRuleList(ctx utils.LCOSContext, request *serviceable_area_rule.ListEffectiveRuleRequest) (*common.NewPageModel, *lcos_error.LCOSError)
	GetServiceableEffectiveRule(ctx utils.LCOSContext, request *common_protocol.IDRequest) (*effective_model.ServiceableEffectiveRuleTab, *lcos_error.LCOSError)
	BatchUpdateServiceableEffectiveRule(ctx utils.LCOSContext, request *serviceable_area_rule.BatchUpdateEffectiveRuleRequest) *lcos_error.LCOSError
	GetBatchUpdateRuleInfo(ctx utils.LCOSContext, request *serviceable_area_rule.GetRuleInfoRequest) (*effective_model.ServiceableEffectiveRuleTab, *lcos_error.LCOSError)
	CheckEffectiveRule(ctx utils.LCOSContext, request *serviceable_area_rule.CheckEffectiveRuleRequest) *lcos_error.LCOSError
	ExportEffectiveRule(ctx utils.LCOSContext, request *serviceable_area_rule.ExportEffectiveRuleRequest) (serviceable_area_rule.ExportEffectiveRuleResponse, *lcos_error.LCOSError)

	GetProductIdList(ctx utils.LCOSContext) (*serviceable_area_rule.ListProductResponse, *lcos_error.LCOSError)
	GetLaneCodeList(ctx utils.LCOSContext, request *serviceable_area_rule.ListLaneCodeRequest) (*serviceable_area_rule.ListLaneCodeResponse, *lcos_error.LCOSError)
	GetFulfillmentModelList(ctx utils.LCOSContext, request *serviceable_area_rule.ListFulfillmentModelRequest) (*serviceable_area_rule.ListFulfillmentModelResponse, *lcos_error.LCOSError)
}

type ServiceableAreaRuleService struct {
	initializedRuleDAO initialized_rule.InitializedRuleDAO
	effectiveRuleDAO   effective_rule.EffectiveRuleDAO
}

/*
Initialized Rule Service
*/
func (s *ServiceableAreaRuleService) CreateServiceableInitializedRule(ctx utils.LCOSContext, request *serviceable_area_rule.CreateInitializedRuleRequest) (*initialized_model.ServiceableInitializedRuleTab, *lcos_error.LCOSError) {
	// strange logic from FE, both CB/XX region will send XX
	region := getRealRegion(ctx)
	rule, err := s.initializedRuleDAO.GetInitializedRuleByModelId(ctx, request.FulfillmentModel, region, constant.RuleActive)
	if err == nil && rule != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.CreateRuleErrorCode, fmt.Sprintf("model_id[%d] has a exist rule[%s]", request.FulfillmentModel, rule.RuleId))
	}
	rule, err = s.initializedRuleDAO.GetInitializedRuleByRuleName(ctx, request.RuleName, region)
	if err == nil && rule != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.CreateRuleErrorCode, fmt.Sprintf("rule_name[%s] has a exist rule[%s]", request.RuleName, rule.RuleId))
	}

	model := &initialized_model.ServiceableInitializedRuleTab{
		RuleName:   request.RuleName,
		RuleStatus: request.RuleStatus,
		Region:     region,
		ModelId:    request.FulfillmentModel,
		Range:      request.Range,
		Operator:   ctx.GetUserEmail(),
	}

	model.LaneConn = request.LaneConnection
	model.ServiceableRule = request.ServiceableRule
	now := int64(recorder.Now(ctx).Unix())
	model.CTime = now
	model.MTime = now
	if request.RuleStatus < constant.RuleDraft {
		// did not pass rule status, auto DRAFT status.
		model.RuleStatus = constant.RuleDraft
	}
	if request.RuleStatus == constant.RuleActive {
		model.EffectiveTime = now
	}
	ruleId, err := s.initializedRuleDAO.GenRuleId(ctx)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.CreateRuleErrorCode, fmt.Sprintf("gen rule id error:%v", err.Msg))
	}
	model.RuleId = ruleId
	ret, err := s.initializedRuleDAO.CreateInitializedRule(ctx, model)
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (s *ServiceableAreaRuleService) UpdateServiceableInitializedRule(ctx utils.LCOSContext, request *serviceable_area_rule.UpdateInitializedRuleRequest) (*initialized_model.ServiceableInitializedRuleTab, *lcos_error.LCOSError) {
	rule, err := s.initializedRuleDAO.GetInitializedRuleById(ctx, request.Id)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UpdateRuleErrorCode, fmt.Sprintf("find rule[%d] err:%v ", request.Id, err.Msg))
	}
	if (rule.RuleStatus == constant.RuleActive && request.RuleStatus == constant.RuleDraft) || (rule.RuleStatus == constant.RuleDisabled) {
		return nil, lcos_error.NewLCOSError(lcos_error.UpdateRuleErrorCode, fmt.Sprintf("invalid rule status, rule_id[%s]", rule.RuleId))
	}
	region := getRealRegion(ctx)
	if request.RuleStatus == constant.RuleActive {

		existRule, err := s.initializedRuleDAO.GetInitializedRuleByModelId(ctx, rule.ModelId, region, constant.RuleActive)
		if err == nil && rule != nil && existRule.ID != request.Id {
			return nil, lcos_error.NewLCOSError(lcos_error.UpdateRuleErrorCode, fmt.Sprintf("model_id[%d] already has an active rule[%s]", rule.ModelId, existRule.RuleId))
		}
	}

	rule.ServiceableRule = request.ServiceableRule
	now := int64(recorder.Now(ctx).Unix())
	rule.MTime = now
	rule.Operator = ctx.GetUserEmail()
	if request.RuleStatus == constant.RuleActive && rule.RuleStatus < constant.RuleActive {
		rule.EffectiveTime = now
	}
	rule.RuleStatus = request.RuleStatus
	ret, err := s.initializedRuleDAO.UpdateInitializedRule(ctx, rule)
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (s *ServiceableAreaRuleService) GetServiceableInitializedRuleList(ctx utils.LCOSContext, request *serviceable_area_rule.ListInitializedRuleRequest) (*common.NewPageModel, *lcos_error.LCOSError) {
	pageNo, Count := utils.GenPageAndCount(request.PageNo, request.PageSize)
	searchMap, _ := utils.Struct2map(request)
	region := getRealRegion(ctx)
	searchMap["region"] = region
	models, total, err := s.initializedRuleDAO.SearchInitializedRule(ctx, pageNo, Count, searchMap)
	if err != nil {
		return nil, err
	}
	modelIdList := []uint64{}
	for _, m := range models {
		modelIdList = append(modelIdList, m.ModelId)
	}
	modelInfoMap, err := lfs_service.BatchGetModelInfoMap(ctx, modelIdList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "request lfs err:%v", err.Msg)
	}
	if len(modelInfoMap) > 0 {
		for _, m := range models {
			if info, ok := modelInfoMap[m.ModelId]; ok {
				m.ModelName = info.ModelName
				m.ModelLink = info.ModelLink
			}
		}
	}
	return &common.NewPageModel{
		PageNO:   pageNo,
		Total:    total,
		PageSize: Count,
		List:     models,
	}, nil
}

func (s *ServiceableAreaRuleService) GetServiceableInitializedRule(ctx utils.LCOSContext, request *common_protocol.IDRequest) (*initialized_model.ServiceableInitializedRuleTab, *lcos_error.LCOSError) {
	rule, err := s.initializedRuleDAO.GetInitializedRuleById(ctx, request.ID)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, fmt.Sprintf("find rule[%d] err:%v ", request.ID, err.Msg))
	}
	modelIdList := []uint64{rule.ModelId}

	modelInfoMap, err := lfs_service.BatchGetModelInfoMap(ctx, modelIdList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "request lfs err:%v", err.Msg)
	}
	if info, ok := modelInfoMap[rule.ModelId]; ok {
		rule.ModelName = info.ModelName
		rule.ModelLink = info.ModelLink
	} else {
		logger.CtxLogErrorf(ctx, "get model name from lfs err, model_id:%d", rule.ModelId)
	}
	return rule, nil
}

func (s *ServiceableAreaRuleService) DeleteServiceableInitializedRule(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	rule, err := s.initializedRuleDAO.GetInitializedRuleById(ctx, request.ID)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, fmt.Sprintf("find rule[%d] err:%v ", request.ID, err.Msg))
	}
	if rule.RuleStatus == constant.RuleActive {
		return lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, fmt.Sprintf("Can't delete rule[%s] because status in Active.", rule.RuleId))
	}
	return s.initializedRuleDAO.DeleteInitializedRuleById(ctx, request.ID)
}

func (s *ServiceableAreaRuleService) DisableServiceableInitializedRule(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	rule, err := s.initializedRuleDAO.GetInitializedRuleById(ctx, request.ID)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, fmt.Sprintf("find rule[%d] err:%v ", request.ID, err.Msg))
	}
	rule.RuleStatus = constant.RuleDisabled
	_, err = s.initializedRuleDAO.UpdateInitializedRule(ctx, rule)
	return err
}

func (s *ServiceableAreaRuleService) CheckServiceableInitializedRuleExist(ctx utils.LCOSContext, request *serviceable_area_rule.CheckInitializedRuleExistRequest) (*serviceable_area_rule.CheckInitializedRuleExistResponse, *lcos_error.LCOSError) {
	if request.FormatIdList == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "invalid param: format id list is empty")
	}
	needCheckFormatList := make([]uint64, 0)
	skipFormatList := make([]uint64, 0)
	formatIdList := make([]int, 0)
	if config.GetMutableConf(ctx) != nil {
		formatIdList = config.GetCheckFormatIdList(ctx)
	}
	if len(formatIdList) > 0 {
		for _, formatId := range request.FormatIdList {
			if utils.ContainsInt(formatIdList, int(formatId)) {
				needCheckFormatList = append(needCheckFormatList, formatId)
			} else {
				skipFormatList = append(skipFormatList, formatId)
			}
		}
	} else {
		needCheckFormatList = append(needCheckFormatList, request.FormatIdList...)
	}
	retList := make([]*serviceable_area_rule.RuleStatus, 0)
	if len(needCheckFormatList) <= 0 {
		// skip all check
		for _, formatId := range request.FormatIdList {
			retList = append(retList, &serviceable_area_rule.RuleStatus{
				FormatId: formatId,
				Exist:    1,
			})
		}
		return &serviceable_area_rule.CheckInitializedRuleExistResponse{List: retList}, nil
	}

	// init those no need to check format
	for _, formatId := range skipFormatList {
		retList = append(retList, &serviceable_area_rule.RuleStatus{
			FormatId: formatId,
			Exist:    1,
		})
	}
	region := getRealRegion(ctx)

	// need check, begin to check initialized rule
	ruleMap, err := s.initializedRuleDAO.GetInitializedRuleMapByModelIds(ctx, needCheckFormatList, region)
	if err != nil {
		return nil, err
	}

	for _, formatId := range needCheckFormatList {
		if _, ok := ruleMap[formatId]; ok {
			retList = append(retList, &serviceable_area_rule.RuleStatus{
				FormatId: formatId,
				Exist:    1,
			})
		} else {
			retList = append(retList, &serviceable_area_rule.RuleStatus{
				FormatId: formatId,
				Exist:    0,
			})
		}
	}
	return &serviceable_area_rule.CheckInitializedRuleExistResponse{List: retList}, nil
}

func (s *ServiceableAreaRuleService) GetRuleIdList(ctx utils.LCOSContext) (*serviceable_area_rule.ListInitializedRuleIdResponse, *lcos_error.LCOSError) {
	ruleList, err := s.initializedRuleDAO.GetAllRuleId(ctx, getRealRegion(ctx))
	if err != nil {
		return nil, err
	}
	ret := []*serviceable_area_rule.RuleInfo{}
	for _, rule := range ruleList {
		ret = append(ret, &serviceable_area_rule.RuleInfo{
			RuleName: rule.RuleName,
			RuleId:   rule.RuleId,
		})
	}
	return &serviceable_area_rule.ListInitializedRuleIdResponse{List: ret}, nil
}

/*
Effective Rule Service
*/

func (s *ServiceableAreaRuleService) GetBatchUpdateRuleInfo(ctx utils.LCOSContext, request *serviceable_area_rule.GetRuleInfoRequest) (*effective_model.ServiceableEffectiveRuleTab, *lcos_error.LCOSError) {
	region := getRealRegion(ctx)
	initialRule, err := s.initializedRuleDAO.GetInitializedRuleByModelId(ctx, request.FulfillmentModel, region, constant.RuleActive)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, fmt.Sprintf("not found rule by model id:%d, err:%v", request.FulfillmentModel, err))
	}
	// construct temp effective rule
	effectiveRule := &effective_model.ServiceableEffectiveRuleTab{
		ProductId:       request.ProductId,
		ModelId:         request.FulfillmentModel,
		Range:           request.Range,
		LaneConn:        initialRule.LaneConn,
		ServiceableRule: initialRule.ServiceableRule,
	}

	effectiveRule.LaneCode = strings.Replace(strings.Trim(fmt.Sprint(request.LaneCodeList), "[]"), " ", ",", -1)

	return effectiveRule, nil
}

func (s *ServiceableAreaRuleService) UpdateServiceableEffectiveRule(ctx utils.LCOSContext, request *serviceable_area_rule.UpdateEffectiveRuleRequest) (*effective_model.ServiceableEffectiveRuleTab, *lcos_error.LCOSError) {
	rule, err := s.effectiveRuleDAO.GetEffectiveRuleById(ctx, request.Id)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UpdateRuleErrorCode, fmt.Sprintf("find rule[%d] err:%v ", request.Id, err))
	}

	rule.ServiceableRule = request.ServiceableRule
	rule.RuleStatus = constant.RuleModified
	now := int64(recorder.Now(ctx).Unix())
	rule.MTime = now
	ret, err := s.effectiveRuleDAO.UpdateEffectiveRule(ctx, rule)
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (s *ServiceableAreaRuleService) GetServiceableEffectiveRuleList(ctx utils.LCOSContext, request *serviceable_area_rule.ListEffectiveRuleRequest) (*common.NewPageModel, *lcos_error.LCOSError) {
	pageNo, Count := utils.GenPageAndCount(request.PageNo, request.PageSize)
	searchMap, _ := utils.Struct2map(request)
	region := getRealRegion(ctx)
	searchMap["region"] = region
	models, total, err := s.effectiveRuleDAO.SearchEffectiveRule(ctx, pageNo, Count, searchMap)
	if err != nil {
		return nil, err
	}
	modelIdList := []uint64{}
	for _, m := range models {
		modelIdList = append(modelIdList, m.ModelId)
	}
	modelInfoMap, err := lfs_service.BatchGetModelInfoMap(ctx, modelIdList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "request lfs err:%v", err.Msg)
	}
	if len(modelInfoMap) > 0 {
		for _, m := range models {
			if info, ok := modelInfoMap[m.ModelId]; ok {
				m.ModelName = info.ModelName
				m.ModelLink = info.ModelLink
			}
		}
	}
	return &common.NewPageModel{
		PageNO:   pageNo,
		PageSize: Count,
		Total:    total,
		List:     models,
	}, nil
}

func (s *ServiceableAreaRuleService) GetServiceableEffectiveRule(ctx utils.LCOSContext, request *common_protocol.IDRequest) (*effective_model.ServiceableEffectiveRuleTab, *lcos_error.LCOSError) {
	rule, err := s.effectiveRuleDAO.GetEffectiveRuleById(ctx, request.ID)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, fmt.Sprintf("get effective rule err:%v", err))
	}
	modelIdList := []uint64{rule.ModelId}

	modelInfoMap, err := lfs_service.BatchGetModelInfoMap(ctx, modelIdList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "request lfs err:%v", err.Msg)
	}
	if info, ok := modelInfoMap[rule.ModelId]; ok {
		rule.ModelName = info.ModelName
		rule.ModelLink = info.ModelLink
	} else {
		logger.CtxLogErrorf(ctx, "get model name from lfs err, model_id:%d", rule.ModelId)
	}
	return rule, nil
}

func (s *ServiceableAreaRuleService) BatchUpdateServiceableEffectiveRule(ctx utils.LCOSContext, request *serviceable_area_rule.BatchUpdateEffectiveRuleRequest) *lcos_error.LCOSError {
	if len(request.LaneCodeList) <= 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "batch update effective rule err:empty lanecode list")
	}
	rules, err := s.effectiveRuleDAO.GetEffectiveRuleByLaneCodes(ctx, request.LaneCodeList)
	if err != nil {
		return err
	}
	if len(rules) <= 0 {
		logger.CtxLogErrorf(ctx, "cannot get any rules by input lane_code list[%s], fulfillment_model:%d, product:%d", request.LaneCodeList, request.FulfillmentModelId, request.ProductId)
		return lcos_error.NewLCOSError(lcos_error.UpdateRuleErrorCode, "cannot get any rules by input lane_code list")
	}
	for _, r := range rules {
		now := int64(recorder.Now(ctx).Unix())
		r.MTime = now
		r.RuleStatus = constant.RuleModified
		r.ServiceableRule = request.ServiceableRule
	}
	_, err = s.effectiveRuleDAO.BatchUpdateEffectiveRule(ctx, rules)
	if err != nil {
		return err
	}
	return nil
}

func (s *ServiceableAreaRuleService) CheckEffectiveRule(ctx utils.LCOSContext, request *serviceable_area_rule.CheckEffectiveRuleRequest) *lcos_error.LCOSError {
	if len(request.LaneInfoList) <= 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("invalid input lanecode[%v]", request.LaneInfoList))
	}
	region := getRealRegion(ctx)
	var existList []*serviceable_area_rule.LaneInfo
	var pendingList []*serviceable_area_rule.LaneInfo
	var formatIdList []uint64
	laneInfoMap := map[uint64]bool{}
	var configList []int
	if config.GetMutableConf(ctx) != nil {
		configList = config.GetCheckFormatIdList(ctx)
	}
	for _, LaneInfo := range request.LaneInfoList {
		rule, err := s.effectiveRuleDAO.GetEffectiveRuleByLaneAndProd(ctx, LaneInfo.LaneCode, 0, 0, region)
		if rule == nil && region == "SG" {
			rule, err = s.effectiveRuleDAO.GetEffectiveRuleByLaneAndProd(ctx, LaneInfo.LaneCode, 0, 0, "CB")
		}
		if err != nil {
			return err
		}
		if rule != nil {
			existList = append(existList, LaneInfo)
			continue
		}
		// test config
		if len(configList) > 0 {
			if utils.ContainsInt(configList, int(LaneInfo.FormatId)) {
				pendingList = append(pendingList, LaneInfo)
				if _, ok := laneInfoMap[LaneInfo.FormatId]; !ok {
					laneInfoMap[LaneInfo.FormatId] = true
					formatIdList = append(formatIdList, LaneInfo.FormatId)
				}
			} else {
				existList = append(existList, LaneInfo)
			}
			continue
		}
		pendingList = append(pendingList, LaneInfo)
		if _, ok := laneInfoMap[LaneInfo.FormatId]; !ok {
			laneInfoMap[LaneInfo.FormatId] = true
			formatIdList = append(formatIdList, LaneInfo.FormatId)
		}
	}

	if len(pendingList) <= 0 {
		if len(existList) != len(request.LaneInfoList) {
			logger.CtxLogErrorf(ctx, "exist lanecode[%v] is not equal to input[%v]", existList, len(request.LaneInfoList))
		}
		return nil
	}

	resultMap, err := s.initializedRuleDAO.GetInitializedRuleMapByModelIds(ctx, formatIdList, region)
	cbFlag := false
	if len(resultMap) == 0 && region == "SG" {
		// 比较"怪异"的兼容逻辑：上游接受CB市场入参是都是SG，所以需要在查不到时用CB再查一次。
		// 当前可以确保两个市场没有重复模板。
		resultMap, err = s.initializedRuleDAO.GetInitializedRuleMapByModelIds(ctx, formatIdList, "CB")
		cbFlag = true
	}
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, fmt.Sprintf("Cannot find Initialized Serviceable Area Rule for FulfillmentModel(Template) , model id%v, err:%v", formatIdList, err))
	}
	if cbFlag {
		region = "CB"
	}
	if len(resultMap) <= 0 || len(resultMap) != len(formatIdList) {
		errList := []uint64{}
		for _, formatId := range formatIdList {
			if _, ok := resultMap[formatId]; !ok {
				errList = append(errList, formatId)
			}
		}
		return lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, fmt.Sprintf("Cannot find Initialized Serviceable Area Rule for FulfillmentModel(Template) , format_id%v", errList))
	}
	ruleIdList, err := s.effectiveRuleDAO.BatchGenRuleId(ctx, len(pendingList))
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.CreateRuleErrorCode, fmt.Sprintf("create effective rule fail, err:%s", err.Msg))
	}
	var effectiveRuleList []*effective_model.ServiceableEffectiveRuleTab
	for i, laneInfo := range pendingList {
		initialRule, ok := resultMap[laneInfo.FormatId]
		if !ok {
			return lcos_error.NewLCOSError(lcos_error.GetRuleErrorCode, fmt.Sprintf("Cannot find Initialized Serviceable Area Rule for FulfillmentModel(Template):%d", laneInfo.FormatId))
		}
		effectiveRule := &effective_model.ServiceableEffectiveRuleTab{
			ProductId:       request.ProductId,
			LaneCode:        laneInfo.LaneCode,
			ModelId:         laneInfo.FormatId,
			Range:           initialRule.Range,
			RuleStatus:      constant.RuleInitialized,
			LaneConn:        initialRule.LaneConn,
			ServiceableRule: initialRule.ServiceableRule,
			Region:          region,
			Operator:        "lfs-system",
		}
		now := recorder.Now(ctx).Unix()
		effectiveRule.CTime = now
		effectiveRule.MTime = now
		effectiveRule.RuleId = ruleIdList[i]
		effectiveRuleList = append(effectiveRuleList, effectiveRule)
	}

	if len(effectiveRuleList) <= 0 {
		return nil
	}

	_, err = s.effectiveRuleDAO.BatchCreateEffectiveRule(ctx, effectiveRuleList)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.CreateRuleErrorCode, fmt.Sprintf("create effective rule err:%v, product_id:%d", err, request.ProductId))
	}
	return nil
}

func (s *ServiceableAreaRuleService) ExportEffectiveRule(ctx utils.LCOSContext, request *serviceable_area_rule.ExportEffectiveRuleRequest) (serviceable_area_rule.ExportEffectiveRuleResponse, *lcos_error.LCOSError) {
	region := getRealRegion(ctx)
	laneCodeList := request.LaneCodeList
	reqLen := len(laneCodeList)
	maxBatchSize := 50
	exportEffectiveRuleResponse := serviceable_area_rule.ExportEffectiveRuleResponse{
		List: make([]serviceable_area_rule.ExportEffectiveRuleItem, 0),
	}

	if reqLen > maxBatchSize {
		return exportEffectiveRuleResponse, lcos_error.NewLCOSError(lcos_error.ExportLaneCodeErrorCode, fmt.Sprintf("export lane_code length is too long, the max size is:%d", maxBatchSize))
	}
	// step1: batch request the list_lane_info at lfs admin system
	laneListInfo, err := lfs_service.BatchListLaneInfo(ctx, laneCodeList)
	if err != nil {
		return exportEffectiveRuleResponse, lcos_error.NewLCOSError(lcos_error.ExportLaneCodeErrorCode, fmt.Sprintf("request /admin/api/list_lane_info error:%v", err))
	}
	modelIdList := make([]uint64, 0)
	// key: laneCode value:modelId
	laneCodeModelMap := make(map[string]uint64)
	// key: laneCode value:range
	laneCodeRangeMap := make(map[string]uint8)
	// key: laneCode value:ruleId
	laneCodeRuleIdMap := make(map[string]string)
	for _, laneList := range laneListInfo {
		modelIdList = append(modelIdList, laneList.FormatId)
		laneCodeModelMap[laneList.LaneCode] = laneList.FormatId
	}
	// step2: get rule_range
	initializedRuleMap, mapError := s.initializedRuleDAO.GetInitializedRuleMapByModelIds(ctx, modelIdList, region)
	if mapError != nil {
		return exportEffectiveRuleResponse, lcos_error.NewLCOSError(lcos_error.ExportLaneCodeErrorCode, fmt.Sprintf("get initialize rule error:%v", err))
	}
	for modelId, initializedRule := range initializedRuleMap {
		for _, laneList := range laneListInfo {
			if laneList.FormatId == modelId {
				laneCodeRangeMap[laneList.LaneCode] = initializedRule.Range
			}
		}
	}
	// step3: batchQueryLaneInfo
	lfsService := lfs_service.NewLFSService(ctx, region)
	if region == "CB" {
		lfsService = lfs_service.NewLFSService(ctx, "SG")
	}
	laneCodeInfoMap, lcosErr := lfsService.BatchQueryLaneInfo(ctx, laneCodeList)
	if lcosErr != nil {
		return exportEffectiveRuleResponse, lcos_error.NewLCOSError(lcos_error.ExportLaneCodeErrorCode, fmt.Sprintf("request lfs BatchQueryLaneInfo error:%v", err))
	}
	// step4: BatchGenRuleId
	ruleIdList, ruleErr := s.effectiveRuleDAO.BatchGenRuleId(ctx, len(laneListInfo))
	if ruleErr != nil {
		return exportEffectiveRuleResponse, lcos_error.NewLCOSError(lcos_error.ExportLaneCodeErrorCode, fmt.Sprintf("BatchGenRuleId error:%v", err))
	}
	ruleIndex := 0
	for _, laneList := range laneListInfo {
		if ruleIndex < len(ruleIdList) {
			laneCodeRuleIdMap[laneList.LaneCode] = ruleIdList[ruleIndex]
			ruleIndex = ruleIndex + 1
		}
	}
	effectiveRuleMap, effectiveRuleMapError := s.effectiveRuleDAO.GetEffectiveRuleMapByLaneCodes(ctx, laneCodeList)
	if effectiveRuleMapError != nil {
		return exportEffectiveRuleResponse, lcos_error.NewLCOSError(lcos_error.ExportLaneCodeErrorCode, fmt.Sprintf("get effective rule error:%v", err))
	}
	for laneCode, effectiveRule := range effectiveRuleMap {
		laneCodeRuleIdMap[laneCode] = effectiveRule.RuleId
	}
	// step5: convertLaneRuleToEffectiveRule
	effectiveRuleList := s.convertLaneRuleToEffectiveRule(region, laneCodeRuleIdMap, laneCodeInfoMap, laneCodeModelMap, laneCodeRangeMap)
	// step6：insert db
	insertEffectiveRuleList := make([]*effective_rule.ServiceableEffectiveRuleTab, 0)
	updateEffectiveRuleList := make([]*effective_rule.ServiceableEffectiveRuleTab, 0)
	for _, effectiveRule := range effectiveRuleList {
		if tmpEffectiveRule, ok := effectiveRuleMap[effectiveRule.LaneCode]; ok {
			effectiveRule.RuleStatus = constant.RuleModified
			effectiveRule.ID = tmpEffectiveRule.ID
			effectiveRule.MTime = recorder.Now(ctx).Unix()
			updateEffectiveRuleList = append(updateEffectiveRuleList, effectiveRule)
		} else {
			effectiveRule.CTime = recorder.Now(ctx).Unix()
			effectiveRule.MTime = recorder.Now(ctx).Unix()
			insertEffectiveRuleList = append(insertEffectiveRuleList, effectiveRule)
		}
	}
	if len(insertEffectiveRuleList) > 0 {
		createDataList, createError := s.effectiveRuleDAO.BatchCreateEffectiveRule(ctx, insertEffectiveRuleList)
		if createError != nil {
			return exportEffectiveRuleResponse, lcos_error.NewLCOSError(lcos_error.ExportLaneCodeErrorCode, fmt.Sprintf("create effective rule error:%v", err))
		}
		for _, createData := range createDataList {
			exportEffectiveRuleItem := serviceable_area_rule.ExportEffectiveRuleItem{
				LaneCode: createData.LaneCode,
				Status:   true,
			}
			exportEffectiveRuleResponse.List = append(exportEffectiveRuleResponse.List, exportEffectiveRuleItem)
		}
	}
	if len(updateEffectiveRuleList) > 0 {
		updateDataList, updateError := s.effectiveRuleDAO.BatchUpdateEffectiveRule(ctx, updateEffectiveRuleList)
		if updateError != nil {
			return exportEffectiveRuleResponse, lcos_error.NewLCOSError(lcos_error.ExportLaneCodeErrorCode, fmt.Sprintf("update effective rule error:%v", err))
		}
		for _, updateData := range updateDataList {
			exportEffectiveRuleItem := serviceable_area_rule.ExportEffectiveRuleItem{
				LaneCode: updateData.LaneCode,
				Status:   true,
			}
			exportEffectiveRuleResponse.List = append(exportEffectiveRuleResponse.List, exportEffectiveRuleItem)
		}
	}

	return exportEffectiveRuleResponse, nil
}

func (s *ServiceableAreaRuleService) convertLaneRuleToEffectiveRule(region string, laneCodeRuleIdMap map[string]string, laneInfo map[string]*lfs_service.LaneCodeStruct, laneCodeModelMap map[string]uint64, laneCodeRangeMap map[string]uint8) []*effective_rule.ServiceableEffectiveRuleTab {
	effectiveRuleList := make([]*effective_rule.ServiceableEffectiveRuleTab, 0, len(laneCodeRuleIdMap))
	for laneCode, info := range laneInfo {
		modelId := laneCodeModelMap[laneCode]
		ruleRange := laneCodeRangeMap[laneCode]
		ruleId := laneCodeRuleIdMap[laneCode]
		rule := &effective_rule.ServiceableEffectiveRuleTab{
			RuleId:     ruleId,
			ProductId:  0,
			LaneCode:   laneCode,
			ModelId:    modelId,
			Range:      ruleRange,
			Region:     region,
			RuleStatus: constant.RuleInitialized,
			Operator:   "lcos-export",
		}
		for i := 0; i < len(info.Composes); i++ {
			resource := info.Composes[i]
			moduleRule := info.ModelRules[i]
			rule.LaneConn = append(rule.LaneConn, &dbhelper.LaneConnection{
				Sequence: uint32(resource.Sequence),
				Type:     uint32(resource.ResourceType),
				SubType:  uint32(resource.SubType),
			})
			if resource.ResourceType == constant.LaneComposeLine {
				saRule := &dbhelper.SARule{
					Sequence:             uint32(resource.Sequence),
					CheckCollectAddress:  constant.AddrCheckDisabled,
					CheckDeliveryAddress: constant.AddrCheckDisabled,
				}
				if resource.IsLastMile() || resource.IsFirstLeg() || resource.IsFirstMile() {
					saRule.CheckCollectAddress = constant.AddrCheckEnabled
					saRule.CheckDeliveryAddress = constant.AddrCheckEnabled
				}
				if utils.ContainsUint32(constant.CheckSenderLps, uint32(resource.SubType)) {
					saRule.CheckCollectAddress = constant.AddrCheckLpsScene
				}
				if utils.ContainsUint32(constant.CheckReceiverLps, uint32(resource.SubType)) {
					saRule.CheckDeliveryAddress = constant.AddrCheckLpsScene
				}
				if utils.ContainsUint32(constant.CheckSenderFalse, uint32(resource.SubType)) {
					saRule.CheckCollectAddress = constant.AddrCheckDisabled
				}
				if utils.ContainsUint32(constant.CheckReceiverFalse, uint32(resource.SubType)) {
					saRule.CheckDeliveryAddress = constant.AddrCheckDisabled
				}
				if saRule.CheckCollectAddress != constant.AddrCheckDisabled {
					saRule.CheckCollectAddressType = constant.AddrTypeUser
				}
				if saRule.CheckDeliveryAddress != constant.AddrCheckDisabled {
					saRule.CheckDeliveryAddressType = constant.AddrTypeUser
				}
				rule.ServiceableRule = append(rule.ServiceableRule, saRule)
			}
			if resource.ResourceType == constant.LaneComposeSite {
				saRule := &dbhelper.SARule{
					Sequence:   uint32(resource.Sequence),
					ApFiltrate: constant.ActualPointFiltrateOff,
				}
				if moduleRule.IsSortingAsPreDeliver() || moduleRule.IsSortingAsNextPickup() {
					saRule.ApFiltrate = constant.ActualPointFiltrateOn
				}
				if saRule.ApFiltrate == constant.ActualPointFiltrateOn {
					if moduleRule.SiteSortingDeliverAddrFlag == moduleRule.SiteSortingPickupAddrFlag {
						saRule.FiltrateMode = constant.SinglePointMode
						if moduleRule.SiteSortingDeliverAddrFlag == 2 {
							saRule.PointFiltrate = constant.PointAddrSeller
						}
						if moduleRule.SiteSortingDeliverAddrFlag == 1 {
							saRule.PointFiltrate = constant.PointAddrBuyer
						}
					} else {
						saRule.FiltrateMode = constant.InOutMode
						saRule.OutboundRule = int32(constant.PointAddrBuyer)
						saRule.InboundRule = int32(constant.PointAddrSeller)
					}
				}
				rule.ServiceableRule = append(rule.ServiceableRule, saRule)
			}
		}
		for i := 0; i < len(info.Composes); i++ {
			if info.Composes[i].ResourceType != constant.LaneComposeSite {
				continue
			}
			if i-1 >= 0 && info.ModelRules[i].IsPreResourceSACheck() {
				rule.ServiceableRule[i-1].CheckDeliveryAddressType = constant.AddrTypeActualPoint
				rule.ServiceableRule[i-1].CheckDeliveryAddress = constant.AddrCheckEnabled
			}
			if i+1 < len(info.Composes) && info.ModelRules[i].IsNextResourceSACheck() {
				rule.ServiceableRule[i+1].CheckCollectAddressType = constant.AddrTypeActualPoint
				rule.ServiceableRule[i+1].CheckCollectAddress = constant.AddrCheckEnabled
			}
		}

		effectiveRuleList = append(effectiveRuleList, rule)
	}
	return effectiveRuleList
}

func (s *ServiceableAreaRuleService) GetProductIdList(ctx utils.LCOSContext) (*serviceable_area_rule.ListProductResponse, *lcos_error.LCOSError) {
	region := getRealRegion(ctx)
	productIds, err := s.effectiveRuleDAO.GetAllProduct(ctx, region)
	if err != nil {
		return nil, err
	}
	return &serviceable_area_rule.ListProductResponse{List: productIds}, nil
}

func (s *ServiceableAreaRuleService) GetLaneCodeList(ctx utils.LCOSContext, request *serviceable_area_rule.ListLaneCodeRequest) (*serviceable_area_rule.ListLaneCodeResponse, *lcos_error.LCOSError) {
	region := getRealRegion(ctx)
	laneCodes, err := s.effectiveRuleDAO.GetLaneCodeList(ctx, request.FulfillmentModel, request.ProductId, request.Range, region)
	if err != nil {
		return nil, err
	}
	return &serviceable_area_rule.ListLaneCodeResponse{List: laneCodes}, nil
}

func (s *ServiceableAreaRuleService) GetFulfillmentModelList(ctx utils.LCOSContext, request *serviceable_area_rule.ListFulfillmentModelRequest) (*serviceable_area_rule.ListFulfillmentModelResponse, *lcos_error.LCOSError) {
	modelInfoList := []*serviceable_area_rule.ModelInfo{}
	region := getRealRegion(ctx)
	if request.RuleType == constant.InitializedRule {
		rules, err := s.initializedRuleDAO.GetAllModel(ctx, region)
		if err != nil {
			return nil, err
		}
		modelIdList := []uint64{}
		for _, r := range rules {
			modelIdList = append(modelIdList, r.ModelId)
		}
		modelInfoMap, err := lfs_service.BatchGetModelInfoMap(ctx, modelIdList)
		if err != nil {
			logger.CtxLogErrorf(ctx, "request lfs err:%v", err.Msg)
		}
		for _, rule := range rules {
			if info, ok := modelInfoMap[rule.ModelId]; ok {
				rule.ModelName = info.ModelName
			}
			modelInfoList = append(modelInfoList, &serviceable_area_rule.ModelInfo{
				ModelName:        rule.ModelName,
				FulfillmentModel: rule.ModelId,
			})
		}
	}
	if request.RuleType == constant.EffectiveRule {
		rules, err := s.effectiveRuleDAO.GetAllModel(ctx, region)
		if err != nil {
			return nil, err
		}
		modelIdList := []uint64{}
		for _, r := range rules {
			modelIdList = append(modelIdList, r.ModelId)
		}
		modelInfoMap, err := lfs_service.BatchGetModelInfoMap(ctx, modelIdList)
		if err != nil {
			logger.CtxLogErrorf(ctx, "request lfs err:%v", err.Msg)
		}
		for _, rule := range rules {
			if info, ok := modelInfoMap[rule.ModelId]; ok {
				rule.ModelName = info.ModelName
			}
			modelInfoList = append(modelInfoList, &serviceable_area_rule.ModelInfo{
				ModelName:        rule.ModelName,
				FulfillmentModel: rule.ModelId,
			})
		}
	}
	return &serviceable_area_rule.ListFulfillmentModelResponse{List: modelInfoList}, nil
}

func NewServiceableAreaRuleService(initializedRule initialized_rule.InitializedRuleDAO, effectiveRule effective_rule.EffectiveRuleDAO) *ServiceableAreaRuleService {
	return &ServiceableAreaRuleService{
		initializedRuleDAO: initializedRule,
		effectiveRuleDAO:   effectiveRule,
	}
}

func getRealRegion(ctx utils.LCOSContext) string {
	region := ctx.GetCountry()
	if region == "XX" {
		return "CB"
	}
	return region
}
