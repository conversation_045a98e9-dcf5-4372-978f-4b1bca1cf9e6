package serviceable_core_logic

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

var (
	support    uint32 = 1
	notSupport uint32 = 0
)

type FourLevelAddress struct {
	StateList    []string `form:"state_list" json:"state_list"`
	CityList     []string `form:"city_list" json:"city_list"`
	DistrictList []string `form:"district_list" json:"district_list"`
	StreetList   []string `form:"street_list" json:"street_list"`
}

type PickupServiceable struct {
	CanPickup    uint32 `form:"can_pickup" json:"can_pickup"`
	CanCodPickup uint32 `form:"can_cod_pickup" json:"can_cod_pickup"`
}

type DeliverServiceable struct {
	CanDeliver    uint32 `form:"can_deliver" json:"can_deliver"`
	CanCodDeliver uint32 `form:"can_cod_deliver" json:"can_cod_deliver"`
	CanTradeIn    uint32 `form:"can_trade_in" json:"can_trade_in"`
}

type OriginServiceableAbility interface {
	GetCanPickup() uint8
	GetCanCodPickup() uint8
}

type DestServiceableAbility interface {
	GetCanDeliver() uint8
	GetCanCodDeliver() uint8
	GetCanTradeIn() uint8
}

func GenAllFailedPickupServiceable() *PickupServiceable {
	return &PickupServiceable{
		CanPickup:    notSupport,
		CanCodPickup: notSupport,
	}
}

func GenAllSuccessPickupServiceable() *PickupServiceable {
	return &PickupServiceable{
		CanPickup:    support,
		CanCodPickup: support,
	}
}

func GenAllFailedDeliverServiceable() *DeliverServiceable {
	return &DeliverServiceable{
		CanDeliver:    notSupport,
		CanCodDeliver: notSupport,
		CanTradeIn:    notSupport,
	}
}

func GenAllSuccessDeliverServiceable() *DeliverServiceable {
	return &DeliverServiceable{
		CanDeliver:    support,
		CanCodDeliver: support,
		CanTradeIn:    support,
	}
}

func GenAllSuccessServiceable() *pb.ServiceableAreaInfo {
	return &pb.ServiceableAreaInfo{
		CanPickup:     &support,
		CanCodPickup:  &support,
		CanDeliver:    &support,
		CanCodDeliver: &support,
		CanTradeIn:    &support,
	}
}

func GenAllFailedServiceable() *pb.ServiceableAreaInfo {
	return &pb.ServiceableAreaInfo{
		CanPickup:     &notSupport,
		CanCodPickup:  &notSupport,
		CanDeliver:    &notSupport,
		CanCodDeliver: &notSupport,
		CanTradeIn:    &notSupport,
	}
}

type CheckFailErrcode struct {
	canNotPickup     int32
	canNotCodPickup  int32
	canNotDeliver    int32
	canNotCodDeliver int32
}

func (c *CheckFailErrcode) set(ok uint32, serviceableType uint8, attr string, locationCode, postalCode int32) {
	if ok == 0 {
		toSetValue := c.getAttrAddr(attr)
		if toSetValue == nil {
			return
		}
		if serviceableType == constant.LOCATION {
			*toSetValue = locationCode
		} else if serviceableType == constant.POSTCODE {
			*toSetValue = postalCode
		}
	}
}

func (c *CheckFailErrcode) GetErrorcode(attr string, defaultErrcode int32) int32 {
	v := c.getAttrAddr(attr)
	if v != nil && *v != 0 {
		return *v
	}
	return defaultErrcode
}

func (c *CheckFailErrcode) getAttrAddr(attr string) *int32 {
	switch attr {
	case "pickup":
		return &c.canNotPickup
	case "codPickup":
		return &c.canNotCodPickup
	case "deliver":
		return &c.canNotDeliver
	case "codDeliver":
		return &c.canNotCodDeliver
	default:
	}
	return nil
}

type EFenceResult struct {
	Support uint32
	ZoneId  string
	Report  string
}
