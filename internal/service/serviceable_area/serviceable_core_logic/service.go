package serviceable_core_logic

import (
	"context"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"

	"github.com/go-faker/faker/v4/pkg/slice"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/geopolygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	commonConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/e_fence"
	eFenceConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	internalConfig "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	Logger "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	collectDeliverGroupRef "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	collectDeliver "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic/shop_serviceable_checker"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type ServiceableCheckerServiceInterface interface {
	// Deprecated 通过场景配置校验买卖家
	GetServiceable(ctx utils.LCOSContext, request *pb.GetLineServiceableInfoRequest) (*pb.ServiceableAreaInfo, *lcos_error.LCOSError)
	// 直接从入参获取校验买卖家标识
	GetServiceableWithCheckFlag(ctx utils.LCOSContext, request *pb.GetLineServiceableInfoRequest2) (*pb.ServiceableAreaInfo, *CheckFailErrcode, *lcos_error.LCOSError)
	// 批量接口：从入参获取校验买卖家标识
	BatchGetServiceable(ctx utils.LCOSContext, request *pb.BatchGetLineServiceableInfoRequest2) (*pb.BatchGetLineServiceableInfoResponse2, *lcos_error.LCOSError)
	// Deprecated 使用的场景配置的Request
	CheckServiceable(ctx utils.LCOSContext, request *pb.CheckLineServiceableAreaRequest) (*pb.CheckLineServiceableAreaResponse, *lcos_error.LCOSError)
	// 直接从入参获取校验买卖家标识
	CheckServiceable2(ctx utils.LCOSContext, request *pb.CheckLineServiceableAreaRequest2) (*pb.CheckLineServiceableAreaResponse, *lcos_error.LCOSError)
	// 批量校验服务范围
	BatchCheckServiceable(ctx utils.LCOSContext, request *pb.BatchCheckLineServiceableAreaRequest) (*pb.BatchCheckLineServiceableAreaResponse, *lcos_error.LCOSError)
	// 多批量校验服务范围
	MultipleBatchCheckLineServiceableArea(ctx utils.LCOSContext, request *pb.MultipleBatchCheckLineServiceableAreaRequest) (*pb.MultipleBatchCheckLineServiceableAreaResponse, *lcos_error.LCOSError)
	// 多批量校验服务范围替代 MultipleBatchCheckLineServiceableArea
	BatchCheckLineServiceableArea2(ctx utils.LCOSContext, request *pb.BatchCheckLineServiceableAreaRequest2) (*pb.BatchCheckLineServiceableAreaResponse2, *lcos_error.LCOSError)
	CheckFmServiceableArea(ctx utils.LCOSContext, request *pb.CheckFmServiceableAreaRequest) (*pb.CheckFmServiceableAreaResponse, *lcos_error.LCOSError)
	// 通过collect deliver type获取collectDeliverGroupId
	GetCollectDeliverGroupIdByCollectDeliverType(ctx utils.LCOSContext, lineId string, collectType *uint32, deliverType *uint32) (*string, *lcos_error.LCOSError)
	// 通过laneCode list获取生效规则
	GetEffectiveRuleByLaneCodes(ctx utils.LCOSContext, laneCodeList []string) (map[string]*effective_rule.ServiceableEffectiveRuleTab, *lcos_error.LCOSError)
	GetLaneServiceableRuleList(ctx utils.LCOSContext, laneCodeList []string) ([]*pb.ServiceableRule, *lcos_error.LCOSError)
	// 单个获取 用于内部校验
	GetLaneServiceableRule(ctx utils.LCOSContext, laneCode string) (*pb.ServiceableRule, *lcos_error.LCOSError)

	CheckShopServiceable(ctx utils.LCOSContext, request *pb.CheckShopServiceableReq) (*pb.CheckShopServiceableResp, *lcos_error.LCOSError)
	SearchProductServiceableZone(ctx utils.LCOSContext, request *pb.SearchProductServiceableZoneAddress) (*pb.SearchProductServiceableZoneResult, *lcos_error.LCOSError)

	BatchGetProductServiceableRouteCode(ctx utils.LCOSContext, reqList []*pb.GetProductServiceableRouteCodeReq) ([]*pb.GetProductServiceableRouteCodeResp, *lcos_error.LCOSError)
}

type ServiceableCheckerService struct {
	shopSaChecker shop_serviceable_checker.ShopServiceableAreaChecker

	basicConfDao               basic_conf.LineBasicServiceableConfDAO
	basicLocationDao           basic_location.LineBasicServiceableLocationDAO
	basicPostcodeDao           basic_postcode.LineBasicServiceablePostcodeDAO
	scenarioConf               scenario_conf.LineCommonServiceableScenarioConfDAO
	operationConfDao           operation_conf.LogisticLineOperationServiceableConfTabDAO
	operationLocationDao       operation_location.LogisticLineOperationServiceableLocationTabDAO
	operationPostcodeDao       operation_postcode.LogisticLineOperationServiceablePostcodeTabDAO
	routeDao                   route.LogisticLineServiceableRouteTabDAO
	opsRouteDao                operation_route.LineOperationRouteTabDAO
	areaLocationDao            area_location_ref.LogisticLineServiceableAreaLocationRefTabDAO
	collectDeliverGroupDao     collectDeliver.LineCollectDeliverGroupConfDao
	collectDeliverGroupRefDao  collectDeliverGroupRef.LineBasicServiceableGroupRefDao
	cepRangeDao                cep_range.LineServiceableCepRangeDaoInterface
	effectiveRuleDao           effective_rule.EffectiveRuleDAO
	eFenceLineToggleDao        line_toggle.EFenceLineToggleDao
	eFenceLocationWhitelistDao location_whitelist.EFenceLocationWhitelistDao
	eFenceMeshDao              mesh.EFenceMeshDao
	eFencePolygonDao           polygon.EFencePolygonDao
	predefinedRouteDao         predefined_route_model.LogisticLinePredefinedRouteDao
}

func NewServiceableCheckerService(basicConfDao basic_conf.LineBasicServiceableConfDAO, basicLocationDao basic_location.LineBasicServiceableLocationDAO, basicPostcodeDao basic_postcode.LineBasicServiceablePostcodeDAO, scenarioConf scenario_conf.LineCommonServiceableScenarioConfDAO,
	operationConfDao operation_conf.LogisticLineOperationServiceableConfTabDAO, operationLocationDao operation_location.LogisticLineOperationServiceableLocationTabDAO, operationPostcodeDao operation_postcode.LogisticLineOperationServiceablePostcodeTabDAO, routeDao route.LogisticLineServiceableRouteTabDAO,
	areaLocationDao area_location_ref.LogisticLineServiceableAreaLocationRefTabDAO, collectDeliverGroupDao collectDeliver.LineCollectDeliverGroupConfDao, collectDeliverGroupRefDao collectDeliverGroupRef.LineBasicServiceableGroupRefDao, cepRangeDao cep_range.LineServiceableCepRangeDaoInterface,
	opsRouteDao operation_route.LineOperationRouteTabDAO, effectiveRuleDao effective_rule.EffectiveRuleDAO, eFenceLineToggleDao line_toggle.EFenceLineToggleDao, eFenceLocationWhitelistDao location_whitelist.EFenceLocationWhitelistDao, eFenceMeshDao mesh.EFenceMeshDao,
	eFencePolygonDao polygon.EFencePolygonDao, predefinedRouteDao predefined_route_model.LogisticLinePredefinedRouteDao, shopSaChecker shop_serviceable_checker.ShopServiceableAreaChecker) *ServiceableCheckerService {
	return &ServiceableCheckerService{
		basicConfDao:               basicConfDao,
		basicLocationDao:           basicLocationDao,
		basicPostcodeDao:           basicPostcodeDao,
		scenarioConf:               scenarioConf,
		operationConfDao:           operationConfDao,
		operationLocationDao:       operationLocationDao,
		operationPostcodeDao:       operationPostcodeDao,
		routeDao:                   routeDao,
		opsRouteDao:                opsRouteDao,
		areaLocationDao:            areaLocationDao,
		collectDeliverGroupDao:     collectDeliverGroupDao,
		collectDeliverGroupRefDao:  collectDeliverGroupRefDao,
		cepRangeDao:                cepRangeDao,
		effectiveRuleDao:           effectiveRuleDao,
		eFenceLineToggleDao:        eFenceLineToggleDao,
		eFenceLocationWhitelistDao: eFenceLocationWhitelistDao,
		eFenceMeshDao:              eFenceMeshDao,
		eFencePolygonDao:           eFencePolygonDao,
		predefinedRouteDao:         predefinedRouteDao,

		shopSaChecker: shopSaChecker,
	}
}

func (checker *ServiceableCheckerService) GetLaneServiceableRule(ctx utils.LCOSContext, laneCode string) (*pb.ServiceableRule, *lcos_error.LCOSError) {
	rules, err := checker.effectiveRuleDao.GetEffectiveRuleMapByLaneCodesUsingCache(ctx, []string{laneCode})
	if err != nil {
		return nil, err
	}

	if len(rules) <= 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLaneServiceableRuleErrorCode, "can't find serviceable rule for every single lane")
	}
	// lcos生效规则转换为lfs服务范围规则数据结构
	lfsRules := convertEffectiveRuleToLaneRule(rules, true)
	if len(lfsRules) <= 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLaneServiceableRuleErrorCode, fmt.Sprintf("can't find serviceable rule for lane:%s", laneCode))
	}

	return lfsRules[0], nil
}

func (checker *ServiceableCheckerService) GetLaneServiceableRuleList(ctx utils.LCOSContext, laneCodeList []string) ([]*pb.ServiceableRule, *lcos_error.LCOSError) {
	rules, err := checker.effectiveRuleDao.GetEffectiveRuleMapByLaneCodesUsingCache(ctx, laneCodeList)
	if err != nil {
		return nil, err
	}
	// do not produce error when part of lane's effective rules miss.
	// just return what we get from database

	//if len(rules) != len(laneCodeList) {
	//	return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLaneServiceableRuleErrorCode, "can't find serviceable rule for every single lane")
	//}
	return convertEffectiveRuleToLaneRule(rules, false), nil
}

// SPLN-23735 配置化改造
func (checker *ServiceableCheckerService) GetEffectiveRuleByLaneCodes(ctx utils.LCOSContext, laneCodeList []string) (map[string]*effective_rule.ServiceableEffectiveRuleTab, *lcos_error.LCOSError) {
	return checker.effectiveRuleDao.GetEffectiveRuleMapByLaneCodesUsingCache(ctx, laneCodeList)
}

// SPLLS-3980 增加postcode中存在非数字内容的特殊处理逻辑
func validatePostalCode(postalCode string) (string, *lcos_error.LCOSError) {
	// 使用正则替换掉所有非数字内容的邮编
	//pattern, err := regexp.Compile(`[^0-9]+`)
	expr := "[^0-9]+"
	pattern, err := utils.GetRegexp(expr)
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "reg pattern is not valid")
	}
	returnedPostalCode := pattern.ReplaceAllString(postalCode, "")
	// 检查经过正则替换后，邮编是否为空字符串，是的话则报错
	if returnedPostalCode == "" {
		return "", lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "postal code is not valid")
	}
	return returnedPostalCode, nil
}

// Deprecated
// request包含场景配置
func (checker *ServiceableCheckerService) GetServiceable(ctx utils.LCOSContext, request *pb.GetLineServiceableInfoRequest) (*pb.ServiceableAreaInfo, *lcos_error.LCOSError) {
	basicConf, err := checker.basicConfDao.GetBasicServiceableConfModelByLineIdUseCache(ctx, *request.BaseInfo.LineId)
	if err != nil {
		return nil, err
	}
	if basicConf == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicConfErrorCode)
	}

	// 获取运营层配置，注意：有可能运营层配置为空
	operationConf, err := checker.operationConfDao.GetLogisticLineOperationServiceableConfTabByLineIDUseCache(ctx, *request.BaseInfo.LineId)
	if err != nil {
		return nil, err
	}

	// 检查线的揽收能力是否支持
	if request.BaseInfo.CollectType != nil { // collect_type不为空，校验collect ability
		if err := serviceable_util.CheckLineCollectAbility(basicConf.CollectDeliverAbility, *request.BaseInfo.CollectType); err != nil {
			logger.LogInfof("line collect ability check failed, detail: %s", err.Msg)
			return nil, err
		}
	}
	if request.BaseInfo.DeliverType != nil { // deliver_type不为空，校验deliver ability
		if err := serviceable_util.CheckLineDeliverAbility(basicConf.CollectDeliverAbility, *request.BaseInfo.DeliverType); err != nil {
			logger.LogInfof("line deliver ability check failed, detail: %s", err.Msg)
			return nil, err
		}
	}
	// 校验完揽派能力后填充揽派类型
	serviceable_util.FillCollectDeliverType(basicConf, request)

	// 检查线的揽派距离是否支持
	if err := serviceable_util.CheckLineDistance(basicConf, request.PickupInfo, request.DeliverInfo); err != nil {
		logger.LogInfof("line distance check failed, detail: %s", err.Msg)
		return nil, err
	}

	// 如果入参选择都不校验，直接返回四个can都是true
	if uint8(*request.BaseInfo.IsCheckOperation) == constant.DISABLED && uint8(*request.BaseInfo.IsCheckBasic) == constant.DISABLED {
		return GenAllSuccessServiceable(), nil
	}

	// 只要运营层或基础层有一个配置的是enable，就需要校验collectType, deliverType能够唯一命中collectDeliverGroupId
	var defaultCollectDeliverGroupId = ""
	collectDeliverGroupId := &defaultCollectDeliverGroupId
	if basicConf.IsCheckServiceable == constant.ENABLED || (operationConf != nil && operationConf.IsCheckServiceable == constant.ENABLED) {
		collectType := request.BaseInfo.CollectType
		deliverType := request.BaseInfo.DeliverType
		// to-wms, to-3pl, to-branch 需要转成 to-site
		if *request.BaseInfo.DeliverType == uint32(constant.TOWMS) || *request.BaseInfo.DeliverType == uint32(constant.TO3PL) || *request.BaseInfo.DeliverType == uint32(constant.TOBRANCH) {
			toSite := uint32(constant.TOSITE)
			deliverType = &toSite
			request.BaseInfo.DeliverType = &toSite // 更新request里面的deliver_type为to-site
		}
		collectDeliverGroupId, err = checker.GetCollectDeliverGroupIdByCollectDeliverType(ctx, basicConf.LineId, collectType, deliverType)

		if err != nil {
			if err.RetCode == lcos_error.NotFoudCollectDeliverGroupIdErrorCode || err.RetCode == lcos_error.NotHitCollectDeliverGroupIdErrorCode {
				defaultPickupEnable := uint32(basicConf.DefaultPickupEnabled)
				defaultCodPickupEnable := uint32(basicConf.DefaultCodPickupEnabled)
				defaultDeliverEnable := uint32(basicConf.DefaultDeliverEnabled)
				defaultCodDeliverEnable := uint32(basicConf.DefaultCodDeliverEnabled)
				return &pb.ServiceableAreaInfo{
					CanPickup:     &defaultPickupEnable,
					CanCodPickup:  &defaultCodPickupEnable,
					CanDeliver:    &defaultDeliverEnable,
					CanCodDeliver: &defaultCodDeliverEnable,
				}, nil
			}
		}
	}

	// 获取场景配置
	var scenarioConf *scenario_conf.LineCommonServiceableScenarioConfTab
	if *request.BaseInfo.Scenario != uint32(constant.BUILD) {
		scenarioConf, err = checker.scenarioConf.GetScenarioConfModelByLineIdAndScenarioUseCache(ctx, *request.BaseInfo.LineId, uint8(*request.BaseInfo.Scenario))
		if err != nil {
			return nil, err
		}
		if scenarioConf == nil {
			return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundScenarioConfErrorCode)
		}
	}

	// 初始化结果集
	resultServiceable := GenAllSuccessServiceable()

	// 根据场景获取是否要校验买家/卖家地址
	isCheckSeller, isCheckBuyer := serviceable_util.GetCheckAddressType(request.PickupInfo, request.DeliverInfo, *request.BaseInfo.CollectType, *request.BaseInfo.Scenario, scenarioConf)

	// 校验运营层服务范围
	if uint8(*request.BaseInfo.IsCheckOperation) == constant.ENABLED {
		if operationConf != nil && operationConf.IsCheckServiceable == constant.ENABLED {
			if isCheckSeller {
				pickupServiceable, err := checker.getOriginServiceable(ctx, true, request, basicConf, scenarioConf, *collectDeliverGroupId)
				if err != nil {
					return nil, err
				}
				resultServiceable.CanPickup = &pickupServiceable.CanPickup
				resultServiceable.CanCodPickup = &pickupServiceable.CanCodPickup
			}
			if isCheckBuyer {
				deliverServiceable, err := checker.getDestServiceable(ctx, true, request, basicConf, scenarioConf, *collectDeliverGroupId)
				if err != nil {
					return nil, err
				}
				resultServiceable.CanDeliver = &deliverServiceable.CanDeliver
				resultServiceable.CanCodDeliver = &deliverServiceable.CanCodDeliver
			}
		}
	}

	// 校验基础层服务范围
	if uint8(*request.BaseInfo.IsCheckBasic) == constant.ENABLED && basicConf.IsCheckServiceable == constant.ENABLED {
		if isCheckSeller {
			if *resultServiceable.CanPickup == support || *resultServiceable.CanCodPickup == support { // 运营层白名单检验通过，继续校验基础层
				pickupServiceable, err := checker.getOriginServiceable(ctx, false, request, basicConf, scenarioConf, *collectDeliverGroupId)
				if err != nil {
					return nil, err
				}
				// 取运营层结果和基础层结果的取并集
				canPickup := *resultServiceable.CanPickup & pickupServiceable.CanPickup
				canCodPickup := *resultServiceable.CanCodPickup & pickupServiceable.CanCodPickup
				resultServiceable.CanPickup = &canPickup
				resultServiceable.CanCodPickup = &canCodPickup
			}

		}
		if isCheckBuyer {
			if *resultServiceable.CanDeliver == support || *resultServiceable.CanCodDeliver == support { // 运营层白名单校验通过，继续校验基础层
				deliverServiceable, err := checker.getDestServiceable(ctx, false, request, basicConf, scenarioConf, *collectDeliverGroupId)
				if err != nil {
					return nil, err
				}
				// 取运营层结果和基础层结果的取并集
				canDeliver := *resultServiceable.CanDeliver & deliverServiceable.CanDeliver
				canCodDeliver := *resultServiceable.CanCodDeliver & deliverServiceable.CanCodDeliver
				resultServiceable.CanDeliver = &canDeliver
				resultServiceable.CanCodDeliver = &canCodDeliver
			}
		}
		if request.BaseInfo.SkipZoneRoute == nil || *request.BaseInfo.SkipZoneRoute == uint32(constant.DISABLED) { // 没传SkipZoneRoute，或者 SkipZoneRoute=0
			// 校验route
			if basicConf.IsCheckRoute == constant.ENABLED {
				if request.PickupInfo == nil || request.DeliverInfo == nil {
					return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check route,but pickup_info or deliver_info is nil")
				}
				isInLineRoute, err := checker.checkLocationServiceableRoute(ctx, *request.BaseInfo.LineId, *collectDeliverGroupId, request.PickupInfo, request.DeliverInfo, basicConf.RouteMode)
				if err != nil {
					return nil, err
				}
				if !isInLineRoute {
					logger.LogInfof("route check failed, location not in line route")
					return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.RouteCheckErrorCode)
				}
			}
		}
	}

	return resultServiceable, nil
}

// GetServiceableWithCheckFlag 直接从入参校验买卖家
func (checker *ServiceableCheckerService) GetServiceableWithCheckFlag(ctx utils.LCOSContext, request *pb.GetLineServiceableInfoRequest2) (resultServiceable *pb.ServiceableAreaInfo, checkFailErrCode *CheckFailErrcode, err *lcos_error.LCOSError) {
	var (
		errorCode    = "0"
		errorMessage = ""
	)
	checkFailErrCode = &CheckFailErrcode{} // 新增一个返回值明确check失败原因
	// 上报线服务范围底层的校验结果
	defer func() {
		// 上报服务范围开关
		if !internalConfig.GetServiceAbleReport(ctx) {
			return
		}
		if err != nil {
			errorCode = fmt.Sprintf("%d", err.RetCode)
			errorMessage = fmt.Sprintf("%d-%s", err.RetCode, err.Msg)
		}
		_ = metrics.CounterIncr(constant.MetricServiceAbleByLine, map[string]string{
			"func":          constant.GetServiceableWithCheckFlag,
			"line_id":       request.GetBaseInfo().GetLineId(),
			"error_code":    errorCode,
			"error_message": errorMessage,
		})
	}()

	// location level参数校验
	err = serviceable_util.CheckLocationLevel(request.BaseInfo.CheckSender, request.BaseInfo.CheckReceiver, request.BaseInfo.SenderCheckLevel, request.BaseInfo.ReceiverCheckLevel)
	if err != nil {
		return nil, nil, err
	}
	basicConf, err := checker.basicConfDao.GetBasicServiceableConfModelByLineIdUseCache(ctx, *request.BaseInfo.LineId)
	if err != nil {
		lineServiceableAreaConfLog(ctx, request.GetBaseInfo().GetLineId(), nil, nil, err)
		return nil, nil, err
	}
	if basicConf == nil {
		err = lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicConfErrorCode)
		lineServiceableAreaConfLog(ctx, request.GetBaseInfo().GetLineId(), nil, nil, err)
		return nil, nil, err
	}

	// 获取运营层配置，注意：有可能运营层配置为空
	operationConf, err := checker.operationConfDao.GetLogisticLineOperationServiceableConfTabByLineIDUseCache(ctx, *request.BaseInfo.LineId)
	if err != nil {
		lineServiceableAreaConfLog(ctx, request.GetBaseInfo().GetLineId(), basicConf, operationConf, err)
		return nil, nil, err
	}

	// 检查线的揽收能力是否支持
	if request.BaseInfo.CollectType != nil { // collect_type不为空，校验collect ability
		if err = serviceable_util.CheckLineCollectAbility(basicConf.CollectDeliverAbility, *request.BaseInfo.CollectType); err != nil {
			logger.CtxLogInfof(ctx, "line collect ability check failed, detail: %s", err.Msg)
			lineServiceableAreaConfLog(ctx, request.GetBaseInfo().GetLineId(), basicConf, operationConf, err)
			return nil, nil, err
		}
	}
	if request.BaseInfo.DeliverType != nil { // deliver_type不为空，校验deliver ability
		if err = serviceable_util.CheckLineDeliverAbility(basicConf.CollectDeliverAbility, *request.BaseInfo.DeliverType); err != nil {
			logger.CtxLogInfof(ctx, "line deliver ability check failed, detail: %s", err.Msg)
			lineServiceableAreaConfLog(ctx, request.GetBaseInfo().GetLineId(), basicConf, operationConf, err)
			return nil, nil, err
		}
	}

	// 校验完揽派能力后填充揽派类型
	serviceable_util.FillCollectDeliverTypeWhileEmpty(ctx, basicConf, request)

	// 检查线的揽派距离是否支持
	if err = serviceable_util.CheckLineDistance(basicConf, request.PickupInfo, request.DeliverInfo); err != nil {
		logger.CtxLogInfof(ctx, "line distance check failed, detail:%s", err.Msg)
		lineServiceableAreaConfLog(ctx, request.GetBaseInfo().GetLineId(), basicConf, operationConf, err)
		return nil, nil, err
	}
	lineServiceableAreaConfLog(ctx, request.GetBaseInfo().GetLineId(), basicConf, operationConf, nil)

	// 如果入参都不校验，直接返回四个can都是true
	if uint8(*request.BaseInfo.IsCheckOperation) == constant.DISABLED && uint8(*request.BaseInfo.IsCheckBasic) == constant.DISABLED {
		resultServiceable = GenAllSuccessServiceable()
		// 如果所有服务校验都没有通过，没必要在做电子围栏校验了
		if internalConfig.GetMutableConf(ctx).EFenceConfig.GetDowngradingSwitch() || isAllServiceAbleNotSupport(resultServiceable) {
			return resultServiceable, checkFailErrCode, nil
		}
		// 电子围栏检验
		eFenceResult, err := checker.checkElectricFence(ctx, request)
		if err != nil {
			logger.CtxLogErrorf(ctx, "electronic fence check fail|err[%s]", err.Msg)
			return nil, nil, err
		}
		// 上报电子围栏和服务范围校验结果对比
		if request.GetBaseInfo().GetUseElectricFence() {
			reportServiceCheckAndEFenceDiff(ctx, request.GetBaseInfo().GetLineId(), resultServiceable, eFenceResult)
		}

		transferFinalResult(resultServiceable, eFenceResult)

		return resultServiceable, checkFailErrCode, nil
	}

	// 只要运营层或基础层有一个配置的是enable，就需要校验collectType, deliverType能够唯一命中collectDeliverGroupId
	var defaultCollectDeliverGroupId = ""
	collectDeliverGroupId := &defaultCollectDeliverGroupId
	if basicConf.IsCheckServiceable == constant.ENABLED || (operationConf != nil && operationConf.IsCheckServiceable == constant.ENABLED) {
		collectType := request.BaseInfo.CollectType
		deliverType := request.BaseInfo.DeliverType
		// to-wms, to-3pl, to-branch 需要转成 to-site
		if *request.BaseInfo.DeliverType == uint32(constant.TOWMS) || *request.BaseInfo.DeliverType == uint32(constant.TO3PL) || *request.BaseInfo.DeliverType == uint32(constant.TOBRANCH) {
			toSite := uint32(constant.TOSITE)
			deliverType = &toSite
			request.BaseInfo.DeliverType = &toSite // 更新request里面的deliver_type为to-site
		}
		collectDeliverGroupId, err = checker.GetCollectDeliverGroupIdByCollectDeliverType(ctx, basicConf.LineId, collectType, deliverType)
		if err != nil {
			if err.RetCode == lcos_error.NotFoudCollectDeliverGroupIdErrorCode || err.RetCode == lcos_error.NotHitCollectDeliverGroupIdErrorCode {
				defaultPickupEnable := uint32(basicConf.DefaultPickupEnabled)
				defaultCodPickupEnable := uint32(basicConf.DefaultCodPickupEnabled)
				defaultDeliverEnable := uint32(basicConf.DefaultDeliverEnabled)
				defaultCodDeliverEnable := uint32(basicConf.DefaultCodDeliverEnabled)
				defaultTradeIn := uint32(constant.False)

				if *request.BaseInfo.CheckSender == 0 { // 不校验卖家，pick类型全部支持
					defaultPickupEnable = uint32(constant.ENABLED)
					defaultCodPickupEnable = uint32(constant.ENABLED)
				}
				if *request.BaseInfo.CheckReceiver == 0 { // 不校验买家，deliver类型全部支持
					defaultDeliverEnable = uint32(constant.ENABLED)
					defaultCodDeliverEnable = uint32(constant.ENABLED)
				}
				if request.BaseInfo.GetIsCheckTradeIn() == uint32(constant.IsCheck) {
					defaultDeliverEnable = defaultDeliverEnable & defaultTradeIn
					defaultCodDeliverEnable = defaultCodDeliverEnable & defaultTradeIn
				}

				resultServiceable = &pb.ServiceableAreaInfo{
					CanPickup:     &defaultPickupEnable,
					CanCodPickup:  &defaultCodPickupEnable,
					CanDeliver:    &defaultDeliverEnable,
					CanCodDeliver: &defaultCodDeliverEnable,
				}
				// 如果所有服务校验都没有通过，没必要在做电子围栏校验了
				if internalConfig.GetMutableConf(ctx).EFenceConfig.GetDowngradingSwitch() || isAllServiceAbleNotSupport(resultServiceable) {
					return resultServiceable, checkFailErrCode, nil
				}
				// 电子围栏检验
				eFenceResult, err := checker.checkElectricFence(ctx, request)
				if err != nil {
					logger.CtxLogErrorf(ctx, "electronic fence check fail|err[%s]", err.Msg)
					return nil, nil, err
				}
				// 上报电子围栏和服务范围校验结果对比
				if request.GetBaseInfo().GetUseElectricFence() {
					reportServiceCheckAndEFenceDiff(ctx, request.GetBaseInfo().GetLineId(), resultServiceable, eFenceResult)
				}

				transferFinalResult(resultServiceable, eFenceResult)

				return resultServiceable, checkFailErrCode, nil
			}
		}
	}

	// 初始化结果集
	resultServiceable = GenAllSuccessServiceable()

	// 提取入参的买卖家校验标识
	isCheckSeller, isCheckBuyer := request.BaseInfo.CheckSender, request.BaseInfo.CheckReceiver

	// 运营层服务范围校验
	if uint8(*request.BaseInfo.IsCheckOperation) == constant.ENABLED {
		if operationConf != nil && operationConf.IsCheckServiceable == constant.ENABLED {
			if uint8(*isCheckSeller) == constant.ENABLED { // 校验卖家
				var pickupServiceable *PickupServiceable
				if operationConf.MaintenanceMode == constant.TRUE {
					// 运营层服务范围maintenance mode，ban所有地址，返回pickup和cod pickup能力为不支持
					pickupServiceable = &PickupServiceable{
						CanPickup:    uint32(constant.FALSE),
						CanCodPickup: uint32(constant.FALSE),
					}
				} else {
					// 运营层服务范围未开启maintenance mode，则需要根据地址匹配是否禁用pickup和cod pickup
					var err *lcos_error.LCOSError
					pickupServiceable, err = checker.getOriginServiceableByRequest(ctx, true, request, basicConf, request.BaseInfo.SkipPostcode, *collectDeliverGroupId)
					if err != nil {
						return nil, nil, err
					}
				}
				resultServiceable.CanPickup = &pickupServiceable.CanPickup
				resultServiceable.CanCodPickup = &pickupServiceable.CanCodPickup
				// 先在这里细化错误码, 如果后续逻辑复杂再想办法在底层函数返回
				checkFailErrCode.set(pickupServiceable.CanPickup, basicConf.OriginServiceableType, "pickup",
					lcos_error.NotSupportPickupAddressErrorCode, lcos_error.NotSupportPickupPostcodeErrorCode)
				checkFailErrCode.set(pickupServiceable.CanCodPickup, basicConf.OriginServiceableType, "codPickup",
					lcos_error.NotSupportCodPickupAddressErrorCode, lcos_error.NotSupportCodPickupPostcodeErrorCode)
			}
			if uint8(*isCheckBuyer) == constant.ENABLED { // 校验买家
				var deliverServiceable *DeliverServiceable
				if operationConf.MaintenanceMode == constant.TRUE {
					deliverServiceable = &DeliverServiceable{
						CanDeliver:    uint32(constant.FALSE),
						CanCodDeliver: uint32(constant.FALSE),
						CanTradeIn:    notSupport,
					}
				} else {
					var err *lcos_error.LCOSError
					deliverServiceable, err = checker.getDestServiceableByRequest(ctx, true, request, basicConf, request.BaseInfo.SkipPostcode, *collectDeliverGroupId)
					if err != nil {
						return nil, nil, err
					}
				}
				resultServiceable.CanDeliver = &deliverServiceable.CanDeliver
				resultServiceable.CanCodDeliver = &deliverServiceable.CanCodDeliver
				resultServiceable.CanTradeIn = &deliverServiceable.CanTradeIn
				// 先在这里细化错误码, 如果后续逻辑复再想办法在底层函数返回
				checkFailErrCode.set(deliverServiceable.CanDeliver, basicConf.DestinationServiceableType, "deliver",
					lcos_error.NotSupportDeliverAddressErrorCode, lcos_error.NotSupportDeliverPostcodeErrorCode)
				checkFailErrCode.set(deliverServiceable.CanCodDeliver, basicConf.DestinationServiceableType, "codDeliver",
					lcos_error.NotSupportCodDeliverAddressErrorCode, lcos_error.NotSupportCodDeliverPostcodeErrorCode)
			}
			if request.BaseInfo.GetIsCheckTradeIn() == uint32(constant.IsCheck) {
				resultServiceable.CanDeliver = utils.NewUint32(*resultServiceable.CanDeliver & *resultServiceable.CanTradeIn)
				resultServiceable.CanCodDeliver = utils.NewUint32(*resultServiceable.CanCodDeliver & *resultServiceable.CanTradeIn)
			}
			// furthermore, to check operation zone/route
			if request.BaseInfo.SkipZoneRoute == nil || *request.BaseInfo.SkipZoneRoute == uint32(constant.DISABLED) {
				if basicConf.IsCheckRoute == constant.ENABLED {
					logger.CtxLogDebugf(ctx, "start checking operation route ...")
					if request.PickupInfo == nil || request.DeliverInfo == nil {
						return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check route,but pickup_info or deliver_info is nil")
					}
					IsLineRouteDisable, err := checker.checkOperationServiceableRoute(ctx, *request.BaseInfo.LineId, *collectDeliverGroupId, request.PickupInfo, request.DeliverInfo, basicConf.RouteMode)
					if err != nil {
						return nil, nil, err
					}
					if IsLineRouteDisable {
						logger.CtxLogInfof(ctx, "operation route check failed, location not in line route")
						return nil, nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.RouteCheckErrorCode)
					}
				}
			}
		}
	}

	// 校验基础层服务范围
	if uint8(*request.BaseInfo.IsCheckBasic) == constant.ENABLED && basicConf.IsCheckServiceable == constant.ENABLED {
		if uint8(*isCheckSeller) == constant.ENABLED { // 校验卖家
			if *resultServiceable.CanPickup == support || *resultServiceable.CanCodPickup == support { // 运营层白名单检验通过，继续校验基础层
				pickupServiceable, err := checker.getOriginServiceableByRequest(ctx, false, request, basicConf, request.BaseInfo.SkipPostcode, *collectDeliverGroupId)
				if err != nil {
					return nil, nil, err
				}
				// 取运营层结果和基础层结果的取并集
				canPickup := *resultServiceable.CanPickup & pickupServiceable.CanPickup
				canCodPickup := *resultServiceable.CanCodPickup & pickupServiceable.CanCodPickup
				resultServiceable.CanPickup = &canPickup
				resultServiceable.CanCodPickup = &canCodPickup
				// 先在这里细化错误码, 如果后续逻辑复杂再想办法在底层函数返回
				checkFailErrCode.set(pickupServiceable.CanPickup, basicConf.OriginServiceableType, "pickup",
					lcos_error.NotSupportPickupAddressErrorCode, lcos_error.NotSupportPickupPostcodeErrorCode)
				checkFailErrCode.set(pickupServiceable.CanCodPickup, basicConf.OriginServiceableType, "codPickup",
					lcos_error.NotSupportCodPickupAddressErrorCode, lcos_error.NotSupportCodPickupPostcodeErrorCode)
			}
		}
		if uint8(*isCheckBuyer) == constant.ENABLED { // 校验买家
			if *resultServiceable.CanDeliver == support || *resultServiceable.CanCodDeliver == support { // 运营层白名单检验通过，继续校验基础层
				deliverServiceable, err := checker.getDestServiceableByRequest(ctx, false, request, basicConf, request.BaseInfo.SkipPostcode, *collectDeliverGroupId)
				if err != nil {
					return nil, nil, err
				}
				// 取运营层结果和基础层结果的取并集
				canDeliver := *resultServiceable.CanDeliver & deliverServiceable.CanDeliver
				canCodDeliver := *resultServiceable.CanCodDeliver & deliverServiceable.CanCodDeliver
				canTradeIn := *resultServiceable.CanTradeIn & deliverServiceable.CanTradeIn
				resultServiceable.CanTradeIn = &canTradeIn
				resultServiceable.CanDeliver = &canDeliver
				resultServiceable.CanCodDeliver = &canCodDeliver
				// 先在这里细化错误码, 如果后续逻辑复杂再想办法在底层函数返回
				checkFailErrCode.set(deliverServiceable.CanDeliver, basicConf.DestinationServiceableType, "deliver",
					lcos_error.NotSupportDeliverAddressErrorCode, lcos_error.NotSupportDeliverPostcodeErrorCode)
				checkFailErrCode.set(deliverServiceable.CanCodDeliver, basicConf.DestinationServiceableType, "codDeliver",
					lcos_error.NotSupportCodDeliverAddressErrorCode, lcos_error.NotSupportCodDeliverPostcodeErrorCode)
			}
		}
		if request.BaseInfo.GetIsCheckTradeIn() == uint32(constant.IsCheck) {
			resultServiceable.CanDeliver = utils.NewUint32(*resultServiceable.CanDeliver & *resultServiceable.CanTradeIn)
			resultServiceable.CanCodDeliver = utils.NewUint32(*resultServiceable.CanCodDeliver & *resultServiceable.CanTradeIn)
		}
		if request.BaseInfo.SkipZoneRoute == nil || *request.BaseInfo.SkipZoneRoute == uint32(constant.DISABLED) { // 没传SkipZoneRoute，或者 SkipZoneRoute=0
			// 校验route
			if basicConf.IsCheckRoute == constant.ENABLED {
				if request.PickupInfo == nil || request.DeliverInfo == nil {
					return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check route,but pickup_info or deliver_info is nil")
				}
				isInLineRoute, err := checker.checkLocationServiceableRoute(ctx, *request.BaseInfo.LineId, *collectDeliverGroupId, request.PickupInfo, request.DeliverInfo, basicConf.RouteMode)
				if err != nil {
					return nil, nil, err
				}
				if !isInLineRoute {
					logger.CtxLogInfof(ctx, "route check failed, location not in line route")
					return nil, nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.RouteCheckErrorCode)
				}
			}
		}

		// SPLN-36590 上游需要校验predefined route并且线开启了predefined route校验（NSS only）
		if request.BaseInfo.GetCheckPredefinedRoute() && basicConf.IsCheckPredefinedRoute == constant.ENABLED {
			if err = checker.filterServiceablePredefinedRoute(ctx, request.GetBaseInfo().GetPredefinedRouteCodes(), request.GetBaseInfo().GetLineId(), *collectDeliverGroupId, request.GetPickupInfo(), request.GetDeliverInfo()); err != nil {
				return nil, nil, err
			}
		}
	}

	// 电子围栏校验
	// 如果所有服务校验都没有通过，没必要在做电子围栏校验了
	if internalConfig.GetMutableConf(ctx).EFenceConfig.GetDowngradingSwitch() || isAllServiceAbleNotSupport(resultServiceable) {
		return resultServiceable, checkFailErrCode, nil
	}
	eFenceResult, err := checker.checkElectricFence(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "electronic fence check fail|err[%s]", err.Msg)
		return nil, nil, err
	}

	// 上报电子围栏和服务范围校验结果对比
	if request.GetBaseInfo().GetUseElectricFence() {
		reportServiceCheckAndEFenceDiff(ctx, request.GetBaseInfo().GetLineId(), resultServiceable, eFenceResult)
	}

	transferFinalResult(resultServiceable, eFenceResult)

	return resultServiceable, checkFailErrCode, nil

}

func isAllServiceAbleNotSupport(result *pb.ServiceableAreaInfo) bool {
	if result.GetCanPickup() == notSupport && result.GetCanCodPickup() == notSupport &&
		result.GetCanDeliver() == notSupport && result.GetCanCodDeliver() == notSupport {
		return true
	}
	return false
}

func transferFinalResult(serviceableResult *pb.ServiceableAreaInfo, eFenceResult *pb.ServiceableAreaInfo) {
	serviceableResult.CanPickup = utils.NewUint32(serviceableResult.GetCanPickup() & eFenceResult.GetCanPickup())
	serviceableResult.CanCodPickup = utils.NewUint32(serviceableResult.GetCanCodPickup() & eFenceResult.GetCanCodPickup())
	serviceableResult.CanDeliver = utils.NewUint32(serviceableResult.GetCanDeliver() & eFenceResult.GetCanDeliver())
	serviceableResult.CanCodDeliver = utils.NewUint32(serviceableResult.GetCanCodDeliver() & eFenceResult.GetCanCodDeliver())
	serviceableResult.PickupInEFence = eFenceResult.PickupInEFence
	serviceableResult.DeliverInEFence = eFenceResult.DeliverInEFence
}

func reportServiceCheckAndEFenceDiff(ctx utils.LCOSContext, lineId string, serviceableResult *pb.ServiceableAreaInfo, eFenceResult *pb.ServiceableAreaInfo) {
	if internalConfig.GetMutableConf(ctx).EFenceConfig.GetReportDowngrading() {
		return
	}
	reportMap := map[string]string{
		"line_id":              lineId,
		"pickup_can_diff":      "",
		"pickup_can_cod_diff":  "",
		"deliver_can_diff":     "",
		"deliver_can_cod_diff": "",
	}
	if serviceableResult.GetCanPickup() == support && eFenceResult.GetCanPickup() == notSupport {
		reportMap["pickup_can_diff"] = e_fence.ElectronicFenceNotSupport
	} else if serviceableResult.GetCanPickup() == notSupport && eFenceResult.GetCanPickup() == support {
		reportMap["pickup_can_diff"] = e_fence.ServiceCheckNotSupport
	}

	if serviceableResult.GetCanCodPickup() == support && eFenceResult.GetCanCodPickup() == notSupport {
		reportMap["pickup_can_cod_diff"] = e_fence.ElectronicFenceNotSupport
	} else if serviceableResult.GetCanCodPickup() == notSupport && eFenceResult.GetCanCodPickup() == support {
		reportMap["pickup_can_cod_diff"] = e_fence.ServiceCheckNotSupport
	}

	if serviceableResult.GetCanDeliver() == support && eFenceResult.GetCanDeliver() == notSupport {
		reportMap["deliver_can_diff"] = e_fence.ElectronicFenceNotSupport
	} else if serviceableResult.GetCanDeliver() == notSupport && eFenceResult.GetCanDeliver() == support {
		reportMap["deliver_can_diff"] = e_fence.ServiceCheckNotSupport
	}

	if serviceableResult.GetCanCodDeliver() == support && eFenceResult.GetCanCodDeliver() == notSupport {
		reportMap["deliver_can_cod_diff"] = e_fence.ElectronicFenceNotSupport
	} else if serviceableResult.GetCanCodDeliver() == notSupport && eFenceResult.GetCanCodDeliver() == support {
		reportMap["deliver_can_cod_diff"] = e_fence.ServiceCheckNotSupport
	}

	// 没有差异无需上报
	if reportMap["pickup_can_diff"] == "" && reportMap["pickup_can_cod_diff"] == "" && reportMap["deliver_can_diff"] == "" && reportMap["deliver_can_cod_diff"] == "" {
		return
	}
	_ = metrics.CounterIncr(constant.MetricsEFenceAndServiceCheckDiffReport, reportMap)
}

func (checker *ServiceableCheckerService) checkElectricFence(ctx utils.LCOSContext, req *pb.GetLineServiceableInfoRequest2) (*pb.ServiceableAreaInfo, *lcos_error.LCOSError) {
	if !req.BaseInfo.GetUseElectricFence() {
		return checker.EFenceCheckSuccessResult(), nil
	}

	// 校验Line配置
	lineConf, err := checker.eFenceLineToggleDao.GetLineConfFromCache(ctx, req.GetBaseInfo().GetLineId())
	if err != nil {
		logger.CtxLogErrorf(ctx, "get line from cache fail|lineId[%s]", req.GetBaseInfo().GetLineId())
		checker.reportEFenceResult(ctx, req.GetBaseInfo().GetLineId(), fmt.Sprintf("%d", err.RetCode), fmt.Sprintf("%d", err.RetCode))
		return nil, err
	}
	if lineConf == nil || lineConf.SupportLmHubZone == e_fence.NotSupportLmHubZone {
		logger.CtxLogInfof(ctx, "%s not need electric fence check", req.BaseInfo.GetLineId())
		checker.reportEFenceResult(ctx, req.GetBaseInfo().GetLineId(), e_fence.ReportLineNotConfig, e_fence.ReportLineNotConfig)
		return checker.EFenceCheckSuccessResult(), nil
	}

	// 校验LocationId白名单
	pickupInWhitelist, err := checker.checkEFenceLocationWhitelist(ctx, req.GetPickupInfo(), lineConf.LayerId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "check pickup location whitelist fail")
		checker.reportEFenceResult(ctx, req.GetBaseInfo().GetLineId(), fmt.Sprintf("%d", err.RetCode), fmt.Sprintf("%d", err.RetCode))
		return nil, err
	}
	deliverInWhitelist, err := checker.checkEFenceLocationWhitelist(ctx, req.GetDeliverInfo(), lineConf.LayerId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "check deliver location whitelist fail")
		checker.reportEFenceResult(ctx, req.GetBaseInfo().GetLineId(), fmt.Sprintf("%d", err.RetCode), fmt.Sprintf("%d", err.RetCode))
		return nil, err
	}
	if pickupInWhitelist && deliverInWhitelist {
		logger.CtxLogInfof(ctx, "pickup and deliver address are both on the whitelist")
		checker.reportEFenceResult(ctx, req.GetBaseInfo().GetLineId(), e_fence.ReportLocationWhiteList, e_fence.ReportLocationWhiteList)
		return checker.EFenceCheckSuccessResult(), nil
	}

	// line serviceable 检查
	pickupResult, deliverResult, err := checker.checkEFenceLineServiceable(ctx, lineConf, req, pickupInWhitelist, deliverInWhitelist)
	if err != nil {
		logger.CtxLogErrorf(ctx, "check electric fence fail|err[%s]", err.Msg)
		checker.reportEFenceResult(ctx, req.GetBaseInfo().GetLineId(), fmt.Sprintf("%d", err.RetCode), fmt.Sprintf("%d", err.RetCode))
		return nil, err
	}
	eFenceResult := &pb.ServiceableAreaInfo{
		CanPickup:       utils.NewUint32(pickupResult.Support),
		CanCodPickup:    utils.NewUint32(pickupResult.Support),
		CanDeliver:      utils.NewUint32(deliverResult.Support),
		CanCodDeliver:   utils.NewUint32(deliverResult.Support),
		PickupInEFence:  utils.NewUint32(pickupResult.Support),
		DeliverInEFence: utils.NewUint32(deliverResult.Support),
	}
	logger.CtxLogInfof(ctx, "electric fence result|lineId[%s]|pickup[%d]|pickup zoneId[%s]|deliver[%d]|deliver zoneId[%s]", req.BaseInfo.GetLineId(), pickupResult.Support, pickupResult.ZoneId, deliverResult.Support, deliverResult.ZoneId)
	checker.reportEFenceResult(ctx, req.GetBaseInfo().GetLineId(), pickupResult.Report, deliverResult.Report)

	return eFenceResult, nil
}

func (checker *ServiceableCheckerService) reportEFenceResult(ctx utils.LCOSContext, lineId string, pickupResult, deliverResult string) {
	if internalConfig.GetMutableConf(ctx).EFenceConfig.GetReportDowngrading() {
		return
	}
	reportMap := map[string]string{
		"line_id":         lineId,
		"pickup_result":   pickupResult,
		"deliver_result":  deliverResult,
		"whether_success": "success",
		"ret_code":        fmt.Sprintf("%s-%s", pickupResult, deliverResult),
	}
	if !slice.Contains(e_fence.SuccessReportCode, pickupResult) || !slice.Contains(e_fence.SuccessReportCode, deliverResult) {
		reportMap["whether_success"] = "fail"
	}

	_ = metrics.CounterIncr(constant.MetricsElectronicFenceReport, reportMap)
}

func (checker *ServiceableCheckerService) checkEFenceLineServiceable(ctx utils.LCOSContext, lineConf *line_toggle.EFenceLineToggleTab, req *pb.GetLineServiceableInfoRequest2, pickupInWhitelist, deliverInWhitelist bool) (*EFenceResult, *EFenceResult, *lcos_error.LCOSError) {
	pickupResult, deliverResult := &EFenceResult{Support: support}, &EFenceResult{Support: support}
	var err *lcos_error.LCOSError

	switch lineConf.CheckWith {
	case e_fence.EFenceCheckPickup:
		pickupResult, err = checker.checkEFenceLocationInfo(ctx, req.PickupInfo, lineConf, pickupInWhitelist)
		if err != nil {
			return nil, nil, err
		}
	case e_fence.EFenceCheckDeliver:
		deliverResult, err = checker.checkEFenceLocationInfo(ctx, req.DeliverInfo, lineConf, deliverInWhitelist)
		if err != nil {
			return nil, nil, err
		}
	case e_fence.EFenceCheckBoth:
		pickupResult, err = checker.checkEFenceLocationInfo(ctx, req.PickupInfo, lineConf, pickupInWhitelist)
		if err != nil {
			return nil, nil, err
		}
		deliverResult, err = checker.checkEFenceLocationInfo(ctx, req.DeliverInfo, lineConf, deliverInWhitelist)
		if err != nil {
			return nil, nil, err
		}
	}
	return pickupResult, deliverResult, nil
}

func (checker *ServiceableCheckerService) checkEFenceLocationInfo(ctx utils.LCOSContext, location *pb.LocationInfo, lineConf *line_toggle.EFenceLineToggleTab, inWhitelist bool) (*EFenceResult, *lcos_error.LCOSError) {
	// 白名单校验通过，不需要做经纬度校验
	if inWhitelist {
		return &EFenceResult{Support: support, Report: e_fence.ReportLocationWhiteList}, nil
	}
	result, err := checker.checkEFenceLngAndLat(ctx, location.GetLongitude(), location.GetLatitude(), lineConf)
	if err != nil {
		return nil, err
	}

	// 如果经纬度和地址信息都为空，用单独的标记上报。主要是针对tw的某些业务场景，不会传买家地址，此时过不了电子围栏校验是符合预期的，但是要单独观察到比例
	if location.GetLongitude() == "" && location.GetLatitude() == "" && location.GetStateLocationId() == 0 && location.GetCityLocationId() == 0 && location.GetDistrictLocationId() == 0 && location.GetStreetLocationId() == 0 {
		logger.CtxLogInfof(ctx, "location info is empty: %d, %d, %d, %d", location.GetStateLocationId(), location.GetCityLocationId(), location.GetDistrictLocationId(), location.GetStreetLocationId())
		result.Report = e_fence.ReportMissLocationInfo
	}

	return result, nil
}
func (checker *ServiceableCheckerService) checkEFenceLngAndLat(ctx utils.LCOSContext, lngStr, latStr string, lineConf *line_toggle.EFenceLineToggleTab) (*EFenceResult, *lcos_error.LCOSError) {
	if lngStr == "" || latStr == "" {
		logger.CtxLogErrorf(ctx, "invalid geo info|lngStr[%s]|latStr[%s]", lngStr, latStr)
		return &EFenceResult{Support: notSupport, Report: e_fence.ReportMissGeoInfo}, nil
	}

	region := lineConf.Region
	layerId := lineConf.LayerId

	lng, err := strconv.ParseFloat(lngStr, 64)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("Illegal longitude|[%s]", lngStr))
	}
	lat, err := strconv.ParseFloat(latStr, 64)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("Illegal latitude|[%s]", latStr))
	}

	// 先生成geohash
	encodingMin, encodingMax := internalConfig.GetMutableConf(ctx).EFenceConfig.GetEncodingSize(region, e_fence.CheckAreaServiceable)
	geoHash, err := geopolygon.GetGeoHash(lng, lat, encodingMax)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SdkGeoHashError, fmt.Sprintf("gen geohash fail|err[%s]", err.Error()))
	}

	var geoHashList []string
	for i := encodingMin; i <= encodingMax; i++ {
		geoHashList = append(geoHashList, geoHash[0:i])
	}

	needCheckLayer := utils.CheckPercent(ctx, internalConfig.GetMutableConf(ctx).EFenceConfig.NeedCheckLayer[region])

	for _, hash := range geoHashList {
		zoneList, exist, lcosErr := checker.eFenceMeshDao.GetMeshFromCache(ctx, hash, region)
		if lcosErr != nil {
			return nil, lcosErr
		}
		if !exist {
			continue
		}

		for _, zone := range zoneList {
			// 只有layer id相同，才能做全覆盖
			if !checkLayerId(ctx, region, layerId, zone.LayerId, needCheckLayer) {
				continue
			}

			if zone.FullCoverage {
				return &EFenceResult{Support: support, ZoneId: zone.ZoneId, Report: e_fence.ReportFullCover}, nil
			}
		}
		// 所有的zone都没有全覆盖这个网格，需要做射线法
		for _, zone := range zoneList {
			// 只有layer id相同，才能做射线法
			if !checkLayerId(ctx, region, layerId, zone.LayerId, needCheckLayer) {
				continue
			}

			zoneInfo, exist, err := checker.eFencePolygonDao.GetPolygonInfoFromCache(ctx, region, zone.ZoneId, zone.Version, zone.LayerId)
			if err != nil {
				return nil, err
			}
			if !exist {
				continue
			}
			cover, geoErr := geopolygon.IsCoordinateInsidePolygon(lng, lat, zoneInfo)
			if geoErr != nil {
				return nil, lcos_error.NewLCOSError(lcos_error.SdkGeoHashError, fmt.Sprintf("ray method fail|err[%s]", geoErr.Error()))
			}
			if cover {
				return &EFenceResult{Support: support, ZoneId: zone.ZoneId, Report: e_fence.ReportUseRay}, nil
			}
		}
	}

	logger.CtxLogErrorf(ctx, "eFence not support to service|geoHash[%s]|lng,lat[%s,%s]|region[%s]｜layer[%s]", geoHash, lngStr, latStr, region, layerId)
	return &EFenceResult{Support: notSupport, Report: e_fence.ReportNotSupport}, nil
}

func checkLayerId(ctx context.Context, region, requestLayerId, zoneLayerId string, needCheckLayer bool) bool {
	region = strings.ToLower(region)
	// 默认不做layer维度校验，tw用 layer id = 1 来校验，非tw用 layer id = 0 来校验
	if !needCheckLayer {
		if region == eFenceConstant.RegionTW {
			requestLayerId = eFenceConstant.TwDefaultLayer
		} else {
			requestLayerId = eFenceConstant.DefaultLayer
		}
	}

	if requestLayerId != zoneLayerId {
		// 监控上报上线兼容情况
		eventNameEFenceLayer := utils.GenKey("_", commonConstant.EventNameEFenceLayer, region, requestLayerId, zoneLayerId)
		_ = monitor.ReportEvent(commonConstant.CatModuleEFenceCheckOut, eventNameEFenceLayer, commonConstant.StatusError, "")
		return false

	}
	_ = monitor.ReportEvent(commonConstant.CatModuleEFenceCheckOut, commonConstant.EventNameEFenceLayer, commonConstant.StatusSuccess, "")
	return true
}

// todo数据量
func (checker *ServiceableCheckerService) checkEFenceLocationWhitelist(ctx utils.LCOSContext, locationInfo *pb.LocationInfo, layerId string) (bool, *lcos_error.LCOSError) {
	locationIdList := []uint32{locationInfo.GetStreetLocationId(), locationInfo.GetDistrictLocationId(), locationInfo.GetCityLocationId(), locationInfo.GetStateLocationId()}
	for _, locationId := range locationIdList {
		_, exist, err := checker.eFenceLocationWhitelistDao.GetLocationWhitelistFromCache(ctx, int64(locationId), layerId)
		if err != nil {
			return false, err
		}
		if exist {
			return true, nil
		}
	}
	return false, nil
}

func (checker *ServiceableCheckerService) EFenceCheckSuccessResult() *pb.ServiceableAreaInfo {
	return &pb.ServiceableAreaInfo{
		CanPickup:       &support,
		CanCodPickup:    &support,
		CanDeliver:      &support,
		CanCodDeliver:   &support,
		PickupInEFence:  &support,
		DeliverInEFence: &support,
	}
}

// 批量获取线服务范围
func (checker *ServiceableCheckerService) BatchGetServiceable(ctx utils.LCOSContext, request *pb.BatchGetLineServiceableInfoRequest2) (*pb.BatchGetLineServiceableInfoResponse2, *lcos_error.LCOSError) {
	reqList := request.ServiceableReqList
	// 结果列表
	respList := make([]*pb.SingleGetServiceableResponse2, 0, len(reqList))
	for _, signalRequest := range reqList {
		serviceableInfo, _, err := checker.GetServiceableWithCheckFlag(ctx, &pb.GetLineServiceableInfoRequest2{
			ReqHeader:   request.ReqHeader,
			BaseInfo:    signalRequest.BaseInfo,
			PickupInfo:  signalRequest.PickupInfo,
			DeliverInfo: signalRequest.DeliverInfo,
		})
		if err != nil {
			respList = append(respList, &pb.SingleGetServiceableResponse2{
				LineId:          signalRequest.BaseInfo.LineId,
				ServiceableInfo: GenAllFailedServiceable(),
				ItemCode:        &err.RetCode,
				Message:         proto.String(err.Msg),
			})
		} else {
			itemCode := lcos_error.SuccessCode
			respList = append(respList, &pb.SingleGetServiceableResponse2{
				LineId:          signalRequest.BaseInfo.LineId,
				ServiceableInfo: serviceableInfo,
				ItemCode:        &itemCode,
				Message:         proto.String("success"),
			})
		}
	}
	return &pb.BatchGetLineServiceableInfoResponse2{
		RespHeader:          http.GrpcSuccessRespHeader(),
		ServiceableRespList: respList,
	}, nil

}

// 通过request，获取起始端的服务范围能力
func (checker *ServiceableCheckerService) getOriginServiceableByRequest(ctx utils.LCOSContext, isOperation bool, request *pb.GetLineServiceableInfoRequest2, basicConf *basic_conf.LineBasicServiceableConfTab, skipPostcode *uint32, collectDeliverGroupId string) (*PickupServiceable, *lcos_error.LCOSError) {
	senderCheckLevel := request.BaseInfo.SenderCheckLevel
	locationId := serviceable_util.GetLocationIdByCheckLevel(uint8(*senderCheckLevel), request.PickupInfo)
	postcode := serviceable_util.GetPostCode(request.PickupInfo)

	// 如果origin类型是postcode，且传入的skipPostcode=1，则直接返回pickup，codPickup支持
	if skipPostcode != nil && uint8(*skipPostcode) == constant.ENABLED && basicConf.OriginServiceableType == constant.POSTCODE {
		return GenAllSuccessPickupServiceable(), nil
	}

	// 传入的location或postcode为空，使用默认配置的能力
	if (basicConf.OriginServiceableType == constant.LOCATION && locationId == nil) ||
		(basicConf.OriginServiceableType == constant.POSTCODE && postcode == nil) {
		if isOperation {
			return GenAllSuccessPickupServiceable(), nil
		}
		return &PickupServiceable{
			CanPickup:    uint32(basicConf.DefaultPickupEnabled),
			CanCodPickup: uint32(basicConf.DefaultCodPickupEnabled),
		}, nil
	}

	originServiceableAbility, err := checker.getOriginServiceableDetail(ctx, isOperation, basicConf.OriginServiceableType, locationId, postcode, *request.BaseInfo.LineId, collectDeliverGroupId)
	logOpsOriginServiceableDetail(ctx, isOperation, basicConf.OriginServiceableType, locationId, postcode, request.GetBaseInfo().GetLineId(), collectDeliverGroupId, originServiceableAbility, err)
	if err != nil {
		return GenAllFailedPickupServiceable(), err
	}

	if originServiceableAbility == nil || reflect.ValueOf(originServiceableAbility).IsNil() {
		if isOperation {
			return GenAllSuccessPickupServiceable(), nil
		} else {
			return &PickupServiceable{
				CanPickup:    uint32(basicConf.DefaultPickupEnabled),
				CanCodPickup: uint32(basicConf.DefaultCodPickupEnabled),
			}, nil
		}
	}

	return &PickupServiceable{
		CanPickup:    uint32(originServiceableAbility.GetCanPickup()),
		CanCodPickup: uint32(originServiceableAbility.GetCanCodPickup()),
	}, nil
}

// Deprecated
// 旧的通过 scenario 场景配置获取origin serviceable将被废弃，请使用getOriginServiceableByRequest
func (checker *ServiceableCheckerService) getOriginServiceable(ctx utils.LCOSContext, isOperation bool, request *pb.GetLineServiceableInfoRequest, basicConf *basic_conf.LineBasicServiceableConfTab, scenarioConf *scenario_conf.LineCommonServiceableScenarioConfTab, collectDeliverGroupId string) (*PickupServiceable, *lcos_error.LCOSError) {
	locationId := serviceable_util.GetLocationIdByLevel(scenarioConf, request.PickupInfo, *request.BaseInfo.Scenario)
	postcode := serviceable_util.GetPostCode(request.PickupInfo)

	if basicConf.OriginServiceableType == constant.POSTCODE && (*request.BaseInfo.Scenario == uint32(constant.PDP)) {
		return &PickupServiceable{
			CanPickup:    uint32(basicConf.DefaultPdpPostcode),
			CanCodPickup: uint32(basicConf.DefaultPdpPostcode),
		}, nil
	}

	if (basicConf.OriginServiceableType == constant.LOCATION && locationId == nil) ||
		(basicConf.OriginServiceableType == constant.POSTCODE && postcode == nil) {
		if isOperation {
			return GenAllSuccessPickupServiceable(), nil
		}
		return &PickupServiceable{
			CanPickup:    uint32(basicConf.DefaultPickupEnabled),
			CanCodPickup: uint32(basicConf.DefaultCodPickupEnabled),
		}, nil
	}
	originServiceableAbility, err := checker.getOriginServiceableDetail(ctx, isOperation, basicConf.OriginServiceableType, locationId, postcode, *request.BaseInfo.LineId, collectDeliverGroupId)
	if err != nil {
		return GenAllFailedPickupServiceable(), err
	}

	if originServiceableAbility == nil || reflect.ValueOf(originServiceableAbility).IsNil() {
		if isOperation {
			return GenAllSuccessPickupServiceable(), nil
		} else {
			return &PickupServiceable{
				CanPickup:    uint32(basicConf.DefaultPickupEnabled),
				CanCodPickup: uint32(basicConf.DefaultCodPickupEnabled),
			}, nil
		}
	}

	return &PickupServiceable{
		CanPickup:    uint32(originServiceableAbility.GetCanPickup()),
		CanCodPickup: uint32(originServiceableAbility.GetCanCodPickup()),
	}, nil
}

func (checker *ServiceableCheckerService) getDestServiceableByRequest(ctx utils.LCOSContext, isOperation bool, request *pb.GetLineServiceableInfoRequest2, basicConf *basic_conf.LineBasicServiceableConfTab, skipPostcode *uint32, collectDeliverGroupId string) (*DeliverServiceable, *lcos_error.LCOSError) {
	checkLevel := request.BaseInfo.ReceiverCheckLevel
	locationId := serviceable_util.GetLocationIdByCheckLevel(uint8(*checkLevel), request.DeliverInfo)
	postcode := serviceable_util.GetPostCode(request.DeliverInfo)

	// 如果destination类型是postcode，且传入的skipPostcode=1，则直接返回deliver，codDeliver支持
	if skipPostcode != nil && uint8(*skipPostcode) == constant.ENABLED && basicConf.DestinationServiceableType == constant.POSTCODE {
		return GenAllSuccessDeliverServiceable(), nil
	}

	// 传入的location或postcode为空，使用默认配置的能力
	if (basicConf.DestinationServiceableType == constant.LOCATION && locationId == nil) ||
		(basicConf.DestinationServiceableType == constant.POSTCODE && postcode == nil) {
		if isOperation {
			return GenAllSuccessDeliverServiceable(), nil
		}
		return &DeliverServiceable{
			CanDeliver:    uint32(basicConf.DefaultDeliverEnabled),
			CanCodDeliver: uint32(basicConf.DefaultCodDeliverEnabled),
			CanTradeIn:    notSupport,
		}, nil
	}

	destServiceableAbility, err := checker.getDestServiceableDetail(ctx, isOperation, basicConf.DestinationServiceableType, locationId, postcode, *request.BaseInfo.LineId, collectDeliverGroupId)
	logOpsDestServiceableDetail(ctx, isOperation, basicConf.DestinationServiceableType, locationId, postcode, *request.BaseInfo.LineId, collectDeliverGroupId, destServiceableAbility, err)
	if err != nil {
		return GenAllFailedDeliverServiceable(), err
	}

	if destServiceableAbility == nil || reflect.ValueOf(destServiceableAbility).IsNil() {
		if isOperation {
			return GenAllSuccessDeliverServiceable(), nil
		} else {
			return &DeliverServiceable{
				CanDeliver:    uint32(basicConf.DefaultDeliverEnabled),
				CanCodDeliver: uint32(basicConf.DefaultCodDeliverEnabled),
				CanTradeIn:    notSupport,
			}, nil
		}
	}

	return &DeliverServiceable{
		CanDeliver:    uint32(destServiceableAbility.GetCanDeliver()),
		CanCodDeliver: uint32(destServiceableAbility.GetCanCodDeliver()),
		CanTradeIn:    uint32(destServiceableAbility.GetCanTradeIn()),
	}, nil
}

// Deprecated
// 使用带场景配置的request获取，后面将会被废弃，请使用getDestServiceableByRequest
func (checker *ServiceableCheckerService) getDestServiceable(ctx utils.LCOSContext, isOperation bool, request *pb.GetLineServiceableInfoRequest, basicConf *basic_conf.LineBasicServiceableConfTab, scenarioConf *scenario_conf.LineCommonServiceableScenarioConfTab, collectDeliverGroupId string) (*DeliverServiceable, *lcos_error.LCOSError) {
	locationId := serviceable_util.GetLocationIdByLevel(scenarioConf, request.DeliverInfo, *request.BaseInfo.Scenario)
	postcode := serviceable_util.GetPostCode(request.DeliverInfo)

	if basicConf.DestinationServiceableType == constant.POSTCODE && (*request.BaseInfo.Scenario == uint32(constant.PDP)) {
		return &DeliverServiceable{
			CanDeliver:    uint32(basicConf.DefaultPdpPostcode),
			CanCodDeliver: uint32(basicConf.DefaultPdpPostcode),
		}, nil
	}

	if (basicConf.DestinationServiceableType == constant.LOCATION && locationId == nil) ||
		(basicConf.DestinationServiceableType == constant.POSTCODE && postcode == nil) {
		if isOperation {
			return GenAllSuccessDeliverServiceable(), nil
		}
		return &DeliverServiceable{
			CanDeliver:    uint32(basicConf.DefaultDeliverEnabled),
			CanCodDeliver: uint32(basicConf.DefaultCodDeliverEnabled),
		}, nil
	}

	destServiceableAbility, err := checker.getDestServiceableDetail(ctx, isOperation, basicConf.DestinationServiceableType, locationId, postcode, *request.BaseInfo.LineId, collectDeliverGroupId)
	if err != nil {
		return GenAllFailedDeliverServiceable(), err
	}

	if destServiceableAbility == nil || reflect.ValueOf(destServiceableAbility).IsNil() {
		if isOperation {
			return GenAllSuccessDeliverServiceable(), nil
		} else {
			return &DeliverServiceable{
				CanDeliver:    uint32(basicConf.DefaultDeliverEnabled),
				CanCodDeliver: uint32(basicConf.DefaultCodDeliverEnabled),
			}, nil
		}
	}

	return &DeliverServiceable{
		CanDeliver:    uint32(destServiceableAbility.GetCanDeliver()),
		CanCodDeliver: uint32(destServiceableAbility.GetCanCodDeliver()),
	}, nil
}

func (checker *ServiceableCheckerService) getOriginServiceableDetail(ctx utils.LCOSContext, isOperation bool, serviceableType uint8, locationId *uint32, postcode *string, lineId string, collectDeliverGroupId string) (OriginServiceableAbility, *lcos_error.LCOSError) {
	if serviceableType == constant.LOCATION {
		if locationId == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check origin location, but location_id is nil")
		}
		if isOperation {
			return checker.operationLocationDao.SearchLineOperationServiceableLocationDetailUseCache(ctx, lineId, *locationId, collectDeliverGroupId)
		} else {
			return checker.basicLocationDao.SearchBasicServiceableLocationDetailUseCache(ctx, lineId, *locationId, collectDeliverGroupId)
		}
	} else if serviceableType == constant.POSTCODE { // serviceableType == constant.POSTCODE
		if postcode == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check origin postcode, but postcode is nil")
		}
		if isOperation {
			return checker.operationPostcodeDao.SearchLineOperationServiceablePostcodeDetailUseCache(ctx, lineId, *postcode, collectDeliverGroupId)
		} else {
			return checker.basicPostcodeDao.SearchBasicServiceablePostcodeDetailUseCache(ctx, lineId, *postcode, collectDeliverGroupId)
		}
	} else if serviceableType == constant.CEPRANGE {
		if postcode == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check origin cep range, but postcode is nil")
		}

		// 增加对postcode中存在非数字的处理逻辑
		var lcosErr *lcos_error.LCOSError
		*postcode, lcosErr = validatePostalCode(*postcode)
		if lcosErr != nil {
			return nil, lcosErr
		}

		cepRangeCode, err := strconv.Atoi(*postcode)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "postcode is not number")
		}
		if cepRangeCode < 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "postcode is negative number")
		}
		if isOperation {
			return checker.cepRangeDao.GetOperationCepRangeModelUseCache(ctx, lineId, collectDeliverGroupId, uint64(cepRangeCode))
		} else {
			return checker.cepRangeDao.GetBasicCepRangeModelUseCache(ctx, lineId, collectDeliverGroupId, uint64(cepRangeCode))
		}
	}
	return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "serviceableType is not valid")
}

func (checker *ServiceableCheckerService) getDestServiceableDetail(ctx utils.LCOSContext, isOperation bool, serviceableType uint8, locationId *uint32, postcode *string, lineId string, collectDeliverGroupId string) (DestServiceableAbility, *lcos_error.LCOSError) {
	if serviceableType == constant.LOCATION {
		if locationId == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check destination location, but location_id is nil")
		}
		if isOperation {
			return checker.operationLocationDao.SearchLineOperationServiceableLocationDetailUseCache(ctx, lineId, *locationId, collectDeliverGroupId)
		} else {
			return checker.basicLocationDao.SearchBasicServiceableLocationDetailUseCache(ctx, lineId, *locationId, collectDeliverGroupId)
		}
	} else if serviceableType == constant.POSTCODE { // serviceableType == constant.POSTCODE
		if postcode == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check destination postcode, but postcode is nil")
		}
		if isOperation {
			return checker.operationPostcodeDao.SearchLineOperationServiceablePostcodeDetailUseCache(ctx, lineId, *postcode, collectDeliverGroupId)
		} else {
			return checker.basicPostcodeDao.SearchBasicServiceablePostcodeDetailUseCache(ctx, lineId, *postcode, collectDeliverGroupId)
		}
	} else if serviceableType == constant.CEPRANGE {
		if postcode == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "check dest cep range, but postcode is nil")
		}

		// 增加对postcode中存在非数字的处理逻辑
		var lcosErr *lcos_error.LCOSError
		*postcode, lcosErr = validatePostalCode(*postcode)
		if lcosErr != nil {
			return nil, lcosErr
		}

		cepRangeCode, err := strconv.Atoi(*postcode)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "postcode is not number")
		}
		if cepRangeCode < 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "postcode is negative number")
		}
		if isOperation {
			return checker.cepRangeDao.GetOperationCepRangeModelUseCache(ctx, lineId, collectDeliverGroupId, uint64(cepRangeCode))
		} else {
			return checker.cepRangeDao.GetBasicCepRangeModelUseCache(ctx, lineId, collectDeliverGroupId, uint64(cepRangeCode))
		}
	}
	return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "serviceableType is not valid")
}

func (checker *ServiceableCheckerService) checkLocationServiceableRoute(ctx utils.LCOSContext, lineId string, collectDeliverGroupId string, pickupInfo *pb.LocationInfo, deliverInfo *pb.LocationInfo, routeMode uint8) (bool, *lcos_error.LCOSError) {
	// 切换开关
	if switchConfig := internalConfig.GetCheckLocationServiceableRoute(ctx); switchConfig {
		if doubleCall := internalConfig.GetCheckLocationServiceableRouteDouble(ctx); doubleCall {
			//_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleCheckLocationServiceableRoute, "requestV1V2", "0", "")
			v1Result, err1 := checker.checkLocationServiceableRouteV1(ctx, lineId, collectDeliverGroupId, pickupInfo, deliverInfo, routeMode)
			v2Result, err2 := checker.checkLocationServiceableRouteV2(ctx, lineId, collectDeliverGroupId, pickupInfo, deliverInfo, routeMode)
			if v1Result != v2Result {
				//_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleCheckLocationServiceableRoute, "diff", "0", "")
				return v1Result, err1
			} else {
				return v2Result, err2
			}
		} else {
			//_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleCheckLocationServiceableRoute, "requestV2", "0", "")
			return checker.checkLocationServiceableRouteV2(ctx, lineId, collectDeliverGroupId, pickupInfo, deliverInfo, routeMode)
		}
	} else {
		return checker.checkLocationServiceableRouteV1(ctx, lineId, collectDeliverGroupId, pickupInfo, deliverInfo, routeMode)
	}
}

// checkLocationServiceableRouteV1
func (checker *ServiceableCheckerService) checkLocationServiceableRouteV1(ctx utils.LCOSContext, lineId string, collectDeliverGroupId string, pickupInfo *pb.LocationInfo, deliverInfo *pb.LocationInfo, routeMode uint8) (bool, *lcos_error.LCOSError) {
	areaLocationRefs := checker.getAreaByLocationId(ctx, pickupInfo)
	deliverLocationRefs := checker.getAreaByLocationId(ctx, deliverInfo)
	if len(areaLocationRefs) == 0 || len(deliverLocationRefs) == 0 {
		if routeMode == constant.ROUTE_UNSUPPORTED {
			return true, nil
		}
		return false, nil
	}
	if routeMode == constant.ROUTE_UNSUPPORTED {
		for _, areaLocationRef := range areaLocationRefs {
			for _, deliverLocationRef := range deliverLocationRefs {
				if exist, lcosErr := checker.routeDao.CheckLineServiceableRouteBlacklistExistUseCache(ctx, lineId, collectDeliverGroupId, areaLocationRef.AreaID, deliverLocationRef.AreaID); lcosErr == nil && exist {
					return false, nil
				}
			}
		}
		return true, nil
	} else if routeMode == constant.ROUTE_SUPPORTED {
		for _, areaLocationRef := range areaLocationRefs {
			for _, deliverLocationRef := range deliverLocationRefs {
				if exist, lcosErr := checker.routeDao.CheckLineServiceableRouteExistUseCache(ctx, lineId, collectDeliverGroupId, areaLocationRef.AreaID, deliverLocationRef.AreaID); lcosErr == nil && exist {
					return true, nil
				}
			}
		}
	}
	return false, nil
}

// checkLocationServiceableRouteV2
func (checker *ServiceableCheckerService) checkLocationServiceableRouteV2(ctx utils.LCOSContext, lineId string, collectDeliverGroupId string, pickupInfo *pb.LocationInfo, deliverInfo *pb.LocationInfo, routeMode uint8) (bool, *lcos_error.LCOSError) {
	areaLocationRefs := checker.getAreaByLocationId(ctx, pickupInfo)
	deliverLocationRefs := checker.getAreaByLocationId(ctx, deliverInfo)

	if len(areaLocationRefs) == 0 || len(deliverLocationRefs) == 0 {
		if routeMode == constant.ROUTE_UNSUPPORTED {
			return true, nil
		}
		return false, nil
	}
	if routeMode == constant.ROUTE_UNSUPPORTED {
		// 步骤1：先判断 lineId + collectDeliverGroupId 是否存在
		globalExist, _ := checker.routeDao.CheckLineServiceableRouteBlacklistExistUseCache(ctx, lineId, collectDeliverGroupId, constant.FromAreaPlaceholder, constant.ToAreaPlaceholder)
		if globalExist {
			for _, areaLocationRef := range areaLocationRefs {
				// 步骤2：判断 lineId + collectDeliverGroupId + from_area_id 是否存在
				fromAreaExist, _ := checker.routeDao.CheckLineServiceableRouteBlacklistExistUseCache(ctx, lineId, collectDeliverGroupId, areaLocationRef.AreaID, constant.ToAreaPlaceholder)
				if fromAreaExist {
					// 步骤3：判断 lineId + collectDeliverGroupId + from_area_id + to_area_id 是否存在
					for _, deliverLocationRef := range deliverLocationRefs {
						if exist, lcosErr := checker.routeDao.CheckLineServiceableRouteBlacklistExistUseCache(ctx, lineId, collectDeliverGroupId, areaLocationRef.AreaID, deliverLocationRef.AreaID); lcosErr == nil && exist {
							Logger.OpsLogThreePLZoneRouteServiceableAreaData(ctx,
								Logger.ThreePLZoneRouteServiceableAreaParams{
									LineId:                lineId,
									CollectDeliverGroupId: collectDeliverGroupId,
									FromAreaId:            areaLocationRef.AreaName,
									ToAreaId:              deliverLocationRef.AreaName,
									RouteType:             int(routeMode),
								},
								Logger.ThreePLServiceableAreaResult{})
							return false, nil
						}
					}
				}
			}
		}
		Logger.OpsLogThreePLZoneRouteServiceableAreaData(ctx,
			Logger.ThreePLZoneRouteServiceableAreaParams{
				LineId:                lineId,
				CollectDeliverGroupId: collectDeliverGroupId,
				RouteType:             int(routeMode),
			},
			Logger.ThreePLServiceableAreaResult{})
		return true, nil
	} else if routeMode == constant.ROUTE_SUPPORTED {
		for _, areaLocationRef := range areaLocationRefs {
			// 步骤1：先判断 lineId + collectDeliverGroupId + from_area_id 是否存在
			fromExists, _ := checker.routeDao.CheckLineServiceableRouteExistUseCache(ctx, lineId, collectDeliverGroupId, areaLocationRef.AreaID, constant.ToAreaPlaceholder)
			if fromExists {
				for _, deliverLocationRef := range deliverLocationRefs {
					// 步骤2：判断 lineId + collectDeliverGroupId + from_area_id + to_area_id 是否存在
					if exist, lcosErr := checker.routeDao.CheckLineServiceableRouteExistUseCache(ctx, lineId, collectDeliverGroupId, areaLocationRef.AreaID, deliverLocationRef.AreaID); lcosErr == nil && exist {
						Logger.OpsLogThreePLZoneRouteServiceableAreaData(ctx,
							Logger.ThreePLZoneRouteServiceableAreaParams{
								LineId:                lineId,
								CollectDeliverGroupId: collectDeliverGroupId,
								FromAreaId:            areaLocationRef.AreaName,
								ToAreaId:              deliverLocationRef.AreaName,
								RouteType:             int(routeMode),
							},
							Logger.ThreePLServiceableAreaResult{})
						return true, nil
					}
				}
			}
		}
	}
	Logger.OpsLogThreePLZoneRouteServiceableAreaData(ctx,
		Logger.ThreePLZoneRouteServiceableAreaParams{
			LineId:                lineId,
			CollectDeliverGroupId: collectDeliverGroupId,
			RouteType:             int(routeMode),
		},
		Logger.ThreePLServiceableAreaResult{})
	return false, nil
}

func (checker *ServiceableCheckerService) checkOperationServiceableRoute(ctx utils.LCOSContext, lineId string, collectDeliverGroupId string, pickupInfo *pb.LocationInfo, deliverInfo *pb.LocationInfo, routeMode uint8) (bool, *lcos_error.LCOSError) {
	// 切换开关
	if switchConfig := internalConfig.GetCheckOperationServiceableRoute(ctx); switchConfig {
		if doubleCall := internalConfig.GetCheckOperationServiceableRouteDouble(ctx); doubleCall {
			//_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleCheckOperationServiceableRoute, "requestV1V2", "0", "")
			v1Result, err1 := checker.checkOperationServiceableRouteV1(ctx, lineId, collectDeliverGroupId, pickupInfo, deliverInfo, routeMode)
			v2Result, err2 := checker.checkOperationServiceableRouteV2(ctx, lineId, collectDeliverGroupId, pickupInfo, deliverInfo, routeMode)
			if v1Result != v2Result {
				//_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleCheckOperationServiceableRoute, "diff", "0", "")
				return v1Result, err1
			} else {
				return v2Result, err2
			}
		} else {
			//_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleCheckOperationServiceableRoute, "requestV2", "0", "")
			return checker.checkOperationServiceableRouteV2(ctx, lineId, collectDeliverGroupId, pickupInfo, deliverInfo, routeMode)
		}
	} else {
		return checker.checkOperationServiceableRouteV1(ctx, lineId, collectDeliverGroupId, pickupInfo, deliverInfo, routeMode)
	}
}

// checkOperationServiceableRouteV1
func (checker *ServiceableCheckerService) checkOperationServiceableRouteV1(ctx utils.LCOSContext, lineId string, collectDeliverGroupId string, pickupInfo *pb.LocationInfo, deliverInfo *pb.LocationInfo, routeMode uint8) (bool, *lcos_error.LCOSError) {
	areaLocationRefs := checker.getAreaByLocationId(ctx, pickupInfo)
	deliverLocationRefs := checker.getAreaByLocationId(ctx, deliverInfo)
	if len(areaLocationRefs) == 0 || len(deliverLocationRefs) == 0 {
		return false, nil
	}

	for _, areaLocationRef := range areaLocationRefs {
		for _, deliverLocationRef := range deliverLocationRefs {
			if exist, lcosErr := checker.opsRouteDao.CheckLineOperationRouteExistUseCache(ctx, lineId, collectDeliverGroupId, areaLocationRef.AreaID, deliverLocationRef.AreaID); lcosErr == nil && exist {
				Logger.OpsLogOperationZoneRouteServiceableAreaData(ctx,
					Logger.OperationZoneRouteServiceableAreaParams{
						LineId:                lineId,
						CollectDeliverGroupId: collectDeliverGroupId,
						DisableFromAreaId:     areaLocationRef.AreaName,
						DisableToAreaId:       deliverLocationRef.AreaName,
					},
					Logger.OperationServiceableAreaResult{})
				return true, nil
			}
		}
	}
	Logger.OpsLogOperationZoneRouteServiceableAreaData(ctx,
		Logger.OperationZoneRouteServiceableAreaParams{
			LineId:                lineId,
			CollectDeliverGroupId: collectDeliverGroupId,
		},
		Logger.OperationServiceableAreaResult{
			Error: "zone or route black list not match",
		})
	return false, nil
}

// checkOperationServiceableRouteV2
func (checker *ServiceableCheckerService) checkOperationServiceableRouteV2(ctx utils.LCOSContext, lineId string, collectDeliverGroupId string, pickupInfo *pb.LocationInfo, deliverInfo *pb.LocationInfo, routeMode uint8) (bool, *lcos_error.LCOSError) {
	areaLocationRefs := checker.getAreaByLocationId(ctx, pickupInfo)
	deliverLocationRefs := checker.getAreaByLocationId(ctx, deliverInfo)
	if len(areaLocationRefs) == 0 || len(deliverLocationRefs) == 0 {
		return false, nil
	}

	// 步骤1：先判断 lineId + collectDeliverGroupId 是否存在
	globalExist, _ := checker.opsRouteDao.CheckLineOperationRouteExistUseCache(ctx, lineId, collectDeliverGroupId, constant.FromAreaPlaceholder, constant.ToAreaPlaceholder)
	if globalExist {
		for _, areaLocationRef := range areaLocationRefs {
			fromAreaExist, _ := checker.opsRouteDao.CheckLineOperationRouteExistUseCache(ctx, lineId, collectDeliverGroupId, areaLocationRef.AreaID, constant.ToAreaPlaceholder)
			// 步骤2：判断 line_id + group_id + disable_from_area_id 维度是否存在
			if fromAreaExist {
				// 步骤3：判断 line_id + group_id + disable_from_area_id + disable_to_area_id 维度是否存在
				for _, deliverLocationRef := range deliverLocationRefs {
					exist, lcosErr := checker.opsRouteDao.CheckLineOperationRouteExistUseCache(ctx, lineId, collectDeliverGroupId, areaLocationRef.AreaID, deliverLocationRef.AreaID)
					if lcosErr == nil && exist {
						Logger.OpsLogOperationZoneRouteServiceableAreaData(ctx,
							Logger.OperationZoneRouteServiceableAreaParams{
								LineId:                lineId,
								CollectDeliverGroupId: collectDeliverGroupId,
								DisableFromAreaId:     areaLocationRef.AreaName,
								DisableToAreaId:       deliverLocationRef.AreaName,
							},
							Logger.OperationServiceableAreaResult{})
						return true, nil
					}
				}
			}
		}
	}
	Logger.OpsLogOperationZoneRouteServiceableAreaData(ctx,
		Logger.OperationZoneRouteServiceableAreaParams{
			LineId:                lineId,
			CollectDeliverGroupId: collectDeliverGroupId,
		},
		Logger.OperationServiceableAreaResult{
			Error: "zone or route black list not match",
		})
	return false, nil
}

// area类型也要支持向上查找
func (checker *ServiceableCheckerService) getAreaByLocationId(ctx utils.LCOSContext, locationInfo *pb.LocationInfo) []*area_location_ref.LogisticLineServiceableAreaLocationRefTab {
	var areaLocationRefs []*area_location_ref.LogisticLineServiceableAreaLocationRefTab
	var locationIdList []uint64
	if locationInfo.StateLocationId != nil {
		locationIdList = append(locationIdList, uint64(*locationInfo.StateLocationId))
	}
	if locationInfo.CityLocationId != nil {
		locationIdList = append(locationIdList, uint64(*locationInfo.CityLocationId))
	}
	if locationInfo.DistrictLocationId != nil {
		locationIdList = append(locationIdList, uint64(*locationInfo.DistrictLocationId))
	}

	areaLocationRefs, err := checker.areaLocationDao.GetLogisticLineServiceableAreaLocationRefTabByLocationIdListUseCache(ctx, locationIdList)
	if err != nil {
		return []*area_location_ref.LogisticLineServiceableAreaLocationRefTab{}
	}
	return areaLocationRefs
}

// Deprecated
// 通过场景参数校验买卖家
func (checker *ServiceableCheckerService) CheckServiceable(ctx utils.LCOSContext, request *pb.CheckLineServiceableAreaRequest) (*pb.CheckLineServiceableAreaResponse, *lcos_error.LCOSError) {
	getRequest := convertCheckRequestToGet(request)
	serviceableInfo, err := checker.GetServiceable(ctx, getRequest)
	if err != nil {
		return &pb.CheckLineServiceableAreaResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}
	logger.LogInfof("get serviceable_area detail, can_pickup: %d, can_cod_pickup: %d, can_deliver: %d, can_cod_pickup: %d", *serviceableInfo.CanPickup, *serviceableInfo.CanCodPickup, *serviceableInfo.CanDeliver, *serviceableInfo.CanCodDeliver)
	if *request.BaseInfo.PaymentMethod == uint32(constant.STANDARD) {
		if *serviceableInfo.CanPickup == uint32(constant.DISABLED) {
			return &pb.CheckLineServiceableAreaResponse{
				RespHeader: http.GrpcErrorRespHeader(lcos_error.NotSupportPickupErrorCode),
			}, nil
		}
		if *serviceableInfo.CanDeliver == uint32(constant.DISABLED) {
			return &pb.CheckLineServiceableAreaResponse{
				RespHeader: http.GrpcErrorRespHeader(lcos_error.NotSupportDeliverErrorCode),
			}, nil
		}
	} else {
		if *serviceableInfo.CanCodPickup == uint32(constant.DISABLED) {
			return &pb.CheckLineServiceableAreaResponse{
				RespHeader: http.GrpcErrorRespHeader(lcos_error.NotSupportCodPickupErrorCode),
			}, nil
		}
		if *serviceableInfo.CanCodDeliver == uint32(constant.DISABLED) {
			return &pb.CheckLineServiceableAreaResponse{
				RespHeader: http.GrpcErrorRespHeader(lcos_error.NotSupportCodDeliverErrorCode),
			}, nil
		}
	}
	return &pb.CheckLineServiceableAreaResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

func (checker *ServiceableCheckerService) CheckServiceable2(ctx utils.LCOSContext, request *pb.CheckLineServiceableAreaRequest2) (*pb.CheckLineServiceableAreaResponse, *lcos_error.LCOSError) {
	// check参数转换为get参数
	getRequest := convertCheckRequestToGetRequest(request)
	serviceableInfo, reason, err := checker.GetServiceableWithCheckFlag(ctx, getRequest)
	if err != nil {
		return &pb.CheckLineServiceableAreaResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}
	logger.LogInfof("get serviceable_area detail, can_pickup: %d, can_cod_pickup: %d, can_deliver: %d, can_cod_pickup: %d", *serviceableInfo.CanPickup, *serviceableInfo.CanCodPickup, *serviceableInfo.CanDeliver, *serviceableInfo.CanCodDeliver)
	if *request.BaseInfo.PaymentMethod == uint32(constant.STANDARD) {
		if *serviceableInfo.CanPickup == uint32(constant.DISABLED) {
			return &pb.CheckLineServiceableAreaResponse{
				RespHeader: http.GrpcErrorRespHeader(reason.GetErrorcode("pickup", lcos_error.NotSupportPickupErrorCode)),
			}, nil
		}
		if *serviceableInfo.CanDeliver == uint32(constant.DISABLED) {
			return &pb.CheckLineServiceableAreaResponse{
				RespHeader: http.GrpcErrorRespHeader(reason.GetErrorcode("deliver", lcos_error.NotSupportDeliverErrorCode)),
			}, nil
		}
	} else {
		if *serviceableInfo.CanCodPickup == uint32(constant.DISABLED) {
			return &pb.CheckLineServiceableAreaResponse{
				RespHeader: http.GrpcErrorRespHeader(reason.GetErrorcode("codPickup", lcos_error.NotSupportCodPickupErrorCode)),
			}, nil
		}
		if *serviceableInfo.CanCodDeliver == uint32(constant.DISABLED) {
			return &pb.CheckLineServiceableAreaResponse{
				RespHeader: http.GrpcErrorRespHeader(reason.GetErrorcode("codDeliver", lcos_error.NotSupportCodDeliverErrorCode)),
			}, nil
		}
	}
	return &pb.CheckLineServiceableAreaResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

// 批量校验线的服务范围
func (checker *ServiceableCheckerService) BatchCheckServiceable(ctx utils.LCOSContext, request *pb.BatchCheckLineServiceableAreaRequest) (*pb.BatchCheckLineServiceableAreaResponse, *lcos_error.LCOSError) {
	checkReqList := request.CheckReqList
	// 存放批量结果列表
	checkRespList := getBatchCheckServiceableRespList(checkReqList, request.ReqHeader, ctx, checker)
	return &pb.BatchCheckLineServiceableAreaResponse{
		RespHeader:    http.GrpcSuccessRespHeader(),
		CheckRespList: checkRespList,
	}, nil
}

// 批量校验线的服务范围
func (checker *ServiceableCheckerService) MultipleBatchCheckLineServiceableArea(ctx utils.LCOSContext, request *pb.MultipleBatchCheckLineServiceableAreaRequest) (*pb.MultipleBatchCheckLineServiceableAreaResponse, *lcos_error.LCOSError) {
	orderRespMap := make(map[string]*pb.BatchCheckLineServiceableAreaResponse)
	checkReqList := request.OrderReqList
	rspHeader := http.GrpcSuccessRespHeader()
	rspHeader.RequestId = request.ReqHeader.RequestId
	for _, orderReq := range checkReqList {
		checkReqList := orderReq.CheckReqList
		// 存放批量结果列表
		checkRespList := getBatchCheckServiceableRespList(checkReqList, request.ReqHeader, ctx, checker)
		orderRespMap[*orderReq.ReqNo] = &pb.BatchCheckLineServiceableAreaResponse{
			RespHeader:    rspHeader,
			CheckRespList: checkRespList,
		}
	}
	return &pb.MultipleBatchCheckLineServiceableAreaResponse{
		RespHeader:   rspHeader,
		OrderRespMap: orderRespMap,
	}, nil
}

// 批量校验线的服务范围
func (checker *ServiceableCheckerService) BatchCheckLineServiceableArea2(ctx utils.LCOSContext, request *pb.BatchCheckLineServiceableAreaRequest2) (*pb.BatchCheckLineServiceableAreaResponse2, *lcos_error.LCOSError) {
	orderRespMap := make(map[string]*pb.BatchCheckLineServiceableAreaResponse)
	checkReqList := request.OrderReqList
	rspHeader := http.GrpcSuccessRespHeader()
	rspHeader.RequestId = request.ReqHeader.RequestId
	for _, orderReq := range checkReqList {
		baseInfoList := orderReq.GetBaseInfo()
		checkRespList := make([]*pb.SingleCheckServiceableResponse, 0, len(checkReqList))
		for _, baseInfo := range baseInfoList {
			checkLineServiceableAreaReq2 := &pb.CheckLineServiceableAreaRequest2{
				ReqHeader:   request.GetReqHeader(),
				BaseInfo:    baseInfo,
				PickupInfo:  orderReq.PickupInfo,
				DeliverInfo: orderReq.DeliverInfo,
			}
			serviceableInfo, err := checker.CheckServiceable2(ctx, checkLineServiceableAreaReq2)
			if err != nil {
				_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(err.RetCode)), "line": *baseInfo.LineId})
				checkRespList = append(checkRespList, &pb.SingleCheckServiceableResponse{
					LineId:      baseInfo.LineId,
					ItemRetCode: &err.RetCode,
					ItemRetMsg:  &err.Msg,
				})
			} else {
				_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(*serviceableInfo.RespHeader.Retcode)), "line": *baseInfo.LineId})
				checkRespList = append(checkRespList, &pb.SingleCheckServiceableResponse{
					LineId:      baseInfo.LineId,
					ItemRetCode: serviceableInfo.RespHeader.Retcode,
					ItemRetMsg:  serviceableInfo.RespHeader.Message,
				})
			}
		}
		orderRespMap[*orderReq.ReqNo] = &pb.BatchCheckLineServiceableAreaResponse{
			RespHeader:    rspHeader,
			CheckRespList: checkRespList,
		}
	}
	return &pb.BatchCheckLineServiceableAreaResponse2{
		RespHeader:   rspHeader,
		OrderRespMap: orderRespMap,
	}, nil
}

// 通过collect deliver type获取collectDeliverGroupId
func (checker *ServiceableCheckerService) GetCollectDeliverGroupIdByCollectDeliverType(ctx utils.LCOSContext, lineId string, collectType *uint32, deliverType *uint32) (*string, *lcos_error.LCOSError) {
	collectGroupIds, err := checker.collectDeliverGroupDao.GetConfsByCollectDeliverTypeUseCache(ctx, *collectType, *deliverType)
	if err != nil {
		return nil, err
	}
	if len(collectGroupIds) <= 0 {
		logger.LogInfof("can not find collect deliver group by collectType=%v, deliverType=%v", collectType, deliverType)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoudCollectDeliverGroupIdErrorCode, fmt.Sprintf("can not find collect deliver group by collectType=%v, deliverType=%v", *collectType, *deliverType))
	}

	lineBindCollectGroupIds, err := checker.collectDeliverGroupRefDao.GetGroupIdListByLineIdUseCache(ctx, lineId)
	if err != nil {
		return nil, err
	}
	var collectGroupId *string
	hitCount := 0
	for _, lineBindCollectGroupId := range lineBindCollectGroupIds {
		for _, groupId := range collectGroupIds {
			if groupId == lineBindCollectGroupId {
				hitGroupId := groupId
				collectGroupId = &hitGroupId
				hitCount++
			}
		}
	}
	if hitCount != 1 {
		logger.LogInfof("mis hit collect deliver group, lineid=%s, collectType=%v,deliverType=%v, collectGroupIds=%v, lineBindCollectGroupIds=%v", lineId, collectType, deliverType, collectGroupIds, lineBindCollectGroupIds)
		return nil, lcos_error.NewLCOSError(lcos_error.NotHitCollectDeliverGroupIdErrorCode, fmt.Sprintf("not hit collect deliver group|lineid=%s, collectType=%v,deliverType=%v, "+
			"searchCollectGroupIds=%v, lineBindCollectGroupIds=%v", lineId, *collectType, *deliverType, collectGroupIds, lineBindCollectGroupIds))
	}
	return collectGroupId, nil
}

// Deprecated
// v1版本的携带场景配置的request，后面使用 convertCheckRequestToGetRequest
func (checker *ServiceableCheckerService) CheckFmServiceableArea(ctx utils.LCOSContext, request *pb.CheckFmServiceableAreaRequest) (*pb.CheckFmServiceableAreaResponse, *lcos_error.LCOSError) {
	basicConf, err := checker.basicConfDao.GetBasicServiceableConfModelByLineIdUseCache(ctx, *request.LineId)
	if err != nil {
		return nil, err
	}
	if basicConf == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicConfErrorCode)
	}

	if basicConf.CollectDeliverAbility&*request.CollectType == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.FmAbilityErrorCode, "line collect ability not support")
	}

	return &pb.CheckFmServiceableAreaResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

func (checker *ServiceableCheckerService) CheckShopServiceable(ctx utils.LCOSContext, request *pb.CheckShopServiceableReq) (*pb.CheckShopServiceableResp, *lcos_error.LCOSError) {
	ability, err := checker.shopSaChecker.CheckShopServiceable(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "check shop serviceable error|cause=%s", err.Msg)
		return &pb.CheckShopServiceableResp{
			UniqueId:    request.UniqueId,
			ProductId:   request.ProductId,
			ShopId:      request.ShopId,
			CanPickup:   utils.NewBool(false),
			PickupZone:  utils.NewString(""),
			CanDelvier:  utils.NewBool(false),
			DeliverZone: utils.NewString(""),
		}, nil
	}
	return &pb.CheckShopServiceableResp{
		UniqueId:    request.UniqueId,
		ProductId:   request.ProductId,
		ShopId:      request.ShopId,
		CanPickup:   utils.NewBool(ability.CanPickup),
		PickupZone:  utils.NewString(ability.PickupZone),
		CanDelvier:  utils.NewBool(ability.CanDeliver),
		DeliverZone: utils.NewString(ability.DeliverZone),
	}, nil
}

func (checker *ServiceableCheckerService) SearchProductServiceableZone(ctx utils.LCOSContext, request *pb.SearchProductServiceableZoneAddress) (*pb.SearchProductServiceableZoneResult, *lcos_error.LCOSError) {
	zoneName, err := checker.shopSaChecker.SearchProductServiceableZone(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "search product serviceable zone error|cause=%s", err.Msg)
		return &pb.SearchProductServiceableZoneResult{
			UniqueId: request.UniqueId,
			Retcode:  utils.NewInt32(-1),
			Message:  utils.NewString(err.Msg),
			ZoneName: utils.NewString(""),
		}, nil
	}
	return &pb.SearchProductServiceableZoneResult{
		UniqueId: request.UniqueId,
		Retcode:  utils.NewInt32(0),
		Message:  utils.NewString("success"),
		ZoneName: utils.NewString(zoneName),
	}, nil
}

func (checker *ServiceableCheckerService) BatchGetProductServiceableRouteCode(ctx utils.LCOSContext, reqList []*pb.GetProductServiceableRouteCodeReq) ([]*pb.GetProductServiceableRouteCodeResp, *lcos_error.LCOSError) {
	// 1. 批量获取链路信息
	var (
		regionLaneCodesMap = make(map[string][]string)
		laneInfoMap        = make(map[string]*lfs_service.LaneCodeStruct)
	)
	for _, req := range reqList {
		regionLaneCodesMap[req.GetRegion()] = append(regionLaneCodesMap[req.GetRegion()], req.GetLaneCodes()...)
	}
	for region, laneCodes := range regionLaneCodesMap {
		laneInfoSubMap, err := lfs_service.NewLFSService(ctx, region).GetLaneCodeMapWithGreySwitch(ctx, laneCodes)
		if err != nil {
			return nil, err
		}
		for laneCode, laneInfo := range laneInfoSubMap {
			laneInfoMap[laneCode] = laneInfo
		}
	}

	// 2. 遍历请求获取lane维度可用的route code
	respList := make([]*pb.GetProductServiceableRouteCodeResp, 0, len(reqList))
	for _, req := range reqList {
		resp := &pb.GetProductServiceableRouteCodeResp{
			UniqueId:                  req.UniqueId,
			ProductId:                 req.ProductId,
			LaneServiceableRouteCodes: make([]*pb.LaneServiceableRouteCodeInfo, 0, len(req.GetLaneCodes())),
		}
		for _, laneCode := range req.GetLaneCodes() {
			laneInfo, ok := laneInfoMap[laneCode]
			if !ok {
				logger.CtxLogErrorf(ctx, "get lane serviceable route code error|lane_code=%s, cause=lane not found", laneCode)
				continue
			}
			if laneInfo.ErrCode != int(lcos_error.SuccessCode) {
				logger.CtxLogErrorf(ctx, "get lane serviceable route code error|lane_code=%s, cause=%s", laneCode, laneInfo.Message)
				continue
			}
			routeCodes, err := checker.getLaneServiceableRouteCode(ctx, laneInfo)
			if err != nil {
				logger.CtxLogErrorf(ctx, "get lane serviceable route code error|lane_code=%s, cause=%s", laneCode, err.Msg)
				continue
			}

			resp.LaneServiceableRouteCodes = append(resp.LaneServiceableRouteCodes, &pb.LaneServiceableRouteCodeInfo{
				LaneCode:   proto.String(laneCode),
				RouteCodes: routeCodes,
			})
		}
		respList = append(respList, resp)
	}
	return respList, nil
}

func (checker *ServiceableCheckerService) filterServiceablePredefinedRoute(ctx utils.LCOSContext, predefinedRoutes []string, lineId, groupId string, pickupAddr, deliverAddr *pb.LocationInfo) *lcos_error.LCOSError {
	// 1. 参数校验
	if pickupAddr == nil || deliverAddr == nil {
		return lcos_error.NewLCOSError(lcos_error.PredefinedRouteCheckErrorCode, "pickup or deliver address is empty")
	}
	if len(predefinedRoutes) == 0 {
		return lcos_error.NewLCOSError(lcos_error.PredefinedRouteCheckErrorCode, "no serviceable predefined route")
	}

	// 2. 获取pickup和deliver地址所属的area列表
	var (
		predefinedRouteMap = make(map[string]struct{})
		fromAreaList       = checker.getAreaByLocationId(ctx, pickupAddr)
		toAreaList         = checker.getAreaByLocationId(ctx, deliverAddr)
	)
	for _, predefinedRoute := range predefinedRoutes {
		predefinedRouteMap[predefinedRoute] = struct{}{}
	}
	if len(fromAreaList) == 0 {
		return lcos_error.NewLCOSError(lcos_error.PredefinedRouteCheckErrorCode, "pickup addr cannot match any area")
	}
	if len(toAreaList) == 0 {
		return lcos_error.NewLCOSError(lcos_error.PredefinedRouteCheckErrorCode, "deliver addr cannot match any area")
	}

	// 3. 检查用户选择的route是否与揽派地址匹配
	for _, fromArea := range fromAreaList {
		for _, toArea := range toAreaList {
			hitRoute, err := checker.predefinedRouteDao.GetLinePredefinedRouteByRouteUsingCache(ctx, lineId, groupId, fromArea.AreaID, toArea.AreaID)
			if err != nil || hitRoute == nil {
				// 揽派地址无法命中route
				continue
			}
			if _, ok := predefinedRouteMap[hitRoute.RouteCode]; ok {
				// 揽派地址命中了用户选择的route
				return nil
			}
			if _, ok := predefinedRouteMap[serviceable_constant.PredefinedRouteAll]; ok {
				// 揽派地址能命中route，同时用户选择了'All' predefined route
				return nil
			}
		}
	}
	return lcos_error.NewLCOSError(lcos_error.PredefinedRouteCheckErrorCode, "no serviceable predefined route")
}

func (checker *ServiceableCheckerService) getLaneServiceableRouteCode(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct) ([]string, *lcos_error.LCOSError) {
	var (
		routeCodes    []string
		routeCodesMap = make(map[string]struct{}) // 用于去重
	)
	for _, compose := range laneInfo.Composes {
		if !compose.IsLine() {
			// 不是线角色可以跳过
			continue
		}

		lineRouteCodes, err := checker.getLineServiceableRouteCode(ctx, compose.ResourceID)
		if err != nil {
			return nil, err
		}
		for _, routeCode := range lineRouteCodes {
			if _, ok := routeCodesMap[routeCode]; !ok {
				routeCodes = append(routeCodes, routeCode)
				routeCodesMap[routeCode] = struct{}{}
			}
		}
	}
	return routeCodes, nil
}

func (checker *ServiceableCheckerService) getLineServiceableRouteCode(ctx utils.LCOSContext, lineId string) ([]string, *lcos_error.LCOSError) {
	groupIdList, err := checker.collectDeliverGroupRefDao.GetGroupIdListByLineIdUseCache(ctx, lineId)
	if err != nil {
		return nil, err
	}

	var (
		routeCodes    []string
		routeCodesMap = make(map[string]struct{}) // 用于去重
	)
	for _, groupId := range groupIdList {
		predefinedRouteMap, err := checker.predefinedRouteDao.GetLinePredefinedRouteListByLineAndGroupUsingCache(ctx, lineId, groupId)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get line serviceable predefined route code error|line_id=%s, group_id=%s, cause=%s", lineId, groupId, err.Msg)
			continue
		}
		for _, predefinedRoute := range predefinedRouteMap {
			if _, ok := routeCodesMap[predefinedRoute.RouteCode]; !ok {
				routeCodes = append(routeCodes, predefinedRoute.RouteCode)
				routeCodesMap[predefinedRoute.RouteCode] = struct{}{}
			}
		}
	}
	return routeCodes, nil
}

func convertCheckRequestToGet(req *pb.CheckLineServiceableAreaRequest) *pb.GetLineServiceableInfoRequest {
	baseInfo := &pb.GetServiceableInfoBase{
		LineId:           req.BaseInfo.LineId,
		IsCheckOperation: req.BaseInfo.IsCheckOperation,
		IsCheckBasic:     req.BaseInfo.IsCheckBasic,
		CollectType:      req.BaseInfo.CollectType,
		DeliverType:      req.BaseInfo.DeliverType,
		Scenario:         req.BaseInfo.Scenario,
	}
	return &pb.GetLineServiceableInfoRequest{
		ReqHeader:   req.ReqHeader,
		BaseInfo:    baseInfo,
		PickupInfo:  req.PickupInfo,
		DeliverInfo: req.DeliverInfo,
	}
}

// tips：check参数转换为get参数
func convertCheckRequestToGetRequest(req *pb.CheckLineServiceableAreaRequest2) *pb.GetLineServiceableInfoRequest2 {
	baseInfo := &pb.GetServiceableInfoBase2{
		LineId:               req.BaseInfo.LineId,
		IsCheckOperation:     req.BaseInfo.IsCheckOperation,
		IsCheckBasic:         req.BaseInfo.IsCheckBasic,
		CollectType:          req.BaseInfo.CollectType,
		DeliverType:          req.BaseInfo.DeliverType,
		CheckSender:          req.BaseInfo.CheckSender,
		SenderCheckLevel:     req.BaseInfo.SenderCheckLevel,
		CheckReceiver:        req.BaseInfo.CheckReceiver,
		ReceiverCheckLevel:   req.BaseInfo.ReceiverCheckLevel,
		SkipPostcode:         req.BaseInfo.SkipPostcode,
		SkipZoneRoute:        req.BaseInfo.SkipZoneRoute,
		SkipElectricFence:    req.BaseInfo.SkipElectricFence,
		UseElectricFence:     req.BaseInfo.UseElectricFence,
		CheckPredefinedRoute: req.BaseInfo.CheckPredefinedRoute,
		PredefinedRouteCodes: req.BaseInfo.PredefinedRouteCodes,
	}
	return &pb.GetLineServiceableInfoRequest2{
		ReqHeader:   req.ReqHeader,
		BaseInfo:    baseInfo,
		PickupInfo:  req.PickupInfo,
		DeliverInfo: req.DeliverInfo,
	}
}

// tips：批量singleReq转换为CheckReq参数
func convertBatchSingleCheckReqToCheckReq(reqHeader *pb.ReqHeader, req *pb.SingleCheckServiceableAreaRequest) *pb.CheckLineServiceableAreaRequest2 {
	checkLineServiceableAreaReq2 := &pb.CheckLineServiceableAreaRequest2{
		ReqHeader:   reqHeader,
		BaseInfo:    req.BaseInfo,
		PickupInfo:  req.PickupInfo,
		DeliverInfo: req.DeliverInfo,
	}
	return checkLineServiceableAreaReq2
}

// 批量CheckServiceable2，通用方法抽取，MultipleBatchCheckLineServiceableArea和BatchCheckLineServiceableArea方法都会有用到
func getBatchCheckServiceableRespList(checkReqList []*pb.SingleCheckServiceableAreaRequest, reqHeader *pb.ReqHeader, ctx utils.LCOSContext, checker *ServiceableCheckerService) []*pb.SingleCheckServiceableResponse {
	// 存放批量结果列表
	checkRespList := make([]*pb.SingleCheckServiceableResponse, 0, len(checkReqList))
	for _, singleCheckReq := range checkReqList {
		// 批量check的singleReq参数转换为CheckReq
		checkLineServiceableAreaReq := convertBatchSingleCheckReqToCheckReq(reqHeader, singleCheckReq)
		serviceableInfo, err := checker.CheckServiceable2(ctx, checkLineServiceableAreaReq)
		if err != nil {
			_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(err.RetCode)), "line": *singleCheckReq.BaseInfo.LineId})
			checkRespList = append(checkRespList, &pb.SingleCheckServiceableResponse{
				LineId:      singleCheckReq.BaseInfo.LineId,
				ItemRetCode: &err.RetCode,
				ItemRetMsg:  &err.Msg,
			})
		} else {
			_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": ctx.GetUrl(), "rsp_status": strconv.Itoa(int(*serviceableInfo.RespHeader.Retcode)), "line": *singleCheckReq.BaseInfo.LineId})
			checkRespList = append(checkRespList, &pb.SingleCheckServiceableResponse{
				LineId:      singleCheckReq.BaseInfo.LineId,
				ItemRetCode: serviceableInfo.RespHeader.Retcode,
				ItemRetMsg:  serviceableInfo.RespHeader.Message,
			})
		}
	}
	return checkRespList
}

func convertEffectiveRuleToLaneRule(ruleMap map[string]*effective_rule.ServiceableEffectiveRuleTab, isLcosUse bool) []*pb.ServiceableRule {
	serviceableRuleList := make([]*pb.ServiceableRule, 0, len(ruleMap))

	for _, rule := range ruleMap {
		saRule := &pb.ServiceableRule{
			LaneCode: proto.String(rule.LaneCode),
		}
		sort.SliceStable(rule.LaneConn, func(i, j int) bool {
			return rule.LaneConn[i].Sequence < rule.LaneConn[j].Sequence
		})
		sort.SliceStable(rule.ServiceableRule, func(i, j int) bool {
			return rule.ServiceableRule[i].Sequence < rule.ServiceableRule[j].Sequence
		})
		details := make([]*pb.RuleDetail, 0, len(rule.LaneConn))
		for idx, compose := range rule.LaneConn {
			targetSaRule := rule.ServiceableRule[idx]
			tmpDetail := &pb.RuleDetail{
				Sequence:        proto.Int32(int32(compose.Sequence)),
				ResourceType:    proto.Int32(int32(compose.Type)),
				ResourceSubType: proto.Int32(int32(compose.SubType)),
				CheckSender:     proto.Int32(0),
				CheckReceiver:   proto.Int32(0),
				SenderUseLps:    proto.Int32(0),
				ReceiverUseLps:  proto.Int32(0),
			}
			// line check
			if targetSaRule.CheckCollectAddress == constant.AddrCheckLpsScene {
				tmpDetail.SenderUseLps = proto.Int32(1)
			}
			if targetSaRule.CheckDeliveryAddress == constant.AddrCheckLpsScene {
				tmpDetail.ReceiverUseLps = proto.Int32(1)
			}

			if targetSaRule.CheckCollectAddress == constant.AddrCheckEnabled && targetSaRule.CheckCollectAddressType == constant.AddrTypeUser {
				tmpDetail.CheckSender = proto.Int32(1)
			}
			if targetSaRule.CheckDeliveryAddress == constant.AddrCheckEnabled && targetSaRule.CheckDeliveryAddressType == constant.AddrTypeUser {
				tmpDetail.CheckReceiver = proto.Int32(1)
			}

			if targetSaRule.CheckCollectAddress == constant.AddrCheckEnabled && targetSaRule.CheckCollectAddressType == constant.AddrTypeHandoverPoint {
				tmpDetail.CheckSender = proto.Int32(1)
				tmpDetail.CheckSenderAddrType = proto.Int32(targetSaRule.CheckCollectAddressType)
			}
			if targetSaRule.CheckDeliveryAddress == constant.AddrCheckEnabled && targetSaRule.CheckDeliveryAddressType == constant.AddrTypeHandoverPoint {
				tmpDetail.CheckReceiver = proto.Int32(1)
				tmpDetail.CheckReceiverAddrType = proto.Int32(targetSaRule.CheckDeliveryAddressType)
			}

			// site sorting flag
			if targetSaRule.ApFiltrate == constant.ActualPointFiltrateOn {
				tmpDetail.SiteSortingFlag = proto.Int32(1)
				tmpDetail.SortingAsNextPickupFlag = proto.Int32(1)
				tmpDetail.SortingAsPreDeliverFlag = proto.Int32(1)
				if targetSaRule.FiltrateMode == constant.SinglePointMode {
					if targetSaRule.PointFiltrate == constant.PointAddrSeller {
						tmpDetail.SiteSortingPickupAddressFlag = proto.Int32(2)
						tmpDetail.SiteSortingDeliverAddressFlag = proto.Int32(2)
					}
					if targetSaRule.PointFiltrate == constant.PointAddrBuyer {
						tmpDetail.SiteSortingPickupAddressFlag = proto.Int32(1)
						tmpDetail.SiteSortingDeliverAddressFlag = proto.Int32(1)
					}
				} else {
					tmpDetail.SiteSortingPickupAddressFlag = proto.Int32(2)
					tmpDetail.SiteSortingDeliverAddressFlag = proto.Int32(1)
				}
			}
			details = append(details, tmpDetail)
		}
		// next/prev calculation
		for idx := 0; idx < len(details); idx++ {
			if details[idx].GetResourceType() != constant.LaneComposeSite {
				continue
			}
			pre := -1
			next := -1
			if idx > 0 {
				pre = idx - 1
			}
			if idx < len(details)-1 {
				next = idx + 1
			}
			if pre >= 0 && rule.ServiceableRule[pre].CheckDeliveryAddressType == constant.AddrTypeActualPoint {
				details[idx].PreResourceSaCheckFlag = proto.Int32(1)
				// 如果是内部使用，新增冗余字段；如果不是，不返回给LFS
				if isLcosUse {
					details[pre].NextResourceSaCheckFlag = proto.Int32(1)
				}

			}
			if next > 0 && rule.ServiceableRule[next].CheckCollectAddressType == constant.AddrTypeActualPoint {
				details[idx].NextResourceSaCheckFlag = proto.Int32(1)
				if isLcosUse {
					details[next].PreResourceSaCheckFlag = proto.Int32(1)
				}
			}
		}
		saRule.RuleDetail = details
		serviceableRuleList = append(serviceableRuleList, saRule)
	}

	return serviceableRuleList
}

func lineServiceableAreaConfLog(ctx context.Context, lineId string, basicConf *basic_conf.LineBasicServiceableConfTab,
	operationConf *operation_conf.LogisticLineOperationServiceableConfTab, err *lcos_error.LCOSError) {
	req := Logger.LineServiceableAreaParams{LineId: lineId}
	resp := Logger.LineServiceableAreaResult{}
	defer func() {
		if panicErr := recover(); panicErr != nil {
			Logger.CtxLogErrorf(ctx, "ops log panic: %v", panicErr)
		}
	}()
	if err != nil {
		resp.Error = err.Msg
	}
	if basicConf != nil {
		resp.ThreePLServiceableAreaCheck = basicConf.IsCheckServiceable == constant.ENABLED
		resp.DefaultSupportPickup = basicConf.DefaultPickupEnabled == constant.TRUE
		resp.DefaultSupportDeliver = basicConf.DefaultDeliverEnabled == constant.TRUE
		resp.DefaultSupportCodPickup = basicConf.DefaultCodPickupEnabled == constant.TRUE
		resp.DefaultSupportCodDeliver = basicConf.DefaultCodDeliverEnabled == constant.TRUE
	}
	if operationConf != nil {
		resp.OperationServiceableAreaCheck = operationConf.IsCheckServiceable == constant.ENABLED
	}
	Logger.OpsLogLineServiceableAreaData(ctx, req, resp)
}

func logOpsOriginServiceableDetail(ctx context.Context, isOperation bool, serviceableType uint8, locationId *uint32,
	postcode *string, lineId string, collectDeliverGroupId string, ability OriginServiceableAbility, err *lcos_error.LCOSError) {
	if !Logger.IsOpenOpsConfigLog(ctx) {
		return
	}
	defer func() {
		if panicErr := recover(); panicErr != nil {
			Logger.CtxLogErrorf(ctx, "ops log panic: %v", panicErr)
		}
	}()
	if destAbility, ok := ability.(DestServiceableAbility); ok {
		serviceableDetailConfLog(ctx, isOperation, true, serviceableType, locationId, postcode, lineId,
			collectDeliverGroupId, ability, destAbility, err)
		return
	}
	serviceableDetailConfLog(ctx, isOperation, true, serviceableType, locationId, postcode, lineId,
		collectDeliverGroupId, ability, nil, err)
}

func logOpsDestServiceableDetail(ctx context.Context, isOperation bool, serviceableType uint8, locationId *uint32,
	postcode *string, lineId string, collectDeliverGroupId string, ability DestServiceableAbility, err *lcos_error.LCOSError) {
	if !Logger.IsOpenOpsConfigLog(ctx) {
		return
	}
	defer func() {
		if panicErr := recover(); panicErr != nil {
			Logger.CtxLogErrorf(ctx, "ops log panic: %v", panicErr)
		}
	}()
	if originAbility, ok := ability.(OriginServiceableAbility); ok {
		serviceableDetailConfLog(ctx, isOperation, true, serviceableType, locationId, postcode, lineId,
			collectDeliverGroupId, originAbility, ability, err)
		return
	}
	serviceableDetailConfLog(ctx, isOperation, true, serviceableType, locationId, postcode, lineId,
		collectDeliverGroupId, nil, ability, err)
}

func serviceableDetailConfLog(ctx context.Context, isOperation bool, isOrigin bool,
	serviceableType uint8, locationId *uint32, postcode *string, lineId string, collectDeliverGroupId string,
	originAbility OriginServiceableAbility, destAbility DestServiceableAbility, err *lcos_error.LCOSError) {
	checkUserType := "sender"
	if !isOrigin {
		checkUserType = "receiver"
	}
	realLocationID := 0
	if locationId != nil {
		realLocationID = int(*locationId)
	}
	realPostcode := ""
	if postcode != nil {
		realPostcode = *postcode
	}
	if isOperation {
		result := Logger.OperationServiceableAreaResult{
			CheckUserType: checkUserType,
		}
		if err != nil {
			result.Error = err.Msg
		}
		if originAbility != nil && !reflect.ValueOf(originAbility).IsNil() {
			result.BanPickup = originAbility.GetCanPickup() == constant.FALSE
			result.BanCodPickup = originAbility.GetCanCodPickup() == constant.FALSE
		}
		if destAbility != nil && !reflect.ValueOf(destAbility).IsNil() {
			result.BanDeliver = destAbility.GetCanDeliver() == constant.FALSE
			result.BanCodDeliver = destAbility.GetCanCodDeliver() == constant.FALSE
		}
		switch serviceableType {
		case constant.LOCATION:
			params := Logger.OperationLocationServiceableAreaParams{
				LineId:                lineId,
				CollectDeliverGroupId: collectDeliverGroupId,
				LocationId:            realLocationID,
			}
			Logger.OpsLogOperationLocationServiceableAreaData(ctx, params, result)
		case constant.POSTCODE:
			params := Logger.OperationPostcodeServiceableAreaParams{
				LineId:                lineId,
				CollectDeliverGroupId: collectDeliverGroupId,
				Postcode:              realPostcode,
			}
			Logger.OpsLogOperationPostcodeServiceableAreaData(ctx, params, result)
		case constant.CEPRANGE:
			params := Logger.OperationCEPServiceableAreaParams{
				LineId:                lineId,
				CollectDeliverGroupId: collectDeliverGroupId,
				CepCode:               realPostcode,
			}
			Logger.OpsLogOperationCEPServiceableAreaData(ctx, params, result)
		}
		return
	}
	result := Logger.ThreePLServiceableAreaResult{
		CheckUserType: checkUserType,
	}
	if err != nil {
		result.Error = err.Msg
	}
	if originAbility != nil && !reflect.ValueOf(originAbility).IsNil() {
		result.SupportPickup = originAbility.GetCanPickup() == constant.TRUE
		result.SupportCodPickup = originAbility.GetCanCodPickup() == constant.TRUE
	}
	if destAbility != nil && !reflect.ValueOf(destAbility).IsNil() {
		result.SupportDeliver = destAbility.GetCanDeliver() == constant.TRUE
		result.SupportCodDeliver = destAbility.GetCanCodDeliver() == constant.TRUE
	}
	switch serviceableType {
	case constant.LOCATION:
		params := Logger.ThreePLLocationServiceableAreaParams{
			LineId:                lineId,
			CollectDeliverGroupId: collectDeliverGroupId,
			LocationId:            realLocationID,
		}
		Logger.OpsLogThreePLLocationServiceableAreaData(ctx, params, result)
	case constant.POSTCODE:
		params := Logger.ThreePLPostcodeServiceableAreaParams{
			LineId:                lineId,
			CollectDeliverGroupId: collectDeliverGroupId,
			Postcode:              realPostcode,
		}
		Logger.OpsLogThreePLPostcodeServiceableAreaData(ctx, params, result)
	case constant.CEPRANGE:
		params := Logger.ThreePLCEPServiceableAreaParams{
			LineId:                lineId,
			CollectDeliverGroupId: collectDeliverGroupId,
			CepCode:               realPostcode,
		}
		Logger.OpsLogThreePLCEPServiceableAreaData(ctx, params, result)
	}
}
