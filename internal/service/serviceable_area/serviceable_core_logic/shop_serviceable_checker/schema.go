package shop_serviceable_checker

type ShopServiceableAbility struct {
	CheckPickup        bool
	CanPickup          bool
	PickupZone         string
	PickupMetroRegion  string
	CheckDeliver       bool
	CanDeliver         bool
	DeliverZone        string
	DeliverMetroRegion string
	CrossRegion        bool
}

func NewDefaultShopServiceableAbility(checkPickup, checkDeliver bool) *ShopServiceableAbility {
	return &ShopServiceableAbility{
		CheckPickup:  checkPickup,
		CanPickup:    true,
		CheckDeliver: checkDeliver,
		CanDeliver:   true,
		CrossRegion:  false,
	}
}

func (s *ShopServiceableAbility) IsServiceable() bool {
	return s.CanPickup && s.CanDeliver
}
