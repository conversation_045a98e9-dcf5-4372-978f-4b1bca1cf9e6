package shop_serviceable_checker

import (
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/shop_serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/geo_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_cep_blacklist_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_buyer_address_blacklist_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_conf_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

// ShopServiceableAreaChecker shop服务范围校验逻辑实现
type ShopServiceableAreaChecker interface {
	// CheckShopServiceable 校验shop服务范围
	CheckShopServiceable(ctx utils.LCOSContext, req *pb.CheckShopServiceableReq) (*ShopServiceableAbility, *lcos_error.LCOSError)
	// SearchProductServiceableZone 根据地址匹配对应的zone
	SearchProductServiceableZone(ctx utils.LCOSContext, req *pb.SearchProductServiceableZoneAddress) (string, *lcos_error.LCOSError)
}

type shopServiceableAreaCheckerImpl struct {
	productZoneDao            product_serviceable_zone.ProductServiceableZoneDao
	shopZoneDao               shop_serviceable_zone.ShopServiceableZoneDao
	productCepBlacklistDao    product_cep_blacklist_repo.LogisticProductCepBlacklistRepo
	shopConfDao               shop_serviceable_conf_repo.ShopServiceableAreaBasicConfRepo
	shopBuyerAddrBlacklistDao shop_buyer_address_blacklist_repo.ShopBuyerAddressBlacklistRepo
}

func NewShopServiceableAreaChecker(productZoneDao product_serviceable_zone.ProductServiceableZoneDao, shopZoneDao shop_serviceable_zone.ShopServiceableZoneDao, productCepBlacklistDao product_cep_blacklist_repo.LogisticProductCepBlacklistRepo, shopConfDao shop_serviceable_conf_repo.ShopServiceableAreaBasicConfRepo, shopBuyerAddrBlacklistDao shop_buyer_address_blacklist_repo.ShopBuyerAddressBlacklistRepo) *shopServiceableAreaCheckerImpl {
	return &shopServiceableAreaCheckerImpl{
		productZoneDao:            productZoneDao,
		shopZoneDao:               shopZoneDao,
		productCepBlacklistDao:    productCepBlacklistDao,
		shopConfDao:               shopConfDao,
		shopBuyerAddrBlacklistDao: shopBuyerAddrBlacklistDao,
	}
}

func (s *shopServiceableAreaCheckerImpl) CheckShopServiceable(ctx utils.LCOSContext, req *pb.CheckShopServiceableReq) (*ShopServiceableAbility, *lcos_error.LCOSError) {
	var (
		shopId        = int(req.GetShopId())
		productId     = int(req.GetProductId())
		checkSender   = uint8(req.GetBasis().GetCheckSender()) == constant.TRUE
		checkReceiver = uint8(req.GetBasis().GetCheckReceiver()) == constant.TRUE
		ability       = NewDefaultShopServiceableAbility(checkSender, checkReceiver) // 默认全支持
	)
	defer func() {
		// 上报服务范围校验结果
		s.reportShopServiceableAbility(ctx, shopId, productId, ability)
	}()

	// 1, 获取shop服务范围基础配置
	shopConf, err := s.GetShopServiceableBasicConf(ctx, shopId, productId)
	if err != nil {
		ability.CanPickup = false
		ability.CanDeliver = false
		logger.CtxLogErrorf(ctx, "get shop serviceable basic conf error|shop_id=%d, product_id=%d, cause=%s", shopId, productId, err.Msg)
		return ability, nil
	}

	// 2. 分别校验卖家和买家的服务范围
	if checkSender {
		pickupZone, pickupMetroRegion, err := s.GetPickupServiceableAreaZone(ctx, shopId, productId, shopConf, req.GetPickupAddr(), checkReceiver)
		if err != nil {
			ability.CanPickup = false
			logger.CtxLogErrorf(ctx, "get pickup serviceable zone error|cause=%s", err.Msg)
		} else {
			ability.CanPickup = true
			ability.PickupZone = pickupZone
			ability.PickupMetroRegion = pickupMetroRegion
			logger.CtxLogInfof(ctx, "get pickup serviceable zone|zone_name=%s, metro_region=%s", pickupZone, pickupMetroRegion)
		}
	}
	if checkReceiver {
		deliverZone, deliverMetroRegion, err := s.GetDeliverServiceableAreaZone(ctx, shopId, productId, shopConf, req.GetPickupAddr(), req.GetDeliverAddr())
		if err != nil {
			ability.CanDeliver = false
			logger.CtxLogErrorf(ctx, "get deliver serviceable zone error|cause=%s", err.Msg)
		} else {
			ability.CanDeliver = true
			ability.DeliverZone = deliverZone
			ability.DeliverMetroRegion = deliverMetroRegion
			logger.CtxLogInfof(ctx, "get deliver serviceable zone|zone_name=%s, metro_region=%s", deliverZone, deliverMetroRegion)
		}
	}
	// 3. 同时校验卖家和买家，并且卖家地址和买家地址均已匹配到zone时，需要校验卖家zone和买家zone是否属于同一metro region
	if checkSender && checkReceiver && ability.IsServiceable() {
		if ability.PickupMetroRegion != ability.DeliverMetroRegion {
			ability.CanPickup = false
			ability.CanDeliver = false
			ability.CrossRegion = true
			logger.CtxLogErrorf(ctx, "pickup and deliver zone not in the same metro region|pickup_zone=%s, deliver_zone=%s", ability.PickupZone, ability.DeliverZone)
		}
	}
	return ability, nil
}

func (s *shopServiceableAreaCheckerImpl) SearchProductServiceableZone(ctx utils.LCOSContext, req *pb.SearchProductServiceableZoneAddress) (string, *lcos_error.LCOSError) {
	var (
		shopId    = int(req.GetShopId())
		productId = int(req.GetProductId())
		zoneName  string
	)

	// 1, 获取shop服务范围基础配置
	shopConf, err := s.GetShopServiceableBasicConf(ctx, shopId, productId)
	if err != nil {
		return "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "get shop serviceable basic conf error|shop_id=%d, product_id=%d, cause=%s", req.GetShopId(), req.GetProductId(), err.Msg)
	}

	// 2. 根据地址匹配对应的zone
	deliveryMode := s.GetShopServiceableAreaDeliveryMode(ctx, shopConf)
	switch deliveryMode {
	case shop_serviceable_constant.DeliveryModeDD:
		// shop使用DD模式，使用卖家/买家地址匹配对应的zone
		cep, err := s.parsePostcode(req.GetAddress().GetPostalCode())
		if err != nil {
			return "", err
		}
		zoneName, _, err = s.GetProductServiceableZone(ctx, productId, cep)
		if err != nil {
			return "", err
		}
	case shop_serviceable_constant.DeliveryModeDDE:
		// shop使用DDE模式，zone name固定为"DDE"
		zoneName = shop_serviceable_constant.DDEModeZoneName
	default:
		return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid shop serviceable area delivery mode|shop_id=%d, product_id=%d, delivery_mode=%d", shopId, productId, deliveryMode)
	}

	return zoneName, nil
}

func (s *shopServiceableAreaCheckerImpl) GetShopServiceableBasicConf(ctx utils.LCOSContext, shopId, productId int) (*shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab, *lcos_error.LCOSError) {
	return s.shopConfDao.GetShopServiceableAreaBasicConfUsingCache(ctx, shopId, productId)
}

func (s *shopServiceableAreaCheckerImpl) GetShopServiceableAreaDeliveryMode(ctx utils.LCOSContext, shopConf *shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab) shop_serviceable_constant.DeliveryMode {
	if shopConf == nil {
		// 引入DDE模式前的存量shop可能不存在shop基础配置，此时默认为DD模式
		return shop_serviceable_constant.DeliveryModeDD
	}
	return shopConf.DeliveryMode
}

func (s *shopServiceableAreaCheckerImpl) GetProductServiceableZone(ctx utils.LCOSContext, productId, cep int) (string, string, *lcos_error.LCOSError) {
	zoneName, metroRegion, err := s.productZoneDao.GetProductServiceableZoneByPostcode(ctx, productId, cep)
	if err != nil {
		return "", "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "get product serviceable zone error|product_id=%d, cep=%d, cause=%s", productId, cep, err.Msg)
	}
	return zoneName, metroRegion, nil
}

func (s *shopServiceableAreaCheckerImpl) GetShopServiceableZone(ctx utils.LCOSContext, shopId, productId, cep int) (string, string, *lcos_error.LCOSError) {
	zoneList, err := s.shopZoneDao.SearchShopServiceableZone(ctx, shopId, productId)
	if err != nil {
		return "", "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "get shop serviceable zone list error|shop_id=%d, product_id=%d, cause=%s", shopId, productId, err.Msg)
	}

	for _, zoneName := range zoneList {
		cepGroup, err := s.productZoneDao.GetProductServiceableCepGroupByZoneName(ctx, productId, zoneName)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get cep group by zone name error|product_id=%d, zone_name=%s, cause=%s", productId, zoneName, err.Msg)
			continue
		}
		for _, cepRange := range cepGroup {
			if cep >= cepRange.CepInitial && cep <= cepRange.CepFinal {
				return zoneName, cepRange.MetroRegion, nil
			}
		}
	}
	return "", "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "address cannot hit shop serviceable zone|shop_id=%d, product_id=%d, cep=%d", shopId, productId, cep)
}

func (s *shopServiceableAreaCheckerImpl) CheckProductCepBlacklist(ctx utils.LCOSContext, productId, cep int) *lcos_error.LCOSError {
	if config.GetMutableConf(ctx).DirectDeliveryConfig.DDEDowngrade {
		// DDE降级，所有地址均不可使用DDE模式
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "DDE downgraded")
	}

	// 判断地址是否在DDE黑名单中
	hit, err := s.productCepBlacklistDao.GetLogisticProductCepBlacklistUsingCache(ctx, productId, cep)
	if err != nil {
		return err
	}
	if hit != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "address hit product cep blacklist|product_id=%d, cep=%d", productId, cep)
	}
	return nil
}

func (s *shopServiceableAreaCheckerImpl) CheckShopServiceableRadius(ctx utils.LCOSContext, shopConf *shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab, pickupAddr, deliverAddr *pb.ServiceableAddress) *lcos_error.LCOSError {
	if shopConf == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "delivery mode is DDE but shop basic conf not found")
	}

	var (
		pickupLat, pickupLng   = pickupAddr.GetLatitude(), pickupAddr.GetLongitude()
		deliverLat, deliverLng = deliverAddr.GetLatitude(), deliverAddr.GetLongitude()
	)
	dist, err := geo_utils.CalculateSphereDistance(pickupLat, pickupLng, deliverLat, deliverLng)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "calculate sphere distance error|pickup_addr=[%s,%s], deliver_addr=[%s,%s], cause=%s", pickupLat, pickupLng, deliverLat, deliverLng, err.Msg)
	}
	if dist > float64(shopConf.Radius) {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "deliver address not in seller serviceable raidus|pickup_addr=[%s,%s], deliver_addr=[%s,%s], distance=%f, radius=%d", pickupLat, pickupLng, deliverLat, deliverLng, dist, shopConf.Radius)
	}
	return nil
}

func (s *shopServiceableAreaCheckerImpl) CheckShopBuyerAddressBlacklist(ctx utils.LCOSContext, shopId, productId int, pickupAddr, deliverAddr *pb.ServiceableAddress) *lcos_error.LCOSError {
	var (
		pickupCityLocationId  = int(pickupAddr.GetCityLocationId())
		deliverCityLocationId = int(deliverAddr.GetCityLocationId())
	)
	if pickupCityLocationId == 0 || deliverCityLocationId == 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "pickup or deliver city is empty|shop_id=%d, product_id=%d, pickup_city=%d, deliver_city=%d", shopId, productId, pickupCityLocationId, deliverCityLocationId)
	}

	if pickupCityLocationId == deliverCityLocationId {
		// 卖家和买家同城，则跳过黑名单校验
		return nil
	}
	hit, err := s.shopBuyerAddrBlacklistDao.GetShopBuyerAddressBlacklistUsingCache(ctx, shopId, productId, deliverCityLocationId)
	if err != nil {
		return err
	}
	if hit {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "deliver address in shop blacklist|shop_id=%d, product_id=%d, deliver_city=%d", shopId, productId, deliverCityLocationId)
	}
	return nil
}

func (s *shopServiceableAreaCheckerImpl) GetPickupServiceableAreaZone(ctx utils.LCOSContext, shopId, productId int, shopConf *shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab, pickupAddr *pb.ServiceableAddress, checkDeliver bool) (string, string, *lcos_error.LCOSError) {
	cep, err := s.parsePostcode(pickupAddr.GetPostalCode())
	if err != nil {
		return "", "", err
	}

	// pickup校验默认不限制delivery mode，只要DD和DDE模式任意一种支持即可
	deliveryMode := shop_serviceable_constant.DeliveryModeAny
	if checkDeliver {
		// 如果同时需要校验deliver地址，则需要根据deliver地址校验模式限制pickup地址校验
		deliveryMode = s.GetShopServiceableAreaDeliveryMode(ctx, shopConf) // 获取shop配置的deliver地址校验模式
	}
	switch deliveryMode {
	case shop_serviceable_constant.DeliveryModeDD, shop_serviceable_constant.DeliveryModeAny:
		// DD模式，校验卖家地址是否在渠道的服务范围polygon中
		zoneName, metroRegion, err := s.GetProductServiceableZone(ctx, productId, cep)
		if err == nil {
			// DD模式校验成功，直接返回
			return zoneName, metroRegion, nil
		}
		if deliveryMode == shop_serviceable_constant.DeliveryModeDD {
			// 限制为仅校验DD时，DD校验失败则返回报错
			return "", "", err
		}
		// DD模式校验失败，则再使用DDE模式校验
		fallthrough
	case shop_serviceable_constant.DeliveryModeDDE:
		// DDE模式，校验卖家地址是否在渠道的黑名单中
		if err = s.CheckProductCepBlacklist(ctx, productId, cep); err != nil {
			return "", "", err
		}
		return shop_serviceable_constant.DDEModeZoneName, shop_serviceable_constant.DDEModeMetroRegion, nil
	default:
		return "", "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid shop serviceable area delivery mode|shop_id=%d, product_id=%d, delivery_mode=%d", shopId, productId, deliveryMode)
	}
}

func (s *shopServiceableAreaCheckerImpl) GetDeliverServiceableAreaZone(ctx utils.LCOSContext, shopId, productId int, shopConf *shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab, pickupAddr, deliverAddr *pb.ServiceableAddress) (string, string, *lcos_error.LCOSError) {
	cep, err := s.parsePostcode(deliverAddr.GetPostalCode())
	if err != nil {
		return "", "", err
	}

	deliveryMode := s.GetShopServiceableAreaDeliveryMode(ctx, shopConf)
	switch deliveryMode {
	case shop_serviceable_constant.DeliveryModeDD:
		// DD模式，校验买家地址是否在shop开启的可售区域中
		return s.GetShopServiceableZone(ctx, shopId, productId, cep)
	case shop_serviceable_constant.DeliveryModeDDE:
		// DDE模式，校验买家地址：
		// 1. 是否在渠道的黑名单中
		// 2. 是否在卖家的可售半径内
		// 3. 是否在卖家的黑名单城市中
		if err = s.CheckProductCepBlacklist(ctx, productId, cep); err != nil {
			return "", "", err
		}
		if err = s.CheckShopServiceableRadius(ctx, shopConf, pickupAddr, deliverAddr); err != nil {
			return "", "", err
		}
		if err = s.CheckShopBuyerAddressBlacklist(ctx, shopId, productId, pickupAddr, deliverAddr); err != nil {
			return "", "", err
		}
		return shop_serviceable_constant.DDEModeZoneName, shop_serviceable_constant.DDEModeMetroRegion, nil
	default:
		return "", "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid shop serviceable area delivery mode|shop_id=%d, product_id=%d, delivery_mode=%d", shopId, productId, deliveryMode)
	}
}

func (s *shopServiceableAreaCheckerImpl) parsePostcode(postcode string) (int, *lcos_error.LCOSError) {
	postcode = utils.ValidPostcode(postcode) // 去除邮编非数字部分
	cep, err := strconv.Atoi(postcode)
	if err != nil {
		return 0, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid postcode|postcode=%s, cause=%s", postcode, err.Error())
	}
	return cep, nil
}

func (s *shopServiceableAreaCheckerImpl) reportShopServiceableAbility(ctx utils.LCOSContext, shopId, productId int, ability *ShopServiceableAbility) {
	if !config.GetServiceAbleReport(ctx) {
		return
	}
	if ability == nil {
		return
	}

	_ = metrics.CounterIncr(constant.MetricDirectDeliveryReport, map[string]string{
		"url":            ctx.GetUrl(),
		"product_id":     strconv.Itoa(productId),
		"success":        strconv.FormatBool(ability.IsServiceable()),
		"check_pickup":   strconv.FormatBool(ability.CheckPickup),
		"can_pickup":     strconv.FormatBool(ability.CanPickup),
		"pickup_zone":    ability.PickupZone,
		"pickup_region":  ability.PickupMetroRegion,
		"check_deliver":  strconv.FormatBool(ability.CheckDeliver),
		"can_deliver":    strconv.FormatBool(ability.CanDeliver),
		"deliver_zone":   ability.DeliverZone,
		"deliver_region": ability.DeliverMetroRegion,
		"cross_region":   strconv.FormatBool(ability.CrossRegion),
	})
}
