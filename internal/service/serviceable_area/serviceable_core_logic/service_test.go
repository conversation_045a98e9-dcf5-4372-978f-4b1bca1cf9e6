package serviceable_core_logic

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	collectDeliverGroupRef "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	collectDeliver "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/golang/protobuf/proto"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
)

func TestServiceableCheckerService_GetServiceableWithCheckFlag(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	_, err = cf.InitMutableConfig(ctx)
	if err != nil {
		t.Fatalf("Init Mutable config Error:%v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	if err := cache.InitLocalCache(); err != nil {
		t.Fatalf("InitLocalcache Error: %v", err)
	}
	if err := localcache.LoadLocalCache(); err != nil {
		t.Fatalf("loadLocalcache Error: %v", err)
	}
	type fields struct {
		basicConfDao              basic_conf.LineBasicServiceableConfDAO
		basicLocationDao          basic_location.LineBasicServiceableLocationDAO
		basicPostcodeDao          basic_postcode.LineBasicServiceablePostcodeDAO
		scenarioConf              scenario_conf.LineCommonServiceableScenarioConfDAO
		operationConfDao          operation_conf.LogisticLineOperationServiceableConfTabDAO
		operationLocationDao      operation_location.LogisticLineOperationServiceableLocationTabDAO
		operationPostcodeDao      operation_postcode.LogisticLineOperationServiceablePostcodeTabDAO
		routeDao                  route.LogisticLineServiceableRouteTabDAO
		areaLocationDao           area_location_ref.LogisticLineServiceableAreaLocationRefTabDAO
		collectDeliverGroupDao    collectDeliver.LineCollectDeliverGroupConfDao
		collectDeliverGroupRefDao collectDeliverGroupRef.LineBasicServiceableGroupRefDao
		cepRangeDao               cep_range.LineServiceableCepRangeDaoInterface
	}
	type args struct {
		ctx     utils.LCOSContext
		request *pb.GetLineServiceableInfoRequest2
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *pb.ServiceableAreaInfo
		want1  *CheckFailErrcode
		want2  *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "get serviceable area with check flag",
			fields: fields{
				basicConfDao:              basic_conf.NewLineBasicServiceableConfDAO(),
				basicLocationDao:          basic_location.NewLineBasicServiceableLocationDAO(),
				basicPostcodeDao:          basic_postcode.NewLineBasicServiceablePostcodeDAO(),
				scenarioConf:              scenario_conf.NewLineCommonServiceableScenarioConfDAO(),
				operationConfDao:          operation_conf.NewLogisticLineOperationServiceableConfTabDAO(),
				operationLocationDao:      operation_location.NewLogisticLineOperationServiceableLocationTabDAO(),
				operationPostcodeDao:      operation_postcode.NewLogisticLineOperationServiceablePostcodeTabDAO(),
				routeDao:                  route.NewLogisticLineServiceableRouteTabDAO(),
				areaLocationDao:           area_location_ref.NewLogisticLineServiceableAreaLocationRefTabDAO(),
				collectDeliverGroupDao:    collectDeliver.NewLineCollectDeliverGroupConfDao(),
				collectDeliverGroupRefDao: collectDeliverGroupRef.NewLineBaseServiceableGroupRefDao(),
				cepRangeDao:               cep_range.NewLineServiceableCepRangeDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &pb.GetLineServiceableInfoRequest2{
					ReqHeader: &pb.ReqHeader{},
					BaseInfo: &pb.GetServiceableInfoBase2{
						LineId:             proto.String("LID25"),
						IsCheckBasic:       proto.Uint32(1),
						IsCheckOperation:   proto.Uint32(1),
						CollectType:        proto.Uint32(3),
						DeliverType:        proto.Uint32(8),
						CheckSender:        proto.Uint32(1),
						SenderCheckLevel:   proto.Uint32(3),
						CheckReceiver:      proto.Uint32(1),
						ReceiverCheckLevel: proto.Uint32(3),
					},
					PickupInfo: &pb.LocationInfo{
						StateLocationId:    proto.Uint32(1500),
						CityLocationId:     proto.Uint32(1812),
						DistrictLocationId: proto.Uint32(1840),
					},
					DeliverInfo: &pb.LocationInfo{
						StateLocationId:    proto.Uint32(4159),
						CityLocationId:     proto.Uint32(4199),
						DistrictLocationId: proto.Uint32(4208),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			checker := &ServiceableCheckerService{
				basicConfDao:              tt.fields.basicConfDao,
				basicLocationDao:          tt.fields.basicLocationDao,
				basicPostcodeDao:          tt.fields.basicPostcodeDao,
				scenarioConf:              tt.fields.scenarioConf,
				operationConfDao:          tt.fields.operationConfDao,
				operationLocationDao:      tt.fields.operationLocationDao,
				operationPostcodeDao:      tt.fields.operationPostcodeDao,
				routeDao:                  tt.fields.routeDao,
				areaLocationDao:           tt.fields.areaLocationDao,
				collectDeliverGroupDao:    tt.fields.collectDeliverGroupDao,
				collectDeliverGroupRefDao: tt.fields.collectDeliverGroupRefDao,
				cepRangeDao:               tt.fields.cepRangeDao,
			}
			got, got1, err := checker.GetServiceableWithCheckFlag(tt.args.ctx, tt.args.request)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ServiceableCheckerService.GetServiceableWithCheckFlag() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("ServiceableCheckerService.GetServiceableWithCheckFlag() got1 = %v, want %v", got1, tt.want1)
			}
			if err != nil {
				t.Errorf("serviceable check fail, err:%v", err)
			}
		})
	}
}
