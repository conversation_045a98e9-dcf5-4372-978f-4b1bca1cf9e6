package serviceable_upload

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/task_api"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
)

type BasicLocationFileParser struct{}

func (p *BasicLocationFileParser) GetHeader() []excel.ParseableField {
	return serviceable_util.LineBasicLocationHeader
}

func (p *BasicLocationFileParser) Handle(ctx utils.LCOSContext, region, fileUrl string, header []excel.ParseableField) (excel.ParseFileResult, *lcos_error.LCOSError) {
	req := &common_protocol.UploadFileRequest{
		Region:  region,
		FileUrl: fileUrl,
	}
	return task_api.GetTaskService().SALocationService.ParseAndImportBasicLocationSA(ctx, req, header, false, 0)
}

type BasicPostcodeFileParser struct{}

func (p *BasicPostcodeFileParser) GetHeader() []excel.ParseableField {
	return serviceable_util.LineBasicPostcodeHeader
}

func (p *BasicPostcodeFileParser) Handle(ctx utils.LCOSContext, region, fileUrl string, header []excel.ParseableField) (excel.ParseFileResult, *lcos_error.LCOSError) {
	req := &common_protocol.UploadFileRequest{
		Region:  region,
		FileUrl: fileUrl,
	}
	_, result, fileErr := task_api.GetTaskService().SAPostcodeService.ParseAndImportBasicPostcodeSA(ctx, req, header, false, 0)
	return result, fileErr
}

type BasicCepRangeFileParser struct{}

func (p *BasicCepRangeFileParser) GetHeader() []excel.ParseableField {
	return serviceable_util.LineBasicCepRangeHeader
}

func (p *BasicCepRangeFileParser) Handle(ctx utils.LCOSContext, region, fileUrl string, header []excel.ParseableField) (excel.ParseFileResult, *lcos_error.LCOSError) {
	req := &common_protocol.UploadFileRequest{
		Region:  region,
		FileUrl: fileUrl,
	}
	return task_api.GetTaskService().SACepRangeService.ParseAndImportBasicCepRangeSA(ctx, req, header, false)
}

type BasicRouteFileParser struct{}

func (p *BasicRouteFileParser) GetHeader() []excel.ParseableField {
	return serviceable_util.LineBasicRouteHeader
}

func (p *BasicRouteFileParser) Handle(ctx utils.LCOSContext, region, fileUrl string, header []excel.ParseableField) (excel.ParseFileResult, *lcos_error.LCOSError) {
	req := &area_route_serviceable.UploadRouteRequest{
		FileUrl: fileUrl,
	}
	return task_api.GetTaskService().ServiceableAreaService.ParseAndImportBasicRouteSA(ctx, region, req, header, false)
}

type OperationLocationFileParser struct{}

func (p *OperationLocationFileParser) GetHeader() []excel.ParseableField {
	return serviceable_util.LineOperationLocationHeader
}

func (p *OperationLocationFileParser) Handle(ctx utils.LCOSContext, region, fileUrl string, header []excel.ParseableField) (excel.ParseFileResult, *lcos_error.LCOSError) {
	req := &common_protocol.UploadFileRequest{
		Region:  region,
		FileUrl: fileUrl,
	}
	return task_api.GetTaskService().SAOperationLocationService.ParseAndImportOperationLocationSA(ctx, req, header, false)
}

type OperationPostcodeFileParser struct{}

func (p *OperationPostcodeFileParser) GetHeader() []excel.ParseableField {
	return serviceable_util.LineOperationPostcodeHeader
}

func (p *OperationPostcodeFileParser) Handle(ctx utils.LCOSContext, region, fileUrl string, header []excel.ParseableField) (excel.ParseFileResult, *lcos_error.LCOSError) {
	req := &common_protocol.UploadFileRequest{
		Region:  region,
		FileUrl: fileUrl,
	}
	return task_api.GetTaskService().SAOperationPostCodeService.ParseAndImportOperationPostcodeSA(ctx, req, header, false)
}

type OperationCepRangeFileParser struct{}

func (p *OperationCepRangeFileParser) GetHeader() []excel.ParseableField {
	return serviceable_util.LineOperationCepRangeHeader
}

func (p *OperationCepRangeFileParser) Handle(ctx utils.LCOSContext, region, fileUrl string, header []excel.ParseableField) (excel.ParseFileResult, *lcos_error.LCOSError) {
	req := &common_protocol.UploadFileRequest{
		Region:  region,
		FileUrl: fileUrl,
	}
	return task_api.GetTaskService().SAOperationCepRangeService.ParseAndImportOperationCepRangeSA(ctx, req, header, false)
}

type OperationRouteFileParser struct{}

func (p *OperationRouteFileParser) GetHeader() []excel.ParseableField {
	return serviceable_util.LineOperationRouteHeader
}

func (p *OperationRouteFileParser) Handle(ctx utils.LCOSContext, region, fileUrl string, header []excel.ParseableField) (excel.ParseFileResult, *lcos_error.LCOSError) {
	req := &operation_serviceable.UploadOperationRouteRequest{
		FileUrl: fileUrl,
	}
	return task_api.GetTaskService().ServiceableAreaOperationService.ParseAndImportOperationRouteSA(ctx, region, req, header, false)
}
