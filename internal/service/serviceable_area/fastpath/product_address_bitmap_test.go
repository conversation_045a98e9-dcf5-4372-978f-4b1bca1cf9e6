package fastpath

import (
	"context"
	"testing"

	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

// TestProductAddressIndex 测试索引提供者
func TestProductAddressIndex(t *testing.T) {
	provider := NewProductAddressIndexProvider()

	key1 := ProductAddressKey{

		ProductId:             1001,
		OriginLocationId:      1001,
		DestLocationId:        2001,
		CollectDeliverGroupId: "G1",
	}

	key2 := ProductAddressKey{

		ProductId:             1002,
		OriginLocationId:      1001,
		DestLocationId:        2001,
		CollectDeliverGroupId: "G1",
	}

	// 测试索引生成
	index1 := provider.GetIndex(key1)
	index2 := provider.GetIndex(key2)

	if index1 == index2 {
		t.Errorf("Different keys should have different indexes")
	}

	// 测试相同key返回相同索引
	index1_again := provider.GetIndex(key1)
	if index1 != index1_again {
		t.<PERSON>rrorf("Same key should return same index: %d vs %d", index1, index1_again)
	}

	// 测试统计信息
	stats := provider.GetStats()
	if stats.QueryCount != 3 {
		t.Errorf("Expected 3 query count, got %d", stats.QueryCount)
	}
}

// TestProductAddressBitmap 测试bitmap基本功能
func TestProductAddressBitmap(t *testing.T) {
	indexProvider := NewProductAddressIndexProvider()
	bitmap := NewProductAddressBitmap("SG", indexProvider)

	ctx := utils.NewCommonCtx(context.Background())

	key1 := ProductAddressKey{

		ProductId:             1001,
		OriginLocationId:      1001,
		DestLocationId:        2001,
		CollectDeliverGroupId: "G1",
	}

	key2 := ProductAddressKey{

		ProductId:             1002,
		OriginLocationId:      1001,
		DestLocationId:        2001,
		CollectDeliverGroupId: "G1",
	}

	// 初始状态：都不支持
	if bitmap.IsServiceable(ctx, key1) {
		t.Errorf("Key1 should not be serviceable initially")
	}

	if bitmap.IsServiceable(ctx, key2) {
		t.Errorf("Key2 should not be serviceable initially")
	}

	// 设置key1为支持
	bitmap.SetServiceable(ctx, key1)

	if !bitmap.IsServiceable(ctx, key1) {
		t.Errorf("Key1 should be serviceable after setting")
	}

	if bitmap.IsServiceable(ctx, key2) {
		t.Errorf("Key2 should still not be serviceable")
	}

	// 测试批量操作
	keys := []ProductAddressKey{key1, key2}
	results := bitmap.BatchIsServiceable(ctx, keys)

	if !results[key1] {
		t.Errorf("Key1 should be serviceable in batch result")
	}

	if results[key2] {
		t.Errorf("Key2 should not be serviceable in batch result")
	}

	// 测试重建bitmap
	serviceableKeys := []ProductAddressKey{key1, key2}
	bitmap.Rebuild(ctx, serviceableKeys)

	if !bitmap.IsServiceable(ctx, key1) {
		t.Errorf("Key1 should be serviceable after rebuild")
	}

	if !bitmap.IsServiceable(ctx, key2) {
		t.Errorf("Key2 should be serviceable after rebuild")
	}

	// 测试统计信息
	stats := bitmap.GetStats()
	if stats.SetBits != 2 {
		t.Errorf("Expected 2 set bits, got %d", stats.SetBits)
	}
}
