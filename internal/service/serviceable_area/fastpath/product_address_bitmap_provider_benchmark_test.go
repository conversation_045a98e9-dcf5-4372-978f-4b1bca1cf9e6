package fastpath

import (
	"context"
	"fmt"
	"testing"

	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

// setupBenchmarkProvider 创建带有预填充数据的 bitmap provider
func setupBenchmarkProvider(b *testing.B, region string, numServiceableKeys int) ProductAddressBitmapProvider {
	b.Helper()

    // 为了避免复杂的 DAO mock，我们直接创建索引提供者和按PRODUCT分片的bitmaps
	indexProvider := NewProductAddressIndexProvider()

    // 创建 provider 实现，使用 nil DAO（因为 benchmark 不需要真实的数据库操作）
    provider := &productAddressBitmapProviderImpl{
        productBitmaps:  make(map[int32]*ProductAddressBitmap),
        indexProvider:   indexProvider,
        lineLocationDAO: nil, // benchmark 测试不需要真实的 DAO
    }

	ctx := utils.NewCommonCtx(context.Background())

    // 生成测试数据并预填充到各自的product bitmap中
    keys := generateTestKeys(numServiceableKeys)
    // 按product分组
    productToKeys := make(map[int32][]ProductAddressKey)
    for _, k := range keys {
        productToKeys[k.ProductId] = append(productToKeys[k.ProductId], k)
    }
    for pid, ks := range productToKeys {
        bm := NewProductAddressBitmap(region, indexProvider)
        bm.BatchSetServiceable(ctx, ks)
        provider.productBitmaps[pid] = bm
    }

	return provider
}

// generateTestKeys 生成指定数量的测试键
func generateTestKeys(count int) []ProductAddressKey {
	keys := make([]ProductAddressKey, 0, count)

	// 模拟数据范围
	productIds := []int32{1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010}
	locationIds := make([]uint64, 0, 100)
	for i := uint64(2001); i <= 2100; i++ {
		locationIds = append(locationIds, i)
	}

	for i := 0; i < count; i++ {
		productId := productIds[i%len(productIds)]
		originIdx := i % len(locationIds)
		destIdx := (i + 7) % len(locationIds) // 使用不同的偏移避免同一位置

		// 确保 origin 和 dest 不相同
		if originIdx == destIdx {
			destIdx = (destIdx + 1) % len(locationIds)
		}

		key := ProductAddressKey{
			ProductId:             productId,
			OriginLocationId:      locationIds[originIdx],
			DestLocationId:        locationIds[destIdx],
			CollectDeliverGroupId: "G",
		}

		keys = append(keys, key)
	}

	return keys
}

// generateQueryKeys 生成查询键，包含命中和未命中的情况
func generateQueryKeys(count int, hitRatio float64) []ProductAddressKey {
	keys := make([]ProductAddressKey, 0, count)

	hitCount := int(float64(count) * hitRatio)
	missCount := count - hitCount

	// 生成会命中的键（与预填充数据相同的模式）
	hitKeys := generateTestKeys(hitCount)
	keys = append(keys, hitKeys...)

	// 生成不会命中的键（使用不同的产品ID范围）
	productIds := []int32{9001, 9002, 9003, 9004, 9005} // 不同的产品ID范围
	locationIds := make([]uint64, 0, 50)
	for i := uint64(9001); i <= 9050; i++ {
		locationIds = append(locationIds, i)
	}

	for i := 0; i < missCount; i++ {
		productId := productIds[i%len(productIds)]
		originIdx := i % len(locationIds)
		destIdx := (i + 3) % len(locationIds)

		if originIdx == destIdx {
			destIdx = (destIdx + 1) % len(locationIds)
		}

		key := ProductAddressKey{
			ProductId:             productId,
			OriginLocationId:      locationIds[originIdx],
			DestLocationId:        locationIds[destIdx],
			CollectDeliverGroupId: "G",
		}

		keys = append(keys, key)
	}

	return keys
}

// BenchmarkBatchIsServiceable_Small 小规模查询
func BenchmarkBatchIsServiceable_Small(b *testing.B) {
	region := "SG"
	numServiceableKeys := 10000
	queryBatchSize := 10
	hitRatio := 0.8

	provider := setupBenchmarkProvider(b, region, numServiceableKeys)
	ctx := utils.NewCommonCtx(context.Background())

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		keys := generateQueryKeys(queryBatchSize, hitRatio)
		results := provider.BatchIsServiceable(ctx, region, keys)

		// 验证结果长度，避免编译器优化
		if len(results) != queryBatchSize {
			b.Fatalf("Expected %d results, got %d", queryBatchSize, len(results))
		}
	}
}

// BenchmarkBatchIsServiceable_Medium 中等规模查询
func BenchmarkBatchIsServiceable_Medium(b *testing.B) {
	region := "SG"
	numServiceableKeys := 100000
	queryBatchSize := 100
	hitRatio := 0.8

	provider := setupBenchmarkProvider(b, region, numServiceableKeys)
	ctx := utils.NewCommonCtx(context.Background())

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		keys := generateQueryKeys(queryBatchSize, hitRatio)
		results := provider.BatchIsServiceable(ctx, region, keys)

		if len(results) != queryBatchSize {
			b.Fatalf("Expected %d results, got %d", queryBatchSize, len(results))
		}
	}
}

// BenchmarkBatchIsServiceable_Large 大规模查询
func BenchmarkBatchIsServiceable_Large(b *testing.B) {
	region := "SG"
	numServiceableKeys := 1000000
	queryBatchSize := 1000
	hitRatio := 0.8

	provider := setupBenchmarkProvider(b, region, numServiceableKeys)
	ctx := utils.NewCommonCtx(context.Background())

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		keys := generateQueryKeys(queryBatchSize, hitRatio)
		results := provider.BatchIsServiceable(ctx, region, keys)

		if len(results) != queryBatchSize {
			b.Fatalf("Expected %d results, got %d", queryBatchSize, len(results))
		}
	}
}

// BenchmarkBatchIsServiceable_VeryLarge 超大规模查询
func BenchmarkBatchIsServiceable_VeryLarge(b *testing.B) {
	region := "SG"
	numServiceableKeys := 1000000
	queryBatchSize := 10000
	hitRatio := 0.8

	provider := setupBenchmarkProvider(b, region, numServiceableKeys)
	ctx := utils.NewCommonCtx(context.Background())

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		keys := generateQueryKeys(queryBatchSize, hitRatio)
		results := provider.BatchIsServiceable(ctx, region, keys)

		if len(results) != queryBatchSize {
			b.Fatalf("Expected %d results, got %d", queryBatchSize, len(results))
		}
	}
}

// BenchmarkBatchIsServiceable_HighHitRate 高命中率场景
func BenchmarkBatchIsServiceable_HighHitRate(b *testing.B) {
	region := "SG"
	numServiceableKeys := 100000
	queryBatchSize := 1000
	hitRatio := 0.95 // 95% 命中率

	provider := setupBenchmarkProvider(b, region, numServiceableKeys)
	ctx := utils.NewCommonCtx(context.Background())

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		keys := generateQueryKeys(queryBatchSize, hitRatio)
		results := provider.BatchIsServiceable(ctx, region, keys)

		if len(results) != queryBatchSize {
			b.Fatalf("Expected %d results, got %d", queryBatchSize, len(results))
		}
	}
}

// BenchmarkBatchIsServiceable_LowHitRate 低命中率场景
func BenchmarkBatchIsServiceable_LowHitRate(b *testing.B) {
	region := "SG"
	numServiceableKeys := 100000
	queryBatchSize := 1000
	hitRatio := 0.1 // 10% 命中率

	provider := setupBenchmarkProvider(b, region, numServiceableKeys)
	ctx := utils.NewCommonCtx(context.Background())

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		keys := generateQueryKeys(queryBatchSize, hitRatio)
		results := provider.BatchIsServiceable(ctx, region, keys)

		if len(results) != queryBatchSize {
			b.Fatalf("Expected %d results, got %d", queryBatchSize, len(results))
		}
	}
}

// BenchmarkBatchIsServiceable_NoBitmap 测试没有bitmap的情况
func BenchmarkBatchIsServiceable_NoBitmap(b *testing.B) {
	region := "UNKNOWN_REGION" // 使用一个没有bitmap的region
	queryBatchSize := 1000
	hitRatio := 0.8

	// 创建不包含任何数据的 provider
	indexProvider := NewProductAddressIndexProvider()
    provider := &productAddressBitmapProviderImpl{
        productBitmaps:  make(map[int32]*ProductAddressBitmap),
        indexProvider:   indexProvider,
        lineLocationDAO: nil,
    }

	ctx := utils.NewCommonCtx(context.Background())

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		keys := generateQueryKeys(queryBatchSize, hitRatio)
		results := provider.BatchIsServiceable(ctx, region, keys)

		// 验证所有结果都是 false（因为没有bitmap）
		for _, result := range results {
			if result {
				b.Fatal("Expected all results to be false when no bitmap exists")
			}
		}

		if len(results) != queryBatchSize {
			b.Fatalf("Expected %d results, got %d", queryBatchSize, len(results))
		}
	}
}

// BenchmarkBatchIsServiceable_DifferentBatchSizes 比较不同批量大小的性能
func BenchmarkBatchIsServiceable_DifferentBatchSizes(b *testing.B) {
	region := "SG"
	numServiceableKeys := 100000
	hitRatio := 0.8
	batchSizes := []int{1, 10, 50, 100, 500, 1000, 5000}

	provider := setupBenchmarkProvider(b, region, numServiceableKeys)
	ctx := utils.NewCommonCtx(context.Background())

	for _, batchSize := range batchSizes {
		b.Run(fmt.Sprintf("BatchSize_%d", batchSize), func(b *testing.B) {
			b.ResetTimer()
			b.ReportAllocs()

			for i := 0; i < b.N; i++ {
				keys := generateQueryKeys(batchSize, hitRatio)
				results := provider.BatchIsServiceable(ctx, region, keys)

				if len(results) != batchSize {
					b.Fatalf("Expected %d results, got %d", batchSize, len(results))
				}
			}
		})
	}
}
