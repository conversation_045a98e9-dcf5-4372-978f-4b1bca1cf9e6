package fastpath

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"

	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

// BitmapCapabilityManager bitmap能力管理器，统一管理bitmap预计算任务和查询
type BitmapCapabilityManager struct {
	bitmapProvider ProductAddressBitmapProvider
	precomputeTask *PrecomputeBitmapTask

	mutex       sync.RWMutex
	initialized bool
}

// BitmapCapabilityManagerConfig bitmap能力管理器配置
type BitmapCapabilityManagerConfig struct {
	RefreshInterval time.Duration `json:"refresh_interval"`
	AutoStart       bool          `json:"auto_start"`
}

// DefaultBitmapCapabilityManagerConfig 默认配置
func DefaultBitmapCapabilityManagerConfig() BitmapCapabilityManagerConfig {
	return BitmapCapabilityManagerConfig{
		RefreshInterval: 5 * time.Minute,
		AutoStart:       true,
	}
}

// NewBitmapCapabilityManager 创建bitmap能力管理器
func NewBitmapCapabilityManager(
	lineLocationDAO basic_location.LineBasicServiceableLocationDAO,
) *BitmapCapabilityManager {
	precomputeTask := NewPrecomputeBitmapTask(lineLocationDAO)

	return &BitmapCapabilityManager{
		bitmapProvider: precomputeTask.GetBitmapProvider(),
		precomputeTask: precomputeTask,
	}
}

// Initialize 初始化bitmap能力管理器
// 第一次启动时会同步构建所有需要的BITMAP，确保服务启动后立即可用
func (m *BitmapCapabilityManager) Initialize(ctx context.Context, config BitmapCapabilityManagerConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.initialized {
		return nil
	}

	logger.LogInfof("Initializing BitmapCapabilityManager with config: %+v", config)

	// 更新预计算任务配置
	m.precomputeTask.UpdateConfig(config.RefreshInterval)

	// 同步执行初始构建，阻塞直到所有BITMAP构建完成
	if err := m.precomputeTask.ExecuteInitialBuild(ctx); err != nil {
		logger.LogErrorf("Failed to execute initial bitmap build: %v", err)
		return fmt.Errorf("initial bitmap build failed: %w", err)
	}

	m.initialized = true
	logger.LogInfof("BitmapCapabilityManager initialized successfully")

	return nil
}

// IsServiceable 检查Product+Address组合是否支持配送
// 这是主要的查询接口，直接查bitmap，未命中返回false
func (m *BitmapCapabilityManager) IsServiceable(ctx context.Context, region string, key ProductAddressKey) bool {
	if !m.initialized {
		logger.LogErrorf("BitmapCapabilityManager not initialized")
		return false
	}

	lcosCtx := utils.NewCommonCtx(ctx)
	return m.bitmapProvider.IsServiceable(lcosCtx, region, key)
}

// BatchIsServiceable 批量检查可配送性
func (m *BitmapCapabilityManager) BatchIsServiceable(ctx context.Context, region string, keys []ProductAddressKey) map[ProductAddressKey]bool {
	if !m.initialized {
		logger.LogErrorf("BitmapCapabilityManager not initialized")
		// 返回全部false
		result := make(map[ProductAddressKey]bool, len(keys))
		for _, key := range keys {
			result[key] = false
		}
		return result
	}

	lcosCtx := utils.NewCommonCtx(ctx)
	return m.bitmapProvider.BatchIsServiceable(lcosCtx, region, keys)
}

// GetBitmapProvider 获取bitmap提供者
func (m *BitmapCapabilityManager) GetBitmapProvider() ProductAddressBitmapProvider {
	return m.bitmapProvider
}

// StartPrecompute 启动预计算任务
func (m *BitmapCapabilityManager) StartPrecompute(ctx context.Context) error {
	return m.precomputeTask.Start(ctx)
}

// StopPrecompute 停止预计算任务
func (m *BitmapCapabilityManager) StopPrecompute() {
	m.precomputeTask.Stop()
}

// GetPrecomputeStats 获取预计算统计信息
func (m *BitmapCapabilityManager) GetPrecomputeStats() PrecomputeBitmapStats {
	return m.precomputeTask.GetStats()
}

// GetBitmapStats 获取指定地区的bitmap统计信息
func (m *BitmapCapabilityManager) GetBitmapStats(region string) map[string]interface{} {
	return m.precomputeTask.GetBitmapStats(region)
}

// GetAggregateStats 获取聚合统计（总keys、总压缩字节）
func (m *BitmapCapabilityManager) GetAggregateStats() map[string]interface{} {
	return m.precomputeTask.GetBitmapProvider().GetAggregateStats()
}

// GetRegionAggregateStats 获取分地区聚合统计
func (m *BitmapCapabilityManager) GetRegionAggregateStats(regions []string) map[string]map[string]interface{} {
	return m.precomputeTask.GetBitmapProvider().GetRegionAggregateStats(regions)
}

// Shutdown 优雅关闭
func (m *BitmapCapabilityManager) Shutdown(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.initialized {
		return nil
	}

	logger.LogInfof("Shutting down BitmapCapabilityManager")

	// 停止预计算任务
	m.precomputeTask.Stop()

	// 等待一段时间确保任务完全停止
	time.Sleep(1 * time.Second)

	m.initialized = false
	logger.LogInfof("BitmapCapabilityManager shutdown completed")

	return nil
}

// IsInitialized 检查是否已初始化
func (m *BitmapCapabilityManager) IsInitialized() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.initialized
}

// GetStatus 获取管理器状态
func (m *BitmapCapabilityManager) GetStatus() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	ctx := context.Background()
	loadRegions := config.GetItemCardBitmapLoadRegionList(ctx)
	useRegions := config.GetItemCardBitmapUseRegionList(ctx)

	status := map[string]interface{}{
		"initialized":      m.initialized,
		"precompute_stats": m.precomputeTask.GetStats(),
		"load_regions":     loadRegions,
		"use_regions":      useRegions,
	}

	// 添加各个加载地区的bitmap统计
	regionStats := make(map[string]interface{})
	for _, region := range loadRegions {
		regionStats[region] = m.GetBitmapStats(region)
	}
	status["region_stats"] = regionStats

	return status
}
