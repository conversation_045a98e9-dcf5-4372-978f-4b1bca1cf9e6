package fastpath

import (
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"sync"
)

// LocationIndexProvider Location ID到bitmap index的映射提供者
// 解决不同业务场景下的location ID映射一致性问题
type LocationIndexProvider interface {
	// GetLocationIndex 获取location ID在bitmap中的索引
	GetLocationIndex(locationId uint64) uint32

	// GetStats 获取统计信息
	GetStats() LocationIndexStats
}

// LocationIndexStats 统计信息
type LocationIndexStats struct {
	QueryCount int64 `json:"query_count"` // 查询次数
}

// locationIndexProviderImpl Location映射提供者实现
type locationIndexProviderImpl struct {
	stats LocationIndexStats
	mutex sync.RWMutex
}

// NewLocationIndexProvider 创建Location映射提供者
func NewLocationIndexProvider() LocationIndexProvider {
	return &locationIndexProviderImpl{}
}

// GetLocationIndex 获取location的bitmap索引
func (p *locationIndexProviderImpl) GetLocationIndex(locationId uint64) uint32 {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.stats.QueryCount++
	return p.hashLocationId(locationId)
}

// hashLocationId 计算location ID的hash值作为索引
// 使用一致性hash算法确保相同location ID总是映射到相同index
func (p *locationIndexProviderImpl) hashLocationId(locationId uint64) uint32 {
	// 对于location ID，我们需要更好的hash分布
	// 使用MD5确保一致性和良好的分布特性
	h := md5.New()
	h.Write([]byte(fmt.Sprintf("location:%d", locationId)))

	hash := h.Sum(nil)

	// 取前4个字节作为uint32
	index := binary.BigEndian.Uint32(hash[:4])

	// 为了更好的分布，可以对index进行一些变换
	// 这里使用简单的模运算限制在合理范围内，避免bitmap过于稀疏
	// 假设我们支持最多1000万个不同的location（可根据实际情况调整）
	return index % 10000000
}

// GetStats 获取统计信息
func (p *locationIndexProviderImpl) GetStats() LocationIndexStats {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.stats
}
