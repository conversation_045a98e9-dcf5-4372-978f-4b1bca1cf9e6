package fastpath

import (
	"sync"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

var (
	// 全局bitmap能力管理器实例
	globalBitmapCapabilityManager *BitmapCapabilityManager
	globalMutex                   sync.RWMutex
)

// SetGlobalBitmapCapabilityManager 设置全局bitmap能力管理器实例
func SetGlobalBitmapCapabilityManager(manager *BitmapCapabilityManager) {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	globalBitmapCapabilityManager = manager
	logger.LogInfof("Global BitmapCapabilityManager instance set successfully")
}

// GetGlobalBitmapCapabilityManager 获取全局bitmap能力管理器实例
func GetGlobalBitmapCapabilityManager() *BitmapCapabilityManager {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	return globalBitmapCapabilityManager
}

// IsGlobalBitmapCapabilityManagerAvailable 检查全局bitmap能力管理器是否可用
func IsGlobalBitmapCapabilityManagerAvailable() bool {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	return globalBitmapCapabilityManager != nil && globalBitmapCapabilityManager.IsInitialized()
}
