package fastpath

import (
	"encoding/binary"
	"sync/atomic"

	"github.com/cespare/xxhash/v2"
)

// ProductAddressIndexProvider 产品地址索引提供者
// 将(Product+Address)组合映射到bitmap中的索引位置
type ProductAddressIndexProvider interface {
	// GetIndex 获取ProductAddress组合在bitmap中的索引
	GetIndex(key ProductAddressKey) uint32

	// GetIndexFast 获取索引（不做统计，避免热点原子操作开销）
	GetIndexFast(key ProductAddressKey) uint32

	// GetStats 获取索引统计信息
	GetStats() IndexStats
}

// IndexStats 索引统计信息
type IndexStats struct {
	QueryCount     int64 `json:"query_count"`     // 查询次数
	HashCollisions int64 `json:"hash_collisions"` // hash冲突数（预留）
}

// productAddressIndexProviderImpl 索引提供者实现
// 使用纯hash算法，不存储映射关系，节省内存
type productAddressIndexProviderImpl struct {
	stats IndexStats
}

// NewProductAddressIndexProvider 创建索引提供者
func NewProductAddressIndexProvider() ProductAddressIndexProvider {
	return &productAddressIndexProviderImpl{}
}

// GetIndex 获取索引（纯hash算法，确定性映射）
func (p *productAddressIndexProviderImpl) GetIndex(key ProductAddressKey) uint32 {
	// 无锁计数，降低热点锁竞争
	atomic.AddInt64(&p.stats.QueryCount, 1)
	return p.hashKey(key)
}

// GetIndexFast 获取索引（不做统计）
func (p *productAddressIndexProviderImpl) GetIndexFast(key ProductAddressKey) uint32 {
	return p.hashKey(key)
}

// hashKey 计算key的hash值作为索引
func (p *productAddressIndexProviderImpl) hashKey(key ProductAddressKey) uint32 {
	// 使用 xxhash（64bit）且避免分配与 fmt.Sprintf
	// 由于已按 ProductId 分片到独立 bitmap，此处索引仅需在单个 product 的 bitmap 内唯一
	// 因此哈希基于 [origin, dest, groupHash]
	var buf [24]byte
	binary.BigEndian.PutUint64(buf[0:8], key.OriginLocationId)
	binary.BigEndian.PutUint64(buf[8:16], key.DestLocationId)
	groupHash := xxhash.Sum64String(key.CollectDeliverGroupId)
	binary.BigEndian.PutUint64(buf[16:24], groupHash)
	sum := xxhash.Sum64(buf[:])
	return uint32(sum & 0xffffffff)
}

// GetStats 获取统计信息
func (p *productAddressIndexProviderImpl) GetStats() IndexStats {
	// 原子读取，避免加锁
	return IndexStats{
		QueryCount:     atomic.LoadInt64(&p.stats.QueryCount),
		HashCollisions: atomic.LoadInt64(&p.stats.HashCollisions),
	}
}
