package fastpath

import (
	"fmt"
	"strconv"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
)

// 对象池用于复用切片，减少GC压力
var (
	keySlicePool = sync.Pool{
		New: func() interface{} {
			return make([]ProductAddressKey, 0, 50000) // 预分配50k容量
		},
	}
	locationSlicePool = sync.Pool{
		New: func() interface{} {
			return make([]*ServiceableLocationInfo, 0, 1000) // 预分配1k容量
		},
	}
)

// ProductAddressBitmapProvider 真正的bitmap提供者
// 使用RoaringBitmap存储大量Product+Address组合，内存高效
type ProductAddressBitmapProvider interface {
	// IsServiceable 检查Product+Address组合是否支持配送
	// 直接查bitmap，未命中返回false，不会触发构建
	IsServiceable(ctx utils.LCOSContext, region string, key ProductAddressKey) bool

	// BatchIsServiceable 批量检查可配送性
	BatchIsServiceable(ctx utils.LCOSContext, region string, keys []ProductAddressKey) map[ProductAddressKey]bool

	// RebuildBitmap 重建bitmap（预计算使用）
	RebuildBitmap(ctx utils.LCOSContext, region string) error

	// GetBitmapStats 获取bitmap统计信息
	GetBitmapStats(region string) map[string]interface{}

	// GetAggregateStats 获取全量聚合统计（跨所有product的bitmap）
	// 包含总key数量与序列化压缩后的总字节数
	GetAggregateStats() map[string]interface{}

	// GetRegionAggregateStats 按地区聚合统计：每个地区的bitmap数量、总key数、序列化字节数
	// regions 为空则返回所有已存在地区的统计
	GetRegionAggregateStats(regions []string) map[string]map[string]interface{}

	// IsBitmapReady 检查指定地区的bitmap是否已经构建并准备就绪
	IsBitmapReady(region string) bool

	// AreBitmapsReady 检查多个地区的bitmap是否都已经构建并准备就绪
	AreBitmapsReady(regions []string) bool
}

// productAddressBitmapProviderImpl bitmap提供者实现
type productAddressBitmapProviderImpl struct {
	// 按产品存储bitmap（全REGION唯一的PRODUCT_ID）
	productBitmaps map[int32]*ProductAddressBitmap

	// 索引提供者
	indexProvider ProductAddressIndexProvider

	// DAO层依赖
	lineLocationDAO basic_location.LineBasicServiceableLocationDAO

	// 并发控制
	mutex sync.RWMutex

	// 统计信息
	stats ProviderStats
}

// ProviderStats bitmap provider统计信息
type ProviderStats struct {
	QueryCount      int64     `json:"query_count"`
	CacheHits       int64     `json:"cache_hits"`    // bitmap命中
	CacheMisses     int64     `json:"cache_misses"`  // bitmap未命中
	RebuildCount    int64     `json:"rebuild_count"` // 重建次数
	LastRebuildTime time.Time `json:"last_rebuild_time"`
	mutex           sync.RWMutex
}

// NewProductAddressBitmapProvider 创建bitmap提供者
func NewProductAddressBitmapProvider(
	lineLocationDAO basic_location.LineBasicServiceableLocationDAO,
) ProductAddressBitmapProvider {
	indexProvider := NewProductAddressIndexProvider()

	return &productAddressBitmapProviderImpl{
		productBitmaps:  make(map[int32]*ProductAddressBitmap),
		indexProvider:   indexProvider,
		lineLocationDAO: lineLocationDAO,
	}
}

// IsServiceable 检查是否支持配送
func (p *productAddressBitmapProviderImpl) IsServiceable(ctx utils.LCOSContext, region string, key ProductAddressKey) bool {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		SafeMetrics.GaugeSet("product_address_bitmap_query_duration_ms",
			float64(duration.Milliseconds()), map[string]string{"region": region})
	}()

	// 基于PRODUCT_ID路由到对应bitmap，忽略region参数
	bitmap := p.getProductBitmap(key.ProductId)
	if bitmap == nil {
		logger.CtxLogInfof(ctx, "No bitmap found for product %d, returning false", key.ProductId)
		p.updateStats(false)
		return false
	}

	// 直接查bitmap
	result := bitmap.IsServiceable(ctx, key)
	p.updateStats(result)

	return result
}

// BatchIsServiceable 批量检查可配送性
func (p *productAddressBitmapProviderImpl) BatchIsServiceable(ctx utils.LCOSContext, region string, keys []ProductAddressKey) map[ProductAddressKey]bool {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		SafeMetrics.GaugeSet("product_address_bitmap_batch_query_duration_ms",
			float64(duration.Milliseconds()), map[string]string{"region": region})
	}()

	result := make(map[ProductAddressKey]bool, len(keys))

	// 基于PRODUCT_ID分别查询对应bitmap
	// 这样避免跨产品的hash冲突，并支撑按产品分片的存储结构
	// 为减少重复查找，对keys按product分组
	productToKeys := make(map[int32][]ProductAddressKey)
	for _, k := range keys {
		productToKeys[k.ProductId] = append(productToKeys[k.ProductId], k)
	}
	for pid, ks := range productToKeys {
		bitmap := p.getProductBitmap(pid)
		if bitmap == nil {
			for _, k := range ks {
				result[k] = false
				p.updateStats(false)
			}
			continue
		}
		bmRes := bitmap.BatchIsServiceable(ctx, ks)
		for k, v := range bmRes {
			result[k] = v
			p.updateStats(v)
		}
	}

	logger.CtxLogInfof(ctx, "Batch query %d keys in region %s", len(keys), region)
	return result
}

// RebuildBitmap 重建指定地区（数据范围）的bitmap（按PRODUCT分片存储）
func (p *productAddressBitmapProviderImpl) RebuildBitmap(ctx utils.LCOSContext, region string) error {
	rebuildStart := time.Now()
	logger.CtxLogInfof(ctx, "Starting bitmap rebuild for region %s", region)

	// 新架构：直接流式构建到按PRODUCT分片的bitmap
	serviceableCount, err := p.streamBuildBitmap(ctx, region)
	if err != nil {
		return fmt.Errorf("failed to stream build bitmap for region %s: %w", region, err)
	}

	// 4. 更新统计
	p.stats.mutex.Lock()
	p.stats.RebuildCount++
	p.stats.LastRebuildTime = time.Now()
	p.stats.mutex.Unlock()

	duration := time.Since(rebuildStart)
	logger.CtxLogInfof(ctx, "Completed bitmap rebuild for region %s: %d serviceable keys, duration: %v",
		region, serviceableCount, duration)

	// 上报监控指标（安全调用）
	SafeMetrics.GaugeSet("product_address_bitmap_rebuild_duration_seconds",
		duration.Seconds(), map[string]string{"region": region})
	SafeMetrics.GaugeSet("product_address_bitmap_serviceable_keys",
		float64(serviceableCount), map[string]string{"region": region})

	return nil
}

// streamBuildBitmap 构建bitmap（按PRODUCT分片写入）
func (p *productAddressBitmapProviderImpl) streamBuildBitmap(ctx utils.LCOSContext, region string) (int64, error) {
	logger.CtxLogInfof(ctx, "Starting bitmap build for region %s", region)
	start := time.Now()

	// 1. 获取基础数据
	productLanes, laneToProducts, lineToLanes, err := p.getBuildingData(ctx, region)
	if err != nil {
		return 0, err
	}

	logger.CtxLogInfof(ctx, "[BitmapBuild] region=%s products=%d lanes=%d lines=%d preparation=%v",
		region, len(productLanes), len(laneToProducts), len(lineToLanes), time.Since(start))

	// 2. 生成keys并写入（内部路由到各自的product bitmap）
	totalCount, err := p.generateAndWriteKeys(ctx, region, lineToLanes, laneToProducts)
	if err != nil {
		return 0, err
	}

	logger.CtxLogInfof(ctx, "Bitmap build completed for region %s: %d keys, duration=%v",
		region, totalCount, time.Since(start))

	return totalCount, nil
}

// getBuildingData 获取构建所需的所有数据
func (p *productAddressBitmapProviderImpl) getBuildingData(ctx utils.LCOSContext, region string) (
	[]ProductLaneCodeList,
	map[string][]int32,
	map[string][]string,
	error) {

	// 获取Product-Lane信息
	productLanes, err := p.getAllProductLanes(ctx, region)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to get product lanes: %w", err)
	}

	// 构建Lane->Products映射
	laneToProducts := make(map[string][]int32)
	allLaneCodes := make([]string, 0)
	for _, productLane := range productLanes {
		for _, laneCode := range productLane.LaneCodes {
			if _, exists := laneToProducts[laneCode]; !exists {
				allLaneCodes = append(allLaneCodes, laneCode)
			}
			laneToProducts[laneCode] = append(laneToProducts[laneCode], int32(productLane.ProductId))
		}
	}

	// 获取Line-Lane映射
	lineToLanes, err := p.batchGetLineFromLanes(ctx, region, allLaneCodes)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to get line-lane mapping: %w", err)
	}

	return productLanes, laneToProducts, lineToLanes, nil
}

// generateAndWriteKeys 生成并写入所有keys
func (p *productAddressBitmapProviderImpl) generateAndWriteKeys(
	ctx utils.LCOSContext,
	region string,
	lineToLanes map[string][]string,
	laneToProducts map[string][]int32,
) (int64, error) {

	// 获取所有Line IDs
	allLineIds := make([]string, 0, len(lineToLanes))
	for lineId := range lineToLanes {
		allLineIds = append(allLineIds, lineId)
	}

	var totalCount int64
	const batchSize = 500
	const maxBufferSize = 100000

	for i := 0; i < len(allLineIds); i += batchSize {
		end := i + batchSize
		if end > len(allLineIds) {
			end = len(allLineIds)
		}

		batchLines := allLineIds[i:end]
		count, err := p.generateAndWriteKeysForLines(ctx, region, batchLines, lineToLanes, laneToProducts, maxBufferSize)
		if err != nil {
			return totalCount, fmt.Errorf("failed to process batch [%d:%d]: %w", i, end, err)
		}

		totalCount += count
		logger.CtxLogInfof(ctx, "[BitmapBuild-Batch] region=%s lines=%d keys=%d total=%d",
			region, end-i, count, totalCount)
	}

	return totalCount, nil
}

// generateAndWriteKeysForLines 并发生成并写入keys
func (p *productAddressBitmapProviderImpl) generateAndWriteKeysForLines(
	ctx utils.LCOSContext,
	region string,
	lineIds []string,
	lineToLanes map[string][]string,
	laneToProducts map[string][]int32,
	maxBufferSize int) (int64, error) {

	// 获取Lines的location数据
	lineLocations, err := p.getAllLineServiceableLocations(ctx, lineIds, region)
	if err != nil {
		return 0, fmt.Errorf("failed to get locations: %w", err)
	}

	// 收集所有Line+Group任务
	var tasks []lineGroupTask
	for lineId, locations := range lineLocations {
		// 获取该Line的Products
		productSet := make(map[int32]struct{})
		for _, laneCode := range lineToLanes[lineId] {
			for _, pid := range laneToProducts[laneCode] {
				productSet[pid] = struct{}{}
			}
		}

		// 按CollectDeliverGroupId分组，每个组作为一个任务
		groupToLocations := make(map[string][]*ServiceableLocationInfo)
		for _, loc := range locations {
			groupToLocations[loc.CollectDeliverGroupId] = append(groupToLocations[loc.CollectDeliverGroupId], loc)
		}

		for groupId, groupLocs := range groupToLocations {
			tasks = append(tasks, lineGroupTask{
				lineId:     lineId,
				groupId:    groupId,
				locations:  groupLocs,
				productSet: productSet,
			})
		}
	}

	logger.CtxLogInfof(ctx, "[KeyGeneration] region=%s total_line_groups=%d", region, len(tasks))

	// 使用worker池并发处理Line+Group任务
	return p.processLineGroupTasks(ctx, region, tasks, maxBufferSize)
}

// processLineGroupTasks 并发处理Line+Group任务，使用PRODUCT_ID分片
func (p *productAddressBitmapProviderImpl) processLineGroupTasks(
	ctx utils.LCOSContext,
	region string,
	tasks []lineGroupTask,
	maxBufferSize int) (int64, error) {

	// 计算worker数量
	workerNum := len(tasks)
	maxWorkers := 8 // 限制最大并发数
	if workerNum > maxWorkers {
		workerNum = maxWorkers
	}
	if workerNum < 1 {
		workerNum = 1
	}

	// 任务分发channel
	taskCh := make(chan lineGroupTask, len(tasks))
	for _, task := range tasks {
		taskCh <- task
	}
	close(taskCh)

	// 结果收集channel（收敛缓冲，降低内存占用）
	resultBuf := workerNum * 2
	if resultBuf < 16 {
		resultBuf = 16
	}
	if resultBuf > 32 { // 设置上限避免过度内存使用
		resultBuf = 32
	}
	resultCh := make(chan []ProductAddressKey, resultBuf)
	var wg sync.WaitGroup

	// 启动worker
	for i := 0; i < workerNum; i++ {
		wg.Add(1)
		go func(workerId int) {
			defer func() {
				if r := recover(); r != nil {
					logger.CtxLogErrorf(ctx, "[KeyGeneration-Worker] worker=%d panic: %v", workerId, r)
				}
				wg.Done()
			}()

			for task := range taskCh {
				taskStart := time.Now()
				// 每个任务内流式产出，限制单块大小，避免内存峰值
				const chunkSize = 100000 // 每块最多10万条key（更细颗粒，减轻 resultCh 背压）
				var produced int64
				p.generateKeysForLineGroupStream(task.locations, task.productSet, chunkSize, func(chunk []ProductAddressKey) {
					if len(chunk) == 0 {
						return
					}
					produced += int64(len(chunk))
					// 直接发送chunk，减少内存拷贝开销
					resultCh <- chunk
				})
				// 任务级日志：每个 Line+Group 完成后输出一次
				logger.CtxLogInfof(ctx, "[KeyGeneration-Task] region=%s line=%s group=%s locations=%d products=%d keys=%d duration=%v",
					region, task.lineId, task.groupId, len(task.locations), len(task.productSet), produced, time.Since(taskStart))
			}
		}(i)
	}

	// 等待所有worker完成
	go func() {
		wg.Wait()
		close(resultCh)
	}()

	// 收集结果并按PRODUCT_ID分组写入到各自bitmap
	var totalCount int64
	productBuffers := make(map[int32][]ProductAddressKey)
	var writeWg sync.WaitGroup

	flushProduct := func(productId int32, buf []ProductAddressKey) {
		if len(buf) == 0 {
			return
		}
		writeWg.Add(1)
		go func(pid int32, batch []ProductAddressKey) {
			defer writeWg.Done()
			bm := p.getOrCreateProductBitmap(pid, region)
			bm.BatchSetServiceable(ctx, batch)
		}(productId, buf)
		totalCount += int64(len(buf))
	}

	for keys := range resultCh {
		for _, key := range keys {
			pid := key.ProductId
			productBuffers[pid] = append(productBuffers[pid], key)
			if len(productBuffers[pid]) >= maxBufferSize {
				flushProduct(pid, productBuffers[pid])
				productBuffers[pid] = productBuffers[pid][:0]
			}
		}
	}

	// flush remainings
	for pid, buf := range productBuffers {
		if len(buf) > 0 {
			flushProduct(pid, buf)
		}
	}
	writeWg.Wait()

	logger.CtxLogInfof(ctx, "[KeyGeneration] completed workers=%d total_keys=%d", workerNum, totalCount)
	return totalCount, nil
}

// generateKeysForLineGroupStream 为单个 Line+Group 生成 keys（流式回调，按块返回）
// 内存优化版本：减少对象分配和GC压力
func (p *productAddressBitmapProviderImpl) generateKeysForLineGroupStream(
	locations []*ServiceableLocationInfo,
	productSet map[int32]struct{},
	chunkSize int,
	emit func([]ProductAddressKey),
) {
	if chunkSize <= 0 {
		chunkSize = 50000 // 减少默认chunk大小，降低内存峰值
	}

	// 从对象池获取缓冲区，减少内存分配
	buf := keySlicePool.Get().([]ProductAddressKey)
	defer func() {
		// 清空切片并归还到对象池
		buf = buf[:0]
		keySlicePool.Put(buf)
	}()

	// 从对象池获取location切片
	pickupLocs := locationSlicePool.Get().([]*ServiceableLocationInfo)
	deliverLocs := locationSlicePool.Get().([]*ServiceableLocationInfo)
	defer func() {
		// 清空并归还到对象池
		pickupLocs = pickupLocs[:0]
		deliverLocs = deliverLocs[:0]
		locationSlicePool.Put(pickupLocs)
		locationSlicePool.Put(deliverLocs)
	}()

	for _, loc := range locations {
		if loc.CanPickup {
			pickupLocs = append(pickupLocs, loc)
		}
		if loc.CanDeliver {
			deliverLocs = append(deliverLocs, loc)
		}
	}

	// 如果没有有效的pickup或deliver locations，直接返回
	if len(pickupLocs) == 0 || len(deliverLocs) == 0 {
		return
	}

	// 字符串池化：缓存CollectDeliverGroupId，避免重复分配
	groupIdCache := make(map[string]string)
	getGroupId := func(groupId string) string {
		if cached, exists := groupIdCache[groupId]; exists {
			return cached
		}
		groupIdCache[groupId] = groupId
		return groupId
	}

	flush := func() {
		if len(buf) == 0 {
			return
		}
		// 创建副本发送，避免并发问题
		chunk := make([]ProductAddressKey, len(buf))
		copy(chunk, buf)
		emit(chunk)
		buf = buf[:0] // 重置切片长度，但保留底层数组
	}

	// 优化的嵌套循环：先按group过滤，减少无效组合
	for _, originLoc := range pickupLocs {
		cachedGroupId := getGroupId(originLoc.CollectDeliverGroupId)

		for _, destLoc := range deliverLocs {
			// 提前检查group匹配和location不同，避免无效计算
			if originLoc.LocationId == destLoc.LocationId ||
			   destLoc.CollectDeliverGroupId != originLoc.CollectDeliverGroupId {
				continue
			}

			// 批量生成该location pair的所有product keys
			for productId := range productSet {
				buf = append(buf, ProductAddressKey{
					ProductId:             productId,
					OriginLocationId:      originLoc.LocationId,
					DestLocationId:        destLoc.LocationId,
					CollectDeliverGroupId: cachedGroupId, // 使用缓存的字符串
				})

				// 检查是否需要flush，减少内存占用
				if len(buf) >= chunkSize {
					flush()
				}
			}
		}
	}
	flush()
}

// extractLineIDsFromLaneInfo 从Lane信息中提取Line ID列表
func (p *productAddressBitmapProviderImpl) extractLineIDsFromLaneInfo(ctx utils.LCOSContext, laneInfo *lfs_service.LaneCodeStruct) []string {
	var lineIds []string

	for _, compose := range laneInfo.Composes {
		// 只处理Line类型的compose
		if compose.ResourceType == constant.LaneComposeLine {
			lineIds = append(lineIds, compose.ResourceID)
		}
	}

	logger.CtxLogDebugf(ctx, "Extracted %d lines from lane %s: %v",
		len(lineIds), laneInfo.LaneCode, lineIds)

	return lineIds
}

// getProductBitmap 获取指定产品的bitmap
func (p *productAddressBitmapProviderImpl) getProductBitmap(productId int32) *ProductAddressBitmap {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.productBitmaps[productId]
}

// getOrCreateProductBitmap 获取或创建指定产品的bitmap（region仅用于日志/指标打点）
func (p *productAddressBitmapProviderImpl) getOrCreateProductBitmap(productId int32, region string) *ProductAddressBitmap {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	if bm, ok := p.productBitmaps[productId]; ok {
		return bm
	}
	bm := NewProductAddressBitmap(region, p.indexProvider)
	p.productBitmaps[productId] = bm
	return bm
}

// updateStats 更新统计信息
func (p *productAddressBitmapProviderImpl) updateStats(hit bool) {
	p.stats.mutex.Lock()
	defer p.stats.mutex.Unlock()

	p.stats.QueryCount++
	if hit {
		p.stats.CacheHits++
	} else {
		p.stats.CacheMisses++
	}
}

// GetBitmapStats 获取bitmap统计信息
func (p *productAddressBitmapProviderImpl) GetBitmapStats(region string) map[string]interface{} {
	// 兼容原接口：聚合所有product bitmap的统计信息
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	if len(p.productBitmaps) == 0 {
		return map[string]interface{}{
			"error": "No product bitmaps available",
		}
	}

	// 简化：汇总第一个bitmap的信息并附加全局统计
	var anyBm *ProductAddressBitmap
	for _, bm := range p.productBitmaps {
		anyBm = bm
		break
	}
	if anyBm == nil {
		return map[string]interface{}{"error": "No product bitmaps available"}
	}
	bitmapInfo := anyBm.GetInfo()

	// 添加provider级别的统计
	p.stats.mutex.RLock()
	bitmapInfo["provider_query_count"] = p.stats.QueryCount
	bitmapInfo["provider_cache_hits"] = p.stats.CacheHits
	bitmapInfo["provider_cache_misses"] = p.stats.CacheMisses
	bitmapInfo["provider_hit_rate"] = float64(p.stats.CacheHits) / float64(p.stats.QueryCount)
	bitmapInfo["provider_rebuild_count"] = p.stats.RebuildCount
	bitmapInfo["provider_last_rebuild"] = p.stats.LastRebuildTime
	p.stats.mutex.RUnlock()

	return bitmapInfo
}

// GetAggregateStats 聚合统计：总keys与序列化压缩字节数
func (p *productAddressBitmapProviderImpl) GetAggregateStats() map[string]interface{} {
	// 复制一份指针切片，避免长时间持有provider锁
	p.mutex.RLock()
	bitmaps := make([]*ProductAddressBitmap, 0, len(p.productBitmaps))
	for _, bm := range p.productBitmaps {
		bitmaps = append(bitmaps, bm)
	}
	p.mutex.RUnlock()

	var totalKeys uint64
	var totalBytes int64
	for _, bm := range bitmaps {
		totalKeys += bm.Cardinality()
		if data, err := bm.Serialize(); err == nil {
			totalBytes += int64(len(data))
		}
	}

	return map[string]interface{}{
		"total_keys":             totalKeys,
		"total_serialized_bytes": totalBytes,
		"product_bitmap_count":   len(bitmaps),
	}
}

// GetRegionAggregateStats 实现按地区的聚合统计
func (p *productAddressBitmapProviderImpl) GetRegionAggregateStats(regions []string) map[string]map[string]interface{} {
	// 构造过滤集合（可选）
	regionFilter := make(map[string]struct{})
	if len(regions) > 0 {
		for _, r := range regions {
			regionFilter[r] = struct{}{}
		}
	}

	// 复制指针切片，避免长时间持锁
	p.mutex.RLock()
	bitmaps := make([]*ProductAddressBitmap, 0, len(p.productBitmaps))
	for _, bm := range p.productBitmaps {
		bitmaps = append(bitmaps, bm)
	}
	p.mutex.RUnlock()

	result := make(map[string]map[string]interface{})
	// 先把目标地区建好空壳，保证都有输出
	if len(regionFilter) > 0 {
		for r := range regionFilter {
			result[r] = map[string]interface{}{
				"product_bitmap_count":   0,
				"total_keys":             uint64(0),
				"total_serialized_bytes": int64(0),
			}
		}
	}

	for _, bm := range bitmaps {
		// 读取region字段
		// 轻锁读取需要的元数据
		bmRegion := func() string {
			bm.mutex.RLock()
			defer bm.mutex.RUnlock()
			return bm.region
		}()

		if len(regionFilter) > 0 {
			if _, ok := regionFilter[bmRegion]; !ok {
				continue
			}
		}

		stats, ok := result[bmRegion]
		if !ok {
			stats = map[string]interface{}{
				"product_bitmap_count":   0,
				"total_keys":             uint64(0),
				"total_serialized_bytes": int64(0),
			}
			result[bmRegion] = stats
		}

		// 更新统计
		stats["product_bitmap_count"] = stats["product_bitmap_count"].(int) + 1
		stats["total_keys"] = stats["total_keys"].(uint64) + bm.Cardinality()
		if data, err := bm.Serialize(); err == nil {
			stats["total_serialized_bytes"] = stats["total_serialized_bytes"].(int64) + int64(len(data))
		}
	}

	return result
}

// IsBitmapReady 检查指定地区的bitmap是否已经构建并准备就绪
func (p *productAddressBitmapProviderImpl) IsBitmapReady(region string) bool {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return len(p.productBitmaps) > 0
}

// AreBitmapsReady 检查多个地区的bitmap是否都已经构建并准备就绪
func (p *productAddressBitmapProviderImpl) AreBitmapsReady(regions []string) bool {
	if len(regions) == 0 {
		return true
	}

	for _, region := range regions {
		if !p.IsBitmapReady(region) {
			return false
		}
	}

	return true
}

// getAllProductLanes 获取所有Product的Lane信息（复用原有逻辑）
func (p *productAddressBitmapProviderImpl) getAllProductLanes(ctx utils.LCOSContext, region string) ([]ProductLaneCodeList, error) {
	logger.CtxLogDebugf(ctx, "Getting all product lanes for region: %s", region)
	start := time.Now()

	// 调用product_service获取product-lane映射
	productLaneCodeLists, err := product_service.ListLaneCodesByRegion(ctx, region)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get product lanes from product service: region=%s, error=%v", region, err)
		return nil, fmt.Errorf("product service call failed: %v", err)
	}

	// 转换为内部结构
	result := make([]ProductLaneCodeList, len(productLaneCodeLists))
	for i, item := range productLaneCodeLists {
		result[i] = ProductLaneCodeList{
			ProductId: item.ProductId,
			LaneCodes: item.LaneCodes,
		}
	}

	logger.CtxLogInfof(ctx, "Successfully retrieved %d products with lanes for region %s, duration=%v",
		len(result), region, time.Since(start))
	return result, nil
}

// ProductLaneCodeList Product-Lane映射结构
type ProductLaneCodeList struct {
	ProductId int      `json:"product_id"`
	LaneCodes []string `json:"lane_codes"`
}

// batchGetLineFromLanes 批量获取Lane到Line的映射关系
func (p *productAddressBitmapProviderImpl) batchGetLineFromLanes(ctx utils.LCOSContext, region string, laneCodes []string) (map[string][]string, error) {
	start := time.Now()
	lineToLanes := make(map[string][]string)
	if len(laneCodes) == 0 {
		return lineToLanes, nil
	}

	// 使用批量接口一次性获取所有Lane信息
	lfsService := lfs_service.NewLFSService(ctx, region)
	laneInfoMap, err := lfsService.BatchQueryLaneInfoWithPaging(ctx, laneCodes, 100)
	if err != nil {
		logger.CtxLogErrorf(ctx, "[RebuildBitmap-LFS] BatchQueryLaneInfoWithPaging failed: %v", err)
		return nil, fmt.Errorf("lfs_service.BatchQueryLaneInfoWithPaging failed: %v", err)
	}

	logger.CtxLogInfof(ctx, "[RebuildBitmap-LFS] Received responses for %d lanes from batch query.", len(laneInfoMap))

	// 遍历返回的Lane信息，提取Line ID
	for laneCode, laneInfo := range laneInfoMap {
		if laneInfo == nil {
			continue
		}
		lineIds := p.extractLineIDsFromLaneInfo(ctx, laneInfo)
		for _, lineId := range lineIds {
			lineToLanes[lineId] = append(lineToLanes[lineId], laneCode)
		}
	}

	logger.CtxLogInfof(ctx, "[RebuildBitmap-Stage2-LFS-Detail] region=%s lanes=%d lines=%d duration=%v",
		region, len(laneCodes), len(lineToLanes), time.Since(start))
	return lineToLanes, nil
}

// ServiceableLocationInfo 服务范围location信息
type ServiceableLocationInfo struct {
	LocationId            uint64
	CanPickup             bool
	CanDeliver            bool
	Region                string
	CollectDeliverGroupId string
}

// lineGroupTask Line+Group任务单元，用于并发处理
type lineGroupTask struct {
	lineId     string
	groupId    string
	locations  []*ServiceableLocationInfo
	productSet map[int32]struct{}
}

// getAllLineServiceableLocations 批量获取所有Line的服务范围location数据
func (p *productAddressBitmapProviderImpl) getAllLineServiceableLocations(ctx utils.LCOSContext, lineIds []string, region string) (map[string][]*ServiceableLocationInfo, error) {
	start := time.Now()

	if len(lineIds) == 0 {
		return make(map[string][]*ServiceableLocationInfo), nil
	}

	if p.lineLocationDAO == nil {
		return nil, fmt.Errorf("line location DAO not available")
	}

	logger.CtxLogInfof(ctx, "Starting query for %d lines in region %s", len(lineIds), region)

	// 分批处理
	batches := p.createLineBatches(lineIds, 100)
	result := make(map[string][]*ServiceableLocationInfo)
	var mutex sync.Mutex
	var wg sync.WaitGroup
	errorChan := make(chan error, len(batches))

	logger.CtxLogInfof(ctx, "[LineQuery] region=%s total_lines=%d batch_size=%d total_batches=%d",
		region, len(lineIds), 100, len(batches))

	// 并发处理所有批次
	for i, batch := range batches {
		wg.Add(1)
		go func(batchIdx int, batchLineIds []string) {
			defer wg.Done()

			logger.CtxLogInfof(ctx, "[LineQuery-Batch-Start] region=%s batch_idx=%d size=%d",
				region, batchIdx, len(batchLineIds))

			callStart := time.Now()

			// 调用DAO查询
			locationTabsMap, lcosErr := p.lineLocationDAO.BatchSearchAllBasicServiceableLocation(ctx, batchLineIds, region)
			if lcosErr != nil {
				errorChan <- fmt.Errorf("batch %d failed: %v", batchIdx, lcosErr)
				return
			}

			logger.CtxLogInfof(ctx, "[LineQuery-Batch-Done] region=%s batch_idx=%d returned_lines=%d duration=%v",
				region, batchIdx, len(locationTabsMap), time.Since(callStart))

			// 转换并合并结果
			batchResult := p.convertToServiceableLocationInfo(ctx, locationTabsMap, region)

			mutex.Lock()
			for lineId, locations := range batchResult {
				result[lineId] = locations
			}
			mutex.Unlock()
		}(i, batch)
	}

	// 等待所有批次完成
	wg.Wait()
	close(errorChan)

	// 检查错误
	for err := range errorChan {
		if err != nil {
			logger.CtxLogErrorf(ctx, "Error in line processing: %v", err)
			return nil, err
		}
	}

	logger.CtxLogInfof(ctx, "Query completed: requested_lines=%d serviceable_lines=%d duration=%v",
		len(lineIds), len(result), time.Since(start))
	return result, nil
}

// createLineBatches 将lineIds分批
func (p *productAddressBitmapProviderImpl) createLineBatches(lineIds []string, batchSize int) [][]string {
	var batches [][]string
	for i := 0; i < len(lineIds); i += batchSize {
		end := i + batchSize
		if end > len(lineIds) {
			end = len(lineIds)
		}
		batches = append(batches, lineIds[i:end])
	}
	return batches
}

// convertToServiceableLocationInfo 转换DAO返回的数据为内部格式
func (p *productAddressBitmapProviderImpl) convertToServiceableLocationInfo(ctx utils.LCOSContext, locationTabsMap map[string][]*basic_location.LineBasicServiceableLocationTab, region string) map[string][]*ServiceableLocationInfo {
	result := make(map[string][]*ServiceableLocationInfo)

	for lineId, locationTabs := range locationTabsMap {
		if len(locationTabs) == 0 {
			continue
		}

		locations := p.filterAndDeduplicateLocations(locationTabs)
		if len(locations) > 0 {
			result[lineId] = locations
			logger.CtxLogDebugf(ctx, "Line %s has %d serviceable locations in region %s",
				lineId, len(locations), region)
		}
	}

	return result
}

// filterAndDeduplicateLocations 过滤并去重位置数据
func (p *productAddressBitmapProviderImpl) filterAndDeduplicateLocations(locationTabs []*basic_location.LineBasicServiceableLocationTab) []*ServiceableLocationInfo {
	var locations []*ServiceableLocationInfo
	seen := make(map[string]struct{})

	for _, tab := range locationTabs {
		// 检查pickup和deliver能力
		canPickup := tab.CanPickup != nil && *tab.CanPickup == constant.TRUE
		canDeliver := tab.CanDeliver != nil && *tab.CanDeliver == constant.TRUE

		// 只保留有pickup或deliver能力的location
		if !canPickup && !canDeliver {
			continue
		}

		// 去重：同一(groupId, locationId)只保留一次
		dupKey := tab.CollectDeliverGroupId + ":" + strconv.FormatUint(tab.LocationId, 10)
		if _, exists := seen[dupKey]; exists {
			continue
		}
		seen[dupKey] = struct{}{}

		locations = append(locations, &ServiceableLocationInfo{
			LocationId:            tab.LocationId,
			CanPickup:             canPickup,
			CanDeliver:            canDeliver,
			Region:                tab.Region,
			CollectDeliverGroupId: tab.CollectDeliverGroupId,
		})
	}

	return locations
}
