package fastpath

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
)

// PrecomputeBitmapTask bitmap预计算任务
// 定时重建RoaringBitmap，内存高效地存储大量Product+Address组合
type PrecomputeBitmapTask struct {
	bitmapProvider ProductAddressBitmapProvider

	// 配置
	refreshInterval time.Duration // 刷新间隔

	// 状态控制
	isRunning bool
	stopChan  chan struct{}
	ticker    *time.Ticker
	mutex     sync.RWMutex

	// 统计信息
	lastRefreshTime time.Time
	totalRebuilt    int64
	errorCount      int64
}

// PrecomputeBitmapStats bitmap预计算统计信息
type PrecomputeBitmapStats struct {
	IsRunning       bool      `json:"is_running"`
	LastRefreshTime time.Time `json:"last_refresh_time"`
	TotalRebuilt    int64     `json:"total_rebuilt"`
	ErrorCount      int64     `json:"error_count"`
	NextRefreshTime time.Time `json:"next_refresh_time"`
	AllRegions      []string  `json:"all_regions"`
}

// NewPrecomputeBitmapTask 创建bitmap预计算任务
func NewPrecomputeBitmapTask(
	lineLocationDAO basic_location.LineBasicServiceableLocationDAO,
) *PrecomputeBitmapTask {
	bitmapProvider := NewProductAddressBitmapProvider(lineLocationDAO)

	return &PrecomputeBitmapTask{
		bitmapProvider:  bitmapProvider,
		refreshInterval: 5 * time.Minute, // 5分钟刷新
		stopChan:        make(chan struct{}),
	}
}

// Start 启动bitmap预计算任务
func (task *PrecomputeBitmapTask) Start(ctx context.Context) error {
	task.mutex.Lock()
	defer task.mutex.Unlock()

	if task.isRunning {
		return nil // 已经在运行
	}

	task.isRunning = true
	task.ticker = time.NewTicker(task.refreshInterval)

	logger.LogInfof("Starting bitmap precompute task with interval %v for enabled regions",
		task.refreshInterval)

	// 定时执行（不在启动时立即执行，避免和初始化的首次全量构建重复）
	go func() {
		for {
			select {
			case <-task.ticker.C:
				task.executePrecompute(ctx)
			case <-task.stopChan:
				logger.LogInfof("Bitmap precompute task stopped")
				return
			}
		}
	}()

	return nil
}

// Stop 停止bitmap预计算任务
func (task *PrecomputeBitmapTask) Stop() {
	task.mutex.Lock()
	defer task.mutex.Unlock()

	if !task.isRunning {
		return
	}

	task.isRunning = false
	close(task.stopChan)
	if task.ticker != nil {
		task.ticker.Stop()
	}

	logger.LogInfof("Bitmap precompute task stop requested")
}

// executePrecompute 执行bitmap预计算
func (task *PrecomputeBitmapTask) executePrecompute(ctx context.Context) {
	startTime := time.Now()
	lcosCtx := utils.NewCommonCtx(ctx)

	enabledRegions := task.getEnabledRegions(ctx)
	if len(enabledRegions) == 0 {
		logger.LogInfof("No regions enabled for bitmap precompute, skipping")
		return
	}

	logger.LogInfof("Starting bitmap precompute for enabled regions: %v", enabledRegions)

	var totalRebuilt int64
	var errorCount int64

	// 并发重建各个启用地区的bitmap
	var wg sync.WaitGroup
	errorChan := make(chan error, len(enabledRegions))

	for _, region := range enabledRegions {
		wg.Add(1)
		go func(r string) {
			defer wg.Done()

			regionStart := time.Now()
			logger.LogInfof("Rebuilding bitmap for region %s", r)

			if err := task.bitmapProvider.RebuildBitmap(lcosCtx, r); err != nil {
				logger.LogErrorf("Failed to rebuild bitmap for region %s: %v", r, err)
				errorChan <- err
			} else {
				logger.LogInfof("Successfully rebuilt bitmap for region %s, duration: %v",
					r, time.Since(regionStart))
				// 原子操作更新计数
				task.incrementRebuiltCount()
			}
		}(region)
	}

	// 等待所有地区完成
	wg.Wait()
	close(errorChan)

	// 统计错误
	for err := range errorChan {
		if err != nil {
			errorCount++
		}
	}

	// 更新统计信息
	task.mutex.Lock()
	task.lastRefreshTime = time.Now()
	task.totalRebuilt += totalRebuilt
	task.errorCount += errorCount
	task.mutex.Unlock()

	duration := time.Since(startTime)
	// 聚合统计：全量 + 分地区
	agg := task.bitmapProvider.GetAggregateStats()
	logger.LogInfof("Bitmap precompute completed: rebuilt=%d regions, errors=%d, duration=%v, total_keys=%v, total_bytes=%v, product_bitmaps=%v",
		len(enabledRegions)-int(errorCount), errorCount, duration,
		agg["total_keys"], agg["total_serialized_bytes"], agg["product_bitmap_count"])

	regionAgg := task.bitmapProvider.GetRegionAggregateStats(enabledRegions)
	for region, s := range regionAgg {
		logger.LogInfof("Bitmap region stats | region=%s product_bitmaps=%v total_keys=%v total_bytes=%v",
			region, s["product_bitmap_count"], s["total_keys"], s["total_serialized_bytes"])
	}

	// 上报指标（安全调用）
	SafeMetrics.GaugeSet("product_address_bitmap_precompute_duration_seconds",
		duration.Seconds(), map[string]string{})
	SafeMetrics.GaugeSet("product_address_bitmap_precompute_regions",
		float64(len(enabledRegions)), map[string]string{})
	SafeMetrics.GaugeSet("product_address_bitmap_precompute_errors",
		float64(errorCount), map[string]string{})
	if v, ok := agg["total_serialized_bytes"].(int64); ok {
		SafeMetrics.GaugeSet("product_address_bitmap_total_serialized_bytes", float64(v), map[string]string{})
	}
	if v, ok := agg["total_keys"].(uint64); ok {
		SafeMetrics.GaugeSet("product_address_bitmap_total_keys", float64(v), map[string]string{})
	}
	// 分地区指标
	for region, s := range regionAgg {
		if v, ok := s["total_serialized_bytes"].(int64); ok {
			SafeMetrics.GaugeSet("product_address_bitmap_region_serialized_bytes", float64(v), map[string]string{"region": region})
		}
		if v, ok := s["total_keys"].(uint64); ok {
			SafeMetrics.GaugeSet("product_address_bitmap_region_total_keys", float64(v), map[string]string{"region": region})
		}
		if v, ok := s["product_bitmap_count"].(int); ok {
			SafeMetrics.GaugeSet("product_address_bitmap_region_bitmap_count", float64(v), map[string]string{"region": region})
		}
	}
}

// incrementRebuiltCount 原子递增重建计数
func (task *PrecomputeBitmapTask) incrementRebuiltCount() {
	task.mutex.Lock()
	defer task.mutex.Unlock()
	task.totalRebuilt++
}

// GetStats 获取bitmap预计算统计信息
func (task *PrecomputeBitmapTask) GetStats() PrecomputeBitmapStats {
	task.mutex.RLock()
	defer task.mutex.RUnlock()

	var nextRefreshTime time.Time
	if task.isRunning && !task.lastRefreshTime.IsZero() {
		nextRefreshTime = task.lastRefreshTime.Add(task.refreshInterval)
	}

	return PrecomputeBitmapStats{
		IsRunning:       task.isRunning,
		LastRefreshTime: task.lastRefreshTime,
		TotalRebuilt:    task.totalRebuilt,
		ErrorCount:      task.errorCount,
		NextRefreshTime: nextRefreshTime,
		AllRegions:      task.getEnabledRegions(context.Background()),
	}
}

// UpdateConfig 更新配置
func (task *PrecomputeBitmapTask) UpdateConfig(refreshInterval time.Duration) {
	task.mutex.Lock()
	defer task.mutex.Unlock()

	task.refreshInterval = refreshInterval

	// 如果正在运行，重启ticker
	if task.isRunning && task.ticker != nil {
		task.ticker.Stop()
		task.ticker = time.NewTicker(task.refreshInterval)
	}

	logger.LogInfof("Updated bitmap precompute config: interval=%v",
		refreshInterval)
}

// GetBitmapStats 获取指定地区的bitmap统计信息
func (task *PrecomputeBitmapTask) GetBitmapStats(region string) map[string]interface{} {
	return task.bitmapProvider.GetBitmapStats(region)
}

// GetBitmapProvider 获取bitmap提供者（用于测试）
func (task *PrecomputeBitmapTask) GetBitmapProvider() ProductAddressBitmapProvider {
	return task.bitmapProvider
}

// getEnabledRegions 获取需要加载bitmap的区域列表
// 如果白名单为空，返回空列表（不加载任何region的bitmap）
func (task *PrecomputeBitmapTask) getEnabledRegions(ctx context.Context) []string {
	regions := config.GetItemCardBitmapLoadRegionList(ctx)
	if len(regions) == 0 {
		logger.LogInfof("ItemCard bitmap load region whitelist is empty, no regions will be loaded")
		return []string{}
	}
	logger.LogInfof("ItemCard bitmap load enabled regions: %v", regions)
	return regions
}

// ExecuteInitialBuild 同步执行初始构建，确保启用地区的bitmap都构建完成
// 这个方法主要用于服务启动时的初次构建，会阻塞直到所有启用地区的bitmap构建完成
func (task *PrecomputeBitmapTask) ExecuteInitialBuild(ctx context.Context) error {
	enabledRegions := constant.ALL_REGIONS

	logger.LogInfof("Starting initial bitmap build for enabled regions: %v", enabledRegions)
	startTime := time.Now()

	// 检查是否已经有构建好的bitmap
	readyRegions := make([]string, 0)
	needBuildRegions := make([]string, 0)

	for _, region := range enabledRegions {
		if task.bitmapProvider.IsBitmapReady(region) {
			readyRegions = append(readyRegions, region)
			logger.LogInfof("Bitmap for region %s is already ready", region)
		} else {
			needBuildRegions = append(needBuildRegions, region)
		}
	}

	if len(needBuildRegions) == 0 {
		logger.LogInfof("All bitmaps are already ready for regions: %v", readyRegions)
		return nil
	}

	// 并发构建所有需要的bitmap
	lcosCtx := utils.NewCommonCtx(ctx)
	var wg sync.WaitGroup
	var errorsMutex sync.Mutex
	var errors []string

	for _, r := range needBuildRegions {
		wg.Add(1)
		go func(region string) {
			defer wg.Done()

			regionStart := time.Now()
			logger.LogInfof("Building initial bitmap for region %s", region)

			if err := task.bitmapProvider.RebuildBitmap(lcosCtx, region); err != nil {
				logger.LogErrorf("Failed to build initial bitmap for region %s: %v", region, err)
				errorsMutex.Lock()
				errors = append(errors, fmt.Sprintf("region %s: %v", region, err))
				errorsMutex.Unlock()
			} else {
				logger.LogInfof("Successfully built initial bitmap for region %s, duration: %v",
					region, time.Since(regionStart))
			}
		}(r)
	}

	// 等待所有地区完成
	wg.Wait()

	if len(errors) > 0 {
		return fmt.Errorf("initial bitmap build failed for some regions: %v", errors)
	}

	duration := time.Since(startTime)
	logger.LogInfof("Initial bitmap build completed for %d regions, duration: %v",
		len(needBuildRegions), duration)

	// 验证所有启用地区的bitmap都已经准备就绪
	if !task.bitmapProvider.AreBitmapsReady(enabledRegions) {
		return fmt.Errorf("bitmap verification failed: not all enabled regions are ready after build")
	}

	logger.LogInfof("All bitmaps verified and ready for enabled regions: %v", enabledRegions)
	return nil
}
