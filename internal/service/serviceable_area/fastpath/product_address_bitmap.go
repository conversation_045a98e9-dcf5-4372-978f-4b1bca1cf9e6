package fastpath

import (
	"encoding/gob"
	"fmt"
	"sort"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"github.com/RoaringBitmap/roaring"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

var (
	// 确保gob注册只执行一次
	productAddressBitmapGobRegistered sync.Once
)

func init() {
	productAddressBitmapGobRegistered.Do(func() {
		// 注册用于序列化的类型
		gob.Register(&ProductAddressBitmap{})
		gob.Register(&ProductAddressBitmapStats{})
		gob.Register(&SerializableBitmapData{})
		gob.Register(roaring.New())
	})
}

// ProductAddressKey 产品+地址组合键
type ProductAddressKey struct {
	ProductId             int32
	OriginLocationId      uint64
	DestLocationId        uint64
	CollectDeliverGroupId string
}

// String 生成缓存键字符串
func (k ProductAddressKey) String() string {
	return fmt.Sprintf("PRODUCT_ADDRESS:%d:%d:%d:%s",
		k.ProductId, k.OriginLocationId, k.DestLocationId, k.CollectDeliverGroupId)
}

// ProductAddressBitmap 真正的产品地址bitmap
// 使用RoaringBitmap存储大量的(Product+Address)组合
type ProductAddressBitmap struct {
	// 核心bitmap，每个bit代表一个(Product+Address)组合是否支持配送
	bitmap *roaring.Bitmap

	// 索引提供者，负责将key映射到bitmap位置
	indexProvider ProductAddressIndexProvider

	// 元数据
	version   string    // bitmap版本
	buildTime time.Time // 构建时间
	region    string    // 所属地区

	// 并发控制
	mutex sync.RWMutex

	// 统计信息
	stats ProductAddressBitmapStats
}

// ProductAddressBitmapStats product address bitmap统计信息
type ProductAddressBitmapStats struct {
	TotalBits       uint64        `json:"total_bits"`       // 总bit数
	SetBits         uint64        `json:"set_bits"`         // 设置为1的bit数
	QueryCount      int64         `json:"query_count"`      // 查询次数
	HitCount        int64         `json:"hit_count"`        // 命中次数 (bit=1)
	MissCount       int64         `json:"miss_count"`       // 未命中次数 (bit=0)
	LastQueryTime   time.Time     `json:"last_query_time"`  // 最后查询时间
	BuildDuration   time.Duration `json:"build_duration"`   // 构建耗时
	CompressionSize int           `json:"compression_size"` // 压缩后大小(bytes)
}

// SerializableBitmapData 可序列化的bitmap数据
// 用于localcache持久化和跨进程传输
type SerializableBitmapData struct {
	BitmapBytes []byte                    `json:"bitmap_bytes"` // RoaringBitmap序列化后的数据
	Version     string                    `json:"version"`      // 版本号
	BuildTime   time.Time                 `json:"build_time"`   // 构建时间
	Region      string                    `json:"region"`       // 所属地区
	Stats       ProductAddressBitmapStats `json:"stats"`        // 统计信息
}

// NewProductAddressBitmap 创建新的bitmap
func NewProductAddressBitmap(region string, indexProvider ProductAddressIndexProvider) *ProductAddressBitmap {
	return &ProductAddressBitmap{
		bitmap:        roaring.New(),
		indexProvider: indexProvider,
		region:        region,
		version:       fmt.Sprintf("v%d", time.Now().Unix()),
		buildTime:     time.Now(),
	}
}

// SetServiceable 设置某个Product+Address组合为可配送
func (b *ProductAddressBitmap) SetServiceable(ctx utils.LCOSContext, key ProductAddressKey) {
	// 索引在锁外计算，缩短锁持有时间
	index := b.indexProvider.GetIndexFast(key)
	b.mutex.Lock()
	b.bitmap.Add(index)
	b.mutex.Unlock()
}

// IsServiceable 检查某个Product+Address组合是否可配送
func (b *ProductAddressBitmap) IsServiceable(ctx utils.LCOSContext, key ProductAddressKey) bool {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	index := b.indexProvider.GetIndex(key)
	result := b.bitmap.Contains(index)

	// 更新统计
	b.stats.QueryCount++
	b.stats.LastQueryTime = time.Now()
	if result {
		b.stats.HitCount++
	} else {
		b.stats.MissCount++
	}

	logger.CtxLogDebugf(ctx, "Query serviceable: %v -> index %d, result: %t", key, index, result)

	// 上报监控指标（安全调用，避免测试环境崩溃）
	SafeMetrics.CounterIncrWithContext(ctx, "product_address_bitmap_query_total",
		map[string]string{"region": b.region, "result": fmt.Sprintf("%t", result)})

	return result
}

// BatchSetServiceable 批量设置可配送
func (b *ProductAddressBitmap) BatchSetServiceable(ctx utils.LCOSContext, keys []ProductAddressKey) {
	// 索引化→排序→去重 在锁外完成；锁内仅执行 AddMany
	if len(keys) == 0 {
		return
	}
	indices := make([]uint32, 0, len(keys))
	for _, key := range keys {
		indices = append(indices, b.indexProvider.GetIndexFast(key))
	}
	sort.Slice(indices, func(i, j int) bool { return indices[i] < indices[j] })
	uniq := indices[:0]
	var prev uint32
	for i, v := range indices {
		if i == 0 || v != prev {
			uniq = append(uniq, v)
			prev = v
		}
	}
	b.mutex.Lock()
	b.bitmap.AddMany(uniq)
	b.mutex.Unlock()

	logger.CtxLogInfof(ctx, "Batch set %d serviceable keys", len(keys))
}

// BatchSetServiceableOptimized 优化的批量设置可配送，降低CPU使用率
func (b *ProductAddressBitmap) BatchSetServiceableOptimized(ctx utils.LCOSContext, keys []ProductAddressKey) {
	if len(keys) == 0 {
		return
	}

	// 对于小批量数据，直接使用单个Add操作，避免排序开销
	if len(keys) <= 100 {
		b.mutex.Lock()
		for _, key := range keys {
			index := b.indexProvider.GetIndexFast(key)
			b.bitmap.Add(index)
		}
		b.mutex.Unlock()
		return
	}

	// 对于大批量数据，使用map去重，避免排序
	indexSet := make(map[uint32]struct{}, len(keys))
	for _, key := range keys {
		index := b.indexProvider.GetIndexFast(key)
		indexSet[index] = struct{}{}
	}

	// 分批处理，避免一次性AddMany太多数据
	const batchSize = 1000
	indices := make([]uint32, 0, batchSize)

	b.mutex.Lock()
	for index := range indexSet {
		indices = append(indices, index)
		if len(indices) >= batchSize {
			b.bitmap.AddMany(indices)
			indices = indices[:0]
			// 在锁内短暂让出CPU
			b.mutex.Unlock()
			time.Sleep(time.Microsecond * 10)
			b.mutex.Lock()
		}
	}
	if len(indices) > 0 {
		b.bitmap.AddMany(indices)
	}
	b.mutex.Unlock()

	logger.CtxLogDebugf(ctx, "Optimized batch set %d serviceable keys", len(keys))
}

// BatchAddIndices 批量写入已计算好的索引
// 注意：该方法会在锁外完成排序与去重，锁内仅执行一次 AddMany
func (b *ProductAddressBitmap) BatchAddIndices(indices []uint32) {
	if len(indices) == 0 {
		return
	}
	sort.Slice(indices, func(i, j int) bool { return indices[i] < indices[j] })
	uniq := indices[:0]
	var prev uint32
	for i, v := range indices {
		if i == 0 || v != prev {
			uniq = append(uniq, v)
			prev = v
		}
	}
	b.mutex.Lock()
	b.bitmap.AddMany(uniq)
	b.mutex.Unlock()
}

// BatchIsServiceable 批量检查可配送性
func (b *ProductAddressBitmap) BatchIsServiceable(ctx utils.LCOSContext, keys []ProductAddressKey) map[ProductAddressKey]bool {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	result := make(map[ProductAddressKey]bool, len(keys))
	hitCount := 0

	for _, key := range keys {
		index := b.indexProvider.GetIndex(key)
		isServiceable := b.bitmap.Contains(index)
		result[key] = isServiceable

		if isServiceable {
			hitCount++
		}
	}

	// 更新统计
	b.stats.QueryCount += int64(len(keys))
	b.stats.HitCount += int64(hitCount)
	b.stats.MissCount += int64(len(keys) - hitCount)
	b.stats.LastQueryTime = time.Now()

	logger.CtxLogInfof(ctx, "Batch query %d keys: %d serviceable, %d not serviceable",
		len(keys), hitCount, len(keys)-hitCount)

	return result
}

// Clear 清空bitmap
func (b *ProductAddressBitmap) Clear(ctx utils.LCOSContext) {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	b.bitmap.Clear()
	b.stats.SetBits = 0
	b.updateTotalBits()

	logger.CtxLogInfof(ctx, "Cleared bitmap for region %s", b.region)
}

// Rebuild 重建bitmap
func (b *ProductAddressBitmap) Rebuild(ctx utils.LCOSContext, serviceableKeys []ProductAddressKey) {
	buildStart := time.Now()

	// 双缓冲：在锁外构建新 bitmap，完成后一次性替换
	newBitmap := roaring.New()
	if len(serviceableKeys) > 0 {
		unique := make(map[uint32]struct{}, len(serviceableKeys))
		for _, key := range serviceableKeys {
			idx := b.indexProvider.GetIndex(key)
			unique[idx] = struct{}{}
		}
		for idx := range unique {
			newBitmap.Add(idx)
		}
	}

	// 计算新统计数据
	// 延后统计基数：不在此处调用 GetCardinality，减少大批次的额外计算成本
	newSetBits := uint64(0)
	newVersion := fmt.Sprintf("v%d", time.Now().Unix())
	newBuildTime := time.Now()
	buildDuration := time.Since(buildStart)
	serialized, _ := newBitmap.ToBytes()
	compressionSize := len(serialized)

	b.mutex.Lock()
	b.bitmap = newBitmap
	b.version = newVersion
	b.buildTime = newBuildTime
	b.stats.SetBits = newSetBits
	b.stats.BuildDuration = buildDuration
	b.stats.CompressionSize = compressionSize
	b.updateTotalBits()
	b.mutex.Unlock()

	logger.CtxLogInfof(ctx, "Rebuilt bitmap for region %s: %d keys, compression: %d bytes, duration: %v",
		b.region, len(serviceableKeys), b.stats.CompressionSize, b.stats.BuildDuration)

	// 上报监控指标（安全调用）
	SafeMetrics.GaugeSetWithContext(ctx, "product_address_bitmap_build_duration_ms",
		float64(b.stats.BuildDuration.Milliseconds()),
		map[string]string{"region": b.region})
	SafeMetrics.GaugeSetWithContext(ctx, "product_address_bitmap_compression_bytes",
		float64(b.stats.CompressionSize),
		map[string]string{"region": b.region})
}

// GetStats 获取统计信息
func (b *ProductAddressBitmap) GetStats() ProductAddressBitmapStats {
	b.mutex.RLock()
	defer b.mutex.RUnlock()
	return b.stats
}

// GetInfo 获取bitmap基本信息
func (b *ProductAddressBitmap) GetInfo() map[string]interface{} {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	return map[string]interface{}{
		"version":           b.version,
		"region":            b.region,
		"build_time":        b.buildTime,
		"total_bits":        b.stats.TotalBits,
		"set_bits":          b.stats.SetBits,
		"compression_ratio": float64(b.stats.SetBits) / float64(b.stats.TotalBits),
		"compression_size":  b.stats.CompressionSize,
		"query_count":       b.stats.QueryCount,
		"hit_rate":          float64(b.stats.HitCount) / float64(b.stats.QueryCount),
	}
}

// Cardinality 返回bitmap中已设置为1的bit数量（即key数量）
func (b *ProductAddressBitmap) Cardinality() uint64 {
	b.mutex.RLock()
	defer b.mutex.RUnlock()
	return b.bitmap.GetCardinality()
}

// updateTotalBits 更新总bit数统计
func (b *ProductAddressBitmap) updateTotalBits() {
	if !b.bitmap.IsEmpty() {
		b.stats.TotalBits = uint64(b.bitmap.GetCardinality())
	}
}

// Clone 克隆bitmap（用于备份）
func (b *ProductAddressBitmap) Clone() *ProductAddressBitmap {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	cloned := &ProductAddressBitmap{
		bitmap:        b.bitmap.Clone(),
		indexProvider: b.indexProvider, // 共享索引提供者
		version:       b.version,
		buildTime:     b.buildTime,
		region:        b.region,
		stats:         b.stats, // 复制统计信息
	}

	return cloned
}

// Serialize 序列化bitmap到字节数组
func (b *ProductAddressBitmap) Serialize() ([]byte, error) {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	return b.bitmap.ToBytes()
}

// Deserialize 从字节数组反序列化bitmap
func (b *ProductAddressBitmap) Deserialize(data []byte) error {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	newBitmap := roaring.New()
	if err := newBitmap.UnmarshalBinary(data); err != nil {
		return fmt.Errorf("failed to deserialize bitmap: %w", err)
	}

	b.bitmap = newBitmap
	b.updateTotalBits()

	return nil
}

// SaveToLocalCache 保存bitmap到localcache
// 由于localcache是只读的，这里返回序列化数据供外部系统使用
func (b *ProductAddressBitmap) SaveToLocalCache(ctx utils.LCOSContext) (*SerializableBitmapData, error) {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	// 序列化bitmap数据
	bitmapBytes, err := b.bitmap.ToBytes()
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to serialize bitmap for region %s: %v", b.region, err)
		return nil, fmt.Errorf("bitmap serialization failed: %w", err)
	}

	// 创建可序列化的数据结构
	serializableData := &SerializableBitmapData{
		BitmapBytes: bitmapBytes,
		Version:     b.version,
		BuildTime:   b.buildTime,
		Region:      b.region,
		Stats:       b.stats,
	}

	// 更新压缩大小统计
	b.stats.CompressionSize = len(bitmapBytes)

	logger.CtxLogInfof(ctx, "Prepared bitmap data for persistence: region=%s, size=%d bytes, version=%s",
		b.region, len(bitmapBytes), b.version)

	SafeMetrics.GaugeSet("product_address_bitmap_size_bytes",
		float64(len(bitmapBytes)),
		map[string]string{"region": b.region})

	return serializableData, nil
}

// LoadFromLocalCache 从localcache加载bitmap数据
func (b *ProductAddressBitmap) LoadFromLocalCache(ctx utils.LCOSContext, cache *localcache.Manager, cacheKey string) error {
	// 从localcache获取数据
	cachedData, err := cache.Get(ctx, "product_address_bitmap", cacheKey)
	if err != nil {
		if err == localcache.ErrKeyNotFound {
			logger.CtxLogInfof(ctx, "No cached bitmap found for key %s", cacheKey)
			return nil // 不是错误，表示需要重新构建
		}
		logger.CtxLogErrorf(ctx, "Failed to get bitmap from cache: key=%s, error=%v", cacheKey, err)
		return fmt.Errorf("cache retrieval failed: %w", err)
	}

	// 类型断言
	serializableData, ok := cachedData.(*SerializableBitmapData)
	if !ok {
		logger.CtxLogErrorf(ctx, "Invalid cached data type for key %s", cacheKey)
		return fmt.Errorf("invalid cached data type")
	}

	// 反序列化bitmap
	newBitmap := roaring.New()
	if err := newBitmap.UnmarshalBinary(serializableData.BitmapBytes); err != nil {
		logger.CtxLogErrorf(ctx, "Failed to deserialize cached bitmap: key=%s, error=%v", cacheKey, err)
		return fmt.Errorf("bitmap deserialization failed: %w", err)
	}

	b.mutex.Lock()
	defer b.mutex.Unlock()

	// 更新bitmap和元数据
	b.bitmap = newBitmap
	b.version = serializableData.Version
	b.buildTime = serializableData.BuildTime
	b.region = serializableData.Region
	b.stats = serializableData.Stats
	b.updateTotalBits()

	logger.CtxLogInfof(ctx, "Successfully loaded bitmap from cache: key=%s, version=%s, set_bits=%d",
		cacheKey, b.version, b.stats.SetBits)

	SafeMetrics.CounterIncr("product_address_bitmap_cache_load_total",
		map[string]string{"region": b.region, "result": "success"})

	return nil
}

// GenerateCacheKey 生成localcache的键
func (b *ProductAddressBitmap) GenerateCacheKey() string {
	return fmt.Sprintf("bitmap:%s:%s", b.region, b.version)
}
