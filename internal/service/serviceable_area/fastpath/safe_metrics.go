package fastpath

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
)

// safeMetrics 提供安全的metrics调用，避免在测试环境中崩溃
type safeMetrics struct{}

var SafeMetrics = &safeMetrics{}

// CounterIncr 安全的计数器递增
func (s *safeMetrics) CounterIncr(name string, labels map[string]string) {
	defer func() {
		if r := recover(); r != nil {
			logger.LogDebugf("Metrics call panicked for %s: %v", name, r)
		}
	}()

	if err := metrics.CounterIncr(name, labels); err != nil {
		// 在正常环境中，这种错误很少见，只在调试时记录
		// 在测试环境中，这可以避免崩溃
		logger.LogDebugf("Failed to increment counter %s: %v", name, err)
	}
}

// GaugeSet 安全的gauge设置
func (s *safeMetrics) GaugeSet(name string, val float64, labels map[string]string) {
	defer func() {
		if r := recover(); r != nil {
			logger.LogDebugf("Metrics call panicked for %s: %v", name, r)
		}
	}()

	if err := metrics.GaugeSet(name, val, labels); err != nil {
		logger.LogDebugf("Failed to set gauge %s: %v", name, err)
	}
}

// CounterAdd 安全的计数器增加
func (s *safeMetrics) CounterAdd(name string, val float64, labels map[string]string) {
	defer func() {
		if r := recover(); r != nil {
			logger.LogDebugf("Metrics call panicked for %s: %v", name, r)
		}
	}()

	if err := metrics.CounterAdd(name, val, labels); err != nil {
		logger.LogDebugf("Failed to add counter %s: %v", name, err)
	}
}

// CounterIncrWithContext 带上下文的安全计数器递增
func (s *safeMetrics) CounterIncrWithContext(ctx utils.LCOSContext, name string, labels map[string]string) {
	defer func() {
		if r := recover(); r != nil {
			// 捕获panic，在测试环境中metrics可能未初始化
			logger.CtxLogDebugf(ctx, "Metrics call panicked for %s: %v", name, r)
		}
	}()

	if err := metrics.CounterIncr(name, labels); err != nil {
		logger.CtxLogDebugf(ctx, "Failed to increment counter %s: %v", name, err)
	}
}

// GaugeSetWithContext 带上下文的安全gauge设置
func (s *safeMetrics) GaugeSetWithContext(ctx utils.LCOSContext, name string, val float64, labels map[string]string) {
	defer func() {
		if r := recover(); r != nil {
			// 捕获panic，在测试环境中metrics可能未初始化
			logger.CtxLogDebugf(ctx, "Metrics call panicked for %s: %v", name, r)
		}
	}()

	if err := metrics.GaugeSet(name, val, labels); err != nil {
		logger.CtxLogDebugf(ctx, "Failed to set gauge %s: %v", name, err)
	}
}
