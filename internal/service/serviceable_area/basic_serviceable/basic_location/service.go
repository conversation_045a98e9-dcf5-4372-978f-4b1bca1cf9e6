package basic_location

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	order_account_mapping2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seabank_service"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/basic_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

type LineBasicServiceableLocationServiceInterface interface {
	// 过期
	CreateBasicServiceableOriginLocation(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicOriginLocationRequest) (*model.LineOriginLocationBasicServiceableTab, *lcos_error.LCOSError)
	BatchCreateBasicServiceableOriginLocation(ctx utils.LCOSContext, request *basic_serviceable.BatchCreateBasicOriginLocationRequest) ([]*model.LineOriginLocationBasicServiceableTab, *lcos_error.LCOSError)
	CreateBasicServiceableDestLocation(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicDestLocationRequest) (*model.LineDestinationLocationBasicServiceableTab, *lcos_error.LCOSError)
	BatchCreateBasicServiceableDestLocation(ctx utils.LCOSContext, request *basic_serviceable.BatchCreateBasicDestLocationRequest) ([]*model.LineDestinationLocationBasicServiceableTab, *lcos_error.LCOSError)
	UpdateBasicServiceableOriginLocation(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicOriginLocationRequest) (*model.LineOriginLocationBasicServiceableTab, *lcos_error.LCOSError)
	UpdateBasicServiceableDestLocation(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicDestLocationRequest) (*model.LineDestinationLocationBasicServiceableTab, *lcos_error.LCOSError)
	GetBasicServiceableOriginLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicOriginLocationListRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllBasicServiceableOriginLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicOriginLocationRequest) ([]*model.LineOriginLocationBasicServiceableTab, *lcos_error.LCOSError)
	GetBasicServiceableDestLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicDestLocationListRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllBasicServiceableDestLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicDestLocationRequest) ([]*model.LineDestinationLocationBasicServiceableTab, *lcos_error.LCOSError)
	DeleteBasicServiceableOriginLocation(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicLocationRequest) *lcos_error.LCOSError
	DeleteBasicServiceableDestLocation(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicLocationRequest) *lcos_error.LCOSError
	UploadBasicServiceableOriginLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	UploadBasicServiceableDestLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError

	// 创建基础层location
	CreateBasicServiceableLocation(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicLocationRequest) (*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError)
	// 批量创建基础层location
	BatchCreateBasicServiceableLocation(ctx utils.LCOSContext, request *basic_serviceable.BatchCreateBasicLocationRequest) ([]*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError)
	// 更新基础层location
	UpdateBasicServiceableLocation(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicLocationRequest) (*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError)
	// 获取基础层location 分页列表
	GetBasicServiceableLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicLocationListRequest) (*common.PageModel, *lcos_error.LCOSError)
	// 获取所有的location列表
	GetAllBasicServiceableLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicLocationRequest) ([]*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError)
	// 删除基础层location
	DeleteBasicServiceableLocation(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicLocationRequest) *lcos_error.LCOSError

	DeleteBasicServiceableLocationByLineId(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError
	// 上传基础层location
	UploadBasicServiceableLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	ParseAndImportBasicLocationSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool, effectiveTime uint32) (excel.ParseFileResult, *lcos_error.LCOSError)
	ParseBasicLocationServiceableArea(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, effectiveTime uint32) (map[string][]*model.LineBasicServiceableLocationTab, map[string][]*model.LineBasicServiceableLocationTab, excel.ParseFileResult)
	// 搜索四级地址
	SearchAllFourLevelAddress(ctx utils.LCOSContext, request *common_protocol.SearchAllFourLevelAddressRequest) (*serviceable_core_logic.FourLevelAddress, *lcos_error.LCOSError)

	GetAllBasicServiceableLocationByLineAndGroup(ctx utils.LCOSContext, lineId, groupId string) ([]*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError)
	GetAllBasicServiceableLocationByLine(ctx utils.LCOSContext, lineId string) ([]*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError)
	NotifyCardDeliveryAddressAllData(ctx utils.LCOSContext) *lcos_error.LCOSError
}

type LineBasicServiceableLocationService struct {
	lineBasicServiceableLocationDAO model.LineBasicServiceableLocationDAO
	scheduledService                scheduled.ScheduledService
	basicConfDao                    basic_conf.LineBasicServiceableConfDAO
	orderAccountMappingService      order_account_mapping.OrderAccountMappingService
	spxServiceableAreaService       spx_serviceable_area.SpxServiceableAreaService
	cardDeliveryAddressService      card_delivery_address.CardDeliveryAddressService
}

func NewLineBasicServiceableLocationService(lineBasicServiceableLocationDAO model.LineBasicServiceableLocationDAO, scheduledService scheduled.ScheduledService, basicConfDao basic_conf.LineBasicServiceableConfDAO, orderAccountMappingService order_account_mapping.OrderAccountMappingService, spxServiceableAreaService spx_serviceable_area.SpxServiceableAreaService, cardDeliveryAddressService card_delivery_address.CardDeliveryAddressService) *LineBasicServiceableLocationService {
	return &LineBasicServiceableLocationService{
		lineBasicServiceableLocationDAO: lineBasicServiceableLocationDAO,
		scheduledService:                scheduledService,
		basicConfDao:                    basicConfDao,
		orderAccountMappingService:      orderAccountMappingService,
		spxServiceableAreaService:       spxServiceableAreaService,
		cardDeliveryAddressService:      cardDeliveryAddressService,
	}
}

// checkLocationLineOverlap
func (s *LineBasicServiceableLocationService) checkLocationLineOverlap(ctx utils.LCOSContext, lineID string, locationServiceables []*model.LineBasicServiceableLocationTab, oldLocationMap map[string]map[uint64][]*model.LineBasicServiceableLocationTab) *lcos_error.LCOSError {
	// SPLN-19677 需要校验线服务范围不重叠
	// 获取跟当前line位于一个组的group
	notAllowdOverlapList := config.GetFMLineGroupNoOverlap(ctx, lineID)
	if len(notAllowdOverlapList) != 0 {
		for _, notOverlapLine := range notAllowdOverlapList {
			if lineID != notOverlapLine {
				if oldServiceableLocationsMap, ok1 := oldLocationMap[notOverlapLine]; ok1 {
					for _, newLocation := range locationServiceables {
						// 有重合的location
						if item2, ok2 := oldServiceableLocationsMap[newLocation.LocationId]; ok2 {
							for _, item := range item2 {
								if item.CollectDeliverGroupId == newLocation.CollectDeliverGroupId {
									errMsg := fmt.Sprintf("line serviceable overlap|new_line:[%s], old_line:[%s], location_id:[%d], group_id:[%s], state:[%s], city:[%s], district:[%s], street:[%s]", newLocation.LineId, item.LineId, item.LocationId, item.CollectDeliverGroupId, item.State, item.City, item.District, item.Street)
									logger.CtxLogErrorf(ctx, errMsg)
									return lcos_error.NewLCOSError(lcos_error.LineServiceAbleOverlap, errMsg)
								}
							}
						}
					}

				}
			}
		}
	}
	return nil
}

// 将需要删除的服务范围数据删除  deleteMap: line_id->location_id->serviceableLocation
func (s *LineBasicServiceableLocationService) eliminatedDeleteServiceableLocation(deleteMap map[string]map[uint64]*model.LineBasicServiceableLocationTab, inputs []*model.LineBasicServiceableLocationTab) []*model.LineBasicServiceableLocationTab {
	var outputs []*model.LineBasicServiceableLocationTab
	for _, input := range inputs {
		if item1, ok1 := deleteMap[input.LineId]; ok1 {
			// 需要删除的元素中，存在重复的serviceableLocation
			if item2, ok2 := item1[input.LocationId]; ok2 && item2.LocationId == input.LocationId && item2.CollectDeliverGroupId == input.CollectDeliverGroupId {
				continue
			}
		}
		outputs = append(outputs, input)
	}
	return outputs
}

// 创建基础层location
func (s *LineBasicServiceableLocationService) CreateBasicServiceableLocation(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicLocationRequest) (*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	//exist, err := lls_service.CheckLineExist(ctx, request.LineId)
	//if err != nil {
	//return nil, err
	//}
	//if !exist {
	//logger.LogErrorf("create basic origin location failed, line_id not exist|lineId=%s", request.LineId)
	//return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	//}

	locationModel := new(model.LineBasicServiceableLocationTab)
	if err := fillLocationInfo(ctx, locationModel, request); err != nil {
		return nil, err
	}

	// spx服务范围校验
	basicConf, err := s.basicConfDao.GetBasicServiceableConfModelByLineId(ctx, request.LineId)
	if err != nil {
		return nil, err
	}
	if basicConf == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line basic config not found")
	}
	dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)
	if err := s.singleCheckSpxLocationServiceableArea(ctx, locationModel.Region, locationModel.LineId, locationModel.CollectDeliverGroupId, locationModel.LocationId, locationModel, dropoffOnly); err != nil {
		return nil, err
	}

	notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, request.LineId)
	// lineID->locationID->locations
	oldServiceableLocationMap := map[string]map[uint64][]*model.LineBasicServiceableLocationTab{}
	for _, singleLine := range notAllowedOverlapList {
		serviceableLocations, lcosErr := s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableLocation(ctx, singleLine, map[string]interface{}{"line_id": singleLine})
		if lcosErr != nil {
			return nil, lcosErr
		}
		for _, serviceableLocation := range serviceableLocations {
			if _, ok := oldServiceableLocationMap[singleLine]; !ok {
				oldServiceableLocationMap[singleLine] = map[uint64][]*model.LineBasicServiceableLocationTab{}
			}
			oldServiceableLocationMap[singleLine][serviceableLocation.LocationId] = append(oldServiceableLocationMap[singleLine][serviceableLocation.LocationId], serviceableLocation)
		}
	}
	lcosErr := s.checkLocationLineOverlap(ctx, locationModel.LineId, []*model.LineBasicServiceableLocationTab{locationModel}, oldServiceableLocationMap)
	if lcosErr != nil {
		return nil, lcosErr
	}

	remoteFilePath, lcosErr := s.cardDeliveryAddressService.UploadCardDeliveryAddress(ctx, request.Region, []*model.LineBasicServiceableLocationTab{locationModel}, nil, map[string]*basic_conf.LineBasicServiceableConfTab{basicConf.LineId: basicConf})
	if lcosErr != nil {
		return nil, lcosErr
	}
	fn := func() *lcos_error.LCOSError {
		if _, err := s.lineBasicServiceableLocationDAO.CreateBasicServiceableLocationModel(ctx, locationModel); err != nil {
			return err
		}
		return s.cardDeliveryAddressService.CreateCardDeliveryAddressChangeVersion(ctx, request.Region, remoteFilePath)
	}
	if err = ctx.Transaction(fn); err != nil {
		return nil, err
	}
	return locationModel, nil

}

// 批量创建基础层location
func (s *LineBasicServiceableLocationService) BatchCreateBasicServiceableLocation(ctx utils.LCOSContext, request *basic_serviceable.BatchCreateBasicLocationRequest) ([]*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	//exist, err := lls_service.CheckLineExist(ctx, request.LineId)
	//if err != nil {
	//return nil, err
	//}
	//if !exist {
	//logger.LogErrorf("batch create location failed, line_id not exist|lineId=%s", request.LineId)
	//return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	//}

	locationModels := make([]*model.LineBasicServiceableLocationTab, 0, len(request.LocationId))
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	wg.Add(len(request.LocationId))
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for _, locationId := range request.LocationId {
		limitChannel <- struct{}{}
		go func(singleLocationId uint64) {
			locationModel := new(model.LineBasicServiceableLocationTab)
			if err := fillLocationInfo(ctx, locationModel, &basic_serviceable.CreateBasicLocationRequest{
				BaseLocationInfo: basic_serviceable.BaseLocationInfo{
					LineId:     request.LineId,
					Region:     request.Region,
					LocationId: singleLocationId,
				},
				CollectDeliverGroupId: request.CollectDeliverGroupId,
				CanPickup:             request.CanPickup,
				CanCodPickup:          request.CanCodPickup,
				CanDeliver:            request.CanDeliver,
				CanCodDeliver:         request.CanCodDeliver,
				SupportTradeIn:        request.SupportTradeIn,
			}); err != nil {
				errList = append(errList, err)
			} else {
				locationModel.LineId = request.LineId
				locationModels = append(locationModels, locationModel)
			}
			<-limitChannel
			wg.Done()
		}(locationId)
	}
	wg.Wait()

	if len(errList) != 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.BatchCreateLocationErrorCode, lcos_error.GetMessageFromList(errList))
	}

	// 校验spx服务范围是否支持
	basicConf, err := s.basicConfDao.GetBasicServiceableConfModelByLineId(ctx, request.LineId)
	if err != nil {
		return nil, err
	}
	if basicConf == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line basic config not found")
	}
	dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)
	for _, locationModel := range locationModels {
		if err := s.singleCheckSpxLocationServiceableArea(ctx, locationModel.Region, locationModel.LineId, locationModel.CollectDeliverGroupId, locationModel.LocationId, locationModel, dropoffOnly); err != nil {
			return nil, err
		}
	}

	// 批量校验
	notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, request.LineId)
	// lineID->locationID->locations
	oldServiceableLocationMap := map[string]map[uint64][]*model.LineBasicServiceableLocationTab{}
	for _, singleLine := range notAllowedOverlapList {
		serviceableLocations, lcosErr := s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableLocation(ctx, singleLine, map[string]interface{}{"line_id": singleLine})
		if lcosErr != nil {
			return nil, lcosErr
		}
		for _, serviceableLocation := range serviceableLocations {
			if _, ok := oldServiceableLocationMap[singleLine]; !ok {
				oldServiceableLocationMap[singleLine] = map[uint64][]*model.LineBasicServiceableLocationTab{}
			}
			oldServiceableLocationMap[singleLine][serviceableLocation.LocationId] = append(oldServiceableLocationMap[singleLine][serviceableLocation.LocationId], serviceableLocation)
		}
	}
	lcosErr := s.checkLocationLineOverlap(ctx, request.LineId, locationModels, oldServiceableLocationMap)
	if lcosErr != nil {
		return nil, lcosErr
	}

	remoteFilePath, lcosErr := s.cardDeliveryAddressService.UploadCardDeliveryAddress(ctx, request.Region, locationModels, nil, map[string]*basic_conf.LineBasicServiceableConfTab{request.LineId: basicConf})
	if lcosErr != nil {
		return nil, lcosErr
	}
	fn := func() *lcos_error.LCOSError {
		// 先批量删除，再批量新增
		if err := s.lineBasicServiceableLocationDAO.BatchDeleteBasicServiceableLocation(ctx, request.LineId, locationModels); err != nil {
			return err
		}
		if _, err := s.lineBasicServiceableLocationDAO.BatchCreateBasicServiceableLocationModel(ctx, request.LineId, locationModels); err != nil {
			return err
		}
		return s.cardDeliveryAddressService.CreateCardDeliveryAddressChangeVersion(ctx, request.Region, remoteFilePath)
	}
	if err := ctx.Transaction(fn); err != nil {
		return nil, err
	}
	return locationModels, nil
}

// 更新基础层location
func (s *LineBasicServiceableLocationService) UpdateBasicServiceableLocation(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicLocationRequest) (*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	locationModel, err := s.lineBasicServiceableLocationDAO.GetBasicServiceableLocationModelById(ctx, request.ID, request.LineId)
	if err != nil {
		return nil, err
	}
	if locationModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicLocationErrorCode)
	}

	if err := fillLocationInfo(ctx, locationModel, &request.CreateBasicLocationRequest); err != nil {
		return nil, err
	}

	basicConf, err := s.basicConfDao.GetBasicServiceableConfModelByLineId(ctx, request.LineId)
	if err != nil {
		return nil, err
	}
	if basicConf == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line basic config not found")
	}
	dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)
	if err := s.singleCheckSpxLocationServiceableArea(ctx, locationModel.Region, locationModel.LineId, locationModel.CollectDeliverGroupId, locationModel.LocationId, locationModel, dropoffOnly); err != nil {
		return nil, err
	}

	remoteFilePath, lcosErr := s.cardDeliveryAddressService.UploadCardDeliveryAddress(ctx, request.Region, []*model.LineBasicServiceableLocationTab{locationModel}, nil, map[string]*basic_conf.LineBasicServiceableConfTab{request.LineId: basicConf})
	if lcosErr != nil {
		return nil, lcosErr
	}
	fn := func() *lcos_error.LCOSError {
		if _, err := s.lineBasicServiceableLocationDAO.UpdateBasicServiceableLocationModel(ctx, locationModel); err != nil {
			return err
		}
		return s.cardDeliveryAddressService.CreateCardDeliveryAddressChangeVersion(ctx, request.Region, remoteFilePath)
	}
	if err := ctx.Transaction(fn); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// 获取基础层location 分页列表
func (s *LineBasicServiceableLocationService) GetBasicServiceableLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicLocationListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)

	searchMap, _ := utils.Struct2map(request)
	models, total, err := s.lineBasicServiceableLocationDAO.SearchBasicServiceableLocation(ctx, pageNo, count, request.LineId, searchMap)
	if err != nil {
		return nil, err
	}

	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// 获取所有的location列表
func (s *LineBasicServiceableLocationService) GetAllBasicServiceableLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicLocationRequest) ([]*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	searchMap, _ := utils.Struct2map(request)
	models, err := s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableLocation(ctx, request.LineId, searchMap)
	if err != nil {
		return nil, err
	}

	return models, nil
}

// 删除基础层location
func (s *LineBasicServiceableLocationService) DeleteBasicServiceableLocation(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicLocationRequest) *lcos_error.LCOSError {
	locationModel, err := s.lineBasicServiceableLocationDAO.GetBasicServiceableLocationModelById(ctx, request.ID, request.LineId)
	if err != nil {
		return err
	}

	// 删除服务范围配置时，需要校验线默认的揽派能力在spx是否支持
	basicConf, err := s.basicConfDao.GetBasicServiceableConfModelByLineId(ctx, request.LineId)
	if err != nil {
		return err
	}
	if basicConf == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line basic config not found")
	}
	dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)
	if err := s.singleCheckSpxLocationServiceableArea(ctx, locationModel.Region, locationModel.LineId, locationModel.CollectDeliverGroupId, locationModel.LocationId, basicConf, dropoffOnly); err != nil {
		return err
	}

	remoteFilePath, lcosErr := s.cardDeliveryAddressService.UploadCardDeliveryAddress(ctx, locationModel.Region, nil, []*model.LineBasicServiceableLocationTab{locationModel}, map[string]*basic_conf.LineBasicServiceableConfTab{request.LineId: basicConf})
	if lcosErr != nil {
		return lcosErr
	}
	fn := func() *lcos_error.LCOSError {
		if err := s.lineBasicServiceableLocationDAO.DeleteBasicServiceableLocation(ctx, request.LineId, request.ID); err != nil {
			return err
		}
		return s.cardDeliveryAddressService.CreateCardDeliveryAddressChangeVersion(ctx, locationModel.Region, remoteFilePath)
	}
	return ctx.Transaction(fn)
}

func (s *LineBasicServiceableLocationService) parseSingleBasicServiceableLocation(ctx utils.LCOSContext, rowData []string, addLocationModelsMap map[string][]*model.LineBasicServiceableLocationTab, deleteLocationModelsMap map[string][]*model.LineBasicServiceableLocationTab, lineInfoMap map[string]*llspb.GetLineInfoResponseData) []*lcos_error.LCOSError {
	var errList []*lcos_error.LCOSError
	rowLineId := rowData[0]
	exist := true
	if _, ok := lineInfoMap[rowLineId]; !ok {
		exist = false
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.LineNotValidErrorCode, fmt.Sprintf("line is not valid|line_id=%v", rowLineId)))
	}
	if !exist {
		logger.LogErrorf("upload location failed, line_id not exist|lineId=%s", rowLineId)
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
	}

	locationStruct := serviceable_util.GetLocationRequestByLocationFileRow(rowData)
	var state, city, district, street string
	for index, name := range locationStruct.LocationName {
		switch index {
		case 0:
			state = name
		case 1:
			city = name
		case 2:
			district = name
		case 3:
			street = name
		}
	}
	locationInfo, lcosErr := address_service.LocationServer.GetLocationInfoByName(ctx, locationStruct.Country, state, city, district, street)
	if lcosErr != nil {
		errList = append(errList, lcosErr)
	} else {
		// 找到最底层的location id
		var locationID int
		if locationInfo.StreetLocationId != 0 {
			locationID = locationInfo.StreetLocationId
		} else if locationInfo.DistrictLocationId != 0 {
			locationID = locationInfo.DistrictLocationId
		} else if locationInfo.CityLocationId != 0 {
			locationID = locationInfo.CityLocationId
		} else if locationInfo.StateLocationId != 0 {
			locationID = locationInfo.StateLocationId
		}
		rowStruct := serviceable_util.ParseLocationRowData(rowData)
		locationModel := &model.LineBasicServiceableLocationTab{
			LineId:                rowStruct.LineId,
			CollectDeliverGroupId: rowStruct.CollectDeliverGroupId,
			Region:                rowStruct.Region,
			LocationId:            uint64(locationID),
			State:                 rowStruct.State,
			City:                  rowStruct.City,
			District:              rowStruct.District,
			Street:                rowStruct.Street,
			CanPickup:             &rowStruct.PickUp,
			CanCodPickup:          &rowStruct.CodPickUp,
			CanDeliver:            &rowStruct.Deliver,
			CanCodDeliver:         &rowStruct.CodDeliver,
			//SupportTradeIn: &rowStruct.,
		}
		if rowStruct.ActionCode != -1 {
			if addLocationModelsMap[rowStruct.LineId] == nil {
				addLocationModelsMap[rowStruct.LineId] = []*model.LineBasicServiceableLocationTab{}
			}
			addLocationModelsMap[rowStruct.LineId] = append(addLocationModelsMap[rowStruct.LineId], locationModel)
		} else {
			if deleteLocationModelsMap[rowStruct.LineId] == nil {
				deleteLocationModelsMap[rowStruct.LineId] = []*model.LineBasicServiceableLocationTab{}
			}
			deleteLocationModelsMap[rowStruct.LineId] = append(deleteLocationModelsMap[rowStruct.LineId], locationModel)
		}
	}
	return errList
}

// TODO 上传基础层location
func (s *LineBasicServiceableLocationService) UploadBasicServiceableLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	job, err := s.scheduledService.CreateScheduledJob(ctx, request.Region, scheduled_constant.ThreePLServiceableArea, scheduled_constant.Location, request.FileUrl, request.IsScheduled, request.ScheduledTime, ctx.GetUserEmail())
	if err != nil {
		return err
	}

	go func() {
		parseFileResult, _ := s.ParseAndImportBasicLocationSA(ctx, request, serviceable_util.LineBasicLocationHeader, true, job.ScheduledTime)
		if err := s.scheduledService.UpdateValidateResult(ctx, job, parseFileResult); err != nil {
			logger.CtxLogErrorf(ctx, "scheduled job update validate result error|job_id=%d, cause=%s", job.ID, err.Msg)
		}
	}()

	return nil
}

func (s *LineBasicServiceableLocationService) ParseAndImportBasicLocationSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool, effectiveTime uint32) (excel.ParseFileResult, *lcos_error.LCOSError) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}

	// 1. 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return result, nil
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
		return result, nil
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
		return result, nil
	}
	defer os.Remove(filePath)

	uniqueKeyMap := make(map[string]int)
	lineIdMap := make(map[string]bool)
	var lineIdList []string
	var rowDataList []*serviceable_util.LineBasicLocationRowData
	orderAccountMap := make(map[int]bool)
	var orderAccountSet []int
	orderAccountMapping := make(map[string][]*order_account_mapping2.OrderAccountMapping) // line_id+group_id -> order_account list
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头和空行
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount() // 只统计非header行和有效行

		rowData := &serviceable_util.LineBasicLocationRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}
		if request.Region != rowData.Region {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", request.Region, rowData.RowId))
			continue
		}
		uniqueKey := utils.GenKey("-", rowData.LineId, rowData.GroupId, rowData.State, rowData.City, rowData.District, rowData.Street)
		if lastRowId, ok := uniqueKeyMap[uniqueKey]; ok {
			result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import data duplicated with same Line-Group-Address, please check | row %d duplicate with row %d, unique key=%s", rowData.RowId, lastRowId, uniqueKey)
			return result, nil
		} else {
			uniqueKeyMap[uniqueKey] = rowData.RowId
		}

		// 将line id去重存在在列表中，方便批量查询
		if _, ok := lineIdMap[rowData.LineId]; !ok {
			lineIdList = append(lineIdList, rowData.LineId)
			lineIdMap[rowData.LineId] = true
		}

		lineGroupKey := utils.GenKey(":", rowData.LineId, rowData.GroupId)
		if _, ok := orderAccountMapping[lineGroupKey]; !ok {
			mappingList, err := s.orderAccountMappingService.GetOrderAccountMappingByLineAndGroup(ctx, rowData.Region, rowData.LineId, rowData.GroupId)
			if err != nil {
				result.ParseFileErr = err
				return result, nil
			}
			for _, mapping := range mappingList {
				orderAccountMapping[lineGroupKey] = append(orderAccountMapping[lineGroupKey], mapping)
				if _, ok := orderAccountMap[mapping.OrderAccount]; !ok {
					orderAccountSet = append(orderAccountSet, mapping.OrderAccount)
					orderAccountMap[mapping.OrderAccount] = true
				}
			}
		}

		rowDataList = append(rowDataList, rowData)
	}

	if len(lineIdList) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is empty")
		return result, nil
	}

	// 请求lls获取所有的line信息
	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIdList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return result, nil
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}
	//// 请求lls获取所有的草稿态lineDraft信息
	//lineDraftMap, lcosErr := lls_service.BatchGetLineDraftsMap(ctx, lineIdList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return result, nil
	//}
	//for k, v := range lineDraftMap {
	//	lineInfoMap[k] = v
	//}
	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(ctx, request.Region, lineIdList, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return result, nil
	}
	// 获取上传的所有线的默认揽派能力
	basicConfMap, err := s.basicConfDao.GetBasicServiceableConfModelMapByLineIds(ctx, lineIdList)
	if err != nil {
		result.ParseFileErr = err
		return result, nil
	}

	// 获取用于校验的spx服务范围版本
	var spxVersion *spx_serviceable_area_version.SpxServiceableAreaVersion
	if async {
		// 非立即落库，需要以生效时间获取spx版本校验
		spxVersion, err = s.spxServiceableAreaService.GetLastAvailableSpxServiceableAreaVersion(ctx, request.Region, effectiveTime)
	} else {
		// 立即落库，需要获取已生效的spx版本校验
		spxVersion, err = s.spxServiceableAreaService.GetActiveSpxServiceableAreaVersion(ctx, request.Region)
	}
	if err != nil {
		result.ParseFileErr = err
		return result, nil
	}

	spxDataMap := make(map[int]map[uint64]*spx_serviceable_area_data.SpxServiceableAreaLocationData)
	if spxVersion != nil {
		spxData, err := s.spxServiceableAreaService.ListSpxServiceabelAreaLocationDataByOrderAccountList(ctx, request.Region, spxVersion.VersionId, orderAccountSet)
		if err != nil {
			result.ParseFileErr = err
			return result, nil
		}
		for _, datum := range spxData {
			locationMap, ok := spxDataMap[datum.OrderAccount]
			if !ok {
				locationMap = make(map[uint64]*spx_serviceable_area_data.SpxServiceableAreaLocationData)
			}
			locationMap[datum.LocationId] = datum
			spxDataMap[datum.OrderAccount] = locationMap
		}
	}

	addLocationModelsMap := make(map[string][]*model.LineBasicServiceableLocationTab)
	deleteLocationModelsMap := make(map[string][]*model.LineBasicServiceableLocationTab)
	for _, rowData := range rowDataList {
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}

		locationInfo, err := address_service.LocationServer.GetLocationInfoByName(ctx, rowData.Region, rowData.State, rowData.City, rowData.District, rowData.Street)
		if err != nil {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Address invalid, %s | row=%d", err.Msg, rowData.RowId))
			continue
		}

		basicConf, ok := basicConfMap[rowData.LineId]
		if !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line serviceable area basic configuration not found | row=%d", rowData.RowId))
			continue
		}
		dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)

		locationModel := &model.LineBasicServiceableLocationTab{
			LineId:                rowData.LineId,
			CollectDeliverGroupId: rowData.GroupId,
			Region:                rowData.Region,
			LocationId:            uint64(locationInfo.GetLocationId()),
			State:                 rowData.State,
			City:                  rowData.City,
			District:              rowData.District,
			Street:                rowData.Street,
			CanPickup:             &rowData.SupportPickup,
			CanCodPickup:          &rowData.SupportCodPickup,
			CanDeliver:            &rowData.SupportDeliver,
			CanCodDeliver:         &rowData.SupportCodDeliver,
			SupportTradeIn:        rowData.SupportTradeIn,
		}
		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			// 新增或更新，校验此服务范围揽派能力在spx是否支持
			if err := s.batchCheckSpxLocationServiceableArea(ctx, locationModel.Region, locationModel.LineId, locationModel.CollectDeliverGroupId, locationModel.LocationId, locationModel, dropoffOnly, orderAccountMapping, spxDataMap); err != nil {
				result.WriteRowError(rowData.RowId, err)
				continue
			}

			if addLocationModelsMap[rowData.LineId] == nil {
				addLocationModelsMap[rowData.LineId] = []*model.LineBasicServiceableLocationTab{}
			}
			addLocationModelsMap[rowData.LineId] = append(addLocationModelsMap[rowData.LineId], locationModel)
		} else {
			// 删除，校验线默认揽派能力在spx是否支持
			if err := s.batchCheckSpxLocationServiceableArea(ctx, locationModel.Region, locationModel.LineId, locationModel.CollectDeliverGroupId, locationModel.LocationId, basicConf, dropoffOnly, orderAccountMapping, spxDataMap); err != nil {
				result.WriteRowError(rowData.RowId, err)
				continue
			}

			if deleteLocationModelsMap[rowData.LineId] == nil {
				deleteLocationModelsMap[rowData.LineId] = []*model.LineBasicServiceableLocationTab{}
			}
			deleteLocationModelsMap[rowData.LineId] = append(deleteLocationModelsMap[rowData.LineId], locationModel)
		}
	}

	// 检查line的服务范围是否重合 SPLN-19677
	// 先获取所有可能冲突的line列表
	var allNotAllowedLines []string
	NotAllowedOverlapMap := map[string][]*model.LineBasicServiceableLocationTab{}
	for _, tmpLine := range lineIdList {
		notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, tmpLine)
		allNotAllowedLines = append(allNotAllowedLines, notAllowedOverlapList...)
	}
	// 获取所有可能冲突的line服务范围
	for _, tmpLine := range allNotAllowedLines {
		if _, ok := NotAllowedOverlapMap[tmpLine]; !ok {
			serviceableLocations, lcosErr := s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableLocation(ctx, tmpLine, map[string]interface{}{"line_id": tmpLine})
			if lcosErr != nil {
				result.ParseFileErr = lcosErr
				return result, nil
			}
			NotAllowedOverlapMap[tmpLine] = serviceableLocations
		}
	}

	// 需要删除掉deleteLocationModelsMap中的服务范围数据，防止误差
	eliminatedServiceableLocationMap := map[string]map[uint64]*model.LineBasicServiceableLocationTab{}
	for lineID, locationList := range deleteLocationModelsMap {
		for _, singleServiceableLocation := range locationList {
			if _, ok := eliminatedServiceableLocationMap[lineID]; !ok {
				eliminatedServiceableLocationMap[lineID] = map[uint64]*model.LineBasicServiceableLocationTab{}
			}
			eliminatedServiceableLocationMap[lineID][singleServiceableLocation.LocationId] = singleServiceableLocation
		}
	}
	// 经过eliminatedServiceableLocationMap过滤的需要比对重叠性的服务范围数据
	oldServiceableLocationMap := map[string]map[uint64][]*model.LineBasicServiceableLocationTab{}
	for lineID, serviceLocations := range NotAllowedOverlapMap {
		results := s.eliminatedDeleteServiceableLocation(eliminatedServiceableLocationMap, serviceLocations)
		for _, result := range results {
			if _, ok := oldServiceableLocationMap[lineID]; !ok {
				oldServiceableLocationMap[lineID] = map[uint64][]*model.LineBasicServiceableLocationTab{}
			}
			oldServiceableLocationMap[lineID][result.LocationId] = append(oldServiceableLocationMap[lineID][result.LocationId], result)
		}
	}

	// 对于批量上传来说，导入的数据也不能相互重叠。
	for _, serviceLocations := range addLocationModelsMap {
		comparedServiceLocations := s.eliminatedDeleteServiceableLocation(eliminatedServiceableLocationMap, serviceLocations)
		// 将数据导入到oldServiceableLocationMap，用于之后对比
		for _, singleLocation := range comparedServiceLocations {
			if _, ok := oldServiceableLocationMap[singleLocation.LineId]; !ok {
				oldServiceableLocationMap[singleLocation.LineId] = map[uint64][]*model.LineBasicServiceableLocationTab{}
			}
			oldServiceableLocationMap[singleLocation.LineId][singleLocation.LocationId] = append(oldServiceableLocationMap[singleLocation.LineId][singleLocation.LocationId], singleLocation)
		}
	}

	// 对比
	for lineID, serviceLocations := range addLocationModelsMap {
		comparedServiceLocations := s.eliminatedDeleteServiceableLocation(eliminatedServiceableLocationMap, serviceLocations)
		lcosErr := s.checkLocationLineOverlap(ctx, lineID, comparedServiceLocations, oldServiceableLocationMap)
		if lcosErr != nil {
			result.ParseFileErr = lcosErr
			return result, nil
		}
	}

	if async {
		// 异步落库，因此直接返回解析结果
		return result, nil
	}

	var addLocationList, deleteLocationList []*model.LineBasicServiceableLocationTab
	for _, addLocationModels := range addLocationModelsMap {
		addLocationList = append(addLocationList, addLocationModels...)
	}
	for _, deleteLocationModels := range deleteLocationModelsMap {
		deleteLocationList = append(deleteLocationList, deleteLocationModels...)
	}
	remoteFilePath, lcosErr := s.cardDeliveryAddressService.UploadCardDeliveryAddress(ctx, request.Region, addLocationList, deleteLocationList, basicConfMap)
	if lcosErr != nil {
		return result, lcosErr
	}

	fc := func() *lcos_error.LCOSError {
		for lineId, addLocationModels := range addLocationModelsMap {
			//if err := s.lineBasicServiceableLocationDAO.BatchDeleteBasicServiceableLocation(ctx, lineId, addLocationModels); err != nil {
			//	return err
			//}
			if _, err := s.lineBasicServiceableLocationDAO.BatchCreateBasicServiceableLocationModelOnDuplicate(ctx, lineId, addLocationModels); err != nil {
				return err
			}
		}
		for lineId, deleteLocationModels := range deleteLocationModelsMap {
			if err := s.lineBasicServiceableLocationDAO.BatchDeleteBasicServiceableLocation(ctx, lineId, deleteLocationModels); err != nil {
				return err
			}
		}
		return s.cardDeliveryAddressService.CreateCardDeliveryAddressChangeVersion(ctx, request.Region, remoteFilePath)
	}
	if err := ctx.Transaction(fc); err != nil {
		return result, err
	}

	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(lineIdList, ","))
	return result, nil
}

func (s *LineBasicServiceableLocationService) ParseBasicLocationServiceableArea(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, effectiveTime uint32) (map[string][]*model.LineBasicServiceableLocationTab, map[string][]*model.LineBasicServiceableLocationTab, excel.ParseFileResult) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}

	// 1. 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return nil, nil, result
	}

	// 开始解析
	lineNum := 0
	beginTime := time.Now() // nolint
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
		return nil, nil, result
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
		return nil, nil, result
	}
	defer os.Remove(filePath)

	uniqueKeyMap := make(map[string]int)
	lineIdMap := make(map[string]bool)
	var lineIdList []string
	var rowDataList []*serviceable_util.LineBasicLocationRowData
	orderAccountMap := make(map[int]bool)
	var orderAccountSet []int
	orderAccountMapping := make(map[string][]*order_account_mapping2.OrderAccountMapping) // line_id+group_id -> order_account list
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头和空行
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount() // 只统计非header行和有效行

		rowData := &serviceable_util.LineBasicLocationRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}
		if request.Region != rowData.Region {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", request.Region, rowData.RowId))
			continue
		}
		uniqueKey := utils.GenKey("-", rowData.LineId, rowData.GroupId, rowData.State, rowData.City, rowData.District, rowData.Street)
		if lastRowId, ok := uniqueKeyMap[uniqueKey]; ok {
			result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import data duplicated with same Line-Group-Address, please check | row %d duplicate with row %d, unique key=%s", rowData.RowId, lastRowId, uniqueKey)
			return nil, nil, result
		} else {
			uniqueKeyMap[uniqueKey] = rowData.RowId
		}

		// 将line id去重存在在列表中，方便批量查询
		if _, ok := lineIdMap[rowData.LineId]; !ok {
			lineIdList = append(lineIdList, rowData.LineId)
			lineIdMap[rowData.LineId] = true
		}

		lineGroupKey := utils.GenKey(":", rowData.LineId, rowData.GroupId)
		if _, ok := orderAccountMapping[lineGroupKey]; !ok {
			mappingList, err := s.orderAccountMappingService.GetOrderAccountMappingByLineAndGroup(ctx, rowData.Region, rowData.LineId, rowData.GroupId)
			if err != nil {
				result.ParseFileErr = err
				return nil, nil, result
			}
			for _, mapping := range mappingList {
				orderAccountMapping[lineGroupKey] = append(orderAccountMapping[lineGroupKey], mapping)
				if _, ok := orderAccountMap[mapping.OrderAccount]; !ok {
					orderAccountSet = append(orderAccountSet, mapping.OrderAccount)
					orderAccountMap[mapping.OrderAccount] = true
				}
			}
		}

		rowDataList = append(rowDataList, rowData)
	}

	if len(lineIdList) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is empty")
		return nil, nil, result
	}

	// 请求lls获取所有的line信息
	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIdList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return nil, nil, result
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}
	//// 请求lls获取所有的草稿态lineDraft信息
	//lineDraftMap, lcosErr := lls_service.BatchGetLineDraftsMap(ctx, lineIdList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return nil, nil, result
	//}
	//for k, v := range lineDraftMap {
	//	lineInfoMap[k] = v
	//}

	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(ctx, request.Region, lineIdList, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return nil, nil, result
	}
	// 获取上传的所有线的默认揽派能力
	basicConfMap, err := s.basicConfDao.GetBasicServiceableConfModelMapByLineIds(ctx, lineIdList)
	if err != nil {
		result.ParseFileErr = err
		return nil, nil, result
	}

	// 获取用于校验的spx服务范围版本
	spxVersion, err := s.spxServiceableAreaService.GetLastAvailableSpxServiceableAreaVersion(ctx, request.Region, effectiveTime)
	if err != nil {
		result.ParseFileErr = err
		return nil, nil, result
	}

	spxDataMap := make(map[int]map[uint64]*spx_serviceable_area_data.SpxServiceableAreaLocationData)
	if spxVersion != nil {
		spxData, err := s.spxServiceableAreaService.ListSpxServiceabelAreaLocationDataByOrderAccountList(ctx, request.Region, spxVersion.VersionId, orderAccountSet)
		if err != nil {
			result.ParseFileErr = err
			return nil, nil, result
		}
		for _, datum := range spxData {
			locationMap, ok := spxDataMap[datum.OrderAccount]
			if !ok {
				locationMap = make(map[uint64]*spx_serviceable_area_data.SpxServiceableAreaLocationData)
			}
			locationMap[datum.LocationId] = datum
			spxDataMap[datum.OrderAccount] = locationMap
		}
	}

	addLocationModelsMap := make(map[string][]*model.LineBasicServiceableLocationTab)
	deleteLocationModelsMap := make(map[string][]*model.LineBasicServiceableLocationTab)
	for _, rowData := range rowDataList {
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}

		locationInfo, err := address_service.LocationServer.GetLocationInfoByName(ctx, rowData.Region, rowData.State, rowData.City, rowData.District, rowData.Street)
		if err != nil {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Address invalid, %s | row=%d", err.Msg, rowData.RowId))
			continue
		}

		basicConf, ok := basicConfMap[rowData.LineId]
		if !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line serviceable area basic configuration not found | row=%d", rowData.RowId))
			continue
		}
		dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)

		locationModel := &model.LineBasicServiceableLocationTab{
			LineId:                rowData.LineId,
			CollectDeliverGroupId: rowData.GroupId,
			Region:                rowData.Region,
			LocationId:            uint64(locationInfo.GetLocationId()),
			State:                 rowData.State,
			City:                  rowData.City,
			District:              rowData.District,
			Street:                rowData.Street,
			CanPickup:             &rowData.SupportPickup,
			CanCodPickup:          &rowData.SupportCodPickup,
			CanDeliver:            &rowData.SupportDeliver,
			CanCodDeliver:         &rowData.SupportCodDeliver,
			SupportTradeIn:        rowData.SupportTradeIn,
		}
		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			// 新增或更新，校验此服务范围揽派能力在spx是否支持
			if err := s.batchCheckSpxLocationServiceableArea(ctx, locationModel.Region, locationModel.LineId, locationModel.CollectDeliverGroupId, locationModel.LocationId, locationModel, dropoffOnly, orderAccountMapping, spxDataMap); err != nil {
				result.WriteRowError(rowData.RowId, err)
				continue
			}

			if addLocationModelsMap[rowData.LineId] == nil {
				addLocationModelsMap[rowData.LineId] = []*model.LineBasicServiceableLocationTab{}
			}
			addLocationModelsMap[rowData.LineId] = append(addLocationModelsMap[rowData.LineId], locationModel)
		} else {
			// 删除，校验线默认揽派能力在spx是否支持
			if err := s.batchCheckSpxLocationServiceableArea(ctx, locationModel.Region, locationModel.LineId, locationModel.CollectDeliverGroupId, locationModel.LocationId, basicConf, dropoffOnly, orderAccountMapping, spxDataMap); err != nil {
				result.WriteRowError(rowData.RowId, err)
				continue
			}

			if deleteLocationModelsMap[rowData.LineId] == nil {
				deleteLocationModelsMap[rowData.LineId] = []*model.LineBasicServiceableLocationTab{}
			}
			deleteLocationModelsMap[rowData.LineId] = append(deleteLocationModelsMap[rowData.LineId], locationModel)
		}
	}

	// 检查line的服务范围是否重合 SPLN-19677
	// 先获取所有可能冲突的line列表
	var allNotAllowedLines []string
	NotAllowedOverlapMap := map[string][]*model.LineBasicServiceableLocationTab{}
	for _, tmpLine := range lineIdList {
		notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, tmpLine)
		allNotAllowedLines = append(allNotAllowedLines, notAllowedOverlapList...)
	}
	// 获取所有可能冲突的line服务范围
	for _, tmpLine := range allNotAllowedLines {
		if _, ok := NotAllowedOverlapMap[tmpLine]; !ok {
			serviceableLocations, lcosErr := s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableLocation(ctx, tmpLine, map[string]interface{}{"line_id": tmpLine})
			if lcosErr != nil {
				result.ParseFileErr = lcosErr
				return nil, nil, result
			}
			NotAllowedOverlapMap[tmpLine] = serviceableLocations
		}
	}

	// 需要删除掉deleteLocationModelsMap中的服务范围数据，防止误差
	eliminatedServiceableLocationMap := map[string]map[uint64]*model.LineBasicServiceableLocationTab{}
	for lineID, locationList := range deleteLocationModelsMap {
		for _, singleServiceableLocation := range locationList {
			if _, ok := eliminatedServiceableLocationMap[lineID]; !ok {
				eliminatedServiceableLocationMap[lineID] = map[uint64]*model.LineBasicServiceableLocationTab{}
			}
			eliminatedServiceableLocationMap[lineID][singleServiceableLocation.LocationId] = singleServiceableLocation
		}
	}
	// 经过eliminatedServiceableLocationMap过滤的需要比对重叠性的服务范围数据
	oldServiceableLocationMap := map[string]map[uint64][]*model.LineBasicServiceableLocationTab{}
	for lineID, serviceLocations := range NotAllowedOverlapMap {
		results := s.eliminatedDeleteServiceableLocation(eliminatedServiceableLocationMap, serviceLocations)
		for _, result := range results {
			if _, ok := oldServiceableLocationMap[lineID]; !ok {
				oldServiceableLocationMap[lineID] = map[uint64][]*model.LineBasicServiceableLocationTab{}
			}
			oldServiceableLocationMap[lineID][result.LocationId] = append(oldServiceableLocationMap[lineID][result.LocationId], result)
		}
	}

	// 对于批量上传来说，导入的数据也不能相互重叠。
	for _, serviceLocations := range addLocationModelsMap {
		comparedServiceLocations := s.eliminatedDeleteServiceableLocation(eliminatedServiceableLocationMap, serviceLocations)
		// 将数据导入到oldServiceableLocationMap，用于之后对比
		for _, singleLocation := range comparedServiceLocations {
			if _, ok := oldServiceableLocationMap[singleLocation.LineId]; !ok {
				oldServiceableLocationMap[singleLocation.LineId] = map[uint64][]*model.LineBasicServiceableLocationTab{}
			}
			oldServiceableLocationMap[singleLocation.LineId][singleLocation.LocationId] = append(oldServiceableLocationMap[singleLocation.LineId][singleLocation.LocationId], singleLocation)
		}
	}

	// 对比
	for lineID, serviceLocations := range addLocationModelsMap {
		comparedServiceLocations := s.eliminatedDeleteServiceableLocation(eliminatedServiceableLocationMap, serviceLocations)
		lcosErr := s.checkLocationLineOverlap(ctx, lineID, comparedServiceLocations, oldServiceableLocationMap)
		if lcosErr != nil {
			result.ParseFileErr = lcosErr
			return nil, nil, result
		}
	}

	return addLocationModelsMap, deleteLocationModelsMap, result
}

// 搜索四级地址
func (s *LineBasicServiceableLocationService) SearchAllFourLevelAddress(ctx utils.LCOSContext, request *common_protocol.SearchAllFourLevelAddressRequest) (*serviceable_core_logic.FourLevelAddress, *lcos_error.LCOSError) {
	searchMap := make(map[string]interface{})
	searchMap["line_id"] = request.LineId
	searchMap["region"] = request.Region
	searchMap["collect_deliver_group_id"] = request.CollectDeliverGroupId
	locationList, err := s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableLocation(ctx, request.LineId, searchMap)
	if err != nil {
		logger.LogErrorf("find four level address err, %s", err)
		return nil, err
	}
	stateMap := make(map[string]struct{})
	cityMap := make(map[string]struct{})
	districtMap := make(map[string]struct{})
	streetMap := make(map[string]struct{})

	stateList := make([]string, 0)
	cityList := make([]string, 0)
	districtList := make([]string, 0)
	streetList := make([]string, 0)

	for _, location := range locationList {
		if _, ok := stateMap[location.State]; !ok {
			if len(location.State) > 0 {
				stateMap[location.State] = struct{}{}
				stateList = append(stateList, location.State)
			}
		}
		if _, ok := cityMap[location.City]; !ok {
			if len(location.City) > 0 {
				cityMap[location.City] = struct{}{}
				cityList = append(cityList, location.City)
			}
		}
		if _, ok := districtMap[location.District]; !ok {
			if len(location.District) > 0 {
				districtMap[location.District] = struct{}{}
				districtList = append(districtList, location.District)
			}
		}
		if _, ok := streetMap[location.Street]; !ok {
			if len(location.Street) > 0 {
				streetMap[location.Street] = struct{}{}
				streetList = append(streetList, location.Street)
			}
		}
	}
	fourLevelAddress := &serviceable_core_logic.FourLevelAddress{
		StateList:    stateList,
		CityList:     cityList,
		DistrictList: districtList,
		StreetList:   streetList,
	}
	return fourLevelAddress, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) CreateBasicServiceableOriginLocation(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicOriginLocationRequest) (*model.LineOriginLocationBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	locationModel := new(model.LineOriginLocationBasicServiceableTab)
	if err := fillOriginLocationInfo(ctx, locationModel, request); err != nil {
		return nil, err
	}
	locationModel.LineId = request.LineId
	if _, err := s.lineBasicServiceableLocationDAO.CreateOriginLocationBasicServiceableModel(ctx, locationModel); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) CreateBasicServiceableDestLocation(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicDestLocationRequest) (*model.LineDestinationLocationBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	locationModel := new(model.LineDestinationLocationBasicServiceableTab)
	if err := fillDestLocationInfo(ctx, locationModel, request); err != nil {
		return nil, err
	}
	locationModel.LineId = request.LineId
	if _, err := s.lineBasicServiceableLocationDAO.CreateDestLocationBasicServiceableModel(ctx, locationModel); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) UpdateBasicServiceableOriginLocation(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicOriginLocationRequest) (*model.LineOriginLocationBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	locationModel, err := s.lineBasicServiceableLocationDAO.GetOriginLocationBasicServiceableModelById(ctx, request.ID, request.LineId)
	if err != nil {
		return nil, err
	}
	if locationModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicOriginLocationErrorCode)
	}

	if err := fillOriginLocationInfo(ctx, locationModel, &request.CreateBasicOriginLocationRequest); err != nil {
		return nil, err
	}
	if _, err := s.lineBasicServiceableLocationDAO.UpdateOriginLocationBasicServiceableModel(ctx, locationModel); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) UpdateBasicServiceableDestLocation(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicDestLocationRequest) (*model.LineDestinationLocationBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	locationModel, err := s.lineBasicServiceableLocationDAO.GetDestLocationBasicServiceableModelById(ctx, request.ID, request.LineId)
	if err != nil {
		return nil, err
	}
	if locationModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicDestLocationErrorCode)
	}

	if err := fillDestLocationInfo(ctx, locationModel, &request.CreateBasicDestLocationRequest); err != nil {
		return nil, err
	}
	if _, err := s.lineBasicServiceableLocationDAO.UpdateDestLocationBasicServiceableModel(ctx, locationModel); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) GetBasicServiceableOriginLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicOriginLocationListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)

	searchMap, _ := utils.Struct2map(request)
	models, total, err := s.lineBasicServiceableLocationDAO.SearchBasicServiceableOriginLocation(ctx, pageNo, count, request.LineId, searchMap)
	if err != nil {
		return nil, err
	}

	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) GetAllBasicServiceableOriginLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicOriginLocationRequest) ([]*model.LineOriginLocationBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	searchMap, _ := utils.Struct2map(request)
	models, err := s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableOriginLocation(ctx, request.LineId, searchMap)
	if err != nil {
		return nil, err
	}

	return models, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) GetBasicServiceableDestLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicDestLocationListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)

	searchMap, _ := utils.Struct2map(request)
	models, total, err := s.lineBasicServiceableLocationDAO.SearchBasicServiceableDestLocation(ctx, pageNo, count, request.LineId, searchMap)
	if err != nil {
		return nil, err
	}

	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) GetAllBasicServiceableDestLocationList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicDestLocationRequest) ([]*model.LineDestinationLocationBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	searchMap, _ := utils.Struct2map(request)
	models, err := s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableDestLocation(ctx, request.LineId, searchMap)
	if err != nil {
		return nil, err
	}

	return models, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) DeleteBasicServiceableOriginLocation(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicLocationRequest) *lcos_error.LCOSError {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return err
	}
	if !exists {
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	return s.lineBasicServiceableLocationDAO.DeleteOriginLocationBasicServiceableModel(ctx, request.LineId, request.ID)
}

// Deprecated
func (s *LineBasicServiceableLocationService) DeleteBasicServiceableDestLocation(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicLocationRequest) *lcos_error.LCOSError {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return err
	}
	if !exists {
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	return s.lineBasicServiceableLocationDAO.DeleteDestLocationBasicServiceableModel(ctx, request.LineId, request.ID)
}

// Deprecated
func (s *LineBasicServiceableLocationService) BatchCreateBasicServiceableOriginLocation(ctx utils.LCOSContext, request *basic_serviceable.BatchCreateBasicOriginLocationRequest) ([]*model.LineOriginLocationBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	locationModels := make([]*model.LineOriginLocationBasicServiceableTab, 0, len(request.LocationId))
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	wg.Add(len(request.LocationId))
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for _, locationId := range request.LocationId {
		limitChannel <- struct{}{}
		go func(singleLocationId uint64) {
			locationModel := new(model.LineOriginLocationBasicServiceableTab)
			if err := fillOriginLocationInfo(ctx, locationModel, &basic_serviceable.CreateBasicOriginLocationRequest{
				BaseLocationInfo: basic_serviceable.BaseLocationInfo{
					LineId:     request.LineId,
					Region:     request.Region,
					LocationId: singleLocationId,
				},
				CollectServiceType: request.CollectServiceType,
				CanPickup:          request.CanPickup,
				CanCodPickup:       request.CanCodPickup,
			}); err != nil {
				errList = append(errList, err)
			} else {
				locationModel.LineId = request.LineId
				locationModels = append(locationModels, locationModel)
			}
			<-limitChannel
			wg.Done()
		}(locationId)
	}
	wg.Wait()

	if len(errList) != 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.BatchCreateLocationErrorCode, lcos_error.GetMessageFromList(errList))
	}

	if _, err := s.lineBasicServiceableLocationDAO.BatchCreateOriginLocationBasicServiceableModel(ctx, request.LineId, locationModels); err != nil {
		return nil, err
	}
	return locationModels, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) BatchCreateBasicServiceableDestLocation(ctx utils.LCOSContext, request *basic_serviceable.BatchCreateBasicDestLocationRequest) ([]*model.LineDestinationLocationBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	locationModels := make([]*model.LineDestinationLocationBasicServiceableTab, 0, len(request.LocationId))
	var errList []*lcos_error.LCOSError

	// 是否需要流控
	wg := sync.WaitGroup{}
	wg.Add(len(request.LocationId))
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for _, locationId := range request.LocationId {
		limitChannel <- struct{}{}
		go func(singleLocationId uint64) {
			locationModel := new(model.LineDestinationLocationBasicServiceableTab)
			if err := fillDestLocationInfo(ctx, locationModel, &basic_serviceable.CreateBasicDestLocationRequest{
				BaseLocationInfo: basic_serviceable.BaseLocationInfo{
					LineId:     request.LineId,
					Region:     request.Region,
					LocationId: singleLocationId,
				},
				DeliverServiceType: request.DeliverServiceType,
				CanDeliver:         request.CanDeliver,
				CanCodDeliver:      request.CanCodDeliver,
			}); err != nil {
				errList = append(errList, err)
			} else {
				locationModel.LineId = request.LineId
				locationModels = append(locationModels, locationModel)
			}
			<-limitChannel
			wg.Done()
		}(locationId)
	}
	wg.Wait()

	if len(errList) != 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.BatchCreateLocationErrorCode, lcos_error.GetMessageFromList(errList))
	}

	if _, err := s.lineBasicServiceableLocationDAO.BatchCreateDestLocationBasicServiceableModel(ctx, request.LineId, locationModels); err != nil {
		return nil, err
	}
	return locationModels, nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) UploadBasicServiceableOriginLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/lcos_test_xlsx/test_basic_origin_location.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	addLocationModelsMap := make(map[string][]*model.LineOriginLocationBasicServiceableTab)
	deleteLocationModelsMap := make(map[string][]*model.LineOriginLocationBasicServiceableTab)
	var mutex sync.Mutex
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		if err := serviceable_util.CheckLocationRowData(row, request.Region, lineNum); err != nil {
			return err
		}

		wg.Add(1)
		limitChannel <- struct{}{}
		go func(rowData []string) {
			rowLineId := rowData[0]
			exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, rowLineId)
			if e != nil {
				errList = append(errList, e)
			}
			if !exists {
				logger.LogErrorf("upload location failed, line_id not exist|lineId=%s", rowLineId)
				errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
			}

			locationInfo, err := ops_service.GetLocationInfoByLocationName(ctx, serviceable_util.GetLocationRequestByLocationFileRow(row))
			if err != nil {
				errList = append(errList, err)
			} else {
				rowStruct := serviceable_util.ParseLocationRowData(rowData)
				locationModel := &model.LineOriginLocationBasicServiceableTab{
					LineId: rowStruct.LineId,
					//CollectServiceType: rowStruct.ServiceType,
					Region:     request.Region,
					LocationId: uint64(locationInfo.LocationId),
					State:      rowStruct.State,
					City:       rowStruct.City,
					District:   rowStruct.District,
					Street:     rowStruct.Street,
					/*CanPickup:          &rowStruct.PickupDeliver,
					CanCodPickup:       &rowStruct.CodPickupDeliver,*/
				}
				mutex.Lock()
				if rowStruct.ActionCode != -1 {
					if addLocationModelsMap[rowStruct.LineId] == nil {
						addLocationModelsMap[rowStruct.LineId] = []*model.LineOriginLocationBasicServiceableTab{}
					}
					addLocationModelsMap[rowStruct.LineId] = append(addLocationModelsMap[rowStruct.LineId], locationModel)
				} else {
					if deleteLocationModelsMap[rowStruct.LineId] == nil {
						deleteLocationModelsMap[rowStruct.LineId] = []*model.LineOriginLocationBasicServiceableTab{}
					}
					deleteLocationModelsMap[rowStruct.LineId] = append(deleteLocationModelsMap[rowStruct.LineId], locationModel)
				}
				mutex.Unlock()
			}
			<-limitChannel
			wg.Done()
		}(row)
	}
	wg.Wait()

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}

	fc := func() *lcos_error.LCOSError {
		for lineId, addLocationModels := range addLocationModelsMap {
			if err := s.lineBasicServiceableLocationDAO.BatchDeleteOriginLocationBasicServiceableModel(ctx, lineId, addLocationModels); err != nil {
				return err
			}
			if _, err := s.lineBasicServiceableLocationDAO.BatchCreateOriginLocationBasicServiceableModel(ctx, lineId, addLocationModels); err != nil {
				return err
			}
		}
		for lineId, deleteLocationModels := range deleteLocationModelsMap {
			if err := s.lineBasicServiceableLocationDAO.BatchDeleteOriginLocationBasicServiceableModel(ctx, lineId, deleteLocationModels); err != nil {
				return err
			}
		}

		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}

// Deprecated
func (s *LineBasicServiceableLocationService) UploadBasicServiceableDestLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_dest_location.xlsx")
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_dest_location.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	addLocationModelsMap := make(map[string][]*model.LineDestinationLocationBasicServiceableTab)
	deleteLocationModelsMap := make(map[string][]*model.LineDestinationLocationBasicServiceableTab)
	var mutex sync.Mutex
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		if err := serviceable_util.CheckLocationRowData(row, request.Region, lineNum); err != nil {
			return err
		}

		wg.Add(1)
		limitChannel <- struct{}{}
		go func(rowData []string) {
			rowLineId := rowData[0]
			exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, rowLineId)
			if e != nil {
				errList = append(errList, e)
			}
			if !exists {
				logger.LogErrorf("upload location failed, line_id not exist|lineId=%s", rowLineId)
				errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
			}

			locationInfo, err := ops_service.GetLocationInfoByLocationName(ctx, serviceable_util.GetLocationRequestByLocationFileRow(row))
			if err != nil {
				errList = append(errList, err)
			} else {
				rowStruct := serviceable_util.ParseLocationRowData(rowData)
				locationModel := &model.LineDestinationLocationBasicServiceableTab{
					LineId: rowStruct.LineId,
					//DeliverServiceType: rowStruct.ServiceType,
					Region:     request.Region,
					LocationId: uint64(locationInfo.LocationId),
					State:      rowStruct.State,
					City:       rowStruct.City,
					District:   rowStruct.District,
					Street:     rowStruct.Street,
					/*CanDeliver:         &rowStruct.PickupDeliver,
					CanCodDeliver:      &rowStruct.CodPickupDeliver,*/
				}
				mutex.Lock()
				if rowStruct.ActionCode != -1 {
					if addLocationModelsMap[rowStruct.LineId] == nil {
						addLocationModelsMap[rowStruct.LineId] = []*model.LineDestinationLocationBasicServiceableTab{}
					}
					addLocationModelsMap[rowStruct.LineId] = append(addLocationModelsMap[rowStruct.LineId], locationModel)
				} else {
					if deleteLocationModelsMap[rowStruct.LineId] == nil {
						deleteLocationModelsMap[rowStruct.LineId] = []*model.LineDestinationLocationBasicServiceableTab{}
					}
					deleteLocationModelsMap[rowStruct.LineId] = append(deleteLocationModelsMap[rowStruct.LineId], locationModel)
				}
				mutex.Unlock()
			}
			<-limitChannel
			wg.Done()
		}(row)
	}
	wg.Wait()

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}

	fc := func() *lcos_error.LCOSError {
		for lineId, addLocationModels := range addLocationModelsMap {
			if err := s.lineBasicServiceableLocationDAO.BatchDeleteDestLocationBasicServiceableModel(ctx, lineId, addLocationModels); err != nil {
				return err
			}
			if _, err := s.lineBasicServiceableLocationDAO.BatchCreateDestLocationBasicServiceableModel(ctx, lineId, addLocationModels); err != nil {
				return err
			}
		}
		for lineId, deleteLocationModels := range deleteLocationModelsMap {
			if err := s.lineBasicServiceableLocationDAO.BatchDeleteDestLocationBasicServiceableModel(ctx, lineId, deleteLocationModels); err != nil {
				return err
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}

func fillLocationInfo(ctx context.Context, locationModel *model.LineBasicServiceableLocationTab, request *basic_serviceable.CreateBasicLocationRequest) *lcos_error.LCOSError {
	locationModel.LineId = request.LineId
	locationModel.CollectDeliverGroupId = request.CollectDeliverGroupId
	locationModel.Region = request.Region
	locationModel.LocationId = request.LocationId
	locationModel.CanPickup = request.CanPickup
	locationModel.CanCodPickup = request.CanCodPickup
	locationModel.CanDeliver = request.CanDeliver
	locationModel.CanCodDeliver = request.CanCodDeliver
	if request.SupportTradeIn != nil {
		locationModel.SupportTradeIn = *request.SupportTradeIn
	}
	locationInfo, err := ops_service.GetUpLocationInfo(ctx, &ops_service.GetLocationInfoByIdRequest{
		Country:    request.Region,
		LocationId: request.LocationId,
	})

	if err != nil {
		return err
	}

	if locationInfo.State != nil {
		locationModel.State = *locationInfo.State
	}
	if locationInfo.City != nil {
		locationModel.City = *locationInfo.City
	}
	if locationInfo.District != nil {
		locationModel.District = *locationInfo.District
	}
	if locationInfo.Street != nil {
		locationModel.Street = *locationInfo.Street
	}
	return nil
}

// Deprecated
func fillOriginLocationInfo(ctx context.Context, locationModel *model.LineOriginLocationBasicServiceableTab, request *basic_serviceable.CreateBasicOriginLocationRequest) *lcos_error.LCOSError {
	locationModel.CollectServiceType = request.CollectServiceType
	locationModel.Region = request.Region
	locationModel.LocationId = request.LocationId
	locationModel.CanPickup = request.CanPickup
	locationModel.CanCodPickup = request.CanCodPickup

	locationInfo, err := ops_service.GetUpLocationInfo(ctx, &ops_service.GetLocationInfoByIdRequest{
		Country:    request.Region,
		LocationId: request.LocationId,
	})

	if err != nil {
		return err
	}

	if locationInfo.State != nil {
		locationModel.State = *locationInfo.State
	}
	if locationInfo.City != nil {
		locationModel.City = *locationInfo.City
	}
	if locationInfo.District != nil {
		locationModel.District = *locationInfo.District
	}
	if locationInfo.Street != nil {
		locationModel.Street = *locationInfo.Street
	}
	return nil
}

// Deprecated
func fillDestLocationInfo(ctx context.Context, locationModel *model.LineDestinationLocationBasicServiceableTab, request *basic_serviceable.CreateBasicDestLocationRequest) *lcos_error.LCOSError {
	locationModel.DeliverServiceType = request.DeliverServiceType
	locationModel.Region = request.Region
	locationModel.LocationId = request.LocationId
	locationModel.CanDeliver = request.CanDeliver
	locationModel.CanCodDeliver = request.CanCodDeliver

	locationInfo, err := ops_service.GetUpLocationInfo(ctx, &ops_service.GetLocationInfoByIdRequest{
		Country:    request.Region,
		LocationId: request.LocationId,
	})

	if err != nil {
		return err
	}

	if locationInfo.State != nil {
		locationModel.State = *locationInfo.State
	}
	if locationInfo.City != nil {
		locationModel.City = *locationInfo.City
	}
	if locationInfo.District != nil {
		locationModel.District = *locationInfo.District
	}
	if locationInfo.Street != nil {
		locationModel.Street = *locationInfo.Street
	}
	return nil
}

func (s *LineBasicServiceableLocationService) DeleteBasicServiceableLocationByLineId(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError {
	return s.lineBasicServiceableLocationDAO.DeleteBasicServiceableLocationByLineId(ctx, lineId)
}

func (s *LineBasicServiceableLocationService) GetAllBasicServiceableLocationByLineAndGroup(ctx utils.LCOSContext, lineId, groupId string) ([]*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"line_id":                  lineId,
		"collect_deliver_group_id": groupId,
	}
	return s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableLocation(ctx, lineId, queryParams)
}

func (s *LineBasicServiceableLocationService) GetAllBasicServiceableLocationByLine(ctx utils.LCOSContext, lineId string) ([]*model.LineBasicServiceableLocationTab, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"line_id": lineId,
	}
	return s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableLocation(ctx, lineId, queryParams)
}

func (s *LineBasicServiceableLocationService) singleCheckSpxLocationServiceableArea(ctx utils.LCOSContext, region, lineId, groupId string, locationId uint64, slsAbility serviceable_util.ServiceableAreaAbility, dropoffOnly bool) *lcos_error.LCOSError {
	if spx_serviceable_area_utils.SkipValidation(region) {
		// 此region不支持或禁用spx服务范围校验，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to apollo config|region=%s", region)
		return nil
	}
	if spx_serviceable_area_utils.IsPostcodeRegion(region) {
		// 此region在sls配置地址类型是location，在spx是postcode，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to location type mismatch|region=%s", region)
		return nil
	}

	// 1. 获取order account映射
	mappingList, err := s.orderAccountMappingService.GetOrderAccountMappingByLineAndGroup(ctx, region, lineId, groupId)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Check SPX serviceable area error, %s", err.Msg)
	}
	if len(mappingList) == 0 {
		// 此line无法找到order account映射，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to order account mapping not found|region=%s", region)
		return nil
	}

	// 2. 获取spx配置数据
	spxDataMap := make(map[int]*spx_serviceable_area_data.SpxServiceableAreaLocationData)
	spxVersion, err := s.spxServiceableAreaService.GetActiveSpxServiceableAreaVersion(ctx, region)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Check SPX serviceable area error, %s", err.Msg)
	}
	if spxVersion != nil {
		spxDataList, err := s.spxServiceableAreaService.ListSpxServiceabelAreaLocationDataByLocationId(ctx, region, spxVersion.VersionId, locationId)
		if err != nil {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Check SPX serviceable area error, %s", err.Msg)
		}
		for _, spxData := range spxDataList {
			spxDataMap[spxData.OrderAccount] = spxData
		}
	}

	// 3. 校验服务范围
	logger.CtxLogInfof(ctx, "spx serviceable area validation start|line_id=%s, group_id=%s, location_id=%d", lineId, groupId, locationId)
	for _, mapping := range mappingList {
		spxAbility, ok := spxDataMap[mapping.OrderAccount]
		if !ok {
			spxAbility = &spx_serviceable_area_data.SpxServiceableAreaLocationData{}
		}
		unsupportedAbility := spx_serviceable_area_utils.CheckUnsupportedAbilityInSpx(spxAbility, slsAbility, dropoffOnly)
		if len(unsupportedAbility) != 0 {
			logger.CtxLogErrorf(ctx, "spx serviceable area validation failed|line_id=%s, group_id=%s, order_account=%d, location_id=%d", lineId, groupId, mapping.OrderAccount, locationId)
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "SPX doesn't support %s under account '%s'", strings.Join(unsupportedAbility, ","), mapping.OrderAccountName)
		}
		logger.CtxLogInfof(ctx, "spx serviceable area validation success|line_id=%s, group_id=%s, order_account=%d, location_id=%d", lineId, groupId, mapping.OrderAccount, locationId)
	}

	return nil
}

func (s *LineBasicServiceableLocationService) batchCheckSpxLocationServiceableArea(ctx utils.LCOSContext, region, lineId, groupId string, locationId uint64, slsAbility serviceable_util.ServiceableAreaAbility, dropoffOnly bool, orderAccountMapping map[string][]*order_account_mapping2.OrderAccountMapping, spxDataMap map[int]map[uint64]*spx_serviceable_area_data.SpxServiceableAreaLocationData) *lcos_error.LCOSError {
	if spx_serviceable_area_utils.SkipValidation(region) {
		// 此region不支持或禁用spx服务范围校验，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to apollo config|region=%s", region)
		return nil
	}
	if spx_serviceable_area_utils.IsPostcodeRegion(region) {
		// 此region在sls配置地址类型是location，在spx是postcode，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to location type mismatch|region=%s", region)
		return nil
	}

	lineGroupKey := utils.GenKey(":", lineId, groupId)
	mappingList, ok := orderAccountMapping[lineGroupKey]
	if !ok || len(mappingList) == 0 {
		// 没有配置order account映射，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to order account mapping not found|region=%s", region)
		return nil
	}

	logger.CtxLogInfof(ctx, "spx serviceable area validation start|line_id=%s, group_id=%s, location_id=%d", lineId, groupId, locationId)
	for _, mapping := range mappingList {
		spxAbility := &spx_serviceable_area_data.SpxServiceableAreaLocationData{} // spx默认揽派能力不支持
		if locationMap, ok := spxDataMap[mapping.OrderAccount]; ok {
			if locationModel, ok := locationMap[locationId]; ok {
				spxAbility = locationModel // 根据order account和location id能匹配到服务范围配置
			}
		}
		unsupportedAbility := spx_serviceable_area_utils.CheckUnsupportedAbilityInSpx(spxAbility, slsAbility, dropoffOnly)
		if len(unsupportedAbility) != 0 {
			logger.CtxLogErrorf(ctx, "spx serviceable area validation failed|line_id=%s, group_id=%s, order_account=%d, location_id=%d", lineId, groupId, mapping.OrderAccount, locationId)
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "SPX doesn't support %s under account '%s'", strings.Join(unsupportedAbility, ","), mapping.OrderAccountName)
		}
		logger.CtxLogInfof(ctx, "spx serviceable area validation success|line_id=%s, group_id=%s, order_account=%d, location_id=%d", lineId, groupId, mapping.OrderAccount, locationId)
	}
	return nil
}

func (s *LineBasicServiceableLocationService) NotifyCardDeliveryAddressAllData(ctx utils.LCOSContext) *lcos_error.LCOSError {
	for region, lineId := range config.GetMutableConf(ctx).CardDeliveryAddressSyncConfig.SupportedLineMap {
		addresses, err := s.lineBasicServiceableLocationDAO.SearchAllBasicServiceableLocation(ctx, lineId, map[string]interface{}{"line_id": lineId})
		if err != nil {
			return err
		}
		if len(addresses) == 0 {
			continue
		}
		remoteFilePath, err := s.cardDeliveryAddressService.UploadCardDeliveryAddress(ctx, region, addresses, nil, nil)
		if err != nil {
			return err
		}
		if err = seabank_service.NewSeabankGatewayService(ctx, region).NotifyCardDeliveryAddressUpdate(ctx, "ALL", remoteFilePath); err != nil {
			return err
		}
	}
	return nil
}
