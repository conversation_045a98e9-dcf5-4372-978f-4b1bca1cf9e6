package basic_postcode

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	order_account_mapping2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area_utils"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/basic_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

type LineBasicServiceablePostcodeServiceInterface interface {
	CreateBasicServiceableOriginPostcode(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicOriginPostcodeRequest) (*model.LineOriginPostcodeBasicServiceableTab, *lcos_error.LCOSError)
	CreateBasicServiceableDestPostcode(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicDestPostcodeRequest) (*model.LineDestinationPostcodeBasicServiceableTab, *lcos_error.LCOSError)
	UpdateBasicServiceableOriginPostcode(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicOriginPostcodeRequest) (*model.LineOriginPostcodeBasicServiceableTab, *lcos_error.LCOSError)
	UpdateBasicServiceableDestPostcode(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicDestPostcodeRequest) (*model.LineDestinationPostcodeBasicServiceableTab, *lcos_error.LCOSError)
	GetBasicServiceableOriginPostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicOriginPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllBasicServiceableOriginPostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicOriginPostcodeRequest) ([]*model.LineOriginPostcodeBasicServiceableTab, *lcos_error.LCOSError)
	GetBasicServiceableDestPostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicDestPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllBasicServiceableDestPostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicDestPostcodeRequest) ([]*model.LineDestinationPostcodeBasicServiceableTab, *lcos_error.LCOSError)
	DeleteBasicServiceableOriginPostcode(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicPostcodeRequest) *lcos_error.LCOSError
	DeleteBasicServiceableDestPostcode(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicPostcodeRequest) *lcos_error.LCOSError
	UploadBasicServiceableOriginPostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	UploadBasicServiceableDestPostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError

	// 创建基础层postcode
	CreateBasicServiceablePostcode(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicPostcodeRequest) (*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError)
	// 更新基础层postocde
	UpdateBasicServiceablePostcode(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicPostcodeRequest) (*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError)
	// 获取基础层postcode分页列表
	GetBasicServiceablePostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError)
	// 获取基础层所有postcode
	GetAllBasicServiceablePostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicPostcodeRequest) ([]*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError)
	// 删除基础层postcode
	DeleteBasicServiceablePostcode(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicPostcodeRequest) *lcos_error.LCOSError

	// 删除基础层postcode
	DeleteBasicServiceablePostcodeByLineId(ctx utils.LCOSContext, lineID string) *lcos_error.LCOSError

	// 上传基础层postcode
	UploadBasicServiceablePostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	ParseAndImportBasicPostcodeSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool, effectiveTime uint32) ([][]string, excel.ParseFileResult, *lcos_error.LCOSError)
	ParseBasicPostcodeServiceableArea(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, effectiveTime uint32) (map[string][]*model.LineBasicServiceablePostcodeTab, map[string][]*model.LineBasicServiceablePostcodeTab, excel.ParseFileResult)

	// 通过入参获取所有的postcode信息 for saturn task
	ListAllBasicServiceablePostcode(ctx utils.LCOSContext, lineID string, queryParams map[string]interface{}) ([]*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError)

	GetAllBasicServiceablePostcodeByLineAndGroup(ctx utils.LCOSContext, lineId, groupId string) ([]*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError)
	GetAllBasicServiceablePostcodeByLine(ctx utils.LCOSContext, lineId string) ([]*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError)
}

type LineBasicServiceablePostcodeService struct {
	lineBasicServiceablePostcodeDAO model.LineBasicServiceablePostcodeDAO
	scheduledService                scheduled.ScheduledService
	basicConfDao                    basic_conf.LineBasicServiceableConfDAO
	orderAccountMappingService      order_account_mapping.OrderAccountMappingService
	spxServiceableAreaService       spx_serviceable_area.SpxServiceableAreaService
}

func NewLineBasicServiceablePostcodeService(lineBasicServiceablePostcodeDAO model.LineBasicServiceablePostcodeDAO, scheduledService scheduled.ScheduledService, basicConfDao basic_conf.LineBasicServiceableConfDAO, orderAccountMappingService order_account_mapping.OrderAccountMappingService, spxServiceableAreaService spx_serviceable_area.SpxServiceableAreaService) *LineBasicServiceablePostcodeService {
	return &LineBasicServiceablePostcodeService{
		lineBasicServiceablePostcodeDAO: lineBasicServiceablePostcodeDAO,
		scheduledService:                scheduledService,
		basicConfDao:                    basicConfDao,
		orderAccountMappingService:      orderAccountMappingService,
		spxServiceableAreaService:       spxServiceableAreaService,
	}
}

// 给saturn任务调用，获取相关的postcode信息
func (s *LineBasicServiceablePostcodeService) ListAllBasicServiceablePostcode(ctx utils.LCOSContext, lineID string, queryParams map[string]interface{}) ([]*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError) {
	return s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceablePostcode(ctx, lineID, queryParams)
}

// checkPostcodeLineOverlap oldPostcodeMap   line_id->postcode->postcodeServiceables
func (s *LineBasicServiceablePostcodeService) checkPostcodeLineOverlap(ctx utils.LCOSContext, lineID string, postcodeServiceables []*model.LineBasicServiceablePostcodeTab, oldPostcodeMap map[string]map[string][]*model.LineBasicServiceablePostcodeTab) *lcos_error.LCOSError {
	// SPLN-19677 需要校验线服务范围不重叠
	// 获取跟当前line位于一个组的group
	notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, lineID)
	if len(notAllowedOverlapList) != 0 {
		for _, notOverlapLine := range notAllowedOverlapList {
			if lineID != notOverlapLine {
				if oldServiceableLocationsMap, ok1 := oldPostcodeMap[notOverlapLine]; ok1 {
					for _, newPostcode := range postcodeServiceables {
						// 有重合的location
						if item2, ok2 := oldServiceableLocationsMap[newPostcode.Postcode]; ok2 {
							for _, item := range item2 {
								if item.CollectDeliverGroupId == newPostcode.CollectDeliverGroupId {
									errMsg := fmt.Sprintf("line serviceable overlap|new_line:[%s], old_line:[%s], postcode:[%s], group_id:[%s]", newPostcode.LineId, item.LineId, item.Postcode, item.CollectDeliverGroupId)
									logger.CtxLogErrorf(ctx, errMsg)
									return lcos_error.NewLCOSError(lcos_error.LineServiceAbleOverlap, errMsg)
								}
							}
						}
					}
				}
			}
		}
	}
	return nil
}

// 将需要删除的服务范围数据删除  deleteMap: line_id->location_id->serviceablePostcode
func (s *LineBasicServiceablePostcodeService) eliminatedDeleteServiceablePostcode(deleteMap map[string]map[string]*model.LineBasicServiceablePostcodeTab, inputs []*model.LineBasicServiceablePostcodeTab) []*model.LineBasicServiceablePostcodeTab {
	var outputs []*model.LineBasicServiceablePostcodeTab
	for _, input := range inputs {
		if item1, ok1 := deleteMap[input.LineId]; ok1 {
			// 需要删除的元素中，存在重复的serviceableLocation
			if item2, ok2 := item1[input.Postcode]; ok2 && item2.Postcode == input.Postcode && item2.CollectDeliverGroupId == input.CollectDeliverGroupId {
				continue
			}
		}
		outputs = append(outputs, input)
	}
	return outputs
}

// 创建基础层postcode
func (s *LineBasicServiceablePostcodeService) CreateBasicServiceablePostcode(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicPostcodeRequest) (*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	//exist, err := lls_service.CheckLineExist(ctx, request.LineId)
	//if err != nil {
	//return nil, err
	//}
	//if !exist {
	//logger.LogErrorf("batch create postcode failed, line_id not exist|lineId=%s", request.LineId)
	//return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	//}

	postcodeModel := new(model.LineBasicServiceablePostcodeTab)
	fillPostcodeModel(postcodeModel, request)
	postcodeModel.LineId = request.LineId

	basicConf, err := s.basicConfDao.GetBasicServiceableConfModelByLineId(ctx, request.LineId)
	if err != nil {
		return nil, err
	}
	if basicConf == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line basic config not found")
	}
	dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)
	if err := s.singleCheckSpxPostcodeServiceableArea(ctx, postcodeModel.Region, postcodeModel.LineId, postcodeModel.CollectDeliverGroupId, postcodeModel.Postcode, postcodeModel, dropoffOnly); err != nil {
		return nil, err
	}

	// SPLN-19677
	notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, request.LineId)
	// lineID->postcode->postcodeServiceable
	oldServiceablePostcodeMap := map[string]map[string][]*model.LineBasicServiceablePostcodeTab{}
	for _, singleLine := range notAllowedOverlapList {
		serviceablePostcodes, lcosErr := s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceablePostcode(ctx, singleLine, map[string]interface{}{"line_id": singleLine})
		if lcosErr != nil {
			return nil, lcosErr
		}
		for _, serviceablePostcode := range serviceablePostcodes {
			if _, ok := oldServiceablePostcodeMap[singleLine]; !ok {
				oldServiceablePostcodeMap[singleLine] = map[string][]*model.LineBasicServiceablePostcodeTab{}
			}
			oldServiceablePostcodeMap[singleLine][serviceablePostcode.Postcode] = append(oldServiceablePostcodeMap[singleLine][serviceablePostcode.Postcode], serviceablePostcode)
		}
	}
	lcosErr := s.checkPostcodeLineOverlap(ctx, postcodeModel.LineId, []*model.LineBasicServiceablePostcodeTab{postcodeModel}, oldServiceablePostcodeMap)
	if lcosErr != nil {
		return nil, lcosErr
	}

	if _, err := s.lineBasicServiceablePostcodeDAO.CreateBasicServiceablePostcodeModel(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// 更新基础层postocde
func (s *LineBasicServiceablePostcodeService) UpdateBasicServiceablePostcode(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicPostcodeRequest) (*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	postcodeModel, err := s.lineBasicServiceablePostcodeDAO.GetBasicServiceablePostcodeModelById(ctx, request.ID, request.LineId)
	if err != nil {
		return nil, err
	}
	if postcodeModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicPostcodeErrorCode)
	}

	fillPostcodeModel(postcodeModel, &request.CreateBasicPostcodeRequest)
	postcodeModel.LineId = request.LineId

	basicConf, err := s.basicConfDao.GetBasicServiceableConfModelByLineId(ctx, request.LineId)
	if err != nil {
		return nil, err
	}
	if basicConf == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line basic config not found")
	}
	dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)
	if err := s.singleCheckSpxPostcodeServiceableArea(ctx, postcodeModel.Region, postcodeModel.LineId, postcodeModel.CollectDeliverGroupId, postcodeModel.Postcode, postcodeModel, dropoffOnly); err != nil {
		return nil, err
	}

	if _, err := s.lineBasicServiceablePostcodeDAO.UpdateBasicServiceablePostcodeModel(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// 获取基础层postcode分页列表
func (s *LineBasicServiceablePostcodeService) GetBasicServiceablePostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)

	searchParams, _ := utils.Struct2map(request)
	models, total, err := s.lineBasicServiceablePostcodeDAO.SearchBasicServiceablePostcode(ctx, pageNo, count, request.LineId, searchParams)
	if err != nil {
		return nil, err
	}

	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// 获取基础层所有postcode
func (s *LineBasicServiceablePostcodeService) GetAllBasicServiceablePostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicPostcodeRequest) ([]*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	searchParams, _ := utils.Struct2map(request)
	models, err := s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceablePostcode(ctx, request.LineId, searchParams)
	if err != nil {
		return nil, err
	}

	return models, nil
}

// 删除基础层postcode
func (s *LineBasicServiceablePostcodeService) DeleteBasicServiceablePostcode(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicPostcodeRequest) *lcos_error.LCOSError {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return err
	}
	if !exists {
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	postcodeModel, err := s.lineBasicServiceablePostcodeDAO.GetBasicServiceablePostcodeModelById(ctx, request.ID, request.LineId)
	if err != nil {
		return err
	}

	basicConf, err := s.basicConfDao.GetBasicServiceableConfModelByLineId(ctx, request.LineId)
	if err != nil {
		return err
	}
	if basicConf == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line basic config not found")
	}

	// 删除配置后，校验线的默认揽派能力是否支持
	dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)
	if err := s.singleCheckSpxPostcodeServiceableArea(ctx, postcodeModel.Region, postcodeModel.LineId, postcodeModel.CollectDeliverGroupId, postcodeModel.Postcode, basicConf, dropoffOnly); err != nil {
		return err
	}

	return s.lineBasicServiceablePostcodeDAO.DeleteBasicServiceablePostcode(ctx, request.LineId, request.ID)
}

func (s *LineBasicServiceablePostcodeService) DeleteBasicServiceablePostcodeByLineId(ctx utils.LCOSContext, lineID string) *lcos_error.LCOSError {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, lineID) // 为了权限控制
	if err != nil {
		return err
	}
	if !exists {
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	return s.lineBasicServiceablePostcodeDAO.DeleteBasicServiceablePostcodeByLineId(ctx, lineID)
}

func (s *LineBasicServiceablePostcodeService) parseSingleBasicServiceablePostcode(ctx utils.LCOSContext, region string, rowData []string, addPostcodeModelsMap map[string][]*model.LineBasicServiceablePostcodeTab, deletePostcodeModelsMap map[string][]*model.LineBasicServiceablePostcodeTab, lineInfoMap map[string]*llspb.GetLineInfoResponseData) []*lcos_error.LCOSError {
	var errList []*lcos_error.LCOSError
	rowLineId := rowData[0]
	exist := true
	if _, ok := lineInfoMap[rowLineId]; !ok {
		exist = false
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.LineNotValidErrorCode, fmt.Sprintf("line is not valid|line_id=%v", rowLineId)))
	}
	if !exist {
		logger.LogErrorf("upload postcode failed, line_id not exist|lineId=%s", rowLineId)
		errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
	}
	rowStruct := serviceable_util.ParsePostcodeRowData(rowData)
	postcodeModel := &model.LineBasicServiceablePostcodeTab{
		LineId:                rowStruct.LineId,
		CollectDeliverGroupId: rowStruct.CollectDeliverGroupId,
		Region:                region,
		Postcode:              rowStruct.Postcode,
		CanPickup:             &rowStruct.Pickup,
		CanCodPickup:          &rowStruct.CodPickup,
		CanDeliver:            &rowStruct.Deliver,
		CanCodDeliver:         &rowStruct.CodDeliver,
	}
	if rowStruct.ActionCode != -1 {
		if addPostcodeModelsMap[rowStruct.LineId] == nil {
			addPostcodeModelsMap[rowStruct.LineId] = []*model.LineBasicServiceablePostcodeTab{}
		}
		addPostcodeModelsMap[rowStruct.LineId] = append(addPostcodeModelsMap[rowStruct.LineId], postcodeModel)
	} else {
		if deletePostcodeModelsMap[rowStruct.LineId] == nil {
			deletePostcodeModelsMap[rowStruct.LineId] = []*model.LineBasicServiceablePostcodeTab{}
		}
		deletePostcodeModelsMap[rowStruct.LineId] = append(deletePostcodeModelsMap[rowStruct.LineId], postcodeModel)
	}
	return errList
}

func (s *LineBasicServiceablePostcodeService) UploadBasicServiceablePostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	job, err := s.scheduledService.CreateScheduledJob(ctx, request.Region, scheduled_constant.ThreePLServiceableArea, scheduled_constant.PostCode, request.FileUrl, request.IsScheduled, request.ScheduledTime, ctx.GetUserEmail())
	if err != nil {
		return err
	}

	go func() {
		_, parseFileResult, _ := s.ParseAndImportBasicPostcodeSA(ctx, request, serviceable_util.LineBasicPostcodeHeader, true, job.ScheduledTime)
		if err := s.scheduledService.UpdateValidateResult(ctx, job, parseFileResult); err != nil {
			logger.CtxLogErrorf(ctx, "scheduled job update validate result error|job_id=%d, cause=%s", job.ID, err.Msg)
		}
	}()

	return nil
}

func (s *LineBasicServiceablePostcodeService) ParseAndImportBasicPostcodeSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool, effectiveTime uint32) ([][]string, excel.ParseFileResult, *lcos_error.LCOSError) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}

	region := request.Region
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return nil, result, nil
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
		return nil, result, nil
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
		return nil, result, nil
	}
	defer os.Remove(filePath)

	uniqueKeyMap := make(map[string]int)
	lineIDMap := make(map[string]bool) // 用于去重
	var lineIDList []string
	var rowDataList []*serviceable_util.LineBasicPostcodeRowData
	var allRows [][]string // 保存上传文件内容，需要返回
	orderAccountMap := make(map[int]bool)
	var orderAccountSet []int
	orderAccountMapping := make(map[string][]*order_account_mapping2.OrderAccountMapping) // line_id+group_id -> order_account list
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount()

		rowData := &serviceable_util.LineBasicPostcodeRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}
		if request.Region != rowData.Region {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", request.Region, rowData.RowId))
			continue
		}
		uniqueKey := utils.GenKey("-", rowData.LineId, rowData.GroupId, rowData.Postcode)
		if lastRowId, ok := uniqueKeyMap[uniqueKey]; ok {
			result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import data duplicated with same Line-Group-Address, please check | row %d duplicate with row %d, unique key=%s", rowData.RowId, lastRowId, uniqueKey)
			return allRows, result, nil
		} else {
			uniqueKeyMap[uniqueKey] = rowData.RowId
		}

		if _, ok := lineIDMap[rowData.LineId]; !ok {
			lineIDList = append(lineIDList, rowData.LineId)
			lineIDMap[rowData.LineId] = true
		}

		lineGroupKey := utils.GenKey(":", rowData.LineId, rowData.GroupId)
		if _, ok := orderAccountMapping[lineGroupKey]; !ok {
			mappingList, err := s.orderAccountMappingService.GetOrderAccountMappingByLineAndGroup(ctx, rowData.Region, rowData.LineId, rowData.GroupId)
			if err != nil {
				result.ParseFileErr = err
				return nil, result, nil
			}
			for _, mapping := range mappingList {
				orderAccountMapping[lineGroupKey] = append(orderAccountMapping[lineGroupKey], mapping)
				if _, ok := orderAccountMap[mapping.OrderAccount]; !ok {
					orderAccountSet = append(orderAccountSet, mapping.OrderAccount)
					orderAccountMap[mapping.OrderAccount] = true
				}
			}
		}

		rowDataList = append(rowDataList, rowData)
		allRows = append(allRows, row)
	}

	if len(lineIDList) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is empty")
		return allRows, result, nil
	}

	// 请求lls获取所有的line信息
	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIDList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return allRows, result, nil
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}
	//// 请求lls获取所有的草稿态lineDraft信息
	//lineDraftMap, lcosErr := lls_service.BatchGetLineDraftsMap(ctx, lineIDList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return allRows, result, nil
	//}
	//for k, v := range lineDraftMap {
	//	lineInfoMap[k] = v
	//}
	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(ctx, region, lineIDList, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return allRows, result, nil
	}
	basicConfMap, err := s.basicConfDao.GetBasicServiceableConfModelMapByLineIds(ctx, lineIDList)
	if err != nil {
		result.ParseFileErr = err
		return allRows, result, nil
	}

	// 获取用于校验的spx服务范围版本
	var spxVersion *spx_serviceable_area_version.SpxServiceableAreaVersion
	if async {
		// 非立即落库，需要以生效时间获取spx版本校验
		spxVersion, err = s.spxServiceableAreaService.GetLastAvailableSpxServiceableAreaVersion(ctx, request.Region, effectiveTime)
	} else {
		// 立即落库，需要获取已生效的spx版本校验
		spxVersion, err = s.spxServiceableAreaService.GetActiveSpxServiceableAreaVersion(ctx, request.Region)
	}
	if err != nil {
		result.ParseFileErr = err
		return nil, result, nil
	}

	spxDataMap := make(map[int]map[string]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData)
	if spxVersion != nil {
		spxData, err := s.spxServiceableAreaService.ListSpxServiceabelAreaPostcodeDataByOrderAccountList(ctx, request.Region, spxVersion.VersionId, orderAccountSet)
		if err != nil {
			result.ParseFileErr = err
			return nil, result, nil
		}
		for _, datum := range spxData {
			postcodeMap, ok := spxDataMap[datum.OrderAccount]
			if !ok {
				postcodeMap = make(map[string]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData)
			}
			postcodeMap[datum.Postcode] = datum
			spxDataMap[datum.OrderAccount] = postcodeMap
		}
	}

	addPostcodeModelsMap := make(map[string][]*model.LineBasicServiceablePostcodeTab)
	deletePostcodeModelsMap := make(map[string][]*model.LineBasicServiceablePostcodeTab)
	for _, rowData := range rowDataList {
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}

		basicConf, ok := basicConfMap[rowData.LineId]
		if !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line serviceable area basic configuration not found | row=%d", rowData.RowId))
			continue
		}
		dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)

		postcodeModel := &model.LineBasicServiceablePostcodeTab{
			LineId:                rowData.LineId,
			CollectDeliverGroupId: rowData.GroupId,
			Region:                region,
			Postcode:              rowData.Postcode,
			CanPickup:             &rowData.SupportPickup,
			CanCodPickup:          &rowData.SupportCodPickup,
			CanDeliver:            &rowData.SupportDeliver,
			CanCodDeliver:         &rowData.SupportCodDeliver,
			SupportTradeIn:        rowData.SupportTradeIn,
		}

		// 校验更新后的服务范围配置在spx是否支持
		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			if err := s.batchCheckSpxPostcodeServiceableArea(ctx, postcodeModel.Region, postcodeModel.LineId, postcodeModel.CollectDeliverGroupId, postcodeModel.Postcode, postcodeModel, dropoffOnly, orderAccountMapping, spxDataMap); err != nil {
				result.WriteRowError(rowData.RowId, err)
				continue
			}
		} else {
			if err := s.batchCheckSpxPostcodeServiceableArea(ctx, postcodeModel.Region, postcodeModel.LineId, postcodeModel.CollectDeliverGroupId, postcodeModel.Postcode, basicConf, dropoffOnly, orderAccountMapping, spxDataMap); err != nil {
				result.WriteRowError(rowData.RowId, err)
				continue
			}
		}

		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			if addPostcodeModelsMap[rowData.LineId] == nil {
				addPostcodeModelsMap[rowData.LineId] = []*model.LineBasicServiceablePostcodeTab{}
			}
			addPostcodeModelsMap[rowData.LineId] = append(addPostcodeModelsMap[rowData.LineId], postcodeModel)
		} else {
			if deletePostcodeModelsMap[rowData.LineId] == nil {
				deletePostcodeModelsMap[rowData.LineId] = []*model.LineBasicServiceablePostcodeTab{}
			}
			deletePostcodeModelsMap[rowData.LineId] = append(deletePostcodeModelsMap[rowData.LineId], postcodeModel)
		}
	}

	// 检查line的服务范围是否重合 SPLN-19677
	// 先获取所有可能冲突的line列表
	var allNotAllowedLines []string
	NotAllowedOverlapMap := map[string][]*model.LineBasicServiceablePostcodeTab{}
	for _, tmpLine := range lineIDList {
		notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, tmpLine)
		allNotAllowedLines = append(allNotAllowedLines, notAllowedOverlapList...)
	}
	// 获取所有可能冲突的line服务范围
	for _, tmpLine := range allNotAllowedLines {
		if _, ok := NotAllowedOverlapMap[tmpLine]; !ok {
			serviceableLocations, lcosErr := s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceablePostcode(ctx, tmpLine, map[string]interface{}{"line_id": tmpLine})
			if lcosErr != nil {
				result.ParseFileErr = lcosErr
				return allRows, result, nil
			}
			NotAllowedOverlapMap[tmpLine] = serviceableLocations
		}
	}

	// 需要删除掉deletePostcodeModelsMap中的服务范围数据，防止误差
	eliminatedServiceablePostcodeMap := map[string]map[string]*model.LineBasicServiceablePostcodeTab{}
	for lineID, locationList := range deletePostcodeModelsMap {
		for _, singleServiceablePostcode := range locationList {
			if _, ok := eliminatedServiceablePostcodeMap[lineID]; !ok {
				eliminatedServiceablePostcodeMap[lineID] = map[string]*model.LineBasicServiceablePostcodeTab{}
			}
			eliminatedServiceablePostcodeMap[lineID][singleServiceablePostcode.Postcode] = singleServiceablePostcode
		}
	}
	// 经过eliminatedServiceableLocationMap过滤的需要比对重叠性的服务范围数据
	oldServiceablePostcodeMap := map[string]map[string][]*model.LineBasicServiceablePostcodeTab{}
	for lineID, servicePostcodes := range NotAllowedOverlapMap {
		results := s.eliminatedDeleteServiceablePostcode(eliminatedServiceablePostcodeMap, servicePostcodes)
		for _, result := range results {
			if _, ok := oldServiceablePostcodeMap[lineID]; !ok {
				oldServiceablePostcodeMap[lineID] = map[string][]*model.LineBasicServiceablePostcodeTab{}
			}
			oldServiceablePostcodeMap[lineID][result.Postcode] = append(oldServiceablePostcodeMap[lineID][result.Postcode], result)
		}
	}

	// 对于批量上传来说，导入的数据也不能相互重叠。
	for _, servicePostcodes := range addPostcodeModelsMap {
		comparedServicePostcodes := s.eliminatedDeleteServiceablePostcode(eliminatedServiceablePostcodeMap, servicePostcodes)
		// 将数据导入到oldServiceableLocationMap，用于之后对比
		for _, singlePostcode := range comparedServicePostcodes {
			if _, ok := oldServiceablePostcodeMap[singlePostcode.LineId]; !ok {
				oldServiceablePostcodeMap[singlePostcode.LineId] = map[string][]*model.LineBasicServiceablePostcodeTab{}
			}
			oldServiceablePostcodeMap[singlePostcode.LineId][singlePostcode.Postcode] = append(oldServiceablePostcodeMap[singlePostcode.LineId][singlePostcode.Postcode], singlePostcode)
		}
	}

	// 对比
	for lineID, serviceLocations := range addPostcodeModelsMap {
		comparedServiceLocations := s.eliminatedDeleteServiceablePostcode(eliminatedServiceablePostcodeMap, serviceLocations)
		lcosErr = s.checkPostcodeLineOverlap(ctx, lineID, comparedServiceLocations, oldServiceablePostcodeMap)
		if lcosErr != nil {
			result.ParseFileErr = lcosErr
			return allRows, result, nil
		}
	}

	if async {
		// 异步落库
		return allRows, result, nil
	}

	fc := func() *lcos_error.LCOSError {
		for lineId, addPostcodeModels := range addPostcodeModelsMap {
			//if err := s.lineBasicServiceablePostcodeDAO.BatchDeleteBasicServiceablePostcode(ctx, lineId, addPostcodeModels); err != nil {
			//	return err
			//}
			if _, err := s.lineBasicServiceablePostcodeDAO.BatchCreateServiceablePostcodeModelOnDuplicate(ctx, lineId, addPostcodeModels); err != nil {
				return err
			}
		}

		for lineId, deletePostcodeModels := range deletePostcodeModelsMap {
			if err := s.lineBasicServiceablePostcodeDAO.BatchDeleteBasicServiceablePostcode(ctx, lineId, deletePostcodeModels); err != nil {
				return err
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return allRows, result, err
	}

	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(lineIDList, ","))
	return allRows, result, nil
}

func (s *LineBasicServiceablePostcodeService) ParseBasicPostcodeServiceableArea(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, effectiveTime uint32) (map[string][]*model.LineBasicServiceablePostcodeTab, map[string][]*model.LineBasicServiceablePostcodeTab, excel.ParseFileResult) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}

	region := request.Region
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return nil, nil, result
	}

	// 开始解析
	lineNum := 0
	beginTime := time.Now() // nolint
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
		return nil, nil, result
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
		return nil, nil, result
	}
	defer os.Remove(filePath)

	uniqueKeyMap := make(map[string]int)
	lineIDMap := make(map[string]bool) // 用于去重
	var lineIDList []string
	var rowDataList []*serviceable_util.LineBasicPostcodeRowData
	orderAccountMap := make(map[int]bool)
	var orderAccountSet []int
	orderAccountMapping := make(map[string][]*order_account_mapping2.OrderAccountMapping) // line_id+group_id -> order_account list
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount()

		rowData := &serviceable_util.LineBasicPostcodeRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}
		if request.Region != rowData.Region {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", request.Region, rowData.RowId))
			continue
		}
		uniqueKey := utils.GenKey("-", rowData.LineId, rowData.GroupId, rowData.Postcode)
		if lastRowId, ok := uniqueKeyMap[uniqueKey]; ok {
			result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import data duplicated with same Line-Group-Address, please check | row %d duplicate with row %d, unique key=%s", rowData.RowId, lastRowId, uniqueKey)
			return nil, nil, result
		} else {
			uniqueKeyMap[uniqueKey] = rowData.RowId
		}

		if _, ok := lineIDMap[rowData.LineId]; !ok {
			lineIDList = append(lineIDList, rowData.LineId)
			lineIDMap[rowData.LineId] = true
		}

		lineGroupKey := utils.GenKey(":", rowData.LineId, rowData.GroupId)
		if _, ok := orderAccountMapping[lineGroupKey]; !ok {
			mappingList, err := s.orderAccountMappingService.GetOrderAccountMappingByLineAndGroup(ctx, rowData.Region, rowData.LineId, rowData.GroupId)
			if err != nil {
				result.ParseFileErr = err
				return nil, nil, result
			}
			for _, mapping := range mappingList {
				orderAccountMapping[lineGroupKey] = append(orderAccountMapping[lineGroupKey], mapping)
				if _, ok := orderAccountMap[mapping.OrderAccount]; !ok {
					orderAccountSet = append(orderAccountSet, mapping.OrderAccount)
					orderAccountMap[mapping.OrderAccount] = true
				}
			}
		}

		rowDataList = append(rowDataList, rowData)
	}

	if len(lineIDList) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is empty")
		return nil, nil, result
	}

	// 请求lls获取所有的line信息
	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIDList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return nil, nil, result
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}
	//// 请求lls获取所有的草稿态lineDraft信息
	//lineDraftMap, lcosErr := lls_service.BatchGetLineDraftsMap(ctx, lineIDList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return nil, nil, result
	//}
	//for k, v := range lineDraftMap {
	//	lineInfoMap[k] = v
	//}
	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(ctx, region, lineIDList, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return nil, nil, result
	}
	basicConfMap, err := s.basicConfDao.GetBasicServiceableConfModelMapByLineIds(ctx, lineIDList)
	if err != nil {
		result.ParseFileErr = err
		return nil, nil, result
	}

	// 获取用于校验的spx服务范围版本
	spxVersion, err := s.spxServiceableAreaService.GetLastAvailableSpxServiceableAreaVersion(ctx, request.Region, effectiveTime)
	if err != nil {
		result.ParseFileErr = err
		return nil, nil, result
	}

	spxDataMap := make(map[int]map[string]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData)
	if spxVersion != nil {
		spxData, err := s.spxServiceableAreaService.ListSpxServiceabelAreaPostcodeDataByOrderAccountList(ctx, request.Region, spxVersion.VersionId, orderAccountSet)
		if err != nil {
			result.ParseFileErr = err
			return nil, nil, result
		}
		for _, datum := range spxData {
			postcodeMap, ok := spxDataMap[datum.OrderAccount]
			if !ok {
				postcodeMap = make(map[string]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData)
			}
			postcodeMap[datum.Postcode] = datum
			spxDataMap[datum.OrderAccount] = postcodeMap
		}
	}

	addPostcodeModelsMap := make(map[string][]*model.LineBasicServiceablePostcodeTab)
	deletePostcodeModelsMap := make(map[string][]*model.LineBasicServiceablePostcodeTab)
	for _, rowData := range rowDataList {
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}

		basicConf, ok := basicConfMap[rowData.LineId]
		if !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line serviceable area basic configuration not found | row=%d", rowData.RowId))
			continue
		}
		dropoffOnly := serviceable_util.IsDropoffOnly(basicConf.CollectDeliverAbility)

		postcodeModel := &model.LineBasicServiceablePostcodeTab{
			LineId:                rowData.LineId,
			CollectDeliverGroupId: rowData.GroupId,
			Region:                region,
			Postcode:              rowData.Postcode,
			CanPickup:             &rowData.SupportPickup,
			CanCodPickup:          &rowData.SupportCodPickup,
			CanDeliver:            &rowData.SupportDeliver,
			CanCodDeliver:         &rowData.SupportCodDeliver,
		}

		// 校验更新后的服务范围配置在spx是否支持
		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			if err := s.batchCheckSpxPostcodeServiceableArea(ctx, postcodeModel.Region, postcodeModel.LineId, postcodeModel.CollectDeliverGroupId, postcodeModel.Postcode, postcodeModel, dropoffOnly, orderAccountMapping, spxDataMap); err != nil {
				result.WriteRowError(rowData.RowId, err)
				continue
			}
			if addPostcodeModelsMap[rowData.LineId] == nil {
				addPostcodeModelsMap[rowData.LineId] = []*model.LineBasicServiceablePostcodeTab{}
			}
			addPostcodeModelsMap[rowData.LineId] = append(addPostcodeModelsMap[rowData.LineId], postcodeModel)
		} else {
			if err := s.batchCheckSpxPostcodeServiceableArea(ctx, postcodeModel.Region, postcodeModel.LineId, postcodeModel.CollectDeliverGroupId, postcodeModel.Postcode, basicConf, dropoffOnly, orderAccountMapping, spxDataMap); err != nil {
				result.WriteRowError(rowData.RowId, err)
				continue
			}

			if deletePostcodeModelsMap[rowData.LineId] == nil {
				deletePostcodeModelsMap[rowData.LineId] = []*model.LineBasicServiceablePostcodeTab{}
			}
			deletePostcodeModelsMap[rowData.LineId] = append(deletePostcodeModelsMap[rowData.LineId], postcodeModel)
		}
	}

	// 检查line的服务范围是否重合 SPLN-19677
	// 先获取所有可能冲突的line列表
	var allNotAllowedLines []string
	NotAllowedOverlapMap := map[string][]*model.LineBasicServiceablePostcodeTab{}
	for _, tmpLine := range lineIDList {
		notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, tmpLine)
		allNotAllowedLines = append(allNotAllowedLines, notAllowedOverlapList...)
	}
	// 获取所有可能冲突的line服务范围
	for _, tmpLine := range allNotAllowedLines {
		if _, ok := NotAllowedOverlapMap[tmpLine]; !ok {
			serviceableLocations, lcosErr := s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceablePostcode(ctx, tmpLine, map[string]interface{}{"line_id": tmpLine})
			if lcosErr != nil {
				result.ParseFileErr = lcosErr
				return nil, nil, result
			}
			NotAllowedOverlapMap[tmpLine] = serviceableLocations
		}
	}

	// 需要删除掉deletePostcodeModelsMap中的服务范围数据，防止误差
	eliminatedServiceablePostcodeMap := map[string]map[string]*model.LineBasicServiceablePostcodeTab{}
	for lineID, locationList := range deletePostcodeModelsMap {
		for _, singleServiceablePostcode := range locationList {
			if _, ok := eliminatedServiceablePostcodeMap[lineID]; !ok {
				eliminatedServiceablePostcodeMap[lineID] = map[string]*model.LineBasicServiceablePostcodeTab{}
			}
			eliminatedServiceablePostcodeMap[lineID][singleServiceablePostcode.Postcode] = singleServiceablePostcode
		}
	}
	// 经过eliminatedServiceableLocationMap过滤的需要比对重叠性的服务范围数据
	oldServiceablePostcodeMap := map[string]map[string][]*model.LineBasicServiceablePostcodeTab{}
	for lineID, servicePostcodes := range NotAllowedOverlapMap {
		results := s.eliminatedDeleteServiceablePostcode(eliminatedServiceablePostcodeMap, servicePostcodes)
		for _, result := range results {
			if _, ok := oldServiceablePostcodeMap[lineID]; !ok {
				oldServiceablePostcodeMap[lineID] = map[string][]*model.LineBasicServiceablePostcodeTab{}
			}
			oldServiceablePostcodeMap[lineID][result.Postcode] = append(oldServiceablePostcodeMap[lineID][result.Postcode], result)
		}
	}

	// 对于批量上传来说，导入的数据也不能相互重叠。
	for _, servicePostcodes := range addPostcodeModelsMap {
		comparedServicePostcodes := s.eliminatedDeleteServiceablePostcode(eliminatedServiceablePostcodeMap, servicePostcodes)
		// 将数据导入到oldServiceableLocationMap，用于之后对比
		for _, singlePostcode := range comparedServicePostcodes {
			if _, ok := oldServiceablePostcodeMap[singlePostcode.LineId]; !ok {
				oldServiceablePostcodeMap[singlePostcode.LineId] = map[string][]*model.LineBasicServiceablePostcodeTab{}
			}
			oldServiceablePostcodeMap[singlePostcode.LineId][singlePostcode.Postcode] = append(oldServiceablePostcodeMap[singlePostcode.LineId][singlePostcode.Postcode], singlePostcode)
		}
	}

	// 对比
	for lineID, serviceLocations := range addPostcodeModelsMap {
		comparedServiceLocations := s.eliminatedDeleteServiceablePostcode(eliminatedServiceablePostcodeMap, serviceLocations)
		lcosErr = s.checkPostcodeLineOverlap(ctx, lineID, comparedServiceLocations, oldServiceablePostcodeMap)
		if lcosErr != nil {
			result.ParseFileErr = lcosErr
			return nil, nil, result
		}
	}

	return addPostcodeModelsMap, deletePostcodeModelsMap, result
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) CreateBasicServiceableOriginPostcode(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicOriginPostcodeRequest) (*model.LineOriginPostcodeBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	postcodeModel := new(model.LineOriginPostcodeBasicServiceableTab)
	fillOriginPostcodeModel(postcodeModel, request)
	postcodeModel.LineId = request.LineId
	if _, err := s.lineBasicServiceablePostcodeDAO.CreateOriginPostcodeBasicServiceableModel(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) CreateBasicServiceableDestPostcode(ctx utils.LCOSContext, request *basic_serviceable.CreateBasicDestPostcodeRequest) (*model.LineDestinationPostcodeBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	postcodeModel := new(model.LineDestinationPostcodeBasicServiceableTab)
	fillDestPostcodeModel(postcodeModel, request)
	postcodeModel.LineId = request.LineId
	if _, err := s.lineBasicServiceablePostcodeDAO.CreateDestPostcodeBasicServiceableModel(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) UpdateBasicServiceableOriginPostcode(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicOriginPostcodeRequest) (*model.LineOriginPostcodeBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	postcodeModel, err := s.lineBasicServiceablePostcodeDAO.GetOriginPostCodeBasicServiceableModelById(ctx, request.ID, request.LineId)
	if err != nil {
		return nil, err
	}
	if postcodeModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicOriginPostcodeErrorCode)
	}

	fillOriginPostcodeModel(postcodeModel, &request.CreateBasicOriginPostcodeRequest)
	if _, err := s.lineBasicServiceablePostcodeDAO.UpdateOriginPostcodeBasicServiceableModel(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) UpdateBasicServiceableDestPostcode(ctx utils.LCOSContext, request *basic_serviceable.UpdateBasicDestPostcodeRequest) (*model.LineDestinationPostcodeBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	postcodeModel, err := s.lineBasicServiceablePostcodeDAO.GetDestPostCodeBasicServiceableModelById(ctx, request.ID, request.LineId)
	if err != nil {
		return nil, err
	}
	if postcodeModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicDestPostcodeErrorCode)
	}

	fillDestPostcodeModel(postcodeModel, &request.CreateBasicDestPostcodeRequest)
	if _, err := s.lineBasicServiceablePostcodeDAO.UpdateDestPostcodeBasicServiceableModel(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) GetBasicServiceableOriginPostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicOriginPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)

	searchParams, _ := utils.Struct2map(request)
	models, total, err := s.lineBasicServiceablePostcodeDAO.SearchBasicServiceableOriginPostcode(ctx, pageNo, count, request.LineId, searchParams)
	if err != nil {
		return nil, err
	}

	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) GetAllBasicServiceableOriginPostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicOriginPostcodeRequest) ([]*model.LineOriginPostcodeBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	searchParams, _ := utils.Struct2map(request)
	models, err := s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceableOriginPostcode(ctx, request.LineId, searchParams)
	if err != nil {
		return nil, err
	}

	return models, nil
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) GetBasicServiceableDestPostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetBasicDestPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)

	searchParams, _ := utils.Struct2map(request)
	models, total, err := s.lineBasicServiceablePostcodeDAO.SearchBasicServiceableDestPostcode(ctx, pageNo, count, request.LineId, searchParams)
	if err != nil {
		return nil, err
	}

	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) GetAllBasicServiceableDestPostcodeList(ctx utils.LCOSContext, request *basic_serviceable.GetAllBasicDestPostcodeRequest) ([]*model.LineDestinationPostcodeBasicServiceableTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	searchParams, _ := utils.Struct2map(request)
	models, err := s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceableDestPostcode(ctx, request.LineId, searchParams)
	if err != nil {
		return nil, err
	}

	return models, nil
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) DeleteBasicServiceableOriginPostcode(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicPostcodeRequest) *lcos_error.LCOSError {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return err
	}
	if !exists {
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	return s.lineBasicServiceablePostcodeDAO.DeleteOriginPostcodeBasicServiceableModel(ctx, request.LineId, request.ID)
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) DeleteBasicServiceableDestPostcode(ctx utils.LCOSContext, request *basic_serviceable.DeleteBasicPostcodeRequest) *lcos_error.LCOSError {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return err
	}
	if !exists {
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	return s.lineBasicServiceablePostcodeDAO.DeleteDestPostcodeBasicServiceableModel(ctx, request.LineId, request.ID)
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) UploadBasicServiceableOriginPostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_origin_postcode.xlsx")
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_origin_postcode.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	addPostcodeModelsMap := make(map[string][]*model.LineOriginPostcodeBasicServiceableTab)
	deletePostcodeModelsMap := make(map[string][]*model.LineOriginPostcodeBasicServiceableTab)

	var mutex sync.Mutex
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		if err := serviceable_util.CheckPostcodeRowData(row, request.Region, lineNum); err != nil {
			return err
		}

		wg.Add(1)
		limitChannel <- struct{}{}
		go func(rowData []string) {
			rowLineId := rowData[0]
			exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, rowLineId)
			if e != nil {
				errList = append(errList, e)
			}
			if !exists {
				logger.LogErrorf("upload postcode failed, line_id not exist|lineId=%s, lineNum=%d", rowLineId, lineNum)
				errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
			}
			rowStruct := serviceable_util.ParsePostcodeRowData(row)
			postcodeModel := &model.LineOriginPostcodeBasicServiceableTab{
				LineId: rowStruct.LineId,
				/*	CollectServiceType: rowStruct.ServiceType,
					Region:             request.Region,
					Postcode:           rowStruct.Postcode,
					CanPickup:          &rowStruct.PickupDeliver,
					CanCodPickup:       &rowStruct.CodPickupDeliver,*/
			}
			mutex.Lock()
			if rowStruct.ActionCode != -1 {
				if addPostcodeModelsMap[rowStruct.LineId] == nil {
					addPostcodeModelsMap[rowStruct.LineId] = []*model.LineOriginPostcodeBasicServiceableTab{}
				}
				addPostcodeModelsMap[rowStruct.LineId] = append(addPostcodeModelsMap[rowStruct.LineId], postcodeModel)
			} else {
				if deletePostcodeModelsMap[rowStruct.LineId] == nil {
					deletePostcodeModelsMap[rowStruct.LineId] = []*model.LineOriginPostcodeBasicServiceableTab{}
				}
				deletePostcodeModelsMap[rowStruct.LineId] = append(deletePostcodeModelsMap[rowStruct.LineId], postcodeModel)
			}
			mutex.Unlock()

			<-limitChannel
			wg.Done()
		}(row)
	}

	wg.Wait()
	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}
	fc := func() *lcos_error.LCOSError {
		for lineId, addPostcodeModels := range addPostcodeModelsMap {
			if err := s.lineBasicServiceablePostcodeDAO.BatchDeleteOriginPostcodeBasicServiceableModel(ctx, lineId, addPostcodeModels); err != nil {
				return err
			}
			if _, err := s.lineBasicServiceablePostcodeDAO.BatchCreateOriginPostcodeBasicServiceableModel(ctx, lineId, addPostcodeModels); err != nil {
				return err
			}
		}

		for lineId, deletePostcodeModels := range deletePostcodeModelsMap {
			if err := s.lineBasicServiceablePostcodeDAO.BatchDeleteOriginPostcodeBasicServiceableModel(ctx, lineId, deletePostcodeModels); err != nil {
				return err
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}

// Deprecated
func (s *LineBasicServiceablePostcodeService) UploadBasicServiceableDestPostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_dest_postcode.xlsx")
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_basic_dest_postcode.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	addPostcodeModelsMap := make(map[string][]*model.LineDestinationPostcodeBasicServiceableTab)
	deletePostcodeModelsMap := make(map[string][]*model.LineDestinationPostcodeBasicServiceableTab)
	var mutex sync.Mutex
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		if err := serviceable_util.CheckPostcodeRowData(row, request.Region, lineNum); err != nil {
			return err
		}

		wg.Add(1)
		limitChannel <- struct{}{}
		go func(rowData []string) {
			rowLineId := rowData[0]
			exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, rowLineId)
			if e != nil {
				errList = append(errList, e)
			}
			if !exists {
				logger.LogErrorf("upload postcode failed, line_id not exist|lineId=%s, lineNum=%d", rowLineId, lineNum)
				errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
			}
			rowStruct := serviceable_util.ParsePostcodeRowData(rowData)
			postcodeModel := &model.LineDestinationPostcodeBasicServiceableTab{
				LineId: rowStruct.LineId,
				/*DeliverServiceType: rowStruct.ServiceType,
				Region:             request.Region,
				Postcode:           rowStruct.Postcode,
				CanDeliver:         &rowStruct.PickupDeliver,
				CanCodDeliver:      &rowStruct.CodPickupDeliver,*/
			}

			mutex.Lock()
			if rowStruct.ActionCode != -1 {
				if addPostcodeModelsMap[rowStruct.LineId] == nil {
					addPostcodeModelsMap[rowStruct.LineId] = []*model.LineDestinationPostcodeBasicServiceableTab{}
				}
				addPostcodeModelsMap[rowStruct.LineId] = append(addPostcodeModelsMap[rowStruct.LineId], postcodeModel)
			} else {
				if deletePostcodeModelsMap[rowStruct.LineId] == nil {
					deletePostcodeModelsMap[rowStruct.LineId] = []*model.LineDestinationPostcodeBasicServiceableTab{}
				}
				deletePostcodeModelsMap[rowStruct.LineId] = append(deletePostcodeModelsMap[rowStruct.LineId], postcodeModel)
			}
			mutex.Unlock()

			<-limitChannel
			wg.Done()
		}(row)
	}

	wg.Wait()
	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}
	fc := func() *lcos_error.LCOSError {
		for lineId, addPostcodeModels := range addPostcodeModelsMap {
			if err := s.lineBasicServiceablePostcodeDAO.BatchDeleteDestPostcodeBasicServiceableModel(ctx, lineId, addPostcodeModels); err != nil {
				return err
			}
			if _, err := s.lineBasicServiceablePostcodeDAO.BatchCreateDestPostcodeBasicServiceableModel(ctx, lineId, addPostcodeModels); err != nil {
				return err
			}
		}

		for lineId, deletePostcodeModels := range deletePostcodeModelsMap {
			if err := s.lineBasicServiceablePostcodeDAO.BatchDeleteDestPostcodeBasicServiceableModel(ctx, lineId, deletePostcodeModels); err != nil {
				return err
			}
		}

		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}

func (s *LineBasicServiceablePostcodeService) GetAllBasicServiceablePostcodeByLineAndGroup(ctx utils.LCOSContext, lineId, groupId string) ([]*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"line_id":                  lineId,
		"collect_deliver_group_id": groupId,
	}
	return s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceablePostcode(ctx, lineId, queryParams)
}

func (s *LineBasicServiceablePostcodeService) GetAllBasicServiceablePostcodeByLine(ctx utils.LCOSContext, lineId string) ([]*model.LineBasicServiceablePostcodeTab, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"line_id": lineId,
	}
	return s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceablePostcode(ctx, lineId, queryParams)
}

func fillPostcodeModel(postcodeModel *model.LineBasicServiceablePostcodeTab, request *basic_serviceable.CreateBasicPostcodeRequest) {
	postcodeModel.CollectDeliverGroupId = request.CollectDeliverGroupId
	postcodeModel.LineId = request.LineId
	postcodeModel.Region = request.Region
	postcodeModel.Postcode = request.Postcode
	postcodeModel.CanPickup = request.CanPickup
	postcodeModel.CanCodPickup = request.CanCodPickup
	postcodeModel.CanDeliver = request.CanDeliver
	postcodeModel.CanCodDeliver = request.CanCodDeliver
	if request.SupportTradeIn != nil {
		postcodeModel.SupportTradeIn = *request.SupportTradeIn
	}
}

// Deprecated
func fillOriginPostcodeModel(postcodeModel *model.LineOriginPostcodeBasicServiceableTab, request *basic_serviceable.CreateBasicOriginPostcodeRequest) {
	postcodeModel.CollectServiceType = request.CollectServiceType
	postcodeModel.Region = request.Region
	postcodeModel.Postcode = request.Postcode
	postcodeModel.CanPickup = request.CanPickup
	postcodeModel.CanCodPickup = request.CanCodPickup
}

// Deprecated
func fillDestPostcodeModel(postcodeModel *model.LineDestinationPostcodeBasicServiceableTab, request *basic_serviceable.CreateBasicDestPostcodeRequest) {
	postcodeModel.DeliverServiceType = request.DeliverServiceType
	postcodeModel.Region = request.Region
	postcodeModel.Postcode = request.Postcode
	postcodeModel.CanDeliver = request.CanDeliver
	postcodeModel.CanCodDeliver = request.CanCodDeliver
}

func (s *LineBasicServiceablePostcodeService) singleCheckSpxPostcodeServiceableArea(ctx utils.LCOSContext, region, lineId, groupId string, postcode string, slsAbility serviceable_util.ServiceableAreaAbility, dropoffOnly bool) *lcos_error.LCOSError {
	if spx_serviceable_area_utils.SkipValidation(region) {
		// 此region不支持或禁用spx服务范围校验，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to apollo config|region=%s", region)
		return nil
	}
	if !spx_serviceable_area_utils.IsPostcodeRegion(region) {
		// 此region在sls配置地址类型是postcode，在spx是location，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to location type mismatch|region=%s", region)
		return nil
	}

	// 1. 获取order account映射
	mappingList, err := s.orderAccountMappingService.GetOrderAccountMappingByLineAndGroup(ctx, region, lineId, groupId)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Check SPX serviceable area error, %s", err.Msg)
	}
	if len(mappingList) == 0 {
		// 此line无法找到order account映射，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to order account mapping not found|region=%s", region)
		return nil
	}

	// 2. 获取spx配置数据
	spxDataMap := make(map[int]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData)
	spxVersion, err := s.spxServiceableAreaService.GetActiveSpxServiceableAreaVersion(ctx, region)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Check SPX serviceable area error, %s", err.Msg)
	}
	if spxVersion != nil {
		spxDataList, err := s.spxServiceableAreaService.ListSpxServiceabelAreaPostcodeDataByPostcode(ctx, region, spxVersion.VersionId, postcode)
		if err != nil {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Check SPX serviceable area error, %s", err.Msg)
		}
		for _, spxData := range spxDataList {
			spxDataMap[spxData.OrderAccount] = spxData
		}
	}

	// 3. 校验服务范围
	logger.CtxLogInfof(ctx, "spx serviceable area validation start|line_id=%s, group_id=%s, postcode=%s", lineId, groupId, postcode)
	for _, mapping := range mappingList {
		spxAbility, ok := spxDataMap[mapping.OrderAccount]
		if !ok {
			spxAbility = &spx_serviceable_area_data.SpxServiceableAreaPostcodeData{}
		}
		unsupportedAbility := spx_serviceable_area_utils.CheckUnsupportedAbilityInSpx(spxAbility, slsAbility, dropoffOnly)
		if len(unsupportedAbility) != 0 {
			logger.CtxLogErrorf(ctx, "spx serviceable area validation failed|line_id=%s, group_id=%s, order_account=%d, postcode=%s", lineId, groupId, mapping.OrderAccount, postcode)
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "SPX doesn't support %s under account '%s'", strings.Join(unsupportedAbility, ","), mapping.OrderAccountName)
		}
		logger.CtxLogInfof(ctx, "spx serviceable area validation success|line_id=%s, group_id=%s, order_account=%d, postcode=%s", lineId, groupId, mapping.OrderAccount, postcode)
	}

	return nil
}

func (s *LineBasicServiceablePostcodeService) batchCheckSpxPostcodeServiceableArea(ctx utils.LCOSContext, region, lineId, groupId string, postcode string, slsAbility serviceable_util.ServiceableAreaAbility, dropoffOnly bool, orderAccountMapping map[string][]*order_account_mapping2.OrderAccountMapping, spxDataMap map[int]map[string]*spx_serviceable_area_data.SpxServiceableAreaPostcodeData) *lcos_error.LCOSError {
	if spx_serviceable_area_utils.SkipValidation(region) {
		// 此region不支持或禁用spx服务范围校验，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to apollo config|region=%s", region)
		return nil
	}
	if !spx_serviceable_area_utils.IsPostcodeRegion(region) {
		// 此region在sls配置地址类型是postcode，在spx是location，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to location type mismatch|region=%s", region)
		return nil
	}

	lineGroupKey := utils.GenKey(":", lineId, groupId)
	mappingList, ok := orderAccountMapping[lineGroupKey]
	if !ok || len(mappingList) == 0 {
		// 没有配置order account映射，默认校验通过
		logger.CtxLogInfof(ctx, "spx serviceable area validation skipped due to order account mapping not found|region=%s", region)
		return nil
	}

	logger.CtxLogInfof(ctx, "spx serviceable area validation start|line_id=%s, group_id=%s, postcode=%s", lineId, groupId, postcode)
	for _, mapping := range mappingList {
		spxAbility := &spx_serviceable_area_data.SpxServiceableAreaPostcodeData{} // spx默认揽派能力不支持
		if postcodeMap, ok := spxDataMap[mapping.OrderAccount]; ok {
			if postcodeModel, ok := postcodeMap[postcode]; ok {
				spxAbility = postcodeModel // 根据order account和location id能匹配到服务范围配置
			}
		}
		unsupportedAbility := spx_serviceable_area_utils.CheckUnsupportedAbilityInSpx(spxAbility, slsAbility, dropoffOnly)
		if len(unsupportedAbility) != 0 {
			logger.CtxLogErrorf(ctx, "spx serviceable area validation failed|line_id=%s, group_id=%s, order_account=%d, postcode=%s", lineId, groupId, mapping.OrderAccount, postcode)
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "SPX doesn't support %s under account '%s'", strings.Join(unsupportedAbility, ","), mapping.OrderAccountName)
		}
		logger.CtxLogInfof(ctx, "spx serviceable area validation success|line_id=%s, group_id=%s, order_account=%d, postcode=%s", lineId, groupId, mapping.OrderAccount, postcode)
	}
	return nil
}
