package collect_deliver_group

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
)

type CollectDeliverGroupServiceInterface interface {
	// 校验group是否冲突
	CheckGroupConflictByGroupIds(ctx utils.LCOSContext, groupIds []string) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError)
	// 通过LineId获取关联的group
	GetCollectDeliverGroupByLineId(ctx utils.LCOSContext, lineId string) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError)
	// 使用缓存获取lineId关联的group
	GetCollectDeliverGroupByLineIdUseCache(ctx utils.LCOSContext, lineId string) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError)
	// 获取所有collect deliver group列表
	GetAllCollectDeliverGroup(ctx utils.LCOSContext) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError)
	// 通过groupId获取所有collect deliver group
	GetAllCollectDeliverByGroupIds(ctx utils.LCOSContext, groupIds []string) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError)
}
type collectDeliverGroupService struct {
	lineBasicServiceableGroupRefDao collect_deliver_group_ref.LineBasicServiceableGroupRefDao
	lineCollectDeliverGroupConfDao  model.LineCollectDeliverGroupConfDao
}

func NewCollectDeliverGroupService(lineBasicServiceableGroupRefDao collect_deliver_group_ref.LineBasicServiceableGroupRefDao,
	ctDeliverGroupConfDao model.LineCollectDeliverGroupConfDao) *collectDeliverGroupService {
	return &collectDeliverGroupService{
		lineBasicServiceableGroupRefDao: lineBasicServiceableGroupRefDao,
		lineCollectDeliverGroupConfDao:  ctDeliverGroupConfDao,
	}
}

// 校验group是否冲突
func (collectDeliverGroupService *collectDeliverGroupService) CheckGroupConflictByGroupIds(ctx utils.LCOSContext, groupIds []string) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError) {
	groupConfList, err := collectDeliverGroupService.lineCollectDeliverGroupConfDao.GetConfByGroupIds(ctx, groupIds)
	if err != nil {
		return nil, err
	}
	if len(groupConfList) >= 2 { // 大于等于两个group才需要去校验冲突
		for i := 0; i < len(groupConfList)-1; i++ {
			for j := i + 1; j < len(groupConfList); j++ {
				bothPickupMode := groupConfList[i].PickupMode & groupConfList[j].PickupMode    // bothPickupMode == 0 表明pickup不重叠
				bothDeliverMode := groupConfList[i].DeliverMode & groupConfList[j].DeliverMode // bothDeliverMode == 0 表明deliver不重叠
				if bothPickupMode != 0 && bothDeliverMode != 0 {
					return groupConfList, lcos_error.NewLCOSError(lcos_error.CollectDeliverGroupConflictErrorCode,
						fmt.Sprintf("collect deliver groups=[%v, %v] conflict", groupConfList[i].GroupId, groupConfList[j].GroupId))
				}
			}
		}
	}
	return groupConfList, nil
}

// 通过LineId获取关联的group
func (collectDeliverGroupService *collectDeliverGroupService) GetCollectDeliverGroupByLineId(ctx utils.LCOSContext, lineId string) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError) {
	groupIdList, err := collectDeliverGroupService.lineBasicServiceableGroupRefDao.GetGroupIdListByLineId(ctx, lineId)
	if err != nil {
		return nil, nil
	}
	return collectDeliverGroupService.lineCollectDeliverGroupConfDao.GetConfByGroupIds(ctx, groupIdList)
}

// 使用缓存获取lineId关联的group
func (collectDeliverGroupService *collectDeliverGroupService) GetCollectDeliverGroupByLineIdUseCache(ctx utils.LCOSContext, lineId string) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError) {
	groupIdList, err := collectDeliverGroupService.lineBasicServiceableGroupRefDao.GetGroupIdListByLineIdUseCache(ctx, lineId)
	if err != nil {
		return nil, nil
	}
	return collectDeliverGroupService.lineCollectDeliverGroupConfDao.BatchGetConfByGroupIdsUseCache(ctx, groupIdList)
}

// 获取所有collect deliver group列表
func (collectDeliverGroupService *collectDeliverGroupService) GetAllCollectDeliverGroup(ctx utils.LCOSContext) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError) {
	return collectDeliverGroupService.lineCollectDeliverGroupConfDao.GetAllConf(ctx)
}

// 通过groupId获取所有collect deliver group
func (collectDeliverGroupService *collectDeliverGroupService) GetAllCollectDeliverByGroupIds(ctx utils.LCOSContext, groupIds []string) ([]*model.LineCollectDeliverGroupConfTab, *lcos_error.LCOSError) {
	searchData := make(map[string]interface{})
	searchData["group_id"] = groupIds
	var models []*model.LineCollectDeliverGroupConfTab
	return models, common.SearchAllData(ctx, &model.LineCollectDeliverGroupConfTab{}, &models, searchData)
}
