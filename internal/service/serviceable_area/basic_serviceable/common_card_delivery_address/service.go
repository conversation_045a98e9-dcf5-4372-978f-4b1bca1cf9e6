package common_card_delivery_address

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/redislib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/array_utils"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/csv"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/card_delivery_address_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	model1 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"os"
	"strconv"
	"strings"
	"time"
)

type CommonCardDeliveryAddressService interface {
	UploadCardDeliveryAddress(ctx utils.LCOSContext, req *card_delivery_address_protocol.GetCardDeliveryAddressDownloadFilePathRequest) (string, string, *lcos_error.LCOSError)
}

func NewCommonCardDeliveryAddressService(dao card_delivery_address.CardDeliveryAddressChangeVersionDao, basicLocationService basic_location.LineBasicServiceableLocationServiceInterface, basicPostcode basic_postcode.LineBasicServiceablePostcodeServiceInterface, basicConfDao basic_conf.LineBasicServiceableConfDAO) *commonCardDeliveryAddressService {
	return &commonCardDeliveryAddressService{
		dao:                  dao,
		basicLocationService: basicLocationService,
		basicPostcodeService: basicPostcode,
		basicConfDao:         basicConfDao,
	}
}

type commonCardDeliveryAddressService struct {
	dao                  card_delivery_address.CardDeliveryAddressChangeVersionDao
	basicLocationService basic_location.LineBasicServiceableLocationServiceInterface
	basicPostcodeService basic_postcode.LineBasicServiceablePostcodeServiceInterface
	basicConfDao         basic_conf.LineBasicServiceableConfDAO
}

func (c *commonCardDeliveryAddressService) UploadCardDeliveryAddress(ctx utils.LCOSContext, req *card_delivery_address_protocol.GetCardDeliveryAddressDownloadFilePathRequest) (string, string, *lcos_error.LCOSError) {
	// 设置一定时间内只能调用一次
	now := recorder.Now(ctx).UnixNano()
	rateLimitKey := "UploadCardDeliveryAddress"
	redisValue, err := redislib.Get(ctx, rateLimitKey)
	if err == nil {
		return "", "", lcos_error.NewLCOSError(lcos_error.SARequestRateLimitErrorCode, fmt.Sprintf("already called at %v", string(redisValue)))
	}

	if config.GetMutableConf(ctx).CardDeliveryAddressSyncConfig.Disable {
		return "", "", lcos_error.NewLCOSError(lcos_error.FunctionUnSupported, "lcos did not support this function")
	}

	if array_utils.CheckIfDuplicate(req.LineIdList) {
		return "", "", lcos_error.NewLCOSError(lcos_error.ParamsError, "duplicate line id")
	}

	basicConfList, sysErr := c.basicConfDao.GetBasicServiceableConfModelByLineIds(ctx, req.LineIdList)
	if sysErr != nil {
		return "", "", sysErr
	}

	if len(basicConfList) == 0 {
		return "", "", lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("basic conf not found line id[%s]", req.LineIdList))
	}

	cardDeliveryAddressDataList, sysErr := c.getCardDeliveryAddressData(ctx, req.Region, basicConfList)
	if sysErr != nil {
		return "", "", sysErr
	}

	region := strings.ToUpper(req.Region)

	// 将需要推送给seabank的服务范围地址数据写入到csv文件
	filePath := fmt.Sprintf("/tmp/%s-%d.csv", region, now)
	file, err := os.Create(filePath)
	if err != nil {
		return "", "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "create csv file error: %s", err.Error())
	}
	defer file.Close()
	if err = csv.WriteTitleAndStruct(file, titles, cardDeliveryAddressDataList); err != nil {
		return "", "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "write csv file error: %s", err.Error())
	}

	hashCode, err := excel.ExcelFileHash(filePath)
	if err != nil {
		return "", "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "get csv file hash code error: %s", err.Error())
	}

	// 将写入的csv本地文件上传到s3集群
	cfg := config.GetConf(ctx).LCOSS3Config
	s3FilePath, sysErr := s3_service.NewS3Service().UploadFile(ctx, cfg.AccessKeyID, cfg.BucketKey, filePath, cfg.TimeOut, "card-delivery-address")
	if sysErr != nil {
		return "", "", sysErr
	}

	timeout := c.getQueryFrequencyTimeout(ctx)
	rateLimitValue := strconv.Itoa(int(now))
	err = redislib.Set(ctx, rateLimitKey, rateLimitValue, time.Duration(timeout)*time.Second)
	if err != nil {
		return "", "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("key[%s] value[%s], error:%s", rateLimitKey, rateLimitValue, err.Error()))
	}
	return s3FilePath, hashCode, nil
}

func (c *commonCardDeliveryAddressService) getCardDeliveryAddressData(ctx utils.LCOSContext, region string, basicConfList []*basic_conf.LineBasicServiceableConfTab) ([]interface{}, *lcos_error.LCOSError) {
	dataList := make([]interface{}, 0)
	for _, basicConf := range basicConfList {
		var cardDeliveryAddressList []interface{}
		lineId := basicConf.LineId
		// 目前只支持location和postcode, 并且postcode和location不会同时存在（pm已确认）
		if basicConf.OriginServiceableType == constant.LOCATION {
			locationDataList, sysErr := c.basicLocationService.GetAllBasicServiceableLocationByLine(ctx, lineId)
			if sysErr != nil {
				logger.CtxLogErrorf(ctx, "GetAllBasicServiceableLocationByLine failed, err:%s", sysErr.Msg)
				return nil, sysErr
			}

			cardDeliveryAddressList = c.generateCardDeliveryAddressByLocation(region, locationDataList)

		} else if basicConf.OriginServiceableType == constant.POSTCODE {
			postcodeDataList, sysErr := c.basicPostcodeService.GetAllBasicServiceablePostcodeByLine(ctx, lineId)
			if sysErr != nil {
				logger.CtxLogErrorf(ctx, "GetAllBasicServiceableLocationByLine failed, err:%s", sysErr.Msg)
				return nil, sysErr
			}

			cardDeliveryAddressList = c.generateCardDeliveryAddressByPostcode(region, postcodeDataList)

		} else {
			errMsg := fmt.Sprintf("only support location or postcode, lineId[%s] OriginServiceableType[%d]", lineId, basicConf.OriginServiceableType)
			logger.CtxLogErrorf(ctx, errMsg)
			return nil, lcos_error.NewLCOSError(lcos_error.ParamsError, errMsg)

		}
		if len(cardDeliveryAddressList) == 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("no avaliable sa, region[%s], line id[%s]", region, lineId))
		}

		cardDeliveryAddress := cardDeliveryAddressList[0].(*CardDeliveryAddressDataV2)
		if !strings.EqualFold(strings.ToUpper(cardDeliveryAddress.Region), strings.ToUpper(region)) {
			return nil, lcos_error.NewLCOSError(lcos_error.ParamsError, fmt.Sprintf("region not match, region[%s], line id[%s], line region[%s]", region, lineId, cardDeliveryAddress.Region))
		}

		dataList = append(dataList, cardDeliveryAddressList...)

	}
	return dataList, nil
}

func (c *commonCardDeliveryAddressService) generateCardDeliveryAddressByLocation(region string, locationList []*model.LineBasicServiceableLocationTab) []interface{} {
	dataList := make([]interface{}, 0)
	for _, location := range locationList {
		address := &CardDeliveryAddressDataV2{
			Region:            region,
			State:             location.State,
			City:              location.City,
			District:          location.District,
			Street:            location.Street,
			SupportDeliver:    location.GetCanDeliver(),
			SupportCodDeliver: location.GetCanCodDeliver(),
			Postcode:          "",
		}
		dataList = append(dataList, address)
	}

	return dataList
}

func (c *commonCardDeliveryAddressService) generateCardDeliveryAddressByPostcode(region string, postcodeList []*model1.LineBasicServiceablePostcodeTab) []interface{} {
	// 目前只支持location和postcode, 并且postcode和location不会同时存在（pm已确认）
	dataList := make([]interface{}, 0)
	for _, postcode := range postcodeList {
		address := &CardDeliveryAddressDataV2{
			Region:            region,
			State:             "",
			City:              "",
			District:          "",
			Street:            "",
			SupportDeliver:    postcode.GetCanDeliver(),
			SupportCodDeliver: postcode.GetCanCodDeliver(),
			Postcode:          postcode.Postcode,
		}
		dataList = append(dataList, address)
	}
	return dataList
}

func (c *commonCardDeliveryAddressService) getQueryFrequencyTimeout(ctx utils.LCOSContext) int64 {
	timeout := config.GetMutableConf(ctx).CardDeliveryAddressSyncConfig.QueryFrequency
	if timeout <= 0 {
		timeout = 600 // 十分钟
	}
	return timeout
}
