package basic_cep_range

import (
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/ceprange_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"github.com/tealeg/xlsx"
	"os"
	"sort"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/basic_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/jinzhu/copier"
)

type LineBasicServiceableCepRangeServiceInterface interface {
	// 创建基础层rep range
	CreateBasicServiceableCepRange(ctx utils.LCOSContext, request *basic_serviceable.BasicCepRangeCreateRequest) (*model.LineBasicServiceableCepRangeTab, *lcos_error.LCOSError)
	// 更新基础层CepRange
	UpdateBasicServiceableCepRange(ctx utils.LCOSContext, request *basic_serviceable.BasicCepRangeUpdateRequest) (*model.LineBasicServiceableCepRangeTab, *lcos_error.LCOSError)
	// 获取基础层CepRange 分页列表
	ListBasicServiceableCepRange(ctx utils.LCOSContext, request *basic_serviceable.BasicCepRangeListRequest) (*common.PageModel, *lcos_error.LCOSError)
	// 获取所有的基础层CepRange列表
	GetAllBasicServiceableCepRangeList(ctx utils.LCOSContext, request map[string]interface{}) ([]*model.LineBasicServiceableCepRangeTab, *lcos_error.LCOSError)
	// 删除基础层CepRange
	DeleteBasicServiceableCepRange(ctx utils.LCOSContext, request map[string]interface{}) *lcos_error.LCOSError
	// 上传基础层CepRange
	UploadBasicServiceableCepRange(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	ParseAndImportBasicCepRangeSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError)
}

type LineBasicServiceableCepRangeService struct {
	lineBasicServiceableCepRangeDAO model.LineServiceableCepRangeDaoInterface
	scheduledService                scheduled.ScheduledService
}

func NewLineBasicServiceableCepRangeService(lineBasicServiceableCepRangeDAO model.LineServiceableCepRangeDaoInterface, scheduledService scheduled.ScheduledService) *LineBasicServiceableCepRangeService {
	return &LineBasicServiceableCepRangeService{
		lineBasicServiceableCepRangeDAO: lineBasicServiceableCepRangeDAO,
		scheduledService:                scheduledService,
	}
}

// checkSingleCepRangeLineOverlap  cepRangeServiceableList需要排序，使用二分法查找是否存在重叠的服务范围
func (l *LineBasicServiceableCepRangeService) checkSingleCepRangeLineOverlap(ctx utils.LCOSContext, singleCepRangeServiceable *model.LineBasicServiceableCepRangeTab, cepRangeServiceableList []*model.LineBasicServiceableCepRangeTab) (bool, int) {
	startIndex := 0
	endIndex := len(cepRangeServiceableList) - 1
	for startIndex <= endIndex {
		mid := (startIndex + endIndex) / 2
		// 有重叠
		if cepRangeServiceableList[mid].Initial <= singleCepRangeServiceable.Final && singleCepRangeServiceable.Initial <= cepRangeServiceableList[mid].Final {
			return true, mid
		} else {
			if singleCepRangeServiceable.Final < cepRangeServiceableList[mid].Initial {
				endIndex = mid - 1
			} else {
				startIndex = mid + 1
			}
		}
	}
	return false, -1
}

// checkCepRangeLineOverlap oldCepRangeMap   line_id->cep range->cep range Serviceables
func (l *LineBasicServiceableCepRangeService) checkCepRangeLineOverlap(ctx utils.LCOSContext, lineID string, cepRangeServiceables []*model.LineBasicServiceableCepRangeTab, oldCepRangeMap map[string]map[string][]*model.LineBasicServiceableCepRangeTab) *lcos_error.LCOSError {
	// SPLN-19677 需要校验线服务范围不重叠
	// 获取跟当前line位于一个组的group
	notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, lineID)
	if len(notAllowedOverlapList) != 0 {
		for _, notOverlapLine := range notAllowedOverlapList {
			if lineID != notOverlapLine {
				if oldServiceableLocationsMap, ok1 := oldCepRangeMap[notOverlapLine]; ok1 {
					for _, newCepRange := range cepRangeServiceables {
						// 有重合的location
						if item2, ok2 := oldServiceableLocationsMap[newCepRange.CollectDeliverGroupID]; ok2 {
							if overlapped, idx := l.checkSingleCepRangeLineOverlap(ctx, newCepRange, item2); overlapped {
								errMsg := fmt.Sprintf("line group serviceable overlap and cannot merge|new_line:[%s], cep_range_initial:[%d],cep_range_final:[%d] | old_line:[%s], cep_range_initial:[%d],cep_range_final:[%d] | group_id:[%s]", newCepRange.LineID, newCepRange.Initial, newCepRange.Final, item2[idx].LineID, item2[idx].Initial, item2[idx].Final, item2[idx].CollectDeliverGroupID)
								logger.CtxLogErrorf(ctx, errMsg)
								return lcos_error.NewLCOSError(lcos_error.LineServiceAbleOverlap, errMsg)
							}
						}
					}
				}
			}
		}
	}
	return nil
}

func (l *LineBasicServiceableCepRangeService) CreateBasicServiceableCepRange(ctx utils.LCOSContext, request *basic_serviceable.BasicCepRangeCreateRequest) (*model.LineBasicServiceableCepRangeTab, *lcos_error.LCOSError) {
	var isAutoMergeOrSplitFlag bool

	// 1. 校验line是否存在
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	if !exists {
		logger.LogErrorf("create basic conf failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}

	// 2. 重叠性校验：校验同一line+group维度下，新增服务范围是否和已有服务范围重叠，如果重叠则尝试进行区间合并，如果不能合并则报错
	// 2.1 从DB中获取相同line+group的全量配置数据
	basicCepRangeList, lcosError := l.lineBasicServiceableCepRangeDAO.GetAllLineBasicServiceableCepRangeModel(ctx, map[string]interface{}{"line_id": request.LineID, "collect_deliver_group_id": request.CollectDeliverGroupID})
	if lcosError != nil {
		return nil, lcosError
	}
	// 2.2 将请求参数转换为线cep range服务范围数据模型
	newCepRange := new(model.LineBasicServiceableCepRangeTab)
	if copierErr := copier.Copy(&newCepRange, request); copierErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("copier error: %v", copierErr))
	}
	// 2.3 判断新增服务范围是否与已有服务范围重叠
	var overlapSet ceprange_util.CepRangeSet                 // 用于进行区间合并
	var overlapList []*model.LineBasicServiceableCepRangeTab // 区间合并前的已有服务范围配置，合并后需要删除
	for _, basicCepRange := range basicCepRangeList {
		if utils.CheckRangeCoincidence(basicCepRange.Initial, basicCepRange.Final, request.Initial, request.Final) {
			// 新建服务范围与DB中已有数据有重叠
			if !serviceable_util.ServiceableAreaAbilityEqual(basicCepRange, newCepRange) || basicCepRange.GetCanTradeIn() != newCepRange.GetCanTradeIn() {
				// 服务范围重叠但揽派能力不一致，那么无法合并，报错overlap
				return nil, lcos_error.NewLCOSError(lcos_error.CepRangeDuplicate, "CEP Range is overlapped with existing ones and cannot merge, please edit and retry.")
			}
			overlapSet.Add(basicCepRange)
			overlapList = append(overlapList, basicCepRange)
		}
	}
	// 2.4 执行区间合并，获取最终需要新增的区间列表
	var actuallyAddList []*model.LineBasicServiceableCepRangeTab // 区间合并后实际需要新增的区间列表，合并前的存量重叠数据会被删除
	if len(overlapList) > 0 {
		// 有重叠区间，需要进行区间合并
		overlapSet.Add(newCepRange)               // 已有服务范围+新增服务范围
		merged := ceprange_util.Merge(overlapSet) // 区间合并
		merged.Range(func(item *ceprange_util.CepRangeItem) {
			actuallyAddList = append(actuallyAddList, &model.LineBasicServiceableCepRangeTab{
				LineID:                newCepRange.LineID,
				Region:                newCepRange.Region,
				CollectDeliverGroupID: newCepRange.CollectDeliverGroupID,
				Initial:               item.GetCepStart(),
				Final:                 item.GetCepEnd(),
				CanPickup:             newCepRange.CanPickup,
				CanCodPickup:          newCepRange.CanCodPickup,
				CanDeliver:            newCepRange.CanDeliver,
				CanCodDeliver:         newCepRange.CanCodDeliver,
				SupportTradeIn:        newCepRange.SupportTradeIn,
			})
		})
		isAutoMergeOrSplitFlag = true
	} else {
		// 无重叠区间
		actuallyAddList = append(actuallyAddList, newCepRange)
	}

	// 3. 互斥组重叠性校验：属于同一互斥组中的不同线服务范围不能重叠，如果重叠则直接报错
	// SPLN-19677 服务范围重叠校验
	notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, request.LineID)
	// lineID->groupID->cepRangeServiceable
	oldServiceableCepRangeMap := map[string]map[string][]*model.LineBasicServiceableCepRangeTab{}
	for _, singleLine := range notAllowedOverlapList {
		tmpbasicCepRangeList, lcosError := l.lineBasicServiceableCepRangeDAO.GetAllLineBasicServiceableCepRangeModel(ctx, map[string]interface{}{"line_id": singleLine, "collect_deliver_group_id": request.CollectDeliverGroupID})
		if lcosError != nil {
			return nil, lcosError
		}
		sort.Slice(tmpbasicCepRangeList, func(i, j int) bool {
			return tmpbasicCepRangeList[i].Initial <= tmpbasicCepRangeList[j].Initial
		})
		for _, serviceableCepRange := range tmpbasicCepRangeList {
			if _, ok := oldServiceableCepRangeMap[singleLine]; !ok {
				oldServiceableCepRangeMap[singleLine] = map[string][]*model.LineBasicServiceableCepRangeTab{}
			}
			oldServiceableCepRangeMap[singleLine][serviceableCepRange.CollectDeliverGroupID] = append(oldServiceableCepRangeMap[singleLine][serviceableCepRange.CollectDeliverGroupID], serviceableCepRange)
		}
	}
	lcosErr := l.checkCepRangeLineOverlap(ctx, newCepRange.LineID, []*model.LineBasicServiceableCepRangeTab{newCepRange}, oldServiceableCepRangeMap)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 4. 数据落DB
	fn := func() *lcos_error.LCOSError {
		if lcosErr = l.lineBasicServiceableCepRangeDAO.BatchDeleteLineBasicServiceableCepRangeModel(ctx, overlapList, constant.DBMAXBATCHDELNUM); lcosErr != nil {
			return lcosErr
		}
		if lcosErr = l.lineBasicServiceableCepRangeDAO.BatchCreateLineBasicServiceableCepRangeModel(ctx, actuallyAddList); lcosErr != nil {
			return lcosErr
		}
		return nil
	}
	if lcosErr = ctx.Transaction(fn); lcosErr != nil {
		return nil, lcosErr
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineBasicCepRangeTabNamespace)

	if isAutoMergeOrSplitFlag {
		alertLineBasicCepRangeAutoMergeOrSplit(ctx, overlapList, actuallyAddList, "Single Create")
	}
	return actuallyAddList[0], nil
}

func (l *LineBasicServiceableCepRangeService) UpdateBasicServiceableCepRange(ctx utils.LCOSContext, request *basic_serviceable.BasicCepRangeUpdateRequest) (*model.LineBasicServiceableCepRangeTab, *lcos_error.LCOSError) {
	// 校验line是否存在
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	if !exists {
		logger.LogErrorf("update basic conf failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "update failed, line_id not found")
	}
	// 判断cep range 是否有重叠
	basicCepRangeList, lcosError := l.lineBasicServiceableCepRangeDAO.GetAllLineBasicServiceableCepRangeModel(ctx, map[string]interface{}{"line_id": request.LineID, "collect_deliver_group_id": request.CollectDeliverGroupID})
	if lcosError != nil {
		return nil, lcosError
	}
	for _, basicCepRange := range basicCepRangeList {
		if basicCepRange.ID == request.ID {
			continue
		}
		if utils.CheckRangeCoincidence(basicCepRange.Initial, basicCepRange.Final, request.Initial, request.Final) {
			return nil, lcos_error.NewLCOSError(lcos_error.CepRangeDuplicate, "CEP Range is overlapped with existing ones, please edit and retry.")
		}
	}
	updateMap, _ := utils.Struct2map(request)
	lcosErr := l.lineBasicServiceableCepRangeDAO.UpdateLineBasicServiceableCepRangeModel(ctx, updateMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineBasicCepRangeTabNamespace)
	return nil, nil
}

func (l *LineBasicServiceableCepRangeService) ListBasicServiceableCepRange(ctx utils.LCOSContext, request *basic_serviceable.BasicCepRangeListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	paramMap, _ := utils.Struct2map(request)
	if request.CepCode != nil {
		paramMap["initial <="] = request.CepCode
		paramMap["final >="] = request.CepCode
	}
	list, total, lcosError := l.lineBasicServiceableCepRangeDAO.SearchLineBasicServiceableCepRangeModel(ctx, pageNo, count, paramMap)
	if lcosError != nil {
		return nil, lcosError
	}
	return &common.PageModel{PageNO: pageNo, Count: count, Total: total, List: list}, nil
}

func (l *LineBasicServiceableCepRangeService) GetAllBasicServiceableCepRangeList(ctx utils.LCOSContext, request map[string]interface{}) ([]*model.LineBasicServiceableCepRangeTab, *lcos_error.LCOSError) {
	return l.lineBasicServiceableCepRangeDAO.GetAllLineBasicServiceableCepRangeModel(ctx, request)
}

func (l *LineBasicServiceableCepRangeService) DeleteBasicServiceableCepRange(ctx utils.LCOSContext, request map[string]interface{}) *lcos_error.LCOSError {
	lcosErr := l.lineBasicServiceableCepRangeDAO.DeleteBasicServiceableCepRangeModel(ctx, request)
	if lcosErr != nil {
		return lcosErr
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineBasicCepRangeTabNamespace)
	return nil
}

func (l *LineBasicServiceableCepRangeService) UploadBasicServiceableCepRange(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	job, err := l.scheduledService.CreateScheduledJob(ctx, request.Region, scheduled_constant.ThreePLServiceableArea, scheduled_constant.CEPRange, request.FileUrl, request.IsScheduled, request.ScheduledTime, ctx.GetUserEmail())
	if err != nil {
		return err
	}

	go func() {
		parseFileResult, _ := l.ParseAndImportBasicCepRangeSA(ctx, request, serviceable_util.LineBasicCepRangeHeader, true)
		if err := l.scheduledService.UpdateValidateResult(ctx, job, parseFileResult); err != nil {
			logger.CtxLogErrorf(ctx, "scheduled job update validate result error|job_id=%d, cause=%s", job.ID, err.Msg)
		}
	}()

	return nil
}

func (l *LineBasicServiceableCepRangeService) ParseAndImportBasicCepRangeSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}
	var isAutoMergeOrSplitFlag bool

	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete, time usage: %s\n", time.Since(beginTime).String())
	}()

	// 1. 下载文件并解析数据
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return result, nil
	}
	lineIdList, addLineList, deleteLineList, addCepRangeList, deleteCepRangeList := checkAndParseFile(ctx, request.Region, filePath, header, &result)
	if result.ParseFileErr != nil {
		return result, nil
	}

	// 2. 校验上传的新增数据是否自重叠，如果重叠则尝试合并，不能合并则报错
	addCepRangeMap := make(map[string][]*model.LineBasicServiceableCepRangeTab)
	for _, addCepRange := range addCepRangeList {
		key := serviceable_util.GetLineServiceableGroupKey(addCepRange)
		addCepRangeMap[key] = append(addCepRangeMap[key], addCepRange)
	}
	for key, lineAddCepRangeList := range addCepRangeMap {
		sort.SliceStable(lineAddCepRangeList, func(i, j int) bool {
			return lineAddCepRangeList[i].GetCepStart() < lineAddCepRangeList[j].GetCepStart()
		})

		mergedList := make([]*model.LineBasicServiceableCepRangeTab, 0, len(lineAddCepRangeList))
		for _, cepRange := range lineAddCepRangeList {
			if len(mergedList) == 0 {
				mergedList = append(mergedList, cepRange)
				continue
			}

			lastMergedCepRange := mergedList[len(mergedList)-1]
			if ceprange_util.Overlap(lastMergedCepRange, cepRange) {
				if !serviceable_util.ServiceableAreaAbilityEqual(lastMergedCepRange, cepRange) || lastMergedCepRange.GetCanTradeIn() != cepRange.GetCanTradeIn() {
					result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.CepRangeDuplicate, fmt.Sprintf("lineID:%s,CollectDeliverGroupID:%s,CEP Range[%d-%d] overlap with others and cannot merge, please edit and retry", cepRange.LineID, cepRange.CollectDeliverGroupID, cepRange.Initial, cepRange.Final))
					return result, nil
				}
				lastMergedCepRange.Final = utils.MaxUInt64(lastMergedCepRange.GetCepEnd(), cepRange.GetCepEnd())
				isAutoMergeOrSplitFlag = true
				continue
			}
			mergedList = append(mergedList, cepRange)
		}
		addCepRangeMap[key] = mergedList
	}

	// 3. 校验新增区间是否和已有区间重叠，如果重叠则尝试合并，不能合并则报错
	// 3.1 查询可能和新增区间重叠的所有已有区间并按照line+group维度进行分组
	existCepRangeList, lcosErr := l.lineBasicServiceableCepRangeDAO.GetAllLineBasicServiceableCepRangeModel(ctx, map[string]interface{}{"line_id in": addLineList})
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return result, nil
	}
	var existCepRangeMap = make(map[string][]*model.LineBasicServiceableCepRangeTab)
	for _, existCepRange := range existCepRangeList {
		key := serviceable_util.GetLineServiceableGroupKey(existCepRange)
		existCepRangeMap[key] = append(existCepRangeMap[key], existCepRange)
	}
	// 3.2 检查新增数据是否和DB数据是否重叠，如果重叠则尝试进行区间合并，获取得到最终需要从DB删除和新增的数据
	var mayAddCepRangeList []*model.LineBasicServiceableCepRangeTab         // 可能需要新增的区间，后续还要进行区间做差得到真正需要新增的区间
	var actuallyDeleteCepRangeList []*model.LineBasicServiceableCepRangeTab // 实际需要删除的区间
	actuallyDeleteCepRangeMap := make(map[string]struct{})
	for key, lineAddCepRangeList := range addCepRangeMap {
		lineExistCepRangeList, ok := existCepRangeMap[key]
		if !ok {
			// 新增数据在DB中不存在相同line+group的数据，不会overlap，可以直接新增
			mayAddCepRangeList = append(mayAddCepRangeList, lineAddCepRangeList...)
			continue
		}

		// 检查DB数据是否和新增数据有重叠以及是否可以合并
		var unmerged []*model.LineBasicServiceableCepRangeTab
		for _, existCepRange := range lineExistCepRangeList {
			// 检查DB数据和新增数据是否有重叠，并且是否能完全合并。有一个重叠区间不能合并则报错
			var hasOverlap bool
			for _, addCepRange := range lineAddCepRangeList {
				if ceprange_util.Overlap(existCepRange, addCepRange) {
					if !serviceable_util.ServiceableAreaAbilityEqual(existCepRange, addCepRange) || existCepRange.GetCanTradeIn() != addCepRange.GetCanTradeIn() {
						result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "added cep range overlap with db and connot merge|import cep range is line_id=%s, group_id=%s, cep_range_initial=%d, cep_range_final=%d", addCepRange.LineID, addCepRange.CollectDeliverGroupID, addCepRange.Initial, addCepRange.Final)
						return result, nil
					}
					hasOverlap = true
				}
			}
			if hasOverlap {
				// DB数据跟新增数据有重叠，并且可以完全合并
				actuallyDeleteCepRangeList = append(actuallyDeleteCepRangeList, existCepRange)
				actuallyDeleteCepRangeMap[serviceable_util.GetLineCepRangeServiceableUniqueKey(existCepRange)] = struct{}{}
				unmerged = append(unmerged, &model.LineBasicServiceableCepRangeTab{
					LineID:                existCepRange.LineID,
					Region:                existCepRange.Region,
					CollectDeliverGroupID: existCepRange.CollectDeliverGroupID,
					Initial:               existCepRange.GetCepStart(),
					Final:                 existCepRange.GetCepEnd(),
					CanPickup:             existCepRange.CanPickup,
					CanCodPickup:          existCepRange.CanCodPickup,
					CanDeliver:            existCepRange.CanDeliver,
					CanCodDeliver:         existCepRange.CanCodDeliver,
					SupportTradeIn:        existCepRange.SupportTradeIn,
				})
			}
		}

		if len(unmerged) == 0 {
			// 新增数据跟DB数据完全不重叠，可以直接新增
			mayAddCepRangeList = append(mayAddCepRangeList, lineAddCepRangeList...)
			continue
		}

		// 新增数据与DB数据进行合并
		unmerged = append(unmerged, lineAddCepRangeList...)
		sort.SliceStable(unmerged, func(i, j int) bool {
			return unmerged[i].GetCepStart() < unmerged[j].GetCepStart()
		})
		merged := make([]*model.LineBasicServiceableCepRangeTab, 0, len(unmerged))
		for _, unmergeCepRange := range unmerged {
			if len(merged) == 0 {
				merged = append(merged, unmergeCepRange)
				continue
			}

			lastMergedCepRange := merged[len(merged)-1]
			if ceprange_util.Overlap(lastMergedCepRange, unmergeCepRange) {
				lastMergedCepRange.Final = utils.MaxUInt64(lastMergedCepRange.GetCepEnd(), unmergeCepRange.GetCepEnd())
				isAutoMergeOrSplitFlag = true
				continue
			}
			merged = append(merged, unmergeCepRange)
		}
		mayAddCepRangeList = append(mayAddCepRangeList, merged...)
	}

	// 4. 根据上传的删除区间对原有区间+新增区间进行删除与拆分
	// 4.1 按照line+group维度对删除区间进行分组
	deleteCepRangeMap := make(map[string][]*model.LineBasicServiceableCepRangeTab)
	for _, deleteCepRange := range deleteCepRangeList {
		key := serviceable_util.GetLineServiceableGroupKey(deleteCepRange)
		deleteCepRangeMap[key] = append(deleteCepRangeMap[key], deleteCepRange)
	}
	// 4.2 获取所有可能需要删除的已有区间
	maybeDeleteCepRangeList, lcosErr := l.lineBasicServiceableCepRangeDAO.GetAllLineBasicServiceableCepRangeModel(ctx, map[string]interface{}{"line_id in": deleteLineList})
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return result, nil
	}
	// 4.3 对已有区间进行删除或拆分
	var actuallyAddCepRangeList []*model.LineBasicServiceableCepRangeTab // 实际需要新增的区间
	for _, maybeDeleteCepRange := range maybeDeleteCepRangeList {
		if _, ok := actuallyDeleteCepRangeMap[serviceable_util.GetLineCepRangeServiceableUniqueKey(maybeDeleteCepRange)]; ok {
			// 此区间在之前的流程中已经明确删除，可以忽略掉
			continue
		}

		key := serviceable_util.GetLineServiceableGroupKey(maybeDeleteCepRange)
		var deleteSet ceprange_util.CepRangeSet
		for _, deleteCepRange := range deleteCepRangeMap[key] {
			// 如果删除区间与原区间有重叠，则添加到delete集合里
			if ceprange_util.Overlap(maybeDeleteCepRange, deleteCepRange) {
				deleteSet.Add(deleteCepRange)
			}
		}
		if deleteSet.Len() != 0 {
			// 如果delete集合不为空，则进行区间作差。此时：
			// 1. 需要删除原区间
			// 2. 作差后如果区间不为空，则需要将拆分后的区间添加到新增区间列表

			// 将原区间添加到实际删除列表
			actuallyDeleteCepRangeList = append(actuallyDeleteCepRangeList, maybeDeleteCepRange)
			actuallyDeleteCepRangeMap[serviceable_util.GetLineCepRangeServiceableUniqueKey(maybeDeleteCepRange)] = struct{}{}

			// 区间作差，原区间 - 所有有重叠的删除区间
			var originSet ceprange_util.CepRangeSet
			originSet.Add(maybeDeleteCepRange)
			resultSet := ceprange_util.Subtract(originSet, deleteSet)

			// 如果resultSet不为空，说明此区间会被拆分为多段，将拆分后的区间添加到新增列表
			resultSet.Range(func(item *ceprange_util.CepRangeItem) {
				actuallyAddCepRangeList = append(actuallyAddCepRangeList, &model.LineBasicServiceableCepRangeTab{
					LineID:                maybeDeleteCepRange.LineID,
					Region:                maybeDeleteCepRange.Region,
					CollectDeliverGroupID: maybeDeleteCepRange.CollectDeliverGroupID,
					Initial:               item.GetCepStart(),
					Final:                 item.GetCepEnd(),
					CanPickup:             maybeDeleteCepRange.CanPickup,
					CanCodPickup:          maybeDeleteCepRange.CanCodPickup,
					CanDeliver:            maybeDeleteCepRange.CanDeliver,
					CanCodDeliver:         maybeDeleteCepRange.CanCodDeliver,
					SupportTradeIn:        maybeDeleteCepRange.SupportTradeIn,
				})
			})
			isAutoMergeOrSplitFlag = true
		}
	}
	// 4.4 对新增区间进行删除或拆分
	for _, addCepRange := range mayAddCepRangeList {
		key := serviceable_util.GetLineServiceableGroupKey(addCepRange)
		var deleteSet ceprange_util.CepRangeSet
		for _, deleteCepRange := range deleteCepRangeMap[key] {
			// 如果删除区间与新增区间有重叠，则添加到delete集合里
			if ceprange_util.Overlap(addCepRange, deleteCepRange) {
				deleteSet.Add(deleteCepRange)
			}
		}
		if deleteSet.Len() == 0 {
			actuallyAddCepRangeList = append(actuallyAddCepRangeList, addCepRange)
			continue
		}

		// 区间作差，新增区间 - 所有有重叠的删除区间
		var originSet ceprange_util.CepRangeSet
		originSet.Add(addCepRange)
		resultSet := ceprange_util.Subtract(originSet, deleteSet)

		// 如果resultSet不为空，说明此区间会被拆分为多段，将拆分后的区间添加到实际新增列表
		resultSet.Range(func(item *ceprange_util.CepRangeItem) {
			actuallyAddCepRangeList = append(actuallyAddCepRangeList, &model.LineBasicServiceableCepRangeTab{
				LineID:                addCepRange.LineID,
				Region:                addCepRange.Region,
				CollectDeliverGroupID: addCepRange.CollectDeliverGroupID,
				Initial:               item.GetCepStart(),
				Final:                 item.GetCepEnd(),
				CanPickup:             addCepRange.CanPickup,
				CanCodPickup:          addCepRange.CanCodPickup,
				CanDeliver:            addCepRange.CanDeliver,
				CanCodDeliver:         addCepRange.CanCodDeliver,
				SupportTradeIn:        addCepRange.SupportTradeIn,
			})
		})
		isAutoMergeOrSplitFlag = true
	}

	// 5. 校验互斥组内线服务范围是否重叠（SPLN-19677）：属于同一互斥组内的不同线的服务范围不能重叠
	// 5.1 获取上传线所属互斥组包含的所有线列表下的全部服务范围数据，并按照line和group进行分组
	var allNotAllowedLines []string
	NotAllowedOverlapMap := map[string]map[string][]*model.LineBasicServiceableCepRangeTab{}
	for _, tmpLine := range lineIdList {
		notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, tmpLine)
		allNotAllowedLines = append(allNotAllowedLines, notAllowedOverlapList...)
	}
	for _, tmpLine := range allNotAllowedLines {
		if _, ok := NotAllowedOverlapMap[tmpLine]; !ok {
			serviceableCepRanges, lcosErr := l.lineBasicServiceableCepRangeDAO.GetAllLineBasicServiceableCepRangeModel(ctx, map[string]interface{}{"line_id": tmpLine})
			if lcosErr != nil {
				result.ParseFileErr = lcosErr
				return result, nil
			}
			for _, serviceableCepRange := range serviceableCepRanges {
				if _, ok1 := actuallyDeleteCepRangeMap[serviceable_util.GetLineCepRangeServiceableUniqueKey(serviceableCepRange)]; ok1 {
					// 这条配置数据即将被删除，因此不参与互斥组校验
					continue
				}
				if _, ok2 := NotAllowedOverlapMap[tmpLine]; !ok2 {
					NotAllowedOverlapMap[tmpLine] = map[string][]*model.LineBasicServiceableCepRangeTab{}
				}
				NotAllowedOverlapMap[tmpLine][serviceableCepRange.CollectDeliverGroupID] = append(NotAllowedOverlapMap[tmpLine][serviceableCepRange.CollectDeliverGroupID], serviceableCepRange)
			}
		}
	}
	// 5.2 将新增写入的数据也加入到map中用于做互斥组校验
	for _, addCepRange := range actuallyAddCepRangeList {
		if _, ok := NotAllowedOverlapMap[addCepRange.LineID]; !ok {
			NotAllowedOverlapMap[addCepRange.LineID] = map[string][]*model.LineBasicServiceableCepRangeTab{}
		}
		NotAllowedOverlapMap[addCepRange.LineID][addCepRange.CollectDeliverGroupID] = append(NotAllowedOverlapMap[addCepRange.LineID][addCepRange.CollectDeliverGroupID], addCepRange)
	}
	// 5.3 对cep range进行排序用于加速校验
	for _, groupIDCepRangeMap := range NotAllowedOverlapMap {
		for groupID, tmpCepRangeList := range groupIDCepRangeMap {
			sortedCepRangeList := tmpCepRangeList
			sort.Slice(sortedCepRangeList, func(i, j int) bool {
				return sortedCepRangeList[i].Initial <= sortedCepRangeList[j].Initial
			})
			groupIDCepRangeMap[groupID] = sortedCepRangeList
		}
	}
	// 5.4 互斥组校验，校验新增数据是否与同组内其他新增数据+DB已有数据重叠
	comparedCepRangeMap := map[string][]*model.LineBasicServiceableCepRangeTab{}
	for _, serviceCepRange := range actuallyAddCepRangeList {
		if _, ok := comparedCepRangeMap[serviceCepRange.LineID]; !ok {
			comparedCepRangeMap[serviceCepRange.LineID] = []*model.LineBasicServiceableCepRangeTab{}
		}
		comparedCepRangeMap[serviceCepRange.LineID] = append(comparedCepRangeMap[serviceCepRange.LineID], serviceCepRange)
	}
	for lineID, lineCepRangelist := range comparedCepRangeMap {
		lcosErr = l.checkCepRangeLineOverlap(ctx, lineID, lineCepRangelist, NotAllowedOverlapMap)
		if lcosErr != nil {
			result.ParseFileErr = lcosErr
			return result, nil
		}
	}

	// 6. 解析和校验完成，根据async flag创建定时任务或者将配置落库
	if async {
		return result, nil
	}

	fn := func() *lcos_error.LCOSError {
		if err := l.lineBasicServiceableCepRangeDAO.BatchDeleteLineBasicServiceableCepRangeModel(ctx, actuallyDeleteCepRangeList, constant.DBMAXBATCHDELNUM); err != nil {
			return err
		}
		if err := l.lineBasicServiceableCepRangeDAO.BatchCreateLineBasicServiceableCepRangeModelOnDuplicateUpdate(ctx, actuallyAddCepRangeList); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fn); err != nil {
		return result, err
	}

	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineBasicCepRangeTabNamespace)
	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(lineIdList, ","))

	if isAutoMergeOrSplitFlag {
		alertLineBasicCepRangeAutoMergeOrSplit(ctx, actuallyDeleteCepRangeList, actuallyAddCepRangeList, "Upload File")
	}
	return result, nil
}

func checkAndParseFile(ctx utils.LCOSContext, region, filePath string, header []excel.ParseableField, result *excel.ParseFileResult) ([]string, []string, []string, []*model.LineBasicServiceableCepRangeTab, []*model.LineBasicServiceableCepRangeTab) {
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file  complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, err := excelize.OpenFile(filePath)
	if err != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
		return nil, nil, nil, nil, nil
	}
	rows, err := file.Rows("Sheet1")
	if err != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
		return nil, nil, nil, nil, nil
	}
	defer os.Remove(filePath)

	var rowDataList []*serviceable_util.LineBasicCepRangeRowData
	var lineIdList []string
	lineIdMap := make(map[string]bool)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount()

		rowData := &serviceable_util.LineBasicCepRangeRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}
		if region != rowData.Region {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", region, rowData.RowId))
			continue
		}
		if rowData.CepFinal < rowData.CepInitial {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Final should not be less than CEP Initial | row=%d", rowData.RowId))
			continue
		}

		if _, ok := lineIdMap[rowData.LineId]; !ok {
			lineIdList = append(lineIdList, rowData.LineId)
			lineIdMap[rowData.LineId] = true
		}

		rowDataList = append(rowDataList, rowData)
	}

	if len(lineIdList) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is empty")
		return nil, nil, nil, nil, nil
	}

	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIdList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return nil, nil, nil, nil, nil
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}
	//lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, lineIdList)
	//for lineId, lineDraft := range lineDraftMap {
	//	lineInfoMap[lineId] = lineDraft
	//}
	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(ctx, region, lineIdList, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return nil, nil, nil, nil, nil
	}
	addedCepRangeDataList := make([]*model.LineBasicServiceableCepRangeTab, 0, 500)
	deletedCepRangeDataList := make([]*model.LineBasicServiceableCepRangeTab, 0, 500)
	var addedLineList []string
	var addedLineMap = make(map[string]bool)
	var deletedLineList []string
	var deleteLineMap = make(map[string]bool)

	for _, rowData := range rowDataList {
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}

		parsedData := &model.LineBasicServiceableCepRangeTab{
			LineID:                rowData.LineId,
			CollectDeliverGroupID: rowData.GroupId,
			Region:                rowData.Region,
			Initial:               uint64(rowData.CepInitial),
			Final:                 uint64(rowData.CepFinal),
			CanPickup:             rowData.SupportPickup,
			CanCodPickup:          rowData.SupportCodPickup,
			CanDeliver:            rowData.SupportDeliver,
			CanCodDeliver:         rowData.SupportCodDeliver,
			SupportTradeIn:        rowData.SupportTradeIn,
		}
		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			if _, ok := addedLineMap[parsedData.LineID]; !ok {
				addedLineList = append(addedLineList, parsedData.LineID)
				addedLineMap[parsedData.LineID] = true
			}
			addedCepRangeDataList = append(addedCepRangeDataList, parsedData)
		} else {
			if _, ok := deleteLineMap[parsedData.LineID]; !ok {
				deletedLineList = append(deletedLineList, parsedData.LineID)
				deleteLineMap[parsedData.LineID] = true
			}
			deletedCepRangeDataList = append(deletedCepRangeDataList, parsedData)
		}
	}

	return lineIdList, addedLineList, deletedLineList, addedCepRangeDataList, deletedCepRangeDataList
}

func checkTableHeader(row []string) error {

	if len(row) < 10 {
		return errors.New("wrong column number")
	}

	expectTitle := [10]string{
		"*Line ID", "*Group ID", "*Region", "*CEP Initial", "*CEP Final", "*Support Pickup", "*Support COD Pickup", "*Support Deliver", "*Support COD Deliver", "*Action Code",
	}

	for i, name := range row {
		if i == 10 {
			return nil
		}
		if name != expectTitle[i] {
			return fmt.Errorf("title must be in:%+v", expectTitle)
		}
	}

	return nil
}

func parseSingleRow(region string, rowId int, row []string) (*model.LineBasicServiceableCepRangeTab, bool, *lcos_error.LCOSError) {
	rowData := &serviceable_util.LineBasicCepRangeRowData{}
	if err := excel.ParseRowDataWithHeader(rowId, row, serviceable_util.LineBasicCepRangeHeader, rowData); err != nil {
		return nil, false, err
	}
	if region != rowData.Region {
		return nil, false, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", region, rowId)
	}

	if rowData.CepFinal < rowData.CepInitial {
		return nil, false, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Final should not be less than CEP Initial | row=%d", rowId)
	}

	addFlag := rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode

	return &model.LineBasicServiceableCepRangeTab{
		LineID:                rowData.LineId,
		CollectDeliverGroupID: rowData.GroupId,
		Region:                rowData.Region,
		Initial:               uint64(rowData.CepInitial),
		Final:                 uint64(rowData.CepFinal),
		CanPickup:             rowData.SupportPickup,
		CanCodPickup:          rowData.SupportCodPickup,
		CanDeliver:            rowData.SupportDeliver,
		CanCodDeliver:         rowData.SupportCodDeliver,
		SupportTradeIn:        rowData.SupportTradeIn,
	}, addFlag, nil
}

func alertLineBasicCepRangeAutoMergeOrSplit(ctx utils.LCOSContext, deleteList, addList []*model.LineBasicServiceableCepRangeTab, changeType string) {
	var (
		lineList []string
		lineMap  = make(map[string]struct{})
		rows     = make([]interface{}, 0, len(deleteList)+len(addList))
	)

	for _, deleteData := range deleteList {
		if _, ok := lineMap[deleteData.LineID]; !ok {
			lineList = append(lineList, deleteData.LineID)
			lineMap[deleteData.LineID] = struct{}{}
		}

		rows = append(rows, &serviceable_util.LineBasicCepRangeRowData{
			LineId:            deleteData.LineID,
			GroupId:           deleteData.CollectDeliverGroupID,
			Region:            deleteData.Region,
			CepInitial:        int(deleteData.Initial),
			CepFinal:          int(deleteData.Final),
			SupportPickup:     deleteData.CanPickup,
			SupportCodPickup:  deleteData.CanCodPickup,
			SupportDeliver:    deleteData.CanDeliver,
			SupportCodDeliver: deleteData.CanCodDeliver,
			ActionCode:        -1,
		})
	}
	for _, addData := range addList {
		if _, ok := lineMap[addData.LineID]; !ok {
			lineList = append(lineList, addData.LineID)
			lineMap[addData.LineID] = struct{}{}
		}

		rows = append(rows, &serviceable_util.LineBasicCepRangeRowData{
			LineId:            addData.LineID,
			GroupId:           addData.CollectDeliverGroupID,
			Region:            addData.Region,
			CepInitial:        int(addData.Initial),
			CepFinal:          int(addData.Final),
			SupportPickup:     addData.CanPickup,
			SupportCodPickup:  addData.CanCodPickup,
			SupportDeliver:    addData.CanDeliver,
			SupportCodDeliver: addData.CanCodDeliver,
			ActionCode:        1,
		})
	}

	filePath := fmt.Sprintf("/tmp/line_basic_ceprange_change_%d_%s.xlsx", utils.GetTimestamp(ctx), utils.GetUUID(ctx))
	file := xlsx.NewFile()
	titles := make([]string, 0, len(serviceable_util.LineBasicCepRangeHeader))
	for _, field := range serviceable_util.LineBasicCepRangeHeader {
		titles = append(titles, field.Name)
	}
	if err := excel.WriteTitleAndStruct(file, "Sheet1", titles, rows); err != nil {
		logger.CtxLogErrorf(ctx, "alert line basic cep range auto merge/split failed, write file error|cause=%s", err.Error())
		return
	}
	if err := file.Save(filePath); err != nil {
		logger.CtxLogErrorf(ctx, "alert line basic cep range auto merge/split failed, save file error|cause=%s", err.Error())
		return
	}

	if err := serviceable_util.AlertLineCepRangeSAAutoMergeOrSplit(ctx, lineList, changeType, filePath); err != nil {
		logger.CtxLogErrorf(ctx, "alert line basic cep range auto merge/split failed, upload file or send message error|cause=%s", err.Msg)
		return
	}
}
