package basic_cep_range

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/ceprange_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"log"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
)

func TestLineBasicServiceableCepRangeService_UploadBasicServiceableCepRange(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	type fields struct {
		lineBasicServiceableCepRangeDAO model.LineServiceableCepRangeDaoInterface
	}
	type args struct {
		ctx      utils.LCOSContext
		request  *common_protocol.UploadFileRequest
		filePath string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "upload line basic cep range",
			fields: fields{
				lineBasicServiceableCepRangeDAO: model.NewLineServiceableCepRangeDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &common_protocol.UploadFileRequest{
					FileUrl: "",
					Region:  "BR",
				},
				filePath: "/Users/<USER>/Desktop/serviceable-basic-cep-range.xlsx",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := &LineBasicServiceableCepRangeService{
				lineBasicServiceableCepRangeDAO: tt.fields.lineBasicServiceableCepRangeDAO,
			}
			if got := l.UploadBasicServiceableCepRange(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("LineBasicServiceableCepRangeService.UploadBasicServiceableCepRange() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCepRangeAutoSplit(t *testing.T) {
	addCepRange := []*model.LineBasicServiceableCepRangeTab{}

	delCepRange := []*model.LineBasicServiceableCepRangeTab{
		{Initial: 101, Final: 150},
		{Initial: 200, Final: 300},
		{Initial: 400, Final: 450},
		{Initial: 620, Final: 650},
	}
	exisitsCepRange := []*model.LineBasicServiceableCepRangeTab{
		{Initial: 1, Final: 100},
		{Initial: 200, Final: 300},
		{Initial: 400, Final: 500},
		{Initial: 600, Final: 700},
	}

	var actuallyDeleteCepRangeList []*model.LineBasicServiceableCepRangeTab

	for _, maybeDeleteCepRange := range exisitsCepRange {
		var deleteSet ceprange_util.CepRangeSet
		for _, deleteCepRange := range delCepRange {
			// 如果删除区间与原区间有重叠，则添加到delete集合里
			if ceprange_util.Overlap(maybeDeleteCepRange, deleteCepRange) {
				deleteSet.Add(deleteCepRange)
			}
		}
		if deleteSet.Len() != 0 {
			// 如果delete集合不为空，则进行区间作差。此时：
			// 1. 需要删除原区间
			// 2. 作差后如果区间不为空，则需要将拆分后的区间添加到新增区间列表

			// 将原区间添加到实际删除列表
			actuallyDeleteCepRangeList = append(actuallyDeleteCepRangeList, maybeDeleteCepRange)

			// 区间作差，原区间 - 所有有重叠的删除区间
			var originSet ceprange_util.CepRangeSet
			originSet.Add(maybeDeleteCepRange)
			resultSet := ceprange_util.Subtract(originSet, deleteSet)

			if resultSet.Len() != 0 {
				// 如果resultSet不为空，说明此区间会被拆分为多段，将拆分后的区间添加到新增列表
				resultSet.Range(func(item *ceprange_util.CepRangeItem) {
					splitedCepRange := *maybeDeleteCepRange
					splitedCepRange.Initial = item.GetCepStart()
					splitedCepRange.Final = item.GetCepEnd()
					addCepRange = append(addCepRange, &splitedCepRange)
				})
			}
		}
	}

	log.Println("actually delete")
	for _, tab := range actuallyDeleteCepRangeList {
		log.Printf("[%d, %d]\n", tab.GetCepStart(), tab.GetCepEnd())
	}

	log.Println("actually add")
	for _, tab := range addCepRange {
		log.Printf("[%d, %d]\n", tab.GetCepStart(), tab.GetCepEnd())
	}
}
