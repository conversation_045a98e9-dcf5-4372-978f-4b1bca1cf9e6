package basic_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
)

type LineUsingLocationCheckedTab struct {
	LineId         string `gorm:"column:line_id" json:"line_id"`
	LineName       string `gorm:"column:line_name" json:"line_name"`
	ProductID      uint64 `gorm:"column:product_id" json:"product_id"`
	ValidatedLevel string `gorm:"column:product_validation_level" json:"product_validation_level"`
	ErrorMessage   string `gorm:"column:Error message" json:"Error message"`
}

var LocationLevelMap = map[uint8]string{
	1: "State",
	2: "City",
	3: "District",
	4: "Street",
}

type (
	// 包含line是否能命中该group的信息，Status为0表示命中不了，1表示可以
	LineCollectDeliverGroupInfo struct {
		*collect_deliver_group.LineCollectDeliverGroupConfTab
		Effective *int32 `json:"effective,omitempty"`
	}
	FullLineBasicServiceableConfInfo struct {
		LineId                      string                                                `form:"line_id" json:"line_id"`
		Region                      string                                                `form:"region" json:"region"`
		OriginServiceableType       uint8                                                 `form:"origin_serviceable_type" json:"origin_serviceable_type"`
		DestServiceableType         uint8                                                 `form:"destination_serviceable_type" json:"destination_serviceable_type"`
		IsCheckBasicServiceable     *uint8                                                `form:"is_check_basic_serviceable" json:"is_check_basic_serviceable"`
		IsCheckOperationServiceable *uint8                                                `form:"is_check_operation_serviceable" json:"is_check_operation_serviceable"`
		OperationMaintenanceMode    *uint8                                                `form:"operation_maintenance_mode" json:"operation_maintenance_mode"`
		IsCheckRoute                *uint8                                                `form:"is_check_route" json:"is_check_route"`
		RouteMode                   *uint8                                                `form:"route_mode" json:"route_mode"`
		IsCheckPredefinedRoute      uint8                                                 `form:"is_check_predefined_route" json:"is_check_predefined_route"`
		DefaultCollectType          uint8                                                 `form:"default_collect_type" json:"default_collect_type"`
		DefaultDeliverType          uint8                                                 `form:"default_deliver_type" json:"default_deliver_type"`
		DefaultPickupEnabled        *uint8                                                `form:"default_pickup_enabled" json:"default_pickup_enabled"`
		DefaultCodPickupEnabled     *uint8                                                `form:"default_cod_pickup_enabled" json:"default_cod_pickup_enabled"`
		DefaultDeliverEnabled       *uint8                                                `form:"default_deliver_enabled" json:"default_deliver_enabled"`
		DefaultCodDeliverEnabled    *uint8                                                `form:"default_cod_deliver_enabled" json:"default_cod_deliver_enabled"`
		DefaultPdpPostcode          *uint8                                                `form:"default_pdp_postcode" json:"default_pdp_postcode"`
		CollectDeliverAbility       uint32                                                `form:"collect_deliver_ability" json:"collect_deliver_ability"`
		IsCheckDistance             *uint8                                                `form:"is_check_distance" json:"is_check_distance"`
		MinDistanceOperator         *uint8                                                `form:"minimum_distance_operator" json:"minimum_distance_operator"`
		MinDistance                 *uint32                                               `form:"minimum_distance" json:"minimum_distance"`
		MaxDistanceOperator         *uint8                                                `form:"maximum_distance_operator" json:"maximum_distance_operator"`
		MaxDistance                 *uint32                                               `form:"maximum_distance" json:"maximum_distance"`
		BusinessAbility             []*model.LineCollectDeliverAbilityTab                 `form:"business_ability" json:"business_ability"`
		ScenarioConf                []*scenario_conf.LineCommonServiceableScenarioConfTab `form:"scenario" json:"scenario"`
		CollectDeliverGroupList     []*LineCollectDeliverGroupInfo                        `form:"collect_deliver_group_list" json:"collect_deliver_group_list"`
		CTime                       int64                                                 `form:"ctime" json:"ctime"`
		MTime                       int64                                                 `form:"mtime" json:"mtime"`
	}

	LineBasicServiceableConfListItemInfo struct {
		LineId                      string  `form:"line_id" json:"line_id"`
		Region                      string  `form:"region" json:"region"`
		OriginServiceableType       uint8   `form:"origin_serviceable_type" json:"origin_serviceable_type"`
		DestServiceableType         uint8   `form:"destination_serviceable_type" json:"destination_serviceable_type"`
		IsCheckBasicServiceable     *uint8  `form:"is_check_basic_serviceable" json:"is_check_basic_serviceable"`
		IsCheckOperationServiceable *uint8  `form:"is_check_operation_serviceable" json:"is_check_operation_serviceable"`
		OperationMaintenanceMode    *uint8  `form:"operation_maintenance_mode" json:"operation_maintenance_mode"`
		IsCheckRoute                *uint8  `form:"is_check_route" json:"is_check_route"`
		RouteMode                   *uint8  `form:"route_mode" json:"route_mode"`
		IsCheckPredefinedRoute      uint8   `form:"is_check_predefined_route" json:"is_check_predefined_route"`
		DefaultCollectType          uint8   `form:"default_collect_type" json:"default_collect_type"`
		DefaultDeliverType          uint8   `form:"default_deliver_type" json:"default_deliver_type"`
		DefaultPickupEnabled        *uint8  `form:"default_pickup_enabled" json:"default_pickup_enabled"`
		DefaultCodPickupEnabled     *uint8  `form:"default_cod_pickup_enabled" json:"default_cod_pickup_enabled"`
		DefaultDeliverEnabled       *uint8  `form:"default_deliver_enabled" json:"default_deliver_enabled"`
		DefaultCodDeliverEnabled    *uint8  `form:"default_cod_deliver_enabled" json:"default_cod_deliver_enabled"`
		DefaultPdpPostcode          *uint8  `form:"default_pdp_postcode" json:"default_pdp_postcode"`
		CollectDeliverAbility       uint32  `form:"collect_deliver_ability" json:"collect_deliver_ability"`
		IsCheckDistance             *uint8  `form:"is_check_distance" json:"is_check_distance"`
		MinDistanceOperator         *uint8  `form:"minimum_distance_operator" json:"minimum_distance_operator"`
		MinDistance                 *uint32 `form:"minimum_distance" json:"minimum_distance"`
		MaxDistanceOperator         *uint8  `form:"maximum_distance_operator" json:"maximum_distance_operator"`
		MaxDistance                 *uint32 `form:"maximum_distance" json:"maximum_distance"`
		CTime                       int64   `form:"ctime" json:"ctime"`
		MTime                       int64   `form:"mtime" json:"mtime"`
	}
)

func genFullLineBasicServiceableConfInfo(confTab *model.LineBasicServiceableConfTab, operationConfTab *operation_conf.LogisticLineOperationServiceableConfTab, abilityTab []*model.LineCollectDeliverAbilityTab,
	scenarioTabs []*scenario_conf.LineCommonServiceableScenarioConfTab, collectDeliverGroupModels []*collect_deliver_group.LineCollectDeliverGroupConfTab) *FullLineBasicServiceableConfInfo {
	groupList := getCollectDeliverGroupInfos(confTab, collectDeliverGroupModels)
	fullLineBasicServiceableConfInfo := &FullLineBasicServiceableConfInfo{
		LineId:                   confTab.LineId,
		Region:                   confTab.Region,
		OriginServiceableType:    confTab.OriginServiceableType,
		DestServiceableType:      confTab.DestinationServiceableType,
		IsCheckBasicServiceable:  &confTab.IsCheckServiceable,
		IsCheckRoute:             &confTab.IsCheckRoute,
		RouteMode:                &confTab.RouteMode,
		IsCheckPredefinedRoute:   confTab.IsCheckPredefinedRoute,
		DefaultCollectType:       confTab.DefaultCollectType,
		DefaultDeliverType:       confTab.DefaultDeliverType,
		DefaultPickupEnabled:     &confTab.DefaultPickupEnabled,
		DefaultCodPickupEnabled:  &confTab.DefaultCodPickupEnabled,
		DefaultDeliverEnabled:    &confTab.DefaultDeliverEnabled,
		DefaultCodDeliverEnabled: &confTab.DefaultCodDeliverEnabled,
		DefaultPdpPostcode:       &confTab.DefaultPdpPostcode,
		CollectDeliverAbility:    confTab.CollectDeliverAbility,
		IsCheckDistance:          &confTab.IsCheckDistance,
		MinDistanceOperator:      &confTab.MinimumDistanceOperator,
		MinDistance:              &confTab.MinimumDistance,
		MaxDistanceOperator:      &confTab.MaximumDistanceOperator,
		MaxDistance:              &confTab.MaximumDistance,
		BusinessAbility:          abilityTab,
		ScenarioConf:             scenarioTabs,
		CollectDeliverGroupList:  groupList,
		CTime:                    confTab.CTime,
		MTime:                    confTab.MTime,
	}

	if operationConfTab != nil {
		fullLineBasicServiceableConfInfo.IsCheckOperationServiceable = &operationConfTab.IsCheckServiceable
		if operationConfTab.IsCheckServiceable == constant.TRUE {
			fullLineBasicServiceableConfInfo.OperationMaintenanceMode = &operationConfTab.MaintenanceMode
		}
	}

	return fullLineBasicServiceableConfInfo
}

func getCollectDeliverGroupInfos(confTab *model.LineBasicServiceableConfTab, collectDeliverGroupModels []*collect_deliver_group.LineCollectDeliverGroupConfTab) []*LineCollectDeliverGroupInfo {
	if collectDeliverGroupModels == nil {
		return nil
	}
	result := []*LineCollectDeliverGroupInfo{}
	for _, group := range collectDeliverGroupModels {
		var item *LineCollectDeliverGroupInfo
		if confTab == nil {
			item = &LineCollectDeliverGroupInfo{
				LineCollectDeliverGroupConfTab: group,
			}
		} else {
			effective := serviceable_util.GetEffectiveStatus(confTab.CollectDeliverAbility, group.PickupMode, group.DeliverMode)
			item = &LineCollectDeliverGroupInfo{
				LineCollectDeliverGroupConfTab: group,
				Effective:                      &effective,
			}
		}
		result = append(result, item)
	}

	return result
}

func GenLineBasicServiceableConfListItemInfo(confTab *model.LineBasicServiceableConfTab, operationConfTab *operation_conf.LogisticLineOperationServiceableConfTab) *LineBasicServiceableConfListItemInfo {
	lineBasicServiceableConfListItemInfo := &LineBasicServiceableConfListItemInfo{
		LineId:                   confTab.LineId,
		Region:                   confTab.Region,
		OriginServiceableType:    confTab.OriginServiceableType,
		DestServiceableType:      confTab.DestinationServiceableType,
		IsCheckBasicServiceable:  &confTab.IsCheckServiceable,
		IsCheckRoute:             &confTab.IsCheckRoute,
		RouteMode:                &confTab.RouteMode,
		IsCheckPredefinedRoute:   confTab.IsCheckPredefinedRoute,
		DefaultCollectType:       confTab.DefaultCollectType,
		DefaultDeliverType:       confTab.DefaultDeliverType,
		DefaultPickupEnabled:     &confTab.DefaultPickupEnabled,
		DefaultCodPickupEnabled:  &confTab.DefaultCodPickupEnabled,
		DefaultDeliverEnabled:    &confTab.DefaultDeliverEnabled,
		DefaultCodDeliverEnabled: &confTab.DefaultCodDeliverEnabled,
		DefaultPdpPostcode:       &confTab.DefaultPdpPostcode,
		CollectDeliverAbility:    confTab.CollectDeliverAbility,
		IsCheckDistance:          &confTab.IsCheckDistance,
		MinDistanceOperator:      &confTab.MinimumDistanceOperator,
		MinDistance:              &confTab.MinimumDistance,
		MaxDistanceOperator:      &confTab.MaximumDistanceOperator,
		MaxDistance:              &confTab.MaximumDistance,
		CTime:                    confTab.CTime,
		MTime:                    confTab.MTime,
	}

	if operationConfTab != nil {
		lineBasicServiceableConfListItemInfo.IsCheckOperationServiceable = &operationConfTab.IsCheckServiceable
		if operationConfTab.IsCheckServiceable == constant.TRUE {
			lineBasicServiceableConfListItemInfo.OperationMaintenanceMode = &operationConfTab.MaintenanceMode
		}
	}
	return lineBasicServiceableConfListItemInfo
}
