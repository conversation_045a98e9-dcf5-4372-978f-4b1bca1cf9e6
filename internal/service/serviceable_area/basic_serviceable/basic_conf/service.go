package basic_conf

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/basic_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	collectDeliver "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type LineBasicServiceableConfServiceInterface interface {
	CreateBasicServiceableConf(ctx utils.LCOSContext, request *basic_serviceable.CreateOrUpdateBasicServiceableConfRequest, checkLineExist bool) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError)
	UpdateBasicServiceableConf(ctx utils.LCOSContext, request *basic_serviceable.CreateOrUpdateBasicServiceableConfRequest) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError)
	SimplePutBasicServiceableConf(ctx utils.LCOSContext, request *basic_serviceable.CreateOrUpdateBasicServiceableConfRequest) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError)
	GetBasicServiceableConfByLineId(ctx utils.LCOSContext, lineId string) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError)
	GetBasicServiceableConfByLineIds(ctx utils.LCOSContext, lineIds []string) ([]*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError)
	GetBasicServiceableConfByLineIdUseCache(ctx utils.LCOSContext, lineId string) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError)
	GetBasicServiceableConfList(ctx utils.LCOSContext, request *basic_serviceable.BasicServiceableConfListRequest) (*common.PageModel, *lcos_error.LCOSError)
	DeleteBasicServiceableConf(ctx utils.LCOSContext, request *common_protocol.LineIdRequest) *lcos_error.LCOSError
	GetLineCollectDeliverAbility(ctx utils.LCOSContext, request *pb.GetLineCollectDeliverAbilityRequest) (*pb.GetLineCollectDeliverAbilityResponse, *lcos_error.LCOSError)
	BatchGetLineCollectDeliverAbility(ctx utils.LCOSContext, request *pb.BatchGetLineCollectDeliverAbilityRequest) (*pb.BatchGetLineCollectDeliverAbilityResponse, *lcos_error.LCOSError)
	CheckCollectDeliverGroupWithLine(ctx utils.LCOSContext, lineId, groupId string) (*LineCollectDeliverGroupInfo, *lcos_error.LCOSError)
	GetAllLinesUsingLocationCheckedList(ctx utils.LCOSContext, request *basic_serviceable.ExportLinesUsingLocationChecked) ([]*LineUsingLocationCheckedTab, *lcos_error.LCOSError)

	GetBasicServiceableConfBaseModelByLineId(ctx utils.LCOSContext, lineId string) (*model.LineBasicServiceableConfTab, *lcos_error.LCOSError)
}

type LineBasicServiceableConfService struct {
	lineBasicServiceableConfDAO          model.LineBasicServiceableConfDAO
	lineCommonServiceableScenarioConfDAO scenario_conf.LineCommonServiceableScenarioConfDAO
	lineOperationServiceableConfDAO      operation_conf.LogisticLineOperationServiceableConfTabDAO
	lineBasicServiceableGroupRefDao      collect_deliver_group_ref.LineBasicServiceableGroupRefDao
	collectDeliverGroupService           collect_deliver_group.CollectDeliverGroupServiceInterface
	lineBasicServiceableLocationDao      basic_location.LineBasicServiceableLocationDAO
	lineBasicServiceablePostcodeDAO      basic_postcode.LineBasicServiceablePostcodeDAO
}

func NewLineBasicServiceableConfService(lineBasicServiceableConfDAO model.LineBasicServiceableConfDAO, lineCommonServiceableScenarioConfDAO scenario_conf.LineCommonServiceableScenarioConfDAO,
	lineOperationServiceableConfDAO operation_conf.LogisticLineOperationServiceableConfTabDAO, lineBasicServiceableGroupRefDao collect_deliver_group_ref.LineBasicServiceableGroupRefDao,
	collectDeliverGroupService collect_deliver_group.CollectDeliverGroupServiceInterface, lineBasicServiceableLocationDao basic_location.LineBasicServiceableLocationDAO,
	lineBasicServiceablePostcodeDAO basic_postcode.LineBasicServiceablePostcodeDAO) *LineBasicServiceableConfService {
	return &LineBasicServiceableConfService{
		lineBasicServiceableConfDAO:          lineBasicServiceableConfDAO,
		lineCommonServiceableScenarioConfDAO: lineCommonServiceableScenarioConfDAO,
		lineOperationServiceableConfDAO:      lineOperationServiceableConfDAO,
		lineBasicServiceableGroupRefDao:      lineBasicServiceableGroupRefDao,
		collectDeliverGroupService:           collectDeliverGroupService,
		lineBasicServiceableLocationDao:      lineBasicServiceableLocationDao,
		lineBasicServiceablePostcodeDAO:      lineBasicServiceablePostcodeDAO,
	}
}

func (s *LineBasicServiceableConfService) CreateBasicServiceableConf(ctx utils.LCOSContext, request *basic_serviceable.CreateOrUpdateBasicServiceableConfRequest, checkLineExist bool) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	lineGroupConfModels, err := s.collectDeliverGroupService.GetAllCollectDeliverByGroupIds(ctx, request.GroupIdList)
	if err != nil {
		return nil, err
	}
	// 如果isCheckBasicServiceable或者isCheckOperationServiceable开关打开 还需要查询 location、postcode有无配置group
	if *request.IsCheckBasicServiceable == constant.ENABLED || *request.IsCheckOperationServiceable == constant.ENABLED {
		if len(request.GroupIdList) <= 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "group id list is empty")
		}
		// check line的group冲突
		lineGroupConfModels, err = s.collectDeliverGroupService.CheckGroupConflictByGroupIds(ctx, request.GroupIdList)
		if err != nil {
			return nil, err
		}

		if request.OriginServiceableType == constant.LOCATION {
			if err := checkLocation(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		} else if request.OriginServiceableType == constant.POSTCODE {
			if err := checkPostcode(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		} else if request.OriginServiceableType == constant.CEPRANGE {
			if err := checkCepRange(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		}

		if request.DestServiceableType == constant.LOCATION {
			if err := checkLocation(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		} else if request.DestServiceableType == constant.POSTCODE {
			if err := checkPostcode(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		} else if request.DestServiceableType == constant.CEPRANGE {
			if err := checkCepRange(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		}
	}

	if request.IsCheckRoute != nil && *request.IsCheckRoute == constant.ENABLED && (request.RouteMode == nil || *request.RouteMode == 0) {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "route mode illegal while route check enable")
	}

	confModel := new(model.LineBasicServiceableConfTab)
	operationConfModel := new(operation_conf.LogisticLineOperationServiceableConfTab)
	scenarioConfModels := make([]*scenario_conf.LineCommonServiceableScenarioConfTab, len(request.ScenarioConf))
	abilityModels := make([]*model.LineCollectDeliverAbilityTab, len(request.BusinessAbility))
	lineGroupRefModels := make([]*collect_deliver_group_ref.LineBasicServiceableGroupRefTab, len(lineGroupConfModels))
	fillModels(confModel, operationConfModel, abilityModels, scenarioConfModels, lineGroupRefModels, lineGroupConfModels, request)
	confModel.LineId = request.LineId
	fc := func() *lcos_error.LCOSError {
		if _, err := s.lineBasicServiceableConfDAO.CreateBasicServiceableConfModel(ctx, confModel); err != nil {
			return err
		}
		if _, err := s.lineOperationServiceableConfDAO.CreateLogisticLineOperationServiceableConfTab(ctx, operationConfModel); err != nil {
			return err
		}
		if _, err := s.lineBasicServiceableConfDAO.BatchCreateLineAbilityModel(ctx, abilityModels); err != nil {
			return err
		}
		if _, err := s.lineCommonServiceableScenarioConfDAO.BatchCreateScenarioConfModel(ctx, scenarioConfModels); err != nil {
			return err
		}
		if len(lineGroupRefModels) > 0 {
			if _, err := s.lineBasicServiceableGroupRefDao.BatchCreateLineBasicServiceableGroupRef(ctx, lineGroupRefModels); err != nil {
				return err
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}

	return genFullLineBasicServiceableConfInfo(confModel, operationConfModel, abilityModels, scenarioConfModels, lineGroupConfModels), nil
}

func (s *LineBasicServiceableConfService) UpdateBasicServiceableConf(ctx utils.LCOSContext, request *basic_serviceable.CreateOrUpdateBasicServiceableConfRequest) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	confModel, err := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineId(ctx, request.LineId)
	if err != nil {
		return nil, err
	}
	if confModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicConfErrorCode)
	}
	operationConfModel, err := s.lineOperationServiceableConfDAO.GetLogisticLineOperationServiceableConfTabByLineID(ctx, request.LineId)
	if err != nil {
		return nil, err
	}
	if operationConfModel == nil {
		operationConfModel = &operation_conf.LogisticLineOperationServiceableConfTab{
			LineID:             request.LineId,
			Region:             request.Region,
			IsCheckServiceable: *request.IsCheckOperationServiceable,
		}
	}

	lineGroupConfModels, err := s.collectDeliverGroupService.GetAllCollectDeliverByGroupIds(ctx, request.GroupIdList)
	if err != nil {
		return nil, err
	}
	// 如果isCheckBasicServiceable或者isCheckOperationServiceable开关打开 还需要查询 location、postcode有无配置group
	if *request.IsCheckBasicServiceable == constant.ENABLED || *request.IsCheckOperationServiceable == constant.ENABLED {
		if len(request.GroupIdList) <= 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "group id list is empty")
		}
		// check line的group冲突
		lineGroupConfModels, err = s.collectDeliverGroupService.CheckGroupConflictByGroupIds(ctx, request.GroupIdList)
		if err != nil {
			return nil, err
		}

		if request.OriginServiceableType == constant.LOCATION {
			if err := checkLocation(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		} else if request.OriginServiceableType == constant.POSTCODE {
			if err := checkPostcode(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		} else if request.OriginServiceableType == constant.CEPRANGE {
			if err := checkCepRange(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}

		}
		if request.DestServiceableType == constant.LOCATION {
			if err := checkLocation(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		} else if request.DestServiceableType == constant.POSTCODE {
			if err := checkPostcode(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		} else if request.DestServiceableType == constant.CEPRANGE {
			if err := checkCepRange(ctx, request.LineId, request.GroupIdList); err != nil {
				return nil, err
			}
		}
	}

	if request.IsCheckRoute != nil && *request.IsCheckRoute == constant.ENABLED && (request.RouteMode == nil || *request.RouteMode == 0) {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "route mode illegal while route check enable")
	}

	abilityModels := make([]*model.LineCollectDeliverAbilityTab, len(request.BusinessAbility))
	scenarioConfModels := make([]*scenario_conf.LineCommonServiceableScenarioConfTab, len(request.ScenarioConf))
	lineGroupRefModels := make([]*collect_deliver_group_ref.LineBasicServiceableGroupRefTab, len(lineGroupConfModels))
	fillModels(confModel, operationConfModel, abilityModels, scenarioConfModels, lineGroupRefModels, lineGroupConfModels, request)
	fc := func() *lcos_error.LCOSError {
		if _, err := s.lineBasicServiceableConfDAO.UpdateBasicServiceableConfModel(ctx, confModel); err != nil {
			return err
		}
		// delete operation conf and insert new one
		if err := s.lineOperationServiceableConfDAO.DeleteLogisticLineOperationServiceableConfTabsByLineID(ctx, request.LineId); err != nil {
			return err
		}
		if _, err := s.lineOperationServiceableConfDAO.CreateLogisticLineOperationServiceableConfTab(ctx, operationConfModel); err != nil {
			return err
		}

		if err := s.lineBasicServiceableConfDAO.DeleteLineAbilityModelByLineId(ctx, request.LineId); err != nil {
			return err
		}
		if _, err := s.lineBasicServiceableConfDAO.BatchCreateLineAbilityModel(ctx, abilityModels); err != nil {
			return err
		}
		if err := s.lineCommonServiceableScenarioConfDAO.DeleteScenarioConfModelByLineId(ctx, request.LineId); err != nil {
			return err
		}
		if _, err := s.lineCommonServiceableScenarioConfDAO.BatchCreateScenarioConfModel(ctx, scenarioConfModels); err != nil {
			return err
		}
		if err := s.lineBasicServiceableGroupRefDao.DeleteLineBasicServiceableGroupRefByLineId(ctx, request.LineId); err != nil {
			return err
		}
		if len(lineGroupRefModels) > 0 {
			if _, err := s.lineBasicServiceableGroupRefDao.BatchCreateLineBasicServiceableGroupRef(ctx, lineGroupRefModels); err != nil {
				return err
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}

	return genFullLineBasicServiceableConfInfo(confModel, operationConfModel, abilityModels, scenarioConfModels, lineGroupConfModels), nil
}

func (s *LineBasicServiceableConfService) GetBasicServiceableConfByLineId(ctx utils.LCOSContext, lineId string) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError) {
	confModel, err := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineId(ctx, lineId)
	if err != nil {
		return nil, err
	}
	if confModel == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBasicConfErrorCode, "line basic conf not found")
	}
	operationConfModel, err := s.lineOperationServiceableConfDAO.GetLogisticLineOperationServiceableConfTabByLineID(ctx, lineId)
	if err != nil {
		return nil, err
	}

	abilityModels, err := s.lineBasicServiceableConfDAO.GetLineAbilityModelByLineId(ctx, lineId)
	if err != nil {
		return nil, err
	}

	scenarioConfModels, err := s.lineCommonServiceableScenarioConfDAO.GetScenarioConfModelByLineId(ctx, lineId)
	if err != nil {
		return nil, err
	}

	collectDeliverGroupModels, err := s.collectDeliverGroupService.GetCollectDeliverGroupByLineId(ctx, lineId)
	if err != nil {
		return nil, err
	}
	return genFullLineBasicServiceableConfInfo(confModel, operationConfModel, abilityModels, scenarioConfModels, collectDeliverGroupModels), nil
}

func (s *LineBasicServiceableConfService) GetBasicServiceableConfByLineIds(ctx utils.LCOSContext, lineIds []string) ([]*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError) {
	confModels, err := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineIds(ctx, lineIds)
	if err != nil {
		return nil, err
	}
	if len(confModels) == 0 {
		return nil, nil
	}

	abilityModels, err := s.lineBasicServiceableConfDAO.GetLineAbilityModelByLineIds(ctx, lineIds)
	if err != nil {
		return nil, err
	}

	var result []*FullLineBasicServiceableConfInfo
	for _, confModel := range confModels {
		var tmpList []*model.LineCollectDeliverAbilityTab
		for _, abilityModel := range abilityModels {
			if abilityModel.LineId == confModel.LineId {
				tmpList = append(tmpList, abilityModel)
			}
		}
		result = append(result, genFullLineBasicServiceableConfInfo(confModel, nil, tmpList, nil, nil)) // 后续优化 使用gorm自带的in操作 <EMAIL>
	}

	return result, nil
}

func (s *LineBasicServiceableConfService) GetBasicServiceableConfByLineIdUseCache(ctx utils.LCOSContext, lineId string) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError) {
	confModel, err := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineIdUseCache(ctx, lineId)
	if err != nil {
		return nil, err
	}
	if confModel == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBasicConfErrorCode, "line basic conf not found")
	}
	operationConfModel, err := s.lineOperationServiceableConfDAO.GetLogisticLineOperationServiceableConfTabByLineIDUseCache(ctx, lineId)
	if err != nil {
		return nil, err
	}

	abilityModels, err := s.lineBasicServiceableConfDAO.GetLineAbilityModelByLineIdUseCache(ctx, lineId)
	if err != nil {
		return nil, err
	}

	scenarioConfModels, err := s.lineCommonServiceableScenarioConfDAO.GetScenarioConfModelByLineIdUseCache(ctx, lineId)
	if err != nil {
		return nil, err
	}

	collectDeliverGroupMoelds, err := s.collectDeliverGroupService.GetCollectDeliverGroupByLineIdUseCache(ctx, lineId)
	if err != nil {
		return nil, err
	}

	return genFullLineBasicServiceableConfInfo(confModel, operationConfModel, abilityModels, scenarioConfModels, collectDeliverGroupMoelds), nil
}

func (s *LineBasicServiceableConfService) GetBasicServiceableConfList(ctx utils.LCOSContext, request *basic_serviceable.BasicServiceableConfListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	basicConfSearchMap, _ := utils.Struct2map(request)
	var basicServiceableConfList []*LineBasicServiceableConfListItemInfo

	if ctx.GetClientType() > 0 {
		allowLineIds, err := lls_service.GetLineIdsByClientType(ctx, ctx.GetClientType())
		if err != nil {
			return nil, err
		}
		if len(allowLineIds) == 0 {
			return common.NewEmptyPageModel(pageNo, count), nil
		}
		basicConfSearchMap["LINE_ID"] = allowLineIds
	}

	var operationConfMap = make(map[string]*operation_conf.LogisticLineOperationServiceableConfTab)
	basicConfModels, total, err := s.lineBasicServiceableConfDAO.SearchBasicServiceableConf(ctx, pageNo, count, basicConfSearchMap)
	if err != nil {
		return nil, err
	}
	if len(basicConfModels) > 0 {
		var lineIdList []string
		operationSearchMap := make(map[string]interface{})
		for _, basicConf := range basicConfModels {
			lineIdList = append(lineIdList, basicConf.LineId)
		}
		operationSearchMap["line_id"] = lineIdList
		operationConfModels, err := s.lineOperationServiceableConfDAO.SearchAllLineOperationServiceableConfTab(ctx, operationSearchMap)
		if err != nil {
			return nil, err
		}
		if len(operationConfModels) > 0 {
			for _, operationConf := range operationConfModels {
				operationConfMap[operationConf.LineID] = operationConf
			}
		}
	}
	for _, basicConf := range basicConfModels {
		var operationConf *operation_conf.LogisticLineOperationServiceableConfTab
		if _, ok := operationConfMap[basicConf.LineId]; ok {
			operationConf = operationConfMap[basicConf.LineId]
		}
		lineBaseServiceableConfListItemInfo := GenLineBasicServiceableConfListItemInfo(basicConf, operationConf)
		basicServiceableConfList = append(basicServiceableConfList, lineBaseServiceableConfListItemInfo)
	}

	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   basicServiceableConfList,
	}, nil
}

func (s *LineBasicServiceableConfService) DeleteBasicServiceableConf(ctx utils.LCOSContext, request *common_protocol.LineIdRequest) *lcos_error.LCOSError {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return err
	}
	if !exists {
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	fc := func() *lcos_error.LCOSError {
		if err := s.lineBasicServiceableConfDAO.DeleteBasicServiceableConfModel(ctx, request.LineId); err != nil {
			return err
		}
		if err := s.lineBasicServiceableConfDAO.DeleteLineAbilityModelByLineId(ctx, request.LineId); err != nil {
			return err
		}
		if err := s.lineCommonServiceableScenarioConfDAO.DeleteScenarioConfModelByLineId(ctx, request.LineId); err != nil {
			return err
		}
		if err := s.lineOperationServiceableConfDAO.DeleteLogisticLineOperationServiceableConfTabsByLineID(ctx, request.LineId); err != nil {
			return err
		}
		if err := s.lineBasicServiceableGroupRefDao.DeleteLineBasicServiceableGroupRefByLineId(ctx, request.LineId); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}
	return nil
}

func (s *LineBasicServiceableConfService) GetLineCollectDeliverAbility(ctx utils.LCOSContext, request *pb.GetLineCollectDeliverAbilityRequest) (*pb.GetLineCollectDeliverAbilityResponse, *lcos_error.LCOSError) {
	basicConf, err := s.GetBasicServiceableConfByLineIdUseCache(ctx, *request.LineId)
	if err != nil {
		return nil, err
		//return &pb.GetLineCollectDeliverAbilityResponse{
		//	RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		//}, nil
	}

	return &pb.GetLineCollectDeliverAbilityResponse{
		RespHeader:  http.GrpcSuccessRespHeader(),
		AbilityInfo: genAbilityInfo(basicConf),
	}, nil
}

func (s *LineBasicServiceableConfService) BatchGetLineCollectDeliverAbility(ctx utils.LCOSContext, request *pb.BatchGetLineCollectDeliverAbilityRequest) (*pb.BatchGetLineCollectDeliverAbilityResponse, *lcos_error.LCOSError) {
	var abilityInfoList []*pb.AbilityInfo
	for _, lineId := range request.LineId {
		basicConf, err := s.GetBasicServiceableConfByLineIdUseCache(ctx, lineId)
		if err != nil {
			logger.LogErrorf("get basic conf error: %s", err.Msg)
			continue
		}
		abilityInfoList = append(abilityInfoList, genAbilityInfo(basicConf))
	}

	return &pb.BatchGetLineCollectDeliverAbilityResponse{
		RespHeader:      http.GrpcSuccessRespHeader(),
		AbilityInfoList: abilityInfoList,
	}, nil
}

func (s *LineBasicServiceableConfService) SimplePutBasicServiceableConf(ctx utils.LCOSContext, request *basic_serviceable.CreateOrUpdateBasicServiceableConfRequest) (*FullLineBasicServiceableConfInfo, *lcos_error.LCOSError) {
	basicConf, err := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineId(ctx, request.LineId)
	if err != nil {
		return nil, err
	}
	if basicConf != nil {
		if *request.IsCheckRoute == constant.ENABLED && request.RouteMode == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "route mode empty while route check enable")
		}
		basicConf.MaximumDistance = *request.MaxDistance
		basicConf.MaximumDistanceOperator = *request.MaxDistanceOperator
		basicConf.MinimumDistance = *request.MinDistance
		basicConf.MinimumDistanceOperator = *request.MinDistanceOperator
		basicConf.IsCheckDistance = *request.IsCheckDistance
		basicConf.CollectDeliverAbility = request.CollectDeliverAbility
		abilityModels := make([]*model.LineCollectDeliverAbilityTab, len(request.BusinessAbility))
		for i, businessAbility := range request.BusinessAbility {
			abilityModels[i] = new(model.LineCollectDeliverAbilityTab)
			abilityModels[i].LineId = request.LineId
			abilityModels[i].Region = request.Region
			abilityModels[i].CollectDeliverType = businessAbility.CollectDeliverType
			abilityModels[i].MaxCapacity = *businessAbility.MaxCapacity
		}

		fc := func() *lcos_error.LCOSError {
			if _, err := s.lineBasicServiceableConfDAO.UpdateBasicServiceableConfModel(ctx, basicConf); err != nil {
				return err
			}
			if err := s.lineBasicServiceableConfDAO.DeleteLineAbilityModelByLineId(ctx, request.LineId); err != nil {
				return err
			}
			if _, err := s.lineBasicServiceableConfDAO.BatchCreateLineAbilityModel(ctx, abilityModels); err != nil {
				return err
			}
			return nil
		}
		if err := ctx.Transaction(fc); err != nil {
			return nil, err
		}
		scenarioConfModels, err := s.lineCommonServiceableScenarioConfDAO.GetScenarioConfModelByLineId(ctx, request.LineId)
		if err != nil {
			return nil, err
		}
		return genFullLineBasicServiceableConfInfo(basicConf, nil, abilityModels, scenarioConfModels, nil), nil

	} else {
		return s.CreateBasicServiceableConf(ctx, request, false)
	}

}

func fillModels(confModel *model.LineBasicServiceableConfTab, operationConfModel *operation_conf.LogisticLineOperationServiceableConfTab, abilityModels []*model.LineCollectDeliverAbilityTab, scenarioConfModels []*scenario_conf.LineCommonServiceableScenarioConfTab,
	lineGroupRefModels []*collect_deliver_group_ref.LineBasicServiceableGroupRefTab, lineCollectDeliverGroupConfModels []*collectDeliver.LineCollectDeliverGroupConfTab, request *basic_serviceable.CreateOrUpdateBasicServiceableConfRequest) {
	confModel.LineId = request.LineId
	confModel.Region = request.Region
	confModel.OriginServiceableType = request.OriginServiceableType
	confModel.DestinationServiceableType = request.DestServiceableType
	confModel.IsCheckServiceable = *request.IsCheckBasicServiceable
	confModel.IsCheckRoute = *request.IsCheckRoute
	confModel.DefaultCollectType = constant.PICKUP
	confModel.DefaultDeliverType = constant.TOHOME
	confModel.DefaultPickupEnabled = *request.DefaultPickupEnabled
	confModel.DefaultCodPickupEnabled = *request.DefaultCodPickupEnabled
	confModel.DefaultDeliverEnabled = *request.DefaultDeliverEnabled
	confModel.DefaultCodDeliverEnabled = *request.DefaultCodDeliverEnabled
	confModel.DefaultPdpPostcode = *request.DefaultPdpPostcode
	confModel.CollectDeliverAbility = request.CollectDeliverAbility
	confModel.IsCheckDistance = *request.IsCheckDistance
	confModel.MinimumDistanceOperator = *request.MinDistanceOperator
	confModel.MinimumDistance = *request.MinDistance
	confModel.MaximumDistanceOperator = *request.MaxDistanceOperator
	confModel.MaximumDistance = *request.MaxDistance
	confModel.IsCheckPredefinedRoute = request.IsCheckPredefinedRoute

	if *request.IsCheckRoute == constant.DISABLED {
		// zero-val is meaningless, should not be display on FE
		confModel.RouteMode = 0
	} else {
		confModel.RouteMode = *request.RouteMode
	}

	// 为了兼容：只要有新类型中的to-branch, to-wms, to-3pl中的一种 就补充to-site类型为1
	if (request.CollectDeliverAbility&uint32(constant.TOBRANCH) != 0) || (request.CollectDeliverAbility&uint32(constant.TOWMS) != 0) || (request.CollectDeliverAbility&uint32(constant.TO3PL) != 0) {
		confModel.CollectDeliverAbility = confModel.CollectDeliverAbility | uint32(constant.TOSITE)
	}
	operationConfModel.Region = request.Region
	operationConfModel.IsCheckServiceable = *request.IsCheckOperationServiceable
	operationConfModel.LineID = request.LineId
	operationConfModel.MaintenanceMode = request.OperationMaintenanceMode

	for i, lineGroupModel := range lineCollectDeliverGroupConfModels {
		groupRefTab := &collect_deliver_group_ref.LineBasicServiceableGroupRefTab{
			LineId:                request.LineId,
			Region:                request.Region,
			CollectDeliverGroupId: lineGroupModel.GroupId,
		}
		lineGroupRefModels[i] = groupRefTab
	}

	for i, businessAbility := range request.BusinessAbility {
		abilityModels[i] = new(model.LineCollectDeliverAbilityTab)
		abilityModels[i].LineId = request.LineId
		abilityModels[i].Region = request.Region
		abilityModels[i].CollectDeliverType = businessAbility.CollectDeliverType
		abilityModels[i].MaxCapacity = *businessAbility.MaxCapacity
	}

	for i, scenarioConf := range request.ScenarioConf {
		scenarioConfModels[i] = new(scenario_conf.LineCommonServiceableScenarioConfTab)
		scenarioConfModels[i].LineId = request.LineId
		scenarioConfModels[i].Region = request.Region
		scenarioConfModels[i].Scenario = scenarioConf.Scenario
		scenarioConfModels[i].CheckLevel = scenarioConf.CheckLevel
		scenarioConfModels[i].CheckAddress = scenarioConf.CheckAddress
	}
}

func genAbilityInfo(basicConf *FullLineBasicServiceableConfInfo) *pb.AbilityInfo {
	pickupAbility := (basicConf.CollectDeliverAbility & uint32(constant.PICKUP)) / uint32(constant.PICKUP)
	dropoffAbility := (basicConf.CollectDeliverAbility & uint32(constant.DROPOFF)) / uint32(constant.DROPOFF)
	b2cAbility := (basicConf.CollectDeliverAbility & uint32(constant.B2C)) / uint32(constant.B2C)
	toHomeAbility := (basicConf.CollectDeliverAbility & uint32(constant.TOHOME)) / uint32(constant.TOHOME)
	toSiteAbility := (basicConf.CollectDeliverAbility & uint32(constant.TOSITE)) / uint32(constant.TOSITE)
	toWmsAbility := (basicConf.CollectDeliverAbility & uint32(constant.TOWMS)) / uint32(constant.TOWMS)
	to3plAbility := (basicConf.CollectDeliverAbility & uint32(constant.TO3PL)) / uint32(constant.TO3PL)
	toBranchAbility := (basicConf.CollectDeliverAbility & uint32(constant.TOBRANCH)) / uint32(constant.TOBRANCH)

	collectType := &pb.CollectType{
		Pickup:  &pickupAbility,
		Dropoff: &dropoffAbility,
		B2C:     &b2cAbility,
	}

	deliverType := &pb.DeliverType{
		ToHome:   &toHomeAbility,
		ToSite:   &toSiteAbility,
		ToWms:    &toWmsAbility,
		To_3Pl:   &to3plAbility,
		ToBranch: &toBranchAbility,
	}

	var pickupMaxCapacity uint32 = 0
	var dropoffMaxCapacity uint32 = 0
	var b2cMaxCapacity uint32 = 0
	var toHomeMaxCapacity uint32 = 0
	var toSiteMaxCapacity uint32 = 0

	for _, maxCapacity := range basicConf.BusinessAbility {
		switch maxCapacity.CollectDeliverType {
		case constant.PICKUP:
			pickupMaxCapacity = maxCapacity.MaxCapacity
		case constant.DROPOFF:
			dropoffMaxCapacity = maxCapacity.MaxCapacity
		case constant.B2C:
			b2cMaxCapacity = maxCapacity.MaxCapacity
		case constant.TOHOME:
			toHomeMaxCapacity = maxCapacity.MaxCapacity
		case constant.TOSITE:
			toSiteMaxCapacity = maxCapacity.MaxCapacity
		}
	}

	maxCapacity := &pb.MaxCapacity{
		Pickup:  &pickupMaxCapacity,
		Dropoff: &dropoffMaxCapacity,
		B2C:     &b2cMaxCapacity,
		ToHome:  &toHomeMaxCapacity,
		ToSite:  &toSiteMaxCapacity,
	}

	isCheckDistance := uint32(*basicConf.IsCheckDistance)
	minimumDistanceOperator := uint32(*basicConf.MinDistanceOperator)
	maximumDistanceOperator := uint32(*basicConf.MaxDistanceOperator)

	return &pb.AbilityInfo{
		LineId:                    &basicConf.LineId,
		CollectType:               collectType,
		DeliverType:               deliverType,
		MaxCapacity:               maxCapacity,
		DistanceLimitationChecked: &isCheckDistance,
		MinimumDistanceOperator:   &minimumDistanceOperator,
		MinimumDistance:           basicConf.MinDistance,
		MaximumDistanceOperator:   &maximumDistanceOperator,
		MaximumDistance:           basicConf.MaxDistance,
	}
}

func checkLocation(ctx utils.LCOSContext, lineId string, collectDeliverGroupIds []string) *lcos_error.LCOSError {
	searchMap := make(map[string]interface{})
	searchMap["line_id"] = lineId
	searchMap["collect_deliver_group_id"] = collectDeliverGroupIds
	query := ctx.ReadDB().Table(basic_location.GetBasicLocationGroupTableName(lineId)).Where(searchMap).Distinct("collect_deliver_group_id")
	var count int64
	d := query.Count(&count)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v, tablename=%s", d.GetError(), basic_location.GetBasicLocationGroupTableName(lineId))
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	if count != int64(len(collectDeliverGroupIds)) {
		return lcos_error.NewLCOSError(lcos_error.NotFoundGroupInLocationOrPostcodeErrorCode,
			"not found collect deliver group in location")
	}
	return nil
}

func checkPostcode(ctx utils.LCOSContext, lineId string, collectDeliverGroupIds []string) *lcos_error.LCOSError {
	searchMap := make(map[string]interface{})
	searchMap["line_id"] = lineId
	searchMap["collect_deliver_group_id"] = collectDeliverGroupIds
	query := ctx.ReadDB().Table(basic_postcode.GetBasicPostcodeTableName(lineId)).Where(searchMap).Distinct("collect_deliver_group_id")
	var count int64
	d := query.Count(&count)
	if d.GetError() != nil {
		logger.LogErrorf("search partition data query count fail| err=%v, tablename=%s", d.GetError(), basic_location.GetBasicLocationGroupTableName(lineId))
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
	}
	if count != int64(len(collectDeliverGroupIds)) {
		return lcos_error.NewLCOSError(lcos_error.NotFoundGroupInLocationOrPostcodeErrorCode,
			"not found collect deliver group in postcode")
	}
	return nil
}

func checkCepRange(ctx utils.LCOSContext, lineId string, collectDeliverGroupIds []string) *lcos_error.LCOSError {
	searchMap := make(map[string]interface{})
	searchMap["line_id"] = lineId
	searchMap["collect_deliver_group_id"] = collectDeliverGroupIds
	query := ctx.ReadDB().Table(cep_range.LineBasicServiceableCepRangeTabTableName).Where(searchMap).Distinct("collect_deliver_group_id")
	var count int64
	d := query.Count(&count)
	if d.GetError() != nil {
		logger.LogErrorf("search cep range data query count fail| err=%v, tableName=%s", d.GetError(), cep_range.LineBasicServiceableCepRangeTabTableName)
		return lcos_error.NewLCOSError(lcos_error.DBReadErrorCode, d.GetError().Error())
	}
	if count != int64(len(collectDeliverGroupIds)) {
		return lcos_error.NewLCOSError(lcos_error.SupportCepRangeButNotSupportBasicErrorCode,
			"not found collect deliver group in cep range")
	}
	return nil
}

func (s *LineBasicServiceableConfService) CheckCollectDeliverGroupWithLine(ctx utils.LCOSContext, lineId, groupId string) (*LineCollectDeliverGroupInfo, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, lineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}

	confModel, err := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineId(ctx, lineId)
	if err != nil {
		return nil, err
	}
	if confModel == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundBasicConfErrorCode, "line basic conf not found")
	}

	groupIdList := []string{}
	groupIdList = append(groupIdList, groupId)
	lineGroupConfModels, err := s.collectDeliverGroupService.GetAllCollectDeliverByGroupIds(ctx, groupIdList)
	if err != nil {
		return nil, err
	}

	if len(lineGroupConfModels) != 1 {
		logger.CtxLogErrorf(ctx, "can not find collect deliver group model with group id:%s", groupId)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundCollectDeliverGroupErrorCode, "can not find collect deliver group model with this group id")
	}

	group := lineGroupConfModels[0]
	status := serviceable_util.GetEffectiveStatus(confModel.CollectDeliverAbility, group.PickupMode, group.DeliverMode)
	result := &LineCollectDeliverGroupInfo{
		LineCollectDeliverGroupConfTab: group,
		Effective:                      &status,
	}

	return result, nil
}

//获取所有的服务范围配置的location信息的line列表
func (s *LineBasicServiceableConfService) GetAllLinesUsingLocationCheckedList(ctx utils.LCOSContext, request *basic_serviceable.ExportLinesUsingLocationChecked) ([]*LineUsingLocationCheckedTab, *lcos_error.LCOSError) {
	var models []*LineUsingLocationCheckedTab

	//查找sa中 对应市场所有符合条件的line信息 -》 所有的lineid
	cond := &basic_serviceable.SearchAllLineBasicConfRequest{
		IsCheckServiceable: constant.ENABLED,
		Region:             *request.Region,
	}

	CheckLinesInfo, err := s.lineBasicServiceableConfDAO.SearchAllSALineLocation(ctx, cond)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get CheckLinesInfo from SA err=%v", err)
		models = append(models, &LineUsingLocationCheckedTab{
			ErrorMessage: "Failed to get all CheckLines info due to system error",
		})
		return models, nil
	}

	linesWithLocation := make(map[string]bool)
	requiredLineIDList := make([]string, len(CheckLinesInfo))
	for index, item := range CheckLinesInfo {
		if item.OriginServiceableType == constant.LOCATION || item.DestinationServiceableType == constant.LOCATION {
			requiredLineIDList[index] = item.LineId
			linesWithLocation[item.LineId] = true
		}
	}

	//// 请求lls获取所有的line信息
	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, requiredLineIDList)
	//if lcosErr != nil {
	//	logger.CtxLogErrorf(ctx, "get lineInfoMap from lls  err=%v", lcosErr)
	//	models = append(models, &LineUsingLocationCheckedTab{
	//		ErrorMessage: "Failed to get all lineInfoMap due to system error can't get lineInfoMap from lls",
	//	})
	//	return models, nil
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}

	productIDToLevels := make(map[int]uint8)

	//获取所有product
	channelMap, lcosErr := product_service.GetAllProducts(ctx, *request.Region)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "get GetAllProducts from lps region=[%v], err=%v", request.Region, lcosErr)
		models = append(models, &LineUsingLocationCheckedTab{
			ErrorMessage: "Failed to get all products info due to system error",
		})
		return models, nil
	}

	for _, product := range channelMap {
		//遍历所有的产品，获取符合场景的product
		ScenarioInfo, lcosErr := product_service.GetScenarioByProduct(ctx, product.ChannelID, *request.Region)
		if lcosErr != nil {
			//获取场景出错
			logger.CtxLogErrorf(ctx, "get ScenarioInfo from lps productId=[%v], err=%v", product.ChannelID, lcosErr)
			models = append(models, &LineUsingLocationCheckedTab{
				ErrorMessage: "Failed to get scenario info due to system error",
				ProductID:    uint64(product.ChannelID),
			})
			continue
		}
		if ScenarioInfo.List == nil {
			continue
		}

		var maxLocationLevel uint8 = 0
		if len(ScenarioInfo.List[0].InfoLists) == 0 {
			continue
		}
		for _, scenarioInfo := range ScenarioInfo.List[0].InfoLists { //所有场景
			if scenarioInfo.CheckReceiver == nil || scenarioInfo.CheckSender == nil {
				continue
			}
			if *scenarioInfo.CheckReceiver == constant.DISABLED && *scenarioInfo.CheckSender == constant.DISABLED {
				continue
			}
			if scenarioInfo.ReceiverCheckLevel != nil {
				if *scenarioInfo.ReceiverCheckLevel > maxLocationLevel {
					maxLocationLevel = *scenarioInfo.ReceiverCheckLevel
				}
			}
			if scenarioInfo.SenderCheckLevel != nil {
				if *scenarioInfo.SenderCheckLevel > maxLocationLevel {
					maxLocationLevel = *scenarioInfo.SenderCheckLevel
				}
			}
		}
		if maxLocationLevel != 0 {
			productIDToLevels[product.ChannelID] = maxLocationLevel
		}
	}

	//卖家或买家 使用location校验的product
	for kProductID, vLevel := range productIDToLevels {
		if kProductID == 0 {
			continue
		}
		//获取该product对应的所有line信息
		lineInfos, lcosErr := s.getAllLinesfromProduct(ctx, kProductID, *request.Region)
		if lcosErr != nil {
			//获取line出错
			logger.CtxLogErrorf(ctx, "get lineInfo from lps,productId=[%v], err=%v", kProductID, lcosErr)
			models = append(models, &LineUsingLocationCheckedTab{
				ErrorMessage:   "Failed to get line info due to system error",
				ValidatedLevel: LocationLevelMap[vLevel],
				ProductID:      uint64(kProductID),
			})
			continue
		}
		if lineInfos == nil {
			continue
		}

		for lineId, lineName := range lineInfos {
			//通过服务范围校验的line
			if _, ok := linesWithLocation[lineId]; !ok {
				continue
			} else {
				models = append(models, &LineUsingLocationCheckedTab{
					LineId:         lineId,
					LineName:       lineName,
					ProductID:      uint64(kProductID),
					ValidatedLevel: LocationLevelMap[vLevel],
				})
			}
		}
	}
	return models, nil
}

func (s *LineBasicServiceableConfService) getAllLinesfromProduct(ctx utils.LCOSContext, ProductID int, region string) (map[string]string, *lcos_error.LCOSError) {

	lineIdToLineName := make(map[string]string)
	productInfos, lcosErr := product_service.GetLaneAndLineInfoByProduct(ctx, ProductID, region)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(productInfos.LaneList) == 0 {
		return nil, nil
	}
	for _, laneList := range productInfos.LaneList {
		if len(laneList.LineList) == 0 {
			continue
		}
		for _, lineList := range laneList.LineList {
			lineIdToLineName[lineList.GetLineId()] = lineList.GetLineName()
		}
	}

	return lineIdToLineName, nil
}

func (s *LineBasicServiceableConfService) GetBasicServiceableConfBaseModelByLineId(ctx utils.LCOSContext, lineId string) (*model.LineBasicServiceableConfTab, *lcos_error.LCOSError) {
	return s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineId(ctx, lineId)
}
