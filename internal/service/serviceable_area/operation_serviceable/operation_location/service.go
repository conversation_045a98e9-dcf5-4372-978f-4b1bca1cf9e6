package operation_location

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	basicLocationModel "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	originLocationModel "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/mohae/deepcopy"
)

type LineOperationServiceableLocationServiceInterface interface {
	CreateOperationServiceableOriginLocation(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationOriginLocationRequest) (*originLocationModel.LogisticLineOperationServiceableOriginLocationTab, *lcos_error.LCOSError)
	BatchCreateOperationServiceableOriginLocation(ctx utils.LCOSContext, request *operation_serviceable.BatchCreateOperationOriginLocationRequest) ([]*originLocationModel.LogisticLineOperationServiceableOriginLocationTab, *lcos_error.LCOSError)
	CreateOperationServiceableDestLocation(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationDestLocationRequest) (*operation_location.LogisticLineOperationServiceableDestLocationTab, *lcos_error.LCOSError)
	BatchCreateOperationServiceableDestLocation(ctx utils.LCOSContext, request *operation_serviceable.BatchCreateOperationDestLocationRequest) ([]*operation_location.LogisticLineOperationServiceableDestLocationTab, *lcos_error.LCOSError)
	UpdateOperationServiceableOriginLocation(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationOriginLocationRequest) (*originLocationModel.LogisticLineOperationServiceableOriginLocationTab, *lcos_error.LCOSError)
	UpdateOperationServiceableDestLocation(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationDestLocationRequest) (*operation_location.LogisticLineOperationServiceableDestLocationTab, *lcos_error.LCOSError)
	GetOperationServiceableOriginLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationOriginLocationListRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllOperationServiceableOriginLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationOriginLocationRequest) ([]*originLocationModel.LogisticLineOperationServiceableOriginLocationTab, *lcos_error.LCOSError)
	GetOperationServiceableDestLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationDestLocationListRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllOperationServiceableDestLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationDestLocationRequest) ([]*operation_location.LogisticLineOperationServiceableDestLocationTab, *lcos_error.LCOSError)
	DeleteOperationServiceableOriginLocation(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	DeleteOperationServiceableDestLocation(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	UploadOperationServiceableOriginLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	UploadOperationServiceableDestLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError

	// 创建运营层location
	CreateOperationServiceableLocation(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationLocationRequest) (*originLocationModel.LineOperationServiceableLocationTab, *lcos_error.LCOSError)
	// 批量创建运营层location
	BatchCreateOperationServiceableLocation(ctx utils.LCOSContext, request *operation_serviceable.BatchCreateOperationLocationRequest) ([]*originLocationModel.LineOperationServiceableLocationTab, *lcos_error.LCOSError)
	// 更新运营层location
	UpdateOperationServiceableLocation(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationLocationRequest) (*originLocationModel.LineOperationServiceableLocationTab, *lcos_error.LCOSError)
	// 获取运营层location分页列表
	GetOperationServiceableLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationLocationListRequest) (*common.PageModel, *lcos_error.LCOSError)
	// 获取运营层所有location
	GetAllOperationServiceableLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationLocationRequest) ([]*originLocationModel.LineOperationServiceableLocationTab, *lcos_error.LCOSError)
	// 删除运营层location
	DeleteOperationServiceableLocation(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError

	// 删除运营层location
	DeleteOperationServiceableLocationByLineId(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError

	// 上传location
	UploadOperationServiceableLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	ParseAndImportOperationLocationSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError)
	// 同步搜索的location到基础层
	SyncSearchLocationToBasicserviceable(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationLocationRequest) *lcos_error.LCOSError
	// 搜索四级地址
	SearchAllFourLevelAddress(ctx utils.LCOSContext, request *common_protocol.SearchAllFourLevelAddressRequest) (*serviceable_core_logic.FourLevelAddress, *lcos_error.LCOSError)
}

type LineOperationServiceableLocationService struct {
	lineOperationServiceableLocationDAO originLocationModel.LogisticLineOperationServiceableLocationTabDAO
	lineBasicServiceableLocationDao     basicLocationModel.LineBasicServiceableLocationDAO
	scheduledService                    scheduled.ScheduledService
}

func (s *LineOperationServiceableLocationService) DeleteOperationServiceableLocationByLineId(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError {
	return s.lineOperationServiceableLocationDAO.DeleteLineOperationServiceableLocationTabsByLineId(ctx, lineId)
}

func NewLineOperationServiceableLocationService(lineOperationServiceableLocationDAO originLocationModel.LogisticLineOperationServiceableLocationTabDAO,
	lineBasicServiceableLocationDao basicLocationModel.LineBasicServiceableLocationDAO, scheduledService scheduled.ScheduledService) *LineOperationServiceableLocationService {
	return &LineOperationServiceableLocationService{
		lineOperationServiceableLocationDAO: lineOperationServiceableLocationDAO,
		lineBasicServiceableLocationDao:     lineBasicServiceableLocationDao,
		scheduledService:                    scheduledService,
	}
}

// 创建运营层location
func (s *LineOperationServiceableLocationService) CreateOperationServiceableLocation(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationLocationRequest) (*originLocationModel.LineOperationServiceableLocationTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	//exist, err := lls_service.CheckLineExist(ctx, request.LineID)
	//if err != nil {
	//return nil, err
	//}
	//if !exist {
	//logger.LogErrorf("create operation location failed, line_id not exist|lineId=%s", request.LineID)
	//return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	//}

	locationModel := new(originLocationModel.LineOperationServiceableLocationTab)
	if err := fillLocationModel(ctx, locationModel, request); err != nil {
		return nil, err
	}
	locationModel.LineID = request.LineID
	if _, err := s.lineOperationServiceableLocationDAO.CreateLineOperationServiceableLocationTab(ctx, locationModel); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// 批量创建运营层location
func (s *LineOperationServiceableLocationService) BatchCreateOperationServiceableLocation(ctx utils.LCOSContext, request *operation_serviceable.BatchCreateOperationLocationRequest) ([]*originLocationModel.LineOperationServiceableLocationTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	//exist, err := lls_service.CheckLineExist(ctx, request.LineId)
	//if err != nil {
	//return nil, err
	//}
	//if !exist {
	//logger.LogErrorf("batch create operation location failed, line_id not exist|lineId=%s", request.LineId)
	//return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	//}

	locationModels := make([]*operation_location.LineOperationServiceableLocationTab, 0, len(request.LocationId))
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	wg.Add(len(request.LocationId))
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for _, locationId := range request.LocationId {
		limitChannel <- struct{}{}
		go func(singleLocationId uint64) {
			locationModel := new(operation_location.LineOperationServiceableLocationTab)
			if err := fillLocationModel(ctx, locationModel, &operation_serviceable.CreateOperationLocationRequest{
				BaseLocationInfo: operation_serviceable.BaseLocationInfo{
					LineID:     request.LineId,
					Region:     request.Region,
					LocationID: singleLocationId,
				},
				CollectDeliverGroupId: request.CollectDeliverGroupId,
				DisablePickup:         request.DisablePickup,
				DisableCodPickup:      request.DisableCodPickup,
				DisableDeliver:        request.DisableDeliver,
				DisableCodDeliver:     request.DisableCodDeliver,
				DisableTradeIn:        request.DisableTradeIn,
			}); err != nil {
				errList = append(errList, err)
			} else {
				locationModel.LineID = request.LineId
				locationModels = append(locationModels, locationModel)
			}
			<-limitChannel
			wg.Done()
		}(locationId)
	}
	wg.Wait()

	if len(errList) != 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.BatchCreateLocationErrorCode, lcos_error.GetMessageFromList(errList))
	}

	// 先批量删除，再批量新增
	if err := s.lineOperationServiceableLocationDAO.BatchDeleteLineOperationServiceableLocationTabs(ctx, locationModels); err != nil {
		return nil, err
	}
	if _, err := s.lineOperationServiceableLocationDAO.BatchCreateLineOperationServiceableLocationTab(ctx, locationModels); err != nil {
		return nil, err
	}
	return locationModels, nil
}

// 更新运营层location
func (s *LineOperationServiceableLocationService) UpdateOperationServiceableLocation(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationLocationRequest) (*originLocationModel.LineOperationServiceableLocationTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	operationLocationModel, err := s.lineOperationServiceableLocationDAO.GetLineOperationServiceableLocationTabById(ctx, request.ID)
	if err != nil {
		return nil, err
	}
	if operationLocationModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundOperationLocationErrorCode)
	}

	dbDisablePickup := *operationLocationModel.DisablePickup
	dbDisableCodPickup := *operationLocationModel.DisableCodPickup
	dbDisableDeliver := *operationLocationModel.DisableDeliver
	dbDisableCodDeliver := *operationLocationModel.DisableCodDeliver
	if err := fillLocationModel(ctx, operationLocationModel, &request.CreateOperationLocationRequest); err != nil {
		return nil, err
	}
	if _, err := s.lineOperationServiceableLocationDAO.UpdateLineOperationServiceableLocationTab(ctx, operationLocationModel); err != nil {
		return nil, err
	}

	// 如果运营层一项或多项从Ban改为不Ban, 需要warning机制
	searchMap := make(map[string]interface{})
	searchMap["line_id"] = request.LineID
	searchMap["collect_deliver_group_id"] = request.CollectDeliverGroupId
	searchMap["location_id"] = request.LocationID
	basicLocationModels, err := s.lineBasicServiceableLocationDao.SearchAllBasicServiceableLocation(ctx, request.LineID, searchMap)
	if err != nil {
		return nil, err
	}
	if len(basicLocationModels) == 0 { // 如果基础层不存在，则直接返回
		return operationLocationModel, nil
	}
	basicLocation := basicLocationModels[0]
	if (dbDisablePickup == 1 && *request.DisablePickup == 0 && *basicLocation.CanPickup == 0) ||
		(dbDisableCodPickup == 1 && *request.DisableCodPickup == 0 && *basicLocation.CanCodPickup == 0) ||
		(dbDisableDeliver == 1 && *request.DisableDeliver == 0 && *basicLocation.CanDeliver == 0) ||
		(dbDisableCodDeliver == 1 && *request.DisableCodDeliver == 0 && *basicLocation.CanCodDeliver == 0) {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.SupportOperationLocationButNotSupportBasicErrorCode)
	}

	return operationLocationModel, nil
}

// 获取运营层location分页列表
func (s *LineOperationServiceableLocationService) GetOperationServiceableLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationLocationListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	searchMap, _ := utils.Struct2map(request)
	models, total, err := s.lineOperationServiceableLocationDAO.SearchLineOperationServiceableLocationTab(ctx, pageNo, count, searchMap)
	if err != nil {
		return nil, err
	}
	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// 获取运营层所有location
func (s *LineOperationServiceableLocationService) GetAllOperationServiceableLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationLocationRequest) ([]*originLocationModel.LineOperationServiceableLocationTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	searchMap, _ := utils.Struct2map(request)
	models, err := s.lineOperationServiceableLocationDAO.SearchAllLineOperationServiceableLocationTab(ctx, searchMap)
	if err != nil {
		return nil, err
	}
	return models, nil
}

// 删除运营层location
func (s *LineOperationServiceableLocationService) DeleteOperationServiceableLocation(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	return s.lineOperationServiceableLocationDAO.DeleteLineOperationServiceableLocationTabsById(ctx, request.ID)
}

// TODO 上传运营层location
func (s *LineOperationServiceableLocationService) UploadOperationServiceableLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	job, err := s.scheduledService.CreateScheduledJob(ctx, request.Region, scheduled_constant.OperationServiceableArea, scheduled_constant.Location, request.FileUrl, request.IsScheduled, request.ScheduledTime, ctx.GetUserEmail())
	if err != nil {
		return err
	}

	go func() {
		parseFileResult, _ := s.ParseAndImportOperationLocationSA(ctx, request, serviceable_util.LineOperationLocationHeader, true)
		if err := s.scheduledService.UpdateValidateResult(ctx, job, parseFileResult); err != nil {
			logger.CtxLogErrorf(ctx, "scheduled job update validate result error|job_id=%d, cause=%s", job.ID, err.Msg)
		}
	}()

	return nil
}

func (s *LineOperationServiceableLocationService) ParseAndImportOperationLocationSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}

	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return result, nil
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
		return result, nil
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
		return result, nil
	}
	defer os.Remove(filePath)

	uniqueKeyMap := make(map[string]int)
	lineIdMap := map[string]bool{}
	var lineIdSet []string
	var rowDataList []*serviceable_util.LineOperationLocationRowData
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount()

		rowData := &serviceable_util.LineOperationLocationRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}
		if request.Region != rowData.Region {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", request.Region, rowData.RowId))
			continue
		}
		uniqueKey := utils.GenKey("-", rowData.LineId, rowData.GroupId, rowData.State, rowData.City, rowData.District, rowData.Street)
		if lastRowId, ok := uniqueKeyMap[uniqueKey]; ok {
			result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import data duplicated with same Line-Group-Address, please check | row %d duplicate with row %d, unique key=%s", rowData.RowId, lastRowId, uniqueKey)
			return result, nil
		} else {
			uniqueKeyMap[uniqueKey] = rowData.RowId
		}

		if _, ok := lineIdMap[rowData.LineId]; !ok {
			lineIdSet = append(lineIdSet, rowData.LineId)
			lineIdMap[rowData.LineId] = true
		}

		rowDataList = append(rowDataList, rowData)
	}

	// 批量line_id合法性校验
	if len(lineIdSet) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is empty")
		return result, nil
	}

	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIdSet)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return result, nil
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}
	//lineDraftMap, lcosErr := lls_service.BatchGetLineDraftsMap(ctx, lineIdSet)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return result, nil
	//}
	//for k, v := range lineDraftMap {
	//	lineInfoMap[k] = v
	//}
	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(ctx, request.Region, lineIdSet, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return result, nil
	}
	var addLocationModels []*operation_location.LineOperationServiceableLocationTab
	var deleteLocationModels []*operation_location.LineOperationServiceableLocationTab
	for _, rowData := range rowDataList {
		// 1. 校验lindId是否存在
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}
		// 2. 校验地址是否存在
		locationInfo, err := address_service.LocationServer.GetLocationInfoByName(ctx, rowData.Region, rowData.State, rowData.City, rowData.District, rowData.Street)
		if err != nil {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Address invalid, %s | row=%d", err.Msg, rowData.RowId))
			continue
		}

		locationModel := &operation_location.LineOperationServiceableLocationTab{
			LineID:                rowData.LineId,
			CollectDeliverGroupId: rowData.GroupId,
			Region:                rowData.Region,
			LocationID:            uint64(locationInfo.GetLocationId()),
			State:                 rowData.State,
			City:                  rowData.City,
			District:              rowData.District,
			Street:                rowData.Street,
			DisablePickup:         &rowData.BanPickup,
			DisableCodPickup:      &rowData.BanCodPickup,
			DisableDeliver:        &rowData.BanDeliver,
			DisableCodDeliver:     &rowData.BanCodDeliver,
			DisableTradeIn:        rowData.BanTradeIn,
		}
		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			addLocationModels = append(addLocationModels, locationModel)
		} else {
			deleteLocationModels = append(deleteLocationModels, locationModel)
		}
	}

	if async {
		return result, nil
	}
	fc := func() *lcos_error.LCOSError {
		if err := s.lineOperationServiceableLocationDAO.BatchDeleteLineOperationServiceableLocationTabs(ctx, addLocationModels); err != nil {
			return err
		}
		if _, err := s.lineOperationServiceableLocationDAO.BatchCreateLineOperationServiceableLocationTab(ctx, addLocationModels); err != nil {
			return err
		}
		if err := s.lineOperationServiceableLocationDAO.BatchDeleteLineOperationServiceableLocationTabs(ctx, deleteLocationModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return result, err
	}
	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(lineIdSet, ","))
	return result, nil
}

// TODO 同步搜索的location到基础层
func (s *LineOperationServiceableLocationService) SyncSearchLocationToBasicserviceable(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationLocationRequest) *lcos_error.LCOSError {
	searchMap, _ := utils.Struct2map(request)
	if ctx.GetClientType() > 0 {
		allowLineIds, err := lls_service.GetLineIdsByClientType(ctx, ctx.GetClientType())
		if err != nil {
			return err
		}
		if len(allowLineIds) == 0 {
			return nil
		}
		searchMap["LINE_ID"] = allowLineIds
	}
	models, err := s.lineOperationServiceableLocationDAO.SearchAllLineOperationServiceableLocationTab(ctx, searchMap)
	if err != nil {
		return err
	}
	if len(models) == 0 {
		return nil
	}

	updateLocationModels := make([]*basicLocationModel.LineBasicServiceableLocationTab, 0)
	insertLocationModels := make([]*basicLocationModel.LineBasicServiceableLocationTab, 0)

	var searchBasicLocationModels []*basicLocationModel.LineBasicServiceableLocationTab
	operationLocationMap := make(map[string]*operation_location.LineOperationServiceableLocationTab)
	basicLocationMap := make(map[string]*basicLocationModel.LineBasicServiceableLocationTab)

	// 构造查询条件，查询basic层location
	querySlice := make([][]interface{}, 0)
	for _, operationLocation := range models {
		queryItem := make([]interface{}, 0)
		queryItem = append(queryItem, operationLocation.LineID, operationLocation.LocationID, operationLocation.CollectDeliverGroupId)
		querySlice = append(querySlice, queryItem)

		uniqKey := fmt.Sprintf("%s-%v-%s", operationLocation.LineID, operationLocation.LocationID, operationLocation.CollectDeliverGroupId)
		operationLocationMap[uniqKey] = operationLocation
	}
	tableName := basicLocationModel.GetBasicLocationGroupTableName(request.LineId)
	db := ctx.ReadDB().Table(tableName).Where("(`line_id`,`location_id`,`collect_deliver_group_id`) in ?", querySlice).Find(&searchBasicLocationModels)
	if db.GetError() != nil {
		logger.LogErrorf("batch query basic location err:%s", db.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}
	for _, basicLocation := range searchBasicLocationModels {
		uniqKey := fmt.Sprintf("%s-%v-%s", basicLocation.LineId, basicLocation.LocationId, basicLocation.CollectDeliverGroupId)
		basicLocationMap[uniqKey] = basicLocation
	}

	for k, operationLocation := range operationLocationMap {
		if basicLocation, ok := basicLocationMap[k]; ok {
			updateFlag := false
			copyLocation := deepcopy.Copy(basicLocation)
			updateBasicLocation := copyLocation.(*basicLocationModel.LineBasicServiceableLocationTab)
			if *operationLocation.DisablePickup == 1 && *basicLocation.CanPickup == 1 {
				updateFlag = true
				var pickup = uint8(0)
				updateBasicLocation.CanPickup = &pickup
			}
			if *operationLocation.DisableCodPickup == 1 && *basicLocation.CanCodPickup == 1 {
				updateFlag = true
				var codPickup = uint8(0)
				updateBasicLocation.CanCodPickup = &codPickup
			}
			if *operationLocation.DisableDeliver == 1 && *basicLocation.CanDeliver == 1 {
				updateFlag = true
				var dilver = uint8(0)
				updateBasicLocation.CanDeliver = &dilver
			}
			if *operationLocation.DisableCodDeliver == 1 && *basicLocation.CanCodDeliver == 1 {
				updateFlag = true
				var codDeliver = uint8(0)
				updateBasicLocation.CanCodDeliver = &codDeliver
			}
			if updateFlag {
				updateLocationModels = append(updateLocationModels, updateBasicLocation)
			}
		} else {
			canPickup := 1 - *operationLocation.DisablePickup
			canCodPickup := 1 - *operationLocation.DisableCodPickup
			canDeliver := 1 - *operationLocation.DisableDeliver
			canCodDeliver := 1 - *operationLocation.DisableCodDeliver
			insertBasicLocation := &basicLocationModel.LineBasicServiceableLocationTab{
				LineId:                operationLocation.LineID,
				CollectDeliverGroupId: operationLocation.CollectDeliverGroupId,
				Region:                operationLocation.Region,
				LocationId:            operationLocation.LocationID,
				State:                 operationLocation.State,
				City:                  operationLocation.City,
				District:              operationLocation.District,
				Street:                operationLocation.Street,
				CanPickup:             &canPickup,
				CanCodPickup:          &canCodPickup,
				CanDeliver:            &canDeliver,
				CanCodDeliver:         &canCodDeliver,
			}
			insertLocationModels = append(insertLocationModels, insertBasicLocation)
		}
	}

	fc := func() *lcos_error.LCOSError {
		if len(insertLocationModels) > 0 {
			_, err := s.lineBasicServiceableLocationDao.BatchCreateBasicServiceableLocationModel(ctx, request.LineId, insertLocationModels)
			if err != nil {
				return err
			}
		}
		for _, updateModel := range updateLocationModels {
			_, err := s.lineBasicServiceableLocationDao.UpdateBasicServiceableLocationModel(ctx, updateModel)
			if err != nil {
				return err
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}
	return nil
}

// 搜索四级地址
func (s *LineOperationServiceableLocationService) SearchAllFourLevelAddress(ctx utils.LCOSContext, request *common_protocol.SearchAllFourLevelAddressRequest) (*serviceable_core_logic.FourLevelAddress, *lcos_error.LCOSError) {
	searchMap := make(map[string]interface{})
	searchMap["line_id"] = request.LineId
	searchMap["region"] = request.Region
	searchMap["collect_deliver_group_id"] = request.CollectDeliverGroupId
	locationList, err := s.lineOperationServiceableLocationDAO.SearchAllLineOperationServiceableLocationTab(ctx, searchMap)
	if err != nil {
		logger.LogErrorf("find four level address err, %s", err)
		return nil, err
	}
	stateMap := make(map[string]struct{})
	cityMap := make(map[string]struct{})
	districtMap := make(map[string]struct{})
	streetMap := make(map[string]struct{})

	stateList := make([]string, 0)
	cityList := make([]string, 0)
	districtList := make([]string, 0)
	streetList := make([]string, 0)

	for _, location := range locationList {
		if _, ok := stateMap[location.State]; !ok {
			if len(location.State) > 0 {
				stateMap[location.State] = struct{}{}
				stateList = append(stateList, location.State)
			}
		}
		if _, ok := cityMap[location.City]; !ok {
			if len(location.City) > 0 {
				cityMap[location.City] = struct{}{}
				cityList = append(cityList, location.City)
			}
		}
		if _, ok := districtMap[location.District]; !ok {
			if len(location.District) > 0 {
				districtMap[location.District] = struct{}{}
				districtList = append(districtList, location.District)
			}
		}
		if _, ok := streetMap[location.Street]; !ok {
			if len(location.Street) > 0 {
				streetMap[location.Street] = struct{}{}
				streetList = append(streetList, location.Street)
			}
		}
	}
	fourLevelAddress := &serviceable_core_logic.FourLevelAddress{
		StateList:    stateList,
		CityList:     cityList,
		DistrictList: districtList,
		StreetList:   streetList,
	}
	return fourLevelAddress, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) CreateOperationServiceableOriginLocation(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationOriginLocationRequest) (*originLocationModel.LogisticLineOperationServiceableOriginLocationTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	if !exists {
		logger.LogErrorf("create origin location failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}

	locationModel := new(originLocationModel.LogisticLineOperationServiceableOriginLocationTab)
	if err := fillOriginLocationModel(ctx, locationModel, request); err != nil {
		return nil, err
	}
	locationModel.LineID = request.LineID
	if _, err := s.lineOperationServiceableLocationDAO.CreateLogisticLineOperationServiceableOriginLocationTab(ctx, locationModel); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) CreateOperationServiceableDestLocation(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationDestLocationRequest) (*operation_location.LogisticLineOperationServiceableDestLocationTab, *lcos_error.LCOSError) {
	exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if e != nil {
		return nil, e
	}
	if !exists {
		logger.LogErrorf("create origin dest failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}

	locationModel := new(operation_location.LogisticLineOperationServiceableDestLocationTab)
	if err := fillDestLocationModel(ctx, locationModel, request); err != nil {
		return nil, err
	}
	locationModel.LineID = request.LineID
	if _, err := s.lineOperationServiceableLocationDAO.CreateLogisticLineOperationServiceableDestLocationTab(ctx, locationModel); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) UpdateOperationServiceableOriginLocation(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationOriginLocationRequest) (*originLocationModel.LogisticLineOperationServiceableOriginLocationTab, *lcos_error.LCOSError) {
	locationModel := new(originLocationModel.LogisticLineOperationServiceableOriginLocationTab)
	if err := fillOriginLocationModel(ctx, locationModel, &request.CreateOperationOriginLocationRequest); err != nil {
		return nil, err
	}
	locationModel.ID = request.ID
	if _, err := s.lineOperationServiceableLocationDAO.UpdateLogisticLineOperationServiceableOriginLocationTab(ctx, locationModel); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) UpdateOperationServiceableDestLocation(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationDestLocationRequest) (*operation_location.LogisticLineOperationServiceableDestLocationTab, *lcos_error.LCOSError) {
	locationModel := new(operation_location.LogisticLineOperationServiceableDestLocationTab)
	if err := fillDestLocationModel(ctx, locationModel, &request.CreateOperationDestLocationRequest); err != nil {
		return nil, err
	}
	locationModel.ID = request.ID
	if _, err := s.lineOperationServiceableLocationDAO.UpdateLogisticLineOperationServiceableDestLocationTab(ctx, locationModel); err != nil {
		return nil, err
	}
	return locationModel, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) GetOperationServiceableOriginLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationOriginLocationListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	searchMap, _ := utils.Struct2map(request)
	models, total, err := s.lineOperationServiceableLocationDAO.SearchLogisticLineOperationServiceableOriginLocationTab(ctx, pageNo, count, searchMap)
	if err != nil {
		return nil, err
	}
	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) GetAllOperationServiceableOriginLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationOriginLocationRequest) ([]*operation_location.LogisticLineOperationServiceableOriginLocationTab, *lcos_error.LCOSError) {
	searchMap, _ := utils.Struct2map(request)
	models, err := s.lineOperationServiceableLocationDAO.SearchAllLogisticLineOperationServiceableOriginLocationTab(ctx, searchMap)
	if err != nil {
		return nil, err
	}
	return models, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) GetOperationServiceableDestLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationDestLocationListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	searchMap, _ := utils.Struct2map(request)
	models, total, err := s.lineOperationServiceableLocationDAO.SearchLogisticLineOperationServiceableDestLocationTab(ctx, pageNo, count, searchMap)
	if err != nil {
		return nil, err
	}
	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) GetAllOperationServiceableDestLocationList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationDestLocationRequest) ([]*operation_location.LogisticLineOperationServiceableDestLocationTab, *lcos_error.LCOSError) {
	searchMap, _ := utils.Struct2map(request)
	models, err := s.lineOperationServiceableLocationDAO.SearchAllLogisticLineOperationServiceableDestLocationTab(ctx, searchMap)
	if err != nil {
		return nil, err
	}
	return models, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) DeleteOperationServiceableOriginLocation(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	return s.lineOperationServiceableLocationDAO.DeleteLogisticLineOperationServiceableOriginLocationTabsById(ctx, request.ID)
}

// Deprecated
func (s *LineOperationServiceableLocationService) DeleteOperationServiceableDestLocation(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	return s.lineOperationServiceableLocationDAO.DeleteLogisticLineOperationServiceableDestLocationTabsById(ctx, request.ID)
}

// Deprecated
func (s *LineOperationServiceableLocationService) BatchCreateOperationServiceableOriginLocation(ctx utils.LCOSContext, request *operation_serviceable.BatchCreateOperationOriginLocationRequest) ([]*operation_location.LogisticLineOperationServiceableOriginLocationTab, *lcos_error.LCOSError) {
	exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId)
	if e != nil {
		return nil, e
	}
	if !exists {
		logger.LogErrorf("batch create origin location failed, line_id not exist|lineId=%s", request.LineId)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}
	locationModels := make([]*operation_location.LogisticLineOperationServiceableOriginLocationTab, 0, len(request.LocationId))
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	wg.Add(len(request.LocationId))
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for _, locationId := range request.LocationId {
		limitChannel <- struct{}{}
		go func(singleLocationId uint64) {
			locationModel := new(operation_location.LogisticLineOperationServiceableOriginLocationTab)
			if err := fillOriginLocationModel(ctx, locationModel, &operation_serviceable.CreateOperationOriginLocationRequest{
				BaseLocationInfo: operation_serviceable.BaseLocationInfo{
					LineID:     request.LineId,
					Region:     request.Region,
					LocationID: singleLocationId,
				},
				CollectServiceType: request.CollectServiceType,
				DisablePickup:      request.DisablePickup,
				DisableCodPickup:   request.DisableCodPickup,
			}); err != nil {
				errList = append(errList, err)
			} else {
				locationModel.LineID = request.LineId
				locationModels = append(locationModels, locationModel)
			}
			<-limitChannel
			wg.Done()
		}(locationId)
	}
	wg.Wait()

	if len(errList) != 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.BatchCreateLocationErrorCode, lcos_error.GetMessageFromList(errList))
	}

	if _, err := s.lineOperationServiceableLocationDAO.BatchCreateLogisticLineOperationServiceableOriginLocationTab(ctx, locationModels); err != nil {
		return nil, err
	}
	return locationModels, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) BatchCreateOperationServiceableDestLocation(ctx utils.LCOSContext, request *operation_serviceable.BatchCreateOperationDestLocationRequest) ([]*operation_location.LogisticLineOperationServiceableDestLocationTab, *lcos_error.LCOSError) {
	exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineId)
	if e != nil {
		return nil, e
	}
	if !exists {
		logger.LogErrorf("batch create dest location failed, line_id not exist|lineId=%s", request.LineId)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}

	locationModels := make([]*operation_location.LogisticLineOperationServiceableDestLocationTab, 0, len(request.LocationId))
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	wg.Add(len(request.LocationId))
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for _, locationId := range request.LocationId {
		limitChannel <- struct{}{}
		go func(singleLocationId uint64) {
			locationModel := new(operation_location.LogisticLineOperationServiceableDestLocationTab)
			if err := fillDestLocationModel(ctx, locationModel, &operation_serviceable.CreateOperationDestLocationRequest{
				BaseLocationInfo: operation_serviceable.BaseLocationInfo{
					LineID:     request.LineId,
					Region:     request.Region,
					LocationID: singleLocationId,
				},
				DeliverServiceType: request.DeliverServiceType,
				DisableDeliver:     request.DisableDeliver,
				DisableCodDeliver:  request.DisableCodDeliver,
			}); err != nil {
				errList = append(errList, err)
			} else {
				locationModel.LineID = request.LineId
				locationModels = append(locationModels, locationModel)
			}
			<-limitChannel
			wg.Done()
		}(locationId)
	}
	wg.Wait()

	if len(errList) != 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.BatchCreateLocationErrorCode, lcos_error.GetMessageFromList(errList))
	}

	if _, err := s.lineOperationServiceableLocationDAO.BatchCreateLogisticLineOperationServiceableDestLocationTab(ctx, locationModels); err != nil {
		return nil, err
	}
	return locationModels, nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) UploadOperationServiceableOriginLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_operation_origin_location.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	var addLocationModels []*operation_location.LogisticLineOperationServiceableOriginLocationTab
	var deleteLocationModels []*operation_location.LogisticLineOperationServiceableOriginLocationTab
	var mutex sync.Mutex
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		if err := serviceable_util.CheckLocationRowData(row, request.Region, lineNum); err != nil {
			return err
		}

		wg.Add(1)
		limitChannel <- struct{}{}
		go func(rowData []string) {
			rowLineId := rowData[0]
			exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, rowLineId)
			if e != nil {
				errList = append(errList, e)
			}
			if exists {
				logger.LogErrorf("upload location failed, line_id not exist|lineId=%s", rowLineId)
				errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
			}

			locationInfo, err := ops_service.GetLocationInfoByLocationName(ctx, serviceable_util.GetLocationRequestByLocationFileRow(row))
			if err != nil {
				errList = append(errList, err)
			} else {
				rowStruct := serviceable_util.ParseLocationRowData(rowData)
				locationModel := &operation_location.LogisticLineOperationServiceableOriginLocationTab{
					LineID: rowStruct.LineId,
					//CollectServiceType: uint64(rowStruct.ServiceType),
					Region:     request.Region,
					LocationID: uint64(locationInfo.LocationId),
					State:      rowStruct.State,
					City:       rowStruct.City,
					District:   rowStruct.District,
					Street:     rowStruct.Street,
					/*DisablePickup:      &rowStruct.PickupDeliver,
					DisableCodPickup:   &rowStruct.CodPickupDeliver,*/
				}
				mutex.Lock()
				if rowStruct.ActionCode != -1 {
					addLocationModels = append(addLocationModels, locationModel)
				} else {
					deleteLocationModels = append(deleteLocationModels, locationModel)
				}
				mutex.Unlock()
			}
			<-limitChannel
			wg.Done()
		}(row)
	}
	wg.Wait()

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}

	fc := func() *lcos_error.LCOSError {
		if err := s.lineOperationServiceableLocationDAO.BatchDeleteLogisticLineOperationServiceableOriginLocationTabs(ctx, addLocationModels); err != nil {
			return err
		}
		if _, err := s.lineOperationServiceableLocationDAO.BatchCreateLogisticLineOperationServiceableOriginLocationTab(ctx, addLocationModels); err != nil {
			return err
		}
		if err := s.lineOperationServiceableLocationDAO.BatchDeleteLogisticLineOperationServiceableOriginLocationTabs(ctx, deleteLocationModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}

// Deprecated
func (s *LineOperationServiceableLocationService) UploadOperationServiceableDestLocation(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_operation_dest_location.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	var addLocationModels []*operation_location.LogisticLineOperationServiceableDestLocationTab
	var deleteLocationModels []*operation_location.LogisticLineOperationServiceableDestLocationTab
	var mutex sync.Mutex
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		if err := serviceable_util.CheckLocationRowData(row, request.Region, lineNum); err != nil {
			return err
		}

		wg.Add(1)
		limitChannel <- struct{}{}
		go func(rowData []string) {
			rowLineId := rowData[0]
			exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, rowLineId)
			if e != nil {
				errList = append(errList, e)
			}
			if !exists {
				logger.LogErrorf("upload location failed, line_id not exist|lineId=%s", rowLineId)
				errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
			}

			locationInfo, err := ops_service.GetLocationInfoByLocationName(ctx, serviceable_util.GetLocationRequestByLocationFileRow(row))
			if err != nil {
				errList = append(errList, err)
			} else {
				rowStruct := serviceable_util.ParseLocationRowData(rowData)
				locationModel := &operation_location.LogisticLineOperationServiceableDestLocationTab{
					LineID: rowStruct.LineId,
					//DeliverServiceType: uint64(rowStruct.ServiceType),
					Region:     request.Region,
					LocationID: uint64(locationInfo.LocationId),
					State:      rowStruct.State,
					City:       rowStruct.City,
					District:   rowStruct.District,
					Street:     rowStruct.Street,
					/*DisableDeliver:     &rowStruct.PickupDeliver,
					DisableCodDeliver:  &rowStruct.CodPickupDeliver,*/
				}

				mutex.Lock()
				if rowStruct.ActionCode != -1 {
					addLocationModels = append(addLocationModels, locationModel)
				} else {
					deleteLocationModels = append(deleteLocationModels, locationModel)
				}
				mutex.Unlock()
			}
			<-limitChannel
			wg.Done()
		}(row)
	}
	wg.Wait()

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}

	fc := func() *lcos_error.LCOSError {
		if err := s.lineOperationServiceableLocationDAO.BatchDeleteLogisticLineOperationServiceableDestLocationTabs(ctx, addLocationModels); err != nil {
			return err
		}
		if _, err := s.lineOperationServiceableLocationDAO.BatchCreateLogisticLineOperationServiceableDestLocationTab(ctx, addLocationModels); err != nil {
			return err
		}
		if err := s.lineOperationServiceableLocationDAO.BatchDeleteLogisticLineOperationServiceableDestLocationTabs(ctx, deleteLocationModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}

func fillLocationModel(ctx context.Context, locationModel *originLocationModel.LineOperationServiceableLocationTab, request *operation_serviceable.CreateOperationLocationRequest) *lcos_error.LCOSError {
	locationModel.CollectDeliverGroupId = request.CollectDeliverGroupId
	locationModel.LineID = request.LineID
	locationModel.Region = request.Region
	locationModel.LocationID = request.LocationID
	locationModel.DisablePickup = request.DisablePickup
	locationModel.DisableCodPickup = request.DisableCodPickup
	locationModel.DisableDeliver = request.DisableDeliver
	locationModel.DisableCodDeliver = request.DisableCodDeliver
	if request.DisableTradeIn != nil {
		locationModel.DisableTradeIn = *request.DisableTradeIn
	}
	locationInfo, err := ops_service.GetUpLocationInfo(ctx, &ops_service.GetLocationInfoByIdRequest{
		Country:    request.Region,
		LocationId: request.LocationID,
	})

	if err != nil {
		return err
	}

	if locationInfo.State != nil {
		locationModel.State = *locationInfo.State
	}
	if locationInfo.City != nil {
		locationModel.City = *locationInfo.City
	}
	if locationInfo.District != nil {
		locationModel.District = *locationInfo.District
	}
	if locationInfo.Street != nil {
		locationModel.Street = *locationInfo.Street
	}
	return nil
}

// Deprecated
func fillOriginLocationModel(ctx context.Context, locationModel *originLocationModel.LogisticLineOperationServiceableOriginLocationTab, request *operation_serviceable.CreateOperationOriginLocationRequest) *lcos_error.LCOSError {
	locationModel.LineID = request.LineID
	locationModel.CollectServiceType = uint64(request.CollectServiceType)
	locationModel.Region = request.Region
	locationModel.LocationID = request.LocationID
	locationModel.DisablePickup = request.DisablePickup
	locationModel.DisableCodPickup = request.DisableCodPickup

	locationInfo, err := ops_service.GetUpLocationInfo(ctx, &ops_service.GetLocationInfoByIdRequest{
		Country:    request.Region,
		LocationId: request.LocationID,
	})

	if err != nil {
		return err
	}

	if locationInfo.State != nil {
		locationModel.State = *locationInfo.State
	}
	if locationInfo.City != nil {
		locationModel.City = *locationInfo.City
	}
	if locationInfo.District != nil {
		locationModel.District = *locationInfo.District
	}
	if locationInfo.Street != nil {
		locationModel.Street = *locationInfo.Street
	}
	return nil
}

// Deprecated
func fillDestLocationModel(ctx context.Context, locationModel *originLocationModel.LogisticLineOperationServiceableDestLocationTab, request *operation_serviceable.CreateOperationDestLocationRequest) *lcos_error.LCOSError {
	locationModel.DeliverServiceType = uint64(request.DeliverServiceType)
	locationModel.Region = request.Region
	locationModel.LocationID = request.LocationID
	locationModel.DisableDeliver = request.DisableDeliver
	locationModel.DisableCodDeliver = request.DisableCodDeliver

	locationInfo, err := ops_service.GetUpLocationInfo(ctx, &ops_service.GetLocationInfoByIdRequest{
		Country:    request.Region,
		LocationId: request.LocationID,
	})

	if err != nil {
		return err
	}

	if locationInfo.State != nil {
		locationModel.State = *locationInfo.State
	}
	if locationInfo.City != nil {
		locationModel.City = *locationInfo.City
	}
	if locationInfo.District != nil {
		locationModel.District = *locationInfo.District
	}
	if locationInfo.Street != nil {
		locationModel.Street = *locationInfo.Street
	}
	return nil
}
