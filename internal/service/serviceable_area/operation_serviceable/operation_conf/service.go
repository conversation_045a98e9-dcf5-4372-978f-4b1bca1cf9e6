package operation_conf

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	basicModel "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
)

type LineOperationServiceableConfServiceInterface interface {
	CreateOperationServiceableConf(ctx utils.LCOSContext, request *operation_serviceable.CreateOrUpdateOperationServiceableConfRequest) (*FullLineOperationServiceableConfInfo, *lcos_error.LCOSError)
	UpdateOperationServiceableConf(ctx utils.LCOSContext, request *operation_serviceable.CreateOrUpdateOperationServiceableConfRequest) (*FullLineOperationServiceableConfInfo, *lcos_error.LCOSError)
	GetOperationServiceableConfByLineID(ctx utils.LCOSContext, lineID string) (*FullLineOperationServiceableConfInfo, *lcos_error.LCOSError)
	GetOperationServiceableConfList(ctx utils.LCOSContext, request *operation_serviceable.OperationServiceableConfListRequest) (*common.PageModel, *lcos_error.LCOSError)
	DeleteOperationServiceableConf(ctx utils.LCOSContext, request *common_protocol.LineIdRequest) *lcos_error.LCOSError
}

type LineOperationServiceableConfService struct {
	lineOperationServiceableConfDAO      model.LogisticLineOperationServiceableConfTabDAO
	lineCommonServiceableScenarioConfDAO scenario_conf.LineCommonServiceableScenarioConfDAO
	lineBasicServiceableConfDAO          basicModel.LineBasicServiceableConfDAO
}

func NewLineOperationServiceableConfService(lineOperationServiceableConfDAO model.LogisticLineOperationServiceableConfTabDAO, lineCommonServiceableScenarioConfDAO scenario_conf.LineCommonServiceableScenarioConfDAO, lineBasicServiceableConfDAO basicModel.LineBasicServiceableConfDAO) *LineOperationServiceableConfService {
	return &LineOperationServiceableConfService{
		lineOperationServiceableConfDAO:      lineOperationServiceableConfDAO,
		lineCommonServiceableScenarioConfDAO: lineCommonServiceableScenarioConfDAO,
		lineBasicServiceableConfDAO:          lineBasicServiceableConfDAO,
	}
}

func (s *LineOperationServiceableConfService) CreateOperationServiceableConf(ctx utils.LCOSContext, request *operation_serviceable.CreateOrUpdateOperationServiceableConfRequest) (*FullLineOperationServiceableConfInfo, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	if !exists {
		logger.LogErrorf("create operation conf failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}

	confModel := new(model.LogisticLineOperationServiceableConfTab)
	scenarioConfModels := make([]*scenario_conf.LineCommonServiceableScenarioConfTab, len(request.ScenarioConf))
	fillModels(confModel, scenarioConfModels, request)
	confModel.LineID = request.LineID
	fc := func() *lcos_error.LCOSError {
		conf, err := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineId(ctx, request.LineID)
		if err != nil {
			return err
		}
		if conf == nil {
			return lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicConfErrorCode)
		}
		if _, err := s.lineOperationServiceableConfDAO.CreateLogisticLineOperationServiceableConfTab(ctx, confModel); err != nil {
			return err
		}

		if err := s.lineCommonServiceableScenarioConfDAO.DeleteScenarioConfModelByLineId(ctx, request.LineID); err != nil {
			return err
		}
		if _, err := s.lineCommonServiceableScenarioConfDAO.BatchCreateScenarioConfModel(ctx, scenarioConfModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return genFullLineOperationServiceableConfInfo(confModel, nil, scenarioConfModels), nil
}

func (s *LineOperationServiceableConfService) UpdateOperationServiceableConf(ctx utils.LCOSContext, request *operation_serviceable.CreateOrUpdateOperationServiceableConfRequest) (*FullLineOperationServiceableConfInfo, *lcos_error.LCOSError) {
	confModel, err := s.lineOperationServiceableConfDAO.GetLogisticLineOperationServiceableConfTabByLineID(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	if confModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundOperationConfErrorCode)
	}
	scenarioConfModels := make([]*scenario_conf.LineCommonServiceableScenarioConfTab, len(request.ScenarioConf))
	fillModels(confModel, scenarioConfModels, request)
	fc := func() *lcos_error.LCOSError {
		if _, err := s.lineOperationServiceableConfDAO.UpdateLogisticLineOperationServiceableConfTab(ctx, confModel); err != nil {
			return err
		}
		if err := s.lineCommonServiceableScenarioConfDAO.DeleteScenarioConfModelByLineId(ctx, request.LineID); err != nil {
			return err
		}
		if _, err := s.lineCommonServiceableScenarioConfDAO.BatchCreateScenarioConfModel(ctx, scenarioConfModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}

	return genFullLineOperationServiceableConfInfo(confModel, nil, scenarioConfModels), nil
}

func (s *LineOperationServiceableConfService) GetOperationServiceableConfByLineID(ctx utils.LCOSContext, lineID string) (*FullLineOperationServiceableConfInfo, *lcos_error.LCOSError) {
	confModel, err := s.lineOperationServiceableConfDAO.GetLogisticLineOperationServiceableConfTabByLineID(ctx, lineID)
	if err != nil {
		return nil, err
	}
	if confModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundOperationConfErrorCode)
	}
	scenarioConfModels, err := s.lineCommonServiceableScenarioConfDAO.GetScenarioConfModelByLineId(ctx, lineID)
	if err != nil {
		return nil, err
	}
	basicConf, lcosError := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineId(ctx, lineID)
	if lcosError != nil {
		return nil, lcosError
	}
	if basicConf == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundBasicConfErrorCode)
	}
	return genFullLineOperationServiceableConfInfo(confModel, basicConf, scenarioConfModels), nil
}

func (s *LineOperationServiceableConfService) GetOperationServiceableConfList(ctx utils.LCOSContext, request *operation_serviceable.OperationServiceableConfListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	searchMap, _ := utils.Struct2map(request)
	models, total, err := s.lineOperationServiceableConfDAO.SearchLogisticLineOperationServiceableConfTab(ctx, pageNo, count, searchMap)
	if err != nil {
		return nil, err
	}
	var lineIds []string
	lineIdRef := make(map[string]bool)
	for _, operationConf := range models {
		if _, ok := lineIdRef[operationConf.LineID]; !ok {
			lineIds = append(lineIds, operationConf.LineID)
		}
		lineIdRef[operationConf.LineID] = true
	}
	basicConfList, lcosError := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineIds(ctx, lineIds)
	if lcosError != nil {
		return nil, lcosError
	}
	confMap := make(map[string]*basicModel.LineBasicServiceableConfTab)
	for _, basicConf := range basicConfList {
		confMap[basicConf.LineId] = basicConf
	}
	fullModel := make([]*FullLineOperationServiceableConfDetailInfo, len(models))
	for i, operationConf := range models {
		fullModel[i] = &FullLineOperationServiceableConfDetailInfo{
			LogisticLineOperationServiceableConfTab: operationConf,
		}
		if v, ok := confMap[operationConf.LineID]; ok {
			fullModel[i].OriginServiceableType = v.OriginServiceableType
			fullModel[i].DestServiceableType = v.DestinationServiceableType
		}
	}
	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   fullModel,
	}, nil
}

func (s *LineOperationServiceableConfService) DeleteOperationServiceableConf(ctx utils.LCOSContext, request *common_protocol.LineIdRequest) *lcos_error.LCOSError {
	return s.lineOperationServiceableConfDAO.DeleteLogisticLineOperationServiceableConfTabsByLineID(ctx, request.LineId)
}

func fillModels(confModel *model.LogisticLineOperationServiceableConfTab, scenarioConfModels []*scenario_conf.LineCommonServiceableScenarioConfTab, request *operation_serviceable.CreateOrUpdateOperationServiceableConfRequest) {
	// confModel.PickupGroupID = request.PickupGroupID
	confModel.Region = request.Region
	confModel.IsCheckServiceable = *request.IsCheckServiceable
	for i, scenarioConf := range request.ScenarioConf {
		scenarioConfModels[i] = new(scenario_conf.LineCommonServiceableScenarioConfTab)
		scenarioConfModels[i].LineId = request.LineID
		scenarioConfModels[i].Region = request.Region
		scenarioConfModels[i].Scenario = scenarioConf.Scenario
		scenarioConfModels[i].CheckLevel = scenarioConf.CheckLevel
		scenarioConfModels[i].CheckAddress = scenarioConf.CheckAddress
	}
}
