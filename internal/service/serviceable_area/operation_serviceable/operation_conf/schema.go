package operation_conf

import (
	basicModel "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
)

type (
	FullLineOperationServiceableConfInfo struct {
		LineID                string                                                `form:"line_id" json:"line_id"`
		Region                string                                                `form:"region" json:"region"`
		OriginServiceableType uint8                                                 `form:"origin_serviceable_type" json:"origin_serviceable_type"`
		DestServiceableType   uint8                                                 `form:"destination_serviceable_type" json:"destination_serviceable_type"`
		IsCheckServiceable    *uint8                                                `form:"is_check_serviceable" json:"is_check_serviceable"`
		ScenarioConf          []*scenario_conf.LineCommonServiceableScenarioConfTab `form:"scenario" json:"scenario"`
		CTime                 int64                                                 `form:"ctime" json:"ctime"`
		MTime                 int64                                                 `form:"mtime" json:"mtime"`
	}

	FullLineOperationServiceableConfDetailInfo struct {
		*model.LogisticLineOperationServiceableConfTab
		OriginServiceableType uint8 `form:"origin_serviceable_type" json:"origin_serviceable_type"`
		DestServiceableType   uint8 `form:"destination_serviceable_type" json:"destination_serviceable_type"`
	}
)

func genFullLineOperationServiceableConfInfo(confTab *model.LogisticLineOperationServiceableConfTab, basicConf *basicModel.LineBasicServiceableConfTab, scenarioTabs []*scenario_conf.LineCommonServiceableScenarioConfTab) *FullLineOperationServiceableConfInfo {
	fullLineOperationServiceableConfInfo := &FullLineOperationServiceableConfInfo{
		LineID:             confTab.LineID,
		Region:             confTab.Region,
		IsCheckServiceable: &confTab.IsCheckServiceable,
		ScenarioConf:       scenarioTabs,
		CTime:              confTab.Ctime,
		MTime:              confTab.Mtime,
	}
	if basicConf != nil {
		fullLineOperationServiceableConfInfo.DestServiceableType = basicConf.DestinationServiceableType
		fullLineOperationServiceableConfInfo.OriginServiceableType = basicConf.OriginServiceableType
	}
	return fullLineOperationServiceableConfInfo
}
