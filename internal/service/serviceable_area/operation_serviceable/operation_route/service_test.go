package operation_route

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	basicRouteModel "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	collectDeliver "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
)

func TestLineOperationRouteService_UploadOperationRoute(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	type fields struct {
		lineServiceableOpsRouteDAO  model.LineOperationRouteTabDAO
		lineServiceableAreaDAO      area.LogisticLineServiceableAreaTabDAO
		lineCollectDeliverGroupDAO  collectDeliver.LineCollectDeliverGroupConfDao
		lineBasicServiceableConfDAO basic_conf.LineBasicServiceableConfDAO
		lineServiceableRouteDAO     basicRouteModel.LogisticLineServiceableRouteTabDAO
	}
	type args struct {
		ctx     utils.LCOSContext
		request *operation_serviceable.UploadOperationRouteRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "upload operation zone/route",
			fields: fields{
				lineServiceableOpsRouteDAO:  model.NewLineOperationRouteTabDAO(),
				lineServiceableAreaDAO:      area.NewLogisticLineServiceableAreaTabDAO(),
				lineCollectDeliverGroupDAO:  collectDeliver.NewLineCollectDeliverGroupConfDao(),
				lineBasicServiceableConfDAO: basic_conf.NewLineBasicServiceableConfDAO(),
				lineServiceableRouteDAO:     basicRouteModel.NewLogisticLineServiceableRouteTabDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &operation_serviceable.UploadOperationRouteRequest{
					FileUrl: "test",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &LineOperationRouteService{
				lineServiceableOpsRouteDAO:  tt.fields.lineServiceableOpsRouteDAO,
				lineServiceableAreaDAO:      tt.fields.lineServiceableAreaDAO,
				lineCollectDeliverGroupDAO:  tt.fields.lineCollectDeliverGroupDAO,
				lineBasicServiceableConfDAO: tt.fields.lineBasicServiceableConfDAO,
				lineServiceableRouteDAO:     tt.fields.lineServiceableRouteDAO,
			}
			if got := s.UploadOperationRoute(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("LineOperationRouteService.UploadOperationRoute() = %v, want %v", got, tt.want)
			}
		})
	}
}
