package operation_route

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	basicRouteModel "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	collectDeliver "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

type LineOperationRouteServiceInterface interface {
	CreateOperationRoute(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationRouteRequest) (*model.LineOperationRouteTab, *lcos_error.LCOSError)
	UpdateOperationRoute(ctx utils.LCOSContext, request *operation_serviceable.UpdateLineOperationRouteRequest) (*model.LineOperationRouteTab, *lcos_error.LCOSError)
	DeleteOperationRoute(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	DeleteOperationRouteByLineID(ctx utils.LCOSContext, LineID string) *lcos_error.LCOSError
	GetOperationRouteList(ctx utils.LCOSContext, request *operation_serviceable.SearchLineOperationRouteRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllOperationRouteList(ctx utils.LCOSContext, request *operation_serviceable.SearchAllLineOperationRouteRequest) ([]*model.LineOperationRouteTab, *lcos_error.LCOSError)
	GetOperationRoute(ctx utils.LCOSContext, request *common_protocol.IDRequest) (*model.LineOperationRouteTab, *lcos_error.LCOSError)
	UploadOperationRoute(ctx utils.LCOSContext, request *operation_serviceable.UploadOperationRouteRequest) *lcos_error.LCOSError
	ParseAndImportOperationRouteSA(ctx utils.LCOSContext, region string, request *operation_serviceable.UploadOperationRouteRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError)
	SyncSearchOperationRouteToBasicRoute(ctx utils.LCOSContext, request *operation_serviceable.SearchAllLineOperationRouteRequest) *lcos_error.LCOSError
}

type LineOperationRouteService struct {
	lineServiceableOpsRouteDAO  model.LineOperationRouteTabDAO
	lineServiceableAreaDAO      area.LogisticLineServiceableAreaTabDAO
	lineCollectDeliverGroupDAO  collectDeliver.LineCollectDeliverGroupConfDao
	lineBasicServiceableConfDAO basic_conf.LineBasicServiceableConfDAO
	lineServiceableRouteDAO     basicRouteModel.LogisticLineServiceableRouteTabDAO
	scheduledService            scheduled.ScheduledService
}

func (s *LineOperationRouteService) CreateOperationRoute(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationRouteRequest) (*model.LineOperationRouteTab, *lcos_error.LCOSError) {
	// get line base info
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	// should not create any data on an illegal line
	if !exists {
		logger.LogErrorf("create operation route failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}
	routeTab := new(model.LineOperationRouteTab)
	routeTab.LineID = request.LineID
	routeTab.CollectDeliverGroupId = request.CollectDeliverGroupId
	routeTab.Region = request.Region
	routeTab.DisableFromAreaID = request.DisableFromAreaID
	routeTab.DisableToAreaID = request.DisableToAreaID
	// validate if area existed in db
	fromArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(ctx, request.DisableFromAreaID)
	if err != nil {
		return nil, err
	}
	if fromArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, "area id not found: "+strconv.Itoa(int(request.DisableFromAreaID)))
	}
	toArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(ctx, request.DisableToAreaID)
	if err != nil {
		return nil, err
	}
	if toArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, "area id not found: "+strconv.Itoa(int(request.DisableToAreaID)))
	}
	routeTab.DisableFromAreaName = fromArea.AreaName
	routeTab.DisableToAreaName = toArea.AreaName
	// insert into database
	routeModel, lcosErr := s.lineServiceableOpsRouteDAO.CreateLineOperationRouteTab(ctx, routeTab)
	if lcosErr != nil {
		return nil, lcosErr
	} else {
		_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineOperationRouteTabNamespace)
		return routeModel, nil
	}
}

func (s *LineOperationRouteService) UpdateOperationRoute(ctx utils.LCOSContext, request *operation_serviceable.UpdateLineOperationRouteRequest) (*model.LineOperationRouteTab, *lcos_error.LCOSError) {
	routeTab, err := s.lineServiceableOpsRouteDAO.GetLineOperationRouteTabById(ctx, request.ID)
	if err != nil {
		return nil, err
	}
	if routeTab == nil || routeTab.ID == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundRouteErrorCode, fmt.Sprintf("cannot get operation route by ID:%d", request.ID))
	}
	routeTab.DisableFromAreaID = request.DisableFromAreaID
	routeTab.DisableToAreaID = request.DisableToAreaID
	fromArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(ctx, request.DisableFromAreaID)
	if err != nil {
		return nil, err
	}
	if fromArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, "area id not found: "+strconv.Itoa(int(request.DisableFromAreaID)))
	}
	toArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(ctx, request.DisableToAreaID)
	if err != nil {
		return nil, err
	}
	if toArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, "area id not found: "+strconv.Itoa(int(request.DisableToAreaID)))
	}

	routeTab.DisableFromAreaName = fromArea.AreaName
	routeTab.DisableToAreaName = toArea.AreaName
	returnRouteModel, lcosErr := s.lineServiceableOpsRouteDAO.UpdateLineOperationRouteTab(ctx, routeTab)
	if lcosErr != nil {
		return nil, lcosErr
	} else {
		// 手动刷新缓存
		_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineOperationRouteTabNamespace)
		return returnRouteModel, nil
	}
}

func (s *LineOperationRouteService) DeleteOperationRoute(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	lcosErr := s.lineServiceableOpsRouteDAO.DeleteLineOperationRouteTabsById(ctx, request.ID)
	if lcosErr != nil {
		return lcosErr
	} else {
		// 手动刷新缓存
		_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineOperationRouteTabNamespace)
		return nil
	}
}

func (s *LineOperationRouteService) DeleteOperationRouteByLineID(ctx utils.LCOSContext, LineID string) *lcos_error.LCOSError {
	lcosErr := s.lineServiceableOpsRouteDAO.DeleteLineOperationRouteTabsByLineId(ctx, LineID)
	if lcosErr != nil {
		return lcosErr
	} else {
		// 手动刷新缓存
		_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineOperationRouteTabNamespace)
		return nil
	}
}

func (s *LineOperationRouteService) GetOperationRouteList(ctx utils.LCOSContext, request *operation_serviceable.SearchLineOperationRouteRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.PageSize)
	paramMap, _ := utils.Struct2map(request)
	opsRouteAreaRefList, total, lcosError := s.lineServiceableOpsRouteDAO.SearchLineOperationRouteTab(ctx, pageNo, count, paramMap)
	return &common.PageModel{
		PageNO: pageNo,
		Count:  count,
		Total:  total,
		List:   opsRouteAreaRefList,
	}, lcosError
}

func (s *LineOperationRouteService) GetAllOperationRouteList(ctx utils.LCOSContext, request *operation_serviceable.SearchAllLineOperationRouteRequest) ([]*model.LineOperationRouteTab, *lcos_error.LCOSError) {
	region := ctx.GetCountry()
	params, _ := utils.Struct2map(request)
	params["region"] = region
	return s.lineServiceableOpsRouteDAO.SearchAllLineOperationRouteTab(ctx, params)
}

func (s *LineOperationRouteService) GetOperationRoute(ctx utils.LCOSContext, request *common_protocol.IDRequest) (*model.LineOperationRouteTab, *lcos_error.LCOSError) {
	route, err := s.lineServiceableOpsRouteDAO.GetLineOperationRouteTabById(ctx, request.ID)
	if err != nil {
		return nil, err
	}
	if route == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundRouteErrorCode)
	}
	return route, nil
}

func (s *LineOperationRouteService) UploadOperationRoute(ctx utils.LCOSContext, request *operation_serviceable.UploadOperationRouteRequest) *lcos_error.LCOSError {
	region := ctx.GetCountry()

	job, err := s.scheduledService.CreateScheduledJob(ctx, region, scheduled_constant.OperationServiceableArea, scheduled_constant.ZoneRoute, request.FileUrl, request.IsScheduled, request.ScheduledTime, ctx.GetUserEmail())
	if err != nil {
		return err
	}

	go func() {
		parseFileResult, _ := s.ParseAndImportOperationRouteSA(ctx, region, request, serviceable_util.LineOperationRouteHeader, true)
		if err := s.scheduledService.UpdateValidateResult(ctx, job, parseFileResult); err != nil {
			logger.CtxLogErrorf(ctx, "scheduled job update validate result error|job_id=%d, cause=%s", job.ID, err.Msg)
		}
	}()

	return nil
}

func (s *LineOperationRouteService) ParseAndImportOperationRouteSA(ctx utils.LCOSContext, region string, request *operation_serviceable.UploadOperationRouteRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}

	//download file
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return result, nil
	}

	// start parsing
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
		return result, nil
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
		return result, nil
	}
	defer os.Remove(filePath)

	uniqueKeyMap := make(map[string]int)
	lineIDMap := make(map[string]bool)
	var lineIDList []string
	var rowDataList []*serviceable_util.LineOperationRouteRowData

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头和空行
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount()

		rowData := &serviceable_util.LineOperationRouteRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}
		if region != rowData.Region {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", region, rowData.RowId))
			continue
		}
		if rowData.Category == serviceable_constant.RouteCategory && rowData.BanFromArea == rowData.BanToArea {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "From Area should be different with To Area for Route category | row=%d", rowData.RowId))
			continue
		}
		if rowData.Category == serviceable_constant.ZoneCategory && rowData.BanFromArea != rowData.BanToArea {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "From Area should be the same as To Area for Zone category | row=%d", rowData.RowId))
			continue
		}

		if _, ok := lineIDMap[rowData.LineId]; !ok {
			lineIDList = append(lineIDList, rowData.LineId)
			lineIDMap[rowData.LineId] = true
		}

		// 基础重复校验，唯一key：LineId-GroupId-FromArea-ToArea
		uniqueKey := utils.GenKey("-", rowData.LineId, rowData.GroupId, rowData.BanFromArea, rowData.BanToArea)
		if lastRowId, ok := uniqueKeyMap[uniqueKey]; ok {
			result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import data duplicated with same Line-Group-FromArea-ToArea, please check | row %d duplicate with row %d, unique key=%s", rowData.RowId, lastRowId, uniqueKey)
			return result, nil
		} else {
			uniqueKeyMap[uniqueKey] = lineNum
		}

		rowDataList = append(rowDataList, rowData)
	}

	if len(lineIDList) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is empty")
		return result, nil
	}

	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIDList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return result, nil
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}
	//lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, lineIDList)
	//for lineId, lineDraft := range lineDraftMap {
	//	lineInfoMap[lineId] = lineDraft
	//}
	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(ctx, region, lineIDList, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return result, nil
	}
	addRouteModelsMap := make(map[string][]*model.LineOperationRouteTab)
	deleteRouteModelsMap := make(map[string][]*model.LineOperationRouteTab)
	for _, rowData := range rowDataList {
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}

		// 填充area id和area name信息，并对修改配置进行分组
		if err := s.parseSingleRow(ctx, rowData, region, addRouteModelsMap, deleteRouteModelsMap); err != nil {
			result.WriteRowError(rowData.RowId, err)
			continue
		}
	}

	if async {
		return result, nil
	}
	// batch insert & delete
	fc := func() *lcos_error.LCOSError {
		for _, models := range addRouteModelsMap {
			if lcosErr = s.lineServiceableOpsRouteDAO.BatchCreateLineOperationRouteTabOnDuplicateUpdate(ctx, models); lcosErr != nil {
				return lcosErr
			}
		}
		for _, models := range deleteRouteModelsMap {
			if lcosErr = s.lineServiceableOpsRouteDAO.BatchDeleteLineOperationRouteTabs(ctx, models); lcosErr != nil {
				return lcosErr
			}
		}
		return nil
	}
	lcosErr = ctx.Transaction(fc)
	if lcosErr != nil {
		return result, lcosErr
	} else {
		// 手动刷新缓存
		_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineOperationRouteTabNamespace)
		change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(lineIDList, ","))
		return result, nil
	}
}

func (s *LineOperationRouteService) parseSingleRow(ctx utils.LCOSContext, rowData *serviceable_util.LineOperationRouteRowData, region string, addOpsRouteModelMap, delOpsRouteModelMap map[string][]*model.LineOperationRouteTab) *lcos_error.LCOSError {
	routeTab := &model.LineOperationRouteTab{
		LineID:                rowData.LineId,
		CollectDeliverGroupId: rowData.GroupId,
		Region:                rowData.Region,
	}

	// 填充from area信息
	fromAreaTabs, lcosErr := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabByParams(ctx, map[string]interface{}{"area_name": rowData.BanFromArea, "region": region})
	if lcosErr != nil || len(fromAreaTabs) == 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "From Area not exists | row=%d", rowData.RowId)
	}
	routeTab.DisableFromAreaID = fromAreaTabs[0].ID
	routeTab.DisableFromAreaName = fromAreaTabs[0].AreaName

	// 填充to area信息
	if rowData.Category == serviceable_constant.ZoneCategory {
		routeTab.DisableToAreaID = fromAreaTabs[0].ID
		routeTab.DisableToAreaName = fromAreaTabs[0].AreaName
	} else {
		toAreaTabs, lcosErr := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabByParams(ctx, map[string]interface{}{"area_name": rowData.BanToArea, "region": region})
		if lcosErr != nil || len(toAreaTabs) == 0 {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "To Area not exists | row=%d", rowData.RowId)
		}
		routeTab.DisableToAreaID = toAreaTabs[0].ID
		routeTab.DisableToAreaName = toAreaTabs[0].AreaName
	}

	if rowData.ActionCode == serviceable_constant.DeleteActionCode {
		delOpsRouteModelMap[rowData.LineId] = append(delOpsRouteModelMap[rowData.LineId], routeTab)
	} else {
		addOpsRouteModelMap[rowData.LineId] = append(addOpsRouteModelMap[rowData.LineId], routeTab)
	}
	return nil
}

func (s *LineOperationRouteService) SyncSearchOperationRouteToBasicRoute(ctx utils.LCOSContext, request *operation_serviceable.SearchAllLineOperationRouteRequest) *lcos_error.LCOSError {
	lineID := request.LineID
	basicConf, err := s.lineBasicServiceableConfDAO.GetBasicServiceableConfModelByLineId(ctx, lineID)
	if err != nil {
		return err
	}
	if basicConf == nil || basicConf.IsCheckRoute == 0 {
		return lcos_error.NewLCOSError(lcos_error.NotFoundLineServiceableBasicConfErrorCode, "The Route/Zone Validation is disabled for the line, no data is synced.")
	}
	searchMap, _ := utils.Struct2map(request)
	if ctx.GetClientType() > 0 {
		allowLineIds, err := lls_service.GetLineIdsByClientType(ctx, ctx.GetClientType())
		if err != nil {
			return err
		}
		if len(allowLineIds) == 0 {
			return nil
		}
		searchMap["LINE_ID"] = allowLineIds
	}
	models, err := s.lineServiceableOpsRouteDAO.SearchAllLineOperationRouteTab(ctx, searchMap)
	if err != nil {
		return err
	}
	if len(models) == 0 {
		return nil
	}

	routeMode := basicConf.RouteMode
	if routeMode == constant.ROUTE_SUPPORTED {
		deleteRouteModels := make([]*basicRouteModel.LineServiceableRouteTab, 0)
		operationRouteMap := make(map[string]*model.LineOperationRouteTab)
		for _, operationRoute := range models {
			deleteRouteModels = append(deleteRouteModels, &basicRouteModel.LineServiceableRouteTab{
				LineID:                operationRoute.LineID,
				CollectDeliverGroupId: operationRoute.CollectDeliverGroupId,
				FromAreaID:            operationRoute.DisableFromAreaID,
				ToAreaID:              operationRoute.DisableToAreaID,
			})

			uniqKey := fmt.Sprintf("%s-%s-%d-%d", operationRoute.LineID, operationRoute.CollectDeliverGroupId, operationRoute.DisableFromAreaID, operationRoute.DisableToAreaID)
			operationRouteMap[uniqKey] = operationRoute
		}

		lcosErr := s.lineServiceableRouteDAO.BatchDeleteLineServiceableRouteTabs(ctx, deleteRouteModels)
		if lcosErr != nil {
			return lcosErr
		} else {
			// 手动刷新缓存
			if len(deleteRouteModels) > 0 {
				_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineRouteTabNamespace2)
			}
			return nil
		}
	} else if routeMode == constant.ROUTE_UNSUPPORTED {
		deleteRouteModels := make([]*basicRouteModel.LineServiceableRouteTab, 0)
		addRouteModels := make([]*basicRouteModel.LineServiceableBlackListRouteTab, 0)
		var searchBasicRouteModels []*basicRouteModel.LineServiceableBlackListRouteTab
		operationRouteMap := make(map[string]*model.LineOperationRouteTab)
		basicRouteMap := make(map[string]*basicRouteModel.LineServiceableBlackListRouteTab)
		querySlice := make([][]interface{}, 0)
		for _, operationRoute := range models {
			queryItem := make([]interface{}, 0)
			queryItem = append(queryItem, operationRoute.LineID, operationRoute.CollectDeliverGroupId, operationRoute.DisableFromAreaID, operationRoute.DisableToAreaID)
			querySlice = append(querySlice, queryItem)

			uniqKey := fmt.Sprintf("%s-%s-%d-%d", operationRoute.LineID, operationRoute.CollectDeliverGroupId, operationRoute.DisableFromAreaID, operationRoute.DisableToAreaID)
			operationRouteMap[uniqKey] = operationRoute
		}
		r := &basicRouteModel.LineServiceableBlackListRouteTab{}
		tableName := r.TableName()
		d := ctx.ReadDB().Table(tableName).Where("(`line_id`, `collect_deliver_group_id`, `from_area_id`, `to_area_id`) in ?", querySlice).Find(&searchBasicRouteModels)
		if d.GetError() != nil {
			logger.CtxLogErrorf(ctx, "batch query basic route err: %s", d.GetError().Error())
			return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, d.GetError().Error())
		}
		for _, basicRoute := range searchBasicRouteModels {
			uniqKey := fmt.Sprintf("%s-%s-%d-%d", basicRoute.LineID, basicRoute.CollectDeliverGroupId, basicRoute.FromAreaID, basicRoute.ToAreaID)
			basicRouteMap[uniqKey] = basicRoute
		}
		for k, opsRoute := range operationRouteMap {
			if _, ok := basicRouteMap[k]; !ok {
				addRouteModels = append(addRouteModels, &basicRouteModel.LineServiceableBlackListRouteTab{
					LineID:                opsRoute.LineID,
					CollectDeliverGroupId: opsRoute.CollectDeliverGroupId,
					Region:                opsRoute.Region,
					FromAreaID:            opsRoute.DisableFromAreaID,
					FromAreaName:          opsRoute.DisableFromAreaName,
					ToAreaID:              opsRoute.DisableToAreaID,
					ToAreaName:            opsRoute.DisableToAreaName,
				})
				deleteRouteModels = append(deleteRouteModels, &basicRouteModel.LineServiceableRouteTab{
					LineID:                opsRoute.LineID,
					CollectDeliverGroupId: opsRoute.CollectDeliverGroupId,
					FromAreaID:            opsRoute.DisableFromAreaID,
					ToAreaID:              opsRoute.DisableToAreaID,
				})
			}
		}

		fc := func() *lcos_error.LCOSError {
			lcosErr := s.lineServiceableRouteDAO.BatchCreateLineServiceableBlacklistRouteTabOnDuplicateUpdate(ctx, addRouteModels)
			if lcosErr != nil {
				return lcosErr
			}
			lcosErr = s.lineServiceableRouteDAO.BatchDeleteLineServiceableRouteTabs(ctx, deleteRouteModels)
			if lcosErr != nil {
				return lcosErr
			}
			return nil
		}
		lcosErr := ctx.Transaction(fc)
		if lcosErr != nil {
			return lcosErr
		} else {
			// 手动刷新缓存
			if len(deleteRouteModels) > 0 {
				_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineRouteTabNamespace2)
			}
			if len(addRouteModels) > 0 {
				_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineBlacklistRouteTabNamespace)
			}
			return nil
		}
	}
	return nil
}

func NewLineOperationRouteService(lineOperationRouteDAO model.LineOperationRouteTabDAO, lineServiceableAreaDAO area.LogisticLineServiceableAreaTabDAO, lineCollectDeliverGroupDAO collectDeliver.LineCollectDeliverGroupConfDao, lineBasicServiceableConfDAO basic_conf.LineBasicServiceableConfDAO, lineServiceableRouteDAO basicRouteModel.LogisticLineServiceableRouteTabDAO, scheduledService scheduled.ScheduledService) *LineOperationRouteService {
	return &LineOperationRouteService{
		lineServiceableOpsRouteDAO:  lineOperationRouteDAO,
		lineServiceableAreaDAO:      lineServiceableAreaDAO,
		lineCollectDeliverGroupDAO:  lineCollectDeliverGroupDAO,
		lineBasicServiceableConfDAO: lineBasicServiceableConfDAO,
		lineServiceableRouteDAO:     lineServiceableRouteDAO,
		scheduledService:            scheduledService,
	}
}
