package operation_cep_range

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
)

func TestLineOperationServiceableCepRangeService_UploadOperationServiceableCepRange(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	type fields struct {
		lineOperationServiceableCepRangeDAO model.LineServiceableCepRangeDaoInterface
	}
	type args struct {
		ctx      utils.LCOSContext
		request  *common_protocol.UploadFileRequest
		filePath string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "upload line basic cep range",
			fields: fields{
				lineOperationServiceableCepRangeDAO: model.NewLineServiceableCepRangeDAO(),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &common_protocol.UploadFileRequest{
					FileUrl: "",
					Region:  "BR",
				},
				filePath: "/Users/<USER>/Desktop/serviceable-operation-cep-range.xlsx",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := &LineOperationServiceableCepRangeService{
				lineOperationServiceableCepRangeDAO: tt.fields.lineOperationServiceableCepRangeDAO,
			}
			if got := l.UploadOperationServiceableCepRange(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("LineOperationServiceableCepRangeService.UploadOperationServiceableCepRange() = %v, want %v", got, tt.want)
			}
		})
	}
}
