package operation_cep_range

import (
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/ceprange_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"github.com/tealeg/xlsx"
	"os"
	"sort"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/jinzhu/copier"
)

type LineOperationServiceableCepRangeServiceInterface interface {
	// 创建基础层rep range
	CreateOperationServiceableCepRange(ctx utils.LCOSContext, request *operation_serviceable.OperationCepRangeCreateRequest) (*model.LineOperationServiceableCepRangeTab, *lcos_error.LCOSError)
	// 更新基础层CepRange
	UpdateOperationServiceableCepRange(ctx utils.LCOSContext, request *operation_serviceable.OperationCepRangeUpdateRequest) (*model.LineOperationServiceableCepRangeTab, *lcos_error.LCOSError)
	// 获取基础层CepRange 分页列表
	ListOperationServiceableCepRange(ctx utils.LCOSContext, request *operation_serviceable.OperationCepRangeListRequest) (*common.PageModel, *lcos_error.LCOSError)
	// 获取所有的基础层CepRange列表
	GetAllOperationServiceableCepRangeList(ctx utils.LCOSContext, request map[string]interface{}) ([]*model.LineOperationServiceableCepRangeTab, *lcos_error.LCOSError)
	// 删除基础层CepRange
	DeleteOperationServiceableCepRange(ctx utils.LCOSContext, request map[string]interface{}) *lcos_error.LCOSError
	// 上传基础层CepRange
	UploadOperationServiceableCepRange(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	ParseAndImportOperationCepRangeSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError)
}

type LineOperationServiceableCepRangeService struct {
	lineOperationServiceableCepRangeDAO model.LineServiceableCepRangeDaoInterface
	scheduledService                    scheduled.ScheduledService
}

func NewLineOperationServiceableCepRangeService(lineOperationServiceableCepRangeDAO model.LineServiceableCepRangeDaoInterface, scheduledService scheduled.ScheduledService) *LineOperationServiceableCepRangeService {
	return &LineOperationServiceableCepRangeService{
		lineOperationServiceableCepRangeDAO: lineOperationServiceableCepRangeDAO,
		scheduledService:                    scheduledService,
	}
}

func (l *LineOperationServiceableCepRangeService) CreateOperationServiceableCepRange(ctx utils.LCOSContext, request *operation_serviceable.OperationCepRangeCreateRequest) (*model.LineOperationServiceableCepRangeTab, *lcos_error.LCOSError) {
	var isAutoMergeOrSplitFlag bool

	// 1. 校验line是否存在
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	if !exists {
		logger.LogErrorf("create operation conf failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}

	// 2. 重叠性校验：校验同一line+group维度下，新增服务范围是否和已有服务范围重叠，如果重叠则尝试进行区间合并，如果不能合并则报错
	// 2.1 从DB中获取相同line+group的全量配置数据
	operationCepRangeList, lcosError := l.lineOperationServiceableCepRangeDAO.GetAllLineOperationServiceableCepRangeModel(ctx, map[string]interface{}{"line_id": request.LineID, "collect_deliver_group_id": request.CollectDeliverGroupID})
	if lcosError != nil {
		return nil, lcosError
	}
	// 2.2 将请求参数转换为线cep range服务范围数据模型
	newCepRange := new(model.LineOperationServiceableCepRangeTab)
	if copierErr := copier.Copy(&newCepRange, request); copierErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("copier error: %v", copierErr))
	}
	// 2.3 判断新增服务范围是否与已有服务范围重叠
	var overlapSet ceprange_util.CepRangeSet                     // 用于进行区间合并
	var overlapList []*model.LineOperationServiceableCepRangeTab // 区间合并前的已有服务范围配置，合并后需要删除
	for _, operationCepRange := range operationCepRangeList {
		if utils.CheckRangeCoincidence(operationCepRange.Initial, operationCepRange.Final, request.Initial, request.Final) {
			// 新建服务范围与DB中已有数据有重叠
			if !serviceable_util.ServiceableAreaAbilityEqual(operationCepRange, newCepRange) || operationCepRange.GetCanTradeIn() != newCepRange.GetCanTradeIn() {
				// 服务范围重叠但揽派能力不一致，那么无法合并，报错overlap
				return nil, lcos_error.NewLCOSError(lcos_error.CepRangeDuplicate, "CEP Range is overlapped with existing ones and cannot merge, please edit and retry.")
			}
			overlapSet.Add(operationCepRange)
			overlapList = append(overlapList, operationCepRange)
		}
	}
	// 2.4 执行区间合并，获取最终需要新增的区间列表
	var actuallyAddList []*model.LineOperationServiceableCepRangeTab // 区间合并后实际需要新增的区间列表，合并前的存量重叠数据会被删除
	if len(overlapList) > 0 {
		// 有重叠区间，需要进行区间合并
		overlapSet.Add(newCepRange)               // 已有服务范围+新增服务范围
		merged := ceprange_util.Merge(overlapSet) // 区间合并
		merged.Range(func(item *ceprange_util.CepRangeItem) {
			actuallyAddList = append(actuallyAddList, &model.LineOperationServiceableCepRangeTab{
				LineID:                newCepRange.LineID,
				Region:                newCepRange.Region,
				CollectDeliverGroupID: newCepRange.CollectDeliverGroupID,
				Initial:               item.GetCepStart(),
				Final:                 item.GetCepEnd(),
				DisablePickup:         newCepRange.DisablePickup,
				DisableCodPickup:      newCepRange.DisableCodPickup,
				DisableDeliver:        newCepRange.DisableDeliver,
				DisableCodDeliver:     newCepRange.DisableCodDeliver,
				DisableTradeIn:        newCepRange.DisableTradeIn,
			})
		})
		isAutoMergeOrSplitFlag = true
	} else {
		// 无重叠区间
		actuallyAddList = append(actuallyAddList, newCepRange)
	}

	// 3. 数据落DB
	fn := func() *lcos_error.LCOSError {
		if lcosErr := l.lineOperationServiceableCepRangeDAO.BatchDeleteLineOperationServiceableCepRangeModel(ctx, overlapList, constant.DBMAXBATCHDELNUM); lcosErr != nil {
			return lcosErr
		}
		if lcosErr := l.lineOperationServiceableCepRangeDAO.BatchCreateLineOperationServiceableCepRangeModel(ctx, actuallyAddList); lcosErr != nil {
			return lcosErr
		}
		return nil
	}
	if lcosErr := ctx.Transaction(fn); lcosErr != nil {
		return nil, lcosErr
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineOperationCepRangeTabNamespace)

	if isAutoMergeOrSplitFlag {
		alertLineOperationCepRangeAutoMergeOrSplit(ctx, overlapList, actuallyAddList, "Single Create")
	}
	return actuallyAddList[0], nil
}

func (l *LineOperationServiceableCepRangeService) UpdateOperationServiceableCepRange(ctx utils.LCOSContext, request *operation_serviceable.OperationCepRangeUpdateRequest) (*model.LineOperationServiceableCepRangeTab, *lcos_error.LCOSError) {
	// 校验line是否存在
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	if !exists {
		logger.LogErrorf("update operation conf failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "update failed, line_id not found")
	}
	// 判断cep range 是否有重叠
	operationCepRangeList, lcosError := l.lineOperationServiceableCepRangeDAO.GetAllLineOperationServiceableCepRangeModel(ctx, map[string]interface{}{"line_id": request.LineID, "collect_deliver_group_id": request.CollectDeliverGroupID})
	if lcosError != nil {
		return nil, lcosError
	}
	for _, operationCepRange := range operationCepRangeList {
		if operationCepRange.ID == request.ID {
			continue
		}
		if utils.CheckRangeCoincidence(operationCepRange.Initial, operationCepRange.Final, request.Initial, request.Final) {
			return nil, lcos_error.NewLCOSError(lcos_error.CepRangeDuplicate, "CEP Range is overlapped with existing ones, please edit and retry.")
		}
	}
	updateMap, _ := utils.Struct2map(request)
	lcosErr := l.lineOperationServiceableCepRangeDAO.UpdateLineOperationServiceableCepRangeModel(ctx, updateMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineOperationCepRangeTabNamespace)
	return nil, nil
}

func (l *LineOperationServiceableCepRangeService) ListOperationServiceableCepRange(ctx utils.LCOSContext, request *operation_serviceable.OperationCepRangeListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	paramMap, _ := utils.Struct2map(request)
	if request.CepCode != nil {
		paramMap["initial <="] = request.CepCode
		paramMap["final >="] = request.CepCode
	}
	list, total, lcosError := l.lineOperationServiceableCepRangeDAO.SearchLineOperationServiceableCepRangeModel(ctx, pageNo, count, paramMap)
	if lcosError != nil {
		return nil, lcosError
	}
	return &common.PageModel{PageNO: pageNo, Count: count, Total: total, List: list}, nil
}

func (l *LineOperationServiceableCepRangeService) GetAllOperationServiceableCepRangeList(ctx utils.LCOSContext, request map[string]interface{}) ([]*model.LineOperationServiceableCepRangeTab, *lcos_error.LCOSError) {
	return l.lineOperationServiceableCepRangeDAO.GetAllLineOperationServiceableCepRangeModel(ctx, request)
}

func (l *LineOperationServiceableCepRangeService) DeleteOperationServiceableCepRange(ctx utils.LCOSContext, request map[string]interface{}) *lcos_error.LCOSError {
	lcosErr := l.lineOperationServiceableCepRangeDAO.DeleteOperationServiceableCepRangeModel(ctx, request)
	if lcosErr != nil {
		return lcosErr
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineOperationCepRangeTabNamespace)
	return nil
}

func (l *LineOperationServiceableCepRangeService) checkCepRangeLineOverlap(ctx utils.LCOSContext, lineID string, cepRangeServiceables []*model.LineOperationServiceableCepRangeTab, oldCepRangeMap map[string]map[string][]*model.LineOperationServiceableCepRangeTab) *lcos_error.LCOSError {
	// line sa cannot overlap
	// it's weird that a blacklist mode still need to check this
	notAllowedOverlapList := config.GetFMLineGroupNoOverlap(ctx, lineID)
	if len(notAllowedOverlapList) != 0 {
		for _, notOverlapLine := range notAllowedOverlapList {
			if lineID != notOverlapLine {
				if oldServiceableLocationsMap, ok1 := oldCepRangeMap[notOverlapLine]; ok1 {
					for _, newCepRange := range cepRangeServiceables {
						// 有重合的location
						if item2, ok2 := oldServiceableLocationsMap[newCepRange.CollectDeliverGroupID]; ok2 {
							if overlapped, idx := l.checkSingleCepRangeLineOverlap(ctx, newCepRange, item2); overlapped {
								errMsg := fmt.Sprintf("line serviceable overlap|new_line:[%s], cep_range_initial:[%d],cep_range_final:[%d] | old_line:[%s], cep_range_initial:[%d],cep_range_final:[%d] | group_id:[%s]", newCepRange.LineID, newCepRange.Initial, newCepRange.Final, item2[idx].LineID, item2[idx].Initial, item2[idx].Final, item2[idx].CollectDeliverGroupID)
								logger.CtxLogErrorf(ctx, errMsg)
								return lcos_error.NewLCOSError(lcos_error.LineServiceAbleOverlap, errMsg)
							}
						}
					}
				}
			}
		}
	}
	return nil
}

func (l *LineOperationServiceableCepRangeService) checkSingleCepRangeLineOverlap(ctx utils.LCOSContext, singleCepRangeServiceable *model.LineOperationServiceableCepRangeTab, cepRangeServiceableList []*model.LineOperationServiceableCepRangeTab) (bool, int) {
	startIndex := 0
	endIndex := len(cepRangeServiceableList) - 1
	for startIndex <= endIndex {
		mid := (startIndex + endIndex) / 2
		// 有重叠
		if cepRangeServiceableList[mid].Initial <= singleCepRangeServiceable.Final && singleCepRangeServiceable.Initial <= cepRangeServiceableList[mid].Final {
			return true, mid
		} else {
			if singleCepRangeServiceable.Final < cepRangeServiceableList[mid].Initial {
				endIndex = mid - 1
			} else {
				startIndex = mid + 1
			}
		}
	}
	return false, -1
}

func (l *LineOperationServiceableCepRangeService) UploadOperationServiceableCepRange(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	job, err := l.scheduledService.CreateScheduledJob(ctx, request.Region, scheduled_constant.OperationServiceableArea, scheduled_constant.CEPRange, request.FileUrl, request.IsScheduled, request.ScheduledTime, ctx.GetUserEmail())
	if err != nil {
		return err
	}

	go func() {
		parseFileResult, _ := l.ParseAndImportOperationCepRangeSA(ctx, request, serviceable_util.LineOperationCepRangeHeader, true)
		if err := l.scheduledService.UpdateValidateResult(ctx, job, parseFileResult); err != nil {
			logger.CtxLogErrorf(ctx, "scheduled job update validate result error|job_id=%d, cause=%s", job.ID, err.Msg)
		}
	}()

	return nil
}

func (l *LineOperationServiceableCepRangeService) ParseAndImportOperationCepRangeSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}
	var isAutoMergeOrSplitFlag bool

	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete, time usage: %s\n", time.Since(beginTime).String())
	}()

	// 1. 下载文件并解析数据
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return result, nil
	}
	lineIdList, addLineList, deleteLineList, addCepRangeList, deleteCepRangeList := checkAndParseFile(ctx, request.Region, filePath, header, &result)
	if result.ParseFileErr != nil {
		return result, nil
	}

	// 2. 校验上传的新增数据是否自重叠，如果重叠则尝试合并，不能合并则报错
	addCepRangeMap := make(map[string][]*model.LineOperationServiceableCepRangeTab)
	for _, addCepRange := range addCepRangeList {
		key := serviceable_util.GetLineServiceableGroupKey(addCepRange)
		addCepRangeMap[key] = append(addCepRangeMap[key], addCepRange)
	}
	for key, lineAddCepRangeList := range addCepRangeMap {
		sort.SliceStable(lineAddCepRangeList, func(i, j int) bool {
			return lineAddCepRangeList[i].GetCepStart() < lineAddCepRangeList[j].GetCepStart()
		})

		mergedList := make([]*model.LineOperationServiceableCepRangeTab, 0, len(lineAddCepRangeList))
		for _, cepRange := range lineAddCepRangeList {
			if len(mergedList) == 0 {
				mergedList = append(mergedList, cepRange)
				continue
			}

			lastMergedCepRange := mergedList[len(mergedList)-1]
			if ceprange_util.Overlap(lastMergedCepRange, cepRange) {
				if !serviceable_util.ServiceableAreaAbilityEqual(lastMergedCepRange, cepRange) || lastMergedCepRange.GetCanTradeIn() != cepRange.GetCanTradeIn() {
					result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.CepRangeDuplicate, fmt.Sprintf("lineID:%s,CollectDeliverGroupID:%s,CEP Range[%d-%d] overlap with others and cannot merge, please edit and retry", cepRange.LineID, cepRange.CollectDeliverGroupID, cepRange.Initial, cepRange.Final))
					return result, nil
				}
				lastMergedCepRange.Final = utils.MaxUInt64(lastMergedCepRange.GetCepEnd(), cepRange.GetCepEnd())
				isAutoMergeOrSplitFlag = true
				continue
			}
			mergedList = append(mergedList, cepRange)
		}
		addCepRangeMap[key] = mergedList
	}

	// 3. 校验新增区间是否和已有区间重叠，如果重叠则尝试合并，不能合并则报错
	// 3.1 查询可能和新增区间重叠的所有已有区间并按照line+group维度进行分组
	existCepRangeList, lcosErr := l.lineOperationServiceableCepRangeDAO.GetAllLineOperationServiceableCepRangeModel(ctx, map[string]interface{}{"line_id in": addLineList})
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return result, nil
	}
	var existCepRangeMap = make(map[string][]*model.LineOperationServiceableCepRangeTab)
	for _, existCepRange := range existCepRangeList {
		key := serviceable_util.GetLineServiceableGroupKey(existCepRange)
		existCepRangeMap[key] = append(existCepRangeMap[key], existCepRange)
	}
	// 3.2 检查新增数据是否和DB数据是否重叠，如果重叠则尝试进行区间合并，获取得到最终需要从DB删除和新增的数据
	var mayAddCepRangeList []*model.LineOperationServiceableCepRangeTab         // 可能需要新增的区间，后续还要进行区间做差得到真正需要新增的区间
	var actuallyDeleteCepRangeList []*model.LineOperationServiceableCepRangeTab // 实际需要删除的区间
	actuallyDeleteCepRangeMap := make(map[string]struct{})
	for key, lineAddCepRangeList := range addCepRangeMap {
		lineExistCepRangeList, ok := existCepRangeMap[key]
		if !ok {
			// 新增数据在DB中不存在相同line+group的数据，不会overlap，可以直接新增
			mayAddCepRangeList = append(mayAddCepRangeList, lineAddCepRangeList...)
			continue
		}

		// 检查DB数据是否和新增数据有重叠以及是否可以合并
		var unmerged []*model.LineOperationServiceableCepRangeTab
		for _, existCepRange := range lineExistCepRangeList {
			// 检查DB数据和新增数据是否有重叠，并且是否能完全合并。有一个重叠区间不能合并则报错
			var hasOverlap bool
			for _, addCepRange := range lineAddCepRangeList {
				if ceprange_util.Overlap(existCepRange, addCepRange) {
					if !serviceable_util.ServiceableAreaAbilityEqual(existCepRange, addCepRange) || existCepRange.GetCanTradeIn() != addCepRange.GetCanTradeIn() {
						result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "added cep range overlap with db and cannot merge|import cep range is line_id=%s, group_id=%s, cep_range_initial=%d, cep_range_final=%d", addCepRange.LineID, addCepRange.CollectDeliverGroupID, addCepRange.Initial, addCepRange.Final)
						return result, nil
					}
					hasOverlap = true
				}
			}
			if hasOverlap {
				// DB数据跟新增数据有重叠，并且可以完全合并
				actuallyDeleteCepRangeList = append(actuallyDeleteCepRangeList, existCepRange)
				actuallyDeleteCepRangeMap[serviceable_util.GetLineCepRangeServiceableUniqueKey(existCepRange)] = struct{}{}
				unmerged = append(unmerged, &model.LineOperationServiceableCepRangeTab{
					LineID:                existCepRange.LineID,
					Region:                existCepRange.Region,
					CollectDeliverGroupID: existCepRange.CollectDeliverGroupID,
					Initial:               existCepRange.GetCepStart(),
					Final:                 existCepRange.GetCepEnd(),
					DisablePickup:         existCepRange.DisablePickup,
					DisableCodPickup:      existCepRange.DisableCodPickup,
					DisableDeliver:        existCepRange.DisableDeliver,
					DisableCodDeliver:     existCepRange.DisableCodDeliver,
					DisableTradeIn:        existCepRange.DisableTradeIn,
				})
			}
		}

		if len(unmerged) == 0 {
			// 新增数据跟DB数据完全不重叠，可以直接新增
			mayAddCepRangeList = append(mayAddCepRangeList, lineAddCepRangeList...)
			continue
		}

		// 新增数据与DB数据进行合并
		unmerged = append(unmerged, lineAddCepRangeList...)
		sort.SliceStable(unmerged, func(i, j int) bool {
			return unmerged[i].GetCepStart() < unmerged[j].GetCepStart()
		})
		merged := make([]*model.LineOperationServiceableCepRangeTab, 0, len(unmerged))
		for _, unmergeCepRange := range unmerged {
			if len(merged) == 0 {
				merged = append(merged, unmergeCepRange)
				continue
			}

			lastMergedCepRange := merged[len(merged)-1]
			if ceprange_util.Overlap(lastMergedCepRange, unmergeCepRange) {
				lastMergedCepRange.Final = utils.MaxUInt64(lastMergedCepRange.GetCepEnd(), unmergeCepRange.GetCepEnd())
				isAutoMergeOrSplitFlag = true
				continue
			}
			merged = append(merged, unmergeCepRange)
		}
		mayAddCepRangeList = append(mayAddCepRangeList, merged...)
	}

	// 4. 根据上传的删除区间对原有区间+新增区间进行删除与拆分
	// 4.1 按照line+group维度对删除区间进行分组
	deleteCepRangeMap := make(map[string][]*model.LineOperationServiceableCepRangeTab)
	for _, deleteCepRange := range deleteCepRangeList {
		key := serviceable_util.GetLineServiceableGroupKey(deleteCepRange)
		deleteCepRangeMap[key] = append(deleteCepRangeMap[key], deleteCepRange)
	}
	// 4.2 获取所有可能需要删除的已有区间
	maybeDeleteCepRangeList, lcosErr := l.lineOperationServiceableCepRangeDAO.GetAllLineOperationServiceableCepRangeModel(ctx, map[string]interface{}{"line_id in": deleteLineList})
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return result, nil
	}
	// 4.3 对已有区间进行删除或拆分
	var actuallyAddCepRangeList []*model.LineOperationServiceableCepRangeTab // 实际需要新增的区间
	for _, maybeDeleteCepRange := range maybeDeleteCepRangeList {
		if _, ok := actuallyDeleteCepRangeMap[serviceable_util.GetLineCepRangeServiceableUniqueKey(maybeDeleteCepRange)]; ok {
			// 此区间在之前的流程中已经明确删除，可以忽略掉
			continue
		}

		key := serviceable_util.GetLineServiceableGroupKey(maybeDeleteCepRange)
		var deleteSet ceprange_util.CepRangeSet
		for _, deleteCepRange := range deleteCepRangeMap[key] {
			// 如果删除区间与原区间有重叠，则添加到delete集合里
			if ceprange_util.Overlap(maybeDeleteCepRange, deleteCepRange) {
				deleteSet.Add(deleteCepRange)
			}
		}
		if deleteSet.Len() != 0 {
			// 如果delete集合不为空，则进行区间作差。此时：
			// 1. 需要删除原区间
			// 2. 作差后如果区间不为空，则需要将拆分后的区间添加到新增区间列表

			// 将原区间添加到实际删除列表
			actuallyDeleteCepRangeList = append(actuallyDeleteCepRangeList, maybeDeleteCepRange)
			actuallyDeleteCepRangeMap[serviceable_util.GetLineCepRangeServiceableUniqueKey(maybeDeleteCepRange)] = struct{}{}

			// 区间作差，原区间 - 所有有重叠的删除区间
			var originSet ceprange_util.CepRangeSet
			originSet.Add(maybeDeleteCepRange)
			resultSet := ceprange_util.Subtract(originSet, deleteSet)

			// 如果resultSet不为空，说明此区间会被拆分为多段，将拆分后的区间添加到新增列表
			resultSet.Range(func(item *ceprange_util.CepRangeItem) {
				actuallyAddCepRangeList = append(actuallyAddCepRangeList, &model.LineOperationServiceableCepRangeTab{
					LineID:                maybeDeleteCepRange.LineID,
					Region:                maybeDeleteCepRange.Region,
					CollectDeliverGroupID: maybeDeleteCepRange.CollectDeliverGroupID,
					Initial:               item.GetCepStart(),
					Final:                 item.GetCepEnd(),
					DisablePickup:         maybeDeleteCepRange.DisablePickup,
					DisableCodPickup:      maybeDeleteCepRange.DisableCodPickup,
					DisableDeliver:        maybeDeleteCepRange.DisableDeliver,
					DisableCodDeliver:     maybeDeleteCepRange.DisableCodDeliver,
					DisableTradeIn:        maybeDeleteCepRange.DisableTradeIn,
				})
			})
			isAutoMergeOrSplitFlag = true
		}
	}
	// 4.4 对新增区间进行删除或拆分
	for _, addCepRange := range mayAddCepRangeList {
		key := serviceable_util.GetLineServiceableGroupKey(addCepRange)
		var deleteSet ceprange_util.CepRangeSet
		for _, deleteCepRange := range deleteCepRangeMap[key] {
			// 如果删除区间与新增区间有重叠，则添加到delete集合里
			if ceprange_util.Overlap(addCepRange, deleteCepRange) {
				deleteSet.Add(deleteCepRange)
			}
		}
		if deleteSet.Len() == 0 {
			actuallyAddCepRangeList = append(actuallyAddCepRangeList, addCepRange)
			continue
		}

		// 区间作差，新增区间 - 所有有重叠的删除区间
		var originSet ceprange_util.CepRangeSet
		originSet.Add(addCepRange)
		resultSet := ceprange_util.Subtract(originSet, deleteSet)

		// 如果resultSet不为空，说明此区间会被拆分为多段，将拆分后的区间添加到实际新增列表
		resultSet.Range(func(item *ceprange_util.CepRangeItem) {
			actuallyAddCepRangeList = append(actuallyAddCepRangeList, &model.LineOperationServiceableCepRangeTab{
				LineID:                addCepRange.LineID,
				Region:                addCepRange.Region,
				CollectDeliverGroupID: addCepRange.CollectDeliverGroupID,
				Initial:               item.GetCepStart(),
				Final:                 item.GetCepEnd(),
				DisablePickup:         addCepRange.DisablePickup,
				DisableCodPickup:      addCepRange.DisableCodPickup,
				DisableDeliver:        addCepRange.DisableDeliver,
				DisableCodDeliver:     addCepRange.DisableCodDeliver,
				DisableTradeIn:        addCepRange.DisableTradeIn,
			})
		})
		isAutoMergeOrSplitFlag = true
	}

	// 5. 解析和校验完成，根据async flag创建定时任务或者将配置落库
	if async {
		return result, nil
	}
	fn := func() *lcos_error.LCOSError {
		if err := l.lineOperationServiceableCepRangeDAO.BatchDeleteLineOperationServiceableCepRangeModel(ctx, actuallyDeleteCepRangeList, constant.DBMAXBATCHNUM); err != nil {
			return err
		}
		if err := l.lineOperationServiceableCepRangeDAO.BatchCreateLineOperationServiceableCepRangeModel(ctx, actuallyAddCepRangeList); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fn); err != nil {
		return result, err
	}

	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineOperationCepRangeTabNamespace)
	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(lineIdList, ","))

	if isAutoMergeOrSplitFlag {
		alertLineOperationCepRangeAutoMergeOrSplit(ctx, actuallyDeleteCepRangeList, actuallyAddCepRangeList, "Upload File")
	}
	return result, nil
}

func checkAndParseFile(ctx utils.LCOSContext, region, filePath string, header []excel.ParseableField, result *excel.ParseFileResult) ([]string, []string, []string, []*model.LineOperationServiceableCepRangeTab, []*model.LineOperationServiceableCepRangeTab) {
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file complete, lineNum:%d, cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, err := excelize.OpenFile(filePath)
	if err != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
		return nil, nil, nil, nil, nil
	}
	rows, err := file.Rows("Sheet1")
	if err != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
		return nil, nil, nil, nil, nil
	}
	defer os.Remove(filePath)

	lineIdMap := make(map[string]bool)
	var lineIdList []string
	var rowDataList []*serviceable_util.LineOperationCepRangeRowData
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount()

		rowData := &serviceable_util.LineOperationCepRangeRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}
		if region != rowData.Region {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", region, rowData.RowId))
			continue
		}
		if rowData.CepFinal < rowData.CepInitial {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Final should not be less than CEP Initial | row=%d", rowData.RowId))
			continue
		}

		if _, ok := lineIdMap[rowData.LineId]; !ok {
			lineIdList = append(lineIdList, rowData.LineId)
			lineIdMap[rowData.LineId] = true
		}

		rowDataList = append(rowDataList, rowData)
	}

	if len(lineIdList) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is empty")
		return nil, nil, nil, nil, nil
	}

	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIdList)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return nil, nil, nil, nil, nil
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}
	//lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, lineIdList)
	//for lineId, lineDraft := range lineDraftMap {
	//	lineInfoMap[lineId] = lineDraft
	//}
	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(ctx, region, lineIdList, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return nil, nil, nil, nil, nil
	}
	addCepRangeDataList := make([]*model.LineOperationServiceableCepRangeTab, 0, 500)
	delCepRangeDataList := make([]*model.LineOperationServiceableCepRangeTab, 0, 500)
	var addLineList []string
	var addLineMap = make(map[string]bool)
	var deleteLineList []string
	var deleteLineMap = make(map[string]bool)
	for _, rowData := range rowDataList {
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}

		parsedData := &model.LineOperationServiceableCepRangeTab{
			LineID:                rowData.LineId,
			CollectDeliverGroupID: rowData.GroupId,
			Region:                rowData.Region,
			Initial:               uint64(rowData.CepInitial),
			Final:                 uint64(rowData.CepFinal),
			DisablePickup:         rowData.BanPickup,
			DisableCodPickup:      rowData.BanCodPickup,
			DisableDeliver:        rowData.BanDeliver,
			DisableCodDeliver:     rowData.BanCodDeliver,
			DisableTradeIn:        rowData.DisAbleTradeIn,
		}
		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			if _, ok := addLineMap[parsedData.LineID]; !ok {
				addLineList = append(addLineList, parsedData.LineID)
				addLineMap[parsedData.LineID] = true
			}
			addCepRangeDataList = append(addCepRangeDataList, parsedData)
		} else {
			if _, ok := deleteLineMap[parsedData.LineID]; !ok {
				deleteLineList = append(deleteLineList, parsedData.LineID)
				deleteLineMap[parsedData.LineID] = true
			}
			delCepRangeDataList = append(delCepRangeDataList, parsedData)
		}
	}

	return lineIdList, addLineList, deleteLineList, addCepRangeDataList, delCepRangeDataList
}

func checkTableHeader(row []string) error {

	if len(row) < 10 {
		return errors.New("wrong column number")
	}

	expectTitlt := [10]string{
		"*Line ID", "*Group ID", "*Region", "*CEP Initial", "*CEP Final", "*Ban Pickup", "*Ban COD Pickup", "*Ban Deliver", "*Ban COD Deliver", "*Action Code",
	}

	for i, name := range row {
		if i == 10 {
			return nil
		}
		if name != expectTitlt[i] {
			return fmt.Errorf("title must be in:%+v", expectTitlt)
		}
	}

	return nil
}

func parseSingleRow(region string, rowId int, row []string) (*model.LineOperationServiceableCepRangeTab, bool, *lcos_error.LCOSError) {
	rowData := &serviceable_util.LineOperationCepRangeRowData{}
	if err := excel.ParseRowDataWithHeader(rowId, row, serviceable_util.LineOperationCepRangeHeader, rowData); err != nil {
		return nil, false, err
	}
	if region != rowData.Region {
		return nil, false, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", region, rowId)
	}

	if rowData.CepFinal < rowData.CepInitial {
		return nil, false, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Final should not be less than CEP Initial | row=%d", rowId)
	}

	addFlag := rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode

	return &model.LineOperationServiceableCepRangeTab{
		LineID:                rowData.LineId,
		CollectDeliverGroupID: rowData.GroupId,
		Region:                rowData.Region,
		Initial:               uint64(rowData.CepInitial),
		Final:                 uint64(rowData.CepFinal),
		DisablePickup:         rowData.BanPickup,
		DisableCodPickup:      rowData.BanCodPickup,
		DisableDeliver:        rowData.BanDeliver,
		DisableCodDeliver:     rowData.BanCodDeliver,
		DisableTradeIn:        rowData.DisAbleTradeIn,
	}, addFlag, nil
}

func alertLineOperationCepRangeAutoMergeOrSplit(ctx utils.LCOSContext, deleteList, addList []*model.LineOperationServiceableCepRangeTab, changeType string) {
	var (
		lineList []string
		lineMap  = make(map[string]struct{})
		rows     = make([]interface{}, 0, len(deleteList)+len(addList))
	)

	for _, deleteData := range deleteList {
		if _, ok := lineMap[deleteData.LineID]; !ok {
			lineList = append(lineList, deleteData.LineID)
			lineMap[deleteData.LineID] = struct{}{}
		}

		rows = append(rows, &serviceable_util.LineOperationCepRangeRowData{
			LineId:        deleteData.LineID,
			GroupId:       deleteData.CollectDeliverGroupID,
			Region:        deleteData.Region,
			CepInitial:    int(deleteData.Initial),
			CepFinal:      int(deleteData.Final),
			BanPickup:     deleteData.DisablePickup,
			BanCodPickup:  deleteData.DisableCodPickup,
			BanDeliver:    deleteData.DisableDeliver,
			BanCodDeliver: deleteData.DisableCodDeliver,
			ActionCode:    -1,
		})
	}
	for _, addData := range addList {
		if _, ok := lineMap[addData.LineID]; !ok {
			lineList = append(lineList, addData.LineID)
			lineMap[addData.LineID] = struct{}{}
		}

		rows = append(rows, &serviceable_util.LineOperationCepRangeRowData{
			LineId:        addData.LineID,
			GroupId:       addData.CollectDeliverGroupID,
			Region:        addData.Region,
			CepInitial:    int(addData.Initial),
			CepFinal:      int(addData.Final),
			BanPickup:     addData.DisablePickup,
			BanCodPickup:  addData.DisableCodPickup,
			BanDeliver:    addData.DisableDeliver,
			BanCodDeliver: addData.DisableCodDeliver,
			ActionCode:    1,
		})
	}

	filePath := fmt.Sprintf("/tmp/line_operation_ceprange_change_%d_%s.xlsx", utils.GetTimestamp(ctx), utils.GetUUID(ctx))
	file := xlsx.NewFile()
	titles := make([]string, 0, len(serviceable_util.LineOperationCepRangeHeader))
	for _, field := range serviceable_util.LineOperationCepRangeHeader {
		titles = append(titles, field.Name)
	}
	if err := excel.WriteTitleAndStruct(file, "Sheet1", titles, rows); err != nil {
		logger.CtxLogErrorf(ctx, "alert line operation cep range auto merge/split failed, write file error|cause=%s", err.Error())
		return
	}
	if err := file.Save(filePath); err != nil {
		logger.CtxLogErrorf(ctx, "alert line operation cep range auto merge/split failed, save file error|cause=%s", err.Error())
		return
	}

	if err := serviceable_util.AlertLineCepRangeSAAutoMergeOrSplit(ctx, lineList, changeType, filePath); err != nil {
		logger.CtxLogErrorf(ctx, "alert line operation cep range auto merge/split failed, upload file or send message error|cause=%s", err.Msg)
		return
	}
}
