package operation_postcode

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	basicPostcodeModel "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	originPostCodeModel "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/jinzhu/copier"
	"github.com/mohae/deepcopy"
)

type LineOperationServiceablePostcodeServiceInterface interface {
	CreateOperationServiceableOriginPostcode(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationOriginPostcodeRequest) (*originPostCodeModel.LogisticLineOperationServiceableOriginPostcodeTab, *lcos_error.LCOSError)
	CreateOperationServiceableDestPostcode(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationDestPostcodeRequest) (*operation_postcode.LogisticLineOperationServiceableDestPostcodeTab, *lcos_error.LCOSError)
	UpdateOperationServiceableOriginPostcode(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationOriginPostcodeRequest) (*originPostCodeModel.LogisticLineOperationServiceableOriginPostcodeTab, *lcos_error.LCOSError)
	UpdateOperationServiceableDestPostcode(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationDestPostcodeRequest) (*operation_postcode.LogisticLineOperationServiceableDestPostcodeTab, *lcos_error.LCOSError)
	GetOperationServiceableOriginPostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationOriginPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllOperationServiceableOriginPostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationOriginPostcodeRequest) ([]*originPostCodeModel.LogisticLineOperationServiceableOriginPostcodeTab, *lcos_error.LCOSError)
	GetOperationServiceableDestPostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationDestPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError)
	GetAllOperationServiceableDestPostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationDestPostcodeRequest) ([]*operation_postcode.LogisticLineOperationServiceableDestPostcodeTab, *lcos_error.LCOSError)
	DeleteOperationServiceableOriginPostcode(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	DeleteOperationServiceableDestPostcode(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	UploadOperationServiceableOriginPostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	UploadOperationServiceableDestPostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError

	// 创建运营层postcode
	CreateOperationServiceablePostcode(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationPostcodeRequest) (*originPostCodeModel.LineOperationServiceablePostcodeTab, *lcos_error.LCOSError)
	// 更新运营层postcode
	UpdateOperationServiceablePostcode(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationPostcodeRequest) (*originPostCodeModel.LineOperationServiceablePostcodeTab, *lcos_error.LCOSError)
	// 获取运营层postcode分页列表
	GetOperationServiceablePostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError)
	// 获取运营层所有postcode
	GetAllOperationServiceablePostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationPostcodeRequest) ([]*originPostCodeModel.LineOperationServiceablePostcodeTab, *lcos_error.LCOSError)
	// 删除运营层postcode
	DeleteOperationServiceablePostcode(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	// 删除运营层postcode
	DeleteOperationServiceablePostcodeByLineId(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError
	// 上传运营层postcode
	UploadOperationServiceablePostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	ParseAndImportOperationPostcodeSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError)
	// 同步搜索的postcode到基础层
	SyncSearchPostcodeToBasicserviceable(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationPostcodeRequest) *lcos_error.LCOSError
}

type LineOperationServiceablePostcodeService struct {
	lineOperationServiceablePostcodeDAO originPostCodeModel.LogisticLineOperationServiceablePostcodeTabDAO
	lineBasicServiceablePostcodeDAO     basicPostcodeModel.LineBasicServiceablePostcodeDAO
	scheduledService                    scheduled.ScheduledService
}

func (s *LineOperationServiceablePostcodeService) DeleteOperationServiceablePostcodeByLineId(ctx utils.LCOSContext, lineId string) *lcos_error.LCOSError {
	return s.lineOperationServiceablePostcodeDAO.DeleteLineOperationServiceablePostcodeTabsByLineId(ctx, lineId)
}

func NewLineOperationServiceablePostcodeService(lineOperationServiceableOriginPostcodeDAO originPostCodeModel.LogisticLineOperationServiceablePostcodeTabDAO,
	lineBasicServiceablePostcodeDAO basicPostcodeModel.LineBasicServiceablePostcodeDAO, scheduledService scheduled.ScheduledService) *LineOperationServiceablePostcodeService {
	return &LineOperationServiceablePostcodeService{
		lineOperationServiceablePostcodeDAO: lineOperationServiceableOriginPostcodeDAO,
		lineBasicServiceablePostcodeDAO:     lineBasicServiceablePostcodeDAO,
		scheduledService:                    scheduledService,
	}
}

// 创建运营层postcode
func (s *LineOperationServiceablePostcodeService) CreateOperationServiceablePostcode(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationPostcodeRequest) (*originPostCodeModel.LineOperationServiceablePostcodeTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	//exist, err := lls_service.CheckLineExist(ctx, request.LineID)
	//if err != nil {
	//return nil, err
	//}
	//if !exist {
	//logger.LogErrorf("create operation postcode failed, line_id not exist|lineId=%s", request.LineID)
	//return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	//}

	postcodeModel := new(originPostCodeModel.LineOperationServiceablePostcodeTab)
	_ = copier.Copy(postcodeModel, request)
	if _, err := s.lineOperationServiceablePostcodeDAO.CreateLineOperationServiceablePostcodeTab(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// 更新运营层postcode
func (s *LineOperationServiceablePostcodeService) UpdateOperationServiceablePostcode(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationPostcodeRequest) (*originPostCodeModel.LineOperationServiceablePostcodeTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	operationPostcodeModel, err := s.lineOperationServiceablePostcodeDAO.GetLineOperationServiceablePostcodeTabById(ctx, request.ID)
	if err != nil {
		return nil, err
	}
	if operationPostcodeModel == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundOperationPostcodeErrorCode)
	}

	dbDisablePickup := *operationPostcodeModel.DisablePickup
	dbDisableCodPickup := *operationPostcodeModel.DisableCodPickup
	dbDisableDeliver := *operationPostcodeModel.DisableDeliver
	dbDisableCodDeliver := *operationPostcodeModel.DisableCodDeliver

	_ = copier.Copy(operationPostcodeModel, request)
	if _, err := s.lineOperationServiceablePostcodeDAO.UpdateLineOperationServiceablePostcodeTab(ctx, operationPostcodeModel); err != nil {
		return nil, err
	}

	// 如果运营层一项或多项从Ban改为不Ban, 需要warning机制
	searchMap := make(map[string]interface{})
	searchMap["line_id"] = request.LineID
	searchMap["collect_deliver_group_id"] = request.CollectDeliverGroupId
	searchMap["postcode"] = request.Postcode
	basicPostcodeModels, err := s.lineBasicServiceablePostcodeDAO.SearchAllBasicServiceablePostcode(ctx, request.LineID, searchMap)
	if err != nil {
		return nil, err
	}
	if len(basicPostcodeModels) == 0 { // 如果基础层不存在，则直接返回
		return operationPostcodeModel, nil
	}
	basicOperation := basicPostcodeModels[0]
	if (dbDisablePickup == 1 && *request.DisablePickup == 0 && *basicOperation.CanPickup == 0) ||
		(dbDisableCodPickup == 1 && *request.DisableCodPickup == 0 && *basicOperation.CanCodPickup == 0) ||
		(dbDisableDeliver == 1 && *request.DisableDeliver == 0 && *basicOperation.CanDeliver == 0) ||
		(dbDisableCodDeliver == 1 && *request.DisableCodDeliver == 0 && *basicOperation.CanCodDeliver == 0) {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.SupportOperationPostcodeButNotSupportBasicErrorCode)
	}
	return operationPostcodeModel, nil
}

// 获取运营层postcode分页列表
func (s *LineOperationServiceablePostcodeService) GetOperationServiceablePostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID) // 为了权限控制
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "line base info not found")
	}
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	searchParams, _ := utils.Struct2map(request)
	models, total, err := s.lineOperationServiceablePostcodeDAO.SearchLineOperationServiceablePostcodeTab(ctx, pageNo, count, searchParams)
	if err != nil {
		return nil, err
	}

	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// 获取运营层所有postcode
func (s *LineOperationServiceablePostcodeService) GetAllOperationServiceablePostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationPostcodeRequest) ([]*originPostCodeModel.LineOperationServiceablePostcodeTab, *lcos_error.LCOSError) {
	searchParams, _ := utils.Struct2map(request)
	models, err := s.lineOperationServiceablePostcodeDAO.SearchAllLineOperationServiceablePostcodeTab(ctx, searchParams)
	if err != nil {
		return nil, err
	}

	return models, nil
}

// 删除运营层postcode
func (s *LineOperationServiceablePostcodeService) DeleteOperationServiceablePostcode(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	return s.lineOperationServiceablePostcodeDAO.DeleteLineOperationServiceablePostcodeTabsById(ctx, request.ID)
}

// TODO 上传运营层postcode
func (s *LineOperationServiceablePostcodeService) UploadOperationServiceablePostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	job, err := s.scheduledService.CreateScheduledJob(ctx, request.Region, scheduled_constant.OperationServiceableArea, scheduled_constant.PostCode, request.FileUrl, request.IsScheduled, request.ScheduledTime, ctx.GetUserEmail())
	if err != nil {
		return err
	}

	go func() {
		parseFileResult, _ := s.ParseAndImportOperationPostcodeSA(ctx, request, serviceable_util.LineOperationPostcodeHeader, true)
		if err := s.scheduledService.UpdateValidateResult(ctx, job, parseFileResult); err != nil {
			logger.CtxLogErrorf(ctx, "scheduled job update validate result error|job_id=%d, cause=%s", job.ID, err.Msg)
		}
	}()

	return nil
}

func (s *LineOperationServiceablePostcodeService) ParseAndImportOperationPostcodeSA(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}

	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return result, nil
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
		return result, nil
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
		return result, nil
	}
	defer os.Remove(filePath)

	uniqueKeyMap := make(map[string]int)
	lineIdMap := map[string]bool{}
	var lineIdSet []string
	var rowDataList []*serviceable_util.LineOperationPostcodeRowData
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount()

		rowData := &serviceable_util.LineOperationPostcodeRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}
		if request.Region != rowData.Region {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch %s | row=%d", request.Region, rowData.RowId))
			continue
		}
		uniqueKey := utils.GenKey("-", rowData.LineId, rowData.GroupId, rowData.Postcode)
		if lastRowId, ok := uniqueKeyMap[uniqueKey]; ok {
			result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import data duplicated with same Line-Group-Address, please check | row %d duplicate with row %d, unique key=%s", rowData.RowId, lastRowId, uniqueKey)
			return result, nil
		} else {
			uniqueKeyMap[uniqueKey] = rowData.RowId
		}

		if _, ok := lineIdMap[rowData.LineId]; !ok {
			lineIdSet = append(lineIdSet, rowData.LineId)
			lineIdMap[rowData.LineId] = true
		}

		rowDataList = append(rowDataList, rowData)
	}

	if len(lineIdSet) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is emtpy")
		return result, nil
	}

	//lineInfoMap, lcosErr := lls_service.BatchGetLineInfosMap(ctx, lineIdSet)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return result, nil
	//}
	//if lineInfoMap == nil {
	//	lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	//}
	//lineDraftMap, lcosErr := lls_service.BatchGetLineDraftsMap(ctx, lineIdSet)
	//if lcosErr != nil {
	//	result.ParseFileErr = lcosErr
	//	return result, nil
	//}
	//for k, v := range lineDraftMap {
	//	lineInfoMap[k] = v
	//}
	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(ctx, request.Region, lineIdSet, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return result, nil
	}
	var addPostcodeModels []*operation_postcode.LineOperationServiceablePostcodeTab
	var deletePostcodeModels []*operation_postcode.LineOperationServiceablePostcodeTab
	for _, rowData := range rowDataList {
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}

		postcodeModel := &operation_postcode.LineOperationServiceablePostcodeTab{
			LineID:                rowData.LineId,
			CollectDeliverGroupId: rowData.GroupId,
			Region:                rowData.Region,
			Postcode:              rowData.Postcode,
			DisablePickup:         &rowData.BanPickup,
			DisableCodPickup:      &rowData.BanCodPickup,
			DisableDeliver:        &rowData.BanDeliver,
			DisableCodDeliver:     &rowData.BanCodDeliver,
			DisableTradeIn:        rowData.BanTradeIn,
		}
		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			addPostcodeModels = append(addPostcodeModels, postcodeModel)
		} else {
			deletePostcodeModels = append(deletePostcodeModels, postcodeModel)
		}
	}

	if async {
		return result, nil
	}
	fc := func() *lcos_error.LCOSError {
		if err := s.lineOperationServiceablePostcodeDAO.BatchDeleteLineOperationServiceablePostcodeTabs(ctx, addPostcodeModels); err != nil {
			return err
		}
		if _, err := s.lineOperationServiceablePostcodeDAO.BatchCreateLineOperationServiceablePostcodeTab(ctx, addPostcodeModels); err != nil {
			return err
		}
		if err := s.lineOperationServiceablePostcodeDAO.BatchDeleteLineOperationServiceablePostcodeTabs(ctx, deletePostcodeModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return result, err
	}
	change_report.SetChangeReportExtraBusinessDetail(ctx, strings.Join(lineIdSet, ","))
	return result, nil
}

// TODO 同步搜索的postcode到基础层
func (s *LineOperationServiceablePostcodeService) SyncSearchPostcodeToBasicserviceable(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationPostcodeRequest) *lcos_error.LCOSError {
	searchMap, _ := utils.Struct2map(request)
	if ctx.GetClientType() > 0 {
		allowLineIds, err := lls_service.GetLineIdsByClientType(ctx, ctx.GetClientType())
		if err != nil {
			return err
		}
		if len(allowLineIds) == 0 {
			return nil
		}
		searchMap["LINE_ID"] = allowLineIds
	}
	models, err := s.lineOperationServiceablePostcodeDAO.SearchAllLineOperationServiceablePostcodeTab(ctx, searchMap)
	if err != nil {
		return err
	}
	if len(models) == 0 {
		return nil
	}

	updatePostcodeModels := make([]*basicPostcodeModel.LineBasicServiceablePostcodeTab, 0)
	insertPostcodeModels := make([]*basicPostcodeModel.LineBasicServiceablePostcodeTab, 0)

	var searchBasicPostcodeModels []*basicPostcodeModel.LineBasicServiceablePostcodeTab
	operationPostcodeMap := make(map[string]*operation_postcode.LineOperationServiceablePostcodeTab)
	basicPostcodeMap := make(map[string]*basicPostcodeModel.LineBasicServiceablePostcodeTab)

	// 构造查询条件，查询basic层postcode
	querySlice := make([][]interface{}, 0)
	for _, operationPostcode := range models {
		queryItem := make([]interface{}, 0)
		queryItem = append(queryItem, operationPostcode.LineID, operationPostcode.Postcode, operationPostcode.CollectDeliverGroupId)
		querySlice = append(querySlice, queryItem)

		uniqKey := fmt.Sprintf("%s-%s-%s", operationPostcode.LineID, operationPostcode.Postcode, operationPostcode.CollectDeliverGroupId)
		operationPostcodeMap[uniqKey] = operationPostcode
	}
	tableName := basicPostcodeModel.GetBasicPostcodeTableName(request.LineId)
	db := ctx.ReadDB().Table(tableName).Where("(`line_id`,`postcode`,`collect_deliver_group_id`) in ?", querySlice).Find(&searchBasicPostcodeModels)
	if db.GetError() != nil {
		logger.LogErrorf("batch query basic location err:%s", db.GetError().Error())
		return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, db.GetError().Error())
	}
	for _, basicPostcode := range searchBasicPostcodeModels {
		uniqKey := fmt.Sprintf("%s-%s-%s", basicPostcode.LineId, basicPostcode.Postcode, basicPostcode.CollectDeliverGroupId)
		basicPostcodeMap[uniqKey] = basicPostcode
	}

	for k, operationPostcode := range operationPostcodeMap {
		if basicPostcode, ok := basicPostcodeMap[k]; ok {
			updateFlag := false
			copyPostcode := deepcopy.Copy(basicPostcode)
			updateBasicPostcode := copyPostcode.(*basicPostcodeModel.LineBasicServiceablePostcodeTab)
			if *operationPostcode.DisablePickup == 1 && *basicPostcode.CanPickup == 1 {
				updateFlag = true
				var pickup = uint8(0)
				updateBasicPostcode.CanPickup = &pickup
			}
			if *operationPostcode.DisableCodPickup == 1 && *basicPostcode.CanCodPickup == 1 {
				updateFlag = true
				var codPickup = uint8(0)
				updateBasicPostcode.CanCodPickup = &codPickup
			}
			if *operationPostcode.DisableDeliver == 1 && *basicPostcode.CanDeliver == 1 {
				updateFlag = true
				var dilver = uint8(0)
				updateBasicPostcode.CanDeliver = &dilver
			}
			if *operationPostcode.DisableCodDeliver == 1 && *basicPostcode.CanCodDeliver == 1 {
				updateFlag = true
				var codDeliver = uint8(0)
				updateBasicPostcode.CanCodDeliver = &codDeliver
			}
			if updateFlag {
				updatePostcodeModels = append(updatePostcodeModels, updateBasicPostcode)
			}
		} else {
			canPickup := 1 - *operationPostcode.DisablePickup
			canCodPickup := 1 - *operationPostcode.DisableCodPickup
			canDeliver := 1 - *operationPostcode.DisableDeliver
			canCodDeliver := 1 - *operationPostcode.DisableCodDeliver
			insertBasicPostCode := &basicPostcodeModel.LineBasicServiceablePostcodeTab{
				LineId:                operationPostcode.LineID,
				CollectDeliverGroupId: operationPostcode.CollectDeliverGroupId,
				Region:                operationPostcode.Region,
				Postcode:              operationPostcode.Postcode,
				CanPickup:             &canPickup,
				CanCodPickup:          &canCodPickup,
				CanDeliver:            &canDeliver,
				CanCodDeliver:         &canCodDeliver,
			}
			insertPostcodeModels = append(insertPostcodeModels, insertBasicPostCode)
		}
	}

	fc := func() *lcos_error.LCOSError {
		if len(insertPostcodeModels) > 0 {
			_, err := s.lineBasicServiceablePostcodeDAO.BatchCreateServiceablePostcodeModel(ctx, request.LineId, insertPostcodeModels)
			if err != nil {
				return err
			}
		}
		for _, updateModel := range updatePostcodeModels {
			_, err := s.lineBasicServiceablePostcodeDAO.UpdateBasicServiceablePostcodeModel(ctx, updateModel)
			if err != nil {
				return err
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}
	return nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) CreateOperationServiceableOriginPostcode(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationOriginPostcodeRequest) (*originPostCodeModel.LogisticLineOperationServiceableOriginPostcodeTab, *lcos_error.LCOSError) {
	exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if e != nil {
		return nil, e
	}
	if !exists {
		logger.LogErrorf("create origin postcode failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}

	postcodeModel := new(originPostCodeModel.LogisticLineOperationServiceableOriginPostcodeTab)
	_ = copier.Copy(postcodeModel, request)
	if _, err := s.lineOperationServiceablePostcodeDAO.CreateLogisticLineOperationServiceableOriginPostcodeTab(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) CreateOperationServiceableDestPostcode(ctx utils.LCOSContext, request *operation_serviceable.CreateOperationDestPostcodeRequest) (*operation_postcode.LogisticLineOperationServiceableDestPostcodeTab, *lcos_error.LCOSError) {
	exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if e != nil {
		return nil, e
	}
	if !exists {
		logger.LogErrorf("create dest postcode failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}

	postcodeModel := new(operation_postcode.LogisticLineOperationServiceableDestPostcodeTab)
	_ = copier.Copy(postcodeModel, request)
	if _, err := s.lineOperationServiceablePostcodeDAO.CreateLogisticLineOperationServiceableDestPostcodeTab(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) UpdateOperationServiceableOriginPostcode(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationOriginPostcodeRequest) (*originPostCodeModel.LogisticLineOperationServiceableOriginPostcodeTab, *lcos_error.LCOSError) {
	postcodeModel := new(originPostCodeModel.LogisticLineOperationServiceableOriginPostcodeTab)
	_ = copier.Copy(postcodeModel, request)
	if _, err := s.lineOperationServiceablePostcodeDAO.UpdateLogisticLineOperationServiceableOriginPostcodeTab(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) UpdateOperationServiceableDestPostcode(ctx utils.LCOSContext, request *operation_serviceable.UpdateOperationDestPostcodeRequest) (*operation_postcode.LogisticLineOperationServiceableDestPostcodeTab, *lcos_error.LCOSError) {
	postcodeModel := new(operation_postcode.LogisticLineOperationServiceableDestPostcodeTab)
	_ = copier.Copy(postcodeModel, &request.CreateOperationDestPostcodeRequest)
	if _, err := s.lineOperationServiceablePostcodeDAO.UpdateLogisticLineOperationServiceableDestPostcodeTab(ctx, postcodeModel); err != nil {
		return nil, err
	}
	return postcodeModel, nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) GetOperationServiceableOriginPostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationOriginPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	searchParams, _ := utils.Struct2map(request)
	models, total, err := s.lineOperationServiceablePostcodeDAO.SearchLogisticLineOperationServiceableOriginPostcodeTab(ctx, pageNo, count, searchParams)
	if err != nil {
		return nil, err
	}

	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) GetAllOperationServiceableOriginPostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationOriginPostcodeRequest) ([]*operation_postcode.LogisticLineOperationServiceableOriginPostcodeTab, *lcos_error.LCOSError) {
	searchParams, _ := utils.Struct2map(request)
	models, err := s.lineOperationServiceablePostcodeDAO.SearchAllLogisticLineOperationServiceableOriginPostcodeTab(ctx, searchParams)
	if err != nil {
		return nil, err
	}

	return models, nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) GetOperationServiceableDestPostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetOperationDestPostcodeListRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	searchParams, _ := utils.Struct2map(request)
	models, total, err := s.lineOperationServiceablePostcodeDAO.SearchLogisticLineOperationServiceableDestPostcodeTab(ctx, pageNo, count, searchParams)
	if err != nil {
		return nil, err
	}
	return &common.PageModel{
		PageNO: pageNo,
		Total:  total,
		Count:  count,
		List:   models,
	}, nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) GetAllOperationServiceableDestPostcodeList(ctx utils.LCOSContext, request *operation_serviceable.GetAllOperationDestPostcodeRequest) ([]*operation_postcode.LogisticLineOperationServiceableDestPostcodeTab, *lcos_error.LCOSError) {
	searchParams, _ := utils.Struct2map(request)
	models, err := s.lineOperationServiceablePostcodeDAO.SearchAllLogisticLineOperationServiceableDestPostcodeTab(ctx, searchParams)
	if err != nil {
		return nil, err
	}
	return models, nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) DeleteOperationServiceableOriginPostcode(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	return s.lineOperationServiceablePostcodeDAO.DeleteLogisticLineOperationServiceableOriginPostcodeTabsById(ctx, request.ID)
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) DeleteOperationServiceableDestPostcode(ctx utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	return s.lineOperationServiceablePostcodeDAO.DeleteLogisticLineOperationServiceableDestPostcodeTabsById(ctx, request.ID)
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) UploadOperationServiceableOriginPostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_operation_origin_postcode.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	var addPostcodeModels []*operation_postcode.LogisticLineOperationServiceableOriginPostcodeTab
	var deletePostcodeModels []*operation_postcode.LogisticLineOperationServiceableOriginPostcodeTab
	var mutex sync.Mutex
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		if err := serviceable_util.CheckPostcodeRowData(row, request.Region, lineNum); err != nil {
			return err
		}

		wg.Add(1)
		limitChannel <- struct{}{}
		go func(rowData []string) {
			rowLineId := rowData[0]
			exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, rowLineId)
			if e != nil {
				errList = append(errList, e)
			}
			if !exists {
				logger.LogErrorf("upload postcode failed, line_id not exist|lineId=%s", rowLineId)
				errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
			}

			rowStruct := serviceable_util.ParsePostcodeRowData(row)
			postcodeModel := &operation_postcode.LogisticLineOperationServiceableOriginPostcodeTab{
				LineID: rowStruct.LineId,
				//CollectServiceType: uint64(rowStruct.ServiceType),
				Region:   request.Region,
				Postcode: rowStruct.Postcode,
				/*DisablePickup:      &rowStruct.PickupDeliver,
				DisableCodPickup:   &rowStruct.CodPickupDeliver,*/
			}
			mutex.Lock()
			if rowStruct.ActionCode != -1 {
				addPostcodeModels = append(addPostcodeModels, postcodeModel)
			} else {
				deletePostcodeModels = append(deletePostcodeModels, postcodeModel)
			}
			mutex.Unlock()

			<-limitChannel
			wg.Done()
		}(row)
	}
	wg.Wait()

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}

	fc := func() *lcos_error.LCOSError {
		if err := s.lineOperationServiceablePostcodeDAO.BatchDeleteLogisticLineOperationServiceableOriginPostcodeTabs(ctx, addPostcodeModels); err != nil {
			return err
		}

		if _, err := s.lineOperationServiceablePostcodeDAO.BatchCreateLogisticLineOperationServiceableOriginPostcodeTab(ctx, addPostcodeModels); err != nil {
			return err
		}

		if err := s.lineOperationServiceablePostcodeDAO.BatchDeleteLogisticLineOperationServiceableOriginPostcodeTabs(ctx, deletePostcodeModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}

// Deprecated
func (s *LineOperationServiceablePostcodeService) UploadOperationServiceableDestPostcode(ctx utils.LCOSContext, request *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Downloads/test_operation_dest_postcode.xlsx")
	//lineNum := 0
	//rows, _ := file.Rows("Sheet1")

	var addPostcodeModels []*operation_postcode.LogisticLineOperationServiceableDestPostcodeTab
	var deletePostcodeModels []*operation_postcode.LogisticLineOperationServiceableDestPostcodeTab
	var mutex sync.Mutex
	var errList []*lcos_error.LCOSError

	wg := sync.WaitGroup{}
	limitChannel := make(chan struct{}, constant.REMOTESERVICEMAXBATCHNUM)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}

		if err := serviceable_util.CheckPostcodeRowData(row, request.Region, lineNum); err != nil {
			return err
		}

		wg.Add(1)
		limitChannel <- struct{}{}
		go func(rowData []string) {
			rowLineId := rowData[0]
			exists, e := lls_service.CheckLineAndInstallationLineExist(ctx, rowLineId)
			if e != nil {
				errList = append(errList, e)
			}
			if !exists {
				logger.LogErrorf("upload postcode failed, line_id not exist|lineId=%s", rowLineId)
				errList = append(errList, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "upload failed, line_id not found|lineId: "+rowLineId))
			}

			rowStruct := serviceable_util.ParsePostcodeRowData(row)
			postcodeModel := &operation_postcode.LogisticLineOperationServiceableDestPostcodeTab{
				LineID: rowStruct.LineId,
				//DeliverServiceType: uint64(rowStruct.ServiceType),
				Region:   request.Region,
				Postcode: rowStruct.Postcode,
				/*DisableDeliver:     &rowStruct.PickupDeliver,
				DisableCodDeliver:  &rowStruct.CodPickupDeliver,*/
			}
			mutex.Lock()
			if rowStruct.ActionCode != -1 {
				addPostcodeModels = append(addPostcodeModels, postcodeModel)
			} else {
				deletePostcodeModels = append(deletePostcodeModels, postcodeModel)
			}
			mutex.Unlock()

			<-limitChannel
			wg.Done()
		}(row)
	}

	wg.Wait()

	if len(errList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, lcos_error.GetMessageFromList(errList))
	}

	fc := func() *lcos_error.LCOSError {
		if err := s.lineOperationServiceablePostcodeDAO.BatchDeleteLogisticLineOperationServiceableDestPostcodeTabs(ctx, addPostcodeModels); err != nil {
			return err
		}

		if _, err := s.lineOperationServiceablePostcodeDAO.BatchCreateLogisticLineOperationServiceableDestPostcodeTab(ctx, addPostcodeModels); err != nil {
			return err
		}

		if err := s.lineOperationServiceablePostcodeDAO.BatchDeleteLogisticLineOperationServiceableDestPostcodeTabs(ctx, deletePostcodeModels); err != nil {
			return err
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}

	return nil
}
