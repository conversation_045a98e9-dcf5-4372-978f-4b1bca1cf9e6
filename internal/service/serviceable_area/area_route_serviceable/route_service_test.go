package area_route_serviceable

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	collectDeliver "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
)

func TestLineServiceableRouteService_Upload(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	type fields struct {
		lineServiceableRouteDAO    model.LogisticLineServiceableRouteTabDAO
		lineServiceableAreaDAO     area.LogisticLineServiceableAreaTabDAO
		lineCollectDeliverGroupDAO collectDeliver.LineCollectDeliverGroupConfDao
	}
	type args struct {
		c       utils.LCOSContext
		request *area_route_serviceable.UploadRouteRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "upload route",
			fields: fields{
				lineServiceableRouteDAO:    model.NewLogisticLineServiceableRouteTabDAO(),
				lineServiceableAreaDAO:     area.NewLogisticLineServiceableAreaTabDAO(),
				lineCollectDeliverGroupDAO: collectDeliver.NewLineCollectDeliverGroupConfDao(),
			},
			args: args{
				c: utils.NewCommonCtx(context.Background()),
				request: &area_route_serviceable.UploadRouteRequest{
					FileUrl: "/Users/<USER>/Desktop/serviceable-basic-zone-route.xlsx",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &LineServiceableRouteService{
				lineServiceableRouteDAO:    tt.fields.lineServiceableRouteDAO,
				lineServiceableAreaDAO:     tt.fields.lineServiceableAreaDAO,
				lineCollectDeliverGroupDAO: tt.fields.lineCollectDeliverGroupDAO,
			}
			if got := s.Upload(tt.args.c, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("LineServiceableRouteService.Upload() = %v, want %v", got, tt.want)
			}
		})
	}
}
