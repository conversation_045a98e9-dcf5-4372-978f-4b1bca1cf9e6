package area_route_serviceable

import (
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"os"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	collectDeliver "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/jinzhu/copier"
)

/*
* @Author: yajun.han
* @Date: 2020/8/5 12:12 上午
* @Name：area_route_serviceable
* @Description:
 */

type LineServiceableRouteServiceInterface interface {
	Create(c utils.LCOSContext, request *area_route_serviceable.CreateRouteRequest) (*model.LogisticLineServiceableRouteTab, *lcos_error.LCOSError)
	Delete(c utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError
	Update(c utils.LCOSContext, request *area_route_serviceable.UpdateRouteRequest) (*model.LogisticLineServiceableRouteTab, *lcos_error.LCOSError)
	GetByID(c utils.LCOSContext, request *common_protocol.IDRequest) (*model.LogisticLineServiceableRouteTab, *lcos_error.LCOSError)
	List(c utils.LCOSContext, request *area_route_serviceable.SearchRouteRequest) (*common.PageModel, *lcos_error.LCOSError)

	CreateLineRoute(c utils.LCOSContext, request *area_route_serviceable.CreateLineRouteRequest) (*model.LineServiceableRouteTab, *lcos_error.LCOSError)
	DeleteLineRoute(c utils.LCOSContext, request *area_route_serviceable.NewIDRequest) *lcos_error.LCOSError
	DeleteLineRouteByLineId(c utils.LCOSContext, lineId string) *lcos_error.LCOSError
	UpdateLineRoute(c utils.LCOSContext, request *area_route_serviceable.UpdateLineRouteRequest) (*model.LineServiceableRouteTab, *lcos_error.LCOSError)
	GetLineRouteByID(c utils.LCOSContext, request *area_route_serviceable.IDLineRouteRequest) (*model.LineServiceableRouteTab, *lcos_error.LCOSError)
	ListLineRoute(c utils.LCOSContext, request *area_route_serviceable.SearchLineRouteRequest) (*common.PageModel, *lcos_error.LCOSError)

	Upload(c utils.LCOSContext, request *area_route_serviceable.UploadRouteRequest) *lcos_error.LCOSError
	ParseAndImportBasicRouteSA(c utils.LCOSContext, region string, request *area_route_serviceable.UploadRouteRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError)
	GetRouteDataWithoutPaging(c utils.LCOSContext, request *area_route_serviceable.SearchAllLineRouteRequest) ([]*model.LineServiceableRouteTab, *lcos_error.LCOSError) // 提供给导出接口使用
	RouteMigration(c utils.LCOSContext) *lcos_error.LCOSError
}

type LineServiceableRouteService struct {
	lineServiceableRouteDAO    model.LogisticLineServiceableRouteTabDAO
	lineServiceableAreaDAO     area.LogisticLineServiceableAreaTabDAO
	lineCollectDeliverGroupDAO collectDeliver.LineCollectDeliverGroupConfDao
	scheduledService           scheduled.ScheduledService
}

func NewLineServiceableRouteService(lineServiceableRouteDAO model.LogisticLineServiceableRouteTabDAO, lineServiceableAreaDAO area.LogisticLineServiceableAreaTabDAO, lineCollectDeliverGroupDAO collectDeliver.LineCollectDeliverGroupConfDao, scheduledService scheduled.ScheduledService) *LineServiceableRouteService {
	return &LineServiceableRouteService{
		lineServiceableRouteDAO:    lineServiceableRouteDAO,
		lineServiceableAreaDAO:     lineServiceableAreaDAO,
		lineCollectDeliverGroupDAO: lineCollectDeliverGroupDAO,
		scheduledService:           scheduledService,
	}
}

func (s *LineServiceableRouteService) CreateLineRoute(ctx utils.LCOSContext, request *area_route_serviceable.CreateLineRouteRequest) (*model.LineServiceableRouteTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	if !exists {
		logger.LogErrorf("create route failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}
	routeTab := new(model.LineServiceableRouteTab)
	_ = copier.Copy(routeTab, request)

	fromArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(ctx, request.FromAreaID)
	if err != nil {
		return nil, err
	}
	if fromArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, "area id not found: "+strconv.Itoa(int(request.FromAreaID)))
	}
	routeTab.FromAreaName = fromArea.AreaName

	toArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(ctx, request.ToAreaID)
	if err != nil {
		return nil, err
	}
	if toArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, "area id not found: "+strconv.Itoa(int(request.FromAreaID)))
	}
	routeTab.ToAreaName = toArea.AreaName
	routeTab.RouteType = request.RouteType
	// check if there is duplicated data
	// for a certain line + grpup + fromArea + toArea, supported data cannot duplicate with unsupported one, vice versa
	if request.RouteType == constant.ROUTE_SUPPORTED {
		exist, err := s.lineServiceableRouteDAO.CheckLineServiceableRouteTabExist(ctx, request.LineID, request.CollectDeliverGroupId, request.FromAreaID, request.ToAreaID, constant.ROUTE_UNSUPPORTED)
		if err == nil && exist {
			return nil, lcos_error.NewLCOSError(lcos_error.CreateRouteErrorCode, "Create SUPPORTED data duplicated with a UNSUPPORTED one")
		}
	}

	if request.RouteType == constant.ROUTE_UNSUPPORTED {
		exist, err := s.lineServiceableRouteDAO.CheckLineServiceableRouteTabExist(ctx, request.LineID, request.CollectDeliverGroupId, request.FromAreaID, request.ToAreaID, constant.ROUTE_SUPPORTED)
		if err == nil && exist {
			return nil, lcos_error.NewLCOSError(lcos_error.CreateRouteErrorCode, "Create UNSUPPORTED data duplicated with a SUPPORTED one")
		}
	}

	routeModel, lcosErr := s.lineServiceableRouteDAO.CreateLineServiceableRouteTab(ctx, routeTab)
	if lcosErr != nil {
		return nil, lcosErr
	} else {
		// 手动刷新缓存
		if request.RouteType == constant.ROUTE_UNSUPPORTED {
			_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineBlacklistRouteTabNamespace)
		} else {
			_, _ = localcache.IncrLocalCacheVersion(ctx, constant.LineRouteTabNamespace2)
		}
		return routeModel, nil
	}
}

func (s *LineServiceableRouteService) DeleteLineRoute(c utils.LCOSContext, request *area_route_serviceable.NewIDRequest) *lcos_error.LCOSError {
	lcosErr := s.lineServiceableRouteDAO.DeleteLineServiceableRouteTabsById(c, request.ID, request.OldToAreaID, request.RouteType)
	if lcosErr != nil {
		return lcosErr
	} else {
		// 手动刷新缓存
		if request.RouteType == constant.ROUTE_UNSUPPORTED {
			_, _ = localcache.IncrLocalCacheVersion(c, constant.LineBlacklistRouteTabNamespace)
		} else {
			_, _ = localcache.IncrLocalCacheVersion(c, constant.LineRouteTabNamespace2)
		}
		return nil
	}
}

func (s *LineServiceableRouteService) DeleteLineRouteByLineId(c utils.LCOSContext, lineId string) *lcos_error.LCOSError {
	lcosErr := s.lineServiceableRouteDAO.DeleteLineServiceableRouteTabsByLineId(c, lineId)
	if lcosErr != nil {
		return lcosErr
	} else {
		// 手动刷新缓存
		_, _ = localcache.IncrLocalCacheVersion(c, constant.LineRouteTabNamespace2)
		_, _ = localcache.IncrLocalCacheVersion(c, constant.LineBlacklistRouteTabNamespace)
		return nil
	}
}

func (s *LineServiceableRouteService) UpdateLineRoute(c utils.LCOSContext, request *area_route_serviceable.UpdateLineRouteRequest) (*model.LineServiceableRouteTab, *lcos_error.LCOSError) {
	routeTab := new(model.LineServiceableRouteTab)
	_ = copier.Copy(routeTab, request)

	fromArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(c, request.FromAreaID)
	if err != nil {
		return nil, err
	}
	if fromArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, fmt.Sprintf("area id not found: %d", request.FromAreaID))
	}
	routeTab.FromAreaName = fromArea.AreaName

	toArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(c, request.ToAreaID)
	if err != nil {
		return nil, err
	}
	if toArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, fmt.Sprintf("area id not found: %d", request.FromAreaID))
	}
	routeTab.ToAreaName = toArea.AreaName
	// check if there is duplicated data
	// for a certain line + grpup + fromArea + toArea, supported data cannot duplicate with unsupported one, vice versa
	if request.RouteType == constant.ROUTE_SUPPORTED {
		exist, err := s.lineServiceableRouteDAO.CheckLineServiceableRouteTabExist(c, request.LineID, request.CollectDeliverGroupId, request.FromAreaID, request.ToAreaID, constant.ROUTE_UNSUPPORTED)
		if err == nil && exist {
			return nil, lcos_error.NewLCOSError(lcos_error.CreateRouteErrorCode, "Create SUPPORTED data duplicated with a UNSUPPORTED one")
		}
	}

	if request.RouteType == constant.ROUTE_UNSUPPORTED {
		exist, err := s.lineServiceableRouteDAO.CheckLineServiceableRouteTabExist(c, request.LineID, request.CollectDeliverGroupId, request.FromAreaID, request.ToAreaID, constant.ROUTE_SUPPORTED)
		if err == nil && exist {
			return nil, lcos_error.NewLCOSError(lcos_error.CreateRouteErrorCode, "Create UNSUPPORTED data duplicated with a SUPPORTED one")
		}
	}
	returnRouteModel, lcosErr := s.lineServiceableRouteDAO.UpdateLineServiceableRouteTab(c, routeTab, request.OldToAreaID)
	if lcosErr != nil {
		return nil, lcosErr
	} else {
		// 手动刷新缓存
		if request.RouteType == constant.ROUTE_UNSUPPORTED {
			_, _ = localcache.IncrLocalCacheVersion(c, constant.LineBlacklistRouteTabNamespace)
		} else {
			_, _ = localcache.IncrLocalCacheVersion(c, constant.LineRouteTabNamespace2)
		}
		return returnRouteModel, nil
	}
}

func (s *LineServiceableRouteService) GetLineRouteByID(c utils.LCOSContext, request *area_route_serviceable.IDLineRouteRequest) (*model.LineServiceableRouteTab, *lcos_error.LCOSError) {
	route, err := s.lineServiceableRouteDAO.GetLineServiceableRouteTabById(c, request.ID, request.RouteType)
	if err != nil {
		return nil, err
	}
	if route == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundRouteErrorCode)
	}
	return route, nil
}

func (s *LineServiceableRouteService) ListLineRoute(c utils.LCOSContext, request *area_route_serviceable.SearchLineRouteRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	request.PageNo = &pageNo
	request.Count = &count
	if request.RouteType == nil {
		sup := constant.ROUTE_SUPPORTED
		request.RouteType = &sup
	}
	data, total, lcosError := s.lineServiceableRouteDAO.SearchLineServiceableRouteTab(c, request)
	if lcosError == nil {
		for _, al := range data {
			al.RouteType = *request.RouteType
		}
	}
	if lcosError != nil {
		return nil, lcosError
	}

	if *request.RouteType == constant.ROUTE_SUPPORTED {
		lcosError = s.fillRouteWithName(c, data, request.Region)
		if lcosError != nil {
			return nil, lcosError
		}
	}
	return &common.PageModel{
		PageNO: pageNo,
		Count:  count,
		Total:  total,
		List:   data,
	}, lcosError
}

func (s *LineServiceableRouteService) fillRouteWithName(c utils.LCOSContext, data []*model.LineServiceableRouteTab, region string) *lcos_error.LCOSError {
	idSet := make(map[uint64]struct{})
	for _, d := range data {
		idSet[d.FromAreaID] = struct{}{}
		idSet[d.ToAreaID] = struct{}{}
	}
	keys := make([]uint64, 0, len(idSet))
	for k := range idSet {
		keys = append(keys, k)
	}
	AreaTabs, lcosErr := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabByParams(c, map[string]interface{}{"id": keys, "region": region})
	if lcosErr != nil {
		return lcosErr
	}

	mapArea := make(map[uint64]*area.LogisticLineServiceableAreaTab)
	for _, area := range AreaTabs {
		mapArea[area.ID] = area
	}

	for _, d := range data {
		if a, ok := mapArea[d.FromAreaID]; ok {
			d.FromAreaName = a.AreaName
		}
		if a, ok := mapArea[d.ToAreaID]; ok {
			d.ToAreaName = a.AreaName
		}
	}
	return nil
}

// Deprecated
func (s *LineServiceableRouteService) Create(ctx utils.LCOSContext, request *area_route_serviceable.CreateRouteRequest) (*model.LogisticLineServiceableRouteTab, *lcos_error.LCOSError) {
	exists, err := lls_service.CheckLineAndInstallationLineExist(ctx, request.LineID)
	if err != nil {
		return nil, err
	}
	if !exists {
		logger.LogErrorf("create route failed, line_id not exist|lineId=%s", request.LineID)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, "create failed, line_id not found")
	}

	routeTab := new(model.LogisticLineServiceableRouteTab)
	_ = copier.Copy(routeTab, request)

	fromArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(ctx, request.FromAreaID)
	if err != nil {
		return nil, err
	}
	if fromArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, "area id not found: "+strconv.Itoa(int(request.FromAreaID)))
	}
	routeTab.FromAreaName = fromArea.AreaName

	toArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(ctx, request.ToAreaID)
	if err != nil {
		return nil, err
	}
	if toArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, "area id not found: "+strconv.Itoa(int(request.FromAreaID)))
	}
	routeTab.ToAreaName = toArea.AreaName

	return s.lineServiceableRouteDAO.CreateLogisticLineServiceableRouteTab(ctx, routeTab)
}

// Deprecated
func (s *LineServiceableRouteService) Delete(c utils.LCOSContext, request *common_protocol.IDRequest) *lcos_error.LCOSError {
	return s.lineServiceableRouteDAO.DeleteLogisticLineServiceableRouteTabsById(c, request.ID)
}

// Deprecated
func (s *LineServiceableRouteService) Update(c utils.LCOSContext, request *area_route_serviceable.UpdateRouteRequest) (*model.LogisticLineServiceableRouteTab, *lcos_error.LCOSError) {
	routeTab := new(model.LogisticLineServiceableRouteTab)
	_ = copier.Copy(routeTab, request)

	fromArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(c, request.FromAreaID)
	if err != nil {
		return nil, err
	}
	if fromArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, fmt.Sprintf("area id not found: %d", request.FromAreaID))
	}
	routeTab.FromAreaName = fromArea.AreaName

	toArea, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabById(c, request.ToAreaID)
	if err != nil {
		return nil, err
	}
	if toArea == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, fmt.Sprintf("area id not found: %d", request.FromAreaID))
	}
	routeTab.ToAreaName = toArea.AreaName
	return s.lineServiceableRouteDAO.UpdateLogisticLineServiceableRouteTab(c, routeTab)
}

// Deprecated
func (s *LineServiceableRouteService) GetByID(c utils.LCOSContext, request *common_protocol.IDRequest) (*model.LogisticLineServiceableRouteTab, *lcos_error.LCOSError) {
	route, err := s.lineServiceableRouteDAO.GetLogisticLineServiceableRouteTabById(c, request.ID)
	if err != nil {
		return nil, err
	}
	if route == nil {
		return nil, lcos_error.NewLCOSErrorWithDefaultMsg(lcos_error.NotFoundRouteErrorCode)
	}
	return route, nil
}

// Deprecated
func (s *LineServiceableRouteService) List(c utils.LCOSContext, request *area_route_serviceable.SearchRouteRequest) (*common.PageModel, *lcos_error.LCOSError) {
	pageNo, count := utils.GenPageAndCount(request.PageNo, request.Count)
	paramMap, _ := utils.Struct2map(request)
	areaLocationRefList, total, lcosError := s.lineServiceableRouteDAO.SearchLogisticLineServiceableRouteTab(c, pageNo, count, paramMap)
	return &common.PageModel{
		PageNO: pageNo,
		Count:  count,
		Total:  total,
		List:   areaLocationRefList,
	}, lcosError
}

func (s *LineServiceableRouteService) parseSingleRoute(c utils.LCOSContext, rowData *serviceable_util.LineBasicRouteRowData, region string, areaRouteModifyMap map[string]*model.ModifyAreaRoute, addLocationModelsMap map[string][]*model.LineServiceableRouteTab, addBlacklistLocationModelsMap map[string][]*model.LineServiceableBlackListRouteTab, deleteBlacklistLocationModelsMap map[string][]*model.LineServiceableBlackListRouteTab) *lcos_error.LCOSError {
	routeTab := &model.LineServiceableRouteTab{
		LineID:                rowData.LineId,
		CollectDeliverGroupId: rowData.GroupId,
		Region:                region,
	}

	// 填充from area信息
	fromAreaTabs, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabByParams(c, map[string]interface{}{"area_name": rowData.FromArea, "region": region})
	if err != nil || len(fromAreaTabs) == 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "From Area not exists | row=%d", rowData.RowId)
	}
	routeTab.FromAreaID = fromAreaTabs[0].ID
	routeTab.FromAreaName = fromAreaTabs[0].AreaName

	// 填充to area信息
	if rowData.Category == serviceable_constant.ZoneCategory {
		routeTab.ToAreaID = routeTab.FromAreaID
		routeTab.ToAreaName = routeTab.FromAreaName
	} else {
		toAreaTabs, err := s.lineServiceableAreaDAO.GetLogisticLineServiceableAreaTabByParams(c, map[string]interface{}{"area_name": rowData.ToArea, "region": region})
		if err != nil || len(toAreaTabs) == 0 {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "To Area not exists | row=%d", rowData.RowId)
		}
		routeTab.ToAreaID = toAreaTabs[0].ID
		routeTab.ToAreaName = toAreaTabs[0].AreaName
	}

	if rowData.Type == serviceable_constant.RouteUnsupportedMode {
		routeBlacklistTab := routeTab.ConvertToBlacklistRouteTable()
		if rowData.ActionCode == serviceable_constant.DeleteActionCode {
			deleteBlacklistLocationModelsMap[rowData.LineId] = append(deleteBlacklistLocationModelsMap[rowData.LineId], routeBlacklistTab)
		} else {
			addBlacklistLocationModelsMap[rowData.LineId] = append(addBlacklistLocationModelsMap[rowData.LineId], routeBlacklistTab)
		}
	} else {
		if rowData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			addLocationModelsMap[rowData.LineId] = append(addLocationModelsMap[rowData.LineId], routeTab)
		}
		key := model.GenerateRouteCacheKey(routeTab.LineID, routeTab.CollectDeliverGroupId, routeTab.FromAreaID)
		if v, ok := areaRouteModifyMap[key]; ok {
			if rowData.ActionCode == serviceable_constant.DeleteActionCode {
				v.DeleteList[routeTab.ToAreaID] = struct{}{}
			} else {
				v.CreateList[routeTab.ToAreaID] = struct{}{}
			}
		} else {
			modifyRoute := &model.ModifyAreaRoute{
				LineServiceableAreaRouteTab: model.LineServiceableAreaRouteTab{
					LineID:                routeTab.LineID,
					Region:                routeTab.Region,
					CollectDeliverGroupId: routeTab.CollectDeliverGroupId,
					FromAreaID:            routeTab.FromAreaID,
				},
				CreateList: map[uint64]struct{}{},
				DeleteList: map[uint64]struct{}{},
			}
			if rowData.ActionCode == serviceable_constant.DeleteActionCode {
				modifyRoute.DeleteList[routeTab.ToAreaID] = struct{}{}
			} else {
				modifyRoute.CreateList[routeTab.ToAreaID] = struct{}{}
			}
			areaRouteModifyMap[key] = modifyRoute
		}
	}
	return nil
}

func (s *LineServiceableRouteService) Upload(ctx utils.LCOSContext, request *area_route_serviceable.UploadRouteRequest) *lcos_error.LCOSError {
	region := ctx.GetCountry()

	job, err := s.scheduledService.CreateScheduledJob(ctx, region, scheduled_constant.ThreePLServiceableArea, scheduled_constant.ZoneRoute, request.FileUrl, request.IsScheduled, request.ScheduledTime, ctx.GetUserEmail())
	if err != nil {
		return err
	}

	go func() {
		parseFileResult, _ := s.ParseAndImportBasicRouteSA(ctx, region, request, serviceable_util.LineBasicRouteHeader, true)
		if err := s.scheduledService.UpdateValidateResult(ctx, job, parseFileResult); err != nil {
			logger.CtxLogErrorf(ctx, "scheduled job update validate result error|job_id=%d, cause=%s", job.ID, err.Msg)
		}
	}()

	return nil
}

func (s *LineServiceableRouteService) ParseAndImportBasicRouteSA(c utils.LCOSContext, region string, request *area_route_serviceable.UploadRouteRequest, header []excel.ParseableField, async bool) (excel.ParseFileResult, *lcos_error.LCOSError) {
	result := excel.ParseFileResult{
		ColumnsCount: len(header),
	}

	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(c, request.FileUrl)
	if err != nil {
		result.ParseFileErr = err
		return result, nil
	}

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(c)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
		return result, nil
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
		return result, nil
	}
	defer os.Remove(filePath)

	uniqueKeyMap := make(map[string]int) // 用于重复数据校验：lineId:groupId:fromArea:toArea -> lineNum
	lineIDMap := make(map[string]bool)
	var lineIDList []string
	var rowDataList []*serviceable_util.LineBasicRouteRowData

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 跳过表头和空行
		if lineNum == 1 || excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}
		result.IncrRowsCount()

		rowData := &serviceable_util.LineBasicRouteRowData{}
		if err := excel.ParseRowDataWithHeader(lineNum, row, header, rowData); err != nil {
			result.WriteRowError(lineNum, err)
			continue
		}

		if rowData.Category == serviceable_constant.RouteCategory && rowData.FromArea == rowData.ToArea {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "From Area should be different with To Area for Route category | row=%d", rowData.RowId))
			continue
		}
		if rowData.Category == serviceable_constant.ZoneCategory && rowData.FromArea != rowData.ToArea {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "From Area should be the same as To Area for Zone category | row=%d", rowData.RowId))
			continue
		}

		if _, ok := lineIDMap[rowData.LineId]; !ok {
			lineIDList = append(lineIDList, rowData.LineId)
			lineIDMap[rowData.LineId] = true
		}

		// 基础重复校验，唯一key：LineId-GroupId-FromArea-ToArea
		uniqueKey := utils.GenKey("-", rowData.LineId, rowData.GroupId, rowData.FromArea, rowData.ToArea)
		if lastRowId, ok := uniqueKeyMap[uniqueKey]; ok {
			result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import data duplicated with same Line-Group-FromArea-ToArea, please check | row %d duplicate with row %d, unique key=%s", rowData.RowId, lastRowId, uniqueKey)
			return result, nil
		} else {
			uniqueKeyMap[uniqueKey] = rowData.RowId
		}

		rowDataList = append(rowDataList, rowData)
	}

	if len(lineIDList) == 0 {
		result.ParseFileErr = lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "File is empty")
		return result, nil
	}

	// 请求lls获取所有的line信息
	lineInfoMap, lcosErr := lls_service.BatchCheckLineExists(c, region, lineIDList, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if lcosErr != nil {
		result.ParseFileErr = lcosErr
		return result, nil
	}
	areaRouteModifyMap := make(map[string]*model.ModifyAreaRoute)
	addRouteModelsMap := make(map[string][]*model.LineServiceableRouteTab)
	addBlacklistRouteModelsMap := make(map[string][]*model.LineServiceableBlackListRouteTab)
	deleteBlacklistRouteModelsMap := make(map[string][]*model.LineServiceableBlackListRouteTab)
	for _, rowData := range rowDataList {
		if _, ok := lineInfoMap[rowData.LineId]; !ok {
			result.WriteRowError(rowData.RowId, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Line ID not exists | row=%d", rowData.RowId))
			continue
		}

		// 填充area id和area name信息，并对修改配置进行分组
		if err := s.parseSingleRoute(c, rowData, region, areaRouteModifyMap, addRouteModelsMap, addBlacklistRouteModelsMap, deleteBlacklistRouteModelsMap); err != nil {
			result.WriteRowError(rowData.RowId, err)
			continue
		}
	}

	// 基础校验：已在黑名单中的area不能新增白名单配置，已在白名单中的area不能新增黑名单配置
	support := constant.ROUTE_SUPPORTED
	unsupport := constant.ROUTE_UNSUPPORTED
	addRouteKeyMap := make(map[string]bool)
	for _, lineID := range lineIDList {
		if routeModels, ok := addRouteModelsMap[lineID]; ok {
			for _, data := range routeModels {
				addRouteKeyMap[fmt.Sprintf("%s:%s:%d:%d-us", data.LineID, data.CollectDeliverGroupId, data.FromAreaID, data.ToAreaID)] = true
			}
			tmpReq := &area_route_serviceable.SearchAllLineRouteRequest{LineID: lineID, RouteType: &unsupport}
			models, lcosErr := s.lineServiceableRouteDAO.SearchAllLineServiceableRouteTab(c, tmpReq)
			if lcosErr != nil {
				result.ParseFileErr = lcosErr
				return result, nil
			}
			for _, data := range models {
				uniqKey := fmt.Sprintf("%s:%s:%d:%d-us", data.LineID, data.CollectDeliverGroupId, data.FromAreaID, data.ToAreaID)
				if _, ok := addRouteKeyMap[uniqKey]; ok {
					result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import SUPPORTED data duplicated in UNSUPPORTED's database, line_id=%v, group_id=%v, from_area=%v, to_area=%v", data.LineID, data.CollectDeliverGroupId, data.FromAreaID, data.ToAreaID)
					return result, nil
				}
			}
		}
		if routeModels, ok := addBlacklistRouteModelsMap[lineID]; ok {
			for _, data := range routeModels {
				addRouteKeyMap[fmt.Sprintf("%s:%s:%d:%d-s", data.LineID, data.CollectDeliverGroupId, data.FromAreaID, data.ToAreaID)] = true
			}
			tmpReq := &area_route_serviceable.SearchAllLineRouteRequest{LineID: lineID, RouteType: &support}
			models, lcosErr := s.lineServiceableRouteDAO.SearchAllLineServiceableRouteTab(c, tmpReq)
			if lcosErr != nil {
				result.ParseFileErr = lcosErr
				return result, nil
			}
			for _, data := range models {
				uniqKey := fmt.Sprintf("%s:%s:%d:%d-s", data.LineID, data.CollectDeliverGroupId, data.FromAreaID, data.ToAreaID)
				if _, ok := addRouteKeyMap[uniqKey]; ok {
					result.ParseFileErr = lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import UNSUPPORTED data duplicated in SUPPORTED's database, line_id=%v, group_id=%v, from_area=%v, to_area=%v", data.LineID, data.CollectDeliverGroupId, data.FromAreaID, data.ToAreaID)
					return result, nil
				}
			}
		}
	}

	if async {
		return result, nil
	}
	fc := func() *lcos_error.LCOSError {
		for _, models := range areaRouteModifyMap {
			if lcosErr = s.lineServiceableRouteDAO.ModifyAreaRoute(c, models); lcosErr != nil {
				return lcosErr
			}
		}

		for _, models := range addBlacklistRouteModelsMap {
			if lcosErr = s.lineServiceableRouteDAO.BatchCreateLineServiceableBlacklistRouteTabOnDuplicateUpdate(c, models); lcosErr != nil {
				return lcosErr
			}
		}

		for _, models := range deleteBlacklistRouteModelsMap {
			if lcosErr = s.lineServiceableRouteDAO.BatchDeleteLineServiceableBlacklistRouteTabs(c, models); lcosErr != nil {
				return lcosErr
			}
		}

		return nil
	}
	lcosErr = c.Transaction(fc)
	if lcosErr != nil {
		return result, lcosErr
	} else {
		// 手动刷新缓存
		_, _ = localcache.IncrLocalCacheVersion(c, constant.LineRouteTabNamespace2)
		_, _ = localcache.IncrLocalCacheVersion(c, constant.LineBlacklistRouteTabNamespace)
		change_report.SetChangeReportExtraBusinessDetail(c, strings.Join(lineIDList, ","))
		return result, nil
	}
}

func (s *LineServiceableRouteService) GetRouteDataWithoutPaging(c utils.LCOSContext, request *area_route_serviceable.SearchAllLineRouteRequest) ([]*model.LineServiceableRouteTab, *lcos_error.LCOSError) {
	region := c.GetCountry()
	data, lcosErr := s.lineServiceableRouteDAO.SearchAllLineServiceableRouteTab(c, request)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if *request.RouteType == constant.ROUTE_SUPPORTED {
		lcosErr = s.fillRouteWithName(c, data, region)
		if lcosErr != nil {
			return nil, lcosErr
		}
	}

	return data, nil
}

func (s *LineServiceableRouteService) RouteMigration(ctx utils.LCOSContext) *lcos_error.LCOSError {
	batchSize := 10000
	lineRouteList := []*route.LineServiceableRouteTab{}
	lineRouteMap := make(map[string]*route.LineServiceableAreaRouteTab)
	selectCol := []string{"id", "region", "line_id", "collect_deliver_group_id", "from_area_id", "to_area_id"}
	handler := func(result interface{}) error {
		data, ok := result.(*[]*route.LineServiceableRouteTab)
		if !ok {
			return errors.New("unsupported type, should be '*[]*route.LineServiceableRouteTab'")
		}
		for _, lineRoute := range *data {
			key := utils.GenKey(":", lineRoute.LineID, lineRoute.CollectDeliverGroupId, strconv.FormatUint(lineRoute.FromAreaID, 10))
			if v, ok := lineRouteMap[key]; ok {
				v.ToAreaIDList = append(v.ToAreaIDList, lineRoute.ToAreaID)
			} else {
				lineRouteMap[key] = &route.LineServiceableAreaRouteTab{
					LineID:                lineRoute.LineID,
					Region:                lineRoute.Region,
					CollectDeliverGroupId: lineRoute.CollectDeliverGroupId,
					FromAreaID:            lineRoute.FromAreaID,
					ToAreaIDList:          []uint64{lineRoute.ToAreaID},
				}
			}
		}
		return nil
	}

	LCOSError := common.HandAllDataBatch(ctx, &route.LineServiceableRouteTab{}, &lineRouteList, batchSize, selectCol, handler)
	if LCOSError != nil {
		return LCOSError
	}

	data := make([]*route.LineServiceableAreaRouteTab, 0, len(lineRouteMap))
	for _, v := range lineRouteMap {
		data = append(data, v)
	}

	for i := 0; i < len(data); i += 100 {
		var batchData []*route.LineServiceableAreaRouteTab
		if i+100 > len(data)-1 {
			batchData = data[i:]
		} else {
			batchData = data[i : i+100]
		}
		s.lineServiceableRouteDAO.InsertOrUpdate(ctx, batchData)
		time.Sleep(1 * time.Second)
	}

	return nil
}
