package area_route_serviceable

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
)

type fullAreaResponse struct {
	*area.LogisticLineServiceableAreaTab
	Locations []*area_location_ref.LogisticLineServiceableAreaLocationRefTab `json:"locations" form:"locations"`
}
