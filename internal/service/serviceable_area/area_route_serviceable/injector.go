package area_route_serviceable

import "github.com/google/wire"

/*
* @Author: yajun.han
* @Date: 2020/8/5 12:12 上午
* @Name：area_route_serviceable
* @Description:
 */

var LineServiceableRouteServiceProviderSet = wire.NewSet(
	NewLineServiceableRouteService,
	wire.Bind(new(LineServiceableRouteServiceInterface), new(*LineServiceableRouteService)),
)

var LineServiceableAreaServiceProviderSet = wire.NewSet(
	NewLineServiceableAreaService,
	wire.Bind(new(LineServiceableAreaServiceInterface), new(*LineServiceableAreaService)),
)
