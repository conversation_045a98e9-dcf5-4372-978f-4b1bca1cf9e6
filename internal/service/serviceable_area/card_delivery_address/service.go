package card_delivery_address

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/card_delivery_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/csv"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"os"
	"strings"
)

type CardDeliveryAddressService interface {
	UploadCardDeliveryAddress(ctx utils.LCOSContext, region string, addLocationList, deleteLocationList []*model.LineBasicServiceableLocationTab, basicConfMap map[string]*basic_conf.LineBasicServiceableConfTab) (string, *lcos_error.LCOSError)
	CreateCardDeliveryAddressChangeVersion(ctx utils.LCOSContext, region string, remoteFilePath string) *lcos_error.LCOSError
	ListPendingCardDeliveryAddressChangeVersion(ctx utils.LCOSContext) ([]*card_delivery_address.CardDeliveryAddressChangeVersionTab, *lcos_error.LCOSError)
	ListProcessingTimeoutChangeVersion(ctx utils.LCOSContext) ([]*card_delivery_address.CardDeliveryAddressChangeVersionTab, *lcos_error.LCOSError)
	ProcessCardDeliveryAddressChangeVersion(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError
	ResetCardDeliveryAddressChangeVersion(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError
	DoneCardDeliveryAddressChangeVersion(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError
	GetCardDeliveryAddressChangeVersionById(ctx utils.LCOSContext, id uint64) (*card_delivery_address.CardDeliveryAddressChangeVersionTab, *lcos_error.LCOSError)
}

func NewCardDeliveryAddressService(dao card_delivery_address.CardDeliveryAddressChangeVersionDao) *cardDeliveryAddressService {
	return &cardDeliveryAddressService{
		dao: dao,
	}
}

type cardDeliveryAddressService struct {
	dao card_delivery_address.CardDeliveryAddressChangeVersionDao
}

func (c *cardDeliveryAddressService) isValidAddress(address *CardDeliveryAddressData) bool {
	// card delivery address必须是完整的四级地址
	return address.State != "" && address.City != "" && address.District != "" && address.Street != ""
}

func (c *cardDeliveryAddressService) getProcessingTimeout(ctx utils.LCOSContext) uint32 {
	timeout := config.GetMutableConf(ctx).CardDeliveryAddressSyncConfig.ProcessTimeout
	if timeout <= 0 {
		timeout = 600 // 十分钟
	}
	return timeout
}

func (c *cardDeliveryAddressService) generateCardDeliveryAddress(ctx utils.LCOSContext, region string, addLocationList, deleteLocationList []*model.LineBasicServiceableLocationTab, basicConfMap map[string]*basic_conf.LineBasicServiceableConfTab) []interface{} {
	cardDeliveryLine := config.GetCardDeliveryLineByRegion(ctx, region)
	if cardDeliveryLine == "" {
		return nil
	}

	var addressList []interface{}
	for _, location := range addLocationList {
		if location.LineId != cardDeliveryLine {
			continue
		}
		address := &CardDeliveryAddressData{
			Region:         region,
			State:          location.State,
			City:           location.City,
			District:       location.District,
			Street:         location.Street,
			SupportDeliver: location.GetCanDeliver(),
		}
		if !c.isValidAddress(address) {
			continue
		}
		addressList = append(addressList, address)
	}
	for _, location := range deleteLocationList {
		if location.LineId != cardDeliveryLine {
			continue
		}
		basicConf, ok := basicConfMap[location.LineId]
		if !ok {
			continue
		}
		address := &CardDeliveryAddressData{
			Region:         region,
			State:          location.State,
			City:           location.City,
			District:       location.District,
			Street:         location.Street,
			SupportDeliver: basicConf.GetCanDeliver(),
		}
		if !c.isValidAddress(address) {
			continue
		}
		addressList = append(addressList, address)
	}
	return addressList
}

func (c *cardDeliveryAddressService) UploadCardDeliveryAddress(ctx utils.LCOSContext, region string, addLocationList, deleteLocationList []*model.LineBasicServiceableLocationTab, basicConfMap map[string]*basic_conf.LineBasicServiceableConfTab) (string, *lcos_error.LCOSError) {
	if config.GetMutableConf(ctx).CardDeliveryAddressSyncConfig.Disable {
		return "", nil
	}

	region = strings.ToUpper(region)
	dataList := c.generateCardDeliveryAddress(ctx, region, addLocationList, deleteLocationList, basicConfMap)
	if len(dataList) == 0 {
		return "", nil
	}

	// 将需要推送给seabank的增量服务范围地址数据写入到csv文件
	filePath := fmt.Sprintf("/tmp/%s-%d.csv", region, recorder.Now(ctx).UnixNano())
	file, err := os.Create(filePath)
	if err != nil {
		return "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "create csv file error: %s", err.Error())
	}
	defer file.Close()
	if err = csv.WriteTitleAndStruct(file, titles, dataList); err != nil {
		return "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "write csv file error: %s", err.Error())
	}

	// 将写入的csv本地文件上传到s3集群
	cfg := config.GetConf(ctx).LCOSS3Config
	return s3_service.NewS3Service().UploadFile(ctx, cfg.AccessKeyID, cfg.BucketKey, filePath, cfg.TimeOut, "card-delivery-address")
}

func (c *cardDeliveryAddressService) CreateCardDeliveryAddressChangeVersion(ctx utils.LCOSContext, region string, remoteFilePath string) *lcos_error.LCOSError {
	if remoteFilePath == "" {
		return nil
	}

	// 保存card delivery address变更版本
	return c.dao.CreateCardDeliveryAddressChangeVersion(ctx, &card_delivery_address.CardDeliveryAddressChangeVersionTab{
		Region:         region,
		RemoteFilePath: remoteFilePath,
		VersionStatus:  card_delivery_constant.VersionStatusPending,
	})
}

func (c *cardDeliveryAddressService) ListPendingCardDeliveryAddressChangeVersion(ctx utils.LCOSContext) ([]*card_delivery_address.CardDeliveryAddressChangeVersionTab, *lcos_error.LCOSError) {
	return c.dao.ListCardDeliveryAddressChangeVersion(ctx, map[string]interface{}{
		"version_status !=": card_delivery_constant.VersionStatusDone,
	})
}

func (c *cardDeliveryAddressService) ListProcessingTimeoutChangeVersion(ctx utils.LCOSContext) ([]*card_delivery_address.CardDeliveryAddressChangeVersionTab, *lcos_error.LCOSError) {
	return c.dao.ListCardDeliveryAddressChangeVersion(ctx, map[string]interface{}{
		"version_status":       card_delivery_constant.VersionStatusProcessing,
		"process_start_time <": utils.GetTimestamp(ctx) - c.getProcessingTimeout(ctx),
	})
}

func (c *cardDeliveryAddressService) ProcessCardDeliveryAddressChangeVersion(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	queryMap := map[string]interface{}{
		"id":             id,
		"version_status": card_delivery_constant.VersionStatusPending,
	}
	updateMap := map[string]interface{}{
		"version_status":     card_delivery_constant.VersionStatusProcessing,
		"process_start_time": utils.GetTimestamp(ctx),
		"process_end_time":   0,
	}
	return c.dao.UpdateCardDeliveryAddressChangeVersionByParamsAndCheckAffected(ctx, queryMap, updateMap)
}

func (c *cardDeliveryAddressService) ResetCardDeliveryAddressChangeVersion(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	queryMap := map[string]interface{}{
		"id":             id,
		"version_status": card_delivery_constant.VersionStatusProcessing,
	}
	updateMap := map[string]interface{}{
		"version_status":     card_delivery_constant.VersionStatusPending,
		"process_start_time": 0,
		"process_end_time":   0,
	}
	return c.dao.UpdateCardDeliveryAddressChangeVersionByParamsAndCheckAffected(ctx, queryMap, updateMap)
}

func (c *cardDeliveryAddressService) DoneCardDeliveryAddressChangeVersion(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	queryMap := map[string]interface{}{
		"id":             id,
		"version_status": card_delivery_constant.VersionStatusProcessing,
	}
	updateMap := map[string]interface{}{
		"version_status":   card_delivery_constant.VersionStatusDone,
		"process_end_time": utils.GetTimestamp(ctx),
	}
	return c.dao.UpdateCardDeliveryAddressChangeVersionByParamsAndCheckAffected(ctx, queryMap, updateMap)
}

func (c *cardDeliveryAddressService) GetCardDeliveryAddressChangeVersionById(ctx utils.LCOSContext, id uint64) (*card_delivery_address.CardDeliveryAddressChangeVersionTab, *lcos_error.LCOSError) {
	return c.dao.GetCardDeliveryAddressChangeVersionById(ctx, id)
}
