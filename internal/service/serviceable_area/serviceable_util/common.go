package serviceable_util

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/ceprange_util"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"strconv"
)

type LineServiceable interface {
	GetLineId() string
	GetGroupId() string
}

// GetLineServiceableGroupKey 生成服务范围的GroupKey，line_id:collect_deliver_group_id
func GetLineServiceableGroupKey(serviceable LineServiceable) string {
	return utils.GenKey(":", serviceable.GetLineId(), serviceable.GetGroupId())
}

type LineCepRangeServiceable interface {
	LineServiceable
	ceprange_util.CepRange
}

// GetLineCepRangeServiceableUniqueKey 生成线服务范围（CEP Range）的唯一Key，line_id:collect_deliver_group_id:cep_start:cep_end
func GetLineCepRangeServiceableUniqueKey(serviceable LineCepRangeServiceable) string {
	return utils.GenKey(":",
		serviceable.GetLineId(),
		serviceable.GetGroupId(),
		strconv.Itoa(int(serviceable.GetCepStart())),
		strconv.Itoa(int(serviceable.GetCepEnd())),
	)
}

type ServiceableAreaAbility interface {
	GetCanPickup() uint8
	GetCanCodPickup() uint8
	GetCanDeliver() uint8
	GetCanCodDeliver() uint8
}

func ServiceableAreaAbilityEqual(a, b ServiceableAreaAbility) bool {
	return a.GetCanPickup() == b.GetCanPickup() &&
		a.GetCanDeliver() == b.GetCanDeliver() &&
		a.GetCanCodPickup() == b.GetCanCodPickup() &&
		b.GetCanDeliver() == a.GetCanDeliver()
}
