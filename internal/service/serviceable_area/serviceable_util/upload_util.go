package serviceable_util

import (
	"context"
	"crypto/md5"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seatalk"
	"net/url"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"
)

type CommonPostcodeRowData struct {
	LineId                string
	CollectDeliverGroupId string
	Region                string
	Postcode              string
	Pickup                uint8
	CodPickup             uint8
	Deliver               uint8
	CodDeliver            uint8
	ActionCode            int
}

type CommonLocationRowData struct {
	LineId                string
	CollectDeliverGroupId string
	Region                string
	State                 string
	City                  string
	District              string
	Street                string
	PickUp                uint8
	CodPickUp             uint8
	Deliver               uint8
	CodDeliver            uint8
	TradeIn               uint8
	ActionCode            int
}

var httpClient *chassis.RestInvoker

func init() {
	httpClient = chassis.NewRestInvoker()
}

// http请求，需要传递ctx进来
func DownloadFileFromS3(ctx utils.LCOSContext, remoteFilePath string) (string, *lcos_error.LCOSError) {
	if s3Url, err := url.Parse(remoteFilePath); err == nil {
		// 上报S3域名
		_ = monitor.AwesomeReportEvent(ctx, constant.CatModuleS3Host, s3Url.Scheme+"://"+s3Url.Host, constant.StatusSuccess, remoteFilePath)
	}
	if !config.CheckS3HostWhitelist(ctx, remoteFilePath) {
		return "", lcos_error.NewLCOSErrorf(lcos_error.InvalidS3HostErrorCode, "s3 url invalid: %s", remoteFilePath)
	}

	localFilePath := "/tmp/" + genTmpFileName(ctx, remoteFilePath)
	output, err := os.OpenFile(localFilePath, os.O_CREATE|os.O_RDWR, 0644)
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	req, err := chassis.NewRestRequest(http.MethodGet, remoteFilePath, nil)
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	var resp *http.Response
	for i := 0; i < int(config.GetRequestMaxRetryTimes(ctx)); i++ {
		//unsafe context
		if resp, err = httpClient.Invoke(ctx, req, chassis.WithoutServiceDiscovery()); err == nil { // nolint
			break
		}
	}
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	defer resp.Body.Close()

	if _, err := io.Copy(output, resp.Body); err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return localFilePath, nil
}

func genTmpFileName(ctx context.Context, path string) string {
	data := []byte(path)
	has := md5.Sum(data)
	md5str := fmt.Sprintf("%x%d", has, recorder.Now(ctx).UnixNano())
	return md5str
}

func CheckPostcodeRowData(rowData []string, region string, lineNum int) *lcos_error.LCOSError {
	if len(rowData) < 9 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", rowData length not enough: "+strconv.Itoa(len(rowData)))
	}

	rowCountry := strings.TrimSpace(rowData[2])
	if rowCountry != region {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", region mismatch: "+rowCountry)
	}

	pickup, codPick, deliver, codDeliver, e := checkSupport(rowData[4], rowData[5], rowData[6], rowData[7], lineNum)
	if e != nil {
		return nil
	}
	rowData[4] = pickup
	rowData[5] = codPick
	rowData[6] = deliver
	rowData[7] = codDeliver

	if _, err := strconv.Atoi(rowData[8]); err != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
	}

	return nil
}

// basic level zone/route
func CheckRouteRow(rowData []string, lineNum int, region string) *lcos_error.LCOSError {
	if len(rowData) < 7 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", rowData length not enough: "+strconv.Itoa(len(rowData)))
	}
	routeType := strings.TrimSpace(rowData[0])
	lineID := strings.TrimSpace(rowData[1])
	groupID := strings.TrimSpace(rowData[2])
	routeMode := strings.TrimSpace(rowData[3])
	fromArea := strings.TrimSpace(rowData[4])
	toArea := strings.TrimSpace(rowData[5])
	actionCode := strings.TrimSpace(rowData[6])
	if !(strings.ToLower(routeType) == "route" || strings.ToLower(routeType) == "zone") {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("Type is not Route or Zone|lineNum=%v", lineNum))
	}
	rowData[0] = strings.ToLower(routeType)
	if len(lineID) < 4 || !strings.EqualFold(utils.GetRegionFromResourceId(lineID), region) {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("line id is not valid|line_id=%v, lineNum=%v, region=%v", lineID, lineNum, region))
	}
	rowData[1] = lineID
	if len(groupID) < 4 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("group is not valid|group_id=%v, lineNum=%v", groupID, lineNum))
	}
	rowData[2] = groupID[0:4]
	if fromArea == "" {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("from area is not valid|from_area=%v, lineNum=%v", fromArea, lineNum))
	}

	if !(strings.ToLower(routeMode) == "supported" || strings.ToLower(routeMode) == "unsupported") {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("Type must be 'supported' or 'unsupported'|input_type=%v, lineNum=%v", routeMode, lineNum))
	}

	rowData[4] = fromArea
	if strings.ToLower(routeType) == "zone" {
		if toArea != "" && toArea != fromArea {
			return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("To Area should left empty or same as From Area for Zone Category|to_area=%v, from_area=%v, lineNum=%v", toArea, fromArea, lineNum))
		}
		rowData[5] = fromArea
	} else if toArea == "" {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("to area is required for route|to_area=%v, lineNum=%v", toArea, lineNum))
	} else {
		if fromArea == toArea {
			return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("From Area and To Area cannot be the same when type is route|lineNum=%v", lineNum))
		}
		rowData[5] = toArea
	}
	if !(actionCode == "1" || actionCode == "-1") {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("Please fill in Actioncode (1 or -1)|action_code=%v, lineNum=%v", actionCode, lineNum))
	}
	rowData[6] = actionCode
	return nil
}

// operation level zone/route
func CheckOpsRouteRow(rowData []string, lineNum int, region string) *lcos_error.LCOSError {
	if len(rowData) < 7 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", rowData length not enough: "+strconv.Itoa(len(rowData)))
	}
	routeType := strings.TrimSpace(rowData[0])
	lineID := strings.TrimSpace(rowData[1])
	groupID := strings.TrimSpace(rowData[2])
	rowRegion := strings.TrimSpace(rowData[3])
	banFromArea := strings.TrimSpace(rowData[4])
	banToArea := strings.TrimSpace(rowData[5])
	actionCode := strings.TrimSpace(rowData[6])
	if !(strings.ToLower(routeType) == "route" || strings.ToLower(routeType) == "zone") {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("Type is not Route or Zone|lineNum=%v", lineID))
	}
	rowData[0] = strings.ToLower(routeType)
	if len(lineID) < 4 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("line id is not valid|line_id=%v, lineNum=%v, region=%v", lineID, lineNum, region))
	}
	if !strings.EqualFold(rowRegion, region) {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("region is not valid|row_region=%v, line_num=%v, region=%v", rowRegion, lineNum, region))
	}
	rowData[1] = lineID
	rowData[3] = rowRegion
	if len(groupID) < 4 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("group is not valid|group_id=%v, lineNum=%v", groupID, lineNum))
	}
	rowData[2] = groupID[0:4]
	if banFromArea == "" {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("from area is not valid|from_area=%v, lineNum=%v", banFromArea, lineNum))
	}
	rowData[4] = banFromArea
	if strings.ToLower(routeType) == "zone" {
		if banToArea != "" && banFromArea != banToArea {
			return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("To Area should left empty or same as From Area for Zone Category|to_area=%v, from_area=%v, lineNum=%v", banToArea, banFromArea, lineNum))
		}
		rowData[5] = banFromArea
	} else if banToArea == "" {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("To Area should not be empty for Route Category|to_area=%v, lineNum=%v", banToArea, lineNum))
	} else {
		if banFromArea == banToArea {
			return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("From Area should be different from To Area for Route Category|lineNum=%v, from_area=%v, to_area=%v", lineNum, banFromArea, banToArea))
		}
		rowData[5] = banToArea
	}
	if !(actionCode == "1" || actionCode == "-1") {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("Please fill in Actioncode (1 or -1)|action_code=%v, lineNum=%v", actionCode, lineNum))
	}
	rowData[6] = actionCode
	return nil
}

func CheckLocationRowData(rowData []string, region string, lineNum int) *lcos_error.LCOSError {
	if len(rowData) < 12 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", rowData length not enough: "+strconv.Itoa(len(rowData)))
	}

	rowCountry := strings.TrimSpace(rowData[2])
	if rowCountry != region {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", region mismatch: "+rowCountry)
	}

	pickup, codPickup, deliver, codDeliver, e := checkSupport(rowData[7], rowData[8], rowData[9], rowData[10], lineNum)
	if e != nil {
		return nil
	}
	rowData[7] = pickup
	rowData[8] = codPickup
	rowData[9] = deliver
	rowData[10] = codDeliver

	if _, err := strconv.Atoi(rowData[11]); err != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
	}

	return nil
}

func CheckAreaRefData(rowData []string, lineNum int) *lcos_error.LCOSError {
	if len(rowData) < 5 {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", rowData length not enough: "+strconv.Itoa(len(rowData)))
	}

	if _, err := strconv.Atoi(rowData[4]); err != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
	}

	return nil
}

func checkSupport(pickupFlag string, codPickupFlag string, deliverFlag string, codDeliverFlag string, lineNum int) (string, string, string, string, *lcos_error.LCOSError) {
	pickupRes := ""
	codPickupRes := ""
	deliverRes := ""
	codDeliverRes := ""
	if strings.ToLower(pickupFlag) == "yes" {
		pickupRes = "1"
	} else if strings.ToLower(pickupFlag) == "no" {
		pickupRes = "0"
	} else {
		return "", "", "", "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", pickup mismatch: "+pickupFlag)
	}
	if strings.ToLower(codPickupFlag) == "yes" {
		codPickupRes = "1"
	} else if strings.ToLower(codPickupFlag) == "no" {
		codPickupRes = "0"
	} else {
		return "", "", "", "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", codPickup mismatch: "+codPickupFlag)
	}

	if strings.ToLower(deliverFlag) == "yes" {
		deliverRes = "1"
	} else if strings.ToLower(deliverFlag) == "no" {
		deliverRes = "0"
	} else {
		return "", "", "", "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", deliver mismatch: "+deliverFlag)
	}
	if strings.ToLower(codDeliverFlag) == "yes" {
		codDeliverRes = "1"
	} else if strings.ToLower(codDeliverFlag) == "no" {
		codDeliverRes = "0"
	} else {
		return "", "", "", "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", codDeliver mismatch: "+codDeliverFlag)
	}
	return pickupRes, codPickupRes, deliverRes, codDeliverRes, nil
}

// Deprecated
func checkAndChangeFileServiceType(serviceType string, isOrigin bool, lineNum int) (string, *lcos_error.LCOSError) {
	if isOrigin {
		if strings.ToLower(serviceType) != "pickup" && strings.ToLower(serviceType) != "b2c" {
			return "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", collectServiceType mismatch: "+serviceType)
		}
		if strings.ToLower(serviceType) == "pickup" {
			return strconv.Itoa(int(constant.PICKUP)), nil
		} else {
			return strconv.Itoa(int(constant.B2C)), nil
		}
	} else {
		if strings.ToLower(serviceType) != "to home" && strings.ToLower(serviceType) != "to site" {
			return "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "lineNum: "+strconv.Itoa(lineNum)+", deliverServiceType mismatch: "+serviceType)
		}
		if strings.ToLower(serviceType) == "to home" {
			return strconv.Itoa(int(constant.TOHOME)), nil
		} else {
			return strconv.Itoa(int(constant.TOSITE)), nil
		}
	}
}

func ParseLocationRowData(rowData []string) *CommonLocationRowData {
	pickup, _ := strconv.Atoi(rowData[7])
	codPickup, _ := strconv.Atoi(rowData[8])
	deliver, _ := strconv.Atoi(rowData[9])
	codDeliver, _ := strconv.Atoi(rowData[10])
	actionCode, _ := strconv.Atoi(rowData[11])
	collectDeliverGroupId := rowData[1]
	if len(collectDeliverGroupId) >= 4 {
		collectDeliverGroupId = collectDeliverGroupId[0:4]
	}
	return &CommonLocationRowData{
		LineId:                rowData[0],
		CollectDeliverGroupId: collectDeliverGroupId,
		Region:                rowData[2],
		State:                 rowData[3],
		City:                  rowData[4],
		District:              rowData[5],
		Street:                rowData[6],
		PickUp:                uint8(pickup),
		CodPickUp:             uint8(codPickup),
		Deliver:               uint8(deliver),
		CodDeliver:            uint8(codDeliver),
		ActionCode:            actionCode,
	}
}

func ParsePostcodeRowData(rowData []string) *CommonPostcodeRowData {
	Pickup, _ := strconv.Atoi(rowData[4])
	CodPickup, _ := strconv.Atoi(rowData[5])
	Deliver, _ := strconv.Atoi(rowData[6])
	CodDeliver, _ := strconv.Atoi(rowData[7])
	actionCode, _ := strconv.Atoi(rowData[8])
	collectDeliverGroupId := rowData[1]
	if len(collectDeliverGroupId) >= 4 {
		collectDeliverGroupId = collectDeliverGroupId[0:4]
	}
	return &CommonPostcodeRowData{
		LineId:                rowData[0],
		CollectDeliverGroupId: collectDeliverGroupId,
		Region:                rowData[2],
		Postcode:              strings.TrimSpace(rowData[3]),
		Pickup:                uint8(Pickup),
		CodPickup:             uint8(CodPickup),
		Deliver:               uint8(Deliver),
		CodDeliver:            uint8(CodDeliver),
		ActionCode:            actionCode,
	}
}

func IsBlankRow(rowData []string) bool {
	for i := 0; i < len(rowData); i++ {
		if len(strings.TrimSpace(rowData[i])) > 0 {
			return false
		}
	}
	return true
}

func GetLocationRequestByLocationFileRow(rowData []string) *ops_service.GetLocationInfoByNameRequest {
	var locationName []string
	if len(rowData[3]) != 0 {
		locationName = append(locationName, strings.TrimSpace(rowData[3]))
	}
	if len(rowData[4]) != 0 {
		locationName = append(locationName, strings.TrimSpace(rowData[4]))
	}
	if len(rowData[5]) != 0 {
		locationName = append(locationName, strings.TrimSpace(rowData[5]))
	}
	if len(rowData[6]) != 0 {
		locationName = append(locationName, strings.TrimSpace(rowData[6]))
	}
	return &ops_service.GetLocationInfoByNameRequest{
		Country:      rowData[2],
		LocationName: locationName,
	}
}

func GetLocationRequestByAreaFileRow(rowData []string, region string) *ops_service.GetLocationInfoByNameRequest {
	var locationName []string
	if len(rowData[1]) != 0 {
		locationName = append(locationName, strings.TrimSpace(rowData[1]))
	}
	if len(rowData[2]) != 0 {
		locationName = append(locationName, strings.TrimSpace(rowData[2]))
	}
	if len(rowData[3]) != 0 {
		locationName = append(locationName, strings.TrimSpace(rowData[3]))
	}
	return &ops_service.GetLocationInfoByNameRequest{
		Country:      region,
		LocationName: locationName,
	}
}

// 上传文件字段校验函数集
// 1. 通用服务范围文件校验
func ValidateGroupId(region, value string) (string, *lcos_error.LCOSError) {
	if len(value) > 4 {
		value = value[:4]
	}
	if _, ok := constant.GroupIDToGroupIDDetailMap[value]; !ok {
		return "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Invalid group id, please re-confirm.")
	}
	return value, nil
}

func ValidateRegion(region, value string) (string, *lcos_error.LCOSError) {
	value = strings.ToUpper(value)
	if region != value {
		return "", lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Region mismatch request region[%s]", region)
	}
	return value, nil
}

func ValidateSupport(region, value string) (string, *lcos_error.LCOSError) {
	if utils.InStringSlice(strings.ToUpper(value), []string{"YES", "1"}) {
		return "1", nil
	}
	if utils.InStringSlice(strings.ToUpper(value), []string{"NO", "0"}) {
		return "0", nil
	}
	return "", lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "'%s' is invalid", value)
}

func ValidateActionCode(region, value string) (string, *lcos_error.LCOSError) {
	if _, err := strconv.Atoi(value); err != nil {
		return "", lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Invalid action code %s", value)
	}
	return value, nil
}

// 2. CepRange服务范围文件校验
func ValidateCEP(region, value string) (string, *lcos_error.LCOSError) {
	cep, err := strconv.Atoi(value)
	if err != nil {
		return "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
	}
	if cep <= 0 {
		return "", lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP should be larger than 0")
	}
	return value, nil
}

// 3. Zone/Route服务范围文件校验
func ValidateCategory(region, value string) (string, *lcos_error.LCOSError) {
	value = strings.ToLower(value)
	if !utils.InStringSlice(value, []string{"route", "zone"}) {
		return "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Invalid category, please re-confirm.")
	}
	return value, nil
}

func ValidateType(region, value string) (string, *lcos_error.LCOSError) {
	value = strings.ToLower(value)
	if !utils.InStringSlice(value, []string{"supported", "unsupported"}) {
		return "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Invalid type, please re-confirm.")
	}
	return value, nil
}

func AlertLineCepRangeSAAutoMergeOrSplit(ctx utils.LCOSContext, lineList []string, changeType, filePath string) *lcos_error.LCOSError {
	s3Conf := config.GetConf(ctx).SlsOpsS3Config
	fileUrl, _, _, err := s3_service.NewS3Service().UploadFileToS3(ctx, s3Conf.AccessKeyID, s3Conf.BucketKey, filePath, s3Conf.TimeOut, s3Conf.ExpirationDays, "serviceable_area_change_file")
	if err != nil {
		return err
	}

	message := fmt.Sprintf(serviceable_constant.LineCepRangeSAAutoMergeOrSplitAlertMessage,
		utils.GetEnv(ctx),
		strings.Join(lineList, ","),
		changeType,
		utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
		fileUrl,
	)
	notifyConf := config.GetScheduledJobConfig(ctx)
	if err = seatalk.NotifyWithTextMessage(ctx, notifyConf.SeatalkNotifyWebhook, message, []string{notifyConf.SeatalkNotifyEmail}, false); err != nil {
		return err
	}
	return nil
}
