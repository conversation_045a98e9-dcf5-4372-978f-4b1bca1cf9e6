package serviceable_util

import (
	"testing"
)

func TestChangeToOldDeliverMode(t *testing.T) {
	type args struct {
		CDMode uint32
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{
			name: "case1",
			args: args{39},
			want: 55,
		},
		{
			name: "case1",
			args: args{27},
			want: 11,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ChangeToOldDeliverMode(tt.args.CDMode); got != tt.want {
				t.Errorf("ChangeToOldDeliverMode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetStatus(t *testing.T) {
	type args struct {
		lineCollectDeliver uint32
		pickupMode         uint32
		deliverMode        uint32
	}
	tests := []struct {
		name string
		args args
		want int32
	}{
		{
			name: "case1",
			args: args{39, 3, 16},
			want: 1,
		},
		{
			name: "case2",
			args: args{39, 3, 8},
			want: 0,
		},
		{
			name: "case3",
			args: args{27, 3, 16},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetEffectiveStatus(tt.args.lineCollectDeliver, tt.args.pickupMode, tt.args.deliverMode); got != tt.want {
				t.Errorf("GetStatus() = %v, want %v", got, tt.want)
			}
		})
	}
}
