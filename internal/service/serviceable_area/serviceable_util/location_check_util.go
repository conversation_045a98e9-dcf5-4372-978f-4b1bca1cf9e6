package serviceable_util

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

// Deprecate 通过场景配置获取location_id
func GetLocationIdByLevel(scenarioConf *scenario_conf.LineCommonServiceableScenarioConfTab, locationInfo *pb.LocationInfo, scenario uint32) *uint32 {
	// build场景根据传入的location信息获取最小的location，并不是通过配置获取
	if scenario == uint32(constant.BUILD) {
		if locationInfo.StreetLocationId != nil {
			return locationInfo.StreetLocationId
		} else if locationInfo.DistrictLocationId != nil {
			return locationInfo.DistrictLocationId
		} else if locationInfo.CityLocationId != nil {
			return locationInfo.CityLocationId
		} else {
			return locationInfo.StateLocationId
		}
	} else {
		if locationInfo == nil {
			return nil
		}
		if scenarioConf.CheckLevel == constant.STATE {
			return locationInfo.StateLocationId
		} else if scenarioConf.CheckLevel == constant.CITY {
			return locationInfo.CityLocationId
		} else if scenarioConf.CheckLevel == constant.DISTRICT {
			return locationInfo.DistrictLocationId
		} else {
			return locationInfo.StreetLocationId
		}
	}
}

// 通过输入的check level级别获取location_id
func GetLocationIdByCheckLevel(checkLevel uint8, locationInfo *pb.LocationInfo) *uint32 {
	if locationInfo == nil {
		return nil
	}
	if checkLevel == constant.STATE {
		return locationInfo.StateLocationId
	} else if checkLevel == constant.CITY {
		return locationInfo.CityLocationId
	} else if checkLevel == constant.DISTRICT {
		return locationInfo.DistrictLocationId
	} else if checkLevel == constant.STREET {
		return locationInfo.StreetLocationId
	}
	return nil
}

func GetCheckAddressType(pickupInfo *pb.LocationInfo, deliverInfo *pb.LocationInfo, collectType uint32, scenario uint32, scenarioConf *scenario_conf.LineCommonServiceableScenarioConfTab) (bool, bool) {
	isCheckBuyer := false
	isCheckSeller := false
	// build场景，根据入参是否传入来判断是否要校验卖家/买家地址
	if scenario == uint32(constant.BUILD) {
		if pickupInfo != nil && collectType != uint32(constant.DROPOFF) {
			isCheckSeller = true
		}
		if deliverInfo != nil {
			isCheckBuyer = true
		}
	} else {
		if scenarioConf.CheckAddress&constant.SELLER != constant.DISABLED && collectType != uint32(constant.DROPOFF) {
			isCheckSeller = true
		}
		if scenarioConf.CheckAddress&constant.BUYER != constant.DISABLED {
			isCheckBuyer = true
		}
	}
	return isCheckSeller, isCheckBuyer
}

func GetPostCode(locationInfo *pb.LocationInfo) *string {
	if locationInfo == nil {
		return nil
	}
	return locationInfo.Postcode
}
