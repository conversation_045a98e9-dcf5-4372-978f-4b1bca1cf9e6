package serviceable_util

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_rule"
	"strconv"
	"strings"
)

type LineBasicLocationRowData struct {
	RowId             int
	LineId            string `json:"Line ID"`
	GroupId           string `json:"Group ID"`
	Region            string `json:"Region"`
	State             string `json:"State"`
	City              string `json:"City"`
	District          string `json:"District"`
	Street            string `json:"Street"`
	SupportPickup     uint8  `json:"Support Pickup"`
	SupportCodPickup  uint8  `json:"Support COD Pickup"`
	SupportDeliver    uint8  `json:"Support Deliver"`
	SupportCodDeliver uint8  `json:"Support COD Deliver"`
	SupportTradeIn    uint8  `json:"Support Trade In Deliver"`
	ActionCode        int8   `json:"Action Code"`
}

func (l *LineBasicLocationRowData) SetRowId(rowId int) {
	l.RowId = rowId
}

type LineOperationLocationRowData struct {
	RowId         int
	LineId        string `json:"Line ID"`
	GroupId       string `json:"Group ID"`
	Region        string `json:"Region"`
	State         string `json:"State"`
	City          string `json:"City"`
	District      string `json:"District"`
	Street        string `json:"Street"`
	BanPickup     uint8  `json:"Ban Pickup"`
	BanCodPickup  uint8  `json:"Ban COD Pickup"`
	BanDeliver    uint8  `json:"Ban Deliver"`
	BanCodDeliver uint8  `json:"Ban COD Deliver"`
	BanTradeIn    uint8  `json:"Ban Trade In Deliver"`
	ActionCode    int8   `json:"Action Code"`
}

func (l *LineOperationLocationRowData) SetRowId(rowId int) {
	l.RowId = rowId
}

type LineBasicPostcodeRowData struct {
	RowId             int
	LineId            string `json:"Line ID"`
	GroupId           string `json:"Group ID"`
	Region            string `json:"Region"`
	Postcode          string `json:"Postcode"`
	SupportPickup     uint8  `json:"Support Pickup"`
	SupportCodPickup  uint8  `json:"Support COD Pickup"`
	SupportDeliver    uint8  `json:"Support Deliver"`
	SupportCodDeliver uint8  `json:"Support COD Deliver"`
	SupportTradeIn    uint8  `json:"Support Trade In Deliver"`
	ActionCode        int8   `json:"Action Code"`
}

func (l *LineBasicPostcodeRowData) SetRowId(rowId int) {
	l.RowId = rowId
}

type LineOperationPostcodeRowData struct {
	RowId         int
	LineId        string `json:"Line ID"`
	GroupId       string `json:"Group ID"`
	Region        string `json:"Region"`
	Postcode      string `json:"Postcode"`
	BanPickup     uint8  `json:"Ban Pickup"`
	BanCodPickup  uint8  `json:"Ban COD Pickup"`
	BanDeliver    uint8  `json:"Ban Deliver"`
	BanCodDeliver uint8  `json:"Ban COD Deliver"`
	BanTradeIn    uint8  `json:"Ban Trade In Deliver"`
	ActionCode    int8   `json:"Action Code"`
}

func (l *LineOperationPostcodeRowData) SetRowId(rowId int) {
	l.RowId = rowId
}

type LineBasicCepRangeRowData struct {
	RowId             int
	LineId            string `json:"Line ID" excel:"title:Line ID"`
	GroupId           string `json:"Group ID" excel:"title:Group ID"`
	Region            string `json:"Region" excel:"title:Region"`
	CepInitial        int    `json:"CEP Initial" excel:"title:CEP Initial"`
	CepFinal          int    `json:"CEP Final" excel:"title:CEP Final"`
	SupportPickup     uint8  `json:"Support Pickup" excel:"title:Support Pickup"`
	SupportCodPickup  uint8  `json:"Support COD Pickup" excel:"title:Support COD Pickup"`
	SupportDeliver    uint8  `json:"Support Deliver" excel:"title:Support Deliver"`
	SupportCodDeliver uint8  `json:"Support COD Deliver" excel:"title:Support COD Deliver"`
	SupportTradeIn    uint8  `json:"Support Trade In Deliver" excel:"title:Support Trade In Deliver"`
	ActionCode        int8   `json:"Action Code" excel:"title:Action Code"`
}

func (l *LineBasicCepRangeRowData) SetRowId(rowId int) {
	l.RowId = rowId
}

type LineOperationCepRangeRowData struct {
	RowId          int
	LineId         string `json:"Line ID" excel:"title:Line ID"`
	GroupId        string `json:"Group ID" excel:"title:Group ID"`
	Region         string `json:"Region" excel:"title:Region"`
	CepInitial     int    `json:"CEP Initial" excel:"title:CEP Initial"`
	CepFinal       int    `json:"CEP Final" excel:"title:CEP Final"`
	BanPickup      uint8  `json:"Ban Pickup" excel:"title:Ban Pickup"`
	BanCodPickup   uint8  `json:"Ban COD Pickup" excel:"title:Ban COD Pickup"`
	BanDeliver     uint8  `json:"Ban Deliver" excel:"title:Ban Deliver"`
	BanCodDeliver  uint8  `json:"Ban COD Deliver" excel:"title:Ban COD Deliver"`
	DisAbleTradeIn uint8  `json:"Ban Trade In Deliver" excel:"title:Ban Trade In Deliver"`
	ActionCode     int8   `json:"Action Code" excel:"title:Action Code"`
}

func (l *LineOperationCepRangeRowData) SetRowId(rowId int) {
	l.RowId = rowId
}

type LineBasicRouteRowData struct {
	RowId      int
	Category   string `json:"Category"`
	LineId     string `json:"Line ID"`
	GroupId    string `json:"Group ID"`
	Type       string `json:"Type"`
	FromArea   string `json:"From Area/Zone Area"`
	ToArea     string `json:"To Area"`
	ActionCode int8   `json:"Action Code"`
}

func (l *LineBasicRouteRowData) SetRowId(rowId int) {
	l.RowId = rowId
}

type LineOperationRouteRowData struct {
	RowId       int
	Category    string `json:"Category"`
	LineId      string `json:"Line ID"`
	GroupId     string `json:"Group ID"`
	Region      string `json:"Region"`
	BanFromArea string `json:"Ban From Area"`
	BanToArea   string `json:"Ban To Area"`
	ActionCode  int8   `json:"Action Code"`
}

func (l *LineOperationRouteRowData) SetRowId(rowId int) {
	l.RowId = rowId
}

type ProductServiceableCepGroupRowdata struct {
	RowId      int
	ChannelId  int    `json:"Channel ID"`
	CepInitial int    `json:"CEP Initial"`
	CepFinal   int    `json:"CEP Final"`
	GroupName  string `json:"Group Name"`
}

func (p *ProductServiceableCepGroupRowdata) SetRowId(rowId int) {
	p.RowId = rowId
}

// 上传文件字段解析函数集
// 1. 通用服务范围文件字段解析
func ParseGroupId(value string) (interface{}, *lcos_error.LCOSError) {
	if len(value) > 4 {
		value = value[:4]
	}
	if _, ok := constant.GroupIDToGroupIDDetailMap[value]; !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Invalid group id, please re-confirm.")
	}
	return value, nil
}

func ParseRegion(value string) (interface{}, *lcos_error.LCOSError) {
	return strings.ToUpper(value), nil
}

func ParseSupport(value string) (interface{}, *lcos_error.LCOSError) {
	if utils.InStringSlice(strings.ToUpper(value), []string{"YES", "1"}) {
		return 1, nil
	}
	if utils.InStringSlice(strings.ToUpper(value), []string{"NO", "0"}) {
		return 0, nil
	}
	return nil, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "'%s' is invalid", value)
}

func ParseActionCode(value string) (interface{}, *lcos_error.LCOSError) {
	code, err := strconv.Atoi(value)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Action code should be 1(create/update) or -1(delete)")
	}
	actionCode := int8(code)
	if !utils.CheckInInt8(actionCode, serviceable_constant.ValidActionCode) {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Action code should be 1(create/update) or -1(delete)")
	}
	return actionCode, nil
}

// 2. CepRange服务范围文件校验
func ParseCEP(value string) (interface{}, *lcos_error.LCOSError) {
	cep, err := strconv.Atoi(value)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
	}
	if cep <= 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP should be larger than 0")
	}
	return cep, nil
}

// 3. Zone/Route服务范围文件校验
func ParseRouteCategory(value string) (interface{}, *lcos_error.LCOSError) {
	value = strings.ToLower(value)
	if !utils.InStringSlice(value, serviceable_constant.ValidRouteCategory) {
		return "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Invalid category, please re-confirm.")
	}
	return value, nil
}

func ParseRouteType(value string) (interface{}, *lcos_error.LCOSError) {
	value = strings.ToLower(value)
	if !utils.InStringSlice(value, serviceable_constant.ValidRouteType) {
		return "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Invalid type, please re-confirm.")
	}
	return value, nil
}

func ParseFixedEdtCepProductId(value string) (interface{}, *lcos_error.LCOSError) {
	productID := strings.Trim(value, " ")
	// product id 不能为空
	if productID == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Missing mandatory data,product id is required.")
	}

	ret, err := strconv.Atoi(productID)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
	}
	return ret, nil
}

func ParseFixedEdtCepCutoffTime(value string) (interface{}, *lcos_error.LCOSError) {
	cutoffTime := strings.Trim(value, " ")
	strList := strings.Split(cutoffTime, ":")
	// cut off time格式错误
	if len(strList) != 2 {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("Formatting error in 'Cutoff Time':%s", cutoffTime))
	}

	// 解析小时和分钟
	hour, err := strconv.Atoi(strList[0])
	if err != nil || hour < 0 || hour > 23 {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("Formatting error in 'Cutoff Time':%s", cutoffTime))
	}

	minute, err := strconv.Atoi(strList[1])
	if err != nil || minute < 0 || minute > 59 {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("Formatting error in 'Cutoff Time':%s", cutoffTime))
	}
	return hour*60*60 + minute*60, nil
}

func ParseFixedEdtCepSenderRegion(value string) (interface{}, *lcos_error.LCOSError) {
	senderRegion := strings.Trim(value, " ")
	// 新增时，country不能为空
	if senderRegion == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Missing mandatory data,sender region is required.")
	}
	return senderRegion, nil
}

func ParseFixedEdtCepReceiverRegion(value string) (interface{}, *lcos_error.LCOSError) {
	receiverRegion := strings.Trim(value, " ")
	// 新增时，receiver region不能为空
	if receiverRegion == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Missing mandatory data, receiver region is required.")
	}
	return receiverRegion, nil
}

func ParseFixedEdtCepFixApt(value string) (interface{}, *lcos_error.LCOSError) {
	fixApt := strings.Trim(value, " ")
	// 新增时，fix apt不能为空
	if fixApt == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Missing mandatory data,fix apt is required.")
	}

	// fix cdt不为负数
	val, err := strconv.Atoi(fixApt)
	if err != nil || val < 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "fix apt must be a positive integer.")
	}
	return val, nil
}

func ParseFixedEdtCepFixCDT(value string) (interface{}, *lcos_error.LCOSError) {
	fixCdt := strings.Trim(value, " ")
	// 新增时，fix cdt不能为空
	if fixCdt == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "Missing mandatory data, fix cdt is required.")
	}
	// fix cdt不为负数
	val, err := strconv.Atoi(fixCdt)
	if err != nil || val < 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, "fix cdt must be a positive integer.")
	}

	return val, nil
}

func ParseFixedEdtCepActionCode(value string) (interface{}, *lcos_error.LCOSError) {
	actionCode := strings.Trim(value, " ")
	if !(actionCode == edt_rule.ActionAdd || actionCode == edt_rule.ActionDelete) {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("Invalid action code.[%s]", actionCode))
	}
	return actionCode, nil
}

func ParseFixedEdtCEP(value string) (interface{}, *lcos_error.LCOSError) {
	if value == "" {
		return 0, nil
	}

	cep, err := strconv.Atoi(value)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
	}
	if cep <= 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP should be larger than 0")
	}
	return cep, nil
}
