package serviceable_util

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"

func GetEffectiveStatus(lineCollectDeliver uint32, pickupMode uint32, deliverMode uint32) int32 {
	oldLineCollectDeliver := ChangeToOldDeliverMode(lineCollectDeliver)
	pick := oldLineCollectDeliver & pickupMode     // line支持group的pickupMode
	deliver := oldLineCollectDeliver & deliverMode // line支持group的deliverMode
	if pick == 0 || deliver == 0 {                 // 有一个不支持就返回0
		return 0
	} else {
		return 1
	}
}

// 为了兼容：只要有新类型中的to-branch, to-wms, to-3pl中的一种 就补充to-site类型为1
func ChangeToOldDeliverMode(CDMode uint32) uint32 {
	// 先把原有的TOSITE位置0
	CDMode = CDMode &^ uint32(constant.TOSITE)

	newToSite := constant.TOBRANCH | constant.TOWMS | constant.TO3PL
	if CDMode&(uint32(newToSite)) != 0 {
		return CDMode | uint32(constant.TOSITE)
	}
	return CDMode
}
