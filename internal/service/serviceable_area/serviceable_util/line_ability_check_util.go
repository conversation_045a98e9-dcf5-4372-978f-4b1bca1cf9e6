package serviceable_util

import (
	"context"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"math"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/local_formula"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

func CheckLocationLevel(checkSender *uint32, checkReceiver *uint32, senderCheckLevel *uint32, receiverCheckLevel *uint32) *lcos_error.LCOSError {
	if uint8(*checkSender) == constant.ENABLED && senderCheckLevel == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "sender check level is null")
	}
	if uint8(*checkReceiver) == constant.ENABLED && receiverCheckLevel == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "receiver check level is null")
	}
	return nil
}

func CheckLineAbility(lineAbility uint32, collectType uint32, deliverType uint32) *lcos_error.LCOSError {
	if lineAbility&collectType == 0 {
		return lcos_error.NewLCOSError(lcos_error.NotSupportCollectAbilityErrorCode, "line collect ability not support")
	}

	if lineAbility&deliverType == 0 {
		return lcos_error.NewLCOSError(lcos_error.NotSupportDeliverAbilityErrorCode, "line deliver ability not support")
	}

	return nil
}

func CheckLineCollectAbility(lineAbility uint32, collectType uint32) *lcos_error.LCOSError {
	if lineAbility&collectType == 0 {
		return lcos_error.NewLCOSError(lcos_error.NotSupportCollectAbilityErrorCode, "line collect ability not support")
	}
	return nil
}

func CheckLineDeliverAbility(lineAbility uint32, deliverType uint32) *lcos_error.LCOSError {
	if lineAbility&deliverType == 0 {
		return lcos_error.NewLCOSError(lcos_error.NotSupportDeliverAbilityErrorCode, "line deliver ability not support")
	}

	return nil
}

func CheckLineDistance(baseConfModel *model.LineBasicServiceableConfTab, pickupInfo *pb.LocationInfo, deliverInfo *pb.LocationInfo) *lcos_error.LCOSError {
	if baseConfModel.IsCheckDistance == constant.ENABLED {
		distance, err := calculateDistance(pickupInfo, deliverInfo)
		if err != nil {
			return err
		}
		if !local_formula.CompareWith(baseConfModel.MaximumDistanceOperator, distance, float64(baseConfModel.MaximumDistance)) {
			return lcos_error.NewLCOSError(lcos_error.DistanceExceedMaxErrorCode, "distance exceed max limit")
		}
		if !local_formula.CompareWith(baseConfModel.MinimumDistanceOperator, distance, float64(baseConfModel.MinimumDistance)) {
			return lcos_error.NewLCOSError(lcos_error.DistanceExceedMinErrorCode, "distance exceed min limit")
		}
	}

	return nil
}

func calculateDistance(pickupInfo *pb.LocationInfo, deliverInfo *pb.LocationInfo) (float64, *lcos_error.LCOSError) {
	if pickupInfo == nil || deliverInfo == nil {
		return 0, lcos_error.NewLCOSError(lcos_error.DistanceCalculateErrorCode, "calculate distance error, pickup info incomplete")
	}
	if pickupInfo.Latitude == nil || pickupInfo.Longitude == nil {
		return 0, lcos_error.NewLCOSError(lcos_error.DistanceCalculateErrorCode, "calculate distance error, pickup info incomplete")
	}
	if deliverInfo.Latitude == nil || deliverInfo.Longitude == nil {
		return 0, lcos_error.NewLCOSError(lcos_error.DistanceCalculateErrorCode, "calculate distance error, deliver info incomplete")
	}

	pickupLatitude, err := strconv.ParseFloat(*pickupInfo.Latitude, 64)
	if err != nil {
		return 0, lcos_error.NewLCOSError(lcos_error.DistanceCalculateErrorCode, "calculate distance error, pickup latitude invalid")
	}
	pickupLongitude, err := strconv.ParseFloat(*pickupInfo.Longitude, 64)
	if err != nil {
		return 0, lcos_error.NewLCOSError(lcos_error.DistanceCalculateErrorCode, "calculate distance error, pickup longitude invalid")
	}
	deliverLatitude, err := strconv.ParseFloat(*deliverInfo.Latitude, 64)
	if err != nil {
		return 0, lcos_error.NewLCOSError(lcos_error.DistanceCalculateErrorCode, "calculate distance error, deliver latitude invalid")
	}
	deliverLongitude, err := strconv.ParseFloat(*deliverInfo.Longitude, 64)
	if err != nil {
		return 0, lcos_error.NewLCOSError(lcos_error.DistanceCalculateErrorCode, "calculate distance error, deliver longitude invalid")
	}

	radius := 6378137.0
	rad := math.Pi / 180.0
	pickupLatitude = pickupLatitude * rad
	pickupLongitude = pickupLongitude * rad
	deliverLatitude = deliverLatitude * rad
	deliverLongitude = deliverLongitude * rad
	theta := deliverLongitude - pickupLongitude
	dist := math.Acos(math.Sin(pickupLatitude)*math.Sin(deliverLatitude) + math.Cos(pickupLatitude)*math.Cos(deliverLatitude)*math.Cos(theta))

	return dist * radius, nil
}

func FillCollectDeliverType(baseConfModel *model.LineBasicServiceableConfTab, request *pb.GetLineServiceableInfoRequest) {
	if request.BaseInfo.CollectType == nil {
		collectType := uint32(constant.PICKUP)
		request.BaseInfo.CollectType = &collectType
	}
	if request.BaseInfo.DeliverType == nil {
		deliverType := uint32(constant.TOHOME)
		request.BaseInfo.DeliverType = &deliverType
	}
}

func FillCollectDeliverTypeWhileEmpty(ctx context.Context, baseConfModel *model.LineBasicServiceableConfTab, request *pb.GetLineServiceableInfoRequest2) {
	if request.BaseInfo.CollectType == nil {
		// SPLN-21094 对于LBR45需要填充其默认的collect type为drop off
		// SPLN-26080 make line configgable
		if utils.InStringSlice(baseConfModel.LineId, config.GetDefaultDropOffLines(ctx)) {
			collectType := uint32(constant.DROPOFF)
			request.BaseInfo.CollectType = &collectType
		} else {
			collectType := uint32(constant.PICKUP)
			request.BaseInfo.CollectType = &collectType
		}
	}
	if request.BaseInfo.DeliverType == nil {
		deliverType := uint32(constant.TOHOME)
		request.BaseInfo.DeliverType = &deliverType
	}
}

func IsDropoffOnly(ability uint32) bool {
	if err := CheckLineCollectAbility(ability, uint32(constant.DROPOFF)); err != nil {
		// 不支持dropoff
		return false
	}
	if err := CheckLineCollectAbility(ability, uint32(constant.PICKUP|constant.B2C)); err == nil {
		// 支持pickup或B2C
		return false
	}
	return true
}
