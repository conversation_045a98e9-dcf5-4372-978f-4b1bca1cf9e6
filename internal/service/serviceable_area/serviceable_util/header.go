package serviceable_util

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
)

var LineBasicLocationHeader = []excel.ParseableField{
	{
		Name:       "Line ID",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Group ID",
		Required:   true,
		ParseValue: ParseGroupId,
	},
	{
		Name:       "Region",
		Required:   true,
		ParseValue: ParseRegion,
	},
	{
		Name:       "State",
		Required:   false,
		ParseValue: nil,
	},
	{
		Name:       "City",
		Required:   false,
		ParseValue: nil,
	},
	{
		Name:       "District",
		Required:   false,
		ParseValue: nil,
	},
	{
		Name:       "Street",
		Required:   false,
		ParseValue: nil,
	},
	{
		Name:       "Support Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support COD Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support COD Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support Trade In Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Action Code",
		Required:   true,
		ParseValue: ParseActionCode,
	},
}

var LineOperationLocationHeader = []excel.ParseableField{
	{
		Name:       "Line ID",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Group ID",
		Required:   true,
		ParseValue: ParseGroupId,
	},
	{
		Name:       "Region",
		Required:   true,
		ParseValue: ParseRegion,
	},
	{
		Name:       "State",
		Required:   false,
		ParseValue: nil,
	},
	{
		Name:       "City",
		Required:   false,
		ParseValue: nil,
	},
	{
		Name:       "District",
		Required:   false,
		ParseValue: nil,
	},
	{
		Name:       "Street",
		Required:   false,
		ParseValue: nil,
	},
	{
		Name:       "Ban Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban COD Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban COD Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban Trade In Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Action Code",
		Required:   true,
		ParseValue: ParseActionCode,
	},
}

var LineBasicPostcodeHeader = []excel.ParseableField{
	{
		Name:       "Line ID",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Group ID",
		Required:   true,
		ParseValue: ParseGroupId,
	},
	{
		Name:       "Region",
		Required:   true,
		ParseValue: ParseRegion,
	},
	{
		Name:       "Postcode",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Support Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support COD Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support COD Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support Trade In Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Action Code",
		Required:   true,
		ParseValue: ParseActionCode,
	},
}

var LineOperationPostcodeHeader = []excel.ParseableField{
	{
		Name:       "Line ID",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Group ID",
		Required:   true,
		ParseValue: ParseGroupId,
	},
	{
		Name:       "Region",
		Required:   true,
		ParseValue: ParseRegion,
	},
	{
		Name:       "Postcode",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Ban Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban COD Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban COD Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban Trade In Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Action Code",
		Required:   true,
		ParseValue: ParseActionCode,
	},
}

var LineBasicCepRangeHeader = []excel.ParseableField{
	{
		Name:       "Line ID",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Group ID",
		Required:   true,
		ParseValue: ParseGroupId,
	},
	{
		Name:       "Region",
		Required:   true,
		ParseValue: ParseRegion,
	},
	{
		Name:       "CEP Initial",
		Required:   true,
		ParseValue: ParseCEP,
	},
	{
		Name:       "CEP Final",
		Required:   true,
		ParseValue: ParseCEP,
	},
	{
		Name:       "Support Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support COD Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support COD Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Support Trade In Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Action Code",
		Required:   true,
		ParseValue: ParseActionCode,
	},
}

var LineOperationCepRangeHeader = []excel.ParseableField{
	{
		Name:       "Line ID",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Group ID",
		Required:   true,
		ParseValue: ParseGroupId,
	},
	{
		Name:       "Region",
		Required:   true,
		ParseValue: ParseRegion,
	},
	{
		Name:       "CEP Initial",
		Required:   true,
		ParseValue: ParseCEP,
	},
	{
		Name:       "CEP Final",
		Required:   true,
		ParseValue: ParseCEP,
	},
	{
		Name:       "Ban Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban COD Pickup",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban COD Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Ban Trade In Deliver",
		Required:   true,
		ParseValue: ParseSupport,
	},
	{
		Name:       "Action Code",
		Required:   true,
		ParseValue: ParseActionCode,
	},
}

var LineBasicRouteHeader = []excel.ParseableField{
	{
		Name:       "Category",
		Required:   true,
		ParseValue: ParseRouteCategory,
	},
	{
		Name:       "Line ID",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Group ID",
		Required:   true,
		ParseValue: ParseGroupId,
	},
	{
		Name:       "Type",
		Required:   true,
		ParseValue: ParseRouteType,
	},
	{
		Name:       "From Area/Zone Area",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "To Area",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Action Code",
		Required:   true,
		ParseValue: ParseActionCode,
	},
}

var LineOperationRouteHeader = []excel.ParseableField{
	{
		Name:       "Category",
		Required:   true,
		ParseValue: ParseRouteCategory,
	},
	{
		Name:       "Line ID",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Group ID",
		Required:   true,
		ParseValue: ParseGroupId,
	},
	{
		Name:       "Region",
		Required:   true,
		ParseValue: ParseRegion,
	},
	{
		Name:       "Ban From Area",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Ban To Area",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Action Code",
		Required:   true,
		ParseValue: ParseActionCode,
	},
}

var ProductServiceableCepGroupHeader = []excel.ParseableField{
	{
		Name:       "Channel ID",
		Required:   true,
		ParseValue: excel.ParseInt,
	},
	{
		Name:       "CEP Initial",
		Required:   true,
		ParseValue: ParseCEP,
	},
	{
		Name:       "CEP Final",
		Required:   true,
		ParseValue: ParseCEP,
	},
	{
		Name:     "Group Name",
		Required: true,
	},
}

var FixedEdtCepGroupHeader = []excel.ParseableField{
	{
		Name:       "product_id",
		Required:   false,
		ParseValue: ParseFixedEdtCepProductId,
	},
	{
		Name:       "client_group_id",
		Required:   false,
		ParseValue: nil,
	},
	{
		Name:       "cutoff_time",
		Required:   false,
		ParseValue: ParseFixedEdtCepCutoffTime,
	},
	{
		Name:       "sender_region",
		Required:   false,
		ParseValue: ParseFixedEdtCepSenderRegion,
	},
	{
		Name:       "sender_cep_initial",
		Required:   false,
		ParseValue: ParseFixedEdtCEP,
	},
	{
		Name:       "sender_cep_final",
		Required:   false,
		ParseValue: ParseFixedEdtCEP,
	},
	{
		Name:       "receiver_region",
		Required:   false,
		ParseValue: ParseFixedEdtCepReceiverRegion,
	},
	{
		Name:       "receiver_cep_initial",
		Required:   false,
		ParseValue: ParseFixedEdtCEP,
	},
	{
		Name:       "receiver_cep_final",
		Required:   false,
		ParseValue: ParseFixedEdtCEP,
	},
	{
		Name:       "fix_apt",
		Required:   false,
		ParseValue: ParseFixedEdtCepFixApt,
	},
	{
		Name:       "fix_cdt",
		Required:   false,
		ParseValue: ParseFixedEdtCepFixCDT,
	},
	{
		Name:       "action_code",
		Required:   false,
		ParseValue: ParseFixedEdtCepActionCode,
	},
}
