package shop_serviceable_zone

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/shop_serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/zone_serviceable_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_cep_blacklist_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_buyer_address_blacklist_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_conf_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
)

// Mock DAOs
type mockProductServiceableZoneDao struct {
	mock.Mock
}

func (m *mockProductServiceableZoneDao) ListAllZoneByProductIdUsingCache(ctx utils.LCOSContext, productId int) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, productId)
	return args.Get(0).([]*product_serviceable_zone.LogisticProductServiceableZoneTab), args.Get(1).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) ListAllCepGroupByProductIdUsingCache(ctx utils.LCOSContext, productId int) ([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, productId)
	return args.Get(0).([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab), args.Get(1).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) GetProductServiceableCepGroupByZoneName(ctx utils.LCOSContext, productId int, zoneName string) ([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, productId, zoneName)
	return args.Get(0).([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab), args.Get(1).(*lcos_error.LCOSError)
}

// Add missing interface methods
func (m *mockProductServiceableZoneDao) BatchCreateLogisticProductServiceableZone(ctx utils.LCOSContext, dataList []*product_serviceable_zone.LogisticProductServiceableZoneTab) *lcos_error.LCOSError {
	args := m.Called(ctx, dataList)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) DeleteLogisticProductServiceableZone(ctx utils.LCOSContext, productId int, metroRegion string) *lcos_error.LCOSError {
	args := m.Called(ctx, productId, metroRegion)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) ListLogisticProductServiceableZoneWithPaging(ctx utils.LCOSContext, queryMap map[string]interface{}, pageNo, pageSize uint32) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, uint32, *lcos_error.LCOSError) {
	args := m.Called(ctx, queryMap, pageNo, pageSize)
	return args.Get(0).([]*product_serviceable_zone.LogisticProductServiceableZoneTab), args.Get(1).(uint32), args.Get(2).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) ListLogisticProductServiceableZone(ctx utils.LCOSContext, queryMap map[string]interface{}) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, queryMap)
	return args.Get(0).([]*product_serviceable_zone.LogisticProductServiceableZoneTab), args.Get(1).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) ListLogisticProductServiceableZoneName(ctx utils.LCOSContext, queryMap map[string]interface{}) ([]string, *lcos_error.LCOSError) {
	args := m.Called(ctx, queryMap)
	return args.Get(0).([]string), args.Get(1).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) BatchCreateLogisticProductServiceableCepGroup(ctx utils.LCOSContext, dataList []*product_serviceable_zone.LogisticProductServiceableCepGroupTab) *lcos_error.LCOSError {
	args := m.Called(ctx, dataList)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) DeleteLogisticProductServiceableCepGroup(ctx utils.LCOSContext, queryMap map[string]interface{}) *lcos_error.LCOSError {
	args := m.Called(ctx, queryMap)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) ListLogisticProductServiceableCepGroup(ctx utils.LCOSContext, queryMap map[string]interface{}) ([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, queryMap)
	return args.Get(0).([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab), args.Get(1).(*lcos_error.LCOSError)
}

func (m *mockProductServiceableZoneDao) GetProductServiceableZoneByPostcode(ctx utils.LCOSContext, productId, postcode int) (string, string, *lcos_error.LCOSError) {
	args := m.Called(ctx, productId, postcode)
	return args.String(0), args.String(1), args.Get(2).(*lcos_error.LCOSError)
}

type mockShopServiceableZoneDao struct {
	mock.Mock
}

func (m *mockShopServiceableZoneDao) BatchCreateLogisticShopServiceableZone(ctx utils.LCOSContext, dataList []*shop_serviceable_zone.LogisticShopServiceableZoneTab) *lcos_error.LCOSError {
	args := m.Called(ctx, dataList)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockShopServiceableZoneDao) DeleteLogisticShopServiceableZone(ctx utils.LCOSContext, queryMap map[string]interface{}) *lcos_error.LCOSError {
	args := m.Called(ctx, queryMap)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockShopServiceableZoneDao) ListLogisticShopServiceableZone(ctx utils.LCOSContext, queryMap map[string]interface{}) ([]*shop_serviceable_zone.LogisticShopServiceableZoneTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, queryMap)
	return args.Get(0).([]*shop_serviceable_zone.LogisticShopServiceableZoneTab), args.Get(1).(*lcos_error.LCOSError)
}

func (m *mockShopServiceableZoneDao) SearchShopServiceableZone(ctx utils.LCOSContext, shopId, productId int) ([]string, *lcos_error.LCOSError) {
	args := m.Called(ctx, shopId, productId)
	return args.Get(0).([]string), args.Get(1).(*lcos_error.LCOSError)
}

type mockShopServiceableAreaBasicConfRepo struct {
	mock.Mock
}

func (m *mockShopServiceableAreaBasicConfRepo) CreateOrUpdateShopServiceableAreaBasicConf(ctx utils.LCOSContext, conf *shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab) *lcos_error.LCOSError {
	args := m.Called(ctx, conf)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockShopServiceableAreaBasicConfRepo) GetShopServiceableAreaBasicConfByShopAndProduct(ctx utils.LCOSContext, shopId, productId int) (*shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, shopId, productId)
	if args.Get(0) == nil {
		return nil, args.Get(1).(*lcos_error.LCOSError)
	}
	return args.Get(0).(*shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab), args.Get(1).(*lcos_error.LCOSError)
}

func (m *mockShopServiceableAreaBasicConfRepo) DeleteShopServiceableAreaBasicConfByShopAndProduct(ctx utils.LCOSContext, shopId, productId int) *lcos_error.LCOSError {
	args := m.Called(ctx, shopId, productId)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockShopServiceableAreaBasicConfRepo) GetShopServiceableAreaBasicConfUsingCache(ctx utils.LCOSContext, shopId, productId int) (*shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, shopId, productId)
	if args.Get(0) == nil {
		return nil, args.Get(1).(*lcos_error.LCOSError)
	}
	return args.Get(0).(*shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab), args.Get(1).(*lcos_error.LCOSError)
}

type mockShopBuyerAddressBlacklistRepo struct {
	mock.Mock
}

func (m *mockShopBuyerAddressBlacklistRepo) BatchCreateShopBuyerAddressBlacklist(ctx utils.LCOSContext, dataList []*shop_buyer_address_blacklist_repo.ShopBuyerAddressBlacklistTab) *lcos_error.LCOSError {
	args := m.Called(ctx, dataList)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockShopBuyerAddressBlacklistRepo) DeleteShopBuyerAddressBlacklistByShopAndProduct(ctx utils.LCOSContext, shopId, productId int) *lcos_error.LCOSError {
	args := m.Called(ctx, shopId, productId)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockShopBuyerAddressBlacklistRepo) ListShopBuyerAddressBlacklistByShopAndProduct(ctx utils.LCOSContext, shopId, productId int) ([]*shop_buyer_address_blacklist_repo.ShopBuyerAddressBlacklistTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, shopId, productId)
	return args.Get(0).([]*shop_buyer_address_blacklist_repo.ShopBuyerAddressBlacklistTab), args.Get(1).(*lcos_error.LCOSError)
}

func (m *mockShopBuyerAddressBlacklistRepo) GetShopBuyerAddressBlacklistUsingCache(ctx utils.LCOSContext, shopId, productId, locationId int) (bool, *lcos_error.LCOSError) {
	args := m.Called(ctx, shopId, productId, locationId)
	return args.Bool(0), args.Get(1).(*lcos_error.LCOSError)
}

type mockProductCepBlacklistRepo struct {
	mock.Mock
}

func (m *mockProductCepBlacklistRepo) ListLogisticProductCepBlacklistByParams(ctx utils.LCOSContext, params map[string]interface{}) ([]*product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, params)
	return args.Get(0).([]*product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab), args.Get(1).(*lcos_error.LCOSError)
}

func (m *mockProductCepBlacklistRepo) BatchCreateLogisticProductCepBlacklist(ctx utils.LCOSContext, dataList []*product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab) *lcos_error.LCOSError {
	args := m.Called(ctx, dataList)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockProductCepBlacklistRepo) DeleteLogisticProductCepBlacklistByProduct(ctx utils.LCOSContext, productId int) *lcos_error.LCOSError {
	args := m.Called(ctx, productId)
	return args.Get(0).(*lcos_error.LCOSError)
}

func (m *mockProductCepBlacklistRepo) GetLogisticProductCepBlacklistUsingCache(ctx utils.LCOSContext, productId int, cep int) (*product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab, *lcos_error.LCOSError) {
	args := m.Called(ctx, productId, cep)
	if args.Get(0) == nil {
		return nil, args.Get(1).(*lcos_error.LCOSError)
	}
	return args.Get(0).(*product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab), args.Get(1).(*lcos_error.LCOSError)
}

// Mock context
type mockLCOSContext struct {
	utils.LCOSContext
	country string
}

func (m *mockLCOSContext) GetCountry() string {
	return m.country
}

func (m *mockLCOSContext) Transaction(fn func() *lcos_error.LCOSError) *lcos_error.LCOSError {
	return fn()
}

// 实现context.Context接口的必要方法
func (m *mockLCOSContext) Deadline() (deadline time.Time, ok bool) {
	return time.Time{}, false
}

func (m *mockLCOSContext) Done() <-chan struct{} {
	return nil
}

func (m *mockLCOSContext) Err() error {
	return nil
}

func (m *mockLCOSContext) Value(key interface{}) interface{} {
	return nil
}

func TestUpdateShopServiceableZone(t *testing.T) {
	// Initialize config for testing
	config.MutableConf = &config.MutableConfig{
		DirectDeliveryConfig: config.DirectDeliveryConfig{
			ProductBlacklist: []int{90033}, // 添加黑名单产品ID
		},
	}

	tests := []struct {
		name          string
		request       *zone_serviceable_protocol.UpdateShopServiceableZoneRequest
		setupMocks    func(*mockProductServiceableZoneDao, *mockShopServiceableZoneDao, *mockShopServiceableAreaBasicConfRepo, *mockShopBuyerAddressBlacklistRepo, *mockProductCepBlacklistRepo)
		expectedResp  *zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse
		expectedError *lcos_error.LCOSError
	}{
		{
			name: "Product在黑名单中 - 直接返回nil",
			request: &zone_serviceable_protocol.UpdateShopServiceableZoneRequest{
				ShopId:    123,
				ProductId: 90033, // 在黑名单中的产品ID
				SellerAddress: zone_serviceable_protocol.AddressDetail{
					Postcode: "12345",
				},
			},
			setupMocks: func(productZoneDao *mockProductServiceableZoneDao, shopZoneDao *mockShopServiceableZoneDao, shopConfDao *mockShopServiceableAreaBasicConfRepo, buyerBlacklistDao *mockShopBuyerAddressBlacklistRepo, cepBlacklistDao *mockProductCepBlacklistRepo) {
				// 当产品在黑名单中时，不需要任何mock调用，函数应该直接返回nil
			},
			expectedResp:  nil,
			expectedError: nil,
		},
		{
			name: "DD模式 - 成功更新shop服务范围",
			request: &zone_serviceable_protocol.UpdateShopServiceableZoneRequest{
				ShopId:          123,
				ProductId:       456,
				EnabledZoneList: []string{"ZONE_A", "ZONE_B"},
				SellerAddress: zone_serviceable_protocol.AddressDetail{
					Postcode: "12345",
				},
			},
			setupMocks: func(productZoneDao *mockProductServiceableZoneDao, shopZoneDao *mockShopServiceableZoneDao, shopConfDao *mockShopServiceableAreaBasicConfRepo, buyerBlacklistDao *mockShopBuyerAddressBlacklistRepo, cepBlacklistDao *mockProductCepBlacklistRepo) {
				// Mock product zone data
				productZones := []*product_serviceable_zone.LogisticProductServiceableZoneTab{
					{ZoneName: "ZONE_A", MetroRegion: "Metro_A"},
					{ZoneName: "ZONE_B", MetroRegion: "Metro_A"},
				}
				productZoneDao.On("ListAllZoneByProductIdUsingCache", mock.Anything, 456).Return(productZones, (*lcos_error.LCOSError)(nil))

				// Mock CEP group data - 12345 falls within ZONE_A range
				cepGroups := []*product_serviceable_zone.LogisticProductServiceableCepGroupTab{
					{ZoneName: "ZONE_A", MetroRegion: "Metro_A", CepInitial: 10000, CepFinal: 20000},
					{ZoneName: "ZONE_B", MetroRegion: "Metro_A", CepInitial: 20000, CepFinal: 30000},
				}
				productZoneDao.On("ListAllCepGroupByProductIdUsingCache", mock.Anything, 456).Return(cepGroups, (*lcos_error.LCOSError)(nil))

				// Mock shop config - existing config
				shopConf := &shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab{
					ShopId:       123,
					ProductId:    456,
					DeliveryMode: shop_serviceable_constant.DeliveryModeDD,
					Radius:       15,
				}
				shopConfDao.On("GetShopServiceableAreaBasicConfByShopAndProduct", mock.Anything, 123, 456).Return(shopConf, (*lcos_error.LCOSError)(nil))

				// Mock shop zone operations
				shopZoneDao.On("DeleteLogisticShopServiceableZone", mock.Anything, mock.Anything).Return((*lcos_error.LCOSError)(nil))
				shopZoneDao.On("BatchCreateLogisticShopServiceableZone", mock.Anything, mock.Anything).Return((*lcos_error.LCOSError)(nil))

				// Mock shop conf update
				shopConfDao.On("CreateOrUpdateShopServiceableAreaBasicConf", mock.Anything, mock.Anything).Return((*lcos_error.LCOSError)(nil))
			},
			expectedResp: &zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse{
				OriginCepZone:          "ZONE_A",
				CityName:               "Metro_A",
				DestinationCepZoneList: []string{"ZONE_A", "ZONE_B"},
				AvailableCepZoneList:   []string{"ZONE_A", "ZONE_B"},
				DeliveryMode:           shop_serviceable_constant.DeliveryModeDD,
			},
			expectedError: nil,
		},
		{
			name: "DDE模式 - 成功更新shop服务范围",
			request: &zone_serviceable_protocol.UpdateShopServiceableZoneRequest{
				ShopId:    123,
				ProductId: 456,
				SellerAddress: zone_serviceable_protocol.AddressDetail{
					Postcode: "99999", // 不在任何zone范围内的邮编
				},
				EverywhereSetting: &zone_serviceable_protocol.EverywhereSetting{
					Radius:             func() *int64 { r := int64(20); return &r }(),
					BuyerCityBlacklist: []string{}, // 空黑名单列表，避免外部服务调用
				},
			},
			setupMocks: func(productZoneDao *mockProductServiceableZoneDao, shopZoneDao *mockShopServiceableZoneDao, shopConfDao *mockShopServiceableAreaBasicConfRepo, buyerBlacklistDao *mockShopBuyerAddressBlacklistRepo, cepBlacklistDao *mockProductCepBlacklistRepo) {
				// Mock product zone data - empty to trigger DDE mode
				productZoneDao.On("ListAllZoneByProductIdUsingCache", mock.Anything, 456).Return([]*product_serviceable_zone.LogisticProductServiceableZoneTab{}, (*lcos_error.LCOSError)(nil))

				// Mock CEP group data - empty
				productZoneDao.On("ListAllCepGroupByProductIdUsingCache", mock.Anything, 456).Return([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab{}, (*lcos_error.LCOSError)(nil))

				// Mock shop config - existing config
				shopConf := &shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab{
					ShopId:       123,
					ProductId:    456,
					DeliveryMode: shop_serviceable_constant.DeliveryModeDDE,
					Radius:       15,
				}
				shopConfDao.On("GetShopServiceableAreaBasicConfByShopAndProduct", mock.Anything, 123, 456).Return(shopConf, (*lcos_error.LCOSError)(nil))

				// Mock CEP blacklist - empty
				cepBlacklistDao.On("ListLogisticProductCepBlacklistByParams", mock.Anything, mock.Anything).Return([]*product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab{}, (*lcos_error.LCOSError)(nil))

				// Mock buyer blacklist operations
				buyerBlacklistDao.On("DeleteShopBuyerAddressBlacklistByShopAndProduct", mock.Anything, 123, 456).Return((*lcos_error.LCOSError)(nil))
				buyerBlacklistDao.On("BatchCreateShopBuyerAddressBlacklist", mock.Anything, mock.Anything).Return((*lcos_error.LCOSError)(nil))

				// Mock shop conf update
				shopConfDao.On("CreateOrUpdateShopServiceableAreaBasicConf", mock.Anything, mock.Anything).Return((*lcos_error.LCOSError)(nil))
			},
			expectedResp: &zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse{
				OriginCepZone:          "",
				CityName:               "",
				DestinationCepZoneList: []string{},
				AvailableCepZoneList:   []string{},
				DeliveryMode:           shop_serviceable_constant.DeliveryModeDDE,
				EverywhereSetting: &zone_serviceable_protocol.EverywhereSetting{
					Radius:             func() *int64 { r := int64(20); return &r }(),
					BuyerCityBlacklist: []string{},
				},
			},
			expectedError: nil,
		},
		{
			name: "无效邮编错误",
			request: &zone_serviceable_protocol.UpdateShopServiceableZoneRequest{
				ShopId:    123,
				ProductId: 456,
				SellerAddress: zone_serviceable_protocol.AddressDetail{
					Postcode: "abc", // 只包含非数字字符，ValidPostcode会返回空字符串
				},
			},
			setupMocks: func(productZoneDao *mockProductServiceableZoneDao, shopZoneDao *mockShopServiceableZoneDao, shopConfDao *mockShopServiceableAreaBasicConfRepo, buyerBlacklistDao *mockShopBuyerAddressBlacklistRepo, cepBlacklistDao *mockProductCepBlacklistRepo) {
				// No mocks needed for this test case
			},
			expectedResp:  nil,
			expectedError: lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid address, strconv.Atoi: parsing \"\": invalid syntax"),
		},
		{
			name: "DDE模式 - 缺少everywhere设置错误",
			request: &zone_serviceable_protocol.UpdateShopServiceableZoneRequest{
				ShopId:    123,
				ProductId: 456,
				SellerAddress: zone_serviceable_protocol.AddressDetail{
					Postcode: "99999", // 不在任何zone范围内的邮编
				},
				// No EverywhereSetting provided
			},
			setupMocks: func(productZoneDao *mockProductServiceableZoneDao, shopZoneDao *mockShopServiceableZoneDao, shopConfDao *mockShopServiceableAreaBasicConfRepo, buyerBlacklistDao *mockShopBuyerAddressBlacklistRepo, cepBlacklistDao *mockProductCepBlacklistRepo) {
				// Mock product zone data - empty to trigger DDE mode
				productZoneDao.On("ListAllZoneByProductIdUsingCache", mock.Anything, 456).Return([]*product_serviceable_zone.LogisticProductServiceableZoneTab{}, (*lcos_error.LCOSError)(nil))

				// Mock CEP group data - empty
				productZoneDao.On("ListAllCepGroupByProductIdUsingCache", mock.Anything, 456).Return([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab{}, (*lcos_error.LCOSError)(nil))

				// Mock shop config - existing config
				shopConf := &shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab{
					ShopId:       123,
					ProductId:    456,
					DeliveryMode: shop_serviceable_constant.DeliveryModeDDE,
					Radius:       15,
				}
				shopConfDao.On("GetShopServiceableAreaBasicConfByShopAndProduct", mock.Anything, 123, 456).Return(shopConf, (*lcos_error.LCOSError)(nil))

				// Mock CEP blacklist - empty
				cepBlacklistDao.On("ListLogisticProductCepBlacklistByParams", mock.Anything, mock.Anything).Return([]*product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab{}, (*lcos_error.LCOSError)(nil))
			},
			expectedResp:  nil,
			expectedError: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "seller address is in DDE mode but everywhere setting is empty"),
		},
		{
			name: "DD模式 - 无效的enabled zone错误",
			request: &zone_serviceable_protocol.UpdateShopServiceableZoneRequest{
				ShopId:          123,
				ProductId:       456,
				EnabledZoneList: []string{"INVALID_ZONE"},
				SellerAddress: zone_serviceable_protocol.AddressDetail{
					Postcode: "12345",
				},
			},
			setupMocks: func(productZoneDao *mockProductServiceableZoneDao, shopZoneDao *mockShopServiceableZoneDao, shopConfDao *mockShopServiceableAreaBasicConfRepo, buyerBlacklistDao *mockShopBuyerAddressBlacklistRepo, cepBlacklistDao *mockProductCepBlacklistRepo) {
				// Mock product zone data
				productZones := []*product_serviceable_zone.LogisticProductServiceableZoneTab{
					{ZoneName: "ZONE_A", MetroRegion: "Metro_A"},
					{ZoneName: "ZONE_B", MetroRegion: "Metro_A"},
				}
				productZoneDao.On("ListAllZoneByProductIdUsingCache", mock.Anything, 456).Return(productZones, (*lcos_error.LCOSError)(nil))

				// Mock CEP group data - 12345 falls within ZONE_A range
				cepGroups := []*product_serviceable_zone.LogisticProductServiceableCepGroupTab{
					{ZoneName: "ZONE_A", MetroRegion: "Metro_A", CepInitial: 10000, CepFinal: 20000},
					{ZoneName: "ZONE_B", MetroRegion: "Metro_A", CepInitial: 20000, CepFinal: 30000},
				}
				productZoneDao.On("ListAllCepGroupByProductIdUsingCache", mock.Anything, 456).Return(cepGroups, (*lcos_error.LCOSError)(nil))

				// Mock shop config - existing config
				shopConf := &shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab{
					ShopId:       123,
					ProductId:    456,
					DeliveryMode: shop_serviceable_constant.DeliveryModeDD,
					Radius:       15,
				}
				shopConfDao.On("GetShopServiceableAreaBasicConfByShopAndProduct", mock.Anything, 123, 456).Return(shopConf, (*lcos_error.LCOSError)(nil))
			},
			expectedResp:  nil,
			expectedError: lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "enabled zone not available|product_id=456, zone_name=INVALID_ZONE"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			productZoneDao := &mockProductServiceableZoneDao{}
			shopZoneDao := &mockShopServiceableZoneDao{}
			shopConfDao := &mockShopServiceableAreaBasicConfRepo{}
			buyerBlacklistDao := &mockShopBuyerAddressBlacklistRepo{}
			cepBlacklistDao := &mockProductCepBlacklistRepo{}

			// Setup mocks
			tt.setupMocks(productZoneDao, shopZoneDao, shopConfDao, buyerBlacklistDao, cepBlacklistDao)

			// Create service
			service := &shopServiceableZoneService{
				productZoneDao:    productZoneDao,
				shopZoneDao:       shopZoneDao,
				cepBlacklistDao:   cepBlacklistDao,
				shopConfDao:       shopConfDao,
				buyerBlacklistDao: buyerBlacklistDao,
			}

			// Create context
			ctx := &mockLCOSContext{country: "SG"}

			// Call function
			resp, err := service.UpdateShopServiceableZone(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				assert.NotNil(t, err)
				assert.Nil(t, resp)
				assert.Equal(t, tt.expectedError.RetCode, err.RetCode)
				assert.Contains(t, err.Msg, tt.expectedError.Msg)
			} else {
				assert.Nil(t, err)
				if tt.expectedResp != nil {
					assert.NotNil(t, resp)
					assert.Equal(t, tt.expectedResp.OriginCepZone, resp.OriginCepZone)
					assert.Equal(t, tt.expectedResp.CityName, resp.CityName)
					assert.Equal(t, tt.expectedResp.DeliveryMode, resp.DeliveryMode)
					if tt.expectedResp.EverywhereSetting != nil {
						assert.Equal(t, tt.expectedResp.EverywhereSetting.Radius, resp.EverywhereSetting.Radius)
						assert.Equal(t, tt.expectedResp.EverywhereSetting.BuyerCityBlacklist, resp.EverywhereSetting.BuyerCityBlacklist)
					}
				} else {
					assert.Nil(t, resp)
				}
			}

			// Verify all mocks were called as expected
			productZoneDao.AssertExpectations(t)
			shopZoneDao.AssertExpectations(t)
			shopConfDao.AssertExpectations(t)
			buyerBlacklistDao.AssertExpectations(t)
			cepBlacklistDao.AssertExpectations(t)
		})
	}
}
