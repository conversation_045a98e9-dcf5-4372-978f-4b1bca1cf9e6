package shop_serviceable_zone

import (
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/shop_serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/zone_serviceable_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_cep_blacklist_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_buyer_address_blacklist_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_conf_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/shop_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
)

type ShopServiceableZoneService interface {
	UpdateShopServiceableZone(ctx utils.LCOSContext, req *zone_serviceable_protocol.UpdateShopServiceableZoneRequest) (*zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse, *lcos_error.LCOSError)
	ListShopServiceableZone(ctx utils.LCOSContext, shopId, productId int) ([]string, *lcos_error.LCOSError)

	// QuerySellerServiceableZoneInfo 提供给seller侧的集成接口：
	// 1. 匹配卖家地址所属的zone
	// 2. 查询渠道支持的zone列表
	// 3. 查询shop开启的zone列表
	QuerySellerServiceableZoneInfo(ctx utils.LCOSContext, req *zone_serviceable_protocol.QuerySellerServiceableZoneInfoRequest) (*zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse, *lcos_error.LCOSError)
}

func NewShopServiceableZoneService(productZoneDao product_serviceable_zone.ProductServiceableZoneDao, shopZoneDao shop_serviceable_zone.ShopServiceableZoneDao, cepBlacklistDao product_cep_blacklist_repo.LogisticProductCepBlacklistRepo, shopConfDao shop_serviceable_conf_repo.ShopServiceableAreaBasicConfRepo, buyerBlacklistDao shop_buyer_address_blacklist_repo.ShopBuyerAddressBlacklistRepo) *shopServiceableZoneService {
	return &shopServiceableZoneService{
		productZoneDao:    productZoneDao,
		shopZoneDao:       shopZoneDao,
		cepBlacklistDao:   cepBlacklistDao,
		shopConfDao:       shopConfDao,
		buyerBlacklistDao: buyerBlacklistDao,
	}
}

type shopServiceableZoneService struct {
	productZoneDao    product_serviceable_zone.ProductServiceableZoneDao
	shopZoneDao       shop_serviceable_zone.ShopServiceableZoneDao
	cepBlacklistDao   product_cep_blacklist_repo.LogisticProductCepBlacklistRepo
	shopConfDao       shop_serviceable_conf_repo.ShopServiceableAreaBasicConfRepo
	buyerBlacklistDao shop_buyer_address_blacklist_repo.ShopBuyerAddressBlacklistRepo
}

// UpdateShopServiceableZone 更新shop服务范围配置。
// shop服务范围支持两种校验模式：DD、DDE。
// DD模式：卖家地址在DD polygon中时使用，支持配置可售区域。
// DDE模式：卖家地址不在DD polygon中时使用，支持配置以卖家为中心的服务半径，同时支持配置半径内的黑名单城市。
func (s *shopServiceableZoneService) UpdateShopServiceableZone(ctx utils.LCOSContext, req *zone_serviceable_protocol.UpdateShopServiceableZoneRequest) (*zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse, *lcos_error.LCOSError) {
	var (
		region            = strings.ToUpper(ctx.GetCountry())
		shopId            = req.ShopId
		productId         = req.ProductId
		postcode          = req.SellerAddress.Postcode
		enabledZoneList   = req.EnabledZoneList
		everywhereSetting = req.EverywhereSetting
	)
	// 黑名单中的渠道直接跳过处理，e.g: 90033
	if utils.CheckInInt(productId, config.GetMutableConf(ctx).DirectDeliveryConfig.ProductBlacklist) {
		logger.CtxLogInfof(ctx, "product in the blacklist, ignore this request|product_id=%d", productId)
		return nil, nil
	}

	// 1. 校验并处理卖家地址邮编
	cep, pErr := strconv.Atoi(utils.ValidPostcode(postcode))
	if pErr != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid address, %s", pErr.Error())
	}

	// 2. 获取shop服务范围校验基础配置，如果不存在则新建
	shopConf, err := s.shopConfDao.GetShopServiceableAreaBasicConfByShopAndProduct(ctx, shopId, productId)
	if err != nil {
		return nil, err
	}
	if shopConf == nil {
		shopConf = &shop_serviceable_conf_repo.ShopServiceableAreaBasicConfTab{
			ShopId:    shopId,
			ProductId: productId,
			Radius:    shop_serviceable_constant.DDEModeDefaultRadius, // 默认15km
		}
	}

	// 3. 卖家地址匹配所属的zone、metro region以及可开启的zone列表（能匹配到使用DD模式，匹配不到用DDE模式）
	metroRegion, originZone, destZoneList, err := s.MatchSellerOriginZoneAndDestinationZone(ctx, productId, cep)
	if err != nil {
		return nil, err
	}
	if originZone == "" {
		// 校验DDE模式是否可用
		if err = s.CheckProductCepRangeBlacklist(ctx, productId, cep); err != nil {
			return nil, err
		}

		// 卖家地址为DDE模式，但卖家提交保存缺失DDE配置
		if everywhereSetting == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "seller address is in DDE mode but everywhere setting is empty")
		}

		// 更新shop服务范围配置，mode为DDE，并填充服务范围半径
		shopConf.DeliveryMode = shop_serviceable_constant.DeliveryModeDDE
		if everywhereSetting.Radius != nil {
			shopConf.Radius = *everywhereSetting.Radius
		}

		// 校验并生成shop买家地址黑名单数据
		buyerCityBlacklist := make([]*shop_buyer_address_blacklist_repo.ShopBuyerAddressBlacklistTab, 0, len(everywhereSetting.BuyerCityBlacklist))
		for _, blacklistCity := range everywhereSetting.BuyerCityBlacklist {
			splitNames := strings.Split(blacklistCity, ",")
			if len(splitNames) != 2 {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid blacklist city %s", blacklistCity)
			}
			cityName, stateName := strings.TrimSpace(splitNames[0]), strings.TrimSpace(splitNames[1])

			locationInfo, err := ops_service.GetLocationInfoByLocationName(ctx, &ops_service.GetLocationInfoByNameRequest{
				Country:      region,
				LocationName: []string{stateName, cityName},
			})
			if err != nil {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "check blacklist city error: %s", err.Msg)
			}
			buyerCityBlacklist = append(buyerCityBlacklist, &shop_buyer_address_blacklist_repo.ShopBuyerAddressBlacklistTab{
				ShopId:        shopId,
				ProductId:     productId,
				LocationId:    locationInfo.LocationId,
				LocationName:  blacklistCity, // state,city
				LocationLevel: locationInfo.Level,
			})
		}

		// 全量更新shop的买家地址黑名单 & 更新shop的服务范围配置
		err = ctx.Transaction(func() *lcos_error.LCOSError {
			if dbErr := s.buyerBlacklistDao.DeleteShopBuyerAddressBlacklistByShopAndProduct(ctx, shopId, productId); dbErr != nil {
				return dbErr
			}
			if dbErr := s.buyerBlacklistDao.BatchCreateShopBuyerAddressBlacklist(ctx, buyerCityBlacklist); dbErr != nil {
				return dbErr
			}
			if dbErr := s.shopConfDao.CreateOrUpdateShopServiceableAreaBasicConf(ctx, shopConf); dbErr != nil {
				return dbErr
			}
			return nil
		})
		if err != nil {
			return nil, err
		}
	} else {
		// DD模式
		// 更新shop服务范围配置，mode为DD
		shopConf.DeliveryMode = shop_serviceable_constant.DeliveryModeDD

		// 校验并生成shop开启可售区域数据
		destZoneMap := make(map[string]struct{}, len(destZoneList))
		for _, zoneName := range destZoneList {
			destZoneMap[zoneName] = struct{}{}
		}
		shopZoneList := make([]*shop_serviceable_zone.LogisticShopServiceableZoneTab, 0, len(enabledZoneList))
		for _, enabledZone := range enabledZoneList {
			if _, ok := destZoneMap[enabledZone]; !ok {
				// zone不存在或者跟卖家在不同metro region
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "enabled zone not available|product_id=%d, zone_name=%s", productId, enabledZone)
			}
			shopZoneList = append(shopZoneList, &shop_serviceable_zone.LogisticShopServiceableZoneTab{
				ShopId:    shopId,
				ProductId: productId,
				ZoneName:  enabledZone,
			})
		}

		// 全量更新shop的开启可售区域 & 更新shop的服务范围配置
		err = ctx.Transaction(func() *lcos_error.LCOSError {
			if dbErr := s.shopZoneDao.DeleteLogisticShopServiceableZone(ctx, map[string]interface{}{"shop_id": shopId, "product_id": productId}); dbErr != nil {
				return dbErr
			}
			if dbErr := s.shopZoneDao.BatchCreateLogisticShopServiceableZone(ctx, shopZoneList); dbErr != nil {
				return dbErr
			}
			if dbErr := s.shopConfDao.CreateOrUpdateShopServiceableAreaBasicConf(ctx, shopConf); dbErr != nil {
				return dbErr
			}
			return nil
		})
		if err != nil {
			return nil, err
		}
	}
	return &zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse{
		OriginCepZone:          originZone,
		CityName:               metroRegion,
		DestinationCepZoneList: destZoneList,
		AvailableCepZoneList:   enabledZoneList,
		DeliveryMode:           shopConf.DeliveryMode,
		EverywhereSetting:      everywhereSetting,
	}, nil
}

func (s *shopServiceableZoneService) ListShopServiceableZone(ctx utils.LCOSContext, shopId, productId int) ([]string, *lcos_error.LCOSError) {
	queryMap := make(map[string]interface{})
	if shopId != 0 {
		queryMap["shop_id"] = shopId
	}
	if productId != 0 {
		queryMap["product_id"] = productId
	}
	shopEnabledZoneList, err := s.shopZoneDao.ListLogisticShopServiceableZone(ctx, queryMap)
	if err != nil {
		return nil, err
	}
	enabledZoneList := make([]string, 0, len(shopEnabledZoneList))
	for _, zone := range shopEnabledZoneList {
		enabledZoneList = append(enabledZoneList, zone.ZoneName)
	}
	return enabledZoneList, nil
}

// QuerySellerServiceableZoneInfo 提供给seller侧的集成接口，获取shop的服务范围配置详情
func (s *shopServiceableZoneService) QuerySellerServiceableZoneInfo(ctx utils.LCOSContext, req *zone_serviceable_protocol.QuerySellerServiceableZoneInfoRequest) (*zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse, *lcos_error.LCOSError) {
	cep, pErr := strconv.Atoi(utils.ValidPostcode(req.PostalCode))
	if pErr != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid address, %s", pErr.Error())
	}

	resp := new(zone_serviceable_protocol.QuerySellerServiceableZoneInfoResponse)
	metroRegion, originZone, destZoneList, err := s.MatchSellerOriginZoneAndDestinationZone(ctx, req.ProductId, cep)
	if err != nil {
		return nil, err
	}
	if originZone == "" {
		// 校验DDE模式是否可用
		if err = s.CheckProductCepRangeBlacklist(ctx, req.ProductId, cep); err != nil {
			return nil, err
		}

		// DDE模式，获取shop服务范围配置（服务范围半径+买家city黑名单）
		radius, buyerCityBlacklist, err := s.GetShopEverywhereSetting(ctx, req.ShopId, req.ProductId)
		if err != nil {
			return nil, err
		}

		// 卖家地址无法匹配到DD zone，则使用DDE模式
		resp.DeliveryMode = shop_serviceable_constant.DeliveryModeDDE
		resp.CityName = shop_serviceable_constant.DDEModeMetroRegion                      // DDE模式origin zone为"DDE"
		resp.OriginCepZone = shop_serviceable_constant.DDEModeZoneName                    // DDE模式metro region为"DDE"
		resp.DestinationCepZoneList = []string{shop_serviceable_constant.DDEModeZoneName} // DDE模式dest zone为"DDE"
		resp.EverywhereSetting = &zone_serviceable_protocol.EverywhereSetting{
			Radius:             utils.NewInt64(radius),
			BuyerCityBlacklist: buyerCityBlacklist,
		}
	} else {
		// DD模式，获取shop服务范围配置（可售zone列表）
		availableZoneList, err := s.ListShopServiceableZone(ctx, req.ShopId, req.ProductId)
		if err != nil {
			return nil, err
		}

		resp.DeliveryMode = shop_serviceable_constant.DeliveryModeDD
		resp.CityName = metroRegion
		resp.OriginCepZone = originZone
		resp.DestinationCepZoneList = destZoneList
		resp.AvailableCepZoneList = availableZoneList
	}
	return resp, nil
}

// MatchSellerOriginZoneAndDestinationZone 匹配卖家所属的origin one以及可用的dest zone列表
func (s *shopServiceableZoneService) MatchSellerOriginZoneAndDestinationZone(ctx utils.LCOSContext, productId int, cep int) (string, string, []string, *lcos_error.LCOSError) {
	// 1. 获取渠道开启的所有电子围栏区域
	productZoneList, err := s.productZoneDao.ListAllZoneByProductIdUsingCache(ctx, productId)
	if err != nil {
		return "", "", nil, err
	}

	// 2. 根据渠道和zone name获取对应的cep group信息，并基于zone name分组
	cepGroupList, err := s.productZoneDao.ListAllCepGroupByProductIdUsingCache(ctx, productId)
	if err != nil {
		return "", "", nil, err
	}
	cepGroupMap := make(map[string][]*product_serviceable_zone.LogisticProductServiceableCepGroupTab)
	for _, cepGroup := range cepGroupList {
		cepGroupMap[cepGroup.ZoneName] = append(cepGroupMap[cepGroup.ZoneName], cepGroup)
	}

	// 3. 匹配卖家地址所属的zone（cep group）并返回所有的destination zone
	var (
		metroRegion         string                      // 卖家地址所属的大都市区
		originZoneName      string                      // 卖家地址所属的zone
		destinationZoneMap  = make(map[string][]string) // 大都市区下的zone列表
		destinationZoneList []string                    // 卖家地址可选开启的zone列表
	)
	for _, zone := range productZoneList {
		cepGroup, ok := cepGroupMap[zone.ZoneName]
		if !ok {
			// 没有上传cep group的zone，不对卖家返回
			continue
		}

		if originZoneName == "" {
			// 没有匹配到卖家地址所属zone，则需要根据cep group进行匹配
			for _, cepRange := range cepGroup {
				if cep >= cepRange.CepInitial && cep <= cepRange.CepFinal {
					metroRegion = zone.MetroRegion
					originZoneName = zone.ZoneName
					break
				}
			}
		}

		// 将zone按照大都市区进行分组
		destinationZoneMap[zone.MetroRegion] = append(destinationZoneMap[zone.MetroRegion], zone.ZoneName)
	}
	if originZoneName == "" {
		// 无法匹配到卖家所属的zone
		return "", "", nil, nil
	}
	// 仅返回卖家所属metro region下的zone
	destinationZoneList = destinationZoneMap[metroRegion]

	return metroRegion, originZoneName, destinationZoneList, nil
}

func (s *shopServiceableZoneService) GetShopEverywhereSetting(ctx utils.LCOSContext, shopId, productId int) (int64, []string, *lcos_error.LCOSError) {
	var (
		radius             = shop_serviceable_constant.DDEModeDefaultRadius // 默认15km
		buyerCityBlacklist []string
	)

	// 从shop基础配置获取服务范围半径。配置可能不存在，不存在则返回默认配置15km
	conf, err := s.shopConfDao.GetShopServiceableAreaBasicConfByShopAndProduct(ctx, shopId, productId)
	if err != nil {
		return 0, nil, err
	}
	if conf != nil {
		radius = conf.Radius
	}

	// 获取shop的买家黑名单城市列表
	blacklist, err := s.buyerBlacklistDao.ListShopBuyerAddressBlacklistByShopAndProduct(ctx, shopId, productId)
	if err != nil {
		return 0, nil, err
	}
	buyerCityBlacklist = make([]string, 0, len(blacklist))
	for _, addr := range blacklist {
		buyerCityBlacklist = append(buyerCityBlacklist, addr.LocationName)
	}
	return radius, buyerCityBlacklist, nil
}

func (s *shopServiceableZoneService) CheckProductCepRangeBlacklist(ctx utils.LCOSContext, productId, cep int) *lcos_error.LCOSError {
	if config.GetMutableConf(ctx).DirectDeliveryConfig.DDEDowngrade {
		// DDE降级，所有地址均不可使用DDE模式
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "DDE downgraded")
	}

	// 判断地址是否在DDE黑名单中
	blacklist, err := s.cepBlacklistDao.ListLogisticProductCepBlacklistByParams(ctx, map[string]interface{}{"product_id": productId, "cep_initial <=": cep})
	if err != nil {
		return err
	}
	for _, cepRange := range blacklist {
		if cep >= cepRange.CepInitial && cep <= cepRange.CepFinal {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "seller address unsupported")
		}
	}
	return nil
}
