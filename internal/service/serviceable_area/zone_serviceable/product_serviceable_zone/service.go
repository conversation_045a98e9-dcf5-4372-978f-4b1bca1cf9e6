package product_serviceable_zone

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/shop_serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/zone_serviceable_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	jsoniter "github.com/json-iterator/go"
	"io/ioutil"
	"sort"
)

type ProductServiceableZoneService interface {
	ImportProductServiceableZone(ctx utils.LCOSContext, productId int, metroRegion, fileUrl string) *lcos_error.LCOSError

	// ListProductServiceableZoneWithPaging 支持分页筛选DD支持的所有zone数据
	ListProductServiceableZoneWithPaging(ctx utils.LCOSContext, req *zone_serviceable_protocol.ListProductServiceableZoneWithPagingRequest) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, uint32, *lcos_error.LCOSError)
	// ListProductServiceableZone 筛选DD支持的所有zone数据
	ListProductServiceableZone(ctx utils.LCOSContext, productId int, stateName, cityName string) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError)
	// ListProductServiceableZoneWithCepGroup 筛选DD支持并且上传过cep group的所有zone数据
	ListProductServiceableZoneWithCepGroup(ctx utils.LCOSContext, productId int, stateName, cityName string) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError)

	ImportProductServiceableCepGroup(ctx utils.LCOSContext, fileUrl string) *lcos_error.LCOSError
	ListProductServiceableCepGroup(ctx utils.LCOSContext, productId int) ([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab, *lcos_error.LCOSError)
}

func NewProductServiceableZoneService(dao product_serviceable_zone.ProductServiceableZoneDao) *productServiceableZoneService {
	return &productServiceableZoneService{
		dao: dao,
	}
}

type productServiceableZoneService struct {
	dao product_serviceable_zone.ProductServiceableZoneDao
}

func (p *productServiceableZoneService) ImportProductServiceableZone(ctx utils.LCOSContext, productId int, metroRegion, fileUrl string) *lcos_error.LCOSError {
	if metroRegion == shop_serviceable_constant.DDEModeMetroRegion {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "import zone metro region could not be %s", shop_serviceable_constant.DDEModeMetroRegion)
	}

	filePath, lcosErr := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if lcosErr != nil {
		return lcosErr
	}
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "read file error, %s", err.Error())
	}

	var metroRegionList []CityServiceableZone
	if err = jsoniter.Unmarshal(data, &metroRegionList); err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "read file error, %s", err.Error())
	}
	var zoneList []*product_serviceable_zone.LogisticProductServiceableZoneTab
	for _, regionInfo := range metroRegionList {
		if regionInfo.Name != metroRegion {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "city name not match|expected_region=%s, actual_region=%s", metroRegion, regionInfo.Name)
		}

		for _, zone := range regionInfo.Zones {
			if zone.Name == shop_serviceable_constant.DDEModeZoneName {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "import zone name cound not be %s", shop_serviceable_constant.DDEModeZoneName)
			}

			zoneList = append(zoneList, &product_serviceable_zone.LogisticProductServiceableZoneTab{
				ProductId:      productId,
				MetroRegion:    regionInfo.Name,
				ZoneName:       zone.Name,
				Polygon:        zone.Polygon,
				Neighborhoods:  zone.Neighborhoods,
				StateName:      zone.StateName,
				ContainsCities: zone.ContainsCities,
			})
		}
	}

	return ctx.Transaction(func() *lcos_error.LCOSError {
		if lcosErr = p.dao.DeleteLogisticProductServiceableZone(ctx, productId, metroRegion); lcosErr != nil {
			return lcosErr
		}
		if lcosErr = p.dao.BatchCreateLogisticProductServiceableZone(ctx, zoneList); lcosErr != nil {
			return lcosErr
		}
		return nil
	})
}

func (p *productServiceableZoneService) ListProductServiceableZoneWithPaging(ctx utils.LCOSContext, req *zone_serviceable_protocol.ListProductServiceableZoneWithPagingRequest) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, uint32, *lcos_error.LCOSError) {
	params, pErr := utils.Struct2map(req)
	if pErr != nil {
		return nil, 0, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid params schema: %s", pErr.Error())
	}
	dataList, total, err := p.dao.ListLogisticProductServiceableZoneWithPaging(ctx, params, req.PageNo, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	return p.filterProductServiceableZoneByCity(ctx, dataList, req.CityName), total, nil
}

func (p *productServiceableZoneService) ListProductServiceableZone(ctx utils.LCOSContext, productId int, stateName, cityName string) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError) {
	zoneQueryMap := make(map[string]interface{})
	if productId != 0 {
		zoneQueryMap["product_id"] = productId
	}
	if stateName != "" {
		zoneQueryMap["state_name"] = stateName
	}
	zoneList, err := p.dao.ListLogisticProductServiceableZone(ctx, zoneQueryMap)
	if err != nil {
		return nil, err
	}
	return p.filterProductServiceableZoneByCity(ctx, zoneList, cityName), nil
}

func (p *productServiceableZoneService) ListProductServiceableZoneWithCepGroup(ctx utils.LCOSContext, productId int, stateName, cityName string) ([]*product_serviceable_zone.LogisticProductServiceableZoneTab, *lcos_error.LCOSError) {
	// 1. 筛选所有的zone
	productZoneList, err := p.ListProductServiceableZone(ctx, productId, stateName, cityName)
	if err != nil {
		return nil, err
	}

	// 2. 获取所有上传过cep group的zone列表
	cepGroupList, err := p.ListProductServiceableCepGroup(ctx, productId)
	if err != nil {
		return nil, err
	}
	cepGroupMap := make(map[string]struct{})
	for _, cepGroup := range cepGroupList {
		cepGroupMap[cepGroup.ZoneName] = struct{}{}
	}

	// 3. 筛选并只返回上传了cep group的zone列表
	productZoneWithCepGroupList := make([]*product_serviceable_zone.LogisticProductServiceableZoneTab, 0, len(productZoneList))
	for _, zone := range productZoneList {
		if _, ok := cepGroupMap[zone.ZoneName]; ok {
			productZoneWithCepGroupList = append(productZoneWithCepGroupList, zone)
		}
	}
	return productZoneWithCepGroupList, nil
}

func (p *productServiceableZoneService) ImportProductServiceableCepGroup(ctx utils.LCOSContext, fileUrl string) *lcos_error.LCOSError {
	header := serviceable_util.ProductServiceableCepGroupHeader

	filePath, lcosErr := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if lcosErr != nil {
		return lcosErr
	}

	// Excel文件数据解析
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "read file error, %s", err.Error())
	}
	rows, err := file.GetRows(file.GetSheetName(0))
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "read file sheet error, %s", err.Error())
	}
	var dataList []*product_serviceable_zone.LogisticProductServiceableCepGroupTab
	var productList []int
	productMap := make(map[int]struct{})
	rowDataMap := make(map[int][]serviceable_util.ProductServiceableCepGroupRowdata)
	for i, row := range rows {
		rowId := i + 1
		if rowId == 1 {
			continue
		}
		if excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue
		}

		var rowData serviceable_util.ProductServiceableCepGroupRowdata
		if lcosErr = excel.ParseRowDataWithHeader(rowId, row, header, &rowData); lcosErr != nil {
			return lcosErr
		}
		if rowData.CepInitial > rowData.CepFinal {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Initial is larger than CEP Final | row=%d", rowData.RowId)
		}

		rowDataMap[rowData.ChannelId] = append(rowDataMap[rowData.ChannelId], rowData)
		model := &product_serviceable_zone.LogisticProductServiceableCepGroupTab{
			ProductId:  rowData.ChannelId,
			ZoneName:   rowData.GroupName,
			CepInitial: rowData.CepInitial,
			CepFinal:   rowData.CepFinal,
		}
		dataList = append(dataList, model)
		if _, ok := productMap[model.ProductId]; !ok {
			productList = append(productList, model.ProductId)
			productMap[model.ProductId] = struct{}{}
		}
	}

	// CEP Group合法性校验
	zoneList, lcosErr := p.dao.ListLogisticProductServiceableZone(ctx, map[string]interface{}{"product_id in": productList})
	if lcosErr != nil {
		return lcosErr
	}
	productZoneMap := make(map[int]map[string]struct{})
	for _, zone := range zoneList {
		zoneMap, ok := productZoneMap[zone.ProductId]
		if !ok {
			zoneMap = make(map[string]struct{})
		}
		zoneMap[zone.ZoneName] = struct{}{}
		productZoneMap[zone.ProductId] = zoneMap
	}
	for _, rowDataList := range rowDataMap {
		for _, rowData := range rowDataList {
			zoneMap, ok := productZoneMap[rowData.ChannelId]
			if !ok {
				return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Group '%s' is invalid | row=%d", rowData.GroupName, rowData.RowId)
			}
			_, ok = zoneMap[rowData.GroupName]
			if !ok {
				return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Group '%s' is invalid | row=%d", rowData.GroupName, rowData.RowId)
			}
		}
	}

	// CEP Range Overlap Check
	for _, rowDataList := range rowDataMap {
		// 排序前，不重叠的情况只有[i, i] [j, j]和[j, j] [i, i]
		sort.SliceStable(rowDataList, func(i, j int) bool {
			return rowDataList[i].CepInitial < rowDataList[j].CepInitial
		})
		// 排序后，由于i.Left < j.Left，所以不重叠的情况只剩[i, i] [j, j]，因此所有i.Right >= j.Left的均有重叠
		for i := 0; i < len(rowDataList)-1; i++ {
			if rowDataList[i].CepFinal >= rowDataList[i+1].CepInitial {
				msg := fmt.Sprintf("CEP range has overlap between row %d and row %d.", rowDataList[i].RowId, rowDataList[i+1].RowId)
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, msg)
			}
		}
	}

	return ctx.Transaction(func() *lcos_error.LCOSError {
		if lcosErr = p.dao.DeleteLogisticProductServiceableCepGroup(ctx, map[string]interface{}{"product_id in": productList}); lcosErr != nil {
			return lcosErr
		}
		if lcosErr = p.dao.BatchCreateLogisticProductServiceableCepGroup(ctx, dataList); lcosErr != nil {
			return lcosErr
		}
		return nil
	})
}

func (p *productServiceableZoneService) ListProductServiceableCepGroup(ctx utils.LCOSContext, productId int) ([]*product_serviceable_zone.LogisticProductServiceableCepGroupTab, *lcos_error.LCOSError) {
	queryMap := make(map[string]interface{})
	if productId != 0 {
		queryMap["product_id"] = productId
	}
	return p.dao.ListLogisticProductServiceableCepGroup(ctx, queryMap)
}

func (p *productServiceableZoneService) filterProductServiceableZoneByCity(ctx utils.LCOSContext, zoneList []*product_serviceable_zone.LogisticProductServiceableZoneTab, cityName string) []*product_serviceable_zone.LogisticProductServiceableZoneTab {
	if cityName == "" {
		return zoneList
	}

	ret := make([]*product_serviceable_zone.LogisticProductServiceableZoneTab, 0, len(zoneList))
	for _, zone := range zoneList {
		if utils.CheckInString(cityName, zone.ContainsCities) {
			ret = append(ret, zone)
		}
	}
	return ret
}
