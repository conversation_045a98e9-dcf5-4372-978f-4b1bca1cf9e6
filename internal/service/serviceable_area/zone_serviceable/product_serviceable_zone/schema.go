package product_serviceable_zone

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"

type ServiceableZone struct {
	Name           string                                     `json:"name"`
	Polygon        product_serviceable_zone.GeoPolygon        `json:"polygon"`
	Neighborhoods  product_serviceable_zone.ZoneNeighborhoods `json:"neighborhoods"`
	StateName      string                                     `json:"state_name"`
	ContainsCities []string                                   `json:"contains_cities"`
}

type CityServiceableZone struct {
	Name  string            `json:"name"`
	Zones []ServiceableZone `json:"zones"`
}
