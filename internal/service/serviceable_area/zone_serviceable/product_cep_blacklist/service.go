package product_cep_blacklist

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_cep_blacklist_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/tealeg/xlsx"
	"sort"
)

type LogisticProductCepBlacklistService interface {
	ImportLogisticProductCepBlacklist(ctx utils.LCOSContext, fileUrl string) *lcos_error.LCOSError
	ExportLogisticProductCepBlacklist(ctx utils.LCOSContext, productId int) (*xlsx.File, *lcos_error.LCOSError)
}

type logisticProductCepBlacklistServiceImpl struct {
	repo product_cep_blacklist_repo.LogisticProductCepBlacklistRepo
}

func NewLogisticProductCepBlacklistService(repo product_cep_blacklist_repo.LogisticProductCepBlacklistRepo) *logisticProductCepBlacklistServiceImpl {
	return &logisticProductCepBlacklistServiceImpl{
		repo: repo,
	}
}

func (l *logisticProductCepBlacklistServiceImpl) ImportLogisticProductCepBlacklist(ctx utils.LCOSContext, fileUrl string) *lcos_error.LCOSError {
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return err
	}
	file, fErr := excelize.OpenFile(filePath)
	if fErr != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "open excel file error: %s", fErr.Error())
	}
	rows, fErr := file.GetRows(file.GetSheetName(0))
	if fErr != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "open excel file error: %s", fErr.Error())
	}

	var (
		header               = CepBlacklistImportFileHeader
		dataList             []*product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab                 // 待新增或更新数据列表
		dataListByProductMap = make(map[int][]*product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab) // 用于按渠道分组做overlap校验
	)
	for i, row := range rows {
		rowId := i + 1
		if rowId == 1 {
			continue // 跳过行首
		}
		if excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue // 跳过空行
		}

		data := new(product_cep_blacklist_repo.LogisticProductCepRangeBlacklistTab)
		if err = excel.ParseRowWithHeader(rowId, row, header, &data); err != nil {
			return err
		}
		if data.CepInitial > data.CepFinal {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "CEP Initial is larger than CEP Final | row=%d", rowId)
		}

		dataList = append(dataList, data)
		dataListByProductMap[data.ProductId] = append(dataListByProductMap[data.ProductId], data)
	}

	for productId, productDataList := range dataListByProductMap {
		// 排序前，不重叠的情况只有[i, i] [j, j]和[j, j] [i, i]
		sort.SliceStable(productDataList, func(i, j int) bool {
			return productDataList[i].CepInitial < productDataList[j].CepInitial
		})
		// 排序后，由于i.Left < j.Left，所以不重叠的情况只剩[i, i] [j, j]，因此所有i.Right >= j.Left的均有重叠
		for i := 0; i < len(productDataList)-1; i++ {
			left, right := productDataList[i], productDataList[i+1]
			if left.CepFinal >= right.CepInitial {
				msg := fmt.Sprintf("Product %d CEP range [%d, %d] overlapped with [%d, %d].", productId, left.CepInitial, left.CepFinal, right.CepInitial, right.CepFinal)
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, msg)
			}
		}
	}

	return ctx.Transaction(func() *lcos_error.LCOSError {
		// 全量更新，先将涉及的渠道数据删除，然后新增
		for productId := range dataListByProductMap {
			if err = l.repo.DeleteLogisticProductCepBlacklistByProduct(ctx, productId); err != nil {
				return err
			}
		}
		return l.repo.BatchCreateLogisticProductCepBlacklist(ctx, dataList)
	})
}

func (l *logisticProductCepBlacklistServiceImpl) ExportLogisticProductCepBlacklist(ctx utils.LCOSContext, productId int) (*xlsx.File, *lcos_error.LCOSError) {
	params := make(map[string]interface{})
	if productId != 0 {
		params["product_id"] = productId
	}
	dataList, err := l.repo.ListLogisticProductCepBlacklistByParams(ctx, params)
	if err != nil {
		return nil, err
	}
	rawDataList := make([]interface{}, 0, len(dataList))
	for _, data := range dataList {
		rawDataList = append(rawDataList, data)
	}

	file := xlsx.NewFile()
	if fErr := excel.WriteTitleAndStruct(file, "Sheet 1", CepBlacklistExportFileTitles, rawDataList); fErr != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "write excel file error: %s", fErr.Error())
	}
	return file, nil
}
