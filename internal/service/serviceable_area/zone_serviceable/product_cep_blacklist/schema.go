package product_cep_blacklist

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"

var CepBlacklistExportFileTitles = []string{"Channel ID", "CEP Initial", "CEP Final"}

var CepBlacklistImportFileHeader = []excel.ParseableField{
	{
		Name:       "product_id",
		Required:   true,
		ParseValue: excel.ParseInt,
	},
	{
		Name:       "cep_initial",
		Required:   true,
		ParseValue: excel.ParseInt,
	},
	{
		Name:       "cep_final",
		Required:   true,
		ParseValue: excel.ParseInt,
	},
}
