package predefined_route_serviceable

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"strings"
)

// ExcelRawPredefinedRouteData Excel数据模型。excel为导出字段，json为导入字段
type ExcelRawPredefinedRouteData struct {
	LineId     string `excel:"title:Line ID" json:"Line ID"`
	Region     string `excel:"title:Region" json:"-"`
	GroupId    string `excel:"title:Group ID" json:"Group ID"`
	FromArea   string `excel:"title:From Area" json:"From Area"`
	ToArea     string `excel:"title:To Area" json:"To Area"`
	RouteCode  string `excel:"title:Route Code" json:"Zone/Route Code"`
	ActionCode int8   `excel:"-" json:"Action Code"`
}

// PredefinedRouteExportFileTitles Predefined Route导出文件表头
var PredefinedRouteExportFileTitles = []string{"Line ID", "Region", "Group ID", "From Area", "To Area", "Route Code"}

// PredefinedRouteImportFileHeader Predefined Route导入文件表头
var PredefinedRouteImportFileHeader = []excel.ParseableField{
	{
		Name:       "Line ID",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Group ID",
		Required:   true,
		ParseValue: serviceable_util.ParseGroupId,
	},
	{
		Name:       "Zone/Route Code",
		Required:   true,
		ParseValue: ParseRouteCode,
	},
	{
		Name:       "From Area",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "To Area",
		Required:   true,
		ParseValue: nil,
	},
	{
		Name:       "Action Code",
		Required:   true,
		ParseValue: serviceable_util.ParseActionCode,
	},
}

func ValidateRouteCode(code string) *lcos_error.LCOSError {
	if len(code) > 32 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "The maximum character length of the Zone/Route Code is 32")
	}
	if strings.ContainsRune(code, ' ') {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "Zone/Route Code cannot have spaces")
	}
	if code == serviceable_constant.PredefinedRouteAll {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "Predefined route code can not be 'All'")
	}
	return nil
}

func ParseRouteCode(value string) (interface{}, *lcos_error.LCOSError) {
	if err := ValidateRouteCode(value); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Msg)
	}
	return value, nil
}
