package predefined_route_serviceable

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/predefined_route_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/predefined_route_model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/tealeg/xlsx"
	"strings"
)

type LogisticLinePredefinedRouteService interface {
	CreateLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.CreateLinePredefinedRouteReq) *lcos_error.LCOSError
	UpdateLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.UpdateLinePredefinedRouteReq) *lcos_error.LCOSError
	DeleteLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.DeleteLinePredefinedRouteReq) *lcos_error.LCOSError
	ListLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.ListLinePredefinedRouteReq) ([]*predefined_route_model.LogisticLinePredefinedRouteTab, uint32, *lcos_error.LCOSError)
	UploadLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *common_protocol.UploadFileRequest) *lcos_error.LCOSError
	ExportLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.ExportLinePredefinedRouteReq) (*xlsx.File, *lcos_error.LCOSError)
	ListAllLogisticLinePredefinedRouteCode(ctx utils.LCOSContext) ([]string, *lcos_error.LCOSError)
}

func NewLogisticLinePredefinedRouteService(predefinedRouteDao predefined_route_model.LogisticLinePredefinedRouteDao, areaDao area.LogisticLineServiceableAreaTabDAO) *logisticLinePredefinedRouteServiceImpl {
	return &logisticLinePredefinedRouteServiceImpl{
		predefinedRouteDao: predefinedRouteDao,
		areaDao:            areaDao,
	}
}

type logisticLinePredefinedRouteServiceImpl struct {
	predefinedRouteDao predefined_route_model.LogisticLinePredefinedRouteDao
	areaDao            area.LogisticLineServiceableAreaTabDAO
}

func (l *logisticLinePredefinedRouteServiceImpl) CreateLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.CreateLinePredefinedRouteReq) *lcos_error.LCOSError {
	var (
		region    = strings.ToUpper(ctx.GetCountry())
		routeCode = strings.TrimSpace(req.RouteCode)
	)

	// 1. 检查线是否存在
	if err := l.batchCheckLineExists(ctx, region, []string{req.LineId}); err != nil {
		return err
	}

	// 2. 检查group id是否合法
	if _, ok := constant.GroupIDToGroupIDDetailMap[req.GroupId]; !ok {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid group id: %s", req.GroupId)
	}

	// 3. 校验area是否存在
	fromArea, err := l.areaDao.GetLogisticLineServiceableAreaTabById(ctx, req.FromAreaId)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "check from area failed: %s", err.Msg)
	}
	toArea, err := l.areaDao.GetLogisticLineServiceableAreaTabById(ctx, req.ToAreaId)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "check to area failed: %s", err.Msg)
	}

	// 4. 检查route code合法性
	if err = ValidateRouteCode(routeCode); err != nil {
		return err
	}

	// 5. 检查新建配置是否与存量配置重叠并落库
	route := &predefined_route_model.LogisticLinePredefinedRouteTab{
		Region:       region,
		LineId:       req.LineId,
		GroupId:      req.GroupId,
		FromAreaId:   fromArea.ID,
		FromAreaName: fromArea.AreaName, // area只能增删，不允许修改area name
		ToAreaId:     toArea.ID,
		ToAreaName:   toArea.AreaName, // area只能增删，不允许修改area name
		RouteCode:    routeCode,
	}
	if err = l.checkSingleRouteDuplicate(ctx, route); err != nil {
		return err
	}
	return l.predefinedRouteDao.BatchCreateOrUpdateLogisticLinePredefinedRoute(ctx, []*predefined_route_model.LogisticLinePredefinedRouteTab{route})
}

func (l *logisticLinePredefinedRouteServiceImpl) UpdateLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.UpdateLinePredefinedRouteReq) *lcos_error.LCOSError {
	var routeCode = strings.TrimSpace(req.RouteCode)

	// 1. 检查配置是否存在
	route, err := l.predefinedRouteDao.GetLogisticLinePredefinedRouteById(ctx, req.Id)
	if err != nil {
		return err
	}

	// 2. 检查area是否存在
	fromArea, err := l.areaDao.GetLogisticLineServiceableAreaTabById(ctx, req.FromAreaId)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "check from area failed: %s", err.Msg)
	}
	toArea, err := l.areaDao.GetLogisticLineServiceableAreaTabById(ctx, req.ToAreaId)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "check to area failed: %s", err.Msg)
	}

	// 3. 检查route code合法性
	if err = ValidateRouteCode(routeCode); err != nil {
		return err
	}

	// 4. 更新字段，检查新建配置是否与存量配置重叠并落库
	route.FromAreaId = fromArea.ID
	route.FromAreaName = fromArea.AreaName
	route.ToAreaId = toArea.ID
	route.ToAreaName = toArea.AreaName
	route.RouteCode = routeCode
	if err = l.checkSingleRouteDuplicate(ctx, route); err != nil {
		return err
	}
	return l.predefinedRouteDao.UpdateLogisticLinePredefinedRouteById(ctx, route)
}

func (l *logisticLinePredefinedRouteServiceImpl) DeleteLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.DeleteLinePredefinedRouteReq) *lcos_error.LCOSError {
	return l.predefinedRouteDao.DeleteLogisticLinePredefinedRouteById(ctx, req.Id)
}

func (l *logisticLinePredefinedRouteServiceImpl) ListLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.ListLinePredefinedRouteReq) ([]*predefined_route_model.LogisticLinePredefinedRouteTab, uint32, *lcos_error.LCOSError) {
	var region = strings.ToUpper(ctx.GetCountry())

	params, argsErr := utils.Struct2map(req)
	if argsErr != nil {
		return nil, 0, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "parse request to map error: %s", argsErr.Error())
	}
	params["region"] = region
	if req.RouteCode != "" {
		params["route_code like"] = "%" + req.RouteCode + "%" // route code支持模糊查询
	}

	return l.predefinedRouteDao.ListLogisticLinePredefinedRouteByParamsWithPaging(ctx, params, req.GetPageNo(), req.GetPageSize())
}

func (l *logisticLinePredefinedRouteServiceImpl) UploadLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *common_protocol.UploadFileRequest) *lcos_error.LCOSError {
	var (
		region = strings.ToUpper(req.Region)
		header = PredefinedRouteImportFileHeader

		dataMap      = make(map[int]*ExcelRawPredefinedRouteData) // rowId -> 行解析结果
		lineIdList   []string                                     // 上传的线列表
		lineIdMap    = make(map[string]struct{})                  // 用于线去重
		areaNameList []string                                     // 上传的area列表
		areaNameMap  = make(map[string]struct{})                  // 用于area去重

		createOrUpdateList []*predefined_route_model.LogisticLinePredefinedRouteTab // 新增或更新route列表
		deleteList         []*predefined_route_model.LogisticLinePredefinedRouteTab // 删除route列表
		uniqueMap          = make(map[string]int)                                   // route唯一键 -> rowId，用于文件内route重叠校验
	)

	// 1. 下载并解析excel文件，获取上传的route列表
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, req.FileUrl)
	if err != nil {
		return err
	}
	file, fErr := excelize.OpenFile(filePath)
	if fErr != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.FileIOErrorCode, "open excel file error: %s", fErr.Error())
	}
	rows, fErr := file.GetRows(file.GetSheetName(0))
	if fErr != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.FileIOErrorCode, "get excel rows error: %s", fErr.Error())
	}
	for i, row := range rows {
		rowId := i + 1
		if rowId == 1 {
			continue // 跳过表头
		}
		if excel.IsBlankRowInHeaderColumns(row, len(header)) {
			continue // 跳过空行
		}

		// 校验行格式并解析行数据
		data := new(ExcelRawPredefinedRouteData)
		if err = excel.ParseRowWithHeader(rowId, row, header, data); err != nil {
			return err
		}
		dataMap[rowId] = data

		if _, ok := lineIdMap[data.LineId]; !ok {
			lineIdList = append(lineIdList, data.LineId)
			lineIdMap[data.LineId] = struct{}{}
		}
		if _, ok := areaNameMap[data.FromArea]; !ok {
			areaNameList = append(areaNameList, data.FromArea)
			areaNameMap[data.FromArea] = struct{}{}
		}
		if _, ok := areaNameMap[data.ToArea]; !ok {
			areaNameList = append(areaNameList, data.ToArea)
			areaNameMap[data.ToArea] = struct{}{}
		}
	}

	// 2. 文件上传数据校验
	// 2.1 校验上传线是否存在
	if err = l.batchCheckLineExists(ctx, region, lineIdList); err != nil {
		return err
	}
	// 2.2 通过上传的area name获取对应的area信息，用于后续校验
	areaInfoList, err := l.areaDao.GetLogisticLineServiceableAreaTabByParams(ctx, map[string]interface{}{"area_name": areaNameList}) // area_name有唯一索引
	if err != nil {
		return err
	}
	areaInfoMap := make(map[string]*area.LogisticLineServiceableAreaTab)
	for _, areaInfo := range areaInfoList {
		areaInfoMap[areaInfo.AreaName] = areaInfo
	}
	// 2.3 逐行校验文件数据
	for rowId, rawData := range dataMap {
		// 2.3.1 校验文件上传数据新增部分是否存在重叠
		if rawData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			uniqueKey := utils.GenKey("-", rawData.LineId, rawData.GroupId, rawData.FromArea, rawData.ToArea)
			if duplicatedRowId, ok := uniqueMap[uniqueKey]; ok {
				return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "Import data duplicated with same Line-Group-FromArea-ToArea, please check | row %d duplicate with row %d, unique key=%s", duplicatedRowId, rowId, uniqueKey)
			}
			uniqueMap[uniqueKey] = rowId
		}

		// 2.3.2 校验from area和to area是否存在
		fromArea, ok := areaInfoMap[rawData.FromArea]
		if !ok {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "From area %s not found in row %d", rawData.FromArea, rowId)
		}
		toArea, ok := areaInfoMap[rawData.ToArea]
		if !ok {
			return lcos_error.NewLCOSErrorf(lcos_error.UploadFileErrorCode, "To area %s not found in row %d", rawData.ToArea, rowId)
		}

		// 2.3.3 生成route数据模型
		route := &predefined_route_model.LogisticLinePredefinedRouteTab{
			Region:       region,
			LineId:       rawData.LineId,
			GroupId:      rawData.GroupId,
			FromAreaId:   fromArea.ID,
			FromAreaName: fromArea.AreaName,
			ToAreaId:     toArea.ID,
			ToAreaName:   toArea.AreaName,
			RouteCode:    rawData.RouteCode,
		}
		if rawData.ActionCode == serviceable_constant.CreateOrUpdateActionCode {
			createOrUpdateList = append(createOrUpdateList, route)
		} else {
			deleteList = append(deleteList, route)
		}
	}

	// 3. 文件数据落库
	return ctx.Transaction(func() *lcos_error.LCOSError {
		if dbErr := l.predefinedRouteDao.BatchDeleteLogisticLinePredefinedRouteList(ctx, deleteList); dbErr != nil {
			return dbErr
		}
		if dbErr := l.predefinedRouteDao.BatchCreateOrUpdateLogisticLinePredefinedRoute(ctx, createOrUpdateList); dbErr != nil {
			return dbErr
		}
		return nil
	})
}

func (l *logisticLinePredefinedRouteServiceImpl) ExportLogisticLinePredefinedRoute(ctx utils.LCOSContext, req *predefined_route_protocol.ExportLinePredefinedRouteReq) (*xlsx.File, *lcos_error.LCOSError) {
	var region = strings.ToUpper(ctx.GetCountry())

	params, argsErr := utils.Struct2map(req)
	if argsErr != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "parse request to map error: %s", argsErr.Error())
	}
	params["region"] = region
	if req.RouteCode != "" {
		params["route_code like"] = "%" + req.RouteCode + "%"
	}

	routeList, err := l.predefinedRouteDao.ListLogisticLinePredefinedRouteByParams(ctx, params)
	if err != nil {
		return nil, err
	}
	dataList := make([]interface{}, 0, len(routeList))
	for _, route := range routeList {
		dataList = append(dataList, &ExcelRawPredefinedRouteData{
			LineId:    route.LineId,
			Region:    route.Region,
			GroupId:   route.GroupId,
			FromArea:  route.FromAreaName,
			ToArea:    route.ToAreaName,
			RouteCode: route.RouteCode,
		})
	}

	file := xlsx.NewFile()
	if fErr := excel.WriteTitleAndStruct(file, "Sheet1", PredefinedRouteExportFileTitles, dataList); fErr != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.FileIOErrorCode, "write title and struct error: %s", fErr.Error())
	}
	return file, nil
}

func (l *logisticLinePredefinedRouteServiceImpl) ListAllLogisticLinePredefinedRouteCode(ctx utils.LCOSContext) ([]string, *lcos_error.LCOSError) {
	var (
		region        = strings.ToUpper(ctx.GetCountry())
		routeCodes    []string
		routeCodesMap = make(map[string]struct{})
	)

	routeList, err := l.predefinedRouteDao.ListLogisticLinePredefinedRouteByParams(ctx, map[string]interface{}{"region": region})
	if err != nil {
		return nil, err
	}
	for _, route := range routeList {
		if _, ok := routeCodesMap[route.RouteCode]; !ok {
			routeCodes = append(routeCodes, route.RouteCode)
			routeCodesMap[route.RouteCode] = struct{}{}
		}
	}
	return routeCodes, nil
}

func (l *logisticLinePredefinedRouteServiceImpl) batchCheckLineExists(ctx utils.LCOSContext, region string, lineIdList []string) *lcos_error.LCOSError {
	lineExistsMap, err := lls_service.BatchCheckLineExists(ctx, region, lineIdList, lls_service.WithDraftLine(), lls_service.WithInstallLine())
	if err != nil {
		return err
	}

	var missingLineList []string
	for _, lineId := range lineIdList {
		if exists := lineExistsMap[lineId]; !exists {
			missingLineList = append(missingLineList, lineId)
		}
	}
	if len(missingLineList) > 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "line %s not exists", strings.Join(missingLineList, ","))
	}
	return nil
}

func (l *logisticLinePredefinedRouteServiceImpl) checkSingleRouteDuplicate(ctx utils.LCOSContext, route *predefined_route_model.LogisticLinePredefinedRouteTab) *lcos_error.LCOSError {
	params := map[string]interface{}{
		"region":       route.Region,
		"line_id":      route.LineId,
		"group_id":     route.GroupId,
		"from_area_id": route.FromAreaId,
		"to_area_id":   route.ToAreaId,
	}
	if route.Id != 0 {
		// 如果是更新，则需要排除自己
		params["id !="] = route.Id
	}

	existsList, err := l.predefinedRouteDao.ListLogisticLinePredefinedRouteByParams(ctx, params)
	if err != nil {
		return err
	}
	if len(existsList) > 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line predefined route already exists")
	}
	return nil
}
