package tpl_id_line_id_ref

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/channel_service"
)

type TPLIDLineIDRefInterface interface {
	QueryTPLIDLineIDRef(ctx utils.LCOSContext, lineID *string, tplID *uint32) ([]*tpl_id_line_id_ref.TPLLineIDRefTab, *lcos_error.LCOSError)
	BatchGet3plInfoList(ctx utils.LCOSContext, channelIDList []string) ([]*channel_service.ThreePLInfo, *lcos_error.LCOSError)
}

type tplIDLineIDRefService struct {
	tplIDLineIDRefDao tpl_id_line_id_ref.TPLIDLineIDRefDAO
}

func NewTplIDLineIDRefService(tplIDLineIDRefDao tpl_id_line_id_ref.TPLIDLineIDRefDAO) *tplIDLineIDRefService {
	return &tplIDLineIDRefService{
		tplIDLineIDRefDao: tplIDLineIDRefDao,
	}
}

func (service *tplIDLineIDRefService) QueryTPLIDLineIDRef(ctx utils.LCOSContext, lineID *string, tplID *uint32) ([]*tpl_id_line_id_ref.TPLLineIDRefTab, *lcos_error.LCOSError) {
	var returnedRefs []*tpl_id_line_id_ref.TPLLineIDRefTab
	if lineID != nil {
		refs, lcosErr := service.tplIDLineIDRefDao.GetRefByLineIDUsingCache(ctx, *lineID)
		if lcosErr != nil {
			return nil, lcosErr
		}
		// 传入了3pl id则检查是否匹配，没有传入则返回全部的refs
		if tplID != nil {
			for _, ref := range refs {
				if ref.ThreePLID == *tplID {
					returnedRefs = append(returnedRefs, ref)
				}
			}
		} else {
			returnedRefs = refs
		}
	} else if tplID != nil {
		refs, lcosErr := service.tplIDLineIDRefDao.GetRefBy3PLIDUsingCache(ctx, *tplID)
		if lcosErr != nil {
			return nil, lcosErr
		}
		returnedRefs = refs
	} else {
		// tpl id和line id都未传入，返回全部数据
		refs, lcosErr := service.tplIDLineIDRefDao.GetAllRefsUsingCache(ctx)
		if lcosErr != nil {
			return nil, lcosErr
		}
		returnedRefs = refs
	}
	return returnedRefs, nil
}

func (service *tplIDLineIDRefService) BatchGet3plInfoList(ctx utils.LCOSContext, channelIDList []string) ([]*channel_service.ThreePLInfo, *lcos_error.LCOSError) {
	region := ctx.GetCountry()
	results, lcosErr := channel_service.GetThreePLInfoList(ctx, region, channelIDList)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return results, nil
}
