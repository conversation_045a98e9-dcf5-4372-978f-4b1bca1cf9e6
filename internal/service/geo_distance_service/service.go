package geo_distance_service

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/geo_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/sls_location_sdk"
	"git.garena.com/shopee/bg-logistics/logistics/sls-location-plugin/geo_distance"
)

type LogisticProductGeoDistanceService interface {
	CreateOrUpdateProductGeoDistanceConf(ctx utils.LCOSContext, productId string, geoDistanceMode string, geoDistanceAvoid []string) *lcos_error.LCOSError
	DeleteProductGeoDistanceConf(ctx utils.LCOSContext, productId string) *lcos_error.LCOSError
	GetProductGeoDistanceConf(ctx utils.LCOSContext, productId string) (*geo_distance_repo.LogisticProductGeoDistanceConfTab, *lcos_error.LCOSError)

	ListAllGeoClient(ctx utils.LCOSContext) ([]*config.GeoClient, *lcos_error.LCOSError)
	ListAllProductGeoDistanceConf(ctx utils.LCOSContext) ([]*geo_distance_repo.LogisticProductGeoDistanceConfTab, *lcos_error.LCOSError)

	GetGeoDistance(ctx utils.LCOSContext, req *geo_distance.GetGeoDistanceRequest) (*geo_distance.GetGeoDistanceResponse, *lcos_error.LCOSError)
	BatchGetGeoDistance(ctx utils.LCOSContext, req *geo_distance.BatchGetGeoDistanceRequest) (*geo_distance.BatchGetGeoDistanceResponse, *lcos_error.LCOSError)
}

type logisticProductGeoDistanceServiceImpl struct {
	repo geo_distance_repo.LogisticProductGeoDistanceConfRepo
}

func NewLogisticProductGeoDistanceService(repo geo_distance_repo.LogisticProductGeoDistanceConfRepo) *logisticProductGeoDistanceServiceImpl {
	return &logisticProductGeoDistanceServiceImpl{
		repo: repo,
	}
}

func (l *logisticProductGeoDistanceServiceImpl) CreateOrUpdateProductGeoDistanceConf(ctx utils.LCOSContext, productId string, geoDistanceMode string, geoDistanceAvoid []string) *lcos_error.LCOSError {
	if geoDistanceMode == "" {
		// 若配置渠道有距离配置，则mode字段为必传。没传则不需要创建距离配置
		return nil
	}

	// GEO距离计算参数 mode 和 avoid 合法性校验
	if !utils.CheckInString(geoDistanceMode, []string{geo_distance.GeoDistanceModeCar, geo_distance.GeoDistanceModeBike}) {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid geo distance mode: %s", geoDistanceMode)
	}
	for _, avoid := range geoDistanceAvoid {
		if !utils.CheckInString(avoid, []string{geo_distance.GeoDistanceAvoidToll, geo_distance.GeoDistanceAvoidHighway, geo_distance.GeoDistanceAvoidFerry}) {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid geo distance avoid: %s", avoid)
		}
	}

	conf := &geo_distance_repo.LogisticProductGeoDistanceConfTab{
		ProductId:        productId,
		GeoDistanceMode:  geoDistanceMode,
		GeoDistanceAvoid: geoDistanceAvoid,
	}
	return l.repo.CreateOrUpdateProductGeoDistanceConfTab(ctx, conf)
}

func (l *logisticProductGeoDistanceServiceImpl) DeleteProductGeoDistanceConf(ctx utils.LCOSContext, productId string) *lcos_error.LCOSError {
	return l.repo.DeleteProductGeoDistanceConfTabByProductId(ctx, productId)
}

func (l *logisticProductGeoDistanceServiceImpl) GetProductGeoDistanceConf(ctx utils.LCOSContext, productId string) (*geo_distance_repo.LogisticProductGeoDistanceConfTab, *lcos_error.LCOSError) {
	return l.repo.GetProductGeoDistanceConfTabByProductId(ctx, productId)
}

func (l *logisticProductGeoDistanceServiceImpl) ListAllGeoClient(ctx utils.LCOSContext) ([]*config.GeoClient, *lcos_error.LCOSError) {
	clientMap := config.GetMutableConf(ctx).GeoConfig.ClientMap

	clientList := make([]*config.GeoClient, 0, len(clientMap))
	for _, client := range clientMap {
		clientList = append(clientList, client)
	}
	return clientList, nil
}

func (l *logisticProductGeoDistanceServiceImpl) ListAllProductGeoDistanceConf(ctx utils.LCOSContext) ([]*geo_distance_repo.LogisticProductGeoDistanceConfTab, *lcos_error.LCOSError) {
	return l.repo.ListAllProductGeoDistanceConfTab(ctx)
}

func (l *logisticProductGeoDistanceServiceImpl) GetGeoDistance(ctx utils.LCOSContext, req *geo_distance.GetGeoDistanceRequest) (*geo_distance.GetGeoDistanceResponse, *lcos_error.LCOSError) {
	if req == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "request is nil")
	}

	resp, err := sls_location_sdk.Client().GetGeoDistance(ctx, *req)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return resp, nil
}

func (l *logisticProductGeoDistanceServiceImpl) BatchGetGeoDistance(ctx utils.LCOSContext, req *geo_distance.BatchGetGeoDistanceRequest) (*geo_distance.BatchGetGeoDistanceResponse, *lcos_error.LCOSError) {
	if req == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "request is nil")
	}

	resp, err := sls_location_sdk.Client().BatchGetGeoDistance(ctx, *req)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return resp, nil
}
