package recurring_holidays

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	holidays2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/recurring_holidays"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/recurring_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	"strings"
)

type RecurringHolidaysServiceInterface interface {
	Update(ctx utils.LCOSContext, request *holidays2.UpdateRecurringHolidayRequest) ([]*recurring_holiday.RecurringHolidayTab, *lcos_error.LCOSError)
}

type RecurringHolidaysService struct {
	recurringHolidayDao recurring_holiday.RecurringHolidayDAO
	holidayDao          holiday.LogisticHolidayDAO
	pickupGroupDao      pickup_group.PickupGroupDAO
}

func NewRecurringHolidaysService(recurringHolidayDao recurring_holiday.RecurringHolidayDAO, holidayDao holiday.LogisticHolidayDAO, pickupGroupDao pickup_group.PickupGroupDAO) *RecurringHolidaysService {
	return &RecurringHolidaysService{
		recurringHolidayDao: recurringHolidayDao,
		holidayDao:          holidayDao,
		pickupGroupDao:      pickupGroupDao,
	}
}

func (l *RecurringHolidaysService) parseWeekends(ctx utils.LCOSContext, weekend *holidays2.Weekend, pickupGroupID string, destinationRegion string, stateNameToLocationIdMap map[string]int) ([]*recurring_holiday.RecurringHolidayTab, []string, []int, *lcos_error.LCOSError) {
	var postcodeList []string
	var stateLocationList []int
	var weekends []*recurring_holiday.RecurringHolidayTab
	switch weekend.WeekendType {
	case constant.Normal:
		for _, value := range weekend.Normal {
			// value必须大于0，小于8
			if value > 0 && value < 8 {
				weekends = append(weekends, &recurring_holiday.RecurringHolidayTab{
					PickupGroupID:     pickupGroupID,
					DestinationRegion: destinationRegion,
					StateId:           0,
					ZipCode:           "",
					StateName:         "",
					WeekDay:           value,
				})
			} else {
				return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.WeekdayNotValidError, "weekday is not valid")
			}
		}
	case constant.ByState:
		for stateName, weekdays := range weekend.ByState {
			if stateID, ok := stateNameToLocationIdMap[stateName]; ok {
				for _, weekday := range weekdays {
					if weekday > 0 && weekday < 8 {
						weekends = append(weekends, &recurring_holiday.RecurringHolidayTab{
							PickupGroupID:     pickupGroupID,
							DestinationRegion: destinationRegion,
							StateId:           int64(stateID),
							ZipCode:           "",
							StateName:         stateName,
							WeekDay:           weekday,
						})
					} else {
						return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.WeekdayNotValidError, "weekday is not valid")
					}
				}
				stateLocationList = append(stateLocationList, stateID)
			} else {
				logger.CtxLogErrorf(ctx, "state name:[%v] is not valid", stateName)
			}
		}
	case constant.ByZipcode:
		for zipcode, weekdays := range weekend.ByZipcode {
			if strings.ToLower(zipcode) == constant.DefaultZipcode {
				zipcode = constant.DefaultZipcode
			}
			for _, weekday := range weekdays {
				if weekday > 0 && weekday < 8 {
					weekends = append(weekends, &recurring_holiday.RecurringHolidayTab{
						PickupGroupID:     pickupGroupID,
						DestinationRegion: destinationRegion,
						StateId:           0,
						ZipCode:           zipcode,
						StateName:         "",
						WeekDay:           weekday,
					})
				} else {
					return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.WeekdayNotValidError, "weekday is not valid")
				}
			}
			postcodeList = append(postcodeList, zipcode)
		}
	}
	return weekends, postcodeList, stateLocationList, nil
}

// 查找新旧weekends的区别
func diffWeekends(oldWeekends, newWeekends []*recurring_holiday.RecurringHolidayTab) ([]*recurring_holiday.RecurringHolidayTab, []*recurring_holiday.RecurringHolidayTab) {
	var addedWeekends, deletedWeekends []*recurring_holiday.RecurringHolidayTab
	// 将新老weekend作为map
	oldWeekendsMap := map[string]*recurring_holiday.RecurringHolidayTab{}
	newWeekendsMap := map[string]*recurring_holiday.RecurringHolidayTab{}
	for _, weekend := range oldWeekends {
		key := fmt.Sprintf("%v:%v:%v:%v:%v:%v", weekend.PickupGroupID, weekend.DestinationRegion, weekend.StateId, weekend.ZipCode, weekend.StateName, weekend.WeekDay)
		oldWeekendsMap[key] = weekend
	}
	for _, weekend := range newWeekends {
		key := fmt.Sprintf("%v:%v:%v:%v:%v:%v", weekend.PickupGroupID, weekend.DestinationRegion, weekend.StateId, weekend.ZipCode, weekend.StateName, weekend.WeekDay)
		newWeekendsMap[key] = weekend
	}

	// 查找增加的weekends
	for key, value := range newWeekendsMap {
		if _, ok := oldWeekendsMap[key]; !ok {
			addedWeekends = append(addedWeekends, value)
		}
	}

	// 查找删除的weekends
	for key, value := range oldWeekendsMap {
		if _, ok := newWeekendsMap[key]; !ok {
			deletedWeekends = append(deletedWeekends, value)
		}
	}
	return addedWeekends, deletedWeekends
}

func (l *RecurringHolidaysService) Update(ctx utils.LCOSContext, request *holidays2.UpdateRecurringHolidayRequest) ([]*recurring_holiday.RecurringHolidayTab, *lcos_error.LCOSError) {
	// CB 场景下，需要前端指明 destination region
	destinationRegion := request.DestinationRegion
	if destinationRegion == "" {
		destinationRegion = strings.ToUpper(ctx.GetCountry())
	}

	// pickupGroup是否有效
	pickupGroup, err := l.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, request.PickupGroupID, destinationRegion, constant.LFS)
	if err != nil {
		return nil, err
	}
	if pickupGroup == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("no such pickup group|pickup_group_id=%s", request.PickupGroupID))
	}
	originRegion := pickupGroup.OriginRegion

	var stateNameToLocationIdMap = make(map[string]int)
	if request.WeekendsSettings.WeekendType == constant.ByState {
		// 请求地址服务，将地址的location信息缓存
		requestParams := &ops_service.GetLocationInfoBySubLevelRequest{
			Country:    strings.ToUpper(originRegion),
			LocationId: 0, //  取整个国家信息
			SubLevel:   1, //  取国家的省信息
		}
		//SPLN-28639去除locationgrpc开关
		stateLocationInfo, err := ops_service.GetLocationInfoBySubLevel(ctx, requestParams)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.LocationServerErrorCode, fmt.Sprintf("err requesting location service: %s", err.Msg))
		}
		for _, i := range stateLocationInfo {
			stateNameToLocationIdMap[i.Name] = i.LocationId
		}
	}

	weekends, _, _, lcosErr := l.parseWeekends(ctx, request.WeekendsSettings, pickupGroup.PickupGroupID, destinationRegion, stateNameToLocationIdMap)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 做diff操作，检查修改了的weekend
	oldWeekends, lcosErr := l.recurringHolidayDao.ListRecurringHolidayDayByParams(ctx, map[string]interface{}{"pickup_group_id": pickupGroup.PickupGroupID, "destination_region": pickupGroup.DestinationRegion})
	if lcosErr != nil {
		return nil, lcosErr
	}

	addedWeekends, deletedWeekends := diffWeekends(oldWeekends, weekends)

	// 先删除对应的了Pickup Group和region信息,需要开启事务
	fc := func() *lcos_error.LCOSError {
		for _, tmpWeekend := range deletedWeekends {
			lcosErr = l.recurringHolidayDao.DeleteRecurringHolidayDayByParams(ctx, map[string]interface{}{"pickup_group_id": tmpWeekend.PickupGroupID, "destination_region": tmpWeekend.DestinationRegion, "state_id": tmpWeekend.StateId, "zipcode": tmpWeekend.ZipCode, "weekday": tmpWeekend.WeekDay})
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "cannot delete weekend|pickup_group_id=%v, state_id=%v, zipcode=%v, weekday=%v", tmpWeekend.PickupGroupID, tmpWeekend.StateId, tmpWeekend.ZipCode, tmpWeekend.WeekDay)
				return lcosErr
			}
		}
		if len(addedWeekends) > 0 {
			_, err = l.recurringHolidayDao.BatchCreateRecurringHolidayDay(ctx, addedWeekends)
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.DBWriteErrorCode, err.Msg)
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return weekends, nil
}
