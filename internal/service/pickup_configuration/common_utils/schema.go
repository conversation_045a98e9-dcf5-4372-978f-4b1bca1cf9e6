package common_utils

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
)

type Volume struct {
	Date   string
	Volume uint32
}

type ArrangedPickupDaysRequest struct {
	ShipByDatetime     uint32
	LineIdList         []string
	ReleaseTime        uint32
	PayTime            uint32
	DaysToShip         int32
	StateLocationId    uint32
	Zipcode            string
	Volumes            []*Volume
	BuyerTimeslotID    uint32
	ClientGroupIDList  []string
	IsChangePickupDate bool
	IsB2C              bool
	Region             string
	Acl2Time           uint32
	IsRapidSla         bool
}

type OpenLogisticArrangedPickupDaysRequest struct {
	LineIdList       []string
	MerchantType     uint32
	AccountGroup     uint32
	PickupLocationID int64
	StartTime        uint32
}

type ReturnPickupDaysRequest struct {
	ShipByDays      uint32
	LineIdList      []string
	StateLocationId uint32
	Zipcode         string
}

type PickupSlot struct {
	Value            uint32 `json:"value"`
	SlotTime         string `json:"time"`
	StartTime        uint32 `json:"start_time"`
	EndTime          uint32 `json:"end_time"`
	SlotStartHour    uint32 `json:"slot_start_hour"`
	SlotCutoffHour   uint32 `json:"slot_cutoff_hour"`
	SlotCutoffMinute uint32 `json:"slot_cutoff_minute"`
}

type TimeSlot struct {
	Date        string        `json:"date"`
	Value       uint32        `json:"value"`
	PickupSlots []*PickupSlot `json:"slots"`
}

type PickupDay struct {
	Date        string `json:"date"`
	Value       uint32 `json:"value"`
	VolumeLimit int    `json:"volume_limit"`
}

type PickupDays struct {
	Days             []*PickupDay
	Timeslots        []*TimeSlot
	PickupGroupId    string
	PickupCutoffHour uint32
}

type OrderTime struct {
	ReleaseTime   uint32
	PayTime       uint32
	DaysToShip    int
	IsReturnOrder bool
}

type CheckSiteLinePickupTimeRequest struct {
	LineIdList        []string
	OriginRegion      string
	StateLocationId   int64
	Zipcode           string
	PickupTime        uint32
	ClientGroupIDList []string
}

type CheckOpenLogisitcPickupTimeRequest struct {
	LineIdList      []string
	OriginRegion    string
	StateLocationId int64
	PickupTime      uint32
	MerchantType    uint32
	AccountGroup    uint32
}

type GetSiteLinePickupTimeslotsRequest struct {
	LineIdList        []string
	PickupTimeRangeId *uint32
}

type GetValidPickupConfRequest struct {
	LineIdList []string
}

type GetSingleOpenLogisticPickupTimeslotRequest struct {
	LineIdList        []string
	PickupTimeRangeId uint32
	MerchantType      uint32
	AccountGroup      uint32
	StartTime         uint32
}

type GetSingleSiteLinePickupTimeslotsRequest struct {
	LineIdList        []string
	PickupTimeRangeId uint32
}

type GetHolidaysRequest struct {
	LineIdList      []string
	Days            int
	StateLocationID int64
	Zipcode         string
}

type TimeslotInfo struct {
	open_logistic_timeslot.OpenLogisticPickupTimeslotTab
	OriginRegion string // 用于兼容老的sls需求，传入origin region用于pickup region
}

type SingleTimeslotInfo struct {
	StartHour    uint32
	StartMinute  uint32
	StartSecond  uint32
	EndHour      uint32
	EndMinute    uint32
	EndSecond    uint32
	CutoffHour   uint32
	CutoffMinute uint32
	CutoffSecond uint32
}

type PickupConf struct {
	pickup_config.PickupConfTab
}

type PickupGroup struct {
	PickupGroupId     string
	PickupGroupName   string
	OriginRegion      string
	DestinationRegion string
	PickupType        uint32
	LineIdList        []string
	Ctime             uint32
	Mtime             uint32
}

type ProductPickupDays struct {
	// 如果返回0 表示响应成功lps直接把结果反馈给调用侧 ，如果返回1 表示需要执行lps逻辑
	Status                  int32
	PickupRecurringHolidays []uint32 // first mile的line id列表
	PickupHolidays          []string
}

type GetPickupHolidaysRequest struct {
	LineIdList      []string
	StateLocationID int64
	Zipcode         string
}
