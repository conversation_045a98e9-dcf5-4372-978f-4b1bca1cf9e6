package common_utils

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
)

type PickupWindowValidatorInterface interface {
	GeneralCheck(ctx utils.LCOSContext, lineIDs []string, pickupGroup *pickup_group.PickupGroupTab, lineServiceableConfMap map[string]*basic_conf.LineBasicServiceableConfTab) *lcos_error.LCOSError
}

type PickupWindowValidator struct {
	clientType uint32
}

func NewPickupWindowValidator(clientType uint32) *PickupWindowValidator {
	return &PickupWindowValidator{clientType: clientType}
}

func (p *PickupWindowValidator) GeneralCheck(ctx utils.LCOSContext, lineIDs []string, pickupGroup *pickup_group.PickupGroupTab, lineServiceableConfMap map[string]*basic_conf.LineBasicServiceableConfTab) *lcos_error.LCOSError {
	if len(lineIDs) < 1 {
		return lcos_error.NewLCOSError(lcos_error.LineNotValidErrorCode, "line id list cannot be empty")
	}

	// 检查pickup group是否具有pickup
	_, lcosErr := GetLinesAndCheckGroupCanPickup(ctx, lineIDs, pickupGroup.OriginRegion, pickupGroup.DestinationRegion, lineServiceableConfMap)
	if lcosErr != nil {
		return lcosErr
	}
	return nil
}
