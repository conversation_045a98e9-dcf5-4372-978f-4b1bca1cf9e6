package common_utils

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	lls_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"time"
)

// CheckLineCanPickupByLineInfo 检查line的类型是否满足要求，line是否为集成。跟line本身信息有关的pickup window逻辑都收拢到这里
func CheckLineCanPickupByLineInfo(lineInfo *lls_protobuf.GetLineInfoResponseData) *lcos_error.LCOSError {
	// 检查入参的line是否满足类型要求
	if !(lineInfo.GetLineType() == constant.C_FMAndFLAndLM || lineInfo.GetLineType() == constant.L_FMAndLM || lineInfo.GetLineType() == constant.L_FM || lineInfo.GetLineType() == constant.L_SelfBuild || lineInfo.GetLineType() == constant.C_OFM || lineInfo.GetLineType() == constant.B_FMAndLM) {
		return lcos_error.NewLCOSError(lcos_error.LineNotSupportPickupUp, fmt.Sprintf("line type has to be one of C_FM&FL&LM/L_FM&LM/L_FM/L_SelfBuild/C_OFM/B_FMAndLM|line_id=%s", lineInfo.GetLineId()))
	}
	// 检查line是否为集成
	if lineInfo.GetLineModel() != uint32(constant.Integration) {
		return lcos_error.NewLCOSError(lcos_error.LineNotSupportPickupUp, fmt.Sprintf("line[%s] is not integrated, and not support pick up", lineInfo.GetLineId()))
	}
	return nil
}

// CheckLineCanPickupByServiceAreaConf 跟LineBasicServiceableConfTab有关的pickup window逻辑，都收拢到这里
func CheckLineCanPickupByServiceAreaConf(serviceableConfTab *basic_conf.LineBasicServiceableConfTab) *lcos_error.LCOSError {
	if serviceableConfTab.CollectDeliverAbility&uint32(constant.PICKUP) == 0 {
		return lcos_error.NewLCOSError(lcos_error.LineNotSupportPickupUp, fmt.Sprintf("line not support pickup|line_id=%s", serviceableConfTab.LineId))
	}
	return nil
}

/*
检查line列表能否揽收，外部可以调用
*/
func CheckLinesCanPickupCommon(lineIds []string, lineMap map[string]*lls_protobuf.GetLineInfoResponseData, lineServiceableConfMap map[string]*basic_conf.LineBasicServiceableConfTab) *lcos_error.LCOSError {
	lineInfoMap := make(map[string]*lls_protobuf.GetLineInfoResponseData)
	// 检查是否所有的line都正确获取到了值
	for _, line := range lineIds {
		lineInfo, ok := lineMap[line]
		if !ok {
			return lcos_error.NewLCOSError(lcos_error.NotFoundLineErrorCode, fmt.Sprintf("cannot get such line|line_id=%s", line))
		}
		// 检查入参的line需要支持pickup，对于grpc和admin接口来说，访问的路径不同
		var serviceableConfTab *basic_conf.LineBasicServiceableConfTab
		if _, ok := lineServiceableConfMap[line]; !ok {
			return lcos_error.NewLCOSError(lcos_error.NotFoundLineServiceableBasicConfErrorCode, fmt.Sprintf("cannot find line_basic_serviceable_conf_tab|line_id=%s", line))
		}
		serviceableConfTab = lineServiceableConfMap[line]
		// 当前line不支持pickup
		if serviceableConfTab == nil {
			return lcos_error.NewLCOSError(lcos_error.LineNotSupportPickupUp, fmt.Sprintf("line not support pickup|line_id=%s", line))
		}
		if lcosErr := CheckLineCanPickupByServiceAreaConf(serviceableConfTab); lcosErr != nil {
			return lcosErr
		}
		lcosErr := CheckLineCanPickupByLineInfo(lineInfo)
		if lcosErr != nil {
			return lcosErr
		}
		lineInfoMap[line] = lineMap[line]
	}
	return nil
}

func CalculateCutoffTime(pickupDatetime time.Time, cutoffHour uint32, pickupGroup *pickup_group.PickupGroupTab) time.Time {
	var cutoffTime time.Time
	if pickupGroup.PickupType == constant.SAMEDAY {
		cutoffTime = time.Date(pickupDatetime.Year(), pickupDatetime.Month(), pickupDatetime.Day(), int(cutoffHour), 0, 0, 0, pickupDatetime.Location())
	} else {
		cutoffDate := pickup.AddDays(pickupDatetime, -1)
		cutoffTime = time.Date(cutoffDate.Year(), cutoffDate.Month(), cutoffDate.Day(), int(cutoffHour), 0, 0, 0, pickupDatetime.Location())
	}
	return cutoffTime
}

/*
通过lls获取line，并检查line列表中的每条线能否揽收
*/
func GetLinesAndCheckCanPickup(ctx utils.LCOSContext, lineIds []string, lineServiceableConfMap map[string]*basic_conf.LineBasicServiceableConfTab) (map[string]*lls_protobuf.GetLineInfoResponseData, *lcos_error.LCOSError) {
	// 获取line数据
	lineMap, err := lls_service.BatchGetLineInfosMap(ctx, lineIds)
	if err != nil {
		return nil, err
	}
	if lineMap == nil {
		lineMap = map[string]*lls_protobuf.GetLineInfoResponseData{}
	}
	lcosErr := CheckLinesCanPickupCommon(lineIds, lineMap, lineServiceableConfMap)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 检查信息是否可以正常获取
	return lineMap, nil
}

/*
用于build
通过lls获取line，并检查line列表中的每条线能否揽收
pickup 支持草稿态line数据， 所以从lls调用lineDraft数据进行拼接
*/
func GetLinesAndCheckCanPickupForBuild(ctx utils.LCOSContext, lineIds []string, lineServiceableConfMap map[string]*basic_conf.LineBasicServiceableConfTab) (map[string]*lls_protobuf.GetLineInfoResponseData, *lcos_error.LCOSError) {
	// 获取line数据
	lineMap, err := lls_service.BatchGetLineInfosMap(ctx, lineIds)
	if err != nil {
		return nil, err
	}
	if lineMap == nil {
		lineMap = map[string]*lls_protobuf.GetLineInfoResponseData{}
	}
	lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, lineIds)
	if len(lineDraftMap) > 0 {
		for lineId, lineDrafr := range lineDraftMap {
			lineMap[lineId] = lineDrafr
		}
	}
	lcosErr := CheckLinesCanPickupCommon(lineIds, lineMap, lineServiceableConfMap)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 检查信息是否可以正常获取
	return lineMap, nil
}

/*
获取对应的line信息，并校验传入的line id是否存在，且满足pickup，要求line必须属于C_FM&FL&LM/L_FM&LM/L_FM。origin可以不填。相关的 Line Info信息会以map形式返回，以供查询之用。
对于admin接口，需要请求数据库，对于grpc服务，需要使用缓存
*/
func GetLinesAndCheckGroupCanPickup(ctx utils.LCOSContext, lineIds []string, originRegion string, destinationRegion string, lineServiceableConfMap map[string]*basic_conf.LineBasicServiceableConfTab) (map[string]*lls_protobuf.GetLineInfoResponseData, *lcos_error.LCOSError) {
	lineInfoMap, lcosErr := GetLinesAndCheckCanPickup(ctx, lineIds, lineServiceableConfMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	// 检查每条line的起止国家正确
	for _, lineID := range lineIds {
		lineInfo := lineInfoMap[lineID]
		if *lineInfo.OriginRegion != originRegion {
			return nil, lcos_error.NewLCOSError(lcos_error.LineNotValidErrorCode, fmt.Sprintf("origin region is [%s] instead of [%s]|line_id=%s", *lineInfo.OriginRegion, originRegion, lineID))
		}
		if *lineInfo.DestinationRegion != destinationRegion {
			return nil, lcos_error.NewLCOSError(lcos_error.LineNotValidErrorCode, fmt.Sprintf("destination region is [%s] instead of [%s]|line_id=%s", *lineInfo.DestinationRegion, destinationRegion, lineID))
		}
	}
	return lineInfoMap, nil
}

/*
用于build
获取对应的line信息，并校验传入的line id是否存在，且满足pickup，要求line必须属于C_FM&FL&LM/L_FM&LM/L_FM。origin可以不填。相关的 Line Info信息会以map形式返回，以供查询之用。
对于admin接口，需要请求数据库，对于grpc服务，需要使用缓存
*/
func GetLinesAndCheckGroupCanPickupForBuild(ctx utils.LCOSContext, lineIds []string, originRegion string, destinationRegion string, lineServiceableConfMap map[string]*basic_conf.LineBasicServiceableConfTab) (map[string]*lls_protobuf.GetLineInfoResponseData, *lcos_error.LCOSError) {
	lineInfoMap, lcosErr := GetLinesAndCheckCanPickupForBuild(ctx, lineIds, lineServiceableConfMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	// 检查每条line的起止国家正确
	for _, lineID := range lineIds {
		lineInfo := lineInfoMap[lineID]
		if *lineInfo.OriginRegion != originRegion {
			return nil, lcos_error.NewLCOSError(lcos_error.LineNotValidErrorCode, fmt.Sprintf("origin region is [%s] instead of [%s]|line_id=%s", *lineInfo.OriginRegion, originRegion, lineID))
		}
		if *lineInfo.DestinationRegion != destinationRegion {
			return nil, lcos_error.NewLCOSError(lcos_error.LineNotValidErrorCode, fmt.Sprintf("destination region is [%s] instead of [%s]|line_id=%s", *lineInfo.DestinationRegion, destinationRegion, lineID))
		}
	}
	return lineInfoMap, nil
}

/*
确认当前的pickup group可以揽收。
@pickGroup: pickup group
@pickupGroupLineRefs: pickup group和line的关系表
@lineServiceableConfMap: line本身的服务范围配置信息
*/
func CheckPickupGroupCanPickup(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, lineIDList []string, lineServiceableConfMap map[string]*basic_conf.LineBasicServiceableConfTab) *lcos_error.LCOSError {
	if pickupGroup == nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "pickup group cannot be nil")
	}
	_, err := GetLinesAndCheckGroupCanPickupForBuild(ctx, lineIDList, pickupGroup.OriginRegion, pickupGroup.DestinationRegion, lineServiceableConfMap)
	if err != nil {
		return err
	}
	return nil
}
