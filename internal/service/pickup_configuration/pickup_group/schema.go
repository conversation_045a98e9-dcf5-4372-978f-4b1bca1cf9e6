package pickup_group

type (
	SingleLineInfo struct {
		LineID      string `json:"line_id"`
		LineName    string `json:"line_name"`
		LineSubType uint32 `json:"line_sub_type"`
	}

	PickupGroupResponse struct {
		ID                uint64            `json:"id"`
		PickupGroupID     string            `json:"pickup_group_id"`
		PickupGroupName   string            `json:"pickup_group_name"`
		OriginRegion      string            `json:"origin_region"`
		DestinationRegion string            `json:"destination_region"`
		PickupType        uint8             `json:"pickup_type"`
		LineIdList        []*SingleLineInfo `json:"line_id_list"`
		CTime             uint32            `json:"ctime"`
		MTime             uint32            `json:"mtime"`
	}

	AllPickupGroupResponse struct {
		PickupGroupID   string `json:"pickup_group_id"`
		PickupGroupName string `json:"pickup_group_name"`
	}

	ListPickupGroupResponse struct {
		PageNo uint32                 `json:"pageno"`
		Count  uint32                 `json:"count"`
		Total  uint32                 `json:"total"`
		List   []*PickupGroupResponse `json:"list"`
	}

	AllPickupLinesResponse struct {
		LineID      string `json:"line_id"`
		LineName    string `json:"line_name"`
		LineSubType uint32 `json:"line_sub_type"`
		CollectType uint32 `json:"collect_type"`
	}

	PickupGroupSearchResponse struct {
		ID                uint64   `json:"id"`
		PickupGroupID     string   `json:"pickup_group_id"`
		PickupGroupName   string   `json:"pickup_group_name"`
		OriginRegion      string   `json:"origin_region"`
		DestinationRegion string   `json:"destination_region"`
		PickupType        uint8    `json:"pickup_type"`
		LineIdList        []string `json:"line_id_list"`
		CTime             uint32   `json:"ctime"`
		MTime             uint32   `json:"mtime"`
	}
)
