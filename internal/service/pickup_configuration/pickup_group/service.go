package pickup_group

import (
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	pickup_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	lls_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
)

const clientType = constant.LFS

type PickupGroupServiceInterface interface {
	CreatePickupGroup(ctx utils.LCOSContext, request *pickup_group2.CreatePickupGroupRequest) (*PickupGroupResponse, *lcos_error.LCOSError)
	UpdatePickupGroup(ctx utils.LCOSContext, request *pickup_group2.UpdatePickupGroupRequest) (*PickupGroupResponse, *lcos_error.LCOSError)
	UpdatePickupGroupWithDraft(ctx utils.LCOSContext, request *pickup_group2.UpdatePickupGroupRequest) (*PickupGroupResponse, *lcos_error.LCOSError)
	DeletePickupGroup(ctx utils.LCOSContext, request *pickup_group2.DeletePickupGroupRequest) *lcos_error.LCOSError
	GetPickupGroupByGroupId(ctx utils.LCOSContext, request *pickup_group2.GetPickupGroupRequest) (*PickupGroupResponse, *lcos_error.LCOSError)
	ListPickupGroup(ctx utils.LCOSContext, request *pickup_group2.ListPickupGroupRequest) (*ListPickupGroupResponse, *lcos_error.LCOSError)
	ListAllPickupGroups(ctx utils.LCOSContext, request *pickup_group2.ListAllPickupGroupRequest) ([]*AllPickupGroupResponse, *lcos_error.LCOSError)
	ListAllPickupLines(ctx utils.LCOSContext, request *pickup_group2.ListAllPickupLinesRequest) ([]*AllPickupLinesResponse, *lcos_error.LCOSError)
	SearchPickupGroup(ctx utils.LCOSContext, request *pickup_group2.SearchPickupGroupRequest) ([]*PickupGroupSearchResponse, *lcos_error.LCOSError)
}

type PickupGroupService struct {
	pickupGroupDao     pickup_group.PickupGroupDAO
	serviceableConfDao basic_conf.LineBasicServiceableConfDAO
}

func NewPickupGroupService(pickupGroupDao pickup_group.PickupGroupDAO, serviceableConfDao basic_conf.LineBasicServiceableConfDAO) *PickupGroupService {
	return &PickupGroupService{
		pickupGroupDao:     pickupGroupDao,
		serviceableConfDao: serviceableConfDao,
	}
}

func (p *PickupGroupService) CreatePickupGroup(ctx utils.LCOSContext, request *pickup_group2.CreatePickupGroupRequest) (*PickupGroupResponse, *lcos_error.LCOSError) {
	// if len(request.LineIDList) < 1 {
	// 	return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line list cannot be empty")
	// }

	var llsErr *lcos_error.LCOSError
	var lineMap map[string]*lls_protobuf.GetLineInfoResponseData
	if len(request.LineIDList) > 0 {
		// 获取line的basic info map信息
		lineBasicConfMap, llsErr := p.serviceableConfDao.GetBasicServiceableConfModelMapByLineIds(ctx, request.LineIDList)
		if llsErr != nil {
			return nil, llsErr
		}

		// 校验入参的line id存在，且类型正确
		lineMap, llsErr = common_utils.GetLinesAndCheckGroupCanPickupForBuild(ctx, request.LineIDList, request.OriginRegion, request.DestinationRegion, lineBasicConfMap)
		if llsErr != nil {
			return nil, llsErr
		}
	}

	var returnedPickupGroup *pickup_group.PickupGroupTab
	// 开启事务，创建pickup group

	fc := func() *lcos_error.LCOSError {
		pickupGroup := &pickup_group.PickupGroupTab{
			PickupGroupID:     request.PickupGroupID,
			PickupGroupName:   request.PickupGroupName,
			OriginRegion:      request.OriginRegion,
			DestinationRegion: request.DestinationRegion,
			PickupType:        request.PickupType,
			ClientType:        clientType, // 点线的pickup window使用LFS
		}
		returnedPickupGroup, llsErr = p.pickupGroupDao.CreatePickupGroup(ctx, pickupGroup)
		if llsErr != nil {
			return llsErr
		}
		pickupGroupLineMapList := make([]*pickup_group.PickupGroupLineIDRefTab, len(request.LineIDList))
		for index, line := range request.LineIDList {
			pickupGroupLineMapList[index] = &pickup_group.PickupGroupLineIDRefTab{
				PickupGroupID:     request.PickupGroupID,
				DestinationRegion: request.DestinationRegion,
				LineID:            line,
				ClientType:        clientType,
			}
		}
		_, llsErr = p.pickupGroupDao.CreatePickupGroupLineIDRef(ctx, pickupGroupLineMapList)
		if llsErr != nil {
			return llsErr
		}
		return nil
	}
	if llsErr := ctx.Transaction(fc); llsErr != nil {
		return nil, llsErr
	}

	// 封装line信息
	lineIDList := make([]*SingleLineInfo, 0, len(request.LineIDList))
	for _, _lineID := range request.LineIDList {
		if _, ok := lineMap[_lineID]; ok {
			lineIDList = append(lineIDList, &SingleLineInfo{
				LineID:      _lineID,
				LineSubType: lineMap[_lineID].GetLineType(),
				LineName:    lineMap[_lineID].GetLineName(),
			})
		}
	}

	pickupGroup := &PickupGroupResponse{
		ID:                returnedPickupGroup.ID,
		PickupGroupID:     request.PickupGroupID,
		PickupGroupName:   request.PickupGroupName,
		OriginRegion:      request.OriginRegion,
		DestinationRegion: request.DestinationRegion,
		PickupType:        request.PickupType,
		LineIdList:        lineIDList,
		CTime:             returnedPickupGroup.CTime,
		MTime:             returnedPickupGroup.MTime,
	}
	return pickupGroup, nil
}

func (p *PickupGroupService) UpdatePickupGroup(ctx utils.LCOSContext, request *pickup_group2.UpdatePickupGroupRequest) (*PickupGroupResponse, *lcos_error.LCOSError) {
	lineIDList := request.LineIDList
	// if len(lineIDList) < 1 {
	// 	return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line list cannot be empty")
	// }

	// 检查传入的pickup_group_id能否获取到内容
	model, llsErr := p.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, request.PickupGroupID, request.DestinationRegion, clientType)
	if llsErr != nil {
		return nil, llsErr
	}

	var lineMap map[string]*lls_protobuf.GetLineInfoResponseData
	if len(lineIDList) > 0 {
		// 获取line basic conf信息
		lineServiceConfMap, llsErr := p.serviceableConfDao.GetBasicServiceableConfModelMapByLineIds(ctx, lineIDList)
		if llsErr != nil {
			return nil, llsErr
		}

		// 校验入参的line id存在，且类型正确
		lineMap, llsErr = common_utils.GetLinesAndCheckGroupCanPickupForBuild(ctx, lineIDList, request.OriginRegion, request.DestinationRegion, lineServiceConfMap)
		if llsErr != nil {
			return nil, llsErr
		}
	}
	var returnedPickupGroup *pickup_group.PickupGroupTab
	// 开启事务，更新pickup group

	fc := func() *lcos_error.LCOSError {
		pickupGroup := &pickup_group.PickupGroupTab{
			ID:                model.ID,
			PickupGroupID:     model.PickupGroupID,
			DestinationRegion: model.DestinationRegion,
			OriginRegion:      request.OriginRegion,
			PickupGroupName:   request.PickupGroupName,
			PickupType:        request.PickupType,
			ClientType:        clientType,
		}
		returnedPickupGroup, llsErr = p.pickupGroupDao.UpdatePickupGroup(ctx, pickupGroup)
		if llsErr != nil {
			return llsErr
		}
		pickupGroupLineMapList := make([]*pickup_group.PickupGroupLineIDRefTab, len(lineIDList))
		for index, line := range request.LineIDList {
			pickupGroupLineMapList[index] = &pickup_group.PickupGroupLineIDRefTab{
				PickupGroupID:     request.PickupGroupID,
				DestinationRegion: request.DestinationRegion,
				LineID:            line,
				ClientType:        clientType,
			}
		}
		// 先删除对应pickup group id的关系表
		llsErr = p.pickupGroupDao.DeletePickupGroupLineIDRefByPickupGroupIDAndDestinationRegionAndClientType(ctx, request.PickupGroupID, request.DestinationRegion, clientType)
		if llsErr != nil {
			return llsErr
		}
		_, llsErr = p.pickupGroupDao.CreatePickupGroupLineIDRef(ctx, pickupGroupLineMapList)
		if llsErr != nil {
			return llsErr
		}
		return nil
	}
	if llsErr := ctx.Transaction(fc); llsErr != nil {
		return nil, llsErr
	}

	// 封装line信息
	singleLineIDList := make([]*SingleLineInfo, 0, len(lineIDList))
	for _, _lineID := range lineIDList {
		if _, ok := lineMap[_lineID]; ok {
			singleLineIDList = append(singleLineIDList, &SingleLineInfo{
				LineID:      _lineID,
				LineSubType: lineMap[_lineID].GetLineType(),
				LineName:    lineMap[_lineID].GetLineName(),
			})
		}
	}

	pickupGroup := &PickupGroupResponse{
		ID:                returnedPickupGroup.ID,
		PickupGroupID:     returnedPickupGroup.PickupGroupID,
		PickupGroupName:   returnedPickupGroup.PickupGroupName,
		OriginRegion:      returnedPickupGroup.OriginRegion,
		DestinationRegion: returnedPickupGroup.DestinationRegion,
		PickupType:        returnedPickupGroup.PickupType,
		LineIdList:        singleLineIDList,
		CTime:             model.CTime,
		MTime:             returnedPickupGroup.MTime,
	}
	return pickupGroup, nil
}

// 该接口仅用于lls 草稿态line修改关联的pickupGroup时使用
func (p *PickupGroupService) UpdatePickupGroupWithDraft(ctx utils.LCOSContext, request *pickup_group2.UpdatePickupGroupRequest) (*PickupGroupResponse, *lcos_error.LCOSError) {
	lineIDList := request.LineIDList
	if len(lineIDList) < 1 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "line list cannot be empty")
	}

	// 检查传入的pickup_group_id能否获取到内容
	model, llsErr := p.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, request.PickupGroupID, request.DestinationRegion, clientType)
	if llsErr != nil {
		return nil, llsErr
	}

	// 获取line basic conf信息
	_, llsErr = p.serviceableConfDao.GetBasicServiceableConfModelMapByLineIds(ctx, lineIDList)
	if llsErr != nil {
		return nil, llsErr
	}

	var returnedPickupGroup *pickup_group.PickupGroupTab
	// 开启事务，更新pickup group

	fc := func() *lcos_error.LCOSError {
		pickupGroup := &pickup_group.PickupGroupTab{
			ID:                model.ID,
			PickupGroupID:     model.PickupGroupID,
			DestinationRegion: model.DestinationRegion,
			OriginRegion:      request.OriginRegion,
			PickupGroupName:   request.PickupGroupName,
			PickupType:        request.PickupType,
			ClientType:        clientType,
		}
		returnedPickupGroup, llsErr = p.pickupGroupDao.UpdatePickupGroup(ctx, pickupGroup)
		if llsErr != nil {
			return llsErr
		}
		pickupGroupLineMapList := make([]*pickup_group.PickupGroupLineIDRefTab, len(lineIDList))
		for index, line := range request.LineIDList {
			pickupGroupLineMapList[index] = &pickup_group.PickupGroupLineIDRefTab{
				PickupGroupID:     request.PickupGroupID,
				DestinationRegion: request.DestinationRegion,
				LineID:            line,
				ClientType:        clientType,
			}
		}
		// 先删除对应pickup group id的关系表
		llsErr = p.pickupGroupDao.DeletePickupGroupLineIDRefByPickupGroupIDAndDestinationRegionAndClientType(ctx, request.PickupGroupID, request.DestinationRegion, clientType)
		if llsErr != nil {
			return llsErr
		}
		_, llsErr = p.pickupGroupDao.CreatePickupGroupLineIDRef(ctx, pickupGroupLineMapList)
		if llsErr != nil {
			return llsErr
		}
		return nil
	}
	if llsErr := ctx.Transaction(fc); llsErr != nil {
		return nil, llsErr
	}

	// 封装line信息
	singleLineIDList := make([]*SingleLineInfo, 0, len(lineIDList))

	pickupGroup := &PickupGroupResponse{
		ID:                returnedPickupGroup.ID,
		PickupGroupID:     returnedPickupGroup.PickupGroupID,
		PickupGroupName:   returnedPickupGroup.PickupGroupName,
		OriginRegion:      returnedPickupGroup.OriginRegion,
		DestinationRegion: returnedPickupGroup.DestinationRegion,
		PickupType:        returnedPickupGroup.PickupType,
		LineIdList:        singleLineIDList,
		CTime:             model.CTime,
		MTime:             returnedPickupGroup.MTime,
	}
	return pickupGroup, nil
}

func (p *PickupGroupService) DeletePickupGroup(ctx utils.LCOSContext, request *pickup_group2.DeletePickupGroupRequest) *lcos_error.LCOSError {
	// 检查传入的pickup_group_id能否获取到内容
	model, llsErr := p.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, request.PickupGroupID, request.DestinationRegion, clientType)
	if llsErr != nil {
		return llsErr
	}
	if model == nil {
		return lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("cannot find pickup group|pickup_group_id=%s", request.PickupGroupID))
	}
	// 开启事务，删除pickup group和关系表
	fc := func() *lcos_error.LCOSError {
		err := p.pickupGroupDao.DeletePickupGroup(ctx, request.PickupGroupID, request.DestinationRegion, clientType)
		if err != nil {
			return err
		}
		err = p.pickupGroupDao.DeletePickupGroupLineIDRefByPickupGroupIDAndDestinationRegionAndClientType(ctx, request.PickupGroupID, request.DestinationRegion, clientType)
		if err != nil {
			return err
		}
		return nil
	}
	if llsErr = ctx.Transaction(fc); llsErr != nil {
		return llsErr
	}
	return nil
}

func (p *PickupGroupService) GetPickupGroupByGroupId(ctx utils.LCOSContext, request *pickup_group2.GetPickupGroupRequest) (*PickupGroupResponse, *lcos_error.LCOSError) {
	// 检查传入的pickup_group_id能否获取到内容
	model, llsErr := p.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, request.PickupGroupID, request.DestinationRegion, clientType)
	if llsErr != nil {
		return nil, llsErr
	}
	if model == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("cannot find pickup group|pickup_group_id=%s", request.PickupGroupID))
	}
	pickupGroupLineIDRefList, llsErr := p.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"pickup_group_id =": request.PickupGroupID, "destination_region": request.DestinationRegion, "client_type": clientType})
	if llsErr != nil {
		return nil, llsErr
	}

	lineList := make([]*SingleLineInfo, 0, len(pickupGroupLineIDRefList))
	if len(pickupGroupLineIDRefList) > 0 {
		for _, line := range pickupGroupLineIDRefList {
			lineList = append(lineList, &SingleLineInfo{
				LineID: line.LineID,
			})
		}
	}

	pickupGroup := &PickupGroupResponse{
		ID:                model.ID,
		PickupGroupID:     request.PickupGroupID,
		PickupGroupName:   model.PickupGroupName,
		OriginRegion:      model.OriginRegion,
		DestinationRegion: request.DestinationRegion,
		PickupType:        model.PickupType,
		LineIdList:        lineList,
		CTime:             model.CTime,
		MTime:             model.MTime,
	}
	return pickupGroup, nil

}

func (p *PickupGroupService) ListPickupGroup(ctx utils.LCOSContext, request *pickup_group2.ListPickupGroupRequest) (*ListPickupGroupResponse, *lcos_error.LCOSError) {
	searchParams := map[string]interface{}{
		"destination_region": request.DestinationRegion, // destination_region必填
		"client_type":        clientType,
	}
	var pickupGroupID string
	// 组成返回结果
	returnResult := &ListPickupGroupResponse{
		PageNo: *request.PageNo,
		Count:  *request.Count,
		Total:  0,
		List:   make([]*PickupGroupResponse, 0),
	}
	if request.PickupType != nil {
		searchParams["pickup_type"] = *request.PickupType
	}
	if request.ComponentLineID != nil {
		// 通过line反查其所属group
		result, llsErr := p.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"line_id": *request.ComponentLineID, "client_type": clientType})
		if llsErr != nil {
			return nil, llsErr
		}
		if len(result) == 0 {
			return returnResult, nil
		}
		// 传入的pickup group id和component line id计算出来的结果不一致
		if request.PickupGroupID != nil && *request.PickupGroupID != result[0].PickupGroupID {
			return returnResult, nil
		}
		pickupGroupID = result[0].PickupGroupID
	} else if request.PickupGroupID != nil {
		pickupGroupID = *request.PickupGroupID
	} else {
		pickupGroupID = ""
	}
	if pickupGroupID != "" {
		searchParams["pickup_group_id"] = pickupGroupID
	}
	pickupGroups, total, llsErr := p.pickupGroupDao.ListPickupGroup(ctx, searchParams, *request.PageNo, *request.Count)
	if llsErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, llsErr.Msg)
	}
	// 将获取到的pickup group id提取到列表中，使用in操作统一获取其line
	pickupGroupIDs := make([]string, len(pickupGroups))
	for index, pickupGroup := range pickupGroups {
		pickupGroupIDs[index] = pickupGroup.PickupGroupID
	}
	pickupGroupLineIDRefs, llsErr := p.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "client_type": clientType})
	if llsErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, llsErr.Msg)
	}
	pickupGroupLineIDListMap := make(map[string][]string)
	allLineIDList := make([]string, 0)
	for _, item := range pickupGroupLineIDRefs {
		if _, ok := pickupGroupLineIDListMap[item.PickupGroupID]; !ok {
			pickupGroupLineIDListMap[item.PickupGroupID] = make([]string, 0, 1)
		}
		allLineIDList = append(allLineIDList, item.LineID)
		pickupGroupLineIDListMap[item.PickupGroupID] = append(pickupGroupLineIDListMap[item.PickupGroupID], item.LineID)
	}

	// 批量获取line信息
	lineMap, llsErr := lls_service.BatchGetLineInfosMap(ctx, allLineIDList)
	if llsErr != nil {
		return nil, llsErr
	}
	if lineMap == nil {
		lineMap = map[string]*lls_protobuf.GetLineInfoResponseData{}
	}
	lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, allLineIDList)
	if len(lineDraftMap) > 0 {
		for lineId, lineDrafr := range lineDraftMap {
			lineMap[lineId] = lineDrafr
		}
	}
	pickupGroupResponseList := make([]*PickupGroupResponse, 0, len(pickupGroups))
	for _, pickupGroup := range pickupGroups {
		lineIDList := make([]*SingleLineInfo, 0, len(pickupGroupLineIDListMap[pickupGroup.PickupGroupID]))
		if _, ok := pickupGroupLineIDListMap[pickupGroup.PickupGroupID]; ok {
			// 封装line信息
			for _, _lineID := range pickupGroupLineIDListMap[pickupGroup.PickupGroupID] {
				if _, ok := lineMap[_lineID]; ok {
					lineIDList = append(lineIDList, &SingleLineInfo{
						LineID:      _lineID,
						LineSubType: lineMap[_lineID].GetLineType(),
						LineName:    lineMap[_lineID].GetLineName(),
					})
				}
			}
		}

		pickupGroupResponseList = append(pickupGroupResponseList, &PickupGroupResponse{
			ID:                pickupGroup.ID,
			PickupGroupID:     pickupGroup.PickupGroupID,
			PickupGroupName:   pickupGroup.PickupGroupName,
			OriginRegion:      pickupGroup.OriginRegion,
			DestinationRegion: pickupGroup.DestinationRegion,
			PickupType:        pickupGroup.PickupType,
			LineIdList:        lineIDList,
			CTime:             pickupGroup.CTime,
			MTime:             pickupGroup.MTime,
		})

	}
	returnResult.Total = total
	returnResult.List = pickupGroupResponseList
	return returnResult, nil
}

func (p *PickupGroupService) ListAllPickupGroups(ctx utils.LCOSContext, request *pickup_group2.ListAllPickupGroupRequest) ([]*AllPickupGroupResponse, *lcos_error.LCOSError) {
	searchParams := map[string]interface{}{
		"destination_region": request.DestinationRegion, // destination_region必填
		"client_type":        clientType,                // client type类型为LFS
	}

	pickupGroups, llsErr := p.pickupGroupDao.ListAllPickupGroup(ctx, searchParams)
	if llsErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, llsErr.Msg)
	}

	returnedPickupGroups := make([]*AllPickupGroupResponse, len(pickupGroups))

	for index, pickupGroup := range pickupGroups {
		returnedPickupGroups[index] = &AllPickupGroupResponse{
			PickupGroupID:   pickupGroup.PickupGroupID,
			PickupGroupName: pickupGroup.PickupGroupName,
		}
	}
	return returnedPickupGroups, nil
}

func (p *PickupGroupService) ListAllPickupLines(ctx utils.LCOSContext, request *pickup_group2.ListAllPickupLinesRequest) ([]*AllPickupLinesResponse, *lcos_error.LCOSError) {
	searchParams := map[string]interface{}{
		"destination_region": request.DestinationRegion, // destination_region必填
		"client_type":        clientType,                // client type类型为LFS
	}
	if request.OriginRegion != "" {
		searchParams["origin_region"] = request.OriginRegion
	}

	lineList, lcosErr := lls_service.BatchGetLineInfosByParams(ctx, searchParams)
	if lcosErr != nil {
		return nil, lcosErr
	}
	lineDraftList, lcosErr := lls_service.BatchGetLineDraftsByParams(ctx, searchParams)
	if lcosErr != nil {
		return nil, lcosErr
	}
	lineList = append(lineList, lineDraftList...)

	// 获取line列表
	var lineIDList = make([]string, 0, len(lineList))
	for _, _line := range lineList {
		lineIDList = append(lineIDList, _line.GetLineId())
	}

	// 查询serviceable conf表，确认collect type属性
	serviceableConfTabs, lcosErr := p.serviceableConfDao.GetBasicServiceableConfModelByLineIds(ctx, lineIDList)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 遍历serviceableConfTabs，作为line id->collect_type
	lineCollectTypeMap := map[string]uint32{}
	for _, _serviceableConf := range serviceableConfTabs {
		// 需要满足pickup条件
		if lcosErr := common_utils.CheckLineCanPickupByServiceAreaConf(_serviceableConf); lcosErr == nil {
			lineCollectTypeMap[_serviceableConf.LineId] = _serviceableConf.CollectDeliverAbility
		}
	}

	// 将line信息汇总返回
	returnLineList := make([]*AllPickupLinesResponse, 0, len(serviceableConfTabs))
	for _, _lineInfo := range lineList {
		if _, ok := lineCollectTypeMap[_lineInfo.GetLineId()]; ok {
			if lcosErr := common_utils.CheckLineCanPickupByLineInfo(_lineInfo); lcosErr == nil {
				returnLineList = append(returnLineList, &AllPickupLinesResponse{
					LineID:      _lineInfo.GetLineId(),
					LineName:    _lineInfo.GetLineName(),
					LineSubType: _lineInfo.GetLineType(),
					CollectType: lineCollectTypeMap[_lineInfo.GetLineId()],
				})
			}
		}
	}

	//// 新增草稿数据
	//for _, _lineInfo := range lineDraftList {
	//	if _, ok := lineCollectTypeMap[_lineInfo.GetLineId()]; ok {
	//		returnLineList = append(returnLineList, &AllPickupLinesResponse{
	//			LineID:      _lineInfo.GetLineId(),
	//			LineName:    _lineInfo.GetLineName(),
	//			LineSubType: _lineInfo.GetLineType(),
	//			CollectType: lineCollectTypeMap[_lineInfo.GetLineId()],
	//		})
	//	}
	//}

	return returnLineList, nil
}

func (p *PickupGroupService) SearchPickupGroup(ctx utils.LCOSContext, request *pickup_group2.SearchPickupGroupRequest) ([]*PickupGroupSearchResponse, *lcos_error.LCOSError) {
	result, llsErr := p.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"line_id in": request.LineID})
	if llsErr != nil {
		return nil, llsErr
	}
	if len(result) == 0 {
		return nil, nil
	}
	newResult := make([]*pickup_group.PickupGroupLineIDRefTab, 0)
	var containGroupId []string
	for _, pg := range result {
		if !in(pg.PickupGroupID, containGroupId) {
			containGroupId = append(containGroupId, pg.PickupGroupID)
			newResult = append(newResult, pg)
		}
	}
	if len(newResult) == 0 {
		return nil, nil
	}

	groupResult := make([]*pickup_group.PickupGroupLineIDRefTab, 0)
	for _, pg := range newResult {
		groupList, llsErr := p.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"pickup_group_id": pg.PickupGroupID})
		if llsErr != nil {
			return nil, llsErr
		}
		if len(groupList) > 0 {
			groupResult = append(groupResult, groupList...)
		}
	}

	groupIdMap := make(map[string][]string)
	var groupIdList []string
	for _, res := range groupResult {
		if value, ok := groupIdMap[res.PickupGroupID]; !ok {
			groupIdList = append(groupIdList, res.PickupGroupID)
			groupIdMap[res.PickupGroupID] = []string{res.LineID}
		} else {
			groupIdMap[res.PickupGroupID] = append(value, res.LineID)
		}

	}
	if len(groupIdList) == 0 {
		return nil, nil
	}
	groupList, lcosError := p.pickupGroupDao.ListAllPickupGroup(ctx, map[string]interface{}{"pickup_group_id in": groupIdList})
	if lcosError != nil {
		return nil, lcosError
	}
	var pickupGroupInfoList []*PickupGroupSearchResponse
	for _, group := range groupList {
		pickupGroupInfoList = append(pickupGroupInfoList, &PickupGroupSearchResponse{
			ID:                group.ID,
			PickupGroupID:     group.PickupGroupID,
			PickupGroupName:   group.PickupGroupName,
			OriginRegion:      group.OriginRegion,
			DestinationRegion: group.DestinationRegion,
			PickupType:        group.PickupType,
			LineIdList:        groupIdMap[group.PickupGroupID],
			CTime:             group.MTime,
			MTime:             group.CTime,
		})
	}

	return pickupGroupInfoList, nil

}

func in(target string, strArray []string) bool {
	for _, element := range strArray {
		if target == element {
			return true
		}
	}
	return false
}
