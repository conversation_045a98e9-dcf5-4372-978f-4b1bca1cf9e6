package open_logistic_pickup_configuration

import (
	"fmt"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/open_logistic_pickup_config"
	openlogisticpickupconfig2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	llsprotobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
)

const clientType = constant.OPEN
const openPrefix = "open"

type OpenLogisticPickupConfigServiceInterface interface {
	Create(ctx utils.LCOSContext, request *open_logistic_pickup_config.CreateOpenLogisticPickupConfigRequest) (*openlogisticpickupconfig2.OpenLogisticPickupConfTab, *lcos_error.LCOSError)
	Update(ctx utils.LCOSContext, request *open_logistic_pickup_config.UpdateOpenLogisticPickupConfigRequest) (*openlogisticpickupconfig2.OpenLogisticPickupConfTab, *lcos_error.LCOSError)
	Delete(ctx utils.LCOSContext, request *open_logistic_pickup_config.DeleteOpenLogisticPickupConfigRequest) *lcos_error.LCOSError
	List(ctx utils.LCOSContext, request *open_logistic_pickup_config.ListOpenLogisticPickupConfigRequest) ([]*SinglePickupConf, *lcos_error.LCOSError)
	ListByMerchantTypes(ctx utils.LCOSContext, request *open_logistic_pickup_config.GetOpenLogisticPickupConfigByMerchantTypeRequest) ([]*PickupAndLineInfo, *lcos_error.LCOSError)
	DeleteByMerchantType(ctx utils.LCOSContext, request *open_logistic_pickup_config.DeleteOpenLogisticPickupConfigByMerchantTypeRequest) *lcos_error.LCOSError
}

type OpenLogisticPickupConfigService struct {
	pickupConfigDao openlogisticpickupconfig2.OpenLogisticPickupConfigurationDAO
	pickupGroupDao  pickup_group.PickupGroupDAO
	basicConfDao    basic_conf.LineBasicServiceableConfDAO
}

func NewOpenLogisticPickupConfigService(pickupConfigDao openlogisticpickupconfig2.OpenLogisticPickupConfigurationDAO, pickupGroupDao pickup_group.PickupGroupDAO, basicConfDao basic_conf.LineBasicServiceableConfDAO) *OpenLogisticPickupConfigService {
	return &OpenLogisticPickupConfigService{
		pickupConfigDao: pickupConfigDao,
		pickupGroupDao:  pickupGroupDao,
		basicConfDao:    basicConfDao,
	}
}

func (l *OpenLogisticPickupConfigService) Create(ctx utils.LCOSContext, request *open_logistic_pickup_config.CreateOpenLogisticPickupConfigRequest) (*openlogisticpickupconfig2.OpenLogisticPickupConfTab, *lcos_error.LCOSError) {
	// 获取service area conf，检查是否可以pickup group
	lineServiceableConfMap, lcosErr := l.basicConfDao.GetBasicServiceableConfModelMapByLineIds(ctx, []string{request.LineID})
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 先检查传入的line是否可以pickup
	lineInfoMap, lcosErr := common_utils.GetLinesAndCheckCanPickupForBuild(ctx, []string{request.LineID}, lineServiceableConfMap)
	if lcosErr != nil {
		return nil, lcosErr
	}
	// 检查传入的destination region是否和line本身的相同，不同则直接报错
	if *lineInfoMap[request.LineID].DestinationRegion != request.DestinationRegion {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("line:[%s] not belong to region:[%s]", request.LineID, request.DestinationRegion))
	}
	// 目前在line前面加上open用于填充pickup group id
	pickupGroupID := fmt.Sprintf("%s:%s", openPrefix, request.LineID)
	pickupGroupName := fmt.Sprintf("%s:%s", openPrefix, request.LineID)
	// 先检查当前的line是否已经创建了group
	models, llsErr := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"line_id": request.LineID, "client_type": clientType})
	if llsErr != nil {
		return nil, llsErr
	}
	pickupGroup := &pickup_group.PickupGroupTab{
		PickupGroupID:     pickupGroupID,
		PickupGroupName:   pickupGroupName,
		OriginRegion:      *lineInfoMap[request.LineID].OriginRegion,
		DestinationRegion: *lineInfoMap[request.LineID].DestinationRegion,
		PickupType:        request.PickupType,
		ClientType:        clientType, // 开放物流设置类型为OPEN
	}
	pickupGroupLineMap := &pickup_group.PickupGroupLineIDRefTab{
		PickupGroupID:     pickupGroupID,
		DestinationRegion: *lineInfoMap[request.LineID].DestinationRegion,
		LineID:            request.LineID,
		ClientType:        clientType,
	}
	openLogisticPickupTab := &openlogisticpickupconfig2.OpenLogisticPickupConfTab{
		PickupGroupID:     pickupGroupID,
		DestinationRegion: *lineInfoMap[request.LineID].DestinationRegion,
		MerchantType:      request.MerchantType,
		AccountGroup:      request.AccountGroup,
		PickupDays:        request.PickupDays,
		PickupCutoffHour:  request.PickupCutoffHour,
		PickupDateFormat:  request.PickupDateFormat,
		IsHaveTimeslot:    request.IsHaveTimeslot,
		SlotNum:           request.SlotNum,
		EffectiveTime:     request.EffectiveTime,
		EnableStatus:      constant.ENABLED,
	}
	// 开启事务，先更新所有当前pickup group相关的数据的status为0，在创建对应的数据的status为1
	fc := func() *lcos_error.LCOSError {
		// 创建pickup group，map和对应的conf
		if len(models) == 0 {
			// 当找不到models时才需要创建
			if _, err := l.pickupGroupDao.CreatePickupGroup(ctx, pickupGroup); err != nil {
				return err
			}
			if _, err := l.pickupGroupDao.CreatePickupGroupLineIDRef(ctx, []*pickup_group.PickupGroupLineIDRefTab{pickupGroupLineMap}); err != nil {
				return err
			}
		}
		if err := l.pickupConfigDao.BatchDisableStatusByPickupGroupIDAndDestinationRegionAndOpenType(ctx, pickupGroupID, request.DestinationRegion, request.MerchantType, request.AccountGroup); err != nil {
			return err
		}
		if _, err := l.pickupConfigDao.CreatePickupConfiguration(ctx, openLogisticPickupTab); err != nil {
			return err
		}
		return nil
	}

	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return openLogisticPickupTab, nil
}

func (l *OpenLogisticPickupConfigService) Update(ctx utils.LCOSContext, request *open_logistic_pickup_config.UpdateOpenLogisticPickupConfigRequest) (*openlogisticpickupconfig2.OpenLogisticPickupConfTab, *lcos_error.LCOSError) {
	// 获取对应id的PickupConfig
	pickupConfig, err := l.pickupConfigDao.GetPickupConfigById(ctx, request.Id)
	if err != nil || pickupConfig == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("no such record|id=%d", request.Id))
	}
	// 开启事务，先更新所有当前line相关的数据的status为0，在创建对应的数据的status为1
	// 将需要更新的字段组合为map
	updatedMap, err1 := utils.Struct2map(request)
	if err1 != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err1.Error())
	}

	fc := func() *lcos_error.LCOSError {
		if err := l.pickupConfigDao.BatchDisableStatusByPickupGroupIDAndDestinationRegionAndOpenType(ctx, pickupConfig.PickupGroupID, pickupConfig.DestinationRegion, pickupConfig.MerchantType, pickupConfig.AccountGroup); err != nil {
			return err
		}
		// 需要在updateMap中加入status字段为enable
		updatedMap["enable_status"] = constant.ENABLED
		if err := l.pickupConfigDao.UpdatePickupConfigById(ctx, request.Id, updatedMap); err != nil {
			return err
		}
		return nil
	}

	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return nil, nil
}

func (l *OpenLogisticPickupConfigService) List(ctx utils.LCOSContext, request *open_logistic_pickup_config.ListOpenLogisticPickupConfigRequest) ([]*SinglePickupConf, *lcos_error.LCOSError) {
	var lineInfoMap map[string]*llsprotobuf.GetLineInfoResponseData
	var err *lcos_error.LCOSError
	emptyReturnedResult := make([]*SinglePickupConf, 0)
	var pickupGroupID string
	// 检索条件中传入了line id
	if request.LineID != nil {
		lineID := *request.LineID
		// 当前的line需要属于传入的destination_region
		if !(len(lineID) >= 4 && utils.GetRegionFromResourceId(lineID) == request.DestinationRegion) {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("line not belong to region:[%s]|line_id=%s", request.DestinationRegion, lineID))
		}
		// 获取line id对应的pickup group
		models, err := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"line_id": *request.LineID, "client_type": clientType})
		if err != nil {
			return nil, err
		}
		if len(models) == 0 {
			// 目前入参的line id找不到pickup group的话，返回空列表
			//return nil, lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("cannot find pickup group|line_id=%s", *request.LineID))
			return emptyReturnedResult, nil
		}
		pickupGroupID = models[0].PickupGroupID
	} else {
		pickupGroupID = ""
	}
	// 用于获取pickup group信息
	var pickupGroups []*pickup_group.PickupGroupTab
	groupInfoMap := make(map[string]*pickup_group.PickupGroupTab)
	if pickupGroupID == "" {
		// 没有检索条件，仅按照destination_region过滤
		pickupGroups, err = l.pickupGroupDao.GetPickupGroupByDestinationRegionAndClientType(ctx, request.DestinationRegion, clientType)
		if err != nil {
			return emptyReturnedResult, err
		}
	} else {
		pickupGroup, err := l.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, pickupGroupID, request.DestinationRegion, clientType)
		if err != nil {
			return emptyReturnedResult, err
		}
		pickupGroups = []*pickup_group.PickupGroupTab{pickupGroup}
	}
	pickupGroupIDs := make([]string, len(pickupGroups))
	for index, item := range pickupGroups {
		pickupGroupIDs[index] = item.PickupGroupID
		groupInfoMap[item.PickupGroupID] = item
	}
	pickupGroupLineIDRefList, err := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "destination_region": request.DestinationRegion, "client_type": clientType})
	if err != nil {
		return emptyReturnedResult, err
	}
	// 汇总line id和对应的pickup group信息
	lineList := make([]string, len(pickupGroupLineIDRefList))
	for _index, _map := range pickupGroupLineIDRefList {
		lineList[_index] = _map.LineID
	}
	lineInfoMap, err = lls_service.BatchGetLineInfosMap(ctx, lineList)
	if err != nil {
		return emptyReturnedResult, err
	}
	if lineInfoMap == nil {
		lineInfoMap = map[string]*llsprotobuf.GetLineInfoResponseData{}
	}
	lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, lineList)
	if len(lineDraftMap) > 0 {
		for lineId, lineDrafr := range lineDraftMap {
			lineInfoMap[lineId] = lineDrafr
		}
	}
	returnLineInfoMap := map[string]*SingleLineInfo{}
	for _, item := range pickupGroupLineIDRefList {
		// 开放物流group信息和line一一对应
		if _lineInfo, ok := lineInfoMap[item.LineID]; ok {
			returnLineInfoMap[item.PickupGroupID] = &SingleLineInfo{
				LineID:       _lineInfo.GetLineId(),
				LineName:     _lineInfo.GetLineName(),
				LineType:     _lineInfo.GetLineType(),
				PickupType:   groupInfoMap[item.PickupGroupID].PickupType,
				OriginRegion: groupInfoMap[item.PickupGroupID].OriginRegion,
			}
		}
	}
	// 通过pickup group获取pickup conf信息
	pickupConfs, err := l.pickupConfigDao.GetAllPickupConfigs(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "destination_region": request.DestinationRegion})
	if err != nil {
		return emptyReturnedResult, err
	}
	realReturnedResult := make([]*SinglePickupConf, 0, len(pickupConfs))
	for _, item := range pickupConfs {
		if _pickupGroupInfo, ok := returnLineInfoMap[item.PickupGroupID]; ok {
			realReturnedResult = append(realReturnedResult, &SinglePickupConf{
				OpenLogisticPickupConfTab: item,
				SingleLineInfo:            _pickupGroupInfo,
			})
		}
	}
	// 按照更新时间逆序
	sort.Slice(realReturnedResult, func(i, j int) bool {
		if realReturnedResult[i].MTime == realReturnedResult[j].MTime {
			// status为1的拥有最大的显示优先级
			if realReturnedResult[i].EnableStatus == constant.ENABLED && realReturnedResult[j].EnableStatus != constant.ENABLED {
				return true
			} else if realReturnedResult[i].EnableStatus != constant.ENABLED && realReturnedResult[j].EnableStatus == constant.ENABLED {
				return false
			}
		}
		return realReturnedResult[i].MTime > realReturnedResult[j].MTime
	})
	return realReturnedResult, nil
}

func (l *OpenLogisticPickupConfigService) Delete(ctx utils.LCOSContext, request *open_logistic_pickup_config.DeleteOpenLogisticPickupConfigRequest) *lcos_error.LCOSError {
	// 检查对应id的pick up conf是否存在
	pickupConfig, err := l.pickupConfigDao.GetPickupConfigById(ctx, request.Id)
	if err != nil {
		return err
	}
	if pickupConfig != nil && pickupConfig.EnableStatus == constant.ENABLED {
		// 当前enable的pickup 无法被删除
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "pickup conf is enabled and cannot be deleted")
	}
	return l.pickupConfigDao.DeletePickupConfigById(ctx, request.Id)
}

func (l *OpenLogisticPickupConfigService) ListByMerchantTypes(ctx utils.LCOSContext, request *open_logistic_pickup_config.GetOpenLogisticPickupConfigByMerchantTypeRequest) ([]*PickupAndLineInfo, *lcos_error.LCOSError) {
	cond := make(map[string]interface{})
	cond["merchant_type in"] = request.MerchantTypes
	cond["destination_region"] = request.DestinationRegion
	pickupConf, err := l.pickupConfigDao.GetAllPickupConfigs(ctx, cond)
	if err != nil {
		return nil, err
	}
	sort.Slice(pickupConf, func(i, j int) bool {
		return pickupConf[i].ID < pickupConf[j].ID
	})
	pickupGroupIDs := []string{}
	for _, conf := range pickupConf {
		pickupGroupIDs = append(pickupGroupIDs, conf.PickupGroupID)
	}
	pickupGroupLineIDRefList, err := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "destination_region": request.DestinationRegion, "client_type": clientType})
	if err != nil {
		return nil, err
	}
	// 汇总line id和对应的pickup group信息
	lineList := make([]string, 0, len(pickupGroupLineIDRefList))
	mapGroupLine := make(map[string]string)
	for _, ref := range pickupGroupLineIDRefList {
		lineList = append(lineList, ref.LineID)
		mapGroupLine[ref.PickupGroupID] = ref.LineID
	}
	lineInfoMap, err := lls_service.BatchGetLineInfosMap(ctx, lineList)
	if err != nil {
		return nil, err
	}
	if lineInfoMap == nil {
		lineInfoMap = map[string]*llsprotobuf.GetLineInfoResponseData{}
	}
	lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, lineList)
	if len(lineDraftMap) > 0 {
		for lineId, lineDrafr := range lineDraftMap {
			lineInfoMap[lineId] = lineDrafr
		}
	}
	result := []*PickupAndLineInfo{}
	for _, conf := range pickupConf {
		if lineId, has := mapGroupLine[conf.PickupGroupID]; has {
			if _lineInfo, ok := lineInfoMap[lineId]; ok {
				result = append(result, &PickupAndLineInfo{
					OpenLogisticPickupConfTab: *conf,
					LineID:                    *_lineInfo.LineId,
					LineName:                  *_lineInfo.LineName,
					LineType:                  *_lineInfo.LineType,
				})
			}
		}
	}

	return result, nil
}

func (l *OpenLogisticPickupConfigService) DeleteByMerchantType(ctx utils.LCOSContext, req *open_logistic_pickup_config.DeleteOpenLogisticPickupConfigByMerchantTypeRequest) *lcos_error.LCOSError {
	cond := make(map[string]interface{})
	cond["merchant_type"] = req.MerchantTypes
	cond["destination_region"] = req.DestinationRegion
	return l.pickupConfigDao.BatchDisableStatusByParams(ctx, cond)
}
