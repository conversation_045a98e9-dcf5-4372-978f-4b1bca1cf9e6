package open_logistic_pickup_configuration

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_pickup_config"
)

type (
	// pickup 信息
	SinglePickupConf struct {
		*open_logistic_pickup_config.OpenLogisticPickupConfTab
		*SingleLineInfo
	}

	SingleLineInfo struct {
		LineID       string `json:"line_id"`
		LineName     string `json:"line_name"`
		LineType     uint32 `json:"line_type"`
		PickupType   uint8  `json:"pickup_type"`
		OriginRegion string `json:"origin_region"`
	}

	PickupAndLineInfo struct {
		open_logistic_pickup_config.OpenLogisticPickupConfTab
		LineID   string `json:"line_id"`
		LineName string `json:"line_name"`
		LineType uint32 `json:"line_type"`
	}
)
