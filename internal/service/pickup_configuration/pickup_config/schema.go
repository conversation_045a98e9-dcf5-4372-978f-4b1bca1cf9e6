package pickup_configuration

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"

type (
	// pickup 信息
	SinglePickupConf struct {
		*SinglePickupGroupInfo
		*pickup_config.PickupConfTab
		SpecialPickupConfList []*pickup_config.PickupConfSpecialSettingsTab `json:"special_pickup_conf_list"`
	}

	SinglePickupGroupInfo struct {
		PickupGroupID     string           `json:"pickup_group_id"`
		PickupGroupName   string           `json:"pickup_group_name"`
		PickupType        uint8            `json:"pickup_type"`
		OriginRegion      string           `json:"origin_region"`
		DestinationRegion string           `json:"destination_region"`
		LineIDList        []*LineExtraInfo `json:"line_id_list"`
	}

	LineExtraInfo struct {
		LineID   string `json:"line_id"`
		LineName string `json:"line_name"`
		LineType uint32 `json:"line_type"`
	}
)
