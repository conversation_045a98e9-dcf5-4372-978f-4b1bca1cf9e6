package pickup_configuration

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/pickup_window_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/timeslot"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	pickup_config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	lls_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
)

const clientType = constant.LFS

type PickupConfigServiceInterface interface {
	Create(ctx utils.LCOSContext, request *pickup_config2.CreatePickupConfigRequest) (*pickup_config.PickupConfTab, *lcos_error.LCOSError)
	Update(ctx utils.LCOSContext, request *pickup_config2.UpdatePickupConfigRequest) (*pickup_config.PickupConfTab, *lcos_error.LCOSError)
	Delete(ctx utils.LCOSContext, request *pickup_config2.DeletePickupConfigRequest) *lcos_error.LCOSError
	List(ctx utils.LCOSContext, request *pickup_config2.ListPickupConfigRequest) ([]*SinglePickupConf, *lcos_error.LCOSError)
	Disable(ctx utils.LCOSContext, request *pickup_config2.DisablePickupConfigRequest) *lcos_error.LCOSError
	//Search(ctx utils.LCOSContext, request *pickup_config2.SearchPickupConfigRequest) ([]*pickup_config.PickupConfTab, *lcos_error.LCOSError)

	CreatePickupConfShopGroupRef(ctx utils.LCOSContext, pickupConf *pickup_config.PickupConfTab, shopGroupList []string) *lcos_error.LCOSError
	DeletePickupConfShopGroupRef(ctx utils.LCOSContext, pickupConfId uint64) *lcos_error.LCOSError
	ListAllUpcomingPickupConf(ctx utils.LCOSContext) ([]*pickup_config.PickupConfTab, *lcos_error.LCOSError)
	EnablePickupConfiguration(ctx utils.LCOSContext, pickupConf *pickup_config.PickupConfTab) *lcos_error.LCOSError
}

type PickupConfigService struct {
	pickupConfigDao pickup_config.PickupConfigurationDAO
	pickupGroupDao  pickup_group.PickupGroupDAO
	basicConfDao    basic_conf.LineBasicServiceableConfDAO

	timeslotService timeslot.PickupTimeslotServiceInterface
}

func NewLinePickupConfigService(pickupConfigDao pickup_config.PickupConfigurationDAO, pickupGroupDao pickup_group.PickupGroupDAO, basicConfDao basic_conf.LineBasicServiceableConfDAO, timeslotService timeslot.PickupTimeslotServiceInterface) *PickupConfigService {
	return &PickupConfigService{
		pickupConfigDao: pickupConfigDao,
		pickupGroupDao:  pickupGroupDao,
		basicConfDao:    basicConfDao,
		timeslotService: timeslotService,
	}
}

func validateSpecialSettings(conf *pickup_config2.SpecialPickupConf) *lcos_error.LCOSError {
	// 如果ApplyForDayType=1，则SpecialPickupType不能为0
	// ApplyForCutoffHour=1，则SpecialCutoffHour不为0
	if *conf.ApplyForDayType == constant.TRUE && *conf.SpecialPickupType == 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "special_pickup_type is required when apply_for_day_type is true")
	}
	if *conf.ApplyForCutoffHour == constant.TRUE && *conf.SpecialCutoffHour > 24 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "special cutoff hour is not valid when apply_for_day_type is true")
	}
	return nil
}

func (l *PickupConfigService) getLineListByPickupGroupID(ctx utils.LCOSContext, pickupGroup *pickup_group.PickupGroupTab, clientType uint32) ([]string, *lcos_error.LCOSError) {
	pickupGroupID := pickupGroup.PickupGroupID
	destinationRegion := pickupGroup.DestinationRegion
	refs, lcosErr := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"pickup_group_id": pickupGroupID, "destination_region": destinationRegion, "client_type": clientType})
	if lcosErr != nil {
		return nil, lcosErr
	}
	lineList := make([]string, 0, len(refs))
	for _, ref := range refs {
		lineList = append(lineList, ref.LineID)
	}
	return lineList, nil
}

func (l *PickupConfigService) Create(ctx utils.LCOSContext, request *pickup_config2.CreatePickupConfigRequest) (*pickup_config.PickupConfTab, *lcos_error.LCOSError) {
	// SPLN-35909 advance_days逻辑下线
	// 目前前端有一大坨展示逻辑，将advance_days和return_days展示为一个字段，即available days before SBD
	// advance_days为jira中的逻辑1，return_days为逻辑2，改造后前端提供的这两个字段均使用逻辑2。改造方式：
	// 1. grpc服务两个字段的读取和计算逻辑保持不变
	// 2. admin服务配置不再写入advance_days，仅写入return_days。从前端接收到这两个字段时做以下处理：
	// 2.1 如果advance_days为0，不做任何处理
	// 2.2 如果advance_days不为0，且return_days不为0，则将advacne_days置0。(这种情况正常不存在)
	// 2.3 如果advance_days不为0，且return_days为0，则将advacne_days的值赋给return_days，然后advance_days置0
	advanceDays := request.GetAdvanceDays()
	if advanceDays != 0 {
		request.AdvanceDays = utils.NewUint8(0)
		if request.GetReturnDays() == 0 {
			request.ReturnDays = utils.NewUint8(advanceDays)
		}
	}

	pickupGroupID := request.PickupGroupID
	destinationRegion := request.DestinationRegion

	// 获取pickup group及其refs信息
	pickupGroup, err := l.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, pickupGroupID, destinationRegion, clientType)
	if err != nil {
		return nil, err
	}
	lineIDList, err := l.getLineListByPickupGroupID(ctx, pickupGroup, clientType)
	if err != nil {
		return nil, err
	}

	// 获取对应的service area conf以检查pickup能力
	basicConfMap, err := l.basicConfDao.GetBasicServiceableConfModelMapByLineIds(ctx, lineIDList)
	if err != nil {
		return nil, err
	}

	// 检查当前的pickup group是否具有pickup能力
	err = common_utils.CheckPickupGroupCanPickup(ctx, pickupGroup, lineIDList, basicConfMap)
	if err != nil {
		return nil, err
	}
	// 如果没有传shop group，则默认使用default shop group
	if len(request.ShopGroupList) == 0 {
		request.ShopGroupList = []string{pickup_window_constant.DefaultShopGroup}
	}
	// 不允许同时勾选default group和其他shop group
	if len(request.ShopGroupList) > 1 {
		for _, shopGroup := range request.ShopGroupList {
			if shopGroup == pickup_window_constant.DefaultShopGroup {
				return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cannot config '0 - Default' and the other shop groups in the same pickup configuration")
			}
		}
	}
	// 新建pickup conf时，所有timeslot的timeslot value必须置空，由后端生成。SPLN-36184不再直接置空，直接通过reset timeslot flag控制是否由后端重新生成
	//for _, timeslot := range request.Timeslots {
	//	timeslot.TimeslotValue = nil
	//}
	// 校验生效时间
	nowTime := utils.GetTimestamp(ctx)
	if request.IsScheduled {
		// 如果配置延迟生效，那么需要校验生效时间晚于当前时间
		if request.EffectiveTime <= nowTime {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "effective time[%s] cannot be earlier than now time", utils.FormatTimestamp(request.EffectiveTime, constant.DateAndTimeFormat))
		}
	} else {
		// 如果配置立即生效，那么需要将生效时间置为当前时间
		request.EffectiveTime = nowTime
	}

	pickupTab := &pickup_config.PickupConfTab{
		PickupGroupID:                  request.PickupGroupID,
		DestinationRegion:              request.DestinationRegion,
		IsExtend:                       request.IsExtend,
		IsNonWorkingDayPickupAllowed:   request.IsNonWorkingDayPickupAllowed,
		PickupDateFormat:               request.PickupDateFormat,
		IsHaveTimeSlot:                 request.IsHaveTimeSlot,
		EffectiveTime:                  request.EffectiveTime,
		EnableStatus:                   constant.UPCOMING,
		IsSpecialPickupSettingsEnabled: request.IsSpecialPickupSettingsEnabled,
		IsScheduled:                    request.IsScheduled,
		UseTimeslotType:                pickup_window_constant.PickupConfTimeslot, // SPLN-33729发布后修改的配置默认使用pickup conf timeslot
		ShopGroupList:                  request.ShopGroupList,
		ExtendDaysBeforeAcl2:           request.ExtendDaysBeforeAcl2,
	}
	if pickupTab.PickupGroupID == pickup_window_constant.UparcelPickupGroup {
		// SPLN-36184 10016渠道特殊逻辑，由于10016固定使用timeslot id为1和2，因此此渠道所有pickup config保持共用pickup group维度timeslot
		pickupTab.UseTimeslotType = pickup_window_constant.PickupGroupTimeslot
	}
	// 填充非必填字段
	if request.ExtendDays != nil {
		pickupTab.ExtendDays = uint32(*request.ExtendDays)
	}
	if request.SlotNum != nil {
		pickupTab.SlotNum = uint32(*request.SlotNum)
	}
	if request.UseReleaseTime != nil {
		pickupTab.UseReleaseTime = *request.UseReleaseTime
	}
	if request.ReturnDays != nil {
		pickupTab.ReturnDays = uint32(*request.ReturnDays)
	}
	if request.AdvanceDays != nil {
		pickupTab.AdvanceDays = uint32(*request.AdvanceDays)
	}
	if request.PickupCutoffHour != nil {
		pickupTab.PickupCutoffHour = uint32(*request.PickupCutoffHour)
	}
	if request.ExtendSlotNum != nil {
		pickupTab.ExtendSlotNum = uint32(*request.ExtendSlotNum)
	}
	if request.DailyMaxVolume != nil {
		pickupTab.DailyMaxVolume = *request.DailyMaxVolume
	}
	if request.DailyControlStatus != nil {
		pickupTab.DailyControlStatus = *request.DailyControlStatus
	}
	if request.DailyMinDaysExtend != nil {
		pickupTab.DailyMinDaysExtend = *request.DailyMinDaysExtend
	}
	if request.DailyControlBeginTime != nil {
		pickupTab.DailyControlBeginTime = *request.DailyControlBeginTime
	}
	if request.DailyControlEndTime != nil {
		pickupTab.DailyControlEndTime = *request.DailyControlEndTime
	}

	// 开启特殊配置后，特殊配置的列表不能为空。关闭特殊配置后，特殊配置的列表必须为0
	if pickupTab.IsSpecialPickupSettingsEnabled == constant.TRUE && len(request.SpecialPickupConfList) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "special settings cannot be empty when special settings enabled")
	}
	if pickupTab.IsSpecialPickupSettingsEnabled == constant.FALSE && len(request.SpecialPickupConfList) > 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "special settings must be empty when special settings disabled")
	}
	// 解析生成special settings
	var specialSettingsModels []*pickup_config.PickupConfSpecialSettingsTab
	for _, specialSettings := range request.SpecialPickupConfList {
		lcosErr := validateSpecialSettings(specialSettings)
		if lcosErr != nil {
			return nil, lcosErr
		}
		specialSettingsModels = append(specialSettingsModels, &pickup_config.PickupConfSpecialSettingsTab{
			ApplyForDayType:       *specialSettings.ApplyForDayType,
			ApplyForCutoffHour:    *specialSettings.ApplyForCutoffHour,
			SpecialPickupType:     *specialSettings.SpecialPickupType,
			SpecialCutoffHour:     *specialSettings.SpecialCutoffHour,
			SpecialPickupCriteria: *specialSettings.SpecialPickupCriteria,
			SpecialShopGroup:      *specialSettings.SpecialShopGroup,
		})
	}

	fc := func() *lcos_error.LCOSError {
		// 1. 创建pickup window配置
		if _, err := l.pickupConfigDao.CreatePickupConfiguration(ctx, pickupTab); err != nil {
			return err
		}
		// 2. 创建pickup window特殊配置（已废弃）
		if pickupTab.IsSpecialPickupSettingsEnabled == constant.TRUE {
			for i := 0; i < len(specialSettingsModels); i++ {
				specialSettingsModels[i].PickupConfID = pickupTab.ID
			}
			if err = l.pickupConfigDao.CreatePickupSpecialSettings(ctx, specialSettingsModels); err != nil {
				return err
			}
		}
		// 3. 构建pickup config与shop group的关联关系
		if err = l.CreatePickupConfShopGroupRef(ctx, pickupTab, request.ShopGroupList); err != nil {
			return err
		}
		// 4. 创建pickup config timeslot配置
		err = l.timeslotService.CreateOrUpdate(ctx, &timeslot2.CreateOrUpdateTimeslotRequest{
			PickupGroupID:      pickupGroupID,
			DestinationRegion:  destinationRegion,
			Timeslots:          request.Timeslots,
			TimeslotType:       request.TimeslotType,
			PickupConfId:       pickupTab.ID,
			ResetTimeslotValue: true, // 创建pickup window配置时生成的timeslot需要重制timeslot_value字段
		})
		if err != nil {
			return err
		}
		// 5. 如果pickup window配置立即生效，那么需要轮转配置状态
		if !request.IsScheduled && config.AllowPickupWindowConfigTakeEffectImmediately(ctx) {
			// pickup window立即生效，需要马上触发状态轮转
			if err = l.EnablePickupConfiguration(ctx, pickupTab); err != nil {
				return err
			}
		}
		return nil
	}

	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return pickupTab, nil
}

func (l *PickupConfigService) Update(ctx utils.LCOSContext, request *pickup_config2.UpdatePickupConfigRequest) (*pickup_config.PickupConfTab, *lcos_error.LCOSError) {
	// SPLN-35909 advance_days逻辑下线
	// 目前前端有一大坨展示逻辑，将advance_days和return_days展示为一个字段，即available days before SBD
	// advance_days为jira中的逻辑1，return_days为逻辑2，改造后前端提供的这两个字段均使用逻辑2。改造方式：
	// 1. grpc服务两个字段的读取和计算逻辑保持不变
	// 2. admin服务配置不再写入advance_days，仅写入return_days。从前端接收到这两个字段时做以下处理：
	// 2.1 如果advance_days为0，不做任何处理
	// 2.2 如果advance_days不为0，且return_days不为0，则将advacne_days置0。(这种情况正常不存在)
	// 2.3 如果advance_days不为0，且return_days为0，则将advacne_days的值赋给return_days，然后advance_days置0
	advanceDays := request.GetAdvanceDays()
	if advanceDays != 0 {
		request.AdvanceDays = utils.NewUint8(0)
		if request.GetReturnDays() == 0 {
			request.ReturnDays = utils.NewUint8(advanceDays)
		}
	}

	// 获取对应id的PickupConfig
	pickupConfig, err := l.pickupConfigDao.GetPickupConfigById(ctx, request.Id)
	if err != nil || pickupConfig == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("no such record|id=%d", request.Id))
	}

	// 开启特殊配置后，特殊配置的列表不能为空。关闭特殊配置后，特殊配置的列表必须为0
	if *request.IsSpecialPickupSettingsEnabled == constant.TRUE && len(request.SpecialPickupConfList) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "special settings cannot be empty when special settings enabled")
	}
	if *request.IsSpecialPickupSettingsEnabled == constant.FALSE && len(request.SpecialPickupConfList) > 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "special settings must be empty when special settings disabled")
	}
	// special settings
	var specialSettingsModels []*pickup_config.PickupConfSpecialSettingsTab
	for _, specialSettings := range request.SpecialPickupConfList {
		lcosErr := validateSpecialSettings(specialSettings)
		if lcosErr != nil {
			return nil, lcosErr
		}
		specialSettingsModels = append(specialSettingsModels, &pickup_config.PickupConfSpecialSettingsTab{
			PickupConfID:          pickupConfig.ID,
			ApplyForDayType:       *specialSettings.ApplyForDayType,
			ApplyForCutoffHour:    *specialSettings.ApplyForCutoffHour,
			SpecialPickupType:     *specialSettings.SpecialPickupType,
			SpecialCutoffHour:     *specialSettings.SpecialCutoffHour,
			SpecialPickupCriteria: *specialSettings.SpecialPickupCriteria,
			SpecialShopGroup:      *specialSettings.SpecialShopGroup,
		})
	}
	// 如果没有传shop group，则默认使用default shop group
	if len(request.ShopGroupList) == 0 {
		request.ShopGroupList = []string{pickup_window_constant.DefaultShopGroup}
	}
	// 不允许同时勾选default group和其他shop group
	if len(request.ShopGroupList) > 1 {
		for _, shopGroup := range request.ShopGroupList {
			if shopGroup == pickup_window_constant.DefaultShopGroup {
				return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cannot config '0 - Default' and the other shop groups in the same pickup configuration")
			}
		}
	}
	pickupConfig.ShopGroupList = request.ShopGroupList
	// 校验生效时间
	nowTime := utils.GetTimestamp(ctx)
	if request.IsScheduled {
		// 如果配置延迟生效，那么需要校验生效时间晚于当前时间
		if request.EffectiveTime != nil {
			pickupConfig.EffectiveTime = *request.EffectiveTime
		}
		if pickupConfig.EffectiveTime <= nowTime {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "effective time[%s] cannot be earlier than now time", utils.FormatTimestamp(pickupConfig.EffectiveTime, constant.DateAndTimeFormat))
		}
	} else {
		// 如果配置立即生效，那么需要将生效时间置为当前时间
		request.EffectiveTime = utils.NewUint32(nowTime)
		pickupConfig.EffectiveTime = nowTime
	}

	// 将需要更新的字段组合为map
	updatedMap, err1 := utils.Struct2map(request)
	if err1 != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err1.Error())
	}
	updatedMap["enable_status"] = constant.UPCOMING // 任务编辑后重新变为upcoming状态
	var resetTimeslotValue bool
	if pickupConfig.PickupGroupID != pickup_window_constant.UparcelPickupGroup && pickupConfig.UseTimeslotType == pickup_window_constant.PickupGroupTimeslot {
		// SPLN-33729发布后修改的配置默认使用pickup conf timeslot
		// 如果pickup window配置原本使用pickup group timeslot，此次更新会生成并使用pickup conf timeslot，需要重新设置timeslot value
		resetTimeslotValue = true
		updatedMap["use_timeslot_type"] = pickup_window_constant.PickupConfTimeslot
	}

	fc := func() *lcos_error.LCOSError {
		// 1. 更新pickup window配置信息
		if err := l.pickupConfigDao.UpdatePickupConfigById(ctx, request.Id, updatedMap); err != nil {
			return err
		}

		// 2. 更新pickup window特殊配置（已废弃）
		err = l.pickupConfigDao.DeletePickupSpecialSettingsByPickupConfID(ctx, pickupConfig.ID)
		if err != nil {
			return err
		}
		if len(specialSettingsModels) > 0 {
			err = l.pickupConfigDao.CreatePickupSpecialSettings(ctx, specialSettingsModels)
		}
		if err != nil {
			return err
		}

		// 3. 重建shop group关联关系
		if err = l.DeletePickupConfShopGroupRef(ctx, pickupConfig.ID); err != nil {
			return err
		}
		if err = l.CreatePickupConfShopGroupRef(ctx, pickupConfig, request.ShopGroupList); err != nil {
			return err
		}

		// 4. 创建pickup config timeslot配置
		err = l.timeslotService.CreateOrUpdate(ctx, &timeslot2.CreateOrUpdateTimeslotRequest{
			PickupGroupID:      pickupConfig.PickupGroupID,
			DestinationRegion:  pickupConfig.DestinationRegion,
			Timeslots:          request.Timeslots,
			TimeslotType:       request.TimeslotType,
			PickupConfId:       pickupConfig.ID,
			ResetTimeslotValue: resetTimeslotValue,
		})
		if err != nil {
			return err
		}

		// 5. 如果pickup window配置立即生效，那么需要轮转配置状态
		if !request.IsScheduled && config.AllowPickupWindowConfigTakeEffectImmediately(ctx) {
			if err = l.EnablePickupConfiguration(ctx, pickupConfig); err != nil {
				return err
			}
		}
		return nil
	}

	if err := ctx.Transaction(fc); err != nil {
		return nil, err
	}
	return nil, nil
}

func (l *PickupConfigService) List(ctx utils.LCOSContext, request *pickup_config2.ListPickupConfigRequest) ([]*SinglePickupConf, *lcos_error.LCOSError) {
	var lineInfoMap map[string]*lls_protobuf.GetLineInfoResponseData
	var err *lcos_error.LCOSError
	emptyReturnedResult := make([]*SinglePickupConf, 0)
	var pickupGroupID string
	// 在同时传入了component line id和line group id的情况下，需要确认两者相同
	if request.ComponentLineID != nil {
		lineID := *request.ComponentLineID
		// 当前的line需要属于传入的destination_region
		if !(len(lineID) >= 4 && utils.GetRegionFromResourceId(lineID) == request.DestinationRegion) {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("line not belong to region:[%s]|line_id=%s", request.DestinationRegion, lineID))
		}
		// 获取line id对应的pickup group
		models, err := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"line_id": *request.ComponentLineID, "client_type": clientType})
		if err != nil {
			return nil, err
		}
		if len(models) == 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("cannot find pickup group|line_id=%s", *request.ComponentLineID))
		}
		// 传入的pickup group和component_line_id计算出来的不一致，此时返回空
		if request.PickupGroupID != nil && *request.PickupGroupID != models[0].PickupGroupID {
			return emptyReturnedResult, nil
		}
		pickupGroupID = models[0].PickupGroupID
	} else if request.PickupGroupID != nil {
		pickupGroupID = *request.PickupGroupID
	} else {
		pickupGroupID = ""
	}
	// 用于存储pickup group信息
	var pickupGroups []*pickup_group.PickupGroupTab
	// 用于缓存查询的pickup group信息
	tmpPickupGroupInfoMap := make(map[string]*SinglePickupGroupInfo)
	if pickupGroupID == "" {
		// 没有检索条件，仅按照destination_region和client type来过滤
		pickupGroups, err = l.pickupGroupDao.GetPickupGroupByDestinationRegionAndClientType(ctx, request.DestinationRegion, clientType)
		if err != nil {
			return emptyReturnedResult, err
		}
	} else {
		pickupGroup, err := l.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, pickupGroupID, request.DestinationRegion, clientType)
		if err != nil {
			return emptyReturnedResult, err
		}
		pickupGroups = []*pickup_group.PickupGroupTab{pickupGroup}
	}
	pickupGroupIDs := make([]string, len(pickupGroups))
	for index, item := range pickupGroups {
		pickupGroupIDs[index] = item.PickupGroupID
		tmpPickupGroupInfoMap[item.PickupGroupID] = &SinglePickupGroupInfo{
			PickupGroupID:     item.PickupGroupID,
			PickupGroupName:   item.PickupGroupName,
			PickupType:        item.PickupType,
			OriginRegion:      item.OriginRegion,
			DestinationRegion: item.DestinationRegion,
			LineIDList:        make([]*LineExtraInfo, 0, 2),
		}
	}
	pickupGroupLineIDRefList, err := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "destination_region": request.DestinationRegion, "client_type": clientType})
	if err != nil {
		return emptyReturnedResult, err
	}
	// 汇总line list，请求lls
	lineList := make([]string, len(pickupGroupLineIDRefList))
	for index, item := range pickupGroupLineIDRefList {
		lineList[index] = item.LineID
	}
	lineInfoMap, err = lls_service.BatchGetLineInfosMap(ctx, lineList)
	if err != nil {
		return emptyReturnedResult, err
	}
	if lineInfoMap == nil {
		lineInfoMap = map[string]*lls_protobuf.GetLineInfoResponseData{}
	}
	lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, lineList)
	if len(lineDraftMap) > 0 {
		for lineId, lineDrafr := range lineDraftMap {
			lineInfoMap[lineId] = lineDrafr
		}
	}
	// 将获取到的line信息填充到pickup group中
	for _, item := range pickupGroupLineIDRefList {
		if _lineInfo, ok := lineInfoMap[item.LineID]; ok {
			if group, ok := tmpPickupGroupInfoMap[item.PickupGroupID]; ok {
				group.LineIDList = append(group.LineIDList, &LineExtraInfo{
					LineID:   _lineInfo.GetLineId(),
					LineName: _lineInfo.GetLineName(),
					LineType: _lineInfo.GetLineType(),
				})
			}
		}
	}
	// 通过pickup group获取pickup conf信息
	queryParams := map[string]interface{}{
		"pickup_group_id in": pickupGroupIDs,
		"destination_region": request.DestinationRegion,
	}
	if request.EnableStatus != nil {
		queryParams["enable_status"] = *request.EnableStatus
	}
	pickupConfs, err := l.pickupConfigDao.GetAllPickupConfigs(ctx, queryParams)
	if err != nil {
		return emptyReturnedResult, err
	}
	// 填充pickup conf的shop group关联信息
	if err = l.fillPickupConfShopGroupRef(ctx, pickupConfs); err != nil {
		return emptyReturnedResult, err
	}

	// 查询所有的pickup special settings
	var pickupConfIDList []uint64
	for _, tmp := range pickupConfs {
		pickupConfIDList = append(pickupConfIDList, tmp.ID)
	}
	pickupSpecialSettings, lcosErr := l.pickupConfigDao.ListPickupSpecialSettingsByParams(ctx, map[string]interface{}{"pickup_conf_id in": pickupConfIDList})
	if lcosErr != nil {
		return emptyReturnedResult, err
	}
	pickupSpecialSettingsMap := map[uint64][]*pickup_config.PickupConfSpecialSettingsTab{}
	// 将结果存为pickupConfID -> specialSettingsList 的形势，方便查询
	for _, setting := range pickupSpecialSettings {
		pickupSpecialSettingsMap[setting.PickupConfID] = append(pickupSpecialSettingsMap[setting.PickupConfID], setting)
	}

	realReturnedResult := make([]*SinglePickupConf, 0, len(pickupConfs))
	for _, item := range pickupConfs {
		if _pickupGroupInfo, ok := tmpPickupGroupInfoMap[item.PickupGroupID]; ok {
			// 基于shop group筛选
			if request.ShopGroup != "" && !utils.CheckInString(request.ShopGroup, item.ShopGroupList) {
				continue
			}

			tmpPickupConf := &SinglePickupConf{
				PickupConfTab:         item,
				SinglePickupGroupInfo: _pickupGroupInfo,
			}
			if _, ok := pickupSpecialSettingsMap[item.ID]; ok {
				tmpPickupConf.SpecialPickupConfList = pickupSpecialSettingsMap[item.ID]
			}
			realReturnedResult = append(realReturnedResult, tmpPickupConf)
		}
	}
	// 按照更新时间逆序
	sort.Slice(realReturnedResult, func(i, j int) bool {
		if realReturnedResult[i].MTime == realReturnedResult[j].MTime {
			// status为1的拥有最大的显示优先级
			if realReturnedResult[i].EnableStatus == constant.ENABLED && realReturnedResult[j].EnableStatus != constant.ENABLED {
				return true
			} else if realReturnedResult[i].EnableStatus != constant.ENABLED && realReturnedResult[j].EnableStatus == constant.ENABLED {
				return false
			}
		}
		return realReturnedResult[i].MTime > realReturnedResult[j].MTime
	})
	return realReturnedResult, nil
}

func (l *PickupConfigService) Delete(ctx utils.LCOSContext, request *pickup_config2.DeletePickupConfigRequest) *lcos_error.LCOSError {
	// 正常流程删除
	if request.Id != 0 {
		// 检查对应id的pick up conf是否存在
		pickupConfig, err := l.pickupConfigDao.GetPickupConfigById(ctx, request.Id)
		if err != nil {
			return err
		}
		if pickupConfig != nil && pickupConfig.EnableStatus == constant.ENABLED {
			// 当前enable的pickup 无法被删除
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "pickup conf is enabled and cannot be deleted")
		}
		return l.pickupConfigDao.DeletePickupConfigById(ctx, request.Id)
	}
	// 草稿状态删除
	return l.pickupConfigDao.DeletePickupConfigByPickupGroupID(ctx, request.PickupGroupID)
}

func (l *PickupConfigService) Disable(ctx utils.LCOSContext, request *pickup_config2.DisablePickupConfigRequest) *lcos_error.LCOSError {
	pickupConf, err := l.pickupConfigDao.GetPickupConfigById(ctx, request.Id)
	if err != nil {
		return err
	}
	if pickupConf == nil {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "pickup conf not found|pickup_conf_id=%d", pickupConf.ID) // nolint
	}
	if err = l.fillPickupConfShopGroupRef(ctx, []*pickup_config.PickupConfTab{pickupConf}); err != nil {
		return err
	}

	switch pickupConf.EnableStatus {
	case constant.DISABLED:
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "pickup config is already in Disabled status|pickup_conf_id=%d", pickupConf.ID)
	case constant.ENABLED:
		for _, shopGroup := range pickupConf.ShopGroupList {
			if shopGroup == pickup_window_constant.DefaultShopGroup {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot disable active pickup config with shop group '0-Default'|pickup_conf_id=%d", pickupConf.ID)
			}
		}
	case constant.UPCOMING:
	default:
	}

	return l.pickupConfigDao.UpdatePickupConfigById(ctx, pickupConf.ID, map[string]interface{}{"enable_status": constant.DISABLED})
}

func (l *PickupConfigService) CreatePickupConfShopGroupRef(ctx utils.LCOSContext, pickupConf *pickup_config.PickupConfTab, shopGroupList []string) *lcos_error.LCOSError {
	if pickupConf == nil || len(shopGroupList) == 0 {
		return nil
	}
	shopGroupRefList := make([]*pickup_config.PickupConfShopGroupRefTab, 0, len(shopGroupList))
	for _, shopGroup := range shopGroupList {
		shopGroupRefList = append(shopGroupRefList, &pickup_config.PickupConfShopGroupRefTab{
			PickupConfId:  pickupConf.ID,
			Region:        pickupConf.DestinationRegion,
			PickupGroupId: pickupConf.PickupGroupID,
			ShopGroupId:   shopGroup,
			EffectiveTime: pickupConf.EffectiveTime,
		})
	}
	return l.pickupConfigDao.BatchCreatePickupConfShopGroupRef(ctx, shopGroupRefList)
}

func (l *PickupConfigService) DeletePickupConfShopGroupRef(ctx utils.LCOSContext, pickupConfId uint64) *lcos_error.LCOSError {
	return l.pickupConfigDao.DeletePickupConfShopGroupRef(ctx, pickupConfId)
}

func (l *PickupConfigService) ListAllUpcomingPickupConf(ctx utils.LCOSContext) ([]*pickup_config.PickupConfTab, *lcos_error.LCOSError) {
	ret, err := l.pickupConfigDao.GetAllPickupConfigs(ctx, map[string]interface{}{
		"enable_status": constant.UPCOMING,
	})
	if err != nil {
		return nil, err
	}
	if err = l.fillPickupConfShopGroupRef(ctx, ret); err != nil {
		return nil, err
	}
	// 生效时间按照从近到远排序
	sort.SliceStable(ret, func(i, j int) bool {
		return ret[i].EffectiveTime < ret[j].EffectiveTime
	})
	return ret, nil
}

func (l *PickupConfigService) EnablePickupConfiguration(ctx utils.LCOSContext, pickupConf *pickup_config.PickupConfTab) *lcos_error.LCOSError {
	// 1. 获取可能需要disable的pickup window配置列表（region和pickup group相同，状态为enabled）
	enabledList, err := l.pickupConfigDao.GetAllPickupConfigs(ctx, map[string]interface{}{
		"destination_region": pickupConf.DestinationRegion,
		"pickup_group_id":    pickupConf.PickupGroupID,
		"enable_status":      constant.ENABLED,
	})
	if err != nil {
		return err
	}
	if err = l.fillPickupConfShopGroupRef(ctx, enabledList); err != nil {
		return err
	}

	// 2. 从enabled配置中筛选出shop group有交集的配置，这些配置需要disable掉
	var disablePickupConfList []uint64
	for _, conf := range enabledList {
		for _, shopGroup := range conf.ShopGroupList {
			if utils.CheckInString(shopGroup, pickupConf.ShopGroupList) {
				disablePickupConfList = append(disablePickupConfList, conf.ID)
				break
			}
		}
	}

	// 3. 更新pickup window配置状态
	fn := func() *lcos_error.LCOSError {
		err = l.pickupConfigDao.UpdatePickupConfigByParams(ctx, map[string]interface{}{"id in": disablePickupConfList}, map[string]interface{}{
			"enable_status": constant.DISABLED,
		})
		if err != nil {
			return err
		}
		err = l.pickupConfigDao.UpdatePickupConfigById(ctx, pickupConf.ID, map[string]interface{}{
			"enable_status": constant.ENABLED,
		})
		if err != nil {
			return err
		}
		return nil
	}
	return ctx.Transaction(fn)
}

func (l *PickupConfigService) fillPickupConfShopGroupRef(ctx utils.LCOSContext, pickupConfList []*pickup_config.PickupConfTab) *lcos_error.LCOSError {
	if len(pickupConfList) == 0 {
		return nil
	}

	pickupConfIdList := make([]uint64, 0, len(pickupConfList))
	for _, pickupConf := range pickupConfList {
		pickupConfIdList = append(pickupConfIdList, pickupConf.ID)
	}
	shopGroupRefList, err := l.pickupConfigDao.ListPickupConfShopGroupRef(ctx, map[string]interface{}{
		"pickup_conf_id in": pickupConfIdList,
	})
	if err != nil {
		return err
	}

	shopGroupRefMap := make(map[uint64][]string, len(pickupConfList))
	for _, shopGroupRef := range shopGroupRefList {
		shopGroupRefMap[shopGroupRef.PickupConfId] = append(shopGroupRefMap[shopGroupRef.PickupConfId], shopGroupRef.ShopGroupId)
	}
	for _, pickupConf := range pickupConfList {
		shopGroupList, ok := shopGroupRefMap[pickupConf.ID]
		if !ok {
			// 兼容已有数据，已有数据不存在shop group关联关系，则返回default shop group
			shopGroupList = []string{pickup_window_constant.DefaultShopGroup}
		}
		pickupConf.ShopGroupList = shopGroupList
	}
	return nil
}

//func (l *PickupConfigService) Search(ctx utils.LCOSContext, request *pickup_config2.SearchPickupConfigRequest) ([]*pickup_config.PickupConfTab, *lcos_error.LCOSError) {
//	refs, lcosErr := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"line_id": request.LineID})
//	if lcosErr != nil {
//		return nil, lcosErr
//	}
//
//}
