package open_logistic_timeslot

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	open_logistic_timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/open_logistic_timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/open_logistic_timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
)

const clientType = constant.OPEN

type OpenLogisticPickupTimeslotServiceInterface interface {
	CreateOrUpdate(ctx utils.LCOSContext, request *open_logistic_timeslot2.CreateOrUpdateOpenLogisticTimeslotRequest) *lcos_error.LCOSError
	Delete(ctx utils.LCOSContext, request *open_logistic_timeslot2.DeleteOpenLogisticTimeslotRequest) *lcos_error.LCOSError
	List(ctx utils.LCOSContext, request *open_logistic_timeslot2.ListOpenLogisticTimeslotRequest) ([]*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, *lcos_error.LCOSError)
}

type OpenLogisticPickupTimeslotService struct {
	pickupTimeslotDao open_logistic_timeslot.OpenLogisticPickupTimeslotDAO
	pickupGroupDao    pickup_group.PickupGroupDAO
}

func NewOpenLogisticPickupTimeslotService(pickupTimeslotDao open_logistic_timeslot.OpenLogisticPickupTimeslotDAO, pickupGroupDao pickup_group.PickupGroupDAO) *OpenLogisticPickupTimeslotService {
	return &OpenLogisticPickupTimeslotService{
		pickupTimeslotDao: pickupTimeslotDao,
		pickupGroupDao:    pickupGroupDao,
	}
}

func (l *OpenLogisticPickupTimeslotService) CreateOrUpdate(ctx utils.LCOSContext, request *open_logistic_timeslot2.CreateOrUpdateOpenLogisticTimeslotRequest) *lcos_error.LCOSError {
	lineID := request.LineID
	merchantType := request.MerchantType
	accountGroup := request.AccountGroup
	// 通过line id获取到映射关系
	pickupGroupMaps, err := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"line_id": lineID, "client_type": clientType})
	if err != nil {
		return err
	}
	if len(pickupGroupMaps) == 0 {
		return lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("cannot find pickup group|line_id=%s", request.LineID))
	}
	pickupGroupID := pickupGroupMaps[0].PickupGroupID
	destinationRegion := pickupGroupMaps[0].DestinationRegion

	fc := func() *lcos_error.LCOSError {
		// 先删除当前所有的timeslots
		if err := l.pickupTimeslotDao.DeleteByPickupGroupIDAndDestinationRegionAndOpenType(ctx, pickupGroupID, destinationRegion, merchantType, accountGroup); err != nil {
			return err
		}
		// 实际的更新操作，只有传入的timeslots数量大于0时才能创建
		if len(request.Timeslots) > 0 {
			timeslots := make([]*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, len(request.Timeslots))
			for _index, _timeslot := range request.Timeslots {
				// 如果timeslotmark为now，则需要设置value为0
				if _timeslot.TimeslotRemark == "Now" {
					_timeslot.TimeslotValue = 0
				}
				// create
				tmpTimeslot := &open_logistic_timeslot.OpenLogisticPickupTimeslotTab{
					PickupGroupID:     pickupGroupID,
					DestinationRegion: destinationRegion,
					MerchantType:      merchantType,
					AccountGroup:      accountGroup,
					TimeslotValue:     _timeslot.TimeslotValue,
					StartTimeHour:     uint32(_timeslot.StartTimeHour),
					StartTimeMin:      uint32(_timeslot.StartTimeMin),
					EndTimeHour:       uint32(_timeslot.EndTimeHour),
					EndTimeMin:        uint32(_timeslot.EndTimeMin),
					TimeslotRemark:    _timeslot.TimeslotRemark,
					EnableStatus:      constant.ENABLED,
				}
				if _timeslot.StartTimeSecond != nil {
					tmpTimeslot.StartTimeSecond = uint32(*_timeslot.StartTimeSecond)
				}
				if _timeslot.EndTimeSecond != nil {
					tmpTimeslot.EndTimeSecond = uint32(*_timeslot.EndTimeSecond)
				}
				if _timeslot.SlotStartHour != nil {
					tmpTimeslot.SlotStartHour = uint32(*_timeslot.SlotStartHour)
				}
				if _timeslot.SlotCutoffHour != nil {
					tmpTimeslot.SlotCutoffHour = *_timeslot.SlotCutoffHour
				}
				if _timeslot.SlotCutoffMin != nil {
					tmpTimeslot.SlotCutoffMin = uint32(*_timeslot.SlotCutoffMin)
				}
				timeslots[_index] = tmpTimeslot
			}
			if _, lcosErr := l.pickupTimeslotDao.CreateTimeslot(ctx, timeslots); lcosErr != nil {
				return lcosErr
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}
	return nil
}

func (l *OpenLogisticPickupTimeslotService) Delete(ctx utils.LCOSContext, request *open_logistic_timeslot2.DeleteOpenLogisticTimeslotRequest) *lcos_error.LCOSError {
	return l.pickupTimeslotDao.Delete(ctx, request.Id)
}

func (l *OpenLogisticPickupTimeslotService) List(ctx utils.LCOSContext, request *open_logistic_timeslot2.ListOpenLogisticTimeslotRequest) ([]*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, *lcos_error.LCOSError) {
	// 通过line id计算pickup group id
	pickupGroupLineMaps, lcosErr := l.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"line_id": request.LineID, "client_type": clientType})
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(pickupGroupLineMaps) == 0 {
		return make([]*open_logistic_timeslot.OpenLogisticPickupTimeslotTab, 0), nil
	}
	pickupGroupID := pickupGroupLineMaps[0].PickupGroupID
	destinationRegion := pickupGroupLineMaps[0].DestinationRegion
	return l.pickupTimeslotDao.List(ctx, map[string]interface{}{"pickup_group_id": pickupGroupID, "destination_region": destinationRegion, "merchant_type": request.MerchantType, "account_group": request.AccountGroup})
}
