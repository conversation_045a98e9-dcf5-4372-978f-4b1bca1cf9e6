package pickup_window_factory

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/common_utils"
	pickup_window_grpc_open_logistic "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_window_open_logisitc"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_window_site_line"
)

type PickupWindowFactoryInterface interface {
	CreatePickupWindowService() PickupWindowServiceInterface
}

type PickupWindowServiceInterface interface {
	GetArrangedPickupDays(ctx utils.LCOSContext, request interface{}) (*common_utils.PickupDays, *lcos_error.LCOSError)
	GetReturnedPickupDays(ctx utils.LCOSContext, request interface{}) (*common_utils.PickupDays, *lcos_error.LCOSError)
	CheckPickupTime(ctx utils.LCOSContext, request interface{}) *lcos_error.LCOSError
	GetPickupTimeslots(ctx utils.LCOSContext, request interface{}) ([]*common_utils.TimeslotInfo, *lcos_error.LCOSError)
	GetPickupTimeslot(ctx utils.LCOSContext, request interface{}) (*common_utils.SingleTimeslotInfo, *lcos_error.LCOSError)
	GetValidPickupConf(ctx utils.LCOSContext, request interface{}) (*common_utils.PickupConf, *lcos_error.LCOSError)
	GetAllPickupGroups(ctx utils.LCOSContext) ([]*common_utils.PickupGroup, *lcos_error.LCOSError)
	GetHolidays(ctx utils.LCOSContext, request interface{}) ([]string, *lcos_error.LCOSError)
	GetPickupHolidays(ctx utils.LCOSContext, request interface{}) (*common_utils.ProductPickupDays, *lcos_error.LCOSError)
}

type PickupWindowFactory struct {
	clientType uint32
}

func NewPickupWindowFactory(clientType uint32) *PickupWindowFactory {
	return &PickupWindowFactory{clientType: clientType}
}

func (p *PickupWindowFactory) CreatePickupWindowService() PickupWindowServiceInterface {
	switch p.clientType {
	case constant.LFS:
		return pickup_window_site_line.NewPickupWindowSiteLineService()
	case constant.OPEN:
		return pickup_window_grpc_open_logistic.NewPickupWindowOpenLogisticService()
	}
	return nil
}
