package pickup_window

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/recurring_holiday"
	"strings"
)

type PickupWindowService interface {
	GetNonworkingDays(ctx utils.LCOSContext, region, groupId string, startTime, endTime uint32) ([]string, *lcos_error.LCOSError)
}

func NewPickupWindowService(holidayDao holiday.LogisticHolidayDAO, recurringHolidayDao recurring_holiday.RecurringHolidayDAO) *pickupWindowService {
	return &pickupWindowService{
		holidayDao:          holidayDao,
		recurringHolidayDao: recurringHolidayDao,
	}
}

type pickupWindowService struct {
	holidayDao          holiday.LogisticHolidayDAO
	recurringHolidayDao recurring_holiday.RecurringHolidayDAO
}

func (p *pickupWindowService) GetNonworkingDays(ctx utils.LCOSContext, region, groupId string, startTime, endTime uint32) ([]string, *lcos_error.LCOSError) {
	region = strings.ToUpper(region)

	// 1. 转换时间戳并计算间隔日期天数
	startDay := pickup.TransferTimeStampToTime(startTime, region)
	endDay := pickup.TransferTimeStampToTime(endTime, region)
	intervalDays := pickup.GetIntervalDays(startDay, endDay)

	// 2. 查询此渠道节假日信息
	queryParams := map[string]interface{}{
		"pickup_group_id":    groupId,
		"destination_region": region,
		"state_id":           0,
	}
	holidays, err := p.holidayDao.ListAllHolidayDays(ctx, queryParams)
	if err != nil {
		return nil, err
	}
	holidaysMap := make(map[string]bool, len(holidays))
	for _, holiday := range holidays {
		holidaysMap[holiday.DateString] = true
	}

	// 3. 查询此渠道周末非工作日
	recurringHolidays, err := p.recurringHolidayDao.ListRecurringHolidayDayByParams(ctx, queryParams)
	if err != nil {
		return nil, err
	}
	recurringHolidaysMap := make(map[int]bool, len(recurringHolidays))
	for _, recurringHoliday := range recurringHolidays {
		weekday := recurringHoliday.WeekDay
		if weekday == 7 {
			weekday = 0 // 适配time.Weekday接口，星期天返回值为0
		}
		recurringHolidaysMap[weekday] = true
	}

	// 4. 计算非工作日天数
	var nonworkingDays []string
	for i := 0; i < intervalDays; i++ {
		checkDay := pickup.AddDays(startDay, i)
		date := checkDay.Format("2006-01-02")
		weekday := int(checkDay.Weekday())

		if _, ok := holidaysMap[date]; ok {
			nonworkingDays = append(nonworkingDays, date)
			continue
		}
		if _, ok := recurringHolidaysMap[weekday]; ok {
			nonworkingDays = append(nonworkingDays, date)
			continue
		}
	}

	return nonworkingDays, nil
}
