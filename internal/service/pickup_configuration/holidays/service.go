package holidays

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/holidays"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/recurring_holiday"
	pickup_configuration "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

const clientType = constant.LFS

type LogisticHolidaysServiceInterface interface {
	Import(ctx utils.LCOSContext, request *holidays.ImportHolidayRequest) *lcos_error.LCOSError
	Delete(ctx utils.LCOSContext, request *holidays.DeleteHolidayRequest) *lcos_error.LCOSError
	GetHolidaysAndWeekends(ctx utils.LCOSContext, request *holidays.ListHolidayRequest) ([]*pickup_configuration.SinglePickupGroupInfo, []*holiday.LogisticHolidayTab, []*recurring_holiday.RecurringHolidayTab, *lcos_error.LCOSError)
	Create(ctx utils.LCOSContext, request *holidays.CreateHolidayRequest) (*holiday.LogisticHolidayTab, *lcos_error.LCOSError)
	List(ctx utils.LCOSContext, request *holidays.ListHolidayRequest) ([]*holiday.NonWorkingDay, *lcos_error.LCOSError)
}

type LogisticHolidaysService struct {
	logisticHolidayDao  holiday.LogisticHolidayDAO
	recurringHolidayDao recurring_holiday.RecurringHolidayDAO
	pickupGroupDao      pickup_group.PickupGroupDAO
}

func NewLogisticHolidaysService(logisticHolidayDao holiday.LogisticHolidayDAO, recurringHolidayDao recurring_holiday.RecurringHolidayDAO, pickupGroupDao pickup_group.PickupGroupDAO) *LogisticHolidaysService {
	return &LogisticHolidaysService{
		logisticHolidayDao:  logisticHolidayDao,
		recurringHolidayDao: recurringHolidayDao,
		pickupGroupDao:      pickupGroupDao,
	}
}

var batch = 1000

// 解析excel数据，对于state的country情况
func ParseExcelStateCountry(originRegion string, destinationRegion string, rows *excelize.Rows, stateNameToLocationIdMap map[string]int) (pickupGroupIDs []string, holidays []*holiday.LogisticHolidayTab, err *lcos_error.LCOSError) {
	lineNum := 0
	tmpPickupGroupMap := map[string]interface{}{} // 用于去重pickup group列表
	pickupGroupIDs = make([]string, 0, batch)
	holidays = make([]*holiday.LogisticHolidayTab, 0, batch)
	locationIds := make([]int, 0, 10)
	stateNames := make([]string, 0, 10)
	emptyColumnsList := map[int]int{}
	metaDataColumns := 2
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 解析表头的state信息
		if lineNum == 1 {
			// 解析表头为stateNames，第一列为pickup group id， 第二列为holiday，第三列开始为stateName
			for i := metaDataColumns; i < len(row); i++ {
				stateName := strings.TrimSpace(row[i])
				if stateName != "" {
					locationId, ok := stateNameToLocationIdMap[stateName]
					if !ok {
						return nil, nil, lcos_error.NewLCOSError(lcos_error.NotFoundAreaErrorCode, fmt.Sprintf("cannot find state:%s in location service", row[i]))
					}
					locationIds = append(locationIds, locationId)
					stateNames = append(stateNames, stateName)
				} else {
					// 将当前列标记为空列，未来解析也会跳过
					emptyColumnsList[i] = 1
				}
			}
			continue
		}
		// 跳过空行
		if serviceable_util.IsBlankRow(row) {
			continue
		}
		// 对于stateCountry上传的文件列的长度不能少于2列
		if len(row) <= metaDataColumns {
			return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "for state country, excel has to be more than 3 columns")
		}
		// 如果是MY的文件,需要解析之后state为location id，state从第三列开始
		for i := metaDataColumns; i < len(row); i++ {
			if row[i] == "" {
				continue
			}
			// 跳过解析表头时的空列
			if _, ok := emptyColumnsList[i]; ok {
				continue
			}
			// 在检查当前的值是否为1，不是则直接跳过
			flag, err := strconv.Atoi(row[i])
			if err != nil {
				return nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "data can only be 1 or 0 or empty")
			}
			if flag != 1 {
				continue
			}
			// 将pickupGroup收集到列表中
			pickupGroupID := strings.TrimSpace(row[0])
			if _, ok := tmpPickupGroupMap[row[0]]; !ok {
				tmpPickupGroupMap[pickupGroupID] = 1
				pickupGroupIDs = append(pickupGroupIDs, pickupGroupID)
			}
			date, err := time.ParseInLocation("2006-01-02", strings.TrimSpace(row[1]), pickup.GetTimeLocation(originRegion))
			if err != nil {
				return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("date:[%s] is not valid, should be in format 2020-01-06", strings.TrimSpace(row[1])))
			}
			// 封装列表数据
			tmpHoliday := &holiday.LogisticHolidayTab{
				PickupGroupID:     pickupGroupID,
				DestinationRegion: strings.ToUpper(destinationRegion),
				StateId:           int64(locationIds[i-metaDataColumns]),
				StateName:         stateNames[i-metaDataColumns],
				DateString:        date.Format("2006-01-02"),
			}
			holidays = append(holidays, tmpHoliday)
		}
	}
	return pickupGroupIDs, holidays, nil
}

// 解析excel数据，对于非state的country情况
func ParseExcelNonStateCountry(originRegion string, destinationRegion string, rows *excelize.Rows) (pickupGroupIDs []string, holidays []*holiday.LogisticHolidayTab, err *lcos_error.LCOSError) {
	lineNum := 0
	tmpPickupGroupMap := map[string]interface{}{} // 用于去重pickup Group列表
	pickupGroupIDs = make([]string, 0, batch)
	holidays = make([]*holiday.LogisticHolidayTab, 0, batch)
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 解析表头的state信息
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}
		if len(row) != 2 {
			return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "for non state country, excel has to be 2 columns")
		}
		// 将pickup group收集到列表中
		pickupGroupID := strings.TrimSpace(row[0])
		if _, ok := tmpPickupGroupMap[row[0]]; !ok {
			tmpPickupGroupMap[pickupGroupID] = 1
			pickupGroupIDs = append(pickupGroupIDs, pickupGroupID)
		}
		date, err := time.ParseInLocation("2006-01-02", strings.TrimSpace(row[1]), pickup.GetTimeLocation(originRegion))
		if err != nil {
			return nil, nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, fmt.Sprintf("date:[%s] is not valid, should be in format 2020-01-06", strings.TrimSpace(row[1])))
		}
		// 封装列表数据
		tmpHoliday := &holiday.LogisticHolidayTab{
			PickupGroupID:     pickupGroupID,
			DestinationRegion: strings.ToUpper(destinationRegion),
			StateId:           0,
			DateString:        date.Format("2006-01-02"),
			StateName:         "",
		}
		holidays = append(holidays, tmpHoliday)
	}
	return pickupGroupIDs, holidays, nil
}

// 用于解析holiday导入
func (s *LogisticHolidaysService) parseAndImportHoliday(ctx utils.LCOSContext, fileUrl string, holidayType uint8, originRegion, destinationRegion string, stateNameToLocationIdMap map[string]int) *lcos_error.LCOSError {
	// 从s3服务器下载文件，并且解析
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return err
	}
	// 开始解析，参考了UploadAreaRef的实现
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.CtxLogInfof(ctx, "Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()
	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	//// 测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Desktop/non-working-VN-template.xlsx")
	//rows, _ := file.Rows("Sheet1")
	//var err *lcos_error.LCOSError

	var pickupGroupIDs []string
	var pickupGroupInfoMap = map[string]*pickup_group.PickupGroupTab{}
	var holidayTabs []*holiday.LogisticHolidayTab
	if holidayType == constant.ByState {
		pickupGroupIDs, holidayTabs, err = ParseExcelStateCountry(originRegion, destinationRegion, rows, stateNameToLocationIdMap)
		if err != nil {
			return err
		}
	} else {
		pickupGroupIDs, holidayTabs, err = ParseExcelNonStateCountry(originRegion, destinationRegion, rows)
		if err != nil {
			return err
		}
	}

	// 批量获取pickup group信息
	pickupGroups, err := s.pickupGroupDao.ListAllPickupGroup(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "destination_region": destinationRegion, "client_type": clientType})
	if err != nil {
		return err
	}
	for _, pickupGroup := range pickupGroups {
		// 所有pickup group的origin region需要和传入的相同
		if pickupGroup.OriginRegion != originRegion {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("the origin region of pickup is region:[%s] instead of region:[%s]|pickup_group_id=%s", pickupGroup.OriginRegion, originRegion, pickupGroup.PickupGroupID))
		}
		if _, ok := pickupGroupInfoMap[pickupGroup.PickupGroupID]; !ok {
			pickupGroupInfoMap[pickupGroup.PickupGroupID] = pickupGroup
		}
	}
	// 校验holidays中pickup group的信息正确
	for _, _holiday := range holidayTabs {
		if _, ok := pickupGroupInfoMap[_holiday.PickupGroupID]; !ok {
			return lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("cannot find pickup group in region:[%s]|pickup_group_id=%s", destinationRegion, _holiday.PickupGroupID))
		}
		if pickupGroupInfoMap[_holiday.PickupGroupID].DestinationRegion != destinationRegion {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("the destination region of pickup_group_id is region:[%s] instead of region:[%s]|pickup_group_id=%s", pickupGroupInfoMap[_holiday.PickupGroupID].DestinationRegion, destinationRegion, _holiday.PickupGroupID))
		}
	}

	// 统一写数据，先删除掉lineIds中的数据，在写入，需要开启事务
	fc := func() *lcos_error.LCOSError {

		err := s.logisticHolidayDao.BatchDeleteHolidayDay(ctx, map[string]interface{}{
			"pickup_group_id in": pickupGroupIDs,
			"destination_region": destinationRegion,
		})
		if err != nil {
			return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Msg)
		}
		_, err = s.logisticHolidayDao.BatchCreateHolidayDay(ctx, holidayTabs)
		if err != nil {
			return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Msg)
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}
	return nil
}

// 将单行的文件导入解析为数据表结构
func validAndParseWeekendRow(row []string, index int, weekendType uint8, destinationRegion string, stateNameToLocationIdMap map[string]int) ([]*recurring_holiday.RecurringHolidayTab, string, *lcos_error.LCOSError) {
	var total int
	if weekendType == constant.Normal {
		if len(row) < 8 {
			return nil, "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("excel should be 8 columns for normal type weekend|row=%v", index))
		}
		total = 8
	} else {
		if len(row) < 9 {
			return nil, "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("excel should be 9 columns|row=%v", index))
		}
		total = 9
	}
	weekStartIndex := total - 7
	weekEndIndex := total - 1
	weekDay := 1
	var weekends []*recurring_holiday.RecurringHolidayTab
	var pickupGroupID string
	if row[0] != "" {
		pickupGroupID = row[0]
	}
	for i := weekStartIndex; i <= weekEndIndex; i++ {
		if row[i] == "1" {
			tmpWeekend := &recurring_holiday.RecurringHolidayTab{
				PickupGroupID:     pickupGroupID,
				DestinationRegion: destinationRegion,
				WeekDay:           weekDay,
			}
			switch weekendType {
			case constant.ByState:
				if row[1] != "" {
					if stateLocationID, ok := stateNameToLocationIdMap[row[1]]; ok {
						tmpWeekend.StateId = int64(stateLocationID)
						tmpWeekend.StateName = row[1]
					} else {
						return nil, "", lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("state name:[%v] is not valid|row=%v", row[1], index))
					}
				}
			case constant.ByZipcode:
				if row[1] != "" {
					zipcode := strings.TrimSpace(row[1])
					if strings.ToLower(zipcode) == constant.DefaultZipcode {
						tmpWeekend.ZipCode = constant.DefaultZipcode
					} else {
						tmpWeekend.ZipCode = zipcode
					}
				}
			}
			weekends = append(weekends, tmpWeekend)
		}
		weekDay++
	}
	return weekends, pickupGroupID, nil
}

func parseWeekend(rows *excelize.Rows, weekendType uint8, destinationRegion string, stateNameToLocationIdMap map[string]int) ([]string, []*recurring_holiday.RecurringHolidayTab, *lcos_error.LCOSError) {
	lineNum := 0
	tmpPickupGroupMap := map[string]bool{} // 用于去重pickup Group列表
	// 检查对应的pickup group id是否存在default
	pickupGroupDefaultMap := map[string]bool{}
	var pickupGroupIDs []string
	var weekendsList []*recurring_holiday.RecurringHolidayTab
	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}
		// 解析表头的state信息
		if lineNum == 1 || serviceable_util.IsBlankRow(row) {
			continue
		}
		if len(row) > 2 && weekendType == constant.ByZipcode {
			zipcode := strings.ToLower(strings.TrimSpace(row[1]))
			pickupGroupID := strings.TrimSpace(row[0])
			if zipcode == constant.DefaultZipcode {
				pickupGroupDefaultMap[pickupGroupID] = true
			}
		}
		weekends, pickupGroupID, lcosErr := validAndParseWeekendRow(row, lineNum, weekendType, destinationRegion, stateNameToLocationIdMap)
		if lcosErr != nil {
			return nil, nil, lcosErr
		}
		if _, ok := tmpPickupGroupMap[pickupGroupID]; !ok {
			pickupGroupIDs = append(pickupGroupIDs, pickupGroupID)
			tmpPickupGroupMap[pickupGroupID] = true
		}
		weekendsList = append(weekendsList, weekends...)
	}
	if weekendType == constant.ByZipcode {
		// 检查zipcode的pickup group是否传入default
		for _, _tmpPickupGroupID := range pickupGroupIDs {
			if _, ok := pickupGroupDefaultMap[_tmpPickupGroupID]; !ok {
				return nil, nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fmt.Sprintf("not default zipcode for pickup group:[%v]", _tmpPickupGroupID))
			}
		}
	}
	return pickupGroupIDs, weekendsList, nil
}

// 用于解析weekend导入
func (s *LogisticHolidaysService) parseAndImportWeekend(ctx utils.LCOSContext, fileUrl string, weekendType uint8, originRegion, destinationRegion string, stateNameToLocationIdMap map[string]int) *lcos_error.LCOSError {
	// 从s3服务器下载文件，并且解析
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return err
	}
	// 开始解析，参考了UploadAreaRef的实现
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.CtxLogInfof(ctx, "Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()
	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	// 测试用
	//file, _ := excelize.OpenFile("/Users/<USER>/Desktop/test_weekends.xlsx")
	//rows, _ := file.Rows("Sheet1")

	// 解析excel为对应的数据表
	pickupGroupIDs, weekends, lcosErr := parseWeekend(rows, weekendType, destinationRegion, stateNameToLocationIdMap)
	if lcosErr != nil {
		return lcosErr
	}

	// 批量获取pickup group信息
	pickupGroupInfoMap := map[string]*pickup_group.PickupGroupTab{}
	pickupGroups, err := s.pickupGroupDao.ListAllPickupGroup(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "destination_region": destinationRegion, "client_type": clientType})
	if err != nil {
		return err
	}
	for _, pickupGroup := range pickupGroups {
		// 所有pickup group的origin region需要和传入的相同
		if pickupGroup.OriginRegion != originRegion {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("the origin region of pickup is region:[%s] instead of region:[%s]|pickup_group_id=%s", pickupGroup.OriginRegion, originRegion, pickupGroup.PickupGroupID))
		}
		if _, ok := pickupGroupInfoMap[pickupGroup.PickupGroupID]; !ok {
			pickupGroupInfoMap[pickupGroup.PickupGroupID] = pickupGroup
		}
	}
	// 校验weekends中pickup group的信息正确
	for _, _weekend := range weekends {
		if _, ok := pickupGroupInfoMap[_weekend.PickupGroupID]; !ok {
			return lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("cannot find pickup group in region:[%s]|pickup_group_id=%s", destinationRegion, _weekend.PickupGroupID))
		}
		if pickupGroupInfoMap[_weekend.PickupGroupID].DestinationRegion != destinationRegion {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("the destination region of pickup_group_id is region:[%s] instead of region:[%s]|pickup_group_id=%s", pickupGroupInfoMap[_weekend.PickupGroupID].DestinationRegion, destinationRegion, _weekend.PickupGroupID))
		}
	}

	// 统一写数据，先删除掉lineIds中的数据，在写入，需要开启事务
	fc := func() *lcos_error.LCOSError {

		err := s.recurringHolidayDao.DeleteRecurringHolidayDayByParams(ctx, map[string]interface{}{
			"pickup_group_id in": pickupGroupIDs,
			"destination_region": destinationRegion,
		})
		if err != nil {
			return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Msg)
		}
		_, err = s.recurringHolidayDao.BatchCreateRecurringHolidayDay(ctx, weekends)
		if err != nil {
			return lcos_error.NewLCOSError(lcos_error.DBReadWriteErrorCode, err.Msg)
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}
	return nil
}

func (s *LogisticHolidaysService) Import(ctx utils.LCOSContext, request *holidays.ImportHolidayRequest) *lcos_error.LCOSError {
	logHead := "import non working days"

	// CB 场景下，需要前端指明 destination region
	destinationRegion := request.DestinationRegion
	if destinationRegion == "" {
		destinationRegion = strings.ToUpper(ctx.GetCountry())
	}

	// 先获取state信息，以供使用
	// 请求地址服务，将地址的location信息缓存
	var stateNameToLocationIdMap = map[string]int{}
	if (request.HolidayType != nil && *request.HolidayType == constant.ByState) || (request.WeekendType != nil && *request.WeekendType == constant.ByState) {
		requestParams := &ops_service.GetLocationInfoBySubLevelRequest{
			Country:    strings.ToUpper(request.OriginRegion),
			LocationId: 0, //  取整个国家信息
			SubLevel:   1, //  去国家的省信息
		}
		//SPLN-28639去除locationgrpc开关
		stateLocationInfo, err := ops_service.GetLocationInfoBySubLevel(ctx, requestParams)
		if err != nil {
			return lcos_error.NewLCOSError(lcos_error.LocationServerErrorCode, fmt.Sprintf("err requesting location service: %s", err.Msg))
		}
		for _, i := range stateLocationInfo {
			stateNameToLocationIdMap[i.Name] = i.LocationId
		}
	}

	// holiday导入
	if request.HolidayType != nil && request.HolidayFileUrl != nil && *request.HolidayFileUrl != "" {
		lcosErr := s.parseAndImportHoliday(ctx, *request.HolidayFileUrl, *request.HolidayType, request.OriginRegion, destinationRegion, stateNameToLocationIdMap)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "[%v], error:%v", logHead, lcosErr.Msg)
			return lcosErr
		}
	}

	// weekend导入
	if request.WeekendType != nil && request.WeekendFileUrl != nil && *request.WeekendFileUrl != "" {
		lcosErr := s.parseAndImportWeekend(ctx, *request.WeekendFileUrl, *request.WeekendType, request.OriginRegion, destinationRegion, stateNameToLocationIdMap)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "[%v], error:%v", logHead, lcosErr.Msg)
			return lcosErr
		}
	}

	return nil
}

func (s *LogisticHolidaysService) Delete(ctx utils.LCOSContext, request *holidays.DeleteHolidayRequest) *lcos_error.LCOSError {
	return s.logisticHolidayDao.DeleteHolidayDay(ctx, request.Id)
}

// 通过参数过滤获取到对应的holidays和weekends
func (s *LogisticHolidaysService) GetHolidaysAndWeekends(ctx utils.LCOSContext, request *holidays.ListHolidayRequest) ([]*pickup_configuration.SinglePickupGroupInfo, []*holiday.LogisticHolidayTab, []*recurring_holiday.RecurringHolidayTab, *lcos_error.LCOSError) {
	// CB 场景下，需要前端指明 destination region
	destinationRegion := request.DestinationRegion
	if destinationRegion == "" {
		destinationRegion = strings.ToUpper(ctx.GetCountry())
	}

	// 先查询符合条件的pickup group信息
	searchParams := make(map[string]interface{})
	emptyPickupGroups := make([]*pickup_configuration.SinglePickupGroupInfo, 0)
	emptyHolidays := make([]*holiday.LogisticHolidayTab, 0)
	emptyRecurringHolidays := make([]*recurring_holiday.RecurringHolidayTab, 0)
	searchParams["destination_region"] = destinationRegion
	searchParams["client_type"] = clientType
	if request.OriginRegion != nil {
		searchParams["origin_region"] = *request.OriginRegion
	}
	if request.ComponentLineID != nil {
		// 通过line id查询到对应的pickup group，当传入line的时候，
		pickupGroupLineIDRef, err := s.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"line_id": *request.ComponentLineID, "client_type": clientType})
		if err != nil {
			return nil, nil, nil, err
		}
		if len(pickupGroupLineIDRef) == 0 {
			return emptyPickupGroups, emptyHolidays, emptyRecurringHolidays, nil
		}
		// 检查是否传入了pickup group id和line id所属的group不同，此时返回空
		if request.PickupGroupID != nil && *request.PickupGroupID != pickupGroupLineIDRef[0].PickupGroupID {
			return emptyPickupGroups, emptyHolidays, emptyRecurringHolidays, nil
		} else {
			searchParams["pickup_group_id in"] = []string{pickupGroupLineIDRef[0].PickupGroupID}
		}
	} else if request.PickupGroupID != nil {
		searchParams["pickup_group_id in"] = []string{*request.PickupGroupID}
	}
	// 根据过滤条件获取pickup group信息
	pickupGroups, err := s.pickupGroupDao.ListAllPickupGroup(ctx, searchParams)
	if err != nil {
		return nil, nil, nil, err
	}
	pickupGroupIDs := make([]string, len(pickupGroups))
	for index, pickupGroup := range pickupGroups {
		pickupGroupIDs[index] = pickupGroup.PickupGroupID
	}
	// 通过pickup group id列表过滤holiday和weekends
	recurringHolidays, err := s.recurringHolidayDao.ListRecurringHolidayDayByParams(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "destination_region": destinationRegion})
	if err != nil {
		return nil, nil, nil, err
	}
	_holidays, err := s.logisticHolidayDao.ListAllHolidayDays(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "destination_region": destinationRegion})
	if err != nil {
		return nil, nil, nil, err
	}
	// 过滤关系表，将所有的line信息取出
	pickupGroupLineIDRefs, err := s.pickupGroupDao.ListPickupGroupLineIDRef(ctx, map[string]interface{}{"pickup_group_id in": pickupGroupIDs, "destination_region": destinationRegion, "client_type": clientType})
	if err != nil {
		return nil, nil, nil, err
	}
	requiredLineIDList := make([]string, len(pickupGroupLineIDRefs))
	for index, item := range pickupGroupLineIDRefs {
		requiredLineIDList[index] = item.LineID
	}
	// 请求lls获取line信息
	lineInfoMap, err := lls_service.BatchGetLineInfosMap(ctx, requiredLineIDList)
	if err != nil {
		return nil, nil, nil, err
	}
	if lineInfoMap == nil {
		lineInfoMap = map[string]*llspb.GetLineInfoResponseData{}
	}
	lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, requiredLineIDList)
	if len(lineDraftMap) > 0 {
		for lineId, lineDrafr := range lineDraftMap {
			lineInfoMap[lineId] = lineDrafr
		}
	}
	// 封装pickup信息
	pickupGroupInfoMaps := map[string]*pickup_configuration.SinglePickupGroupInfo{}
	for _, item := range pickupGroups {
		pickupGroupInfoMaps[item.PickupGroupID] = &pickup_configuration.SinglePickupGroupInfo{
			PickupGroupID:     item.PickupGroupID,
			PickupGroupName:   item.PickupGroupName,
			PickupType:        item.PickupType,
			OriginRegion:      item.OriginRegion,
			DestinationRegion: item.DestinationRegion,
			LineIDList:        make([]*pickup_configuration.LineExtraInfo, 0, 2),
		}
	}
	for _, item := range pickupGroupLineIDRefs {
		if _, ok := lineInfoMap[item.LineID]; ok {
			pickupGroupInfoMaps[item.PickupGroupID].LineIDList = append(pickupGroupInfoMaps[item.PickupGroupID].LineIDList, &pickup_configuration.LineExtraInfo{
				LineID:   *lineInfoMap[item.LineID].LineId,
				LineName: *lineInfoMap[item.LineID].LineName,
				LineType: *lineInfoMap[item.LineID].LineType,
			})
		}

	}
	// 将map中的pickup group汇总为列表
	returnedPickupGroups := make([]*pickup_configuration.SinglePickupGroupInfo, 0, 10)
	for _, item := range pickupGroupInfoMaps {
		returnedPickupGroups = append(returnedPickupGroups, item)
	}
	return returnedPickupGroups, _holidays, recurringHolidays, nil
}

// fillStateWeekend 在weekend为state类型是，将所有的state填充
func fillStateWeekend(ctx utils.LCOSContext, stateNameInfoMap map[string][]string, region string) map[string][]int {
	if _, ok := stateNameInfoMap[region]; !ok {
		stateNameInfoMap[region] = []string{}
		requestParams := &ops_service.GetLocationInfoBySubLevelRequest{
			Country:    strings.ToUpper(region),
			LocationId: 0, //  取整个国家信息
			SubLevel:   1, //  去国家的省信息
		}
		//SPLN-28639去除locationgrpc开关
		stateLocationInfo, err := ops_service.GetLocationInfoBySubLevel(ctx, requestParams)
		if err != nil {
			logger.CtxLogErrorf(ctx, "error=%v", err)
			return nil
		}
		for _, i := range stateLocationInfo {
			stateNameInfoMap[region] = append(stateNameInfoMap[region], i.Name)
		}
	}
	result := map[string][]int{}
	for _, stateName := range stateNameInfoMap[region] {
		result[stateName] = []int{}
	}
	return result
}

func (s *LogisticHolidaysService) List(ctx utils.LCOSContext, request *holidays.ListHolidayRequest) ([]*holiday.NonWorkingDay, *lcos_error.LCOSError) {
	returnedPickupGroups, _holidays, recurringHolidays, err := s.GetHolidaysAndWeekends(ctx, request)
	if err != nil {
		return nil, err
	}
	returnResult := make([]*holiday.NonWorkingDay, 0, 20)
	// 将recurring_holiday和line_holiday转为map
	recurringHolidaysMap := make(map[string][]*recurring_holiday.RecurringHolidayTab)
	for _, item := range recurringHolidays {
		recurringHolidaysMap[item.PickupGroupID] = append(recurringHolidaysMap[item.PickupGroupID], item)
	}
	holidaysMap := make(map[string][]*holiday.LogisticHolidayTab)
	for _, item := range _holidays {
		holidaysMap[item.PickupGroupID] = append(holidaysMap[item.PickupGroupID], item)
	}

	// 存储state信息
	stateNameInfoMap := map[string][]string{}

	// 遍历pickupGroup查找有效的pickup Group
	for _, item := range returnedPickupGroups {
		pickupGroupID := item.PickupGroupID
		// 一个有效的pickupGroup起码需要lineRecurringHolidaysMap或者lineHolidaysMap中有值
		isHoliday := false
		isWeekend := false
		holidayList := make([]*holiday.LogisticHolidayTab, 0)
		weekendList := make([]*recurring_holiday.RecurringHolidayTab, 0)
		stateLocationInfo := make(map[string]int64)
		if item1, ok := recurringHolidaysMap[pickupGroupID]; ok {
			isWeekend = true
			weekendList = item1
		}
		if item2, ok := holidaysMap[pickupGroupID]; ok {
			isHoliday = true
			holidayList = item2
		}
		if isHoliday || isWeekend {
			weekendsStruct := &holiday.SimpleWeekend{
				ByState:   map[string][]int{},
				ByZipcode: map[string][]int{},
				Normal:    []int{},
			}
			holidaysStruct := &holiday.SimpleHoliday{
				ByState: map[string][]*holiday.SingleHoliday{},
				Normal:  []*holiday.SingleHoliday{},
			}
			for _, item3 := range weekendList {
				// 检查当前item是否为state类型
				if item3.ZipCode != "" {
					weekendsStruct.WeekendType = constant.ByZipcode
					if _, ok := weekendsStruct.ByZipcode[item3.ZipCode]; !ok {
						weekendsStruct.ByZipcode[item3.ZipCode] = []int{}
					}
					weekendsStruct.ByZipcode[item3.ZipCode] = append(weekendsStruct.ByZipcode[item3.ZipCode], item3.WeekDay)
				} else if item3.StateName != "" && item3.StateId != 0 {
					weekendsStruct.WeekendType = constant.ByState

					// 将当前的region填充为所有当前所有state且为空列表
					if len(weekendsStruct.ByState) == 0 {
						weekendsStruct.ByState = fillStateWeekend(ctx, stateNameInfoMap, item.OriginRegion)
					}

					if _, ok := weekendsStruct.ByState[item3.StateName]; !ok {
						weekendsStruct.ByState[item3.StateName] = []int{}
					}
					weekendsStruct.ByState[item3.StateName] = append(weekendsStruct.ByState[item3.StateName], item3.WeekDay)
					// 填充地址信息服务
					if _, ok := stateLocationInfo[item3.StateName]; !ok {
						stateLocationInfo[item3.StateName] = item3.StateId
					}
				} else {
					weekendsStruct.WeekendType = constant.Normal
					weekendsStruct.Normal = append(weekendsStruct.Normal, item3.WeekDay)
				}
			}

			// 如果weekend为zipcode，并且没有default，则补充一个空列表
			if weekendsStruct.WeekendType == constant.ByZipcode {
				if _, ok := weekendsStruct.ByZipcode[constant.DefaultZipcode]; !ok {
					weekendsStruct.ByZipcode[constant.DefaultZipcode] = []int{}
				}
			}

			for _, item4 := range holidayList {
				// 检查当前item是否为state类型
				if item4.StateName != "" && item4.StateId != 0 {
					holidaysStruct.HolidayType = constant.ByState
					if _, ok := holidaysStruct.ByState[item4.StateName]; !ok {
						holidaysStruct.ByState[item4.StateName] = []*holiday.SingleHoliday{}
					}
					// 填充地址信息服务
					if _, ok := stateLocationInfo[item4.StateName]; !ok {
						stateLocationInfo[item4.StateName] = item4.StateId
					}
					holidaysStruct.ByState[item4.StateName] = append(holidaysStruct.ByState[item4.StateName], &holiday.SingleHoliday{
						ID:         item4.ID,
						DateString: item4.DateString,
					})
				} else {
					holidaysStruct.HolidayType = constant.Normal
					holidaysStruct.Normal = append(holidaysStruct.Normal, &holiday.SingleHoliday{
						ID:         item4.ID,
						DateString: item4.DateString,
					})
				}
			}
			tmp := &holiday.NonWorkingDay{
				SinglePickupGroupInfo: item,
				StateLocationInfo:     stateLocationInfo,
				Weekends:              weekendsStruct,
				Holidays:              holidaysStruct,
				Mtime:                 0,
			}
			// 获取最新的更新时间
			mtimeList := make([]uint32, 0, len(holidayList)+len(weekendList))
			for _, item := range weekendList {
				mtimeList = append(mtimeList, item.MTime)
			}
			for _, item := range holidayList {
				mtimeList = append(mtimeList, item.MTime)
			}
			// 找到mtime的最大值
			sort.Slice(mtimeList, func(i, j int) bool {
				return mtimeList[i] > mtimeList[j]
			})
			tmp.Mtime = mtimeList[0]
			returnResult = append(returnResult, tmp)
		}
	}
	sort.Slice(returnResult, func(i, j int) bool {
		return returnResult[i].Mtime > returnResult[j].Mtime
	})
	return returnResult, nil
}

func (s *LogisticHolidaysService) Create(ctx utils.LCOSContext, request *holidays.CreateHolidayRequest) (*holiday.LogisticHolidayTab, *lcos_error.LCOSError) {
	// 确认pickup group有效
	pickupGroup, lcosErr := s.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, request.PickupGroupID, request.DestinationRegion, clientType)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if pickupGroup == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("no such pickup group in region:[%s]| pickup_group_id=%s", request.DestinationRegion, request.PickupGroupID))
	}
	date, err := time.ParseInLocation("2006-01-02", request.Date, pickup.GetTimeLocation(pickupGroup.OriginRegion))
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "date is not valid, should be in format 2020-01-30")
	}

	// 如果当前传入的state location id为0，则不允许存在非0的数据，如果传入的state location id不是0，则不允许存在为0的数据
	if request.StateLocationId != 0 {
		// 不能存在state为0的数据
		models, lcosErr := s.logisticHolidayDao.ListAllHolidayDays(ctx, map[string]interface{}{"pickup_group_id": request.PickupGroupID, "destination_region": request.DestinationRegion, "state_id": 0})
		if lcosErr != nil {
			return nil, lcosErr
		}
		if len(models) != 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("holiday is non state country type and not allowed to create state country type holiday|pickup_group_id=%s, destination_region=%s", request.PickupGroupID, request.DestinationRegion))
		}
	} else {
		models, lcosErr := s.logisticHolidayDao.ListAllHolidayDays(ctx, map[string]interface{}{"pickup_group_id": request.PickupGroupID, "destination_region": request.DestinationRegion, "state_id !=": 0})
		if lcosErr != nil {
			return nil, lcosErr
		}
		if len(models) != 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("holiday is state country type and not allowed to create non state country type holiday|pickup_group_id=%s, destination_region=%s", request.PickupGroupID, request.DestinationRegion))
		}
	}

	// 通过地址服务获取对应location id的state name
	var _holiday *holiday.LogisticHolidayTab
	// 传入的state location id不是0，表示需要请求地址服务获取具体的state信息
	if request.StateLocationId != 0 {
		locationRequest := &ops_service.GetLocationInfoByIdRequest{
			Country:    pickupGroup.OriginRegion,
			LocationId: uint64(request.StateLocationId),
		}
		result, err := ops_service.GetLocationInfo(ctx, locationRequest)
		if err != nil {
			return nil, err
		}
		_holiday = &holiday.LogisticHolidayTab{
			PickupGroupID:     pickupGroup.PickupGroupID,
			DestinationRegion: pickupGroup.DestinationRegion,
			StateId:           request.StateLocationId,
			DateString:        date.Format("2006-01-02"),
			StateName:         result.Name,
		}
	} else {
		// 非state country不存储state id和state name
		_holiday = &holiday.LogisticHolidayTab{
			PickupGroupID:     pickupGroup.PickupGroupID,
			DestinationRegion: pickupGroup.DestinationRegion,
			StateId:           0,
			DateString:        date.Format("2006-01-02"),
			StateName:         "",
		}
	}
	return s.logisticHolidayDao.CreateHolidayDay(ctx, _holiday)
}
