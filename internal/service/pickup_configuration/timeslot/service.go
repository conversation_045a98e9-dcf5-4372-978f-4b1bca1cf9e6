package timeslot

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/pickup_window_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/timeslot"
	"sort"
)

type PickupTimeslotServiceInterface interface {
	CreateOrUpdate(ctx utils.LCOSContext, request *timeslot2.CreateOrUpdateTimeslotRequest) *lcos_error.LCOSError
	Delete(ctx utils.LCOSContext, request *timeslot2.DeleteTimeslotRequest) *lcos_error.LCOSError
	List(ctx utils.LCOSContext, request *timeslot2.ListTimeslotRequest) ([]*timeslot.PickupTimeslotTab, *lcos_error.LCOSError)
}

type PickupTimeslotService struct {
	pickupTimeslotDao timeslot.PickupTimeslotDAO
	pickupGroupDao    pickup_group.PickupGroupDAO
	pickupConfDao     pickup_config.PickupConfigurationDAO
}

func NewPickupTimeslotService(pickupTimeslotDao timeslot.PickupTimeslotDAO, pickupGroupDao pickup_group.PickupGroupDAO, pickupConfDao pickup_config.PickupConfigurationDAO) *PickupTimeslotService {
	return &PickupTimeslotService{
		pickupTimeslotDao: pickupTimeslotDao,
		pickupGroupDao:    pickupGroupDao,
		pickupConfDao:     pickupConfDao,
	}
}

func isTimeslotsOverlap(timeslots []*timeslot.PickupTimeslotTab) bool {
	// sort first
	sort.SliceStable(timeslots, func(i, j int) bool {
		return timeslots[i].IsBefore(timeslots[j])
	})

	// check whether timeslot overlap
	for i := 1; i < len(timeslots); i++ {
		if timeslots[i].IsOverlap(timeslots[i-1]) {
			return true
		}
	}
	return false
}

func (l *PickupTimeslotService) CreateOrUpdate(ctx utils.LCOSContext, request *timeslot2.CreateOrUpdateTimeslotRequest) *lcos_error.LCOSError {
	// SPLN-36184 10016特殊逻辑。所有pickup config共用timeslot，同时timeslot value必须包含1和2
	if request.PickupGroupID == pickup_window_constant.UparcelPickupGroup {
		// 将timeslot绑定在pickup group维度，同时复用前端传递的timeslot value
		request.PickupConfId = 0
		request.ResetTimeslotValue = false

		// 检查uparcel timeslot是否完整包含LPS hardcode的buyer slot=1、2
		var (
			hitBuyerSlot1 bool
			hitBuyerSlot2 bool
		)
		for _, slot := range request.Timeslots {
			if slot.TimeslotValue == nil {
				continue
			}
			slotId := *slot.TimeslotValue
			if slotId == pickup_window_constant.UparcelBuyerTimeslot1 {
				hitBuyerSlot1 = true
			}
			if slotId == pickup_window_constant.UparcelBuyerTimeslot2 {
				hitBuyerSlot2 = true
			}
		}
		if !hitBuyerSlot1 || !hitBuyerSlot2 {
			return lcos_error.NewLCOSError(lcos_error.ParamsError, "uparcel pickup config must contains timeslot sequence 1 and 2")
		}
	}

	pickupGroupID := request.PickupGroupID
	destinationRegion := request.DestinationRegion
	// 获取pickup group，验证正确性
	pickupGroup, err := l.pickupGroupDao.GetPickupGroupByPickupGroupIDAndDestinationRegionAndClientType(ctx, pickupGroupID, destinationRegion, constant.LFS)
	if err != nil {
		return err
	}
	if pickupGroup == nil {
		return lcos_error.NewLCOSError(lcos_error.NotFoundPickupGroupErrorCode, fmt.Sprintf("cannot find pickup group|pickup_group_id=%s", request.PickupGroupID))
	}
	maxTimeslotValue, err := l.pickupTimeslotDao.GetMaxTimeslotValueByPickupGroupIdAndDestinationRegion(ctx, pickupGroupID, destinationRegion)
	if err != nil {
		return err
	}
	timeslotValue := maxTimeslotValue + 1 // 新数据的timeslot value最小从1开始

	timeslots := make([]*timeslot.PickupTimeslotTab, len(request.Timeslots))
	timeslotsDayMap := make(map[int][]*timeslot.PickupTimeslotTab) // group by day, to check overlap
	MONDAY, SUNDAY := 1, 7                                         // set monday to 1 and sunday to 7
	for _index, _timeslot := range request.Timeslots {

		// SPLN-24112
		// if timeslot type is 0, applied days need to be empty.
		// if timeslot type is 1, applied days need to be non-empty
		if request.TimeslotType == constant.NormalTimeslotType {
			if len(_timeslot.AppliedDaysList) > 0 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "applied days need to be empty when timeslot type is normal type")
			}
		} else if request.TimeslotType == constant.ByDayTimeslotType {
			if len(_timeslot.AppliedDaysList) == 0 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "applied days cannot be empty when timeslot type is by day type")
			}
		} else {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "timeslot type has to be one of 0 and 1")
		}

		tmpTimeslot := &timeslot.PickupTimeslotTab{
			PickupGroupID:     request.PickupGroupID,
			DestinationRegion: request.DestinationRegion,
			StartTimeHour:     uint32(_timeslot.StartTimeHour),
			StartTimeMin:      uint32(_timeslot.StartTimeMin),
			EndTimeHour:       uint32(_timeslot.EndTimeHour),
			EndTimeMin:        uint32(_timeslot.EndTimeMin),
			TimeslotRemark:    _timeslot.TimeslotRemark,
			EnableStatus:      constant.ENABLED,
			AppliedDaysList:   _timeslot.AppliedDaysList,
			TimeslotType:      uint32(request.TimeslotType),
			PickupConfId:      request.PickupConfId,
		}
		if !request.ResetTimeslotValue && _timeslot.TimeslotValue != nil && pickup.IsValidTimeslotValue(_timeslot.TimeslotRemark, *_timeslot.TimeslotValue) {
			tmpTimeslot.TimeslotValue = *_timeslot.TimeslotValue
		} else {
			tmpTimeslot.TimeslotValue = timeslotValue
			timeslotValue++
		}
		if _timeslot.StartTimeSecond != nil {
			tmpTimeslot.StartTimeSecond = uint32(*_timeslot.StartTimeSecond)
		}
		if _timeslot.EndTimeSecond != nil {
			tmpTimeslot.EndTimeSecond = uint32(*_timeslot.EndTimeSecond)
		}
		if _timeslot.SlotStartHour != nil {
			tmpTimeslot.SlotStartHour = uint32(*_timeslot.SlotStartHour)
		}
		if _timeslot.SlotCutoffHour != nil {
			tmpTimeslot.SlotCutoffHour = *_timeslot.SlotCutoffHour
		}
		if _timeslot.SlotCutoffMin != nil {
			tmpTimeslot.SlotCutoffMin = uint32(*_timeslot.SlotCutoffMin)
		}
		timeslots[_index] = tmpTimeslot

		// store it in map for overlap check
		if _timeslot.TimeslotRemark != "Now" {
			if request.TimeslotType == constant.NormalTimeslotType {
				timeslotsDayMap[0] = append(timeslotsDayMap[0], tmpTimeslot)
			} else {
				for _, day := range _timeslot.AppliedDaysList {
					if day < MONDAY || day > SUNDAY {
						return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "weekday need to be between monday and sunday")
					}
					timeslotsDayMap[day] = append(timeslotsDayMap[day], tmpTimeslot)
				}
			}
		}
	}

	// group timeslots by day, check overlap
	for _, timeslotsList := range timeslotsDayMap {
		if isTimeslotsOverlap(timeslotsList) {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "timeslot overlap")
		}
	}

	fc := func() *lcos_error.LCOSError {

		// 先删除当前所有的timeslots
		if lcosErr := l.pickupTimeslotDao.DeleteByPickupGroupIDAndDestinationRegion(ctx, pickupGroupID, destinationRegion, request.PickupConfId); lcosErr != nil {
			return lcosErr
		}

		// 实际的更新操作，只有当timeslots的数量大于0时才做更新操作
		if len(request.Timeslots) > 0 {
			if _, lcosErr := l.pickupTimeslotDao.CreateTimeslot(ctx, timeslots); lcosErr != nil {
				return lcosErr
			}
		}
		return nil
	}
	if err := ctx.Transaction(fc); err != nil {
		return err
	}
	return nil
}

func (l *PickupTimeslotService) Delete(ctx utils.LCOSContext, request *timeslot2.DeleteTimeslotRequest) *lcos_error.LCOSError {
	return l.pickupTimeslotDao.Delete(ctx, request.Id)
}

func (l *PickupTimeslotService) List(ctx utils.LCOSContext, request *timeslot2.ListTimeslotRequest) ([]*timeslot.PickupTimeslotTab, *lcos_error.LCOSError) {
	var pickupConfId uint64
	if request.PickupConfId != 0 {
		pickupConf, err := l.pickupConfDao.GetPickupConfigById(ctx, request.PickupConfId)
		if err != nil {
			return nil, err
		}
		if pickupConf.UseTimeslotType == pickup_window_constant.PickupConfTimeslot {
			pickupConfId = request.PickupConfId
		}
	}

	models, lcosErr := l.pickupTimeslotDao.List(ctx, map[string]interface{}{"pickup_group_id": request.PickupGroupID, "destination_region": request.DestinationRegion, "pickup_conf_id": pickupConfId})
	if lcosErr != nil {
		return nil, lcosErr
	}
	return models, nil
}
