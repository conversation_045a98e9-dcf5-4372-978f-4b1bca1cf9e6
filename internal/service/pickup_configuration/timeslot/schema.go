package timeslot

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"

type (
	GetPickupConfResponse struct {
		PageNO uint32              `json:"pageno"`
		Total  uint32              `json:"total"`
		Count  uint32              `json:"count"`
		List   []*SinglePickupConf `json:"list"`
	}

	// 补充line相关信息
	SinglePickupConf struct {
		*pickup_config.PickupConfTab
		LineId     string `json:"line_id"`
		LineName   string `json:"line_name"`
		LineType   uint32 `json:"line_type"`
		PickupType uint8  `json:"pickup_type"`
	}
)
