package order_account_mapping

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/spx_serviceable_area_protocol"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spx_service"
	lls_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
)

type OrderAccountMappingService interface {
	CreateOrderAccountMapping(ctx utils.LCOSContext, region string, req *protocol.CreateOrderAccountMappingRequest) *lcos_error.LCOSError
	UpdateOrderAccountMapping(ctx utils.LCOSContext, region string, req *protocol.UpdateOrderAccountMappingReqeust) *lcos_error.LCOSError
	DeleteOrderAccountMapping(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError
	ListOrderAccountMappingByPaging(ctx utils.LCOSContext, region string, req *protocol.ListOrderAccountMappingRequest) ([]*model.OrderAccountMapping, uint32, *lcos_error.LCOSError)
	GetOrderAccountMappingByLineAndGroup(ctx utils.LCOSContext, region, lineId, groupId string) ([]*model.OrderAccountMapping, *lcos_error.LCOSError)
	GetOrderAccountMappingByOrderAccount(ctx utils.LCOSContext, region string, orderAccount int) ([]*model.OrderAccountMapping, *lcos_error.LCOSError)
	BatchGetOrderAccountMappingByOrderAccount(ctx utils.LCOSContext, region string, orderAccountList []int) ([]*model.OrderAccountMapping, *lcos_error.LCOSError)
}

type orderAccountMappingService struct {
	dao model.OrderAccountMappingDao
}

func NewOrderAccountMappingService(dao model.OrderAccountMappingDao) *orderAccountMappingService {
	return &orderAccountMappingService{
		dao: dao,
	}
}

func (o *orderAccountMappingService) CreateOrderAccountMapping(ctx utils.LCOSContext, region string, req *protocol.CreateOrderAccountMappingRequest) *lcos_error.LCOSError {
	mapping := &model.OrderAccountMapping{
		Region:       region,
		LineId:       req.GetLineId(),
		GroupId:      req.GetGroupId(),
		EnableStatus: req.GetEnableStatus(),
		Operator:     ctx.GetUserEmail(),
	}

	orderAccount, err := spx_service.NewSpxFleetOrderService(ctx, region).GetOrderAccountById(ctx, req.GetOrderAccount())
	if err != nil {
		return err
	}
	mapping.OrderAccount = orderAccount.OrderAccountId
	mapping.OrderAccountName = orderAccount.OrderAccountName

	return o.dao.CreateOrderAccountMapping(ctx, mapping)
}

func (o *orderAccountMappingService) UpdateOrderAccountMapping(ctx utils.LCOSContext, region string, req *protocol.UpdateOrderAccountMappingReqeust) *lcos_error.LCOSError {
	updateMap := make(map[string]interface{})
	if req.LineId != nil {
		updateMap["line_id"] = req.GetLineId()
	}
	if req.GroupId != nil {
		updateMap["group_id"] = req.GetGroupId()
	}
	if req.OrderAccount != nil {
		orderAccount, err := spx_service.NewSpxFleetOrderService(ctx, region).GetOrderAccountById(ctx, req.GetOrderAccount())
		if err != nil {
			return err
		}
		updateMap["order_account"] = orderAccount.OrderAccountId
		updateMap["order_account_name"] = orderAccount.OrderAccountName
	}
	if req.EnableStatus != nil {
		updateMap["enable_status"] = req.GetEnableStatus()
	}
	updateMap["operator"] = ctx.GetUserEmail()
	return o.dao.UpdateOrderAccountMapping(ctx, req.GetMappingId(), updateMap)
}

func (o *orderAccountMappingService) DeleteOrderAccountMapping(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return o.dao.DeleteOrderAccountMapping(ctx, id)
}

func (o *orderAccountMappingService) ListOrderAccountMappingByPaging(ctx utils.LCOSContext, region string, req *protocol.ListOrderAccountMappingRequest) ([]*model.OrderAccountMapping, uint32, *lcos_error.LCOSError) {
	queryParams := make(map[string]interface{})
	queryParams["region"] = region
	if req.LineId != nil {
		queryParams["line_id"] = req.GetLineId()
	}
	if req.GroupId != nil {
		queryParams["group_id"] = req.GetGroupId()
	}
	if req.OrderAccount != nil {
		queryParams["order_account"] = req.GetOrderAccount()
	}

	ret, total, err := o.dao.ListOrderAcccountMappingByPaging(ctx, queryParams, req.PageNo, req.PageSize)
	if err != nil {
		return nil, 0, err
	}

	lineIdMap := make(map[string]bool)
	var lineIdSet []string
	for _, mapping := range ret {
		if _, ok := lineIdMap[mapping.LineId]; !ok {
			lineIdMap[mapping.LineId] = true
			lineIdSet = append(lineIdSet, mapping.LineId)
		}
	}

	// 填充line name，查不到不影响主流程
	lineInfoMap, _ := lls_service.BatchGetLineInfosMap(ctx, lineIdSet)
	if lineInfoMap == nil {
		lineInfoMap = make(map[string]*lls_protobuf.GetLineInfoResponseData)
	}
	lineDraftMap, _ := lls_service.BatchGetLineDraftsMap(ctx, lineIdSet)
	for lineId, lineInfo := range lineDraftMap {
		lineInfoMap[lineId] = lineInfo
	}

	for _, mapping := range ret {
		if lineInfo, ok := lineInfoMap[mapping.LineId]; ok {
			mapping.LineName = lineInfo.GetLineName()
		}
	}

	return ret, total, nil
}

func (o *orderAccountMappingService) GetOrderAccountMappingByLineAndGroup(ctx utils.LCOSContext, region, lineId, groupId string) ([]*model.OrderAccountMapping, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":        region,
		"line_id":       lineId,
		"group_id":      groupId,
		"enable_status": constant.ENABLED,
	}
	return o.dao.ListOrderAcccountMapping(ctx, queryParams)
}

func (o *orderAccountMappingService) GetOrderAccountMappingByOrderAccount(ctx utils.LCOSContext, region string, orderAccount int) ([]*model.OrderAccountMapping, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":        region,
		"order_account": orderAccount,
		"enable_status": constant.ENABLED,
	}
	return o.dao.ListOrderAcccountMapping(ctx, queryParams)
}

func (o *orderAccountMappingService) BatchGetOrderAccountMappingByOrderAccount(ctx utils.LCOSContext, region string, orderAccountList []int) ([]*model.OrderAccountMapping, *lcos_error.LCOSError) {
	queryParams := map[string]interface{}{
		"region":           region,
		"order_account in": orderAccountList,
		"enable_status":    constant.ENABLED,
	}
	return o.dao.ListOrderAcccountMapping(ctx, queryParams)
}
