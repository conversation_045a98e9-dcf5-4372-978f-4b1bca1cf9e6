package spx_serviceable_area_compare

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/spx_serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/spx_serviceable_area_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_compare"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lls_service"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"time"
)

type SpxServiceableAreaCompareService interface {
	ListSpxServiceableAreaCompareTaskWithPaging(ctx utils.LCOSContext, region string, req *protocol.ListSpxServiceableAreaCompareTaskRequest) ([]*protocol.SpxServiceableAreaCompareTaskResponse, uint32, *lcos_error.LCOSError)
	TriggerSpxServiceableAreaCompare(ctx utils.LCOSContext, taskId uint64, schedluedJobList []uint32) *lcos_error.LCOSError
	CancelSpxServiceableAreaCompare(ctx utils.LCOSContext, taskId uint64) *lcos_error.LCOSError

	GetAllPendingSpxServiceableAreaCompareTasks(ctx utils.LCOSContext) ([]*spx_serviceable_area_compare.SpxServiceableAreaCompareTask, *lcos_error.LCOSError)
	GetAllTimeoutSpxServiceableAreaCompareTasks(ctx utils.LCOSContext) ([]*spx_serviceable_area_compare.SpxServiceableAreaCompareTask, *lcos_error.LCOSError)
	ProcessSpxServiceableAreaCompare(ctx utils.LCOSContext, taskId uint64) *lcos_error.LCOSError
	DoneSpxServiceableAreaCompare(ctx utils.LCOSContext, taskId uint64, status uint8, resultFileUrl, message string) *lcos_error.LCOSError
}

func NewSpxServiceableAreaCompareService(compareTaskDao spx_serviceable_area_compare.SpxServiceableAreaCompareTaskDao, spxVersionDao spx_serviceable_area_version.SpxServiceableAreaVersionDao) *spxServiceableAreaComapreService {
	return &spxServiceableAreaComapreService{
		compareTaskDao: compareTaskDao,
		spxVersionDao:  spxVersionDao,
	}
}

type spxServiceableAreaComapreService struct {
	compareTaskDao spx_serviceable_area_compare.SpxServiceableAreaCompareTaskDao
	spxVersionDao  spx_serviceable_area_version.SpxServiceableAreaVersionDao
}

// ListSpxServiceableAreaCompareTaskWithPaging 分页查询服务范围对比任务
func (s *spxServiceableAreaComapreService) ListSpxServiceableAreaCompareTaskWithPaging(ctx utils.LCOSContext, region string, req *protocol.ListSpxServiceableAreaCompareTaskRequest) ([]*protocol.SpxServiceableAreaCompareTaskResponse, uint32, *lcos_error.LCOSError) {
	// 1. 获取非过期的spx版本（处于Active、Incoming或Scheduled状态）
	nonExpiredVersionQueryParams := map[string]interface{}{
		"region":            region,
		"version_status in": spx_serviceable_constant.AvailableVersionStatus,
	}
	spxVersionList, err := s.spxVersionDao.ListSpxServiceableAreaVersion(ctx, nonExpiredVersionQueryParams)
	if err != nil {
		return nil, 0, err
	}
	versionIdList := make([]uint64, 0, len(spxVersionList))
	spxVersionMap := make(map[uint64]*spx_serviceable_area_version.SpxServiceableAreaVersion, len(spxVersionList))
	for _, version := range spxVersionList {
		versionIdList = append(versionIdList, version.VersionId)
		spxVersionMap[version.VersionId] = version
	}

	// 2. 条件查询对应的对比任务
	queryParams := make(map[string]interface{})
	queryParams["region"] = region
	queryParams["spx_version_id in"] = versionIdList
	if len(req.LineId) != 0 {
		queryParams["line_id"] = req.GetLineId()
	}
	if len(req.GroupId) != 0 {
		queryParams["group_id"] = req.GetGroupId()
	}
	if req.OrderAccount != nil {
		queryParams["order_account"] = req.GetOrderAccount()
	}
	if req.SaType != 0 {
		queryParams["sa_type"] = req.GetSaType()
	}
	if req.CompareStatus != nil {
		queryParams["compare_status"] = req.GetCompareStatus()
	}

	// 从DB获取服务范围对比任务列表
	taskList, total, err := s.compareTaskDao.ListSpxServiceableAreaCompareTaskWithPaging(ctx, queryParams, req.PageNo, req.PageSize)
	if err != nil {
		return nil, 0, err
	}

	// 将服务范围对比任务转换为接口协议返回类型
	ret := make([]*protocol.SpxServiceableAreaCompareTaskResponse, 0, len(taskList))

	var lineIdSet []string
	lineIdMap := make(map[string]bool)
	for _, task := range taskList {
		resp := &protocol.SpxServiceableAreaCompareTaskResponse{
			Id:             task.Id,
			LineId:         task.LineId,
			GroupId:        task.GroupId,
			OrderAccount:   task.OrderAccount,
			SaType:         task.SaType,
			SpxVersionId:   task.SpxVersionId,
			CompareStatus:  task.CompareStatus,
			ErrorMessage:   task.ErrorMessage,
			CompareResult:  task.CompareResult,
			CompareVersion: task.CompareVersion,
			CompleteTime:   task.CompleteTime,
		}
		ret = append(ret, resp)

		if _, ok := lineIdMap[task.LineId]; !ok {
			lineIdSet = append(lineIdSet, task.LineId)
			lineIdMap[task.LineId] = true
		}
	}

	// 从lls获取线的详细信息
	lineInfosMap, _ := lls_service.BatchGetLineInfosMap(ctx, lineIdSet)
	if lineInfosMap == nil {
		lineInfosMap = make(map[string]*llspb.GetLineInfoResponseData)
	}
	lineDraftsMap, _ := lls_service.BatchGetLineDraftsMap(ctx, lineIdSet)
	for lineId, lineDraft := range lineDraftsMap {
		lineInfosMap[lineId] = lineDraft
	}

	// 填充服务范围地址类型、线名称、spx服务范围版本信息到返回结果中
	for _, resp := range ret {
		if lineInfo, ok := lineInfosMap[resp.LineId]; ok {
			resp.LineName = lineInfo.GetLineName()
		}

		if spxVersion, ok := spxVersionMap[resp.SpxVersionId]; ok {
			resp.SpxVersionName = spxVersion.VersionName
			resp.LastVersionId = spxVersion.LastVersion
			resp.LastVersionName = spxVersion.LastVersionName
			resp.SpxEffectiveTime = spxVersion.EffectiveTime
		}
	}

	return ret, total, nil
}

// GetAllPendingSpxServiceableAreaCompareTasks 获取所有处于Pending状态的服务范围对比任务
func (s *spxServiceableAreaComapreService) GetAllPendingSpxServiceableAreaCompareTasks(ctx utils.LCOSContext) ([]*spx_serviceable_area_compare.SpxServiceableAreaCompareTask, *lcos_error.LCOSError) {
	return s.compareTaskDao.ListSpxServiceableAreaCompareTask(ctx, map[string]interface{}{"compare_status": spx_serviceable_constant.CompareStatusPending})
}

// GetAllAbnormalSpxServiceableAreaCompareTasks 获取所有异常的服务范围对比任务
func (s *spxServiceableAreaComapreService) GetAllTimeoutSpxServiceableAreaCompareTasks(ctx utils.LCOSContext) ([]*spx_serviceable_area_compare.SpxServiceableAreaCompareTask, *lcos_error.LCOSError) {
	nowTime := utils.GetTimestamp(ctx)

	queryParams := map[string]interface{}{
		"compare_status": spx_serviceable_constant.CompareStatusProcessing,
		"start_time <=":  nowTime - s.getProcessingTimeout(),
	}
	return s.compareTaskDao.ListSpxServiceableAreaCompareTask(ctx, queryParams)
}

// TriggerSpxServiceableAreaCompare 触发服务范围对比
func (s *spxServiceableAreaComapreService) TriggerSpxServiceableAreaCompare(ctx utils.LCOSContext, taskId uint64, scheduledJobList []uint32) *lcos_error.LCOSError {
	// 获取对比任务
	task, err := s.compareTaskDao.GetSpxServiceableAreaCompareTaskById(ctx, taskId)
	if err != nil {
		return err
	}

	// 状态机检查
	if err := s.checkCanToggleCompareStatus(task.CompareStatus, spx_serviceable_constant.CompareStatusPending); err != nil {
		return err
	}

	updateMap := map[string]interface{}{
		"compare_status":  spx_serviceable_constant.CompareStatusPending,
		"previous_status": task.CompareStatus,
		"compare_version": utils.MarshToStringWithoutError(scheduledJobList),
	}
	return s.compareTaskDao.UpdateSpxServiceableAreaCompareTaskById(ctx, taskId, updateMap)
}

// CancelSpxServiceableAreaCompare 取消服务范围对比
func (s *spxServiceableAreaComapreService) CancelSpxServiceableAreaCompare(ctx utils.LCOSContext, taskId uint64) *lcos_error.LCOSError {
	// 获取对比任务
	task, err := s.compareTaskDao.GetSpxServiceableAreaCompareTaskById(ctx, taskId)
	if err != nil {
		return err
	}

	// 只有处于Pending状态才可以取消
	if task.CompareStatus != spx_serviceable_constant.CompareStatusPending {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "unable to cancel compare task with status %s", spx_serviceable_constant.CompareStatus2TextMap[task.CompareStatus])
	}

	// 状态机检查
	if err := s.checkCanToggleCompareStatus(task.CompareStatus, task.PreviousStatus); err != nil {
		return err
	}

	updateMap := map[string]interface{}{
		"compare_status":  task.PreviousStatus,
		"previous_status": task.CompareStatus,
	}
	return s.compareTaskDao.UpdateSpxServiceableAreaCompareTaskById(ctx, taskId, updateMap)
}

// ProcessSpxServiceableAreaCompare 对比任务开始处理
func (s *spxServiceableAreaComapreService) ProcessSpxServiceableAreaCompare(ctx utils.LCOSContext, taskId uint64) *lcos_error.LCOSError {
	queryParams := map[string]interface{}{
		"id":             taskId,
		"compare_status": spx_serviceable_constant.CompareStatusPending,
	}
	updateMap := map[string]interface{}{
		"compare_status":  spx_serviceable_constant.CompareStatusProcessing,
		"previous_status": spx_serviceable_constant.CompareStatusPending,
		"start_time":      utils.GetTimestamp(ctx),
	}
	return s.compareTaskDao.UpdateSpxServiceableAreaCompareTaskByParams(ctx, queryParams, updateMap)
}

// DoneSpxServiceableAreaCompare 对比任务处理结束
func (s *spxServiceableAreaComapreService) DoneSpxServiceableAreaCompare(ctx utils.LCOSContext, taskId uint64, status uint8, resultFileUrl, message string) *lcos_error.LCOSError {
	queryParams := map[string]interface{}{
		"id":             taskId,
		"compare_status": spx_serviceable_constant.CompareStatusProcessing,
	}
	updateMap := map[string]interface{}{
		"compare_status":  status,
		"previous_status": spx_serviceable_constant.CompareStatusProcessing,
		"error_message":   message,
		"compare_result":  resultFileUrl,
		"complete_time":   utils.GetTimestamp(ctx),
	}
	return s.compareTaskDao.UpdateSpxServiceableAreaCompareTaskByParams(ctx, queryParams, updateMap)
}

// checkCanToggleCompareStatus 服务范围对比任务状态机
func (s *spxServiceableAreaComapreService) checkCanToggleCompareStatus(fromStatus, toStatus uint8) *lcos_error.LCOSError {
	switch fromStatus {
	case spx_serviceable_constant.CompareStatusNew, spx_serviceable_constant.CompareStatusComplete, spx_serviceable_constant.CompareStatusFailed:
		if toStatus == spx_serviceable_constant.CompareStatusPending {
			// 触发对比：
			// New -> Pending
			// 重新对比：
			// Complete -> Pending
			// Failed -> Pending
			return nil
		}
	case spx_serviceable_constant.CompareStatusPending:
		if utils.CheckInUint8(toStatus, []uint8{spx_serviceable_constant.CompareStatusProcessing, spx_serviceable_constant.CompareStatusNew, spx_serviceable_constant.CompareStatusComplete, spx_serviceable_constant.CompareStatusFailed}) {
			// 开始对比：
			// Pending -> Processing
			// 取消对比：
			// Pending -> New
			// Pending -> Complete
			// Pending -> Failed
			return nil
		}
	case spx_serviceable_constant.CompareStatusProcessing:
		if utils.CheckInUint8(toStatus, []uint8{spx_serviceable_constant.CompareStatusComplete, spx_serviceable_constant.CompareStatusFailed}) {
			// 对比完成：
			// Processing -> Complete
			// Processing -> Failed
			return nil
		}
	}
	return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "unable to toggle status from %s to %s", spx_serviceable_constant.CompareStatus2TextMap[fromStatus], spx_serviceable_constant.CompareStatus2TextMap[toStatus])
}

func (s *spxServiceableAreaComapreService) getProcessingTimeout() uint32 {
	timeout := config.GetSpxServiceableAreaCompareConfig().Timeout
	if timeout <= 0 {
		return uint32(time.Hour.Seconds())
	}
	return timeout * uint32(time.Minute.Seconds())
}
