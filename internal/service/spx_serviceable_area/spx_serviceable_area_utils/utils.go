package spx_serviceable_area_utils

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"strings"
)

func CheckRegionIsSupported(region string) bool {
	region = strings.ToUpper(region)
	cfg, ok := config.GetSpxServiceableAreaRegionConfig()[region]
	if !ok {
		return false
	}
	return !cfg.DisableSync
}

func GetLocationType(region string) (uint8, bool) {
	region = strings.ToUpper(region)
	cfg, ok := config.GetSpxServiceableAreaRegionConfig()[region]
	if !ok {
		return 0, false
	}
	return cfg.LocationType, true
}

func IsPostcodeRegion(region string) bool {
	region = strings.ToUpper(region)
	cfg, ok := config.GetSpxServiceableAreaRegionConfig()[region]
	if !ok {
		return false
	}
	return cfg.LocationType == constant.POSTCODE
}

func SkipValidation(region string) bool {
	region = strings.ToUpper(region)
	cfg, ok := config.GetSpxServiceableAreaRegionConfig()[region]
	if !ok {
		return true
	}
	if cfg.DisableSync {
		return true
	}
	return cfg.DisableValidation
}

func ComapreServiceableAreaAbility(slsAbility, spxAbility serviceable_util.ServiceableAreaAbility, dropoffOnly bool) bool {
	pickupEqual := true
	if !dropoffOnly {
		// dropoff only要跳过pickup能力的校验
		pickupEqual = slsAbility.GetCanPickup() == spxAbility.GetCanPickup() && slsAbility.GetCanCodPickup() == spxAbility.GetCanCodPickup()
	}
	return pickupEqual && slsAbility.GetCanDeliver() == spxAbility.GetCanDeliver() && slsAbility.GetCanCodDeliver() == spxAbility.GetCanCodDeliver()
}

func CheckUnsupportedAbilityInSpx(spxAbility, slsAbility serviceable_util.ServiceableAreaAbility, dropoffOnly bool) []string {
	var unsupportedAbility []string
	if !dropoffOnly {
		if spxAbility.GetCanPickup() == constant.FALSE && slsAbility.GetCanPickup() == constant.TRUE {
			unsupportedAbility = append(unsupportedAbility, "'Pickup'")
		}
		if spxAbility.GetCanCodPickup() == constant.FALSE && slsAbility.GetCanCodPickup() == constant.TRUE {
			unsupportedAbility = append(unsupportedAbility, "'COD Pickup'")
		}
	}
	if spxAbility.GetCanDeliver() == constant.FALSE && slsAbility.GetCanDeliver() == constant.TRUE {
		unsupportedAbility = append(unsupportedAbility, "'Deliver'")
	}
	if spxAbility.GetCanCodDeliver() == constant.FALSE && slsAbility.GetCanCodDeliver() == constant.TRUE {
		unsupportedAbility = append(unsupportedAbility, "'COD Deliver'")
	}
	return unsupportedAbility
}
