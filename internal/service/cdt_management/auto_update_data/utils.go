package auto_update_rule

import (
	"context"
	"fmt"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"strconv"
	"strings"
)

func checkCdtMaxOverThreshold(ctx context.Context, cdtMax float64, productID string, cbFlag bool) string {
	threshold := config.GetDataNotifyThreshold(ctx, productID, cbFlag)
	if cdtMax >= float64(threshold) {
		return fmt.Sprintf("The value of CDT exceeds the threshold: %d", threshold)
	}
	return ""
}

func checkDataDayGroupFormat(ctx context.Context, dayGroup string) error {
	dayGroup = strings.ReplaceAll(dayGroup, " ", "")

	if len(dayGroup) == 0 {
		return nil
	}

	strArr := strings.Split(dayGroup, ",")
	var dayList []uint32
	for _, s := range strArr {
		num, err := strconv.ParseUint(s, 10, 32)
		if err != nil {
			return fmt.Errorf("invalid day group[%s]", dayGroup)
		}

		// 如果出现重复的day则报错
		if utils.CheckInUint32(uint32(num), dayList) {
			return fmt.Errorf("duplicated day in day group[%s]", dayGroup)
		}

		// 校验 day 周一 至 周日  1 <= day <= 7
		if num < 1 || num > 7 {
			return fmt.Errorf("invalid day[%s], should within 1~7", dayGroup)
		}

		dayList = append(dayList, uint32(num))
	}

	return nil
}

var ValidDataTimeName = []string{"0-4", "4-8", "8-12", "12-16", "16-20", "20-24"}

func checkDataTimeBucketFormat(ctx context.Context, timeBucket string) error {
	timeBucket = strings.ReplaceAll(timeBucket, " ", "")

	if len(timeBucket) == 0 {
		return nil
	}

	if !utils.ContainsString(ValidDataTimeName, timeBucket) {
		return fmt.Errorf("invalid time bucket:[%s]", timeBucket)
	}

	return nil
}
