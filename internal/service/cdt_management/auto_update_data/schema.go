package auto_update_rule

type DataNotifyError struct {
	OriginLocationID      int
	DestinationLocationID int
	CepRangeInitial       int
	CepRangeFinal         int
	DestinationPostcode   string
	ProductID             string
	LaneID                string
	LeaderTimeMin         float64
	LeaderTimeMax         float64
	UpdateEvent           int
	AutoUpdateID          int
	CdtVersion            int
	ErrorMessage          string
	TimeBucket            string
	DayGroup              string
}

type EmailContent struct {
	EmailTitle string
	Region     string
	ProductID  string
	URL        string
}

// data 通过 cdt_management/auto_update_rule/notify 传来的 csv 格式
type CDTAutoUpdateCalculateData struct {
	CdtVersion            uint32  `json:"cdt_version"`
	AutoUpdateRuleID      uint64  `json:"auto_update_rule_id"`
	ProductID             string  `json:"product_id"`
	LaneCode              string  `json:"lane_code"`
	UpdateEvent           uint8   `json:"update_event"`
	OriginLocationID      int     `json:"origin_location_id"`
	DestinationLocationID int     `json:"destination_location_id"`
	DestinationPostcode   string  `json:"destination_postcode"`
	DestinationCepInitial int     `json:"destination_cep_initial"`
	DestinationCepFinal   int     `json:"destination_cep_final"`
	IsLM                  uint8   `json:"is_lm"`
	TplUniqueKey          string  `json:"tpl_unique_key"`
	OriginRegion          string  `json:"origin_region"`
	DestinationRegion     string  `json:"destination_region"`
	LeadTimeMin           float64 `json:"lead_time_min"`
	LeadTimeMax           float64 `json:"lead_time_max"`
	CBLMLeadTimeMax       float64 `json:"cblm_lead_time_max"`
	DDLForwardCDT         float64 `json:"ddl_forward_cdt"`
	DDLBackwardCDT        float64 `json:"ddl_backward_cdt"`
	TimeBucket            string  `json:"time_bucket"`
	DayGroup              string  `json:"day_group"`
}
