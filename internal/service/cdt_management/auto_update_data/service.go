package auto_update_rule

import (
	"bytes"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/day_group_and_time_bucket_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/item_card_edt_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/emailhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seatalk"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	jsoniter "github.com/json-iterator/go"
	"html/template"
	"path"
	"regexp"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_auto_update_data"
	cdt_common2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
)

type AutoUpdateDataServiceInterface interface {
	// SPLN-24104: cdt calcualte at lane level, 同时支持product和lane维度cdt的数据导入
	ParseDataFileAndImportData(ctx utils.LCOSContext, request *auto_update_rule.AutoUpdateDataNotify) *lcos_error.LCOSError
	GetCdtCalculationDataByCdtVersion(ctx utils.LCOSContext, autoUpdateRuleID uint64, cdtVersion uint32) ([]*auto_update_data.CDTAutoUpdateCalculateDataTab, *lcos_error.LCOSError)
	// SPLN-24104: cdt calcualte at lane level
	GetLaneCdtDataByCdtVersion(ctx utils.LCOSContext, region string, ruldID uint64, cdtVersion uint32) ([]*lane_auto_update_data.LaneCdtAutoUpdateCalculateDataTab, *lcos_error.LCOSError)
	GetCdtVersionByAutoUpdateRuleID(ctx utils.LCOSContext, autoUpdateRuleID uint64) (uint32, *lcos_error.LCOSError)

	// SPLN-24104: cdt calcualte at lane level, 支持删除lane cdt数据
	// SPLN-21914 delete expired data
	DeleteAutoCalculateData(ctx utils.LCOSContext, region string, batchNum, expiredPeriodNum int, realDelete bool) *lcos_error.LCOSError

	SearchAutoUpdateData(ctx utils.LCOSContext, isSuc uint8, startTime, endTime uint32) ([]*auto_update_data.CDTAutoUpdateCalculateDataVersionTab, *lcos_error.LCOSError)

	SyncAutoUpdateDataToItemCodis(ctx utils.LCOSContext, region string, ruleId uint64) *lcos_error.LCOSError
}

type autoUpdateDataService struct {
	autoUpdateDataDao     auto_update_data.CDTAutoUpdateDataDAO
	laneAutoUpdateDataDao lane_auto_update_data.LaneCdtAutoUpdateDataDAO
	autoUpdateDao         auto_update_rule2.CDTAutoUpdateRuleTabDAO
}

func NewAutoUpdateDataService(autoUpdateDataDao auto_update_data.CDTAutoUpdateDataDAO, laneAutoUpdateDataDao lane_auto_update_data.LaneCdtAutoUpdateDataDAO, autoUpdateDao auto_update_rule2.CDTAutoUpdateRuleTabDAO) *autoUpdateDataService {
	return &autoUpdateDataService{
		autoUpdateDataDao:     autoUpdateDataDao,
		laneAutoUpdateDataDao: laneAutoUpdateDataDao,
		autoUpdateDao:         autoUpdateDao,
	}
}

var _ AutoUpdateDataServiceInterface = (*autoUpdateDataService)(nil)

// SPLN-24104: 支持解析lane cdt数据
func (a *autoUpdateDataService) parseFileToStructData(ctx utils.LCOSContext, fileUrl, region string, fileUrlList []string) (map[uint64][]*auto_update_data.CDTAutoUpdateCalculateDataTab, map[uint64][]*lane_auto_update_data.LaneCdtAutoUpdateCalculateDataTab, map[uint64]uint32, []uint64, []*DataNotifyError, *lcos_error.LCOSError) {
	if len(fileUrl) != 0 {
		fileUrlList = append(fileUrlList, fileUrl)
	}

	var warningList []*DataNotifyError

	// 将csv中的内容传入数据库，见结果存为channel->[]*CDTAutoUpdateCalculateDataTab
	productCdtMap := map[uint64][]*auto_update_data.CDTAutoUpdateCalculateDataTab{}
	laneCdtMap := map[uint64][]*lane_auto_update_data.LaneCdtAutoUpdateCalculateDataTab{}
	// 需要解析channel version
	resultVersionMap := map[uint64]uint32{}
	var autoRuleIDList []uint64        // 用于推送的数据中的自动规则id
	autoRuleIDMap := map[uint64]bool{} // 用于去重

	if config.RegionForbidPickupDropoffCdtInput(ctx, region) {
		logger.CtxLogErrorf(ctx, "mutable_application.forbid_pick_dropoff_cdt_input is true, will not save data if updateEvent in (8,9,10,11)")
	}

	if !config.GetNeedUseGroupDayAndTimeBucketConf(ctx, region, day_group_and_time_bucket_constant.BothEnable) {
		logger.CtxLogErrorf(ctx, "region[%v] DayGroupAndTimeBucketConfig.RegionInfo is false, will not save day group & time bucket data", region)
	}

	var recordKey string // key 值为 product_id+route+lane_code, data 保证相同 key 值一定相邻
	var recordDataList []*CDTAutoUpdateCalculateData

	// 循环遍历整个 fileUrlList, 并记录当前扫到的最新 key
	for _, file := range fileUrlList {
		lcosErr := a.parseSingleFileToStructData(ctx, file, region, &warningList, productCdtMap, laneCdtMap, resultVersionMap, &autoRuleIDList, autoRuleIDMap, &recordKey, &recordDataList)
		if lcosErr != nil {
			return nil, nil, nil, nil, nil, lcosErr
		}
	}

	// 最后一批数据存入 productCdtMap, laneCdtMap
	a.genCDTAutoUpdateCalculateData(ctx, recordDataList, productCdtMap, laneCdtMap)

	return productCdtMap, laneCdtMap, resultVersionMap, autoRuleIDList, warningList, nil
}

func (a *autoUpdateDataService) parseSingleFileToStructData(ctx utils.LCOSContext, file, region string,
	warningList *[]*DataNotifyError,
	productCdtMap map[uint64][]*auto_update_data.CDTAutoUpdateCalculateDataTab,
	laneCdtMap map[uint64][]*lane_auto_update_data.LaneCdtAutoUpdateCalculateDataTab,
	resultVersionMap map[uint64]uint32,
	autoRuleIDList *[]uint64,
	autoRuleIDMap map[uint64]bool,
	recordKey *string,
	recordDataList *[]*CDTAutoUpdateCalculateData) *lcos_error.LCOSError {

	warningListTmp := []*DataNotifyError{}
	autoRuleIDListTmp := []uint64{}
	recordDataListTmp := []*CDTAutoUpdateCalculateData{}

	resultList, lcosErr := cdt_common2.ParseCSVFile(ctx, file)
	if lcosErr != nil {
		return lcosErr
	}

	for _, result := range resultList {
		data, err := parseCDTAutoUpdateCalculateData(ctx, region, result)
		if err != nil {
			return err
		}
		if data == nil {
			continue
		}

		// SPLN-27978
		if errorMsg := checkCdtMaxOverThreshold(ctx, data.LeadTimeMax, data.ProductID, data.OriginRegion != data.DestinationRegion); len(errorMsg) > 0 {
			warningListTmp = append(warningListTmp, &DataNotifyError{
				OriginLocationID:      data.OriginLocationID,
				DestinationLocationID: data.DestinationLocationID,
				CepRangeInitial:       data.DestinationCepInitial,
				CepRangeFinal:         data.DestinationCepFinal,
				DestinationPostcode:   data.DestinationPostcode,
				ProductID:             data.ProductID,
				LaneID:                data.LaneCode,
				LeaderTimeMin:         data.LeadTimeMin,
				LeaderTimeMax:         data.LeadTimeMax,
				UpdateEvent:           int(data.UpdateEvent),
				AutoUpdateID:          int(data.AutoUpdateRuleID),
				CdtVersion:            int(data.CdtVersion),
				ErrorMessage:          errorMsg,
				TimeBucket:            data.TimeBucket,
				DayGroup:              data.DayGroup,
			})
		}

		// 检查当前的autoUpdateRuleID是否存在不同版本号
		if preVersion, ok := resultVersionMap[data.AutoUpdateRuleID]; !ok {
			resultVersionMap[data.AutoUpdateRuleID] = data.CdtVersion
		} else {
			if preVersion != data.CdtVersion {
				logger.CtxLogErrorf(ctx, "[auto update data]auto update rule id:[%v] has different versions", data.AutoUpdateRuleID)
			}
		}

		// 去重自动更新规则id
		if _, ok := autoRuleIDMap[data.AutoUpdateRuleID]; !ok {
			autoRuleIDMap[data.AutoUpdateRuleID] = true
			autoRuleIDListTmp = append(autoRuleIDListTmp, data.AutoUpdateRuleID)
		}

		route := fmt.Sprintf("%v_%v_%v_%v_%v_%v_%v", data.OriginRegion, data.OriginLocationID, data.DestinationRegion, data.DestinationLocationID, data.DestinationCepInitial, data.DestinationCepFinal, data.DestinationPostcode)
		tmpKey := fmt.Sprintf("%v:%v:%v:%v", data.ProductID, route, data.LaneCode, data.UpdateEvent)

		if len(*recordKey) != 0 && tmpKey != *recordKey {
			// 将当前 record 数据存入 productCdtMap, laneCdtMap
			a.genCDTAutoUpdateCalculateData(ctx, recordDataListTmp, productCdtMap, laneCdtMap)

			// 刷新 recordDataList
			recordDataListTmp = []*CDTAutoUpdateCalculateData{}
		}

		recordDataListTmp = append(recordDataListTmp, data)
		recordKey = &tmpKey
	}

	*warningList = append(*warningList, warningListTmp...)
	*autoRuleIDList = append(*autoRuleIDList, autoRuleIDListTmp...)
	*recordDataList = append(*recordDataList, recordDataListTmp...)

	return nil
}

func (a *autoUpdateDataService) genCDTAutoUpdateCalculateData(ctx utils.LCOSContext, dataList []*CDTAutoUpdateCalculateData, productCdtMap map[uint64][]*auto_update_data.CDTAutoUpdateCalculateDataTab, laneCdtMap map[uint64][]*lane_auto_update_data.LaneCdtAutoUpdateCalculateDataTab) {
	if len(dataList) == 0 {
		return
	}

	// 通过 day_group 和 time_bucket 聚合数据
	extraData, routeLeadTimeMin, routeLeadTimeMax, routeDDLBackwardCDT := a.mergeCDTAutoUpdateCalculateDataExtraData(ctx, dataList)

	// 同个 product_id+route+lane_code 下，只有 leadTimeMin, leadTimeMax, ddlBackwardCDT 会不一致，需要聚合
	data := dataList[0]
	if data.LaneCode == "" {
		productCdtData := &auto_update_data.CDTAutoUpdateCalculateDataTab{
			AutoUpdateRuleID:      data.AutoUpdateRuleID,
			ProductID:             data.ProductID,
			UpdateEvent:           data.UpdateEvent,
			IsLM:                  data.IsLM,
			TplUniqueKey:          data.TplUniqueKey,
			OriginRegion:          data.OriginRegion,
			OriginLocationID:      data.OriginLocationID,
			DestinationRegion:     data.DestinationRegion,
			DestinationLocationID: data.DestinationLocationID,
			DestinationCepInitial: data.DestinationCepInitial,
			DestinationCepFinal:   data.DestinationCepFinal,
			DestinationPostcode:   data.DestinationPostcode,
			LeadTimeMin:           routeLeadTimeMin,
			LeadTimeMax:           routeLeadTimeMax,
			CBLMLeadTimeMax:       data.CBLMLeadTimeMax,
			CdtVersion:            data.CdtVersion,
			DDLForwardCDT:         data.DDLForwardCDT,
			DDLBackwardCDT:        routeDDLBackwardCDT,
			CdtExtraData:          extraData,
		}
		if _, ok := productCdtMap[data.AutoUpdateRuleID]; !ok {
			productCdtMap[data.AutoUpdateRuleID] = []*auto_update_data.CDTAutoUpdateCalculateDataTab{}
		}
		productCdtMap[data.AutoUpdateRuleID] = append(productCdtMap[data.AutoUpdateRuleID], productCdtData)
	} else {
		laneCdtData := &lane_auto_update_data.LaneCdtAutoUpdateCalculateDataTab{
			AutoUpdateRuleID:      data.AutoUpdateRuleID,
			ProductID:             data.ProductID,
			LaneCode:              data.LaneCode,
			UpdateEvent:           data.UpdateEvent,
			IsLM:                  data.IsLM,
			TplUniqueKey:          data.TplUniqueKey,
			OriginRegion:          data.OriginRegion,
			OriginLocationID:      data.OriginLocationID,
			DestinationRegion:     data.DestinationRegion,
			DestinationLocationID: data.DestinationLocationID,
			DestinationCepInitial: data.DestinationCepInitial,
			DestinationCepFinal:   data.DestinationCepFinal,
			DestinationPostcode:   data.DestinationPostcode,
			LeadTimeMin:           routeLeadTimeMin,
			LeadTimeMax:           routeLeadTimeMax,
			CBLMLeadTimeMax:       data.CBLMLeadTimeMax,
			CdtVersion:            data.CdtVersion,
			DDLForwardCDT:         data.DDLForwardCDT,
			DDLBackwardCDT:        routeDDLBackwardCDT,
			CdtExtraData:          extraData,
		}
		if _, ok := laneCdtMap[data.AutoUpdateRuleID]; !ok {
			laneCdtMap[data.AutoUpdateRuleID] = []*lane_auto_update_data.LaneCdtAutoUpdateCalculateDataTab{}
		}
		laneCdtMap[data.AutoUpdateRuleID] = append(laneCdtMap[data.AutoUpdateRuleID], laneCdtData)
	}
}

func (a *autoUpdateDataService) mergeCDTAutoUpdateCalculateDataExtraData(ctx utils.LCOSContext, dataList []*CDTAutoUpdateCalculateData) (string, float64, float64, float64) {
	dayGroupMap := map[string]*common_utils.DayGroup{}
	var routeCdtMin float64
	var routeCdtMax float64
	var routeDDLBackwardCDT float64

	for _, data := range dataList {
		if _, ok := dayGroupMap[data.DayGroup]; !ok {
			dayGroupMap[data.DayGroup] = &common_utils.DayGroup{}
		}

		if len(data.DayGroup) == 0 {
			if len(data.TimeBucket) == 0 {
				// route 级别数据
				routeCdtMin = data.LeadTimeMin
				routeCdtMax = data.LeadTimeMax
				routeDDLBackwardCDT = data.DDLBackwardCDT

			} else {
				// 纯 time_bucket 级别数据
				timeBucket := &common_utils.TimeBucket{
					DataTimeName: data.TimeBucket,
					LeadTimeMin:  data.LeadTimeMin,
					LeadTimeMax:  data.LeadTimeMax,
					DDLBackward:  data.DDLBackwardCDT,
				}
				if err := timeBucket.InitDataHour(); err != nil {
					logger.CtxLogErrorf(ctx, "mergeCDTAutoUpdateCalculateDataExtraData fail, err: %s", err.Error())
					continue
				}

				dayGroupMap[data.DayGroup].TimeBuckets = append(dayGroupMap[data.DayGroup].TimeBuckets, timeBucket)

				// 纯 time_bucket 级别数据，默认 day_group 为 [1,2,3,4,5,6,7]
				dayGroupMap[data.DayGroup].Days = []uint32{1, 2, 3, 4, 5, 6, 7}
			}

		} else {
			// 赋值 day_group 级别 days
			if len(dayGroupMap[data.DayGroup].Days) == 0 {
				dayGroupMap[data.DayGroup].DayName = data.DayGroup
				if err := dayGroupMap[data.DayGroup].InitDays(); err != nil {
					logger.CtxLogErrorf(ctx, "mergeCDTAutoUpdateCalculateDataExtraData fail, err: %s", err.Error())
					continue
				}
			}

			if len(data.TimeBucket) == 0 {
				// 纯 day_group 级别数据
				dayGroupMap[data.DayGroup].LeadTimeMin = data.LeadTimeMin
				dayGroupMap[data.DayGroup].LeadTimeMax = data.LeadTimeMax
				dayGroupMap[data.DayGroup].DDLBackward = data.DDLBackwardCDT

			} else {
				// time_bucket + day_group 级别数据
				timeBucket := &common_utils.TimeBucket{
					DataTimeName: data.TimeBucket,
					LeadTimeMin:  data.LeadTimeMin,
					LeadTimeMax:  data.LeadTimeMax,
					DDLBackward:  data.DDLBackwardCDT,
				}
				if err := timeBucket.InitDataHour(); err != nil {
					logger.CtxLogErrorf(ctx, "mergeCDTAutoUpdateCalculateDataExtraData fail, err: %s", err.Error())
					continue
				}

				dayGroupMap[data.DayGroup].TimeBuckets = append(dayGroupMap[data.DayGroup].TimeBuckets, timeBucket)
			}
		}
	}

	var cdtExtraData common_utils.CdtExtraData
	for _, dayGroup := range dayGroupMap {
		if len(dayGroup.Days) == 0 {
			continue
		}
		cdtExtraData.DayGroups = append(cdtExtraData.DayGroups, dayGroup)
	}

	extraData, err := jsoniter.Marshal(cdtExtraData)
	if err != nil {
		logger.CtxLogErrorf(ctx, "marsh extra data fail, extra_data: %+v", cdtExtraData)
		return "{}", routeCdtMin, routeCdtMax, routeDDLBackwardCDT
	}

	return string(extraData), routeCdtMin, routeCdtMax, routeDDLBackwardCDT
}

func (a *autoUpdateDataService) generateWarningReport(ctx utils.LCOSContext, warningList []*DataNotifyError, region string) (string, *lcos_error.LCOSError) {
	templateFilePath := path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/cdt/", warningReportFileName)
	// 打开模板
	f, err := excelize.OpenFile(templateFilePath)
	if err != nil {
		errMsg := fmt.Sprintf("cannot oepn file: %s", templateFilePath)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}
	for index, result := range warningList {
		originLocation := &address_service.LocationInfo{Country: region}
		destinationLocation := &address_service.LocationInfo{Country: region}
		var lcosErr *lcos_error.LCOSError
		if result.OriginLocationID != 0 {
			originLocation, lcosErr = address_service.LocationServer.GetLocationInfoById(ctx, result.OriginLocationID)
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, lcosErr.Msg)
				continue
			}
		}
		if result.DestinationLocationID != 0 {
			destinationLocation, lcosErr = address_service.LocationServer.GetLocationInfoById(ctx, result.DestinationLocationID)
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, lcosErr.Msg)
				continue
			}
		}

		updateEventString, err1 := edd_constant.GetUpdateEventString(uint8(result.UpdateEvent))
		if err1 != nil {
			logger.CtxLogErrorf(ctx, err1.Error())
			continue
		}

		// 从第二行开始写
		var errorList []error
		cepRangeLeftString := ""
		cepRangeRightString := ""
		if result.CepRangeInitial != 0 {
			cepRangeLeftString = strconv.Itoa(result.CepRangeInitial)
		}
		if result.CepRangeFinal != 0 {
			cepRangeRightString = strconv.Itoa(result.CepRangeFinal)
		}
		errorList = append(errorList,
			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", index+2), originLocation.Country),
			f.SetCellValue("Sheet1", fmt.Sprintf("B%d", index+2), originLocation.State),
			f.SetCellValue("Sheet1", fmt.Sprintf("C%d", index+2), originLocation.City),
			f.SetCellValue("Sheet1", fmt.Sprintf("D%d", index+2), originLocation.District),
			f.SetCellValue("Sheet1", fmt.Sprintf("E%d", index+2), destinationLocation.Country),
			f.SetCellValue("Sheet1", fmt.Sprintf("F%d", index+2), destinationLocation.State),
			f.SetCellValue("Sheet1", fmt.Sprintf("G%d", index+2), destinationLocation.City),
			f.SetCellValue("Sheet1", fmt.Sprintf("H%d", index+2), destinationLocation.District),
			f.SetCellValue("Sheet1", fmt.Sprintf("I%d", index+2), cepRangeLeftString),
			f.SetCellValue("Sheet1", fmt.Sprintf("J%d", index+2), cepRangeRightString),
			f.SetCellValue("Sheet1", fmt.Sprintf("K%d", index+2), result.DestinationPostcode),
			f.SetCellValue("Sheet1", fmt.Sprintf("L%d", index+2), result.ProductID),
			f.SetCellValue("Sheet1", fmt.Sprintf("M%d", index+2), result.LaneID),
			f.SetCellValue("Sheet1", fmt.Sprintf("N%d", index+2), result.LeaderTimeMin),
			f.SetCellValue("Sheet1", fmt.Sprintf("O%d", index+2), result.LeaderTimeMax),
			f.SetCellValue("Sheet1", fmt.Sprintf("P%d", index+2), updateEventString),
			f.SetCellValue("Sheet1", fmt.Sprintf("Q%d", index+2), result.AutoUpdateID),
			f.SetCellValue("Sheet1", fmt.Sprintf("R%d", index+2), result.CdtVersion),
			f.SetCellValue("Sheet1", fmt.Sprintf("S%d", index+2), result.ErrorMessage),
			f.SetCellValue("Sheet1", fmt.Sprintf("T%d", index+2), result.TimeBucket),
			f.SetCellValue("Sheet1", fmt.Sprintf("U%d", index+2), result.DayGroup),
		)
		var realError error
		for _, singleErr := range errorList {
			if singleErr != nil {
				realError = singleErr
				break
			}
		}
		if realError != nil {
			errMsg := fmt.Sprintf("cannot write row|content=[%s]|error=[%s]", utils.MarshToStringWithoutError(result), realError.Error())
			logger.CtxLogErrorf(ctx, errMsg)
			return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
		}
	}
	// 以当前的时间戳生成临时文件
	tmpFileName := fmt.Sprintf("/tmp/%s-%s.xlsx", "Attachment-for-Abnormal-Auto-CDT", pickup.GetCurrentTime(ctx, region).Format("20060102150405"))

	// 文件名中，不能有空格
	reg := regexp.MustCompile(" +")
	tmpFileName = reg.ReplaceAllString(tmpFileName, "-")

	err = f.SaveAs(tmpFileName)
	if err != nil {
		logger.CtxLogErrorf(ctx, err.Error())
		return "", lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return tmpFileName, nil

}

func parseCDTAutoUpdateCalculateData(ctx utils.LCOSContext, region string, result []string) (*CDTAutoUpdateCalculateData, *lcos_error.LCOSError) {
	// SPLN-24104: csv文件末尾新增一列，lane_code
	// SPLN-26162: add update event
	if len(result) < 17 {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "csv file has to get at least 17 columns")
	}
	autoUpdateRuleID, err := strconv.Atoi(result[0])
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
	}

	productID := result[1]
	if productID == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "product_id cannot be empty")
	}

	isLM, err := strconv.Atoi(result[2])
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
	}

	tplUniqueKey := result[3]
	originRegion := result[4]
	if originRegion == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "origin region cannot be empty")
	}

	originLocationID, err := strconv.Atoi(result[5])
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
	}

	destinationRegion := result[6]
	if destinationRegion == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "destination region cannot be empty")
	}

	destinationLocationID, err := strconv.Atoi(result[7])
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
	}

	var destinationCepLeft int
	if result[8] == "" {
		destinationCepLeft = 0
	} else {
		destinationCepLeft, err = strconv.Atoi(result[8])
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}
	}

	var destinationCepRight int
	if result[9] == "" {
		destinationCepRight = 0
	} else {
		destinationCepRight, err = strconv.Atoi(result[9])
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}
	}

	var leadTimeMin float64
	if result[10] != "" {
		leadTimeMin, err = strconv.ParseFloat(result[10], 64)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}
	}

	leadTimeMax, err := strconv.ParseFloat(result[11], 64)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
	}

	cblmLeadTimeMax, err := strconv.ParseFloat(result[12], 64)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
	}

	cdtVersion, err := strconv.Atoi(result[13])
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
	}

	postcode := strings.TrimSpace(result[14])

	laneCode := strings.ReplaceAll(strings.TrimSpace(result[15]), "/", "|") // 多段式LaneCode拼接格式有"/"和"|"两种，lcos统一存储为"|"格式

	var updateEvent int
	if result[16] != "" {
		updateEvent, err = strconv.Atoi(result[16])
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}

		// 如果熔断开关启用，则updateEvent为(8,9,10,11)的数据直接跳过
		if config.RegionForbidPickupDropoffCdtInput(ctx, region) {
			if utils.CheckInUint8(uint8(updateEvent), edd_constant.PickupDropoffLevelEventEnumList) {
				return nil, nil
			}
		}
	}
	var ddlForwardCDT, ddlBackwardCDT float64
	if len(result) >= 19 {
		ddlForwardCDT, err = strconv.ParseFloat(result[17], 64)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}
		ddlBackwardCDT, err = strconv.ParseFloat(result[18], 64)
		if err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}
	}

	var timeBucket, dayGroup string
	if len(result) >= 21 {
		timeBucket = result[19]
		dayGroup = result[20]

		if !config.GetNeedUseGroupDayAndTimeBucketConf(ctx, region, day_group_and_time_bucket_constant.BothEnable) {
			if len(timeBucket) != 0 || len(dayGroup) != 0 {
				return nil, nil
			}
		}

		if err = checkDataTimeBucketFormat(ctx, timeBucket); err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}

		if err = checkDataDayGroupFormat(ctx, dayGroup); err != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}
	}

	return &CDTAutoUpdateCalculateData{
		AutoUpdateRuleID:      uint64(autoUpdateRuleID),
		ProductID:             productID,
		LaneCode:              laneCode,
		UpdateEvent:           uint8(updateEvent),
		IsLM:                  uint8(isLM),
		TplUniqueKey:          tplUniqueKey,
		OriginRegion:          originRegion,
		OriginLocationID:      originLocationID,
		DestinationRegion:     destinationRegion,
		DestinationLocationID: destinationLocationID,
		DestinationCepInitial: destinationCepLeft,
		DestinationCepFinal:   destinationCepRight,
		DestinationPostcode:   postcode,
		LeadTimeMin:           leadTimeMin,
		LeadTimeMax:           leadTimeMax,
		CBLMLeadTimeMax:       cblmLeadTimeMax,
		CdtVersion:            uint32(cdtVersion),
		DDLForwardCDT:         ddlForwardCDT,
		DDLBackwardCDT:        ddlBackwardCDT,
		TimeBucket:            timeBucket,
		DayGroup:              dayGroup,
	}, nil
}

func (a *autoUpdateDataService) sendMail(ctx utils.LCOSContext, warningList []*DataNotifyError, region string) *lcos_error.LCOSError {
	productID := warningList[0].ProductID

	notifyList := utils.StringSplit(config.GetDataNotifyList(ctx, productID, region), ",")
	if len(notifyList) <= 0 {
		return nil
	}

	attachmentFileName, lcosErr := a.generateWarningReport(ctx, warningList, region)
	if lcosErr != nil {
		return lcosErr
	}
	data := &EmailContent{
		EmailTitle: fmt.Sprintf("[Alert] %s-%s Auto-calculated value is abnormal", region, productID),
		Region:     region,
		ProductID:  productID,
		URL:        utils.GenerateCdtUrl(ctx, region),
	}
	filePath := path.Join(pathutil.GetProjectAbsolutePath(), "templates/html/cdt", warningReportEmail)
	tpl, err := template.ParseFiles(filePath)
	if err != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "error parsing email template file|file_path=%s|error=%s", filePath, err.Error())
	}
	buf := new(bytes.Buffer) //实现了读写方法的可变大小的字节缓冲
	executeErr := tpl.Execute(buf, data)
	if executeErr != nil {
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "error parsing email template file|error=%s", executeErr.Error())
	}

	logger.CtxLogInfof(ctx, "ready to send mail to %s", notifyList)
	emailErr := emailhelper.SendEmailV2(buf.String(), data.EmailTitle, "text/html", attachmentFileName, notifyList, nil, nil)
	if emailErr != nil {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, emailErr.Error())
	}
	return nil
}

// 当更新成功后，删除掉老的数据
func (a *autoUpdateDataService) deleteOldData(ctx utils.LCOSContext, autoRuleID uint64, cdtVersion uint32) *lcos_error.LCOSError {
	return a.autoUpdateDataDao.DeleteCdtInfoByCdtVersion(ctx, autoRuleID, cdtVersion)
}

// 解析成功的返回
func (a *autoUpdateDataService) parseDataFailAndImportDataForCodeZero(ctx utils.LCOSContext, fileUrl string, updateTime uint32, region string, fileUrlList []string) *lcos_error.LCOSError {
	autoRuleMap := map[uint64]*auto_update_rule2.CDTAutoUpdateRuleTab{} // 用于存储auto rule的id映射
	// data传输成功，需要
	// 1. 解析csv文件
	// 2. 更新对应自动规则的next_update_time
	// 3. 创建自动规则的version信息
	// 4. 删除旧数据

	deletedDataVersion := map[uint64]uint32{}

	productCdtMap, laneCdtMap, resultVersionMap, autoRuleIDList, warningList, lcosErr := a.parseFileToStructData(ctx, fileUrl, region, fileUrlList)
	if lcosErr != nil {
		return lcosErr
	}

	// SPLN-27978 check warning list, send email
	if len(warningList) > 0 {
		emailErr := a.sendMail(ctx, warningList, region)
		if emailErr != nil {
			logger.CtxLogErrorf(ctx, emailErr.Msg)
		}
	}

	// 获取对应的自动更新规则
	autoUpdateRules, lcosErr := a.autoUpdateDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"id in": autoRuleIDList, "region": region})
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "[auto update data]:%v", lcosErr.Msg)
		return lcosErr
	}

	// 将auto update rule存储为map，方便更新的时候查询，并且校验这些自动更新规则的next update time都等于传入的update_time，否则需要报错
	for _, rule := range autoUpdateRules {
		if updateTime == 0 || (rule.IsValidUpdateTime(updateTime)) { // 对于首次失败的任务，其next update time可能已经更新
			autoRuleMap[rule.ID] = rule
		} else {
			logger.CtxLogErrorf(ctx, "[auto update data]the next update time for rule:[%v] is [%v] instead of [%v]", rule.ID, rule.NextUpdateTime, updateTime)
		}
	}

	// 循环resultMap创建数据AutoUpdateData数据
	for _autoRuleID, dataList := range productCdtMap {
		// 检查当前自动规则的next update time是否正确，否则需要报错
		if _rule, ok := autoRuleMap[_autoRuleID]; ok {
			lcosErr = a.autoUpdateDataDao.CreateAutoUpdateData(ctx, dataList, _rule)
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "create auto update data error|error=%v", lcosErr.Msg)
				return lcosErr
			}
		}
	}

	// SPLN-24104: 支持导入lane cdt数据
	for _autoRuleID, dataList := range laneCdtMap {
		if _rule, ok := autoRuleMap[_autoRuleID]; ok {
			// 校验自动更新规则cdt类型是否为lane
			if _rule.CdtType == edd_constant.LaneCdtType {
				lcosErr = a.laneAutoUpdateDataDao.BatchCreateLaneAutoUpdateCdtData(ctx, region, dataList, _rule)
				if lcosErr != nil {
					logger.CtxLogErrorf(ctx, "create lane auto udpate data error|error=%v", lcosErr.Msg)
					return lcosErr
				}
			}
		}
	}

	// 更新对应的自动计算规则的next_update_time
	for _autoRuleID, _autoUpdateRule := range autoRuleMap {
		// 首次更新不会更新next update time，但是需要将自动规则的状态修改为draft
		if updateTime != 0 {
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": _autoRuleID, "region": region}, map[string]interface{}{"prev_update_time": _autoUpdateRule.LastUpdateTime, "last_update_time": updateTime, "next_update_time": updateTime + _autoUpdateRule.Frequency*24*3600})
		} else {
			if _autoUpdateRule.StatusID == constant.Calculating {
				lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": _autoRuleID, "region": region}, map[string]interface{}{"status_id": constant.Draft})
			}
		}
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "update auto update rule error|error=%v", lcosErr.Msg)
			return lcosErr
		}
	}

	// 循环创建需要的version信息
	var versionDatas []*auto_update_data.CDTAutoUpdateCalculateDataVersionTab
	for autoRuleID, version := range resultVersionMap {
		if _autoRule, ok := autoRuleMap[autoRuleID]; ok {
			// 首次更新
			if updateTime == 0 {
				versionDatas = append(versionDatas, &auto_update_data.CDTAutoUpdateCalculateDataVersionTab{
					AutoUpdateRuleID: autoRuleID,
					CdtVersion:       version,
					EffectiveTime:    0,
					ExpirationTime:   0,
					IsFirstTime:      constant.TRUE,
					IsSuccess:        constant.TRUE,
				})
			} else {
				versionDatas = append(versionDatas, &auto_update_data.CDTAutoUpdateCalculateDataVersionTab{
					AutoUpdateRuleID: autoRuleID,
					CdtVersion:       version,
					EffectiveTime:    updateTime,
					ExpirationTime:   updateTime + _autoRule.Frequency*24*3600, // 以effective time+frequency*3600*24作为expiration time
					IsFirstTime:      constant.FALSE,
					IsSuccess:        constant.TRUE,
				})
			}
			if version != 0 {
				deletedDataVersion[autoRuleID] = version
			}
		}
	}
	lcosErr = a.autoUpdateDataDao.CreateAutoUpdateDataVersion(ctx, versionDatas)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "create auto update data version error|error=%v", lcosErr.Msg)
		return lcosErr
	}

	return nil
}

// 解析失败的返回
func (a *autoUpdateDataService) parseDataFailAndImportDataForCodeNonZero(ctx utils.LCOSContext, updateTime uint32, region string, ruleId int) *lcos_error.LCOSError {
	// data传输失败，需要
	// 1. 获取next_update_time=updatetime||updatetime+update_time+3600*24的需要更新的自动规则
	// 2. 更新其next_update_time为update_time+3600*24
	// 3. 创建自动规则的version信息失败信息
	// 4. 是第一次失败 则更新auto rule status 为failed 并通知 创建人

	var autoUpdateVersions []*auto_update_data.CDTAutoUpdateCalculateDataVersionTab

	autoUpdateRules, lcosErr := a.autoUpdateDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"next_update_time": updateTime, "region": region, "id": ruleId})
	if lcosErr != nil {
		return lcosErr
	}
	logger.CtxLogInfof(ctx, "search db data is %s", autoUpdateRules)
	// 更新next_update_time为 update_time+frequency*3600*24
	for _, autoUpdateRule := range autoUpdateRules {
		var isFirstTime uint8
		var expirationTime uint32 = 0
		if updateTime != 0 {
			isFirstTime = constant.FALSE
			expirationTime = updateTime + autoUpdateRule.Frequency*3600*24
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": autoUpdateRule.ID, "region": region}, map[string]interface{}{"next_update_time": expirationTime})
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "update auto update rule error|error=%v", lcosErr.Msg)
				return lcosErr
			}

		} else {
			isFirstTime = constant.TRUE
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": autoUpdateRule.ID, "region": region}, map[string]interface{}{"status_id": constant.Failed})
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "update auto update rule error|error=%v", lcosErr.Msg)
				return lcosErr
			}
		}

		autoUpdateVersions = append(autoUpdateVersions, &auto_update_data.CDTAutoUpdateCalculateDataVersionTab{
			AutoUpdateRuleID: autoUpdateRule.ID,
			CdtVersion:       0,
			EffectiveTime:    updateTime,
			ExpirationTime:   expirationTime,
			IsFirstTime:      isFirstTime,
			IsSuccess:        constant.FALSE,
		})
	}

	lcosErr = a.autoUpdateDataDao.CreateAutoUpdateDataVersion(ctx, autoUpdateVersions)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "create auto update data version error|error=%v", lcosErr.Msg)
		return lcosErr
	}
	return nil
}

func (a *autoUpdateDataService) ParseDataFileAndImportData(ctx utils.LCOSContext, request *auto_update_rule.AutoUpdateDataNotify) *lcos_error.LCOSError {
	fileUrl := request.FileUrl
	updateTime := request.UpdateTime
	code := request.Code
	region := request.Region
	ruleId := request.RuleId
	fileUrlList := request.FileUrlList
	logger.CtxLogInfof(ctx, "req code is %d, rule_id is %d , region is %s", code, ruleId, region)
	// 对于需要在update_time更新的数据，当前时间必须要在updateTime-3600*24~updateTime之间
	nowTimestamp := utils.GetTimestamp(ctx)
	if updateTime != 0 && (nowTimestamp < updateTime-3600*24 || nowTimestamp > updateTime) {
		errMsg := fmt.Sprintf("not allowed to update [%v] data at [%v]", updateTime, nowTimestamp)
		logger.CtxLogErrorf(ctx, errMsg)
		_ = metrics.CounterIncr(constant.MetricDataFailureNotify, map[string]string{"rule_id": strconv.Itoa(ruleId), "file_url": fileUrl, "update_time": strconv.Itoa(int(updateTime)), "region": region, "code": strconv.Itoa(code), "error": errMsg, "file_url_list": fmt.Sprintf("%v", fileUrlList)})
		return lcos_error.NewLCOSError(lcos_error.NotAllowedToUpdateDataTimeErrorCode, errMsg)
	}

	if code == 0 {
		lcosErr := a.parseDataFailAndImportDataForCodeZero(ctx, fileUrl, updateTime, region, fileUrlList)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "parse csv file error|error=%v", lcosErr.Msg)
			_ = metrics.CounterIncr(constant.MetricDataFailureNotify, map[string]string{"rule_id": strconv.Itoa(ruleId), "file_url": fileUrl, "update_time": strconv.Itoa(int(updateTime)), "region": region, "code": strconv.Itoa(code), "error": lcosErr.Msg, "file_url_list": fmt.Sprintf("%v", fileUrlList)})
			return lcosErr
		}
	} else {
		lcosErr := a.parseDataFailAndImportDataForCodeNonZero(ctx, updateTime, region, ruleId)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, lcosErr.Msg)
			_ = metrics.CounterIncr(constant.MetricDataFailureNotify, map[string]string{"rule_id": strconv.Itoa(ruleId), "file_url": fileUrl, "update_time": strconv.Itoa(int(updateTime)), "region": region, "code": strconv.Itoa(code), "error": lcosErr.Msg, "file_url_list": fmt.Sprintf("%v", fileUrlList)})
			return lcosErr
		} else {
			_ = metrics.CounterIncr(constant.MetricDataFailureNotify, map[string]string{"rule_id": strconv.Itoa(ruleId), "file_url": fileUrl, "update_time": strconv.Itoa(int(updateTime)), "region": region, "code": strconv.Itoa(code), "error": "DATA calculate failed", "file_url_list": fmt.Sprintf("%v", fileUrlList)})
		}
	}
	return nil
}

func (a *autoUpdateDataService) GetCdtCalculationDataByCdtVersion(ctx utils.LCOSContext, autoUpdateRuleID uint64, cdtVersion uint32) ([]*auto_update_data.CDTAutoUpdateCalculateDataTab, *lcos_error.LCOSError) {
	return a.autoUpdateDataDao.GetCdtInfoByCdtVersion(ctx, autoUpdateRuleID, map[string]interface{}{"cdt_version": cdtVersion, "auto_update_rule_id": autoUpdateRuleID})
}

func (a *autoUpdateDataService) GetLaneCdtDataByCdtVersion(ctx utils.LCOSContext, region string, ruldID uint64, cdtVersion uint32) ([]*lane_auto_update_data.LaneCdtAutoUpdateCalculateDataTab, *lcos_error.LCOSError) {
	return a.laneAutoUpdateDataDao.GetLaneCdtDataByCdtVersion(ctx, region, ruldID, cdtVersion)
}

func (a *autoUpdateDataService) GetCdtVersionByAutoUpdateRuleID(ctx utils.LCOSContext, autoUpdateRuleID uint64) (uint32, *lcos_error.LCOSError) {
	return a.autoUpdateDataDao.GetCdtVersionByAutoUpdateRuleID(ctx, autoUpdateRuleID)
}

// DeleteAutoCalculateData
//
//	@Description: delete expired data
//	@receiver a
//	@param ctx
//	@param autoUpdateRuleID         auto update rule id
//	@param frequency                frequency of auto update rule, in days
//	@param batchNum                 delete batch num
//	@param expiredFrequencyNum      how many periods of frequency will be deleted
//	@return *lcos_error.LCOSError
func (a *autoUpdateDataService) DeleteAutoCalculateData(ctx utils.LCOSContext, region string, batchNum, expiredPeriodNum int, deleteAllowed bool) *lcos_error.LCOSError {
	// 1. find all update rules that are active or disabled
	autoUpdateRules, lcosErr := a.autoUpdateDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"status_id in": []uint8{constant.Active, constant.Disable}})
	if lcosErr != nil {
		return lcosErr
	}

	// 2. find expired cdt versions of each auto rule
	for _, autoUpdateRule := range autoUpdateRules {

		logger.CtxLogInfof(ctx, "ready to clean auto update rule|id=%d", autoUpdateRule.ID)

		expiredVersions, lcosErr := a.autoUpdateDataDao.GetAllExpiredVersions(ctx, autoUpdateRule.ID, int(autoUpdateRule.Frequency), expiredPeriodNum)
		if lcosErr != nil {
			return lcosErr
		}

		if len(expiredVersions) <= 0 {
			logger.CtxLogInfof(ctx, "there are no expired versions|auto_update_id=%d", autoUpdateRule.ID)
			continue
		}

		logger.CtxLogInfof(ctx, "successfully get all expired versions|auto_update_id=%d, expired_version=[%v]", autoUpdateRule.ID, expiredVersions)

		// 3. delete those expired cdt version data by batch num once
		if deleteAllowed {
			lcosErr = a.autoUpdateDataDao.DeleteCdtInfoByQuery(ctx, autoUpdateRule.ID, map[string]interface{}{"auto_update_rule_id": autoUpdateRule.ID, "cdt_version in": expiredVersions}, batchNum)
			if lcosErr != nil {
				return lcosErr
			}
			// SPLN-24104: 删除lane cdt数据
			if autoUpdateRule.CdtType == edd_constant.LaneCdtType {
				lcosErr = a.laneAutoUpdateDataDao.BatchDeleteLaneCdtDataByCdtVersion(ctx, region, autoUpdateRule.ID, expiredVersions)
				if lcosErr != nil {
					return lcosErr
				}
			}
			logger.CtxLogInfof(ctx, "successfully delete %d rows|auto_update_rule_id=%d, expired_version=[%v]", batchNum, autoUpdateRule.ID, expiredVersions)
		} else {
			logger.CtxLogInfof(ctx, "not allowed to delete cdt version data|auto_update_rule_id=%d", autoUpdateRule.ID)
		}

		// 4. check whether cdt version data has been completely deleted, if so, delete cdt version
		for _, cdtVersion := range expiredVersions {
			// SPLN-24104: 需要同时能校验lane维度cdt数据是否有残留
			if exist, lcosErr := a.autoUpdateDataDao.CheckCdtVersionDataExist(ctx, region, autoUpdateRule.ID, cdtVersion); lcosErr == nil {
				if exist {
					logger.CtxLogInfof(ctx, "cdt_version:[%d] data exists, not allow to delete|auto_update_rule_id=%d", cdtVersion, autoUpdateRule.ID)
				} else {
					if deleteAllowed {
						lcosErr = a.autoUpdateDataDao.DeleteCdtVersion(ctx, map[string]interface{}{"auto_update_rule_id": autoUpdateRule.ID, "cdt_version": cdtVersion})
						if lcosErr != nil {
							return lcosErr
						}
						logger.CtxLogInfof(ctx, "successfully delete cdt_version:[%d]|auto_update_id=%d", cdtVersion, autoUpdateRule.ID)
					} else {
						logger.CtxLogInfof(ctx, "not allowed to delete cdt version:[%d]|auto_update_rule_id=%d", cdtVersion, autoUpdateRule.ID)
					}
				}
			} else {
				return lcosErr
			}
		}
	}

	logger.CtxLogInfof(ctx, "successfully do a full auto update rule data cleaned")
	return nil
}

func (a *autoUpdateDataService) SearchAutoUpdateData(ctx utils.LCOSContext, isSuc uint8, startTime, endTime uint32) ([]*auto_update_data.CDTAutoUpdateCalculateDataVersionTab, *lcos_error.LCOSError) {

	return a.autoUpdateDataDao.SearchAutoUpdateDataVersion(ctx, map[string]interface{}{"is_success": isSuc, "ctime <": endTime, "ctime >": startTime})
}

func (a *autoUpdateDataService) SyncAutoUpdateDataToItemCodis(ctx utils.LCOSContext, region string, ruleId uint64) *lcos_error.LCOSError {
	startTime := utils.GetTimestamp(ctx)
	taskName := fmt.Sprintf("sync_item_cdt:auto:%s:%s:%d", utils.FormatTimestamp(startTime, constant.DateAndTimeFormat), region, ruleId)

	// 1. 获取需要同步的自动更新规则列表
	var rules []*auto_update_rule2.CDTAutoUpdateRuleTab
	var err *lcos_error.LCOSError
	if ruleId != 0 {
		rules, err = a.autoUpdateDao.SearchAutoUpdateRule(ctx, map[string]interface{}{
			"id": ruleId,
		})
	} else {
		queryMap := map[string]interface{}{
			"cdt_type":     edd_constant.ProductCdtType,
			"is_site_line": constant.TRUE,
			"is_lm":        constant.FALSE,
			"status_id in": []uint8{constant.Draft, constant.Upcoming, constant.Active},
		}
		if region != "" {
			queryMap["region"] = region
		}
		rules, err = a.autoUpdateDao.SearchAutoUpdateRule(ctx, queryMap)
	}
	if err != nil {
		_ = monitor.AwesomeReportEvent(ctx, taskName, "failed", constant.StatusError, err.Msg)

		message := fmt.Sprintf(
			item_card_edt_constant.SyncCdtAutoUpdateDataFailedMessage,
			utils.GetEnv(ctx),
			region,
			utils.FormatTimestamp(startTime, constant.DateAndTimeFormat),
			utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
			err.Msg,
		)
		cfg := config.GetMutableConf(ctx).ItemCardSceneConfig.SeatalkNotifyConfig
		_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, true)
		return err
	}

	// 2. 遍历同步自动更新规则当前正在生效的版本数据
	extraCodisList := config.GetExtraCdtSyncCodisList(ctx)
	var failedRuleIdList []string
	var errList []*lcos_error.LCOSError
	var total int
	for _, rule := range rules {
		logger.CtxLogInfof(ctx, "ready to sync auto update data to item codis|rule_id=%d", rule.ID)

		// 2.1 获取自动更新规则当前正在生效的cdt version
		cdtVersion, err := a.GetCdtVersionByAutoUpdateRuleID(ctx, rule.ID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "sync cdt auto update data failed, get cdt version error|rule_id=%d, cause=%s", rule.ID, err.Msg)
			failedRuleIdList = append(failedRuleIdList, strconv.Itoa(int(rule.ID)))
			errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncItemCdtError, "sync cdt auto update data failed, get cdt version error|rule_id=%d, cause=%s", rule.ID, err.Msg))
			_ = monitor.AwesomeReportEvent(ctx, taskName, strconv.Itoa(int(rule.ID)), constant.StatusError, err.Msg)
			continue
		}

		// 2.2 获取自动更新规则版本数据
		dataList, err := a.GetCdtCalculationDataByCdtVersion(ctx, rule.ID, cdtVersion)
		if err != nil {
			logger.CtxLogErrorf(ctx, "sync cdt auto update data failed, get cdt data error|rule_id=%d, cdt_version=%d, cause=%s", rule.ID, cdtVersion, err.Msg)
			failedRuleIdList = append(failedRuleIdList, strconv.Itoa(int(rule.ID)))
			errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncItemCdtError, "sync cdt auto update data failed, get cdt data error|rule_id=%d, cause=%s", rule.ID, err.Msg))
			_ = monitor.AwesomeReportEvent(ctx, taskName, strconv.Itoa(int(rule.ID)), constant.StatusError, err.Msg)
			continue
		}

		// 2.3 分批将自动更新规则数据同步到codis
		success, err := a.autoUpdateDataDao.SyncAutoUpdateDataToItemCodis(ctx, rule, dataList, extraCodisList)
		if err != nil {
			logger.CtxLogErrorf(ctx, "sync cdt auto update data failed, sync data error|rule_id=%d, cdt_version=%d, success=%d, cause=%s", rule.ID, cdtVersion, success, err.Msg)
			failedRuleIdList = append(failedRuleIdList, strconv.Itoa(int(rule.ID)))
			errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncItemCdtError, "sync cdt auto update data failed, sync data error|rule_id=%d, cdt_version=%d, success=%d, cause=%s", rule.ID, cdtVersion, success, err.Msg))
			_ = monitor.AwesomeReportEvent(ctx, taskName, strconv.Itoa(int(rule.ID)), constant.StatusError, err.Msg)
			continue
		}
		_ = monitor.AwesomeReportEvent(ctx, taskName, strconv.Itoa(int(rule.ID)), constant.StatusSuccess, strconv.Itoa(success))
		total += success
	}
	_ = monitor.AwesomeReportEvent(ctx, taskName, "done", constant.StatusSuccess, strconv.Itoa(total))

	message := fmt.Sprintf(
		item_card_edt_constant.SyncCdtAutoUpdateDataNotifyMessage,
		utils.GetEnv(ctx),
		region,
		utils.FormatTimestamp(startTime, constant.DateAndTimeFormat),
		utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
		len(rules),
		strings.Join(failedRuleIdList, ","),
		total,
		strings.Join(extraCodisList, ","),
	)
	cfg := config.GetMutableConf(ctx).ItemCardSceneConfig.SeatalkNotifyConfig
	var atAll bool
	if len(failedRuleIdList) != 0 {
		atAll = true
	}
	_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, atAll)

	if len(errList) != 0 {
		errMsg := lcos_error.GetMessageFromListWithSep(errList, "\n")
		return lcos_error.NewLCOSError(lcos_error.SyncItemCdtError, errMsg)
	}
	return nil
}
