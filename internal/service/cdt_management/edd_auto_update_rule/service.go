package edd_auto_update_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	edd_auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	"strings"
)

type EDDAutoUpdateRuleInterface interface {
	CreateEDDAutoUpdateRule(ctx utils.LCOSContext, request *edd_auto_update_rule.CreateEDDAutoUpdateRuleRequest) *lcos_error.LCOSError
	UpdateEDDAutoUpdateRule(ctx utils.LCOSContext, request *edd_auto_update_rule.UpdateEDDAutoUpdateRuleRequest) *lcos_error.LCOSError
	ListEDDAutoUpdateRules(ctx utils.LCOSContext, request *edd_auto_update_rule.ListEDDAutoUpdateRuleRequest) (interface{}, *lcos_error.LCOSError)
	DeleteEDDAutoUpdateRule(ctx utils.LCOSContext, request *edd_auto_update_rule.GetEDDAutoUpdateRuleRequest) *lcos_error.LCOSError
	GetEDDAutoUpdateRuleByID(ctx utils.LCOSContext, request *edd_auto_update_rule.GetEDDAutoUpdateRuleRequest) (interface{}, *lcos_error.LCOSError)
	ToggleEDDAutoUpdateRuleStatus(ctx utils.LCOSContext, request *edd_auto_update_rule.ToggleEDDAutoUpdateStatusRequest) *lcos_error.LCOSError

	CheckCanCreateEDDAutoUpdateRule(ctx utils.LCOSContext, request *edd_auto_update_rule.CreateEDDAutoUpdateRuleRequest) *lcos_error.LCOSError
}

type eddAutoUpdateRule struct {
	eddAutoUpdateRuleDao edd_auto_update_rule2.EddAutoUpdateRuleDao
	forecastTaskDao      edd_forecast_task.EDDForecastTaskDao

	leadAutoRuleDao auto_update_rule.CDTAutoUpdateRuleTabDAO
}

func containsPreemptiveEvent(UpdateEvents edd_auto_update_rule2.UpdateEventSlice) bool {
	preemptiveFlag := false
	for _, singleEvent := range UpdateEvents {
		if singleEvent.PreemptLateParcels == constant.TRUE {
			preemptiveFlag = true
			break
		}
	}
	return preemptiveFlag
}

func (e *eddAutoUpdateRule) checkCanCreateEDDAutoUpdateRule(ctx utils.LCOSContext, request *edd_auto_update_rule.CreateEDDAutoUpdateRuleRequest) *lcos_error.LCOSError {

	// product list cannot be empty
	if len(request.ProductIDList) <= 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "product list cannot be empty")
	}
	if !(request.MaxTimes >= 1) {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "max times need to be larger than or equal to 1")
	}
	if len(request.UpdateEvents) <= 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "update events cannot be empty")
	}
	if request.UpdateEvents.ContainsPreemptiveConfig() && !utils.ContainsUint32(edd_constant.AllowedCheckpointList, request.CheckpointFrequency) {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "checkpoint frequency is not allowed when enable preemptive config|checkpoint_frequency=[%d]", request.CheckpointFrequency)
	}
	// set update condition to immediate
	for i := 0; i < len(request.UpdateEvents); i++ {
		request.UpdateEvents[i].UpdateCondition = edd_constant.Immediate
	}
	if errMsg := request.UpdateEvents.CheckValid(); len(errMsg) > 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}
	// SPLN-29072 check whether contain error method
	if errMsg := request.UpdateEvents.CheckContainsErrorMethod(); len(errMsg) > 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}
	// sort update event
	request.UpdateEvents.SortByEventSequence(ctx)

	// for request came from forecast, no need to check edd auto update rule
	if request.FromForecastTaskID > 0 {
		return nil
	}

	nowTime := utils.GetTimestamp(ctx)

	productList := make([]string, 0, len(request.ProductIDList))
	productMap := make(map[string]struct{})
	for _, singleProduct := range request.ProductIDList {
		if _, ok := productMap[singleProduct.ProductID]; !ok {
			productList = append(productList, singleProduct.ProductID)
			productMap[singleProduct.ProductID] = struct{}{}
		}
	}

	leadRules, lcosErr := e.leadAutoRuleDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"product_id in": productList, "status_id in": []uint8{constant.Active, constant.Upcoming}, "cdt_type": edd_constant.LaneCdtType})
	if lcosErr != nil {
		return lcosErr
	}
	leadMap := make(map[string]*auto_update_rule.CDTAutoUpdateRuleTab)
	for _, leadRule := range leadRules {
		leadMap[leadRule.ProductID] = leadRule
	}

	// check can create edd auto update rule
	for _, singleProduct := range request.ProductIDList {
		singleLeadRule, hasLeadRule := leadMap[singleProduct.ProductID]
		if hasLeadRule && singleLeadRule.IsActive(nowTime) {
			if lcosErr = singleLeadRule.PreemptiveConfig.CheckLeadAndEDDAutoUpdate(request.UpdateEvents); lcosErr != nil {
				return lcos_error.NewLCOSErrorf(lcosErr.RetCode, "update events mismatch from lead auto update rule[%d], product_id=[%s]|error_message=[%s]", singleLeadRule.ID, singleProduct.ProductID, lcosErr.Msg)
			}
		} else {
			// does not have active lead rule, for these, cannot have preemptive config
			if containsPreemptiveEvent(request.UpdateEvents) {
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "There is no active auto calculation task (lead time) for this product, so you are not allowed to use preempt late parcels|product_id=[%s]", singleProduct.ProductID)
			}
		}
	}
	return nil
}

func (e *eddAutoUpdateRule) CheckCanCreateEDDAutoUpdateRule(ctx utils.LCOSContext, request *edd_auto_update_rule.CreateEDDAutoUpdateRuleRequest) *lcos_error.LCOSError {
	if lcosErr := e.checkCanCreateEDDAutoUpdateRule(ctx, request); lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)

		// SPLN-29072
		// for warning message, need to replace error message
		if lcosErr.RetCode == lcos_error.EDDAutoUpdateRuleCreateWarning {
			lcosErr.Msg = edd_constant.EDDCreateWarningMessage
		}
		return lcosErr
	}
	return nil
}

func (e *eddAutoUpdateRule) CreateEDDAutoUpdateRule(ctx utils.LCOSContext, request *edd_auto_update_rule.CreateEDDAutoUpdateRuleRequest) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	operator := ctx.GetUserEmail()

	// SPLN-29072 add lead preemptive config check
	if lcosErr := e.checkCanCreateEDDAutoUpdateRule(ctx, request); lcosErr != nil {
		if lcosErr.RetCode != lcos_error.EDDAutoUpdateRuleCreateWarning {
			return lcosErr
		}
	}

	// get all product config to verify product
	productMap, lcosErr := product_service.GetAllProducts(ctx, region)
	if lcosErr != nil {
		return lcosErr
	}
	models := make([]*edd_auto_update_rule2.EddAutoUpdateRule, 0, len(request.ProductIDList))
	for _, singleProduct := range request.ProductIDList {
		if _, ok := productMap[singleProduct.ProductID]; !ok {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("product:[%s] is not valid", singleProduct.ProductID))
		}
		models = append(models, &edd_auto_update_rule2.EddAutoUpdateRule{
			Region:             region,
			ProductId:          singleProduct.ProductID,
			ProductName:        singleProduct.ProductName,
			IsCB:               productMap[singleProduct.ProductID].CBType,
			UpdateEvents:       request.UpdateEvents,
			MaxTimes:           request.MaxTimes,
			EddMaxThresholdMin: request.EddMaxThresholdMin,
			EddMaxThresholdMax: request.EddMaxThresholdMax,
			EddMinThresholdMin: request.EddMinThresholdMin,
			EddMinThresholdMax: request.EddMinThresholdMax,
			Operator:           operator,

			CheckpointFrequency: request.CheckpointFrequency,
			FromForecastTaskID:  request.FromForecastTaskID,
		})
	}
	return e.eddAutoUpdateRuleDao.BatchCreateEddAutoUpdateRules(ctx, models)
}

func (e *eddAutoUpdateRule) UpdateEDDAutoUpdateRule(ctx utils.LCOSContext, request *edd_auto_update_rule.UpdateEDDAutoUpdateRuleRequest) *lcos_error.LCOSError {
	updateMap := make(map[string]interface{})
	if len(request.UpdateEvents) > 0 {
		updateMap["update_events"] = request.UpdateEvents
	}
	updateMap["threshold_min"] = request.EddMaxThresholdMin
	updateMap["threshold_max"] = request.EddMaxThresholdMax
	updateMap["edd_min_threshold_min"] = request.EddMinThresholdMin
	updateMap["edd_min_threshold_max"] = request.EddMinThresholdMax
	updateMap["max_times"] = request.MaxTimes
	updateMap["operator"] = ctx.GetUserEmail()
	return e.eddAutoUpdateRuleDao.UpdateEddAutoUpdateRuleById(ctx, request.ID, updateMap)
}

func (e *eddAutoUpdateRule) ListEDDAutoUpdateRules(ctx utils.LCOSContext, request *edd_auto_update_rule.ListEDDAutoUpdateRuleRequest) (interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())

	var pageNo uint32 = 1
	var pageCount uint32 = 10
	if request.PageNo != nil {
		pageNo = *request.PageNo
	}
	if request.PageSize != nil {
		pageCount = *request.PageSize
	}

	queryMap := map[string]interface{}{
		"region": region,
	}
	if request.ProductID != nil {
		queryMap["product_id"] = *request.ProductID
	}
	if request.ID != nil {
		queryMap["id"] = *request.ID
	}

	models, total, lcosErr := e.eddAutoUpdateRuleDao.ListEddAutoUpdateRules(ctx, queryMap, pageNo, pageCount)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return map[string]interface{}{
		"list":      models,
		"page_no":   pageNo,
		"page_size": pageCount,
		"total":     total,
	}, nil
}

func (e *eddAutoUpdateRule) DeleteEDDAutoUpdateRule(ctx utils.LCOSContext, request *edd_auto_update_rule.GetEDDAutoUpdateRuleRequest) *lcos_error.LCOSError {
	return e.eddAutoUpdateRuleDao.DeleteEddAutoUpdateRuleById(ctx, request.ID)
}

func (e *eddAutoUpdateRule) GetEDDAutoUpdateRuleByID(ctx utils.LCOSContext, request *edd_auto_update_rule.GetEDDAutoUpdateRuleRequest) (interface{}, *lcos_error.LCOSError) {
	models, lcosErr := e.eddAutoUpdateRuleDao.ListAllEddAutoUpdateRules(ctx, map[string]interface{}{"id": request.ID})
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(models) <= 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find edd auto update|id=%d", request.ID))
	}
	return models[0], nil
}

func (e *eddAutoUpdateRule) ToggleEDDAutoUpdateRuleStatus(ctx utils.LCOSContext, request *edd_auto_update_rule.ToggleEDDAutoUpdateStatusRequest) *lcos_error.LCOSError {
	var toStatus uint8 = 0
	if request.ToStatus != constant.FALSE {
		toStatus = constant.TRUE
	}

	// check if can find rule
	eddRules, lcosErr := e.eddAutoUpdateRuleDao.ListAllEddAutoUpdateRules(ctx, map[string]interface{}{"id": request.ID})
	if lcosErr != nil || len(eddRules) <= 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find edd auto update rule|id=%d", request.ID)
	}
	eddRule := eddRules[0]

	operator := ctx.GetUserEmail()
	if request.FromForecast == constant.TRUE {
		operator = request.Operator
	}

	if eddRule.FromForecastTaskID > 0 && toStatus == constant.TRUE && request.FromForecast != constant.TRUE {
		// need to check forecast task result, if deploy option
		// 1. TakeEffectImmediately, will be enabled by system
		// 2. InDisableStatus, can be toggled by user
		forecastTasks, lcosErr := e.forecastTaskDao.SearchEDDForecastTasks(ctx, map[string]interface{}{"id": eddRule.FromForecastTaskID})
		if lcosErr != nil || len(forecastTasks) <= 0 {
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find forecast task by id:[%d]", eddRule.FromForecastTaskID)
		}

		if forecastTasks[0].IsSystemTask() {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "for auto update rule come from forecast, only system user can enable")
		}
	}

	fc := func() *lcos_error.LCOSError {
		if toStatus == constant.TRUE {
			lcosErr := e.eddAutoUpdateRuleDao.UpdateEddAutoUpdateRuleByParams(ctx, map[string]interface{}{"product_id": eddRule.ProductId, "enable_status": constant.TRUE}, map[string]interface{}{"enable_status": constant.FALSE, "operator": operator})
			if lcosErr != nil {
				return lcosErr
			}
		}
		return e.eddAutoUpdateRuleDao.UpdateEddAutoUpdateRuleById(ctx, request.ID, map[string]interface{}{"enable_status": toStatus, "operator": operator})
	}

	return ctx.Transaction(fc)
}

func NewEDDAutoUpdateRule(eddAutoUpdateRuleDao edd_auto_update_rule2.EddAutoUpdateRuleDao, forecastTaskDao edd_forecast_task.EDDForecastTaskDao, leadAutoRuleDao auto_update_rule.CDTAutoUpdateRuleTabDAO) *eddAutoUpdateRule {
	return &eddAutoUpdateRule{
		eddAutoUpdateRuleDao: eddAutoUpdateRuleDao,
		forecastTaskDao:      forecastTaskDao,
		leadAutoRuleDao:      leadAutoRuleDao,
	}
}

var _ EDDAutoUpdateRuleInterface = (*eddAutoUpdateRule)(nil)
