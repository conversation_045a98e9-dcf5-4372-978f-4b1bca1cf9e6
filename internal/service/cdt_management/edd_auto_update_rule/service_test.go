package edd_auto_update_rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	edd_auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_task"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
)

func Test_eddAutoUpdateRule_checkCanCreateEDDAutoUpdateRule(t *testing.T) {

	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.TEST))
	_ = os.Setenv("ENV", strings.ToLower(cf.TEST))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	if err := chassis.Init(); err != nil {
		t.Fatalf("init chassis: %v", err)
	}

	c, err := cf.InitConfig()
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := dbhelper.SetLogisticsCoreServiceConnection(c.DBLogisticCoreService); err != nil {
		t.Fatalf(err.Error())
	}

	type args struct {
		ctx     utils.LCOSContext
		request *edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest
	}
	tests := []struct {
		name string
		args args
		want *lcos_error.LCOSError
	}{
		{
			// already have lead auto update rule
			// event exist in lead auto update rule, not in edd auto update rule
			// return error
			name: "test lead auto update rule exists, event in edd auto update rule not exist lead auto update rule",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest{
					ProductIDList: []*edd_auto_update_rule2.SingleProductInfo{{
						ProductID:   "78015",
						ProductName: "Seller Own Fleet",
					}},
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 1,
					EddMinThresholdMin: 0,
					EddMinThresholdMax: 2,
					MaxTimes:           3,
					UpdateEvents: edd_auto_update_rule.UpdateEventSlice{
						{
							Event:              edd_constant.LMHubInbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.TRUE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
					},
					CheckpointFrequency: 7,
				},
			},
			want: lcos_error.NewLCOSError(lcos_error.EDDAutoUpdateRuleCannotCreateError, "update events mismatch from lead auto update rule[822], product_id=[78015]|error_message=[event [LM Hub Inbound] not found in lead auto update rule, but found in edd auto update rule]"),
		},
		{
			// already have lead auto update rule
			// event in lead auto update rule disable, enable in edd auto update rule
			// return error
			name: "test lead auto update rule exists, event in edd auto update rule enable preemptive while lead auto update rule not",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest{
					ProductIDList: []*edd_auto_update_rule2.SingleProductInfo{{
						ProductID:   "78015",
						ProductName: "Seller Own Fleet",
					}},
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 1,
					EddMinThresholdMin: 0,
					EddMinThresholdMax: 2,
					MaxTimes:           3,
					UpdateEvents: edd_auto_update_rule.UpdateEventSlice{
						{
							Event:              edd_constant.TWSInbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.TRUE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
						{
							Event:              edd_constant.TWSOutbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.TRUE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
					},
					CheckpointFrequency: 7,
				},
			},
			want: lcos_error.NewLCOSError(lcos_error.EDDAutoUpdateRuleCannotCreateError, "update events mismatch from lead auto update rule[822], product_id=[78015]|error_message=[event [TWS Outbound] is enable preemptive in edd auto update, but disabled in lead auto update rule]"),
		},
		{
			// already have lead auto update rule
			// event in lead auto update rule but not in edd auto update rule
			// return warning
			name: "test lead auto update rule exists, event exists in lead auto update rule while not edd auto update rule",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest{
					ProductIDList: []*edd_auto_update_rule2.SingleProductInfo{{
						ProductID:   "78015",
						ProductName: "Seller Own Fleet",
					}},
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 1,
					EddMinThresholdMin: 0,
					EddMinThresholdMax: 2,
					MaxTimes:           3,
					UpdateEvents: edd_auto_update_rule.UpdateEventSlice{
						{
							Event:              edd_constant.TWSInbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.TRUE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
						{
							Event:              edd_constant.TWSOutbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.FALSE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
					},
					CheckpointFrequency: 7,
				},
			},
			want: lcos_error.NewLCOSError(lcos_error.EDDAutoUpdateRuleCreateWarning, "update events mismatch from lead auto update rule[822], product_id=[78015]|error_message=[events [Destination Inbound] configured in lead auto update rule, but not found in edd auto update rule]"),
		},
		{
			// success
			name: "success",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest{
					ProductIDList: []*edd_auto_update_rule2.SingleProductInfo{{
						ProductID:   "78015",
						ProductName: "Seller Own Fleet",
					}},
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 1,
					EddMinThresholdMin: 0,
					EddMinThresholdMax: 2,
					MaxTimes:           3,
					UpdateEvents: edd_auto_update_rule.UpdateEventSlice{
						{
							Event:              edd_constant.TWSInbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.TRUE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
						{
							Event:              edd_constant.TWSOutbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.FALSE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
						{
							Event:              edd_constant.DestinationInbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.FALSE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
					},
					CheckpointFrequency: 7,
				},
			},
			want: nil,
		},
		{
			// lead auto update rule not exists
			// cannot enable preemptive
			// return error
			name: "success",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest{
					ProductIDList: []*edd_auto_update_rule2.SingleProductInfo{{
						ProductID:   "780151111",
						ProductName: "Seller Own Fleet",
					}},
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 1,
					EddMinThresholdMin: 0,
					EddMinThresholdMax: 2,
					MaxTimes:           3,
					UpdateEvents: edd_auto_update_rule.UpdateEventSlice{
						{
							Event:              edd_constant.TWSInbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.TRUE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
						{
							Event:              edd_constant.TWSOutbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.FALSE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
						{
							Event:              edd_constant.DestinationInbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.FALSE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
					},
					CheckpointFrequency: 7,
				},
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "There is no active auto calculation task (lead time) for this product, so you are not allowed to use preempt late parcels|product_id=[780151111]"),
		},
		{
			// event duplicate
			// return error
			name: "success",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest{
					ProductIDList: []*edd_auto_update_rule2.SingleProductInfo{{
						ProductID:   "78015",
						ProductName: "Seller Own Fleet",
					}},
					EddMaxThresholdMin: 0,
					EddMaxThresholdMax: 1,
					EddMinThresholdMin: 0,
					EddMinThresholdMax: 2,
					MaxTimes:           3,
					UpdateEvents: edd_auto_update_rule.UpdateEventSlice{
						{
							Event:              edd_constant.TWSInbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.TRUE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
						{
							Event:              edd_constant.TWSInbound,
							Rule:               edd_constant.AllTracking,
							EddMaxTrending:     edd_constant.FreeTrending,
							EddMinTrending:     edd_constant.FreeTrending,
							PreemptLateParcels: constant.FALSE,
							UpdateCondition:    edd_constant.Immediate,
							UpdateMethod:       edd_constant.AssumeNextEventAtNextCronJob,
							MinValue:           1,
						},
					},
					CheckpointFrequency: 7,
				},
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "event:[5] is not unique"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &eddAutoUpdateRule{
				eddAutoUpdateRuleDao: edd_auto_update_rule.NewEddAutoUpdateRuleDao(),
				forecastTaskDao:      edd_forecast_task.NewEDDForecastRuleDao(),
				leadAutoRuleDao:      auto_update_rule.NewCdtAutoUpdateRuleDao(),
			}
			if got := e.checkCanCreateEDDAutoUpdateRule(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("checkCanCreateEDDAutoUpdateRule() = %v, want %v", got, tt.want)
			}
		})
	}
}
