package auto_update_rule

import (
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
)

type ListAutoUpdateRuleResponse struct {
	PageNo uint32                          `json:"pageno"`
	Count  uint32                          `json:"count"`
	Total  uint32                          `json:"total"`
	List   []*CDTAutoUpdateRuleTabResponse `json:"list"`
}

const (
	ACTION_IS_ALLOWED  = 0
	ACTION_NOT_ALLOWED = -1
)

type ActionResult struct {
	ActionResult int    `json:"action_result"` // 是否允许enable/disable,0-可以，-1-不可以
	Message      string `json:"message"`
}

type TimePeriodResponse struct {
	TimePeriod int `json:"time_period"`
}

type CDTAutoUpdateRuleTabResponse struct {
	auto_update_rule.CDTAutoUpdateRuleTab
	ExcludedDaysArray []*rule.ExcludedDay `json:"excluded_days_array"`
}
