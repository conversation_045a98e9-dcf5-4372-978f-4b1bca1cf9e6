package auto_update_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	changereport "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/change_report"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	auto_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/product_ctime"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/common"
	cdt_common2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/data_ssc"
	json "github.com/json-iterator/go"
)

type AutoUpdateServiceInterface interface {
	CreateAutoUpdateRule(ctx utils.LCOSContext, request *auto_update_rule.CreateAutoRuleRequest) *lcos_error.LCOSError
	GetAutoUpdateRuleByID(ctx utils.LCOSContext, request *auto_update_rule.GetAutoRuleRequest) (*auto_rule2.CDTAutoUpdateRuleTab, *lcos_error.LCOSError)
	ListAutoUpdateRuleByParamsPaging(ctx utils.LCOSContext, request *auto_update_rule.ListAutoRuleRequest) (*ListAutoUpdateRuleResponse, *lcos_error.LCOSError)
	ToggleAutoUpdateRuleStatus(ctx utils.LCOSContext, request *auto_update_rule.ToggleAutoUpdateRuleStatusRequest) (*ActionResult, *lcos_error.LCOSError)
	DeleteAutoUpdateRuleByID(ctx utils.LCOSContext, request *auto_update_rule.DeleteAutoRuleRequest) *lcos_error.LCOSError
	GetTimePeriod(ctx utils.LCOSContext, request *auto_update_rule.GetTimePeriod) (*TimePeriodResponse, *lcos_error.LCOSError)

	BatchCreateOrUpdateProductCtime(ctx utils.LCOSContext, request *auto_update_rule.UpdateProductCtimeRequest) *lcos_error.LCOSError

	// 周期任务
	SyncAutoUpdateRuleStatus(ctx utils.LCOSContext) *lcos_error.LCOSError     // 同步需要修改状态的任务
	CheckIfAutoUpdateRuleUpdated(ctx utils.LCOSContext) *lcos_error.LCOSError // 检查第二天是否存在可以用的版本号，对于不存在的任务需要及时告警

	// for auto update postcode
	GetPostcodeListByAutoUpdateRuleID(ctx utils.LCOSContext, autoUpdateID uint64) ([]*auto_rule2.CDTAutoUpdateRulePostcodeTab, *lcos_error.LCOSError)

	SearchAutoUpdateRulesByIDs(ctx utils.LCOSContext, autoUpdataIDs []uint64) ([]*auto_rule2.CDTAutoUpdateRuleTab, *lcos_error.LCOSError)
}

type autoUpdateService struct {
	autoUpdateDao       auto_rule2.CDTAutoUpdateRuleTabDAO
	autoUpdateDataDao   auto_update_data.CDTAutoUpdateDataDAO
	manualUpdateDao     manual_update_rule.CDTManualUpdateRuleTabDAO
	laneManualUpdateDao lane_manual_update_rule.LaneCdtManualUpdateRuleDAO
	productCtimeDao     product_ctime.ProductCtimeTabDAO

	eddAutoUpdateRule edd_auto_update_rule.EddAutoUpdateRuleDao

	// SPLN-30795
	cdtABTestRuleDao cdt_ab_test.CdtAbTestRuleDao
}

func NewAutoUpdateService(autoUpdateDao auto_rule2.CDTAutoUpdateRuleTabDAO, autoUpdateDataDao auto_update_data.CDTAutoUpdateDataDAO, manualUpdateDao manual_update_rule.CDTManualUpdateRuleTabDAO, laneManualUpdateDao lane_manual_update_rule.LaneCdtManualUpdateRuleDAO, productCtimeDao product_ctime.ProductCtimeTabDAO, eddAutoUpdateRule edd_auto_update_rule.EddAutoUpdateRuleDao, cdtABTestRuleDao cdt_ab_test.CdtAbTestRuleDao) *autoUpdateService {
	return &autoUpdateService{
		autoUpdateDao:       autoUpdateDao,
		autoUpdateDataDao:   autoUpdateDataDao,
		manualUpdateDao:     manualUpdateDao,
		laneManualUpdateDao: laneManualUpdateDao,
		productCtimeDao:     productCtimeDao,
		eddAutoUpdateRule:   eddAutoUpdateRule,
		cdtABTestRuleDao:    cdtABTestRuleDao,
	}
}

var _ AutoUpdateServiceInterface = (*autoUpdateService)(nil)

func (a *autoUpdateService) CheckIfAutoUpdateRuleUpdated(ctx utils.LCOSContext) *lcos_error.LCOSError {
	batchSize := 100
	advancedHours := config.GetFloat64WithContext(ctx, constant.ReportAdvancedHours, 6) // 在提前多少小时内，没有第二天的版本号，则告警
	advancedSeconds := uint32(advancedHours * 3600)
	var models []*auto_rule2.CDTAutoUpdateRuleTab
	lcosErr := common.GetAllDataBatch(ctx, &auto_rule2.CDTAutoUpdateRuleTab{}, &models, batchSize)
	if lcosErr != nil {
		return lcosErr
	}
	nowTime := utils.GetTimestamp(ctx)
	for _, model := range models {
		// 只针对生效的任务
		if model.IsActive(nowTime) && model.NextUpdateTime > advancedSeconds && nowTime >= model.NextUpdateTime-advancedSeconds {
			// 当前的next update time还没有更新，存在问题，需要告警
			nextUpdateTimeString := fmt.Sprintf("%v", pickup.TransferTimeStampToTime(model.NextUpdateTime, model.Region))
			_ = metrics.CounterIncr(constant.CheckCdtAutoUpdateTaskUpdated, map[string]string{"auto_update_rule_id": strconv.FormatUint(model.ID, 10), "next_update_time": nextUpdateTimeString, "region": model.Region})
		}
	}
	return nil
}

func (a *autoUpdateService) SyncAutoUpdateRuleStatus(ctx utils.LCOSContext) *lcos_error.LCOSError {
	batchSize := 100
	var models []*auto_rule2.CDTAutoUpdateRuleTab
	lcosErr := common.GetAllDataBatch(ctx, &auto_rule2.CDTAutoUpdateRuleTab{}, &models, batchSize)
	if lcosErr != nil {
		return lcosErr
	}
	nowTime := utils.GetTimestamp(ctx)

	for _, model := range models {
		// 更新对应的model状态
		if model.NeedToBeUpdatedToActive(nowTime) {
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": model.ID}, map[string]interface{}{"status_id": constant.Active})
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "error syncing auto update status|error=%v", lcosErr.Msg)
				return lcosErr
			}
			changereport.ReportCdtAutoRuleActive(ctx, model.Region, model.ID, model.EffectiveTime, model.ExpirationTime)
		}
		if model.NeedToBeUpdatedToDisabled(nowTime) {
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": model.ID}, map[string]interface{}{"status_id": constant.Disable})
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "error syncing auto update status|error=%v", lcosErr.Msg)
				return lcosErr
			}
		}
	}
	return nil
}

func (a *autoUpdateService) checkCanCreateLeadAutoUpdate(ctx utils.LCOSContext, request *auto_update_rule.CreateAutoRuleRequest) *lcos_error.LCOSError {

	if errMsg := request.PreemptiveConfig.CheckValid(); len(errMsg) > 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}

	productList := make([]string, 0, len(request.ProductInfos))
	productMap := make(map[string]struct{})
	for _, singleProduct := range request.ProductInfos {
		if _, ok := productMap[singleProduct.ProductID]; !ok {
			productList = append(productList, singleProduct.ProductID)
			productMap[singleProduct.ProductID] = struct{}{}
		}
	}
	if _, ok := productMap[request.ProductID]; !ok {
		if len(request.ProductID) > 0 {
			productList = append(productList, request.ProductID)
			productMap[request.ProductID] = struct{}{}
		}
	}

	// sort update event
	request.PreemptiveConfig.SortByEventSequence(ctx)

	// for request came from forecast, no need to check edd auto update rule
	if request.FromForecastTaskID > 0 {
		return nil
	}

	// get all edd auto update rule
	eddAutoRules, lcosErr := a.eddAutoUpdateRule.ListAllEddAutoUpdateRules(ctx, map[string]interface{}{"product_id in": productList, "enable_status": constant.TRUE})
	if lcosErr != nil {
		return lcosErr
	}

	// check all match
	for _, singleEDDAutoRule := range eddAutoRules {
		if lcosErr = request.PreemptiveConfig.CheckLeadAndEDDAutoUpdate(singleEDDAutoRule.UpdateEvents); lcosErr != nil {
			if lcosErr.RetCode != lcos_error.EDDAutoUpdateRuleCreateWarning { // ignore warning message
				return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "update events mismatch from edd auto update rule[%d], product_id=[%s]|error_message=[%s]", singleEDDAutoRule.Id, singleEDDAutoRule.ProductId, lcosErr.Msg)
			}
		}
	}
	return nil
}

func (a *autoUpdateService) CreateAutoUpdateRule(ctx utils.LCOSContext, request *auto_update_rule.CreateAutoRuleRequest) *lcos_error.LCOSError {

	region := strings.ToUpper(ctx.GetCountry())
	operator := ctx.GetUserEmail()
	// SPLN-23660 从新的json数组中解析exclude_days
	exclude_days := auto_update_rule.GetExcludedDaysStr(request.ExcludedDaysArray)

	// SPLN-29072
	// if there is a edd auto update, need to check if there are more update events thant lead
	// for product&lane type, preemptive config cannot be empty
	if request.CdtType == edd_constant.LaneCdtType {
		if lcosErr := a.checkCanCreateLeadAutoUpdate(ctx, request); lcosErr != nil {
			return lcosErr
		}
	}

	if lcosErr := a.checkDayGroup(ctx, request.EventTimeLevel, request.DayGroup); lcosErr != nil {
		return lcosErr
	}

	model := &auto_rule2.CDTAutoUpdateRuleTab{
		ProductID:                request.ProductID,
		ProductName:              request.ProductName,
		CdtType:                  request.CdtType,
		Region:                   region,
		CBType:                   request.CBType,
		MaskingType:              request.MaskingType,
		IntegratedType:           request.IntegratedType,
		IsSiteLine:               request.IsSiteLine,
		IsLM:                     request.IsLM,
		PrevUpdateTime:           0,
		LastUpdateTime:           0,
		NextUpdateTime:           0,
		OriginLocationLevel:      request.OriginLocationLevel,
		DestinationLocationLevel: request.DestinationLocationLevel,
		TimePeriod:               request.TimePeriod,
		ExcludedDays:             exclude_days,
		ThresholdNum:             request.ThresholdNum,
		Frequency:                request.Frequency,
		MinPercentile:            request.MinPercentile,
		MaxPercentile:            request.MaxPercentile,
		LmMaxPercentile:          request.LmMaxPercentile,
		StatusID:                 constant.Calculating,
		RemoveFlag:               request.RemoveFlag,
		RemoveThresholdNum:       request.RemoveThresholdNum,
		Operator:                 operator,
		CalculateEddMin:          request.CalculateEddMin,
		EddRangeLimit:            request.EddRangeLimit,
		FromForecastTaskID:       request.FromForecastTaskID,
		PreemptiveConfig:         request.PreemptiveConfig,
		TestGroupTag:             request.TestGroupTag,
		ForecastMethod:           request.ForecastMethod,
		ForecastDataVersion:      request.ForecastDataVersion,
		ForecastDataUUID:         request.ForecastDataUUID,
		EventTimeLevel:           request.EventTimeLevel,
		DayGroup:                 request.DayGroup,
		StartEventFCode:          request.StartEventFCode,
	}

	var autoUpdateRulePostcode []*auto_rule2.CDTAutoUpdateRulePostcodeTab

	// 检查是否需要解析cep range文件
	if request.DestinationLocationLevel == constant.CepRange {

		var cepRangeList []string

		if request.DestinationCepRangeUrl == "" || request.DestinationCepRangeFileName == "" {
			// 如果是cep range，文件url不能为空
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination cep range file url cannot be empty")
		}

		// 创建的时候，cep range文件可能来自于其他的auto update rule，如果url以duplicate_from开头，则需要获取其cep range用于创建
		if strings.HasPrefix(request.DestinationCepRangeUrl, constant.DUPLICATE_FROM_PREFIX) {
			tmpList := strings.Split(request.DestinationCepRangeUrl, ":")
			if len(tmpList) != 2 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination cep range file url is not valid")
			}
			duplicateID := tmpList[1]
			duplicateIDint, err := strconv.Atoi(duplicateID)
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination cep range file url is not valid")
			}
			_model, lcosErr := a.autoUpdateDao.GetAutoUpdateRuleByID(ctx, uint64(duplicateIDint))
			if lcosErr != nil {
				return lcosErr
			}
			model.DestinationCepRange = _model.DestinationCepRange
		} else {
			cepModels, lcosErr := cdt_common2.ParseCepFile(ctx, request.DestinationCepRangeUrl, config2.GetCdtMaxAutoUpdateCepRangeCount(ctx))
			if lcosErr != nil {
				return lcosErr
			}
			// 遍历解析出来的models
			for _, cepModel := range cepModels {
				cepRangeList = append(cepRangeList, fmt.Sprintf("%v-%v", cepModel.CepLeft, cepModel.CepRight))
			}

			model.DestinationCepRange = strings.Join(cepRangeList, "#")
		}
		model.DestinationCepRangeFileName = request.DestinationCepRangeFileName
	} else if request.DestinationLocationLevel == constant.CDTPostcode { // parse postcode file

		if request.DestinationPostcodeUrl == "" || request.DestinationPostcodeFileName == "" {
			// postcode file url cannot be empty
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination postcode file url cannot be empty")
		}

		// when duplicated from other rule, has prefix duplicate_from
		if strings.HasPrefix(request.DestinationPostcodeUrl, constant.DUPLICATE_FROM_PREFIX) {

			var lcosErr *lcos_error.LCOSError

			tmpList := strings.Split(request.DestinationPostcodeUrl, ":")
			if len(tmpList) != 2 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination postcode file url is not valid")
			}
			duplicateID := tmpList[1]
			duplicateIDint, err := strconv.Atoi(duplicateID)
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination postcode file url is not valid")
			}
			autoUpdateRulePostcode, lcosErr = a.autoUpdateDao.GetAutoUpdateRulePostcodesByID(ctx, uint64(duplicateIDint))
			if lcosErr != nil {
				return lcosErr
			}
		} else {
			postcodeList, lcosErr := cdt_common2.DownloadAndParsePostcode(ctx, request.DestinationPostcodeUrl)
			if lcosErr != nil {
				return lcosErr
			}
			// parse postcode into autoUpdateRulePostcodeTab
			for _, singlePostcode := range postcodeList {
				autoUpdateRulePostcode = append(autoUpdateRulePostcode, &auto_rule2.CDTAutoUpdateRulePostcodeTab{
					PostcodeLeft:  singlePostcode,
					PostcodeRight: "",
				})
			}
		}
		model.DestinationPostcodeFileName = request.DestinationPostcodeFileName
	}

	allPostcodeList := make([]string, len(autoUpdateRulePostcode))
	if len(autoUpdateRulePostcode) > 0 {
		for index, singlePostcode := range autoUpdateRulePostcode {
			allPostcodeList[index] = singlePostcode.PostcodeLeft
		}
	}
	models := make([]*auto_rule2.CDTAutoUpdateRuleTab, 0)

	// SPLN-26162 validate products are all cb or local
	allChannelMap, lcosErr := product_service.GetAllProducts(ctx, region)
	if lcosErr != nil {
		return lcosErr
	}
	var cbType uint8

	for index, item := range request.ProductInfos {
		if _, ok := allChannelMap[item.ProductID]; !ok {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("channel:[%s] not found in region:[%s]", item.ProductID, region))
		}

		if index == 0 {
			cbType = allChannelMap[item.ProductID].CBType
		} else if !(cbType == allChannelMap[item.ProductID].CBType) {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cb type of channel:[%s] is not the same as other channels", item.ProductID))
		}

		// 把model 的值赋值给一个临时变量
		var _model = *model
		_model.ProductID = item.ProductID
		_model.ProductName = item.ProductName
		_model.CBType = item.CBType
		_model.MaskingType = item.MaskingType
		_model.IntegratedType = item.IntegratedType

		models = append(models, &_model)
	}
	failProductIDs := make([]string, 0)
	for _, item := range models {
		fc := func() *lcos_error.LCOSError {
			var postcodeFlag uint8 = 1
			_, lcosErr := a.autoUpdateDao.CreateAutoUpdateRule(ctx, item)

			if lcosErr != nil {
				return lcosErr
			}

			if item.DestinationLocationLevel == constant.CepRange && len(item.DestinationCepRange) > 0 {
				postcodeFlag = 2
			}

			// check if need to create postcode data
			if item.DestinationLocationLevel == constant.CDTPostcode && len(autoUpdateRulePostcode) > 0 {
				postcodeFlag = 3
				// fill auto update id
				for _, rule := range autoUpdateRulePostcode {
					rule.AutoUpdateRuleID = item.ID
					rule.ID = 0 // remove primary key
					rule.Region = region
					rule.EventTimeLevel = request.EventTimeLevel
					rule.DayGroup = request.DayGroup
				}
				lcosErr = a.autoUpdateDao.BatchCreateAutoUpdatePostcode(ctx, autoUpdateRulePostcode)
				if lcosErr != nil {
					return lcosErr
				}
			}

			// 触发data自动计算的逻辑
			triggerParams := parseAutoUpdateRuleToTriggerParams(item, region, allPostcodeList, postcodeFlag)
			result, lcosErr := data_ssc.TriggerRunJobTask(ctx, triggerParams)
			if lcosErr != nil {
				return lcos_error.NewLCOSError(lcos_error.TriggerAutoUpdateRuleJobError, lcosErr.Msg)
			}
			if result.Retcode != 0 {
				return lcos_error.NewLCOSError(lcos_error.TriggerAutoUpdateRuleJobError, result.Message)
			}
			return nil
		}

		if err := ctx.Transaction(fc); err != nil {
			failProductIDs = append(failProductIDs, item.ProductID)
		}
	}
	if len(failProductIDs) > 0 {
		failString, _ := json.Marshal(failProductIDs)
		return lcos_error.NewLCOSError(lcos_error.SomeAutoRuleFail, string(failString))
	}
	return nil
}

// GetAutoUpdateRuleByID 由于detail接口未来需要给创建接口用，所以返回创建接口的参数格式
func (a *autoUpdateService) GetAutoUpdateRuleByID(ctx utils.LCOSContext, request *auto_update_rule.GetAutoRuleRequest) (*auto_rule2.CDTAutoUpdateRuleTab, *lcos_error.LCOSError) {
	model, lcosErr := a.autoUpdateDao.GetAutoUpdateRuleByID(ctx, request.ID)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return model, nil
}

func (a *autoUpdateService) getCheckCanEnableResult(ctx utils.LCOSContext, autoUpdateRule *auto_rule2.CDTAutoUpdateRuleTab, region string) (*ActionResult, *lcos_error.LCOSError) {
	// 检查当前channel是否存在其他upcoming的任务
	// SPLN-30795 add test group filter
	var message string

	modesl, lcosErr := a.autoUpdateDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"region": region, "id !=": autoUpdateRule.ID, "cdt_type": autoUpdateRule.CdtType, "product_id": autoUpdateRule.ProductID, "is_lm": autoUpdateRule.IsLM, "is_site_line": autoUpdateRule.IsSiteLine, "status_id": constant.Upcoming, "test_group_tag": autoUpdateRule.TestGroupTag})
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(modesl) != 0 {
		if autoUpdateRule.IsCDTRule() {
			message = "Enabling this Draft job will cause the system to replace the current Upcoming job with it. Are you sure you want to enable the Draft job?"
		} else {
			message = "Enabling this task will replace the currently Upcoming task belonging to the same group. The new task will take effect at 00:00 tomorrow. Are you sure to enable this task?"
		}
		return &ActionResult{
			ActionResult: ACTION_IS_ALLOWED,
			Message:      message,
		}, nil
	}

	// 检查当前channel是否存在其他active的任务
	modesl, lcosErr = a.autoUpdateDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"region": region, "id !=": autoUpdateRule.ID, "cdt_type": autoUpdateRule.CdtType, "product_id": autoUpdateRule.ProductID, "is_lm": autoUpdateRule.IsLM, "is_site_line": autoUpdateRule.IsSiteLine, "status_id": constant.Active, "test_group_tag": autoUpdateRule.TestGroupTag})
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(modesl) != 0 {
		if autoUpdateRule.IsCDTRule() {
			message = "Enabling this Draft job will cause the system to replace the current Active job with it in Tomorrow 0:00. Are you sure you want to enable the Draft job?"
		} else {
			message = "Enabling this task will replace the currently Active task belonging to the same group. The new task will take effect at 00:00 tomorrow. Are you sure to enable this task?"
		}
		return &ActionResult{
			ActionResult: ACTION_IS_ALLOWED,
			Message:      message,
		}, nil
	}

	// 如果当前channel没有其他的upcoming或者active的任务
	if autoUpdateRule.IsTestGroupB() {
		message = "For group B, the status of this task will become Active at 00:00 tomorrow, but it will only be used for calculating EDD once you have enabled AB Test. Are you sure to enable this draft?"
	} else {
		message = "The job will be active tomorrow 0:00，Are you sure you want to enable the Draft job?"
	}
	return &ActionResult{
		ActionResult: ACTION_IS_ALLOWED,
		Message:      message,
	}, nil
}

// ToggleAutoUpdateRuleStatus 如果是check动作，不会返回报错。对于实际的enable/disable动作则会报错
func (a *autoUpdateService) ToggleAutoUpdateRuleStatus(ctx utils.LCOSContext, request *auto_update_rule.ToggleAutoUpdateRuleStatusRequest) (*ActionResult, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	nowTimestamp := utils.GetTimestamp(ctx)

	// 先获取当前的自动更新规则
	autoUpdateRule, lcosErr := a.autoUpdateDao.GetAutoUpdateRuleByID(ctx, request.ID)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// SPLN-30795
	abTestCheckErr := a.actionCheckWithCdtABTestConfig(ctx, autoUpdateRule)

	// 检查status
	switch request.ActionCode {
	case constant.CHECK_CAN_ENABLE:
		// 1. 只有当前状态是draft时，才可以enable，否则报错。
		// 2. 如果当前的channel id存在upcoming的任务，则返回信息"Enabling this Draft job will cause the system to replace the current Upcoming job with it. Are you sure you want to enable the Draft job?’"
		// 3. 如果当前的channel id存在active的任务，则返回信息"Enabling this Draft job will cause the system to replace the current Active job with it in Tomorrow 0:00. Are you sure you want to enable the Draft job?’"
		// 4. 如果当前的channel id不存在active或者upcoming的任务，则返回信息"The job will be active tomorrow 0:00，Are you sure you want to enable the Draft job?’"
		if autoUpdateRule.StatusID != constant.Draft {
			return &ActionResult{
				ActionResult: ACTION_NOT_ALLOWED,
				Message:      "current task status is not draft, and not allowed to enable",
			}, nil
		}

		// SPLN-30795, if ab test rule check fail, will not allow to enable
		if abTestCheckErr != nil {
			return &ActionResult{
				ActionResult: ACTION_NOT_ALLOWED,
				Message:      abTestCheckErr.Msg,
			}, nil
		}

		return a.getCheckCanEnableResult(ctx, autoUpdateRule, region)
	case constant.ENABLE:
		if autoUpdateRule.StatusID != constant.Draft {
			return nil, lcos_error.NewLCOSError(lcos_error.TaskNotAllowedToEnableErrorCode, "current task status is not draft, and not allowed to enable")
		}

		// SPLN-30795, if ab test rule check fail, will not allow to enable
		if abTestCheckErr != nil {
			return nil, abTestCheckErr
		}

		// 计算出当前时区第二天的0点0分0秒
		currentTime := pickup.GetCurrentTime(ctx, autoUpdateRule.Region)
		newDayTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1, 0, 0, 0, 0, currentTime.Location())
		newDayTimestamp := uint32(newDayTime.Unix())

		// 将当前channel的过期时间，将当前任务置为upcoming。需要开启事务
		fc := func() *lcos_error.LCOSError {
			// 将当前channel的状态为upcoming，且生效时间大于当前时间的任务置为disable
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id !=": autoUpdateRule.ID, "cdt_type": autoUpdateRule.CdtType, "is_site_line": autoUpdateRule.IsSiteLine, "is_lm": autoUpdateRule.IsLM, "product_id": autoUpdateRule.ProductID, "status_id": constant.Upcoming, "effective_time >": nowTimestamp, "test_group_tag": autoUpdateRule.TestGroupTag}, map[string]interface{}{"status_id": constant.Disable, "expiration_time": nowTimestamp})
			if lcosErr != nil {
				return lcosErr
			}

			// 将当前channel的状态为upcoming或者active，且当前时间大于effective的任务的expiration time置为now
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id !=": autoUpdateRule.ID, "cdt_type": autoUpdateRule.CdtType, "is_site_line": autoUpdateRule.IsSiteLine, "is_lm": autoUpdateRule.IsLM, "product_id": autoUpdateRule.ProductID, "status_id in": []uint8{constant.Upcoming, constant.Active}, "effective_time <=": nowTimestamp, "test_group_tag": autoUpdateRule.TestGroupTag}, map[string]interface{}{"expiration_time": uint32(newDayTimestamp)})
			if lcosErr != nil {
				return lcosErr
			}

			// 将当前任务置为upcoming，且将其生效时间置为newDayTimestamp
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id =": autoUpdateRule.ID}, map[string]interface{}{"effective_time": uint32(newDayTimestamp), "status_id": constant.Upcoming, "next_update_time": newDayTimestamp + autoUpdateRule.Frequency*3600*24, "last_update_time": newDayTimestamp})
			if lcosErr != nil {
				return lcosErr
			}
			return nil
		}

		if lcosErr := ctx.Transaction(fc); lcosErr != nil {
			return nil, lcosErr
		}
		return nil, nil
	case constant.CHECK_CAN_DISABLE:
		// 1. 只有当前任务是upcoming和active是，才可以disable，否则报错
		// 其他的逻辑参考 https://confluence.shopee.io/pages/viewpage.action?pageId=425624637
		// 可能的任务状态
		// 1. upcoming, now < effective time 任务未生效
		// 2. upcoming, effective time < now < expiration time 任务生效，此时任务生效
		// 3. upcoming, expiration time < now 任务失效，此时点击disable应该返回不允许disable
		// 4. active, effective time < now < expiration time 任务生效，此时任务生效
		// 5. active now > expiration time 任务失效，不应该允许disable
		if !(autoUpdateRule.IsUpcoming(nowTimestamp) || autoUpdateRule.IsActive(nowTimestamp)) {
			return &ActionResult{
				ActionResult: ACTION_NOT_ALLOWED,
				Message:      "current task status is not upcoming or active, and not allowed to disable",
			}, nil
		}

		// SPLN-30795, if ab test rule check fail, will not allow to disable
		if abTestCheckErr != nil {
			return &ActionResult{
				ActionResult: ACTION_NOT_ALLOWED,
				Message:      abTestCheckErr.Msg,
			}, nil
		}

		// 对于group b的规则，可以直接disable，而无需关注是否存在手动数据
		if autoUpdateRule.IsLeadRule() && autoUpdateRule.IsTestGroupB() {
			return &ActionResult{
				ActionResult: ACTION_IS_ALLOWED,
				Message:      "Disabling this task will not affect the current EDD calculation. However, there might be no available Group B Auto Generation Task when creating an AB Test task. Are you sure to disable it?",
			}, nil
		}

		var mtime int
		if autoUpdateRule.IsLeadRule() {
			// SPLN-24104, 校验lane维度数据，检查是否存在现有的lane维度手动更新规则，如无则不允许更新
			locationModels, lcosErr := a.laneManualUpdateDao.GetLaneCdtLocationDataByParams(ctx, region, autoUpdateRule.ProductID, autoUpdateRule.IsSiteLine)
			if lcosErr != nil {
				return nil, lcosErr
			}
			cepRangeModels, lcosErr := a.laneManualUpdateDao.GetLaneCdtCepRangeDataByParams(ctx, region, autoUpdateRule.ProductID, autoUpdateRule.IsSiteLine)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(locationModels) == 0 && len(cepRangeModels) == 0 {
				return &ActionResult{
					ActionResult: ACTION_NOT_ALLOWED,
					Message:      "Disabling the Active job will cause the system to use the CDT Manual Update instead， You are not allowed to disable active job，Please set the CDT Manual Update.",
				}, nil
			}

			// 获取最新的mtime
			for _, locationModel := range locationModels {
				if locationModel.MTime > mtime {
					mtime = locationModel.MTime
				}
			}
			for _, cepRangeModel := range cepRangeModels {
				if cepRangeModel.MTime > mtime {
					mtime = cepRangeModel.MTime
				}
			}
		} else {
			// 检查是否存在现有的手动更新规则，如无则不允许更新
			locationModels, lcosErr := a.manualUpdateDao.GetCdtLocationDataByParams(ctx, map[string]interface{}{"region": region, "product_id": autoUpdateRule.ProductID, "is_lm": autoUpdateRule.IsLM, "is_site_line": autoUpdateRule.IsSiteLine}, "mtime DESC")
			if lcosErr != nil {
				return nil, lcosErr
			}
			postcodeModels, lcosErr := a.manualUpdateDao.GetCdtPostcodeDataByParams(ctx, map[string]interface{}{"region": region, "product_id": autoUpdateRule.ProductID, "is_lm": autoUpdateRule.IsLM, "is_site_line": autoUpdateRule.IsSiteLine}, "mtime DESC")
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(locationModels) == 0 && len(postcodeModels) == 0 {
				return &ActionResult{
					ActionResult: ACTION_NOT_ALLOWED,
					Message:      "Disabling the Active job will cause the system to use the CDT Manual Update instead， You are not allowed to disable active job，Please set the CDT Manual Update.",
				}, nil
			}

			// 获取最新的mtime
			if len(locationModels) > 0 {
				mtime = locationModels[0].Mtime
			}
			if len(postcodeModels) > 0 && postcodeModels[0].Mtime > mtime {
				mtime = postcodeModels[0].Mtime
			}
		}

		// 是否超过24H的flag
		var isOver24HFlag = int(nowTimestamp)-mtime > 24*3600

		// 将mtime转为带时区的时间
		mtimeDatetime := pickup.TransferTimeStampToTime(uint32(mtime), region)

		// 检查是否存在其他upcoming的任务
		upcomingModesl, lcosErr := a.autoUpdateDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"region": region, "id !=": autoUpdateRule.ID, "product_id": autoUpdateRule.ProductID, "is_lm": autoUpdateRule.IsLM, "is_site_line": autoUpdateRule.IsSiteLine, "status_id": constant.Upcoming, "effective_time >": nowTimestamp, "expiration_time": 0, "cdt_type": autoUpdateRule.CdtType, "test_group_tag": autoUpdateRule.TestGroupTag})
		if lcosErr != nil {
			return nil, lcosErr
		}
		isUpcomingFlag := len(upcomingModesl) > 0

		if isOver24HFlag && !isUpcomingFlag {
			return &ActionResult{
				ActionResult: ACTION_IS_ALLOWED,
				Message:      fmt.Sprintf("the manual rule update time is %v，do you want to use an old CDT manual data？", mtimeDatetime),
			}, nil
		} else if !isOver24HFlag && isUpcomingFlag {
			return &ActionResult{
				ActionResult: ACTION_IS_ALLOWED,
				Message:      "Disabling the Active job will cause the system to use the CDT Manual Update instead. If you want to use the Upcoming job, please just wait for it to be effective. Are you sure you want to disable the Active job?' ",
			}, nil
		} else if isOver24HFlag && isUpcomingFlag {
			return &ActionResult{
				ActionResult: ACTION_IS_ALLOWED,
				Message:      fmt.Sprintf("Disabling the Active job will cause the system to use the CDT Manual Update instead. the manual rule update time is %v. \n If you want to use the Upcoming job, please just wait for it to be effective. Are you sure you want to disable the Active job and use an old CDT manual data?' ", mtimeDatetime),
			}, nil
		} else {
			return &ActionResult{
				ActionResult: ACTION_IS_ALLOWED,
				Message:      "Are you sure you want to disable the Active job?",
			}, nil
		}
	case constant.DISABLE:

		// 可能的任务状态
		// 1. upcoming, now < effective time 任务未生效
		// 2. upcoming, effective time < now < expiration time 任务生效，此时任务生效
		// 3. upcoming, expiration time < now 任务失效，此时点击disable应该返回不允许disable
		// 4. active, effective time < now < expiration time 任务生效，此时任务生效
		// 5. active now > expiration time 任务失效，不应该允许disable

		// SPLN-30795, if ab test rule check fail, will not allow to disable
		if abTestCheckErr != nil {
			return nil, abTestCheckErr
		}

		if !(autoUpdateRule.IsUpcoming(nowTimestamp) || autoUpdateRule.IsActive(nowTimestamp)) {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "current status not allowed to be disabled")
		}

		if autoUpdateRule.CdtType == edd_constant.LaneCdtType {
			// SPLN-24104, 检查是否存在现有的lane维度手动更新规则，如无则不允许更新
			locationModels, lcosErr := a.laneManualUpdateDao.GetLaneCdtLocationDataByParams(ctx, region, autoUpdateRule.ProductID, autoUpdateRule.IsSiteLine)
			if lcosErr != nil {
				return nil, lcosErr
			}
			cepRangeModels, lcosErr := a.laneManualUpdateDao.GetLaneCdtCepRangeDataByParams(ctx, region, autoUpdateRule.ProductID, autoUpdateRule.IsSiteLine)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(locationModels) == 0 && len(cepRangeModels) == 0 {
				return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "no valid manual update rule and not allowed to be disabled")
			}
		} else {
			// 检查是否存在现有的手动更新规则，如无则不允许更新
			locationModels, lcosErr := a.manualUpdateDao.GetCdtLocationDataByParams(ctx, map[string]interface{}{"region": region, "product_id": autoUpdateRule.ProductID, "is_lm": autoUpdateRule.IsLM, "is_site_line": autoUpdateRule.IsSiteLine}, "mtime DESC")
			if lcosErr != nil {
				return nil, lcosErr
			}
			postcodeModels, lcosErr := a.manualUpdateDao.GetCdtPostcodeDataByParams(ctx, map[string]interface{}{"region": region, "product_id": autoUpdateRule.ProductID, "is_lm": autoUpdateRule.IsLM, "is_site_line": autoUpdateRule.IsSiteLine}, "mtime DESC")
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(locationModels) == 0 && len(postcodeModels) == 0 {
				return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "no valid manual update rule and not allowed to be disabled")
			}
		}

		// 将其置为disable，过期时间设置为now
		lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": autoUpdateRule.ID}, map[string]interface{}{"status_id": constant.Disable, "expiration_time": nowTimestamp})
		if lcosErr != nil {
			return nil, lcosErr
		}
		return nil, nil

	default:
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "current action code is not valid")

	}
}

func (a *autoUpdateService) fillInDisplayStatusForListAutoUpdateRules(ctx utils.LCOSContext, autoUpdateRules []*CDTAutoUpdateRuleTabResponse, currentTimestamp uint32) *lcos_error.LCOSError {
	// SPLN-30795 add display status for list auto update rule
	// 0-display nothing
	// 1-calculating EDD, for control group A rules that are active or test group b rules that are active and ab test rule active
	// 2-Not ready for Test, for test group b rules that are active and no ab test rule active
	abTestGroupEDTProducts := make([]string, 0, len(autoUpdateRules))
	abTestGroupEDDProducts := make([]string, 0, len(autoUpdateRules))
	for _, singleModel := range autoUpdateRules {
		if singleModel.IsActive(currentTimestamp) && singleModel.IsTestGroupB() {
			if singleModel.CdtType == edd_constant.CdtObject {
				abTestGroupEDTProducts = append(abTestGroupEDTProducts, singleModel.ProductID)
			} else if singleModel.CdtType == edd_constant.LeadTimeObject {
				abTestGroupEDDProducts = append(abTestGroupEDDProducts, singleModel.ProductID)
			}
		}
	}

	abTestGroupRuleEDTMap, err := a.genABTestGroupRuleMap(ctx, abTestGroupEDTProducts, edd_constant.CdtObject)
	if err != nil {
		return err
	}
	a.fillInDisplayStatus(edd_constant.CdtObject, currentTimestamp, abTestGroupRuleEDTMap, autoUpdateRules)

	abTestGroupRuleEDDMap, err := a.genABTestGroupRuleMap(ctx, abTestGroupEDDProducts, edd_constant.LeadTimeObject)
	if err != nil {
		return err
	}
	a.fillInDisplayStatus(edd_constant.LeadTimeObject, currentTimestamp, abTestGroupRuleEDDMap, autoUpdateRules)

	return nil
}

func (a *autoUpdateService) genABTestGroupRuleMap(ctx utils.LCOSContext, abTestGroupEDTProducts []string, objectType uint8) (map[string]*cdt_ab_test.CdtAbTestRule, *lcos_error.LCOSError) {
	// query ab test confs
	abTestGroupRuleMap := make(map[string]*cdt_ab_test.CdtAbTestRule)
	edtAbTestRules, lcosErr := a.cdtABTestRuleDao.ListCdtAbTestRuleByParams(
		ctx,
		map[string]interface{}{
			"product_id in": abTestGroupEDTProducts,
			"rule_status":   edd_constant.AbTestRuleStatusActive,
			"object_type":   objectType,
		},
	)
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, abTestRule := range edtAbTestRules {
		abTestGroupRuleMap[getABTestGroupRuleMapKey(abTestRule.ProductId, objectType)] = abTestRule
	}
	return abTestGroupRuleMap, nil
}

func (a *autoUpdateService) fillInDisplayStatus(objectType uint8, currentTimestamp uint32, abTestGroupRuleMap map[string]*cdt_ab_test.CdtAbTestRule, autoUpdateRules []*CDTAutoUpdateRuleTabResponse) {
	// fill in models
	for i := 0; i < len(autoUpdateRules); i++ {
		if autoUpdateRules[i].CdtType != objectType {
			continue
		}

		if autoUpdateRules[i].IsActive(currentTimestamp) {
			if autoUpdateRules[i].IsControlGroupA() {
				autoUpdateRules[i].DisplayStatus = edd_constant.CalculatingEDD
			} else if autoUpdateRules[i].IsTestGroupB() {
				if _, ok := abTestGroupRuleMap[getABTestGroupRuleMapKey(autoUpdateRules[i].ProductID, objectType)]; ok { // test group b rule and active ab test rule
					autoUpdateRules[i].DisplayStatus = edd_constant.CalculatingEDD
				} else {
					autoUpdateRules[i].DisplayStatus = edd_constant.NotReadyForTest
				}
			}
		}
	}
}

func getABTestGroupRuleMapKey(productId string, objectType uint8) string {
	return utils.GenKey(":", productId, strconv.FormatUint(uint64(objectType), 10))
}

func (a *autoUpdateService) ListAutoUpdateRuleByParamsPaging(ctx utils.LCOSContext, request *auto_update_rule.ListAutoRuleRequest) (*ListAutoUpdateRuleResponse, *lcos_error.LCOSError) {
	// 获取当前的时间戳
	currentTimestamp := uint32(recorder.Now(ctx).Unix())

	var page uint32 = 1
	var count uint32 = 10
	region := strings.ToUpper(ctx.GetCountry())
	var queryMap = make(map[string]interface{})
	if request.ProductID != nil {
		queryMap["product_id"] = *request.ProductID
	}
	if request.CdtType != nil {
		queryMap["cdt_type"] = *request.CdtType
	}
	if request.IntegratedType != nil {
		queryMap["integrated_type"] = *request.IntegratedType
	}
	if request.MaskingType != nil {
		queryMap["masking_type"] = *request.MaskingType
	}
	if request.CBType != nil {
		queryMap["cb_type"] = *request.CBType
	}
	if request.ID != nil {
		queryMap["id"] = *request.ID
	}
	if request.TestGroupTag != nil {
		queryMap["test_group_tag"] = *request.TestGroupTag
	}

	if request.ProductIDList != nil {
		delete(queryMap, "product_id")
		delete(queryMap, "id")
		queryProductIDList := make([]string, 0, len(request.ProductIDList))
		queryProductIDList = append(queryProductIDList, request.ProductIDList...)
		queryMap["product_id in"] = queryProductIDList
	}

	if request.StatusID != nil {
		for key, value := range cdt_common2.GenerateQueryForStatusForAutoUpdateRule(*request.StatusID, currentTimestamp) {
			queryMap[key] = value
		}
	}
	queryMap["is_site_line"] = *request.IsSiteLine
	queryMap["is_lm"] = *request.IsLM
	queryMap["region"] = region
	if request.PageNo != nil {
		page = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}
	models, total, lcosErr := a.autoUpdateDao.ListAutoUpdateRuleByParamsPaging(ctx, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	// 由于enable/disable是延迟生效，所以可能存在状态为upcoming，但是已经生效的任务，所以需要在此处更新这些任务的状态
	var autoRuleIDList []uint64
	for _, _model := range models {
		// 如果当前任务是upcoming或者active，如果超过过期时间则置为disable
		if _model.NeedToBeUpdatedToDisabled(currentTimestamp) {
			_model.StatusID = constant.Disable
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": _model.ID}, map[string]interface{}{"status_id": constant.Disable})
			if lcosErr != nil {
				return nil, lcosErr
			}
		}
		// 如果当前任务是upcoming，如果超过生效时间，则置为active
		if _model.NeedToBeUpdatedToActive(currentTimestamp) {
			_model.StatusID = constant.Active
			lcosErr = a.autoUpdateDao.UpdateAutoUpdateRule(ctx, map[string]interface{}{"id": _model.ID}, map[string]interface{}{"status_id": constant.Active})
			if lcosErr != nil {
				return nil, lcosErr
			}
			changereport.ReportCdtAutoRuleActive(ctx, _model.Region, _model.ID, _model.EffectiveTime, _model.ExpirationTime)
		}
		autoRuleIDList = append(autoRuleIDList, _model.ID)

		// 更新last update time，next_update_time
		_model.LastUpdateTime, _model.NextUpdateTime = _model.GetUpdateTime(currentTimestamp)

		// remove cep range info to import performance
		_model.DestinationCepRange = ""
	}

	// 填充last update time和next update time
	autoUpdateDataVersions, lcosErr := a.autoUpdateDataDao.SearchAutoUpdateDataVersion(ctx, map[string]interface{}{"auto_update_rule_id in": autoRuleIDList})
	if lcosErr != nil {
		return nil, lcosErr
	}

	var autoUpdateDataVersionMap = map[uint64][]*auto_update_data.CDTAutoUpdateCalculateDataVersionTab{}
	for _, model := range autoUpdateDataVersions {
		if _, ok := autoUpdateDataVersionMap[model.AutoUpdateRuleID]; !ok {
			autoUpdateDataVersionMap[model.AutoUpdateRuleID] = []*auto_update_data.CDTAutoUpdateCalculateDataVersionTab{}
		}
		autoUpdateDataVersionMap[model.AutoUpdateRuleID] = append(autoUpdateDataVersionMap[model.AutoUpdateRuleID], model)
	}

	list := make([]*CDTAutoUpdateRuleTabResponse, 0, len(models))
	for _, m := range models {
		item := &CDTAutoUpdateRuleTabResponse{
			CDTAutoUpdateRuleTab: *m,
			ExcludedDaysArray:    auto_update_rule.GetExcludedDaysArray(m.ExcludedDays),
		}
		list = append(list, item)
	}

	// SPLN-30795 add display status
	lcosErr = a.fillInDisplayStatusForListAutoUpdateRules(ctx, list, currentTimestamp)
	if lcosErr != nil {
		logger.CtxLogInfof(ctx, lcosErr.Msg)
	}

	response := &ListAutoUpdateRuleResponse{
		PageNo: page,
		Count:  count,
		Total:  total,
		List:   list,
	}
	return response, nil
}

func (a *autoUpdateService) DeleteAutoUpdateRuleByID(ctx utils.LCOSContext, request *auto_update_rule.DeleteAutoRuleRequest) *lcos_error.LCOSError {
	// 先获取对应id的数据
	model, lcosErr := a.autoUpdateDao.GetAutoUpdateRuleByID(ctx, request.ID)
	if lcosErr != nil {
		return lcosErr
	}

	// 不存在的id直接返回
	if model == nil {
		return nil
	}

	// 只有model的状态为draft或者disabled时才支持删除
	nowTimestamp := utils.GetTimestamp(ctx)
	if model.StatusID == constant.Draft || model.IsDisabled(nowTimestamp) {
		return a.autoUpdateDao.DeleteAutoUpdateRuleByID(ctx, request.ID)
	}
	return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "current task status not allowed to be deleted")
}

func (a *autoUpdateService) GetTimePeriod(ctx utils.LCOSContext, request *auto_update_rule.GetTimePeriod) (*TimePeriodResponse, *lcos_error.LCOSError) {
	productCtime, lcosErr := a.productCtimeDao.GetProductCtimeByProductID(ctx, request.ProductID)
	if lcosErr != nil {
		return nil, lcosErr
	}
	result := &TimePeriodResponse{}
	if productCtime == nil {
		result.TimePeriod = 0
		return result, nil
	}
	nowTime := utils.GetTimestamp(ctx)
	timePeriod := (nowTime - productCtime.CTime) / (3600 * 24)
	result.TimePeriod = int(timePeriod)
	return result, nil
}

func (a *autoUpdateService) BatchCreateOrUpdateProductCtime(ctx utils.LCOSContext, request *auto_update_rule.UpdateProductCtimeRequest) *lcos_error.LCOSError {
	var productCtimeTabs []*product_ctime.ProductCtimeTab
	for _, product := range request.Products {
		productCtimeTabs = append(productCtimeTabs, &product_ctime.ProductCtimeTab{
			ProductID: product.ProductID,
			CTime:     product.Ctime,
		})
	}

	fc := func() *lcos_error.LCOSError {
		for _, product := range productCtimeTabs {
			localProduct, lcosErr := a.productCtimeDao.GetProductCtimeByProductID(ctx, product.ProductID)
			if lcosErr != nil || localProduct == nil {
				lcosErr = a.productCtimeDao.CreateProductCtime(ctx, product)
			} else {
				lcosErr = a.productCtimeDao.UpdateProductCtime(ctx, product)
			}
			if lcosErr != nil {
				return lcosErr
			}
		}
		return nil
	}
	if lcosErr := ctx.Transaction(fc); lcosErr != nil {
		return lcosErr
	}
	return nil
}

func (a *autoUpdateService) GetPostcodeListByAutoUpdateRuleID(ctx utils.LCOSContext, autoUpdateID uint64) ([]*auto_rule2.CDTAutoUpdateRulePostcodeTab, *lcos_error.LCOSError) {
	return a.autoUpdateDao.GetAutoUpdateRulePostcodesByID(ctx, autoUpdateID)
}

func (a *autoUpdateService) SearchAutoUpdateRulesByIDs(ctx utils.LCOSContext, autoUpdataIDs []uint64) ([]*auto_rule2.CDTAutoUpdateRuleTab, *lcos_error.LCOSError) {
	return a.autoUpdateDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"id in": autoUpdataIDs})
}

func (a *autoUpdateService) actionCheckWithCdtABTestConfig(ctx utils.LCOSContext, autoUpdateRule *auto_rule2.CDTAutoUpdateRuleTab) *lcos_error.LCOSError {
	objectType := autoUpdateRule.CdtType

	// query active ab test rule
	abRules, lcosErr := a.cdtABTestRuleDao.ListCdtAbTestRuleByParams(ctx, map[string]interface{}{"product_id": autoUpdateRule.ProductID, "rule_status": edd_constant.AbTestRuleStatusActive, "object_type": objectType})
	if lcosErr != nil || len(abRules) == 0 {
		// if cannot find ab test rules, skip check
		logger.CtxLogInfof(ctx, "find not find active ab test rule for product:[%s]", autoUpdateRule.ProductID)
		return nil
	}

	// check whether ab test rule agree with auto update rule
	abRule := abRules[0]

	// block all action
	if abRule.IsActive() {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot do action because there is an active ab test rule|product_id=[%s], ab_test_rule_id=[%d]", autoUpdateRule.ProductID, abRule.Id)
	}
	return nil
}

func (a *autoUpdateService) checkDayGroup(ctx utils.LCOSContext, eventTimeLevel uint8, dayGroupStr string) *lcos_error.LCOSError {
	// eventTime 为 None 或者 TimeOfDay 时，dayGroup 应该为空
	if eventTimeLevel == constant.EventTimeLevelNone || eventTimeLevel == constant.EventTimeLevelTimeOfDay {
		if len(dayGroupStr) != 0 {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("event_time_level is [%d], day_group should be empty", eventTimeLevel))
		}
	}

	// 校验 day_group 格式，case [ [ 1, 2, 3 ], [ 4, 5, 6 ], [ 7 ] ]
	var dayGroup [][]uint8
	if err := jsoniter.Unmarshal([]byte(dayGroupStr), &dayGroup); err != nil && len(dayGroupStr) != 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("parse day_group:[%s] fail, err: %s", dayGroupStr, err.Error()))
	}

	return nil
}
