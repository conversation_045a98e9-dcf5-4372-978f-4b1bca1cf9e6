package auto_update_rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
)

func Test_autoUpdateService_CreateAutoUpdateRule(t *testing.T) {

	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.TEST))
	_ = os.Setenv("ENV", strings.ToLower(cf.TEST))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	if err := chassis.Init(); err != nil {
		t.Fatalf("init chassis: %v", err)
	}

	c, err := cf.InitConfig()
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := dbhelper.SetLogisticsCoreServiceConnection(c.DBLogisticCoreService); err != nil {
		t.Fatalf(err.Error())
	}

	type args struct {
		ctx     utils.LCOSContext
		request *auto_update_rule2.CreateAutoRuleRequest
	}
	tests := []struct {
		name string
		args args
		want *lcos_error.LCOSError
	}{
		{
			name: "event exists in lead not in edd auto update rule",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &auto_update_rule2.CreateAutoRuleRequest{
					CdtType:                  edd_constant.LaneCdtType,
					IsSiteLine:               1,
					IsLM:                     0,
					OriginLocationLevel:      constant.Country,
					DestinationLocationLevel: constant.State,
					TimePeriod:               14,
					ThresholdNum:             10,
					Frequency:                14,
					MinPercentile:            0,
					MaxPercentile:            78.6,
					ProductInfos: []auto_update_rule2.ProductInfoReq{{
						ProductID:      "78015",
						ProductName:    "Seller Own Fleet - ส่งจากต่างประเทศ",
						CBType:         constant.TRUE,
						MaskingType:    constant.FALSE,
						IntegratedType: constant.TRUE,
					}},
					PreemptiveConfig: auto_update_rule.PreemptiveConfig{
						{
							Event:              edd_constant.SOCOutbound,
							PreemptLateParcels: constant.TRUE,
							ConfidenceLevel:    88,
						},
					},
				},
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "update events mismatch from edd auto update rule[118], product_id=[78015]|error_message=[event [Destination Inbound] not found in lead auto update rule, but found in edd auto update rule]"),
		},
		{
			name: "event exists in both rule, but enable preemptive in lead auto update rule and disable in edd auto update rule",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &auto_update_rule2.CreateAutoRuleRequest{
					CdtType:                  edd_constant.LaneCdtType,
					IsSiteLine:               1,
					IsLM:                     0,
					OriginLocationLevel:      constant.Country,
					DestinationLocationLevel: constant.State,
					TimePeriod:               14,
					ThresholdNum:             10,
					Frequency:                14,
					MinPercentile:            0,
					MaxPercentile:            78.6,
					ProductInfos: []auto_update_rule2.ProductInfoReq{{
						ProductID:      "78015",
						ProductName:    "Seller Own Fleet - ส่งจากต่างประเทศ",
						CBType:         constant.TRUE,
						MaskingType:    constant.FALSE,
						IntegratedType: constant.TRUE,
					}},
					PreemptiveConfig: auto_update_rule.PreemptiveConfig{
						{
							Event:              edd_constant.DestinationInbound,
							PreemptLateParcels: constant.FALSE,
						},
					},
				},
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "update events mismatch from edd auto update rule[118], product_id=[78015]|error_message=[event [Destination Inbound] is enable preemptive in edd auto update, but disabled in lead auto update rule]"),
		},
		{
			name: "rules from lead is more than edd auto update rule",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &auto_update_rule2.CreateAutoRuleRequest{
					CdtType:                  edd_constant.LaneCdtType,
					IsSiteLine:               1,
					IsLM:                     0,
					OriginLocationLevel:      constant.Country,
					DestinationLocationLevel: constant.CepRange, // stop test cases
					TimePeriod:               14,
					ThresholdNum:             10,
					Frequency:                14,
					MinPercentile:            0,
					MaxPercentile:            78.6,
					ProductInfos: []auto_update_rule2.ProductInfoReq{{
						ProductID:      "78015",
						ProductName:    "Seller Own Fleet - ส่งจากต่างประเทศ",
						CBType:         constant.TRUE,
						MaskingType:    constant.FALSE,
						IntegratedType: constant.TRUE,
					}},
					PreemptiveConfig: auto_update_rule.PreemptiveConfig{
						{
							Event:              edd_constant.DestinationInbound,
							PreemptLateParcels: constant.TRUE,
						},
						{
							Event:              edd_constant.TWSInbound,
							PreemptLateParcels: constant.TRUE,
						},
						{
							Event:              edd_constant.TWSOutbound,
							PreemptLateParcels: constant.FALSE,
						},
						{
							Event:              edd_constant.OutForDelivery,
							PreemptLateParcels: constant.TRUE,
						},
					},
				},
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination cep range file url cannot be empty"),
		},
		{
			name: "create success",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				request: &auto_update_rule2.CreateAutoRuleRequest{
					CdtType:                  edd_constant.LaneCdtType,
					IsSiteLine:               1,
					IsLM:                     0,
					OriginLocationLevel:      constant.Country,
					DestinationLocationLevel: constant.CepRange, // stop test cases
					TimePeriod:               14,
					ThresholdNum:             10,
					Frequency:                14,
					MinPercentile:            0,
					MaxPercentile:            78.6,
					ProductInfos: []auto_update_rule2.ProductInfoReq{{
						ProductID:      "78015",
						ProductName:    "Seller Own Fleet - ส่งจากต่างประเทศ",
						CBType:         constant.TRUE,
						MaskingType:    constant.FALSE,
						IntegratedType: constant.TRUE,
					}},
					PreemptiveConfig: auto_update_rule.PreemptiveConfig{
						{
							Event:              edd_constant.DestinationInbound,
							PreemptLateParcels: constant.TRUE,
						},
						{
							Event:              edd_constant.TWSInbound,
							PreemptLateParcels: constant.TRUE,
						},
						{
							Event:              edd_constant.TWSOutbound,
							PreemptLateParcels: constant.FALSE,
						},
					},
				},
			},
			want: lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination cep range file url cannot be empty"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &autoUpdateService{
				autoUpdateDao:     auto_update_rule.NewCdtAutoUpdateRuleDao(),
				eddAutoUpdateRule: edd_auto_update_rule.NewEddAutoUpdateRuleDao(),
			}
			if got := a.CreateAutoUpdateRule(tt.args.ctx, tt.args.request); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateAutoUpdateRule() = %v, want %v", got, tt.want)
			}
		})
	}
}
