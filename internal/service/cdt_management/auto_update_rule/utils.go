package auto_update_rule

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/data_ssc"
	"strings"
)

func getUpdateEventString(configs auto_update_rule.PreemptiveConfig) string {
	updateEvents := make([]data_ssc.SinglePreemptiveEvent, 0, len(configs))
	for _, config := range configs {
		updateEvents = append(updateEvents, data_ssc.SinglePreemptiveEvent{
			Event:              config.Event,
			DeadlineMethod:     config.DeadlineMethod,
			AllowPreemptUpdate: config.PreemptLateParcels,
			ConfidenceLevel:    config.GetConfidenceLevel(),
		})
	}
	return utils.MarshToStringWithoutError(updateEvents)
}

func parseAutoUpdateRuleToTriggerParams(item *auto_update_rule.CDTAutoUpdateRuleTab, region string, allPostcodeList []string, postcodeFlag uint8) *data_ssc.TriggerRunJob {
	params := &data_ssc.TriggerRunJob{
		CdtType:                  item.CdtType,
		ProductID:                item.ProductID,
		AutoUpdateRuleID:         item.ID,
		IsLM:                     item.IsLM,
		MaskingType:              item.MaskingType,
		OriginLocationLevel:      item.OriginLocationLevel,
		DestinationLocationLevel: item.DestinationLocationLevel,
		PostcodeList:             strings.Join(allPostcodeList, ","),
		Frequency:                int(item.Frequency),
		CepRange:                 item.DestinationCepRange,
		MinPercentile:            item.GetMinPercentile(),
		MaxPercentile:            item.GetMaxPercentile(),
		LMMaxPercentile:          item.GetLmMaxPercentile(),
		ThresholdNum:             int(item.ThresholdNum),
		ExcludedDays:             item.ExcludedDays,
		Region:                   region,
		TimePeriod:               int(item.TimePeriod),
		PostcodeFlag:             postcodeFlag,
		RemoveFlag:               item.RemoveFlag,
		RemoveThresholdNum:       item.RemoveThresholdNum,
		UpdateEventRuleList:      getUpdateEventString(item.PreemptiveConfig),
		EventTimeLevel:           item.EventTimeLevel,
		DayGroup:                 item.DayGroup,
		StartEventFCode:          item.StartEventFCode,
	}

	// SPLN-30795 for system recommend, need to fill in extra params
	if item.ForecastMethod == edd_constant.SystemRecommendsForecastRules {
		params.SystemRecommendation = edd_constant.SystemRecommendsForecastRules
		params.DataVersion = item.ForecastDataVersion
		params.UUID = item.ForecastDataUUID
	}
	return params
}
