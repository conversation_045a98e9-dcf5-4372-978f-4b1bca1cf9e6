package edd_forecast_result

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/math"
	"strconv"
)

func returnNilOrPercent(number float64) string {
	if number == -1 {
		return "-"
	}
	return math.FloatToPercent(number)
}

func returnNilOrInt(number int64) string {
	if number == -1 {
		return "-"
	}
	return strconv.Itoa(int(number))
}

var EventSequence = []string{
	"pickup_done",
	"shipped_out",
	"cb_dropoff_done",
	"cb_pickup_done",
	"local_dropoff_done",
	"local_pickup_done",

	"before_tws_inbound",
	"tws_inbound",

	"before_tws_outbound",
	"tws_outbound",

	"before_destination_inbound",
	"destination_inbound",

	"before_soc_inbound",
	"soc_inbound",

	"before_soc_outbound",
	"soc_outbound",

	"before_lmhub_inbound",
	"lmhub_inbound",

	"before_out_for_delivery",
	"out_for_delivery",

	"before_first_delivery_attempt",
	"first_delivery_attempt",

	"over_all",
}

var displayedEventNameMap = map[string]string{

	"pickup_done":        "Shipped Out",
	"shipped_out":        "Shipped Out",
	"cb_dropoff_done":    "CB Dropoff Done",
	"cb_pickup_done":     "CB Pickup Done",
	"local_dropoff_done": "Local Dropoff Done",
	"local_pickup_done":  "Local Pickup Done",

	"before_tws_inbound": "before tws inbound",
	"tws_inbound":        "TWS Inbound",

	"before_tws_outbound": "before tws outbound",
	"tws_outbound":        "TWS Outbound",

	"before_destination_inbound": "before destination inbound",
	"destination_inbound":        "Destination Inbound",

	"before_soc_inbound": "before soc inbound",
	"soc_inbound":        "SOC Inbound",

	"before_soc_outbound": "before soc outbound",
	"soc_outbound":        "SOC Outbound",

	"before_lmhub_inbound": "before lm hub inbound",
	"lmhub_inbound":        "LM Hub Inbound",

	"before_out_for_delivery": "before out for delivery",
	"out_for_delivery":        "Out for Delivery",

	"before_first_delivery_attempt": "before first delivery attempt",
	"first_delivery_attempt":        "First Delivery Attempt",

	"over_all": "Overall Initial Event",
}

func getDisplayName(eventName string) string {
	if displayName, ok := displayedEventNameMap[eventName]; ok {
		return displayName
	}
	return eventName
}

func getDisplayFloat(number float64) string {
	return fmt.Sprintf("%.2f", number)
}
