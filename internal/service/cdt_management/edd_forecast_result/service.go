package edd_forecast_result

import (
	"fmt"
	edd_auto_update "git.garena.com/shopee/bg-logistics/algo/sls/edd-auto-update"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/edd_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_forecast_result"
	edd_forecast_result2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_result"
	edd_forecast_task2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_task"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	jsoniter "github.com/json-iterator/go"
	"path"
	"strings"
)

type EDDForecastTaskResultInterface interface {
	ImportEDDForecastTaskResult(ctx utils.LCOSContext, request *edd_forecast_result.ImportEDDForecastTaskResultRequest) *lcos_error.LCOSError
	DetailEDDForecastTaskResult(ctx utils.LCOSContext, request *edd_forecast_result.DetailEDDForecastTaskResultRequest) (*edd_forecast_result2.DetailEDDForecastTaskResultResponse, *lcos_error.LCOSError)
	ExportEDDForecastTaskResult(ctx utils.LCOSContext, forecastTaskID uint64, region string) (*excelize.File, *lcos_error.LCOSError)

	CalculateEDDByDataSDK(ctx utils.LCOSContext, request *edd_forecast_result.CalculateEDDBySDKRequest) (map[string]interface{}, *lcos_error.LCOSError)

	// SPLN-30795
	GetDeployInfo(ctx utils.LCOSContext, forecastTaskID uint64, region string) ([]edd_forecast_result.ForecastResultDeployInfo, *lcos_error.LCOSError)
}

type eddForecastTaskResult struct {
	eddForecastResultDao edd_forecast_result2.EDDForecastResultDao
	eddForecastTaskDao   edd_forecast_task2.EDDForecastTaskDao
}

func (e *eddForecastTaskResult) CalculateEDDByDataSDK(ctx utils.LCOSContext, request *edd_forecast_result.CalculateEDDBySDKRequest) (map[string]interface{}, *lcos_error.LCOSError) {
	newEddMax, newEddMin, newDDL, eddMaxNWD, eddMinNWD, err := edd_util.CallDataSDK(ctx, request.EventTime, request.CurrentEddMax, request.CurrentEddMin, request.DeadlineMethod, edd_auto_update.EddUpdateType(request.UpdateType), request.CheckpointFrequency, request.HolidayList, request.Weekends, request.CdtMax, request.CdtMin, request.DdlCDT, request.MinValue, request.Region)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return map[string]interface{}{
		"new_edd_max": newEddMax,
		"new_edd_min": newEddMin,
		"new_ddl":     newDDL,
		"edd_max_nwd": eddMaxNWD,
		"edd_min_nwd": eddMinNWD,
	}, nil
}

func (e *eddForecastTaskResult) ImportEDDForecastTaskResult(ctx utils.LCOSContext, request *edd_forecast_result.ImportEDDForecastTaskResultRequest) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	nowTime := int(recorder.Now(ctx).Unix())
	//校验参数
	ResultBody := edd_forecast_result2.DataForecastResult{}
	err := jsoniter.Unmarshal([]byte(request.ForecastResult), &ResultBody)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, err.Error())
	}

	ForecastTasks, lcosErr := e.eddForecastTaskDao.SearchEDDForecastTasks(ctx, map[string]interface{}{"id": request.ForecastTaskId, "region": region})
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		return lcosErr
	}
	if len(ForecastTasks) == 0 {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "taskId not find in task db")
	}
	ForecastTask := ForecastTasks[0]
	if ForecastTask.ForecastStatus != edd_constant.Processing {
		return lcos_error.NewLCOSError(lcos_error.ParamsError, "task is not processing,can't import again")
	}

	allRuleFail := true
	// Algo传过来的request.Code有时候会不可靠，因此要先检查是否所有rule都失败，所有rule都失败的话按照整个task失败处理
	if *request.Code == edd_constant.Success {
		for _, item := range ResultBody.ForecastResults {
			if item.IsRuleSuccess {
				allRuleFail = false
				break
			}
		}
	}

	//判断code状态
	if *request.Code == edd_constant.Failed || allRuleFail { // 如果这个task中所有rule都是失败，就按照失败处理
		//更新taskdb状态和时间
		lcosErr = e.eddForecastTaskDao.UpdateEDDForecastTasks(ctx, map[string]interface{}{"id": request.ForecastTaskId}, map[string]interface{}{"forecast_status": edd_constant.ForecastingFailed, "forecast_message": request.Message, "forecast_complete_time": nowTime})
		if lcosErr != nil {
			return lcosErr
		}
		return nil

	} else if *request.Code == edd_constant.Success {
		if request.IsCB && ForecastTask.CBType != constant.CBType {
			return lcos_error.NewLCOSError(lcos_error.ParamsError, "cb flag not match")
		}
		if !request.IsCB && ForecastTask.CBType != constant.LocalType {
			return lcos_error.NewLCOSError(lcos_error.ParamsError, "cb flag not match")
		}

		var TaskStatusFlag = edd_constant.ForecastingComplete
		models := make([]*edd_forecast_result2.EDDForecastResultTab, 0, len(ResultBody.ForecastResults))
		for _, item := range ResultBody.ForecastResults {
			var RuleStatus = edd_constant.Success
			if !item.IsRuleSuccess {
				RuleStatus = edd_constant.Failed
				TaskStatusFlag = edd_constant.ForecastingPartialSuccess
			}

			// SPLN-32530 对于所有的场景，rule name都要赋值进去不能留空
			for _, ruleItem := range ForecastTask.EddAutoUpdateConfigList {
				if item.RuleId == uint64(ruleItem.RuleID) && len(ruleItem.RuleName) == 0 && len(item.RuleName) > 0 {
					ruleItem.RuleName = item.RuleName
				}
			}

			if ForecastTask.IsUserDefineTask() { // SPLN-30795 for user defines rule, rule name come from user define edd auto update rule
				for _, ruleItem := range ForecastTask.EddAutoUpdateConfigList {
					if item.RuleId == uint64(ruleItem.RuleID) {
						item.RuleName = ruleItem.RuleName
					}
				}
			} else if ForecastTask.IsSystemRecommendTask() && ForecastTask.IsEDD() { // SPLN-30795, for system recommend rule, need to check lead config and edd update rule is not empty
				if item.LeadTimeInfo == nil || item.EDDUpdateRule == nil || len(item.EDDUpdateRule.UpdateEventRuleList) <= 0 {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "leadtime info and edd update rule cannot be empty")
				}
			} else if ForecastTask.IsSystemRecommendTask() && ForecastTask.IsEDT() {
				if item.LeadTimeInfo == nil {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "leadtime info cannot be empty")
				}
			}
			models = append(models, &edd_forecast_result2.EDDForecastResultTab{
				Region:         region,
				ForecastTaskID: request.ForecastTaskId,
				ForecastResult: item,
				RuleID:         item.RuleId,
				RuleStatus:     RuleStatus,
			})
		}

		fn := func() *lcos_error.LCOSError {
			if err := e.eddForecastTaskDao.UpdateEDDForecastTasks(ctx, map[string]interface{}{"id": request.ForecastTaskId}, map[string]interface{}{"forecast_status": TaskStatusFlag, "forecast_complete_time": nowTime, "deploy_status": edd_constant.PendingDeploy, "edd_auto_update_config_list": ForecastTask.EddAutoUpdateConfigList}); err != nil {
				return err
			}
			if err := e.eddForecastResultDao.BatchCreateEDDForecastResult(ctx, models); err != nil {
				return err
			}
			return nil
		}

		if err := ctx.Transaction(fn); err != nil {
			return err
		}
		return nil

	}
	return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot match this code type| request.Code=[%d]", request.Code)
}

func (e *eddForecastTaskResult) DetailEDDForecastTaskResult(ctx utils.LCOSContext, request *edd_forecast_result.DetailEDDForecastTaskResultRequest) (*edd_forecast_result2.DetailEDDForecastTaskResultResponse, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	ForecastTasks, lcosErr := e.eddForecastTaskDao.SearchEDDForecastTasks(ctx, map[string]interface{}{"id": request.ForecastTaskID})
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		return nil, lcosErr
	}
	if len(ForecastTasks) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.ParamsError, "taskId not find in task db")
	}

	ForecastTask := ForecastTasks[0]
	ForecastResults, lcosErr := e.eddForecastResultDao.SearchEDDForecastResults(ctx, map[string]interface{}{"region": region, "forecast_task_id": request.ForecastTaskID})
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		return nil, lcosErr
	}

	rsp := &edd_forecast_result2.DetailEDDForecastTaskResultResponse{}

	rsp.IsCB = ForecastTask.CBType != constant.LocalType
	rsp.TaskId = ForecastTask.ID
	rsp.ForecastResults = []edd_forecast_result2.ForecastResult{}

	for _, item := range ForecastResults {
		rsp.ForecastResults = append(rsp.ForecastResults, item.ForecastResult)
	}

	return rsp, nil
}

func exportOverview(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab, objectType uint8) *lcos_error.LCOSError {
	// regroup from group by rule to group by event
	// {
	//    "event_name1":{
	//        "rule_name1":{
	//            "accuracy":0.99,
	//            "precision":0.8,
	//            "avg_day_from_edd_range":0.7,
	//            "avg_edd_range":0.3
	//        },
	//        "rule_name2":{
	//            "accuracy":0.99,
	//            "precision":0.8,
	//            "avg_day_from_edd_range":0.7,
	//            "avg_edd_range":0.3
	//        }
	//    }
	//}
	overviewResultsMap := make(map[string]map[string]*edd_forecast_result2.OverView)
	for _, singleRuleResult := range forecastResults {
		if singleRuleResult.ForecastResult.IsRuleSuccess {
			for eventName, result := range singleRuleResult.ForecastResult.OverView {
				if _, ok1 := overviewResultsMap[eventName]; ok1 {
					overviewResultsMap[eventName][singleRuleResult.ForecastResult.RuleName] = result
				} else {
					overviewResultsMap[eventName] = map[string]*edd_forecast_result2.OverView{
						singleRuleResult.ForecastResult.RuleName: result,
					}
				}
			}
		}
	}
	overviewLineNumber := 2
	for _, eventName := range EventSequence {
		if eventResultMap, ok := overviewResultsMap[eventName]; ok {
			for ruleName, ruleResult := range eventResultMap {
				var errorList []error
				errorList = append(errorList,
					resultFile.SetCellValue("Overall", fmt.Sprintf("A%d", overviewLineNumber), getDisplayName(eventName)),
					resultFile.SetCellValue("Overall", fmt.Sprintf("B%d", overviewLineNumber), ruleName),
					resultFile.SetCellValue("Overall", fmt.Sprintf("C%d", overviewLineNumber), returnNilOrPercent(ruleResult.Accuracy)),
					resultFile.SetCellValue("Overall", fmt.Sprintf("D%d", overviewLineNumber), returnNilOrPercent(ruleResult.Precision)),
					resultFile.SetCellValue("Overall", fmt.Sprintf("E%d", overviewLineNumber), getDisplayFloat(ruleResult.AvgDayFromEddRange)),
					resultFile.SetCellValue("Overall", fmt.Sprintf("F%d", overviewLineNumber), getDisplayFloat(ruleResult.AvgEddRange)),
					resultFile.SetCellValue("Overall", fmt.Sprintf("G%d", overviewLineNumber), returnNilOrPercent(ruleResult.OrderCount)),
				)
				for _, singleError := range errorList {
					if singleError != nil {
						return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot export overview|event_name=[%s], rule_name=[%s], line_number=[%d]", eventName, ruleName, overviewLineNumber)
					}
				}
				overviewLineNumber++
			}
		}

	}
	return nil
}

func exportDistribution(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab) *lcos_error.LCOSError {
	// regroup from group by rule to group by event
	// {
	//    "event_name1":{
	//        "rule_name1":{
	//            "early":{
	//                "more_than_3_days":0.5,
	//                "3_days":0.3,
	//                "2_days":0.1,
	//                "1_days":0.2
	//            },
	//            "on_time":0.1,
	//            "late":{
	//                "more_than_2_days":0.3,
	//                "2_days":0.3,
	//                "1_days":0.4
	//            }
	//        },
	//        "rule_name2":{
	//            "early":{
	//                "more_than_3_days":0.5,
	//                "3_days":0.3,
	//                "2_days":0.1,
	//                "1_days":0.2
	//            },
	//            "on_time":0.1,
	//            "late":{
	//                "more_than_2_days":0.3,
	//                "2_days":0.3,
	//                "1_days":0.4
	//            }
	//        }
	//    }
	//}
	distributionResultsMap := make(map[string]map[string]*edd_forecast_result2.Distribution)
	for _, singleRuleResult := range forecastResults {
		if singleRuleResult.ForecastResult.IsRuleSuccess {
			for eventName, result := range singleRuleResult.ForecastResult.Distribution {
				if _, ok1 := distributionResultsMap[eventName]; ok1 {
					distributionResultsMap[eventName][singleRuleResult.ForecastResult.RuleName] = result
				} else {
					distributionResultsMap[eventName] = map[string]*edd_forecast_result2.Distribution{
						singleRuleResult.ForecastResult.RuleName: result,
					}
				}
			}
		}
	}
	distributionLineNumber := 3
	for _, eventName := range EventSequence {
		if eventResultMap, ok := distributionResultsMap[eventName]; ok {
			for ruleName, ruleResult := range eventResultMap {
				var errorList []error
				errorList = append(errorList,
					resultFile.SetCellValue("Distribution", fmt.Sprintf("A%d", distributionLineNumber), getDisplayName(eventName)),
					resultFile.SetCellValue("Distribution", fmt.Sprintf("B%d", distributionLineNumber), ruleName),
					resultFile.SetCellValue("Distribution", fmt.Sprintf("C%d", distributionLineNumber), returnNilOrPercent(ruleResult.Early.MoreThan3Days)),
					resultFile.SetCellValue("Distribution", fmt.Sprintf("D%d", distributionLineNumber), returnNilOrPercent(ruleResult.Early.ThreeDays)),
					resultFile.SetCellValue("Distribution", fmt.Sprintf("E%d", distributionLineNumber), returnNilOrPercent(ruleResult.Early.TwoDays)),
					resultFile.SetCellValue("Distribution", fmt.Sprintf("F%d", distributionLineNumber), returnNilOrPercent(ruleResult.Early.OneDay)),
					resultFile.SetCellValue("Distribution", fmt.Sprintf("G%d", distributionLineNumber), returnNilOrPercent(ruleResult.OnTime)),
					resultFile.SetCellValue("Distribution", fmt.Sprintf("H%d", distributionLineNumber), returnNilOrPercent(ruleResult.Late.OneDay)),
					resultFile.SetCellValue("Distribution", fmt.Sprintf("I%d", distributionLineNumber), returnNilOrPercent(ruleResult.Late.TwoDays)),
					resultFile.SetCellValue("Distribution", fmt.Sprintf("J%d", distributionLineNumber), returnNilOrPercent(ruleResult.Late.MoreThan2Days)),
				)
				for _, singleError := range errorList {
					if singleError != nil {
						return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot export distribution|event_name=[%s], rule_name=[%s], line_number=[%d]", eventName, ruleName, distributionLineNumber)
					}
				}
				distributionLineNumber++
			}
		}
	}
	return nil
}

func exportLateRate(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab) *lcos_error.LCOSError {
	// regroup from group by rule to group by event
	// {
	//    "event_name1":{
	//        "rule_name1":0.7,
	//        "rule_name2":0.3
	//    }
	//}
	lateRateResultsMap := make(map[string]map[string]*edd_forecast_result2.LateRate)
	for _, singleRuleResult := range forecastResults {
		if singleRuleResult.ForecastResult.IsRuleSuccess {
			for eventName, result := range singleRuleResult.ForecastResult.LateRate {
				if _, ok1 := lateRateResultsMap[eventName]; ok1 {
					lateRateResultsMap[eventName][singleRuleResult.ForecastResult.RuleName] = result
				} else {
					lateRateResultsMap[eventName] = map[string]*edd_forecast_result2.LateRate{
						singleRuleResult.ForecastResult.RuleName: result,
					}
				}
			}
		}
	}
	lateRateLineNumber := 2
	for _, eventName := range EventSequence {
		if eventResultMap, ok := lateRateResultsMap[eventName]; ok {
			for ruleName, ruleResult := range eventResultMap {
				var errorList []error
				errorList = append(errorList,
					resultFile.SetCellValue("Late Rate", fmt.Sprintf("A%d", lateRateLineNumber), getDisplayName(eventName)),
					resultFile.SetCellValue("Late Rate", fmt.Sprintf("B%d", lateRateLineNumber), ruleName),
					resultFile.SetCellValue("Late Rate", fmt.Sprintf("C%d", lateRateLineNumber), returnNilOrPercent(ruleResult.LateRatio)),
				)
				for _, singleError := range errorList {
					if singleError != nil {
						return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot export late rate|event_name=[%s], rule_name=[%s], line_number=[%d]", eventName, ruleName, lateRateLineNumber)
					}
				}
				lateRateLineNumber++
			}
		}
	}
	return nil
}

func exportEDDUpdateDirectionChange(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab) *lcos_error.LCOSError {
	updateTimesSequence := []string{"update_times_0", "update_times_1", "update_times_2", "update_times_3", "update_times_4", "update_times_more_than_4"}
	updateTimesKeyMap := map[string]string{
		"update_times_0":           "0",
		"update_times_1":           "1",
		"update_times_2":           "2",
		"update_times_3":           "3",
		"update_times_4":           "4",
		"update_times_more_than_4": ">4",
	}

	lineNum := 3

	// loop over rule
	for _, singleRule := range forecastResults {
		if !singleRule.ForecastResult.IsRuleSuccess {
			continue
		}

		if singleRule.ForecastResult.EddUpdateDirectionChange.EddMin == nil {
			singleRule.ForecastResult.EddUpdateDirectionChange.EddMin = map[string]*edd_forecast_result2.UpdateTime{}
		}
		if singleRule.ForecastResult.EddUpdateDirectionChange.EddMax == nil {
			singleRule.ForecastResult.EddUpdateDirectionChange.EddMax = map[string]*edd_forecast_result2.UpdateTime{}
		}

		// loop over update time
		for _, singleUpdateTime := range updateTimesSequence {

			// pre process the result, fill all update times sequence key to avoid panic
			if _, ok := singleRule.ForecastResult.EddUpdateDirectionChange.EddMin[singleUpdateTime]; !ok {
				singleRule.ForecastResult.EddUpdateDirectionChange.EddMin[singleUpdateTime] = &edd_forecast_result2.UpdateTime{}
			}
			if _, ok := singleRule.ForecastResult.EddUpdateDirectionChange.EddMax[singleUpdateTime]; !ok {
				singleRule.ForecastResult.EddUpdateDirectionChange.EddMax[singleUpdateTime] = &edd_forecast_result2.UpdateTime{}
			}

			var errorList []error
			errorList = append(errorList,
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("A%d", lineNum), singleRule.ForecastResult.RuleName),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("B%d", lineNum), updateTimesKeyMap[singleUpdateTime]),
				// EDD Min
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("C%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateDirectionChange.EddMin[singleUpdateTime].OrderRatio)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("D%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMin[singleUpdateTime].AllExtendedCount)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("E%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMin[singleUpdateTime].AllDeductedCount)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("F%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMin[singleUpdateTime].ChangeDirection1)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("G%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMin[singleUpdateTime].ChangeDirection2)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("H%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMin[singleUpdateTime].ChangeDirectionMoreThan2)),
				// EDD Max
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("I%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateDirectionChange.EddMax[singleUpdateTime].OrderRatio)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("J%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMax[singleUpdateTime].AllExtendedCount)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("K%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMax[singleUpdateTime].AllDeductedCount)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("L%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMax[singleUpdateTime].ChangeDirection1)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("M%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMax[singleUpdateTime].ChangeDirection2)),
				resultFile.SetCellValue("EDD Update Direction Change%", fmt.Sprintf("N%d", lineNum), returnNilOrInt(singleRule.ForecastResult.EddUpdateDirectionChange.EddMax[singleUpdateTime].ChangeDirectionMoreThan2)),
			)
			for _, singleError := range errorList {
				if singleError != nil {
					return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot export EDD Update Direction Change|rule_name=[%s], update_times=[%s], line_number=[%d]", singleRule.ForecastResult.RuleName, singleUpdateTime, lineNum)
				}
			}
			lineNum++
		}
	}
	return nil
}

func exportEDDUpdateFinalVSInitial(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab) *lcos_error.LCOSError {

	lineNum := 4

	// loop over rule
	for _, singleRule := range forecastResults {
		if !singleRule.ForecastResult.IsRuleSuccess {
			continue
		}

		var errorList []error
		errorList = append(errorList,
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("A%d", lineNum), singleRule.ForecastResult.RuleName),

			// EDD Min
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("B%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMin.FinalEarlierThanInitial.MoreThan2Days)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("C%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMin.FinalEarlierThanInitial.TwoDays)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("D%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMin.FinalEarlierThanInitial.OneDay)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("E%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMin.FinalEqualsInitial)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("F%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMin.FinalLaterThanInitial.OneDay)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("G%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMin.FinalLaterThanInitial.TwoDays)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("H%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMin.FinalLaterThanInitial.MoreThan2Days)),
			// EDD Max
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("I%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMax.FinalEarlierThanInitial.MoreThan2Days)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("J%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMax.FinalEarlierThanInitial.TwoDays)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("K%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMax.FinalEarlierThanInitial.OneDay)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("L%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMax.FinalEqualsInitial)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("M%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMax.FinalLaterThanInitial.OneDay)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("N%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMax.FinalLaterThanInitial.TwoDays)),
			resultFile.SetCellValue("EDD Update Final vs Initial", fmt.Sprintf("O%d", lineNum), returnNilOrPercent(singleRule.ForecastResult.EddUpdateFinalVsInitial.OverallDateDiff.EddMax.FinalLaterThanInitial.MoreThan2Days)),
		)
		for _, singleError := range errorList {
			if singleError != nil {
				return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot export EDD Update Final vs Initial|rule_name=[%s],  line_number=[%d]", singleRule.ForecastResult.RuleName, lineNum)
			}
		}
		lineNum++
	}
	return nil
}

func exportEDDAdvanceNotice(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab) *lcos_error.LCOSError {
	// regroup from group by rule to group by event
	// {
	//    "event_name1":{
	//        "rule_name1":{
	//            "deducted":{
	//                "less_than_zero_days":0.1,
	//                "1_day":0.1,
	//                "2_day":0.1,
	//                "more_than_2_days":0.1
	//            },
	//            "extended":{
	//                "less_than_zero_days":0.1,
	//                "1_day":0.1,
	//                "2_day":0.1,
	//                "more_than_2_days":0.1
	//            }
	//        },
	//        "rule_name2":{
	//            "deducted":{
	//                "less_than_zero_days":0.1,
	//                "1_day":0.1,
	//                "2_day":0.1,
	//                "more_than_2_days":0.1
	//            },
	//            "extended":{
	//                "less_than_zero_days":0.1,
	//                "1_day":0.1,
	//                "2_day":0.1,
	//                "more_than_2_days":0.1
	//            }
	//        }
	//    }
	//}

	lineNum := 3
	advanceNoticeMap := make(map[string]map[string]*edd_forecast_result2.AdvanceNoticeForEddMaxUpdate)

	// loop over rule
	for _, singleRule := range forecastResults {
		if singleRule.ForecastResult.IsRuleSuccess {
			for eventName, result := range singleRule.ForecastResult.AdvanceNoticeForEddMaxUpdate {
				if _, ok1 := advanceNoticeMap[eventName]; ok1 {
					advanceNoticeMap[eventName][singleRule.ForecastResult.RuleName] = result
				} else {
					advanceNoticeMap[eventName] = map[string]*edd_forecast_result2.AdvanceNoticeForEddMaxUpdate{
						singleRule.ForecastResult.RuleName: result,
					}
				}
			}
		}
	}

	// fill edd advance notice
	for _, eventName := range EventSequence {
		if eventResultMap, ok := advanceNoticeMap[eventName]; ok {
			for ruleName, ruleResult := range eventResultMap {
				var errorList []error
				errorList = append(errorList,
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("A%d", lineNum), getDisplayName(eventName)),
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("B%d", lineNum), ruleName),
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("C%d", lineNum), returnNilOrPercent(ruleResult.Deducted.LessThanZeroDays)),
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("D%d", lineNum), returnNilOrPercent(ruleResult.Deducted.OneDay)),
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("E%d", lineNum), returnNilOrPercent(ruleResult.Deducted.TwoDay)),
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("F%d", lineNum), returnNilOrPercent(ruleResult.Deducted.MoreThan2Days)),
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("G%d", lineNum), returnNilOrPercent(ruleResult.Extended.LessThanZeroDays)),
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("H%d", lineNum), returnNilOrPercent(ruleResult.Extended.OneDay)),
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("I%d", lineNum), returnNilOrPercent(ruleResult.Extended.TwoDay)),
					resultFile.SetCellValue("Advance Notice for EDD Max Upda", fmt.Sprintf("J%d", lineNum), returnNilOrPercent(ruleResult.Extended.MoreThan2Days)),
				)
				for _, singleError := range errorList {
					if singleError != nil {
						return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot export advance notice|event_name=[%s], rule_name=[%s], line_number=[%d]", eventName, ruleName, lineNum)
					}
				}
				lineNum++
			}
		}
	}
	return nil
}

func exportTarget(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab) *lcos_error.LCOSError {

	lineNum := 2

	singleRowWrite := func(currentLineNum int, ruleName, metrics string, target edd_forecast_result2.TargetResults) string {
		var errorList []error
		errorList = append(errorList,
			resultFile.SetCellValue("Target", fmt.Sprintf("A%d", currentLineNum), ruleName),
			resultFile.SetCellValue("Target", fmt.Sprintf("B%d", currentLineNum), metrics),
			resultFile.SetCellValue("Target", fmt.Sprintf("C%d", currentLineNum), returnNilOrInt(int64(target.Priority))),
			resultFile.SetCellValue("Target", fmt.Sprintf("D%d", currentLineNum), returnNilOrPercent(target.Metrics)),
			resultFile.SetCellValue("Target", fmt.Sprintf("E%d", currentLineNum), returnNilOrPercent(target.SimulationResults)),
		)
		for _, singleError := range errorList {
			if singleError != nil {
				return fmt.Sprintf("cannot export EDD Update Direction Change|rule_name=[%s], line_number=[%d], metrics=[%s]|error=[%s]", ruleName, currentLineNum, metrics, singleError.Error())
			}
		}
		return ""
	}

	// loop over rule
	for _, singleRule := range forecastResults {
		if !singleRule.ForecastResult.IsRuleSuccess {
			continue
		}
		errMessage := make([]string, 0, 6)
		errMessage = append(errMessage, singleRowWrite(lineNum, singleRule.ForecastResult.RuleName, "Initial Accuracy-% attempted after initial EDD", singleRule.ForecastResult.Targets.InitialAccuracy))
		errMessage = append(errMessage, singleRowWrite(lineNum+1, singleRule.ForecastResult.RuleName, "Initial Precision-% attempted on initial EDD", singleRule.ForecastResult.Targets.InitialPrecision))
		errMessage = append(errMessage, singleRowWrite(lineNum+2, singleRule.ForecastResult.RuleName, "Final Accuracy-% attempted after final EDD", singleRule.ForecastResult.Targets.FinalAccuracy))
		errMessage = append(errMessage, singleRowWrite(lineNum+3, singleRule.ForecastResult.RuleName, "Final Precision-% attempted on final EDD", singleRule.ForecastResult.Targets.FinalPrecision))

		initialEDDRangeMetrics := fmt.Sprintf("Average Initial EDD Range-%% with Initial EDD Range of %d Days", singleRule.ForecastResult.Targets.AverageInitialEDDRange.RangeDays)
		errMessage = append(errMessage, singleRowWrite(lineNum+4, singleRule.ForecastResult.RuleName, initialEDDRangeMetrics, singleRule.ForecastResult.Targets.AverageInitialEDDRange))

		finalEDDRangeMetrics := fmt.Sprintf("Average Final EDD Range-%% with Final EDD Range of %d Days", singleRule.ForecastResult.Targets.AverageFinalEDDRange.RangeDays)
		errMessage = append(errMessage, singleRowWrite(lineNum+5, singleRule.ForecastResult.RuleName, finalEDDRangeMetrics, singleRule.ForecastResult.Targets.AverageFinalEDDRange))

		for _, singleMessage := range errMessage {
			if len(singleMessage) > 0 {
				return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, singleMessage)
			}
		}
		lineNum += 6
	}
	return nil
}

func exportEdtTarget(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab) *lcos_error.LCOSError {

	lineNum := 2

	singleRowWrite := func(currentLineNum int, ruleName, metrics string, target edd_forecast_result2.TargetResults) string {
		var errorList []error
		errorList = append(errorList,
			resultFile.SetCellValue("Target", fmt.Sprintf("A%d", currentLineNum), ruleName),
			resultFile.SetCellValue("Target", fmt.Sprintf("B%d", currentLineNum), metrics),
			resultFile.SetCellValue("Target", fmt.Sprintf("C%d", currentLineNum), returnNilOrInt(int64(target.Priority))),
			resultFile.SetCellValue("Target", fmt.Sprintf("D%d", currentLineNum), returnNilOrPercent(target.Metrics)),
			resultFile.SetCellValue("Target", fmt.Sprintf("E%d", currentLineNum), returnNilOrPercent(target.SimulationResults)),
		)
		for _, singleError := range errorList {
			if singleError != nil {
				return fmt.Sprintf("cannot export EDD Update Direction Change|rule_name=[%s], line_number=[%d], metrics=[%s]|error=[%s]", ruleName, currentLineNum, metrics, singleError.Error())
			}
		}
		return ""
	}

	// loop over rule
	for _, singleRule := range forecastResults {
		if !singleRule.ForecastResult.IsRuleSuccess {
			continue
		}
		errMessage := make([]string, 0, 3)
		errMessage = append(errMessage, singleRowWrite(lineNum, singleRule.ForecastResult.RuleName, "EDT Accuracy-% attempted after initial EDD", singleRule.ForecastResult.EdtTargets.EDTAccuracy))
		errMessage = append(errMessage, singleRowWrite(lineNum+1, singleRule.ForecastResult.RuleName, "EDT Precision-% attempted on initial EDD", singleRule.ForecastResult.EdtTargets.EDTPrecision))

		rangeMetrics := fmt.Sprintf("Average EDT Range-%% with Initial EDD Range of %d Days", singleRule.ForecastResult.EdtTargets.AverageEdtRange.RangeDays)
		errMessage = append(errMessage, singleRowWrite(lineNum+2, singleRule.ForecastResult.RuleName, rangeMetrics, singleRule.ForecastResult.EdtTargets.AverageEdtRange))

		for _, singleMessage := range errMessage {
			if len(singleMessage) > 0 {
				return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, singleMessage)
			}
		}
		lineNum += 3
	}
	return nil
}

func exportEDDRange(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab) *lcos_error.LCOSError {
	// regroup from group by rule to group by event
	// {
	//    "event_name1":{
	//        "rule1":{
	//            "zero_days":1,
	//            "one_day":2,
	//            "two_days":3,
	//            "three_days":4,
	//            "more_than_three_days":5
	//        },
	//        "rule2":{
	//            "zero_days":1,
	//            "one_day":2,
	//            "two_days":3,
	//            "three_days":4,
	//            "more_than_three_days":5
	//        }
	//    }
	//}

	lineNum := 3
	eddRangeMap := make(map[string]map[string]*edd_forecast_result2.SingleEDDRange)

	// loop over rule
	for _, singleRule := range forecastResults {
		if singleRule.ForecastResult.IsRuleSuccess {
			for eventName, result := range singleRule.ForecastResult.EDDRange {
				if _, ok1 := eddRangeMap[eventName]; ok1 {
					eddRangeMap[eventName][singleRule.ForecastResult.RuleName] = result
				} else {
					eddRangeMap[eventName] = map[string]*edd_forecast_result2.SingleEDDRange{
						singleRule.ForecastResult.RuleName: result,
					}
				}
			}
		}
	}

	// fill in edd range
	for _, eventName := range EventSequence {
		if eventResultMap, ok := eddRangeMap[eventName]; ok {
			for ruleName, ruleResult := range eventResultMap {
				var errorList []error
				errorList = append(errorList,
					resultFile.SetCellValue("EDD Range(Distribution)", fmt.Sprintf("A%d", lineNum), getDisplayName(eventName)),
					resultFile.SetCellValue("EDD Range(Distribution)", fmt.Sprintf("B%d", lineNum), ruleName),
					resultFile.SetCellValue("EDD Range(Distribution)", fmt.Sprintf("C%d", lineNum), returnNilOrPercent(ruleResult.ZeroDays)),
					resultFile.SetCellValue("EDD Range(Distribution)", fmt.Sprintf("D%d", lineNum), returnNilOrPercent(ruleResult.OneDay)),
					resultFile.SetCellValue("EDD Range(Distribution)", fmt.Sprintf("E%d", lineNum), returnNilOrPercent(ruleResult.TwoDays)),
					resultFile.SetCellValue("EDD Range(Distribution)", fmt.Sprintf("F%d", lineNum), returnNilOrPercent(ruleResult.ThreeDays)),
					resultFile.SetCellValue("EDD Range(Distribution)", fmt.Sprintf("G%d", lineNum), returnNilOrPercent(ruleResult.MoreThanThreeDays)),
				)
				for _, singleError := range errorList {
					if singleError != nil {
						return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot export edd range|event_name=[%s], rule_name=[%s], line_number=[%d]|error=[%s]", eventName, ruleName, lineNum, singleError.Error())
					}
				}
				lineNum++
			}
		}
	}
	return nil
}

func exportEDDUpdateAtEventLevel(resultFile *excelize.File, forecastResults []*edd_forecast_result2.EDDForecastResultTab) *lcos_error.LCOSError {
	// regroup from group by rule to group by event
	// {
	//    "event_name1":{
	//        "rule1":{
	//            "less_than_minus_two_days":1,
	//            "minus_two_days":2,
	//            "minus_one_days":3,
	//            "zero_days":4,
	//            "one_day":5,
	//            "two_days":2,
	//            "more_than_two_days":3
	//        },
	//        "rule2":{
	//            "less_than_minus_two_days":1,
	//            "minus_two_days":2,
	//            "minus_one_days":3,
	//            "zero_days":4,
	//            "one_day":5,
	//            "two_days":2,
	//            "more_than_two_days":3
	//        }
	//    }
	//}

	lineNum := 3
	eddRangeMap := make(map[string]map[string]*edd_forecast_result2.SingleEDDUpdatesAtEventLevel)

	// loop over rule
	for _, singleRule := range forecastResults {
		if singleRule.ForecastResult.IsRuleSuccess {
			for eventName, result := range singleRule.ForecastResult.EDDUpdatesAtEventLevel {
				if _, ok1 := eddRangeMap[eventName]; ok1 {
					eddRangeMap[eventName][singleRule.ForecastResult.RuleName] = result
				} else {
					eddRangeMap[eventName] = map[string]*edd_forecast_result2.SingleEDDUpdatesAtEventLevel{
						singleRule.ForecastResult.RuleName: result,
					}
				}
			}
		}
	}

	// fill in edd update at event level
	for _, eventName := range EventSequence {
		if eventResultMap, ok := eddRangeMap[eventName]; ok {
			for ruleName, ruleResult := range eventResultMap {
				var errorList []error
				errorList = append(errorList,
					resultFile.SetCellValue("EDD Update at Event Level", fmt.Sprintf("A%d", lineNum), getDisplayName(eventName)),
					resultFile.SetCellValue("EDD Update at Event Level", fmt.Sprintf("B%d", lineNum), ruleName),
					resultFile.SetCellValue("EDD Update at Event Level", fmt.Sprintf("C%d", lineNum), returnNilOrPercent(ruleResult.LessThanMinusTwoDays)),
					resultFile.SetCellValue("EDD Update at Event Level", fmt.Sprintf("D%d", lineNum), returnNilOrPercent(ruleResult.MinusTwoDays)),
					resultFile.SetCellValue("EDD Update at Event Level", fmt.Sprintf("E%d", lineNum), returnNilOrPercent(ruleResult.MinusOneDays)),
					resultFile.SetCellValue("EDD Update at Event Level", fmt.Sprintf("F%d", lineNum), returnNilOrPercent(ruleResult.ZeroDays)),
					resultFile.SetCellValue("EDD Update at Event Level", fmt.Sprintf("G%d", lineNum), returnNilOrPercent(ruleResult.OneDay)),
					resultFile.SetCellValue("EDD Update at Event Level", fmt.Sprintf("H%d", lineNum), returnNilOrPercent(ruleResult.TwoDays)),
					resultFile.SetCellValue("EDD Update at Event Level", fmt.Sprintf("I%d", lineNum), returnNilOrPercent(ruleResult.MoreThanTwoDays)),
				)
				for _, singleError := range errorList {
					if singleError != nil {
						return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot export edd update at event level|event_name=[%s], rule_name=[%s], line_number=[%d]|error=[%s]", eventName, ruleName, lineNum, singleError.Error())
					}
				}
				lineNum++
			}
		}
	}
	return nil
}

func (e *eddForecastTaskResult) ExportEDDForecastTaskResult(ctx utils.LCOSContext, forecastTaskID uint64, region string) (*excelize.File, *lcos_error.LCOSError) {
	// get forecast rule first
	forecastTasks, lcosErr := e.eddForecastTaskDao.SearchEDDForecastTasks(ctx, map[string]interface{}{"id": forecastTaskID, "region": region})
	if lcosErr != nil || len(forecastTasks) <= 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find task|forecast_task_id=[%d]", forecastTaskID)
	}
	forecastTask := forecastTasks[0]

	// get forecast result first
	forecastResults, lcosErr := e.eddForecastResultDao.SearchEDDForecastResults(ctx, map[string]interface{}{"forecast_task_id": forecastTaskID, "region": region})
	if lcosErr != nil || len(forecastResults) <= 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find results for task|forecast_task_id=[%d]", forecastTaskID)
	}

	var templateFileName string
	if forecastTask.IsSystemRecommendTask() {
		if forecastTask.ObjectType == edd_constant.LeadTimeObject {
			templateFileName = path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/forecasts", edd_constant.SystemRecommendForecastResultFileName)
		} else {
			templateFileName = path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/forecasts", edd_constant.SystemRecommendForecastResultFileCDTName)
		}
	} else {
		templateFileName = path.Join(pathutil.GetProjectAbsolutePath(), "/templates/xlsx/forecasts", edd_constant.ForecastResultFileName)
	}
	resultFile, err := excelize.OpenFile(templateFileName)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file: %s|error=%s", templateFileName, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}

	// 1. fill overall sheet
	if lcosErr = exportOverview(resultFile, forecastResults, forecastTask.ObjectType); lcosErr != nil {
		return nil, lcosErr
	}

	// 2. fill distribution
	if lcosErr = exportDistribution(resultFile, forecastResults); lcosErr != nil {
		return nil, lcosErr
	}

	// 3. fill late rate
	if lcosErr = exportLateRate(resultFile, forecastResults); lcosErr != nil {
		return nil, lcosErr
	}

	// 只有EDD的仿真才有这部分EDD自动更新规则
	if forecastTask.ObjectType == edd_constant.LeadTimeObject {
		// 4. fill EDD Update Direction Change%
		if lcosErr = exportEDDUpdateDirectionChange(resultFile, forecastResults); lcosErr != nil {
			return nil, lcosErr
		}

		// 5. fill EDD Update Final vs Initial
		if lcosErr = exportEDDUpdateFinalVSInitial(resultFile, forecastResults); lcosErr != nil {
			return nil, lcosErr
		}

		// 6. fill advance notice for EDD Max Update
		if lcosErr = exportEDDAdvanceNotice(resultFile, forecastResults); lcosErr != nil {
			return nil, lcosErr
		}
	}

	if forecastTask.IsSystemRecommendTask() {
		// 只有EDD的仿真才有这部分EDD自动更新规则
		if forecastTask.ObjectType == edd_constant.LeadTimeObject {
			// 7. fill in target sheet
			if lcosErr = exportTarget(resultFile, forecastResults); lcosErr != nil {
				return nil, lcosErr
			}

			// 8. fill in edd range
			if lcosErr = exportEDDRange(resultFile, forecastResults); lcosErr != nil {
				return nil, lcosErr
			}

			// 9. fill in edd update at event level
			if lcosErr = exportEDDUpdateAtEventLevel(resultFile, forecastResults); lcosErr != nil {
				return nil, lcosErr
			}
		} else {
			// 7. fill in target sheet
			if lcosErr = exportEdtTarget(resultFile, forecastResults); lcosErr != nil {
				return nil, lcosErr
			}
		}
	}

	return resultFile, nil
}

func (e *eddForecastTaskResult) GetDeployInfo(ctx utils.LCOSContext, forecastTaskID uint64, region string) ([]edd_forecast_result.ForecastResultDeployInfo, *lcos_error.LCOSError) {
	var taskNamesInfo []edd_forecast_result.ForecastResultDeployInfo

	// get forecast task first
	forecastTasks, lcosErr := e.eddForecastTaskDao.SearchEDDForecastTasks(ctx, map[string]interface{}{"id": forecastTaskID, "region": region})
	if lcosErr != nil || len(forecastTasks) <= 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find forecast task|forecast_task_id=[%d], region=[%s]", forecastTaskID, region)
	}
	forecastTask := forecastTasks[0]
	if forecastTask.IsUserDefineTask() {
		// for user define task, the deployed tasks come from task itself
		for _, singleRule := range forecastTask.EddAutoUpdateConfigList {
			taskNamesInfo = append(taskNamesInfo, edd_forecast_result.ForecastResultDeployInfo{
				RuleName: singleRule.RuleName,
				RuleID:   singleRule.RuleID,
			})
		}
	} else if forecastTask.IsSystemRecommendTask() {
		forecastResults, lcosErr := e.eddForecastResultDao.SearchEDDForecastResults(ctx, map[string]interface{}{"forecast_task_id": forecastTaskID, "region": region})
		if lcosErr != nil || len(forecastResults) <= 0 {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find results for task|forecast_task_id=[%d]", forecastTaskID)
		}
		for _, singleResult := range forecastResults {
			if singleResult.ForecastResult.Targets != nil {
				taskNamesInfo = append(taskNamesInfo, edd_forecast_result.ForecastResultDeployInfo{
					RuleName:       singleResult.ForecastResult.RuleName,
					RuleID:         uint32(singleResult.ForecastResult.RuleId),
					WarningMessage: singleResult.ForecastResult.Targets.CheckMeetTargets(),
				})
			}
			if singleResult.ForecastResult.EdtTargets != nil {
				taskNamesInfo = append(taskNamesInfo, edd_forecast_result.ForecastResultDeployInfo{
					RuleName:       singleResult.ForecastResult.RuleName,
					RuleID:         uint32(singleResult.ForecastResult.RuleId),
					WarningMessage: singleResult.ForecastResult.EdtTargets.CheckMeetTargets(),
				})
			}
		}
	}
	return taskNamesInfo, nil
}

func NewEDDForecastTaskResult(eddForecastResultDao edd_forecast_result2.EDDForecastResultDao,
	eddForecastTaskDao edd_forecast_task2.EDDForecastTaskDao) *eddForecastTaskResult {
	return &eddForecastTaskResult{
		eddForecastResultDao: eddForecastResultDao,
		eddForecastTaskDao:   eddForecastTaskDao,
	}
}

var _ EDDForecastTaskResultInterface = (*eddForecastTaskResult)(nil)
