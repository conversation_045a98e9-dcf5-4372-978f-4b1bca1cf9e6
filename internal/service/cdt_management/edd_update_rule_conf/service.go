package edd_update_rule_conf

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_update_rule_conf"
	edd_update_rule_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_rule_conf"
	"strings"
)

type EDDUpdateRuleConfInterface interface {
	CreateUpdateEDDConfig(ctx utils.LCOSContext, request *edd_update_rule_conf.CreateEDDUpdateRuleConfRequest) *lcos_error.LCOSError
	UpdateUpdateEDDConfig(ctx utils.LCOSContext, request *edd_update_rule_conf.UpdateEDDUpdateRuleConfRequest) *lcos_error.LCOSError
	ListUpdateEDDConfig(ctx utils.LCOSContext, request *edd_update_rule_conf.ListEDDUpdateRuleConfRequest) (interface{}, *lcos_error.LCOSError)
	DeleteUpdateEDDConfig(ctx utils.LCOSContext, request *edd_update_rule_conf.DeleteEDDUpdateRuleConfRequest) *lcos_error.LCOSError
	GetUpdateEDDConfigByID(ctx utils.LCOSContext, request *edd_update_rule_conf.GetEDDUpdateRuleConfRequest) (interface{}, *lcos_error.LCOSError)
}

type eddUpdateRuleConf struct {
	eddUpdateRuleConfDao edd_update_rule_conf2.EDDUpdateRuleConfTabDAO
}

func (e *eddUpdateRuleConf) GetUpdateEDDConfigByID(ctx utils.LCOSContext, request *edd_update_rule_conf.GetEDDUpdateRuleConfRequest) (interface{}, *lcos_error.LCOSError) {
	models, lcosErr := e.eddUpdateRuleConfDao.SearchEDDUpdateRuleConf(ctx, map[string]interface{}{"id": request.ID})
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(models) <= 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find edd update conf by id:[%d]", request.ID))
	}
	return models[0], nil
}

func (e *eddUpdateRuleConf) CreateUpdateEDDConfig(ctx utils.LCOSContext, request *edd_update_rule_conf.CreateEDDUpdateRuleConfRequest) *lcos_error.LCOSError {
	models := make([]*edd_update_rule_conf2.EddUpdateRuleConfigTab, 0, len(request.ProductIDList))
	for _, singleProductID := range request.ProductIDList {
		models = append(models, &edd_update_rule_conf2.EddUpdateRuleConfigTab{
			ProductID:           singleProductID,
			MaxEddReupdateTimes: request.MaxEddReupdateTimes,
			EDDThresholdMin:     request.EDDThresholdMin,
			EDDThresholdMax:     request.EDDThresholdMax,
			Operator:            ctx.GetUserEmail(),
			Region:              strings.ToUpper(ctx.GetCountry()),
		})
	}
	return e.eddUpdateRuleConfDao.BatchCreateEDDUpdateRuleConf(ctx, models)
}

func (e *eddUpdateRuleConf) UpdateUpdateEDDConfig(ctx utils.LCOSContext, request *edd_update_rule_conf.UpdateEDDUpdateRuleConfRequest) *lcos_error.LCOSError {
	updatedData := map[string]interface{}{}
	updatedData["max_edd_reupdate_times"] = request.MaxEddReupdateTimes
	updatedData["edd_threshold_min"] = request.EDDThresholdMin
	updatedData["edd_threshold_max"] = request.EDDThresholdMax
	updatedData["operator"] = ctx.GetUserEmail()
	return e.eddUpdateRuleConfDao.UpdateEDDUpdateRuleConf(ctx, map[string]interface{}{"id": request.ID}, updatedData)
}

func (e *eddUpdateRuleConf) ListUpdateEDDConfig(ctx utils.LCOSContext, request *edd_update_rule_conf.ListEDDUpdateRuleConfRequest) (interface{}, *lcos_error.LCOSError) {
	var page uint32 = 1
	var count uint32 = 10
	queryMap := map[string]interface{}{
		"region": strings.ToUpper(ctx.GetCountry()),
	}
	if request.PageNo != nil && *request.PageNo != 0 {
		page = *request.PageNo
	}
	if request.Count != nil && *request.Count != 0 {
		count = *request.Count
	}
	if request.ProductID != nil && *request.ProductID != "" {
		queryMap["product_id"] = *request.ProductID
	}
	models, total, lcosErr := e.eddUpdateRuleConfDao.ListEDDUpdateRuleConf(ctx, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return map[string]interface{}{
		"count":  count,
		"pageno": page,
		"list":   models,
		"total":  total,
	}, nil
}

func (e *eddUpdateRuleConf) DeleteUpdateEDDConfig(ctx utils.LCOSContext, request *edd_update_rule_conf.DeleteEDDUpdateRuleConfRequest) *lcos_error.LCOSError {
	return e.eddUpdateRuleConfDao.DeleteEDDUpdateRuleConfig(ctx, request.ID)
}

func NewEDDUpdateRuleConf(eddUpdateRuleConfDao edd_update_rule_conf2.EDDUpdateRuleConfTabDAO) *eddUpdateRuleConf {
	return &eddUpdateRuleConf{
		eddUpdateRuleConfDao: eddUpdateRuleConfDao,
	}
}

var _ EDDUpdateRuleConfInterface = (*eddUpdateRuleConf)(nil)
