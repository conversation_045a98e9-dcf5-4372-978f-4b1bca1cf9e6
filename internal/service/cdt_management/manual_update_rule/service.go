package manual_update_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/item_card_edt_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seatalk"
	"os"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/cdt"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	manual_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/channel_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"golang.org/x/net/context"
)

const OVERRIDETYPE = "SHOPEE24"

type ManualUpdateServiceInterface interface {
	ListCdtProductList(ctx utils.LCOSContext, request *manual_update_rule2.ListAllProductList) ([]map[string]string, *lcos_error.LCOSError)
	ListCdtRecordPaging(ctx utils.LCOSContext, request *manual_update_rule2.ListManualUpdateRecordsPaging) (*ListManualUpdateResponse, *lcos_error.LCOSError)
	// SPLN-24104: 支持导入lane手动cdt数据
	ImportCdtManualUpdateData(ctx utils.LCOSContext, request *manual_update_rule2.ImportCdtDataRequest) (string, *lcos_error.LCOSError)
	SyncCdtManualLocationDataToRedis(ctx context.Context) *lcos_error.LCOSError
	GetDataForExport(ctx utils.LCOSContext, request *manual_update_rule2.ExportCdtDataRequest, region string) ([]*CdtData, *lcos_error.LCOSError)
	CheckManualCdtExistsByProduct(ctx utils.LCOSContext, request *manual_update_rule2.MoudleCheckOfCdtByProductRequest) (*MoudleCdtCheckRsponse, *lcos_error.LCOSError)
	DeleteManualCdtByProduct(ctx utils.LCOSContext, request *manual_update_rule2.MoudleCheckOfCdtByProductRequest) (*MoudleCdtCheckRsponse, *lcos_error.LCOSError)

	ListLaneManualUpdateLocationData(ctx utils.LCOSContext, region, productId string, isSiteLine uint8) ([]*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab, *lcos_error.LCOSError)
	ListLaneManualUpdatePostCodeData(ctx utils.LCOSContext, region, productId string, isSiteLine uint8) ([]*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab, *lcos_error.LCOSError)
	ListLaneManualUpdateCepRangeData(ctx utils.LCOSContext, region, productId string, isSiteLine uint8) ([]*lane_manual_update_rule.LaneCdtManualUpdateCepRangeDataTab, *lcos_error.LCOSError)

	SyncCdtManualUpdateDataToItemCodis(ctx utils.LCOSContext, region, productId string) *lcos_error.LCOSError
}
type manualUpdateService struct {
	manualUpdateDao     manual_update_rule.CDTManualUpdateRuleTabDAO
	laneManualUpdateDao lane_manual_update_rule.LaneCdtManualUpdateRuleDAO

	// SPLN-30795
	cdtABTestRuleDao cdt_ab_test.CdtAbTestRuleDao
}

func NewManualManipulationService(manualUpdateDao manual_update_rule.CDTManualUpdateRuleTabDAO, laneManualUpdateDao lane_manual_update_rule.LaneCdtManualUpdateRuleDAO, cdtABTestRuleDao cdt_ab_test.CdtAbTestRuleDao) *manualUpdateService {
	return &manualUpdateService{
		manualUpdateDao:     manualUpdateDao,
		laneManualUpdateDao: laneManualUpdateDao,
		cdtABTestRuleDao:    cdtABTestRuleDao,
	}
}

var _ ManualUpdateServiceInterface = (*manualUpdateService)(nil)

func (m *manualUpdateService) ListCdtProductList(ctx utils.LCOSContext, request *manual_update_rule2.ListAllProductList) ([]map[string]string, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	productIDs, lcosErr := m.manualUpdateDao.GetCdtProductList(ctx, strings.ToUpper(ctx.GetCountry()), *request.IsSiteLine, request.ObjectType)
	if lcosErr != nil {
		return nil, lcosErr
	}
	// 获取所有的channel/product
	var channelMap map[string]*channel_service.Channel
	if *request.IsSiteLine == constant.ENABLED {
		channelMap, lcosErr = product_service.GetAllProducts(ctx, region)
		if lcosErr != nil {
			return nil, lcosErr
		}
	} else {
		channelMap, lcosErr = channel_service.GetAllChannels(ctx, region)
		if lcosErr != nil {
			return nil, lcosErr
		}
	}

	var returnMapList []map[string]string

	for _, productID := range productIDs {
		if channel, ok := channelMap[productID]; ok {
			returnMapList = append(returnMapList, map[string]string{"product_id": productID, "product_name": channel.ChannelName})
		}
	}
	return returnMapList, nil
}

func (m *manualUpdateService) ListCdtRecordPaging(ctx utils.LCOSContext, request *manual_update_rule2.ListManualUpdateRecordsPaging) (*ListManualUpdateResponse, *lcos_error.LCOSError) {
	var pageNo uint32 = 1
	var count uint32 = 10
	region := strings.ToUpper(ctx.GetCountry())
	if request.PageNo != nil {
		pageNo = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}
	models, total, lcosErr := m.manualUpdateDao.GetAllCdtRecordPaging(ctx, map[string]interface{}{"region": region, "is_site_line": *request.IsSiteLine, "is_lm": *request.IsLM}, pageNo, count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	var cdtRecords []*CdtManualUpdateRecord
	for _, model := range models {
		var fileUrl string
		if model.ErrorFileUrl != "" {
			fileUrl = model.ErrorFileUrl
		} else {
			fileUrl = model.ResultFileUrl
		}
		tmpRecord := &CdtManualUpdateRecord{
			BatchId:    model.BatchId,
			FileName:   model.FileName,
			FileUrl:    fileUrl,
			Status:     model.UploadStatus,
			ImportTime: int(model.Ctime),
			Remark:     model.Remark,
			ObjectType: model.ObjectType,
		}
		cdtRecords = append(cdtRecords, tmpRecord)
	}

	response := &ListManualUpdateResponse{
		PageNo: pageNo,
		Count:  count,
		Total:  total,
		List:   cdtRecords,
	}
	return response, nil
}

func (m *manualUpdateService) SyncCdtManualLocationDataToRedis(ctx context.Context) *lcos_error.LCOSError {
	return m.manualUpdateDao.SyncAllCdtLocationDataToRedis(ctx)
}

func (m *manualUpdateService) ImportCdtManualUpdateData(ctx utils.LCOSContext, request *manual_update_rule2.ImportCdtDataRequest) (string, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	operator := ctx.GetUserEmail()
	batchID := utils.GenerateBatchId(ctx, region)
	if !(request.ObjectType == edd_constant.LeadTimeObject || request.ObjectType == edd_constant.CdtObject) {
		return "", lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "object type has to be one of CDT/Lead")
	}
	newestRecord, lcosErr := m.manualUpdateDao.GetLastCdtRecord(ctx, map[string]interface{}{"region": region, "is_site_line": *request.IsSiteLine, "is_lm": 0, "object_type": request.ObjectType})
	if lcosErr != nil {
		return "", lcosErr
	}

	// 如果上一条上传之后20分钟还没处理完，暂时不让上传新的
	if newestRecord != nil && (newestRecord.UploadStatus == constant.CdtUploadStatusInit || newestRecord.UploadStatus == constant.CdtUploadStatusProcessing) {
		if newestRecord.Ctime+20*60 > uint32(recorder.Now(ctx).Unix()) {
			return "", lcos_error.NewLCOSError(lcos_error.CdtUploadTaskAlreadyExistErrorCode, fmt.Sprintf("record is processing | batch_id=%v", batchID))
		}
	}
	cdtRecord := &manual_update_rule.CdtManualUpdateRecordTab{
		BatchId:       batchID,
		IsSiteLine:    *request.IsSiteLine,
		ObjectType:    request.ObjectType,
		UploadStatus:  constant.CdtUploadStatusInit,
		ResultFileUrl: request.FileUrl,
		Operator:      operator,
		FileName:      request.FileName,
		Region:        region,
	}

	_, lcosErr = m.manualUpdateDao.CreateCdtRecord(ctx, cdtRecord)
	if lcosErr != nil {
		return "", lcosErr
	}

	// 开启协程处理任务导入
	go m.handleUploadTask(ctx, batchID, region, *request.IsSiteLine)
	return batchID, nil
}

func (m *manualUpdateService) downloadFile(ctx utils.LCOSContext, fileUrl string) (string, *lcos_error.LCOSError) {
	// 下载文件到本地
	filePath, err := serviceable_util.DownloadFileFromS3(ctx, fileUrl)
	if err != nil {
		return "", err
	}
	return filePath, nil
}

// validProductAndLaneCode
// valid product id and lane code and return lane code
func validProductAndLaneCode(ctx utils.LCOSContext, cdtType, productId, laneCode, region string, productLaneCodeMap map[string]*product_service.LaneCodeInfo, lineNum int, updateEvent string, isCb bool) (string, *lcos_error.LCOSError) {
	var laneId string
	switch cdtType {
	case "":
		// field Type cannot be empty
		return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: Type is required", lineNum)
	case edd_constant.ProductCdtTypeText:
		// if Type is Product, product_id cannot be empty and lane_id should be empty
		if productId == "" {
			return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: product_id is required", lineNum)
		}
		laneId = laneCode
		if laneId != "" {
			return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: lane_id should be blank if you choose Type=Product. Please check it again", lineNum)
		}
	case edd_constant.LaneCdtTypeText:
		// if Type is Lane, product_id and lane_id cannot be empty
		if productId == "" {
			return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: product_id is required", lineNum)
		}
		laneId = laneCode
		if laneId == "" {
			return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: lane_id is required", lineNum)
		}
		// validate product_id & lane_id
		// 1. if is single product, lane_id must belong to this product
		// 2. if is multi-product or multi-layered single product, lane_id must be in 'FM|LM' format and FM/LM must belong to this product
		laneCodeInfo, ok := productLaneCodeMap[productId]
		if !ok {
			productIdDigit, err := strconv.Atoi(productId)
			if err != nil {
				return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: product_id not exists", lineNum)
			}
			var lcosErr *lcos_error.LCOSError
			laneCodeInfo, lcosErr = product_service.GetLaneCodeInfoByProductId(ctx, region, productIdDigit)
			if lcosErr != nil {
				return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: product_id/lane_id validation failed: %s", lineNum, lcosErr.Msg)
			}
			productLaneCodeMap[productId] = laneCodeInfo
		}
		// lane code校验
		// 1. single product：lane code必须属于填写的product
		// 2. multi product/multi layered：
		// 2.1 update event如果是CB LM event（Destination Inbound/LM Hub inbound/Out For Delivery），那么lane code必须是"FM|LM"格式，并且FM和LM分别都属于填写的product
		// 2.2 如果不是，那么lane code必须是"LM"格式，并且属于填写的product
		if laneCodeInfo.IsMultiProduct {
			event, err := edd_constant.GetUpdateEventUint8(updateEvent, isCb)
			if err != nil {
				return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: %s", lineNum, err.Error())
			}
			if cdt_common.IsCbLmEvent(event) {
				// 如果update event是Destination Inbound/LM Hub inbound/Out For Delivery，那么lane code必须是填写product的LM
				if !utils.InStringSlice(laneId, laneCodeInfo.LM) {
					return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: lane_id does not match product_id", lineNum)
				}
			} else {
				// 如果是其他update event，那么lane code必须是"FM|LM"格式
				laneCodes := strings.Split(laneId, "|")
				if len(laneCodes) != 2 {
					return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: lane_id does not match product_id", lineNum)
				}
				laneCodes[0] = strings.TrimSpace(laneCodes[0])
				laneCodes[1] = strings.TrimSpace(laneCodes[1])
				if !utils.InStringSlice(laneCodes[0], laneCodeInfo.FM) || !utils.InStringSlice(laneCodes[1], laneCodeInfo.LM) {
					return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: lane_id does not match product_id", lineNum)
				}
				laneId = strings.Join(laneCodes, "|") // 上传时|两端可能有空格，需要去除
			}
		} else {
			if !utils.InStringSlice(laneId, laneCodeInfo.ALL) {
				return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: lane_id does not match product_id", lineNum)
			}
		}
	default:
		// field Type is invalid, should be Product or Lane
		return "", lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "Row %d field: Type should be Product or Lane", lineNum)
	}
	return laneId, nil
}

func (m *manualUpdateService) parseFileForCDT(ctx utils.LCOSContext, filePath, region string, countryCdtMap map[string]bool, channelInfoMap map[string]*channel_service.Channel) ([]*RawCdtUploadStructForCdt, *lcos_error.LCOSError) {

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.CtxLogInfof(ctx, "Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	var resultList []*RawCdtUploadStructForCdt

	// 用来缓存productID到ALL LaneCodes、FM LaneCodes、LM LaneCodes的映射
	productLaneCodeMap := map[string]*product_service.LaneCodeInfo{}
	var titleList []string

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}

		// handle excel headers
		if lineNum == 1 {
			titleList = handleFileTitleSequence(ctx, edd_constant.CdtObject, row)
			continue
		}

		// skip blank row
		if serviceable_util.IsBlankRow(row) {
			continue
		}

		rawModel, err1 := parseFromRowToStructForCDT(row, titleList)
		if err1 != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err1.Error())
		}
		rawModel.Row = lineNum

		var cbFlag bool
		// 检查当前product是否可以在当前国家获取
		if _, ok := channelInfoMap[rawModel.ProductID]; !ok {
			errMsg := fmt.Sprintf("channel:[%s] not found in current region:[%s]", rawModel.ProductID, region)
			logger.CtxLogErrorf(ctx, errMsg)
			continue
		}
		if channelInfoMap[rawModel.ProductID].CBType == 0 {
			cbFlag = false
		} else {
			cbFlag = true
		}

		// check cdt_type and product_id/lane_id
		_, validErr := validProductAndLaneCode(ctx, edd_constant.ProductCdtTypeText, rawModel.ProductID, "", region, productLaneCodeMap, lineNum, "", cbFlag)
		if validErr != nil {
			return nil, validErr
		}

		// check level
		level, err := strconv.Atoi(rawModel.LocationLevel)
		if err != nil {
			errMsg := fmt.Sprintf("row=%v|level value cannot convert to number, value=%v", lineNum, level)
			return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
		}
		if level == -1 && rawModel.CDTEvent == edd_constant.ShippedOutString {
			if !cdt.IsCepRangeCountry(region) && rawModel.ActionCode != "1" {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "row=%d|action code of region level data can only be 1", lineNum)
			}
			countryCdtMap[rawModel.ProductID] = true
		}

		// check day and time
		rawModel.DayOfWeekGroup = strings.ReplaceAll(rawModel.DayOfWeekGroup, " ", "")
		dayTimeErr := validateDayAndHour(ctx, lineNum, rawModel.DayOfWeekGroup, rawModel.HourOfEventTime)
		if dayTimeErr != nil {
			return nil, dayTimeErr
		}

		resultList = append(resultList, rawModel)
	}
	return resultList, nil
}

// parse file for lead
func (m *manualUpdateService) parseFileForLead(ctx utils.LCOSContext, filePath, region string, countryCdtMap map[string]bool, channelInfoMap map[string]*channel_service.Channel) ([]*RawCdtUploadStructForLead, *lcos_error.LCOSError) {

	// 开始解析
	lineNum := 0
	beginTime := recorder.Now(ctx)
	defer func() {
		logger.LogInfof("Parse file and save complete,lineNum:%d,cost:%s\n", lineNum, time.Since(beginTime).String())
	}()

	file, fileErr := excelize.OpenFile(filePath)
	if fileErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, fileErr.Error())
	}
	rows, rowErr := file.Rows("Sheet1")
	if rowErr != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, rowErr.Error())
	}
	defer os.Remove(filePath)

	var resultList []*RawCdtUploadStructForLead

	// 用来缓存productID到ALL LaneCodes、FM LaneCodes、LM LaneCodes的映射
	productLaneCodeMap := map[string]*product_service.LaneCodeInfo{}

	// ProductId -> 是否计算EDD_min
	calculateEddMinMap := map[string]bool{}
	var titleList []string

	for rows.Next() {
		lineNum++
		row, err := rows.Columns()
		if err != nil {
			continue
		}

		// handle excel headers
		if lineNum == 1 {
			titleList = handleFileTitleSequence(ctx, edd_constant.LeadTimeObject, row)
			continue
		}

		// skip blank row
		if serviceable_util.IsBlankRow(row) {
			continue
		}

		rawModel, err1 := parseFromRowToStructForLead(row, titleList)
		if err1 != nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err1.Error())
		}
		rawModel.Row = lineNum

		var cbFlag bool
		// 检查当前product是否可以在当前国家获取
		if _, ok := channelInfoMap[rawModel.ProductID]; !ok {
			errMsg := fmt.Sprintf("channel:[%s] not found in current region:[%s]", rawModel.ProductID, region)
			logger.CtxLogErrorf(ctx, errMsg)
			continue
		}
		if channelInfoMap[rawModel.ProductID].CBType == 0 {
			cbFlag = false
		} else {
			cbFlag = true
		}

		// check cdt_type and product_id/lane_id
		laneId, validErr := validProductAndLaneCode(ctx, rawModel.Type, rawModel.ProductID, rawModel.LaneCode, region, productLaneCodeMap, lineNum, rawModel.UpdateEvent, cbFlag)
		if validErr != nil {
			return nil, validErr
		}

		// check level
		level, err := strconv.Atoi(rawModel.LocationLevel)
		if err != nil {
			errMsg := fmt.Sprintf("row=%v|level value cannot convert to number, value=%v", lineNum, level)
			return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
		}
		if level == -1 && rawModel.UpdateEvent == edd_constant.ShippedOutString {
			if rawModel.Type == edd_constant.ProductCdtTypeText { // 上传Excel或者数据库中必须包含兜底的product维度level=-1的数据并且不支持删除
				if !cdt.IsCepRangeCountry(region) && rawModel.ActionCode != "1" {
					return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("row=%v|cannot delete region level cdt data", lineNum))
				}
				if rawModel.UpdateEvent == edd_constant.ShippedOutString {
					countryCdtMap[rawModel.ProductID] = true
				}
			}
		}

		// check day and time
		rawModel.DayOfWeekGroup = strings.ReplaceAll(rawModel.DayOfWeekGroup, " ", "")
		dayTimeErr := validateDayAndHour(ctx, lineNum, rawModel.DayOfWeekGroup, rawModel.HourOfEventTime)
		if dayTimeErr != nil {
			return nil, dayTimeErr
		}

		rawModel.LaneCode = laneId
		resultList = append(resultList, rawModel)

		calculateEddMin := rawModel.LeadTimeMin != "-1"
		if flag, ok := calculateEddMinMap[rawModel.ProductID]; ok {
			if flag != calculateEddMin {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "For Product %s, some edd_lead_time_min=-1, but the rest are not. If you don't want to use edd_min, please fill in all of edd_lead_time_min as -1; if you want to use edd_min, please fill in all of edd_lead_time_min as valid values", rawModel.ProductID)
			}
		} else {
			calculateEddMinMap[rawModel.ProductID] = calculateEddMin
		}
	}
	return resultList, nil
}

func (m *manualUpdateService) doHandleParseAndImportDataForCDT(ctx utils.LCOSContext, filePath string, region string, isSiteLine uint8, countryCdtMap map[string]bool, channelInfoMap map[string]*channel_service.Channel) ([]*ErrorRowMessage, int, int, *lcos_error.LCOSError) {
	resultList, lcosErr := m.parseFileForCDT(ctx, filePath, region, countryCdtMap, channelInfoMap)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		return nil, len(resultList), 0, lcosErr
	}
	// parse cdt struct to lead struct, add update event as pickup done
	leadResult := make([]*RawCdtUploadStructForLead, 0, len(resultList))
	for _, singleResult := range resultList {
		leadResult = append(leadResult, &RawCdtUploadStructForLead{
			Row:                   singleResult.Row,
			Type:                  edd_constant.ProductCdtTypeText, // CDT只有Product维度
			LocationLevel:         singleResult.LocationLevel,
			OriginCountry:         singleResult.OriginCountry,
			OriginState:           singleResult.OriginState,
			OriginCity:            singleResult.OriginCity,
			OriginDistrict:        singleResult.OriginDistrict,
			DestinationCountry:    singleResult.DestinationCountry,
			DestinationState:      singleResult.DestinationState,
			DestinationCity:       singleResult.DestinationCity,
			DestinationDistrict:   singleResult.DestinationDistrict,
			DestinationPostcode:   singleResult.DestinationPostcode,
			DestinationCEPInitial: singleResult.DestinationCEPInitial,
			DestinationCEPFinal:   singleResult.DestinationCEPFinal,
			ProductID:             singleResult.ProductID,
			LaneCode:              "",
			UpdateEvent:           singleResult.CDTEvent,
			DayOfWeekGroup:        singleResult.DayOfWeekGroup,
			HourOfEventTime:       singleResult.HourOfEventTime,
			LeadTimeMin:           singleResult.LeadTimeMin,
			LeadTimeMax:           singleResult.LeadTimeMax,
			ActionCode:            singleResult.ActionCode,
		})
	}

	// 把相同地址不同时间段的数据做聚合到PolymerCdtUploadStruct，顺便做时间段有效性校验
	exceptionRow, mergeResult := m.mergeUploadData(ctx, leadResult)
	if len(exceptionRow) > 0 {
		logger.CtxLogErrorf(ctx, "%d rows invalid, will not import data", len(exceptionRow))
		return exceptionRow, len(resultList), 0, nil
	}

	// SPLN-30795 add check for product ab test rule
	var productList []string
	productMap := make(map[string]struct{})
	for _, singleResult := range mergeResult {
		if _, ok := productMap[singleResult.ProductID]; !ok {
			productList = append(productList, singleResult.ProductID)
			productMap[singleResult.ProductID] = struct{}{}
		}
	}
	if abTestCheckErr := m.checkForAbTestRule(ctx, productList, edd_constant.CdtObject); abTestCheckErr != nil {
		return nil, len(resultList), 0, abTestCheckErr
	}

	return m.parseRowsAndImportData(ctx, mergeResult, region, countryCdtMap, channelInfoMap, isSiteLine, edd_constant.CdtObject)
}

func (m *manualUpdateService) mergeUploadData(ctx utils.LCOSContext, resultList []*RawCdtUploadStructForLead) ([]*ErrorRowMessage, []*MergeCdtUploadStruct) {
	var exceptionRow []*ErrorRowMessage
	mergeMap := make(map[string]*MergeCdtUploadStruct) // key是地址，value是聚合结果

	mergeList := make([]*MergeCdtUploadStruct, 0)

	// 定义一个将error暴露给用户的func
	handleError := func(msg string, row int) {
		errMsg := fmt.Sprintf("row=%d|error=%s", row, msg)
		logger.CtxLogErrorf(ctx, errMsg)
		tmpErrorRow := &ErrorRowMessage{
			Index:        row,
			ErrorMessage: errMsg,
		}
		exceptionRow = append(exceptionRow, tmpErrorRow)
	}

	for i, singleResult := range resultList {
		row := i + 2 // 打印给用户的行号要去掉表头，并从1开始算
		routeKey := fmt.Sprintf("%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s",
			singleResult.OriginCountry, singleResult.OriginState, singleResult.OriginCity, singleResult.OriginDistrict,
			singleResult.DestinationCountry, singleResult.DestinationState, singleResult.DestinationCity, singleResult.DestinationDistrict,
			singleResult.DestinationPostcode, singleResult.DestinationCEPInitial, singleResult.DestinationCEPFinal,
			singleResult.ProductID, singleResult.LaneCode, singleResult.UpdateEvent)

		mergeResult, ok := mergeMap[routeKey]
		if !ok {
			mergeResult = &MergeCdtUploadStruct{
				Row:                   singleResult.Row,
				Type:                  singleResult.Type,
				LocationLevel:         singleResult.LocationLevel,
				OriginCountry:         singleResult.OriginCountry,
				OriginState:           singleResult.OriginState,
				OriginCity:            singleResult.OriginCity,
				OriginDistrict:        singleResult.OriginDistrict,
				DestinationCountry:    singleResult.DestinationCountry,
				DestinationState:      singleResult.DestinationState,
				DestinationCity:       singleResult.DestinationCity,
				DestinationDistrict:   singleResult.DestinationDistrict,
				DestinationPostcode:   singleResult.DestinationPostcode,
				DestinationCEPInitial: singleResult.DestinationCEPInitial,
				DestinationCEPFinal:   singleResult.DestinationCEPFinal,
				ProductID:             singleResult.ProductID,
				LaneCode:              singleResult.LaneCode,
				UpdateEvent:           singleResult.UpdateEvent,
				ActionCode:            singleResult.ActionCode,
			}

			mergeMap[routeKey] = mergeResult
			mergeList = append(mergeList, mergeResult)
		}

		leadTimeMin, leadTimeMax, err := parseLeadTime(singleResult.LeadTimeMin, singleResult.LeadTimeMax)
		if err != nil {
			handleError(err.Error(), row)
			continue
		}

		// 根据不同的 DayOfWeekGroup 和 HourOfEventTime 值，给extraData不同层级的min/max赋值
		if isEmptyDayGroup(singleResult.DayOfWeekGroup) && isEmptyTimeBucket(singleResult.HourOfEventTime) { // 没有指定day和time，说明是route级别
			mergeResult.LeadTimeMin = singleResult.LeadTimeMin
			mergeResult.LeadTimeMax = singleResult.LeadTimeMax
		} else { // 指定了day或time，说明是 day级别或time级别，做进一步判断处理
			// 尝试在extraData找出DayName相同的DayGroup，如果没有，说明当前day组合是新的，要校验里面的每个day是否在已有的DayGroup中已经出现过，出现过则报错return
			dayGroup, dayErr := m.getAndCheckDayGroup(ctx, row, &mergeResult.CdtExtraData, singleResult.DayOfWeekGroup)
			if dayErr != nil {
				handleError(dayErr.Msg, row)
				continue
			}

			if isEmptyTimeBucket(singleResult.HourOfEventTime) { // 没有指定hour，说明是day级别
				// 如果原本有值，要判断是否冲突
				if isConflictValue(dayGroup.LeadTimeMin, dayGroup.LeadTimeMax, leadTimeMin, leadTimeMax) {
					errMsg := fmt.Sprintf("Conflict value [min:%f, max:%f] with other rows [min:%f, max:%f]", leadTimeMin, leadTimeMax, dayGroup.LeadTimeMin, dayGroup.LeadTimeMax)
					handleError(errMsg, row)
					continue
				}

				dayGroup.LeadTimeMin = leadTimeMin
				dayGroup.LeadTimeMax = leadTimeMax
			} else { // 指定了day 和 hour，说明是time级别
				// 尝试在day中找到time_bucket
				timeBucket, timeErr := m.getAndCheckTimeBucket(ctx, row, dayGroup, singleResult.HourOfEventTime)
				if timeErr != nil {
					handleError(timeErr.Msg, row)
					continue
				}

				// 如果原本有值，要判断是否冲突
				if isConflictValue(timeBucket.LeadTimeMin, timeBucket.LeadTimeMax, leadTimeMin, leadTimeMax) {
					errMsg := fmt.Sprintf("Conflict value [min:%f, max:%f] with other rows [min:%f, max:%f]", leadTimeMin, leadTimeMax, timeBucket.LeadTimeMin, timeBucket.LeadTimeMax)
					handleError(errMsg, row)
					continue
				}

				timeBucket.LeadTimeMin = leadTimeMin
				timeBucket.LeadTimeMax = leadTimeMax
			}
		}
	}

	// 聚合完成后对数据做整体合理性校验
	checkResult := checkMergeData(ctx, mergeList)
	exceptionRow = append(exceptionRow, checkResult...)

	return exceptionRow, mergeList
}

// handleParseAndImportDataForCDT
// handle parse file and import data for cdt
func (m *manualUpdateService) handleParseAndImportDataForCDT(ctx utils.LCOSContext, fileUrl string, region string, isSiteLine uint8, countryCdtMap map[string]bool, channelInfoMap map[string]*channel_service.Channel) ([]*ErrorRowMessage, int, int, *lcos_error.LCOSError) {
	filePath, lcosErr := m.downloadFile(ctx, fileUrl)
	if lcosErr != nil {
		return nil, 0, 0, lcosErr
	}
	return m.doHandleParseAndImportDataForCDT(ctx, filePath, region, isSiteLine, countryCdtMap, channelInfoMap)
}

func (m *manualUpdateService) checkForAbTestRule(ctx utils.LCOSContext, productIDList []string, cdtObject uint8) *lcos_error.LCOSError {
	rules, lcosErr := m.cdtABTestRuleDao.ListCdtAbTestRuleByParams(ctx, map[string]interface{}{"product_id in": productIDList, "rule_status": edd_constant.AbTestRuleStatusActive, "object_type": cdtObject})
	// if exist any active rule, stop import
	if lcosErr == nil && len(rules) > 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot do action because there is an active ab test rule|product_id=[%s], ab_test_rule_id=[%d]", rules[0].ProductId, rules[0].Id)
	}
	return nil
}

// doHandleParseAndImportDataForLead
func (m *manualUpdateService) doHandleParseAndImportDataForLead(ctx utils.LCOSContext, filePath string, region string, isSiteLine uint8, channelInfoMap map[string]*channel_service.Channel) ([]*ErrorRowMessage, int, int, *lcos_error.LCOSError) {
	countryCdtMap := make(map[string]bool) // 每次文件上传必须包含product+region维度数据
	resultList, lcosErr := m.parseFileForLead(ctx, filePath, region, countryCdtMap, channelInfoMap)
	if lcosErr != nil {
		return nil, len(resultList), 0, lcosErr
	}

	// 把相同地址不同时间段的数据做聚合到PolymerCdtUploadStruct，顺便做时间段有效性校验
	exceptionRow, mergeResultList := m.mergeUploadData(ctx, resultList)
	if len(exceptionRow) > 0 {
		logger.CtxLogErrorf(ctx, "%d rows invalid, will not import data", len(exceptionRow))
		return exceptionRow, len(resultList), 0, nil
	}

	// SPLN-30795 add check for product ab test rule
	var productList []string
	productMap := make(map[string]struct{})
	for _, singleResult := range mergeResultList {
		if _, ok := productMap[singleResult.ProductID]; !ok {
			productList = append(productList, singleResult.ProductID)
			productMap[singleResult.ProductID] = struct{}{}
		}
	}
	if abTestCheckErr := m.checkForAbTestRule(ctx, productList, edd_constant.LeadTimeObject); abTestCheckErr != nil {
		return nil, len(resultList), 0, abTestCheckErr
	}

	return m.parseRowsAndImportData(ctx, mergeResultList, region, countryCdtMap, channelInfoMap, isSiteLine, edd_constant.LeadTimeObject)
}

// handleParseAndImportDataForLead
func (m *manualUpdateService) handleParseAndImportDataForLead(ctx utils.LCOSContext, fileUrl string, region string, isSiteLine uint8, channelInfoMap map[string]*channel_service.Channel) ([]*ErrorRowMessage, int, int, *lcos_error.LCOSError) {
	filePath, lcosErr := m.downloadFile(ctx, fileUrl)
	if lcosErr != nil {
		return nil, 0, 0, lcosErr
	}
	return m.doHandleParseAndImportDataForLead(ctx, filePath, region, isSiteLine, channelInfoMap)
}

// SPLN-24104: 支持处理lane维度cdt数据的上传
func (m *manualUpdateService) handleUploadTask(ctx utils.LCOSContext, batchID, region string, isSiteLine uint8) *lcos_error.LCOSError {
	logHead := "handling upload task"
	record, lcosErr := m.manualUpdateDao.GetCdtRecordByBatchID(ctx, region, batchID)
	if lcosErr != nil {
		return lcosErr
	}
	if record.UploadStatus != constant.CdtUploadStatusInit {
		logger.CtxLogErrorf(ctx, "[%v]incorrect batch task status: Init, batch id:%v", logHead, batchID)
		return lcos_error.NewLCOSError(lcos_error.CdtUploadTaskStatusNotAllowedErrorCode, fmt.Sprintf("incorrect cdt task status|batch_id=%v", batchID))
	}
	record.UploadStatus = constant.CdtUploadStatusProcessing
	lcosErr = m.manualUpdateDao.UpdateCdtRecord(ctx, map[string]interface{}{"upload_status": constant.CdtUploadStatusProcessing}, record.Id)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "[%v]upload error: %v, batch_id:%v", logHead, lcosErr, batchID)
		return lcosErr
	}

	countryCdtMap, lcosErr := m.manualUpdateDao.GetCountryCdtLocationData(ctx, region, isSiteLine, record.ObjectType)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "[%v]upload error: %v, batch_id:%v", logHead, lcosErr, batchID)
		record.UploadStatus = constant.CdtUploadStatusFailed
		lcosErr = m.manualUpdateDao.UpdateCdtRecord(ctx, map[string]interface{}{"upload_status": record.UploadStatus, "remark": lcosErr.Msg}, record.Id)
		return lcosErr
	}

	// 获取channel info
	var channelInfoMap map[string]*channel_service.Channel
	if isSiteLine == constant.ENABLED {
		channelInfoMap, lcosErr = product_service.GetAllProducts(ctx, region)
	} else {
		channelInfoMap, lcosErr = channel_service.GetAllChannels(ctx, region)
	}
	if lcosErr != nil {
		record.UploadStatus = constant.CdtUploadStatusFailed
		lcosErr = m.manualUpdateDao.UpdateCdtRecord(ctx, map[string]interface{}{"upload_status": record.UploadStatus, "remark": lcosErr.Msg}, record.Id)
		return lcosErr
	}

	fileUrl := record.ResultFileUrl

	var exceptionData []*ErrorRowMessage
	var all, success int // all为上传的数据量，success为上传成功的数据量
	var lcosParseErr *lcos_error.LCOSError

	if record.ObjectType == edd_constant.CdtObject {
		exceptionData, all, success, lcosParseErr = m.handleParseAndImportDataForCDT(ctx, fileUrl, region, isSiteLine, countryCdtMap, channelInfoMap)
	} else {
		exceptionData, all, success, lcosParseErr = m.handleParseAndImportDataForLead(ctx, fileUrl, region, isSiteLine, channelInfoMap)
	}
	_, _ = localcache.IncrLocalCacheVersion(ctx, constant.CDTManualUpdatePostcodeDataTabNamespace)

	if lcosParseErr != nil || len(exceptionData) > 0 {
		if lcosParseErr != nil {
			logger.CtxLogErrorf(ctx, "upload file error:%s, batch_id:%s", lcosParseErr.Msg, batchID)
		}
		if len(exceptionData) > 0 {
			record.UploadStatus = constant.CdtUploadStatusFailed
			record.Remark = fmt.Sprintf("row=%d|%s", exceptionData[0].Index, exceptionData[0].ErrorMessage)
		} else {
			record.UploadStatus = constant.CdtUploadStatusFailed
			record.Remark = fmt.Sprintf("error=[%s]|Has successfully uploaded %d of %d rows", lcosParseErr.Msg, success, all)
		}
		lcosErr = m.manualUpdateDao.UpdateCdtRecord(ctx, map[string]interface{}{"upload_status": record.UploadStatus, "remark": record.Remark}, record.Id)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "error:[%s]|batch_id:%s", lcosErr.Msg, batchID)
		}
	} else {
		record.UploadStatus = constant.CdtUploadStatusSuccess
		lcosErr = m.manualUpdateDao.UpdateCdtRecord(ctx, map[string]interface{}{"upload_status": record.UploadStatus}, record.Id)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "error:%s|batch_id:%s", lcosErr.Msg, batchID)
		}
	}
	// 发送邮件
	_ = m.postHandleUploadCdt(ctx, record.ObjectType, record.UploadStatus, batchID, record.Operator, record.FileName, record.Remark, exceptionData)
	return lcosParseErr
}

// GetDataForExport 获取导出的数据
// SPLN-24104: 支持导出lane维度的cdt数据
func (m *manualUpdateService) GetDataForExport(ctx utils.LCOSContext, request *manual_update_rule2.ExportCdtDataRequest, region string) ([]*CdtData, *lcos_error.LCOSError) {
	var lcosErr *lcos_error.LCOSError

	// 1. 获取Location类型的CDT/LeadTime数据
	var locationData []*manual_update_rule.CdtManualUpdateLocationDataTab
	locationData, lcosErr = m.manualUpdateDao.GetCdtLocationDataByParams(ctx, map[string]interface{}{"product_id": request.ProductID, "is_site_line": *request.IsSiteLine, "is_lm": 0, "region": region, "object_type": request.ObjectType}, "")
	if lcosErr != nil {
		return nil, lcosErr
	}
	// LeadTime还需要查找Lane维度数据
	var laneLocationData []*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab
	if request.ObjectType == edd_constant.LeadTimeObject {
		laneLocationData, lcosErr = m.laneManualUpdateDao.GetLaneCdtLocationDataByParams(ctx, region, request.ProductID, *request.IsSiteLine)
		if lcosErr != nil {
			return nil, lcosErr
		}
	}

	// 2. 获取基于邮编的CDT/LeadTime数据
	var cepRangeData []*manual_update_rule.CdtManualUpdatePostcodeDataTab
	var postcodeData []*manual_update_rule.CdtManualUpdateZipcodeDataTab
	var laneCepRangeData []*lane_manual_update_rule.LaneCdtManualUpdateCepRangeDataTab
	var lanePostCodeData []*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab
	// BR使用CEP Range，非BR使用Postcode
	if cdt.IsCepRangeCountry(region) {
		cepRangeData, lcosErr = m.manualUpdateDao.GetCdtPostcodeDataByParams(ctx, map[string]interface{}{"product_id": request.ProductID, "is_site_line": *request.IsSiteLine, "is_lm": 0, "region": region, "object_type": request.ObjectType}, "")
		if lcosErr != nil {
			return nil, lcosErr
		}
		if request.ObjectType == edd_constant.LeadTimeObject {
			laneCepRangeData, lcosErr = m.laneManualUpdateDao.GetLaneCdtCepRangeDataByParams(ctx, region, request.ProductID, *request.IsSiteLine)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}
	} else {
		postcodeData, lcosErr = m.manualUpdateDao.GetCdtZipcodeDataByParams(ctx, map[string]interface{}{"product_id": request.ProductID, "is_site_line": *request.IsSiteLine, "is_lm": 0, "region": region, "object_type": request.ObjectType}, "")
		if lcosErr != nil {
			return nil, lcosErr
		}
		if request.ObjectType == edd_constant.LeadTimeObject {
			lanePostCodeData, lcosErr = m.laneManualUpdateDao.GetLaneCdtPostCodeDataByParams(ctx, region, request.ProductID, *request.IsSiteLine)
			if lcosErr != nil {
				return nil, lcosErr
			}
		}
	}

	// 3. 检查请求的是否是CB渠道
	var cbFlag bool
	var channelMap map[string]*channel_service.Channel
	if *request.IsSiteLine == constant.ENABLED {
		channelMap, lcosErr = product_service.GetAllProducts(ctx, region)
		if lcosErr != nil {
			return nil, lcosErr
		}
	} else {
		channelMap, lcosErr = channel_service.GetAllChannels(ctx, region)
		if lcosErr != nil {
			return nil, lcosErr
		}
	}
	if channel, ok := channelMap[request.ProductID]; ok {
		cbFlag = channel.CBType == constant.CBType
	} else {
		return nil, lcos_error.NewLCOSError(lcos_error.ChannelNotFoundErrorCode, fmt.Sprintf("cannot find channel:[%v]", request.ProductID))
	}

	// 4. 合并数据
	var allCdt []*CdtData
	for _, data := range locationData {
		updateEventString, updateEventErr := edd_constant.GetUpdateEventString(data.UpdateEvent)
		if updateEventErr != nil {
			logger.CtxLogErrorf(ctx, updateEventErr.Error())
			continue
		}

		tmpCdt := &CdtData{
			CdtType:            edd_constant.ProductCdtTypeText,
			Level:              strconv.Itoa(data.LocationLevel),
			OriginCountry:      region,
			DestinationCountry: region,
			ProductId:          data.ProductId,
			UpdateEvent:        updateEventString,
			CDTEvent:           updateEventString,
			CdtExtraData:       data.CdtExtraData,
		}
		if request.ObjectType == edd_constant.CdtObject {
			tmpCdt.CdtMin = strconv.FormatFloat(data.LeadTimeMin, 'f', -1, 64)
			tmpCdt.CdtMax = strconv.FormatFloat(data.LeadTimeMax, 'f', -1, 64)
		} else {
			tmpCdt.LeadTimeMin = strconv.FormatFloat(data.LeadTimeMin, 'f', -1, 64)
			tmpCdt.LeadTimeMax = strconv.FormatFloat(data.LeadTimeMax, 'f', -1, 64)
		}
		// 查询并填充起止地址信息
		if int8(data.LocationLevel) != constant.Country {
			if lcosErr := m.ConvertLocationIdToLocation2(ctx, data.OriginLocationId, data.DestinationLocationId, data.LocationLevel, tmpCdt, cbFlag, edd_constant.ShippedOut); lcosErr != nil {
				logger.CtxLogErrorf(ctx, "transfer location info error, origin_location_id:[%d], destination_location_id:[%d]|error=%v", data.OriginLocationId, data.DestinationLocationId, lcosErr.Msg)
				continue
			}
		}
		allCdt = append(allCdt, tmpCdt)
	}

	for _, data := range laneLocationData {
		updateEventString, updateEventErr := edd_constant.GetUpdateEventString(data.UpdateEvent)
		if updateEventErr != nil {
			logger.CtxLogErrorf(ctx, updateEventErr.Error())
			continue
		}
		tmpCdt := &CdtData{
			CdtType:            edd_constant.LaneCdtTypeText,
			Level:              strconv.Itoa(data.LocationLevel),
			OriginCountry:      region,
			DestinationCountry: region,
			ProductId:          data.ProductId,
			LaneCode:           data.LaneCode,
			UpdateEvent:        updateEventString,
			CDTEvent:           updateEventString,
			LeadTimeMin:        strconv.FormatFloat(data.LeadTimeMin, 'f', -1, 64),
			LeadTimeMax:        strconv.FormatFloat(data.LeadTimeMax, 'f', -1, 64),
			CdtExtraData:       data.CdtExtraData,
		}
		// 非Pickup Done的Product维度数据存储在hbase
		if len(data.LaneCode) == 0 {
			tmpCdt.CdtType = edd_constant.ProductCdtTypeText
		}
		// 查询并填充起止地址信息
		if int8(data.LocationLevel) != constant.Country {
			if lcosErr := m.ConvertLocationIdToLocation2(ctx, data.OriginLocationId, data.DestinationLocationId, data.LocationLevel, tmpCdt, cbFlag, data.UpdateEvent); lcosErr != nil {
				logger.CtxLogErrorf(ctx, "transfer location info error, origin_location_id:[%d], destination_location_id:[%d]|error=%v", data.OriginLocationId, data.DestinationLocationId, lcosErr.Msg)
				continue
			}
		}
		allCdt = append(allCdt, tmpCdt)
	}

	for _, data := range cepRangeData {
		updateEventString, updateEventErr := edd_constant.GetUpdateEventString(data.UpdateEvent)
		if updateEventErr != nil {
			logger.CtxLogErrorf(ctx, updateEventErr.Error())
			continue
		}

		tmpCdt := &CdtData{
			CdtType:               edd_constant.ProductCdtTypeText,
			Level:                 strconv.Itoa(data.LocationLevel),
			OriginCountry:         region,
			DestinationCountry:    region,
			DestinationCEPInitial: strconv.Itoa(data.DestinationPostcodeInitial),
			DestinationCEPFinal:   strconv.Itoa(data.DestinationPostcodeFinal),
			ProductId:             data.ProductId,
			UpdateEvent:           updateEventString,
			CDTEvent:              updateEventString,
			CdtExtraData:          data.CdtExtraData,
		}
		if request.ObjectType == edd_constant.CdtObject {
			tmpCdt.CdtMin = strconv.FormatFloat(data.LeadTimeMin, 'f', -1, 64)
			tmpCdt.CdtMax = strconv.FormatFloat(data.LeadTimeMax, 'f', -1, 64)
		} else {
			tmpCdt.LeadTimeMin = strconv.FormatFloat(data.LeadTimeMin, 'f', -1, 64)
			tmpCdt.LeadTimeMax = strconv.FormatFloat(data.LeadTimeMax, 'f', -1, 64)
		}
		// 查询并填充起止地址信息
		if lcosErr := m.FillCdtLocationInfo(ctx, data.OriginLocationId, 0, tmpCdt); lcosErr != nil {
			logger.CtxLogErrorf(ctx, "transfer origin location id:[%d] err:%v", data.OriginLocationId, lcosErr.Msg)
			continue
		}
		allCdt = append(allCdt, tmpCdt)
	}

	for _, data := range laneCepRangeData {
		updateEventString, updateEventErr := edd_constant.GetUpdateEventString(data.UpdateEvent)
		if updateEventErr != nil {
			logger.CtxLogErrorf(ctx, updateEventErr.Error())
			continue
		}
		tmpCdt := &CdtData{
			CdtType:               edd_constant.LaneCdtTypeText,
			Level:                 strconv.Itoa(data.LocationLevel),
			OriginCountry:         region,
			DestinationCountry:    region,
			DestinationCEPInitial: strconv.Itoa(data.DestinationPostcodeInitial),
			DestinationCEPFinal:   strconv.Itoa(data.DestinationPostcodeFinal),
			ProductId:             data.ProductId,
			LaneCode:              data.LaneCode,
			UpdateEvent:           updateEventString,
			CDTEvent:              updateEventString,
			LeadTimeMin:           strconv.FormatFloat(data.LeadTimeMin, 'f', -1, 64),
			LeadTimeMax:           strconv.FormatFloat(data.LeadTimeMax, 'f', -1, 64),
			CdtExtraData:          data.CdtExtraData,
		}
		// 非Pickup Done的Product维度数据存储在hbase
		if len(data.LaneCode) == 0 {
			tmpCdt.CdtType = edd_constant.ProductCdtTypeText
		}
		// 查询并填充起止地址信息
		if lcosErr := m.FillCdtLocationInfo(ctx, data.OriginLocationId, 0, tmpCdt); lcosErr != nil {
			logger.CtxLogErrorf(ctx, "transfer origin location id:[%d] err:%v", data.OriginLocationId, lcosErr.Msg)
			continue
		}
		allCdt = append(allCdt, tmpCdt)
	}

	for _, data := range postcodeData {
		updateEventString, updateEventErr := edd_constant.GetUpdateEventString(data.UpdateEvent)
		if updateEventErr != nil {
			logger.CtxLogErrorf(ctx, updateEventErr.Error())
			continue
		}

		tmpCdt := &CdtData{
			CdtType:             edd_constant.ProductCdtTypeText,
			Level:               strconv.Itoa(data.LocationLevel),
			OriginCountry:       region,
			DestinationCountry:  region,
			DestinationPostcode: data.DestinationPostcode,
			ProductId:           data.ProductId,
			UpdateEvent:         updateEventString,
			CDTEvent:            updateEventString,
			CdtExtraData:        data.CdtExtraData,
		}
		if request.ObjectType == edd_constant.CdtObject {
			tmpCdt.CdtMin = strconv.FormatFloat(data.LeadTimeMin, 'f', -1, 64)
			tmpCdt.CdtMax = strconv.FormatFloat(data.LeadTimeMax, 'f', -1, 64)
		} else {
			tmpCdt.LeadTimeMin = strconv.FormatFloat(data.LeadTimeMin, 'f', -1, 64)
			tmpCdt.LeadTimeMax = strconv.FormatFloat(data.LeadTimeMax, 'f', -1, 64)
		}
		// 查询并填充起止地址信息
		if lcosErr := m.FillCdtLocationInfo(ctx, data.OriginLocationId, 0, tmpCdt); lcosErr != nil {
			logger.CtxLogErrorf(ctx, "transfer origin location id:[%d] err:%v", data.OriginLocationId, lcosErr.Msg)
			continue
		}
		allCdt = append(allCdt, tmpCdt)
	}

	for _, data := range lanePostCodeData {
		updateEventString, updateEventErr := edd_constant.GetUpdateEventString(data.UpdateEvent)
		if updateEventErr != nil {
			logger.CtxLogErrorf(ctx, updateEventErr.Error())
			continue
		}
		tmpCdt := &CdtData{
			CdtType:             edd_constant.LaneCdtTypeText,
			Level:               strconv.Itoa(data.LocationLevel),
			OriginCountry:       region,
			DestinationCountry:  region,
			DestinationPostcode: data.DestinationPostcode,
			ProductId:           data.ProductId,
			LaneCode:            data.LaneCode,
			UpdateEvent:         updateEventString,
			CDTEvent:            updateEventString,
			LeadTimeMin:         strconv.FormatFloat(data.LeadTimeMin, 'f', -1, 64),
			LeadTimeMax:         strconv.FormatFloat(data.LeadTimeMax, 'f', -1, 64),
			CdtExtraData:        data.CdtExtraData,
		}
		// 非Pickup Done的Product维度数据存储在hbase
		if len(data.LaneCode) == 0 {
			tmpCdt.CdtType = edd_constant.ProductCdtTypeText
		}
		// 查询并填充起止地址信息
		if lcosErr := m.FillCdtLocationInfo(ctx, data.OriginLocationId, 0, tmpCdt); lcosErr != nil {
			logger.CtxLogErrorf(ctx, "transfer origin location id:[%d] err:%v", data.OriginLocationId, lcosErr.Msg)
			continue
		}
		allCdt = append(allCdt, tmpCdt)
	}

	return allCdt, nil
}

func (m *manualUpdateService) CheckManualCdtExistsByProduct(ctx utils.LCOSContext, request *manual_update_rule2.MoudleCheckOfCdtByProductRequest) (*MoudleCdtCheckRsponse, *lcos_error.LCOSError) {
	resp := &MoudleCdtCheckRsponse{
		Result: true,
	}
	region := request.Region
	productId := request.ProductID
	conditions := map[string]interface{}{
		"product_id": productId,
	}

	// check product cdt location data
	count, err := m.manualUpdateDao.CountCdtLocationDatabyParams(ctx, conditions)
	if err == nil && count != 0 {
		return resp, nil
	}
	// check lane cdt location data
	locationData, err := m.laneManualUpdateDao.GetLaneCdtLocationDataByProductId(ctx, region, productId)
	if err == nil && len(locationData) != 0 {
		return resp, nil
	}
	if cdt.IsCepRangeCountry(region) {
		// check product cdt ceprange data
		count, err := m.manualUpdateDao.CountCdtPostCodeDatabyParams(ctx, conditions)
		if err == nil && count != 0 {
			return resp, nil
		}
		// check lane cdt ceprange data
		cepRangeData, err := m.laneManualUpdateDao.GetLaneCdtCepRangeDataByProductId(ctx, region, productId)
		if err == nil && len(cepRangeData) != 0 {
			return resp, nil
		}
	}
	resp.Result = false
	return resp, err
}

func (m *manualUpdateService) DeleteManualCdtByProduct(ctx utils.LCOSContext, request *manual_update_rule2.MoudleCheckOfCdtByProductRequest) (*MoudleCdtCheckRsponse, *lcos_error.LCOSError) {
	region := request.Region
	productId := request.ProductID
	operator := ctx.GetUserEmail()
	if region == "" || productId == "" {
		return nil, lcos_error.NewLCOSError(-1, fmt.Sprintf("product or region is empty,productId:[%v],region:[%v]", productId, region))
	}
	rsp := &MoudleCdtCheckRsponse{
		Result: true,
	}

	if cdt.IsCepRangeCountry(region) {
		if err := m.DeleteCdtPostCodeByProduct(ctx, region, productId); err != nil {
			rsp.Result = false
			rsp.ErrMessage = fmt.Sprintf("delete postCode cdt fail:[%v],[%v]", productId, region)
			logger.LogInfof("delete postcode-cdt fail for productId:%s, operator:%s", productId, operator)
		} else {
			logger.LogInfof("delete postcode-cdt success for productId:%s, operator:%s", productId, operator)
		}
	}
	if err := m.DeleteCdtLocationByProduct(ctx, region, productId); err != nil {
		rsp.Result = false
		rsp.ErrMessage = fmt.Sprintf("delete location cdt fail:[%v],[%v]", productId, region)
		logger.LogInfof("delete location-cdt fail for productId:%s, operator:%s", productId, operator)
	} else {
		logger.LogInfof("delete location-cdt success for productId:%s, operator:%s", productId, operator)
	}

	return rsp, nil

}

func (m *manualUpdateService) DeleteCdtLocationByProduct(ctx utils.LCOSContext, region, productId string) *lcos_error.LCOSError {
	conditions := map[string]interface{}{
		"product_id": productId,
	}

	count, err := m.manualUpdateDao.CountCdtLocationDatabyParams(ctx, conditions)
	if err != nil {
		return nil
	}
	//批量查询，单批次的行数
	var size int64 = 500
	pages := count/size + 1
	idsM := make(map[int][]int)
	for page := 1; page <= int(pages); page++ {
		locationCdts, err := m.manualUpdateDao.GetCdtLocationDataByPage(ctx, conditions, uint32(page), uint32(size))
		if err != nil {
			return err
		}
		var ids []int
		for _, v := range locationCdts {
			ids = append(ids, v.Id)
		}
		idsM[page] = ids
	}

	for _, ids := range idsM {
		if err := m.manualUpdateDao.BatchDeleteLocationData(ctx, ids); err != nil {
			return err
		}
	}

	// SPLN-24104: 删除lane维度location数据
	if _, err = m.laneManualUpdateDao.DeleteLaneCdtLocationDataByProductId(ctx, region, productId); err != nil {
		return err
	}

	return nil
}

func (m *manualUpdateService) DeleteCdtPostCodeByProduct(ctx utils.LCOSContext, region, productId string) *lcos_error.LCOSError {
	conditions := map[string]interface{}{
		"product_id": productId,
	}

	count, err := m.manualUpdateDao.CountCdtPostCodeDatabyParams(ctx, conditions)
	if err != nil {
		return nil
	}
	//批量查询，单批次的行数
	var size int64 = 500
	pages := count/size + 1
	idsM := make(map[int][]int)
	for page := 1; page <= int(pages); page++ {
		locationCdts, err := m.manualUpdateDao.GetCdtPostCodeDataByPage(ctx, conditions, uint32(page), uint32(size))
		if err != nil {
			return err
		}
		var ids []int
		for _, v := range locationCdts {
			ids = append(ids, v.Id)
		}
		idsM[page] = ids
	}

	for _, ids := range idsM {
		if err := m.manualUpdateDao.BatchDeletePostCodeData(ctx, ids); err != nil {
			return err
		}
	}

	// SPLN-24104: 删除lane维度ceprange数据
	if _, err = m.laneManualUpdateDao.DeleteLaneCdtCepRangeDataByProductId(ctx, region, productId); err != nil {
		return err
	}

	return nil
}

func (m *manualUpdateService) ListLaneManualUpdateLocationData(ctx utils.LCOSContext, region, productId string, isSiteLine uint8) ([]*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab, *lcos_error.LCOSError) {
	return m.laneManualUpdateDao.GetLaneCdtLocationDataByParams(ctx, region, productId, isSiteLine)
}

func (m *manualUpdateService) ListLaneManualUpdatePostCodeData(ctx utils.LCOSContext, region, productId string, isSiteLine uint8) ([]*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab, *lcos_error.LCOSError) {
	return m.laneManualUpdateDao.GetLaneCdtPostCodeDataByParams(ctx, region, productId, isSiteLine)
}

func (m *manualUpdateService) ListLaneManualUpdateCepRangeData(ctx utils.LCOSContext, region, productId string, isSiteLine uint8) ([]*lane_manual_update_rule.LaneCdtManualUpdateCepRangeDataTab, *lcos_error.LCOSError) {
	return m.laneManualUpdateDao.GetLaneCdtCepRangeDataByParams(ctx, region, productId, isSiteLine)
}

func (m *manualUpdateService) SyncCdtManualUpdateDataToItemCodis(ctx utils.LCOSContext, region, productId string) *lcos_error.LCOSError {
	startTime := utils.GetTimestamp(ctx)

	extraSyncCodisList := config.GetExtraCdtSyncCodisList(ctx)
	locationCount, err := m.manualUpdateDao.SyncManualUpdateLocationDataToItemCodis(ctx, region, productId, extraSyncCodisList)
	if err != nil {
		logger.CtxLogErrorf(ctx, err.Msg)
		syncCdtManualUpdateDataErrorNotify(ctx, region, startTime, err)
		return err
	}
	_ = metrics.GaugeSet(constant.MetricsSyncItemCdtDataReport, float64(locationCount), map[string]string{
		"product_id":    "",
		"cdt_type":      item_card_edt_constant.MetricLabelCdtTypeManualUpdate,
		"location_type": item_card_edt_constant.MetricLabelLocationTypeLocation,
		"rule_id":       "",
	})

	postcodeCount, err := m.manualUpdateDao.SyncManualUpdatePostcodeDataToItemCodis(ctx, region, productId, extraSyncCodisList)
	if err != nil {
		logger.CtxLogErrorf(ctx, err.Msg)
		syncCdtManualUpdateDataErrorNotify(ctx, region, startTime, err)
		return err
	}
	_ = metrics.GaugeSet(constant.MetricsSyncItemCdtDataReport, float64(postcodeCount), map[string]string{
		"product_id":    "",
		"cdt_type":      item_card_edt_constant.MetricLabelCdtTypeManualUpdate,
		"location_type": item_card_edt_constant.MetricLabelLocationTypePostcode,
		"rule_id":       "",
	})

	message := fmt.Sprintf(
		item_card_edt_constant.SyncCdtManualUpdateDataSuccessMessage,
		utils.GetEnv(ctx),
		region,
		utils.FormatTimestamp(startTime, constant.DateAndTimeFormat),
		utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
		locationCount,
		postcodeCount,
		strings.Join(extraSyncCodisList, ","),
	)
	cfg := config.GetMutableConf(ctx).ItemCardSceneConfig.SeatalkNotifyConfig
	_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, false)
	return nil
}

func syncCdtManualUpdateDataErrorNotify(ctx utils.LCOSContext, region string, startTime uint32, err *lcos_error.LCOSError) {
	if err == nil {
		return
	}
	message := fmt.Sprintf(
		item_card_edt_constant.SyncCdtManualUpdateDataFailedMessdage,
		utils.GetEnv(ctx),
		region,
		utils.FormatTimestamp(startTime, constant.DateAndTimeFormat),
		utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
		err.Msg,
	)
	cfg := config.GetMutableConf(ctx).ItemCardSceneConfig.SeatalkNotifyConfig
	_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, true)
}

func isConflictValue(minOld float64, maxOld float64, minNew float64, maxNew float64) bool {
	// old都为0说明以前没有值，不会冲突
	if minOld == 0 && maxOld == 0 {
		return false
	}

	// 任何一个不一致就是冲突
	if minOld != minNew || maxOld != maxNew {
		return true
	}

	return false
}

func validateDayAndHour(ctx utils.LCOSContext, lineNum int, dayGroupStr string, hourStr string) *lcos_error.LCOSError {
	// 检查dayGroupStr是否类似 "1,2,3,6"，并且符合数字范围
	var days []string
	if dayGroupStr != "" {
		days = strings.Split(dayGroupStr, ",")
	}

	// 验证每个分割后的部分是否符合范围要求
	for _, day := range days {
		num, err := strconv.Atoi(day)
		if err != nil || num < 1 || num > 7 {
			errMsg := fmt.Sprintf("row=%d|day_of_week_group should be like 1,2,4,6 and can not over 7", lineNum)
			logger.CtxLogErrorf(ctx, errMsg)
			return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, errMsg)
		}
	}

	// hour必须符合固定的字符串 例如0:00 - 4:00 | "Do not consider hour of event time" 视为空
	if !utils.InStringSlice(hourStr, ValidTimeBucketStr) {
		errMsg := fmt.Sprintf("row=%d|hour_of_event_time invalid format", lineNum)
		logger.CtxLogErrorf(ctx, errMsg)
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, errMsg)
	}

	return nil
}

func checkMergeData(ctx utils.LCOSContext, mergeList []*MergeCdtUploadStruct) []*ErrorRowMessage {
	var exceptionRow []*ErrorRowMessage
	// 对于每行数据，route维度的min/max值不能为空
	for row, mergeData := range mergeList {
		if (mergeData.LeadTimeMin == "" || mergeData.LeadTimeMin == "0") && (mergeData.LeadTimeMax == "" || mergeData.LeadTimeMax == "0") {
			routeKey := fmt.Sprintf("%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s",
				mergeData.OriginCountry, mergeData.OriginState, mergeData.OriginCity, mergeData.OriginDistrict,
				mergeData.DestinationCountry, mergeData.DestinationState, mergeData.DestinationCity, mergeData.DestinationDistrict,
				mergeData.DestinationPostcode, mergeData.DestinationCEPInitial, mergeData.DestinationCEPFinal,
				mergeData.ProductID, mergeData.LaneCode, mergeData.UpdateEvent)
			errMsg := fmt.Sprintf("Route+event level min/max can not be both 0, route=[%s], ", routeKey)
			logger.CtxLogErrorf(ctx, errMsg)
			tmpErrorRow := &ErrorRowMessage{
				Index:        row,
				ErrorMessage: errMsg,
			}
			exceptionRow = append(exceptionRow, tmpErrorRow)
		}
	}

	return exceptionRow
}
