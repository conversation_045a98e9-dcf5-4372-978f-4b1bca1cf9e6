package manual_update_rule

import (
	"bytes"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/emailhelper"
	jsoniter "github.com/json-iterator/go"
	"html/template"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	cdt_common "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/channel_service"
)

var EmailLinkMap = map[string]string{
	"LIVE":    "https://ops.ssc.shopeemobile.com/product-cdt/setting/manual",         // nolint
	"TEST":    "https://ops.ssc.test.shopeemobile.com/product-cdt/setting/manual",    // nolint
	"UAT":     "https://ops.ssc.uat.shopeemobile.com/product-cdt/setting/manual",     // nolint
	"STAGING": "https://ops.ssc.staging.shopeemobile.com/product-cdt/setting/manual", // nolint
}

func (m *manualUpdateService) importDataIntoDBAndHBase(ctx utils.LCOSContext,
	// added mysql data
	addedLocationRows []*manual_update_rule.CdtManualUpdateLocationDataTab,
	addedPostcodeRows []*manual_update_rule.CdtManualUpdateZipcodeDataTab,
	addedCepRangeRows []*manual_update_rule.CdtManualUpdatePostcodeDataTab,

	// added hbase data
	addedLaneLocationRows []*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab,
	addedLanePostcodeRows []*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab,
	addedLaneCepRangeRows []*lane_manual_update_rule.LaneCdtManualUpdateCepRangeDataTab,

	// deleted data
	deletedLocationRows []*manual_update_rule.CdtManualUpdateLocationDataTab,
	deletedPostcodeRows []*manual_update_rule.CdtManualUpdateZipcodeDataTab,
	deletedLaneLocationRows []*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab,
	deletedLanePostcodeRows []*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab,

	deletedProductList []string,
	region string,
	isSiteLine uint8,
	totalLength int,
	objectType uint8,
) (int, *lcos_error.LCOSError) {
	batchSize := 2000

	// BR不支持action code，需要清理已有数据
	if len(deletedProductList) > 0 {
		// 手动上传数据cdt仅存储于mysql，leadtime部分存储于mysql，部分存储于hbase

		// 1. 清理hbase中的数据。清理对应渠道的所有leadtime数据
		for _, pid := range deletedProductList {
			if objectType != edd_constant.LeadTimeObject {
				// cdt类型跳过hbase数据清理
				continue
			}

			// delete lane data first
			if _, err := m.laneManualUpdateDao.DeleteLaneCdtLocationDataByParams(ctx, region, pid, isSiteLine, objectType); err != nil {
				return 0, err
			}
			if _, err := m.laneManualUpdateDao.DeleteLaneCdtCepRangeDataByParams(ctx, region, pid, isSiteLine, objectType); err != nil {
				return 0, err
			}
		}
		// 2. 清理mysql中的数据。cdt清理对应渠道所有object_type=0的数据，leadtime清理对应渠道所有object_type=1的数据
		fc := func() *lcos_error.LCOSError {
			lcosErr := m.manualUpdateDao.BatchDeleteOldCdtPostcodeData(ctx, region, deletedProductList, isSiteLine, 0, objectType)
			if lcosErr != nil {
				return lcosErr
			}
			lcosErr = m.manualUpdateDao.BatchDeleteOldCdtLocationData(ctx, region, deletedProductList, isSiteLine, 0, objectType)
			if lcosErr != nil {
				return lcosErr
			}
			return nil
		}
		if lcosErr := ctx.Transaction(fc); lcosErr != nil {
			return 0, lcosErr
		}
	}

	// split batch data first
	var addedLocationRowsChunks [][]*manual_update_rule.CdtManualUpdateLocationDataTab
	var addedPostcodeRowsChunks [][]*manual_update_rule.CdtManualUpdateZipcodeDataTab
	var addedCepRangeRowsChunks [][]*manual_update_rule.CdtManualUpdatePostcodeDataTab

	var addedLaneLocationRowsChunks [][]*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab
	var addedLanePostcodeRowsChunks [][]*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab
	var addedLaneCepRangeRowsChunks [][]*lane_manual_update_rule.LaneCdtManualUpdateCepRangeDataTab

	var deletedLocationRowsChunks [][]*manual_update_rule.CdtManualUpdateLocationDataTab
	var deletedPostcodeRowsChunks [][]*manual_update_rule.CdtManualUpdateZipcodeDataTab
	var deletedLaneLocationRowsChunks [][]*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab
	var deletedLanePostcodeRowsChunks [][]*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab

	for i := 0; i < totalLength; i += batchSize {
		end := i + batchSize
		if end > totalLength {
			end = totalLength
		}
		// mysql
		if i < len(addedLocationRows) {
			lastIndex := end
			if end > len(addedLocationRows) {
				lastIndex = len(addedLocationRows)
			}
			addedLocationRowsChunks = append(addedLocationRowsChunks, addedLocationRows[i:lastIndex])
		}
		if i < len(addedPostcodeRows) {
			lastIndex := end
			if end > len(addedPostcodeRows) {
				lastIndex = len(addedPostcodeRows)
			}
			addedPostcodeRowsChunks = append(addedPostcodeRowsChunks, addedPostcodeRows[i:lastIndex])
		}
		if i < len(addedCepRangeRows) {
			lastIndex := end
			if end > len(addedCepRangeRows) {
				lastIndex = len(addedCepRangeRows)
			}
			addedCepRangeRowsChunks = append(addedCepRangeRowsChunks, addedCepRangeRows[i:lastIndex])
		}
		// hbase
		if i < len(addedLaneLocationRows) {
			lastIndex := end
			if end > len(addedLaneLocationRows) {
				lastIndex = len(addedLaneLocationRows)
			}
			addedLaneLocationRowsChunks = append(addedLaneLocationRowsChunks, addedLaneLocationRows[i:lastIndex])
		}
		if i < len(addedLanePostcodeRows) {
			lastIndex := end
			if end > len(addedLanePostcodeRows) {
				lastIndex = len(addedLanePostcodeRows)
			}
			addedLanePostcodeRowsChunks = append(addedLanePostcodeRowsChunks, addedLanePostcodeRows[i:lastIndex])
		}
		if i < len(addedLaneCepRangeRows) {
			lastIndex := end
			if end > len(addedLaneCepRangeRows) {
				lastIndex = len(addedLaneCepRangeRows)
			}
			addedLaneCepRangeRowsChunks = append(addedLaneCepRangeRowsChunks, addedLaneCepRangeRows[i:lastIndex])
		}
		// delete data
		if i < len(deletedLocationRows) {
			lastIndex := end
			if end > len(deletedLocationRows) {
				lastIndex = len(deletedLocationRows)
			}
			deletedLocationRowsChunks = append(deletedLocationRowsChunks, deletedLocationRows[i:lastIndex])
		}
		if i < len(deletedPostcodeRows) {
			lastIndex := end
			if end > len(deletedPostcodeRows) {
				lastIndex = len(deletedPostcodeRows)
			}
			deletedPostcodeRowsChunks = append(deletedPostcodeRowsChunks, deletedPostcodeRows[i:lastIndex])
		}
		if i < len(deletedLaneLocationRows) {
			lastIndex := end
			if end > len(deletedLaneLocationRows) {
				lastIndex = len(deletedLaneLocationRows)
			}
			deletedLaneLocationRowsChunks = append(deletedLaneLocationRowsChunks, deletedLaneLocationRows[i:lastIndex])
		}
		if i < len(deletedLanePostcodeRows) {
			lastIndex := end
			if end > len(deletedLanePostcodeRows) {
				lastIndex = len(deletedLanePostcodeRows)
			}
			deletedLanePostcodeRowsChunks = append(deletedLanePostcodeRowsChunks, deletedLanePostcodeRows[i:lastIndex])
		}
	}

	success := 0
	// store into db
	fc := func() *lcos_error.LCOSError {

		// 1. delete product data first
		if len(deletedLocationRowsChunks) > 0 {
			for _, tmpRow := range deletedLocationRowsChunks {
				lcosErr := m.manualUpdateDao.BulkDeleteCdtLocationData(ctx, tmpRow)
				if lcosErr != nil {
					return lcosErr
				}
				success += len(tmpRow)
			}
		}

		if len(deletedPostcodeRowsChunks) > 0 {
			for _, tmpRow := range deletedPostcodeRowsChunks {
				lcosErr := m.manualUpdateDao.BulkDeleteCdtZipcodeData(ctx, tmpRow)
				if lcosErr != nil {
					return lcosErr
				}
				success += len(tmpRow)
			}
		}

		// 2. delete lane cdt data
		if len(deletedLaneLocationRowsChunks) > 0 {
			for _, data := range deletedLaneLocationRowsChunks {
				num, err := m.laneManualUpdateDao.BatchDeleteLaneCdtLocationData(ctx, region, data)
				if err != nil {
					return err
				}
				success += num
			}
		}
		if len(deletedLanePostcodeRowsChunks) > 0 {
			for _, data := range deletedLanePostcodeRowsChunks {
				num, err := m.laneManualUpdateDao.BatchDeleteLaneCdtPostCodeData(ctx, region, data)
				if err != nil {
					return err
				}
				success += num
			}
		}

		// 3. add product data
		if len(addedLocationRowsChunks) > 0 {
			for _, tmpRow := range addedLocationRowsChunks {
				lcosErr := m.manualUpdateDao.BulkCreateOrUpdateCdtLocationDataV2(ctx, tmpRow)
				if lcosErr != nil {
					return lcosErr
				}
				success += len(tmpRow)
			}
		}
		if len(addedPostcodeRowsChunks) > 0 {
			for _, tmpRows := range addedPostcodeRowsChunks {
				lcosErr := m.manualUpdateDao.BulkCreateOrUpdateCdtZipCodeDataV2(ctx, tmpRows)
				if lcosErr != nil {
					return lcosErr
				}
				success += len(tmpRows)
			}
		}
		if len(addedCepRangeRowsChunks) > 0 {
			for _, tmpRows := range addedCepRangeRowsChunks {
				lcosErr := m.manualUpdateDao.BulkCreateOrUpdateCdtPostcodeDataV2(ctx, tmpRows)
				if lcosErr != nil {
					return lcosErr
				}
				success += len(tmpRows)
			}
		}

		// 4. create lane cdt data
		if len(addedLaneLocationRowsChunks) > 0 {
			for _, data := range addedLaneLocationRowsChunks {
				num, err := m.laneManualUpdateDao.BatchCreateLaneCdtLocationData(ctx, region, data)
				if err != nil {
					return err // mysql数据修改会被回滚，hbase的不会
				}
				success += num
			}
		}
		if len(addedLanePostcodeRowsChunks) > 0 {
			for _, data := range addedLanePostcodeRowsChunks {
				num, err := m.laneManualUpdateDao.BatchCreateLaneCdtPostCodeData(ctx, region, data)
				if err != nil {
					return err // mysql数据修改会被回滚，hbase的不会
				}
				success += num
			}
		}
		if len(addedLaneCepRangeRowsChunks) > 0 {
			for _, data := range addedLaneCepRangeRowsChunks {
				num, err := m.laneManualUpdateDao.BatchCreateLaneCdtCepRangeData(ctx, region, data)
				if err != nil {
					return err // mysql数据修改会被回滚，hbase的不会
				}
				success += num
			}
		}
		return nil
	}

	return success, ctx.Transaction(fc)

}

// parseRowsAndImportData
func (m *manualUpdateService) parseRowsAndImportData(ctx utils.LCOSContext, rows []*MergeCdtUploadStruct, region string, channelLevelMap map[string]bool, channelInfoMap map[string]*channel_service.Channel, isSiteLine uint8, objectType uint8) ([]*ErrorRowMessage, int, int, *lcos_error.LCOSError) {
	var exceptionRow []*ErrorRowMessage

	// added product data
	var addedLocationRows []*manual_update_rule.CdtManualUpdateLocationDataTab
	var addedPostcodeRows []*manual_update_rule.CdtManualUpdateZipcodeDataTab
	var addedCepRangeRows []*manual_update_rule.CdtManualUpdatePostcodeDataTab

	// added lane data
	var addedLaneLocationRows []*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab
	var addedLanePostcodeRows []*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab
	var addedLaneCepRangeRows []*lane_manual_update_rule.LaneCdtManualUpdateCepRangeDataTab

	// deleted data
	var deletedLocationRows []*manual_update_rule.CdtManualUpdateLocationDataTab
	var deletedPostcodeRows []*manual_update_rule.CdtManualUpdateZipcodeDataTab
	var deletedLaneLocationRows []*lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab
	var deletedLanePostcodeRows []*lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab

	// deleted cep range product list
	var productList []string
	productMap := make(map[string]bool)

	for _, row := range rows {
		locationData, postcodeData, cepRangeData, laneLocationData, lanePostCodeData, laneCepRangeData, actionCodeFlag, errMsg := validRowData(ctx, objectType, row, region, channelLevelMap, channelInfoMap, isSiteLine) // set update event to pickup done as default
		if len(errMsg) != 0 {
			logger.CtxLogErrorf(ctx, "row=%d|error=%s", row.Row, errMsg)
			tmpErrorRow := &ErrorRowMessage{
				Index:        row.Row,
				ErrorMessage: errMsg,
			}
			exceptionRow = append(exceptionRow, tmpErrorRow)
			continue
		}

		if cdt.IsCepRangeCountry(region) { // for cep range region, need to delete all data first
			if _, ok := productMap[row.ProductID]; !ok {
				productList = append(productList, row.ProductID)
				productMap[row.ProductID] = true
			}
		}

		if locationData != nil {
			if actionCodeFlag {
				addedLocationRows = append(addedLocationRows, locationData)
			} else {
				deletedLocationRows = append(deletedLocationRows, locationData)
			}
		}
		if postcodeData != nil {
			if actionCodeFlag {
				addedPostcodeRows = append(addedPostcodeRows, postcodeData)
			} else {
				deletedPostcodeRows = append(deletedPostcodeRows, postcodeData)
			}
		}
		if cepRangeData != nil {
			addedCepRangeRows = append(addedCepRangeRows, cepRangeData)
		}

		if laneLocationData != nil {
			if actionCodeFlag {
				addedLaneLocationRows = append(addedLaneLocationRows, laneLocationData)
			} else {
				deletedLaneLocationRows = append(deletedLaneLocationRows, laneLocationData)
			}
		}
		if lanePostCodeData != nil {
			if actionCodeFlag {
				addedLanePostcodeRows = append(addedLanePostcodeRows, lanePostCodeData)
			} else {
				deletedLanePostcodeRows = append(deletedLanePostcodeRows, lanePostCodeData)
			}
		}
		if laneCepRangeData != nil {
			addedLaneCepRangeRows = append(addedLaneCepRangeRows, laneCepRangeData)
		}
	}
	if len(exceptionRow) > 0 {
		return exceptionRow, len(rows), 0, nil
	}
	processedRows, lcosErr := m.importDataIntoDBAndHBase(ctx, addedLocationRows, addedPostcodeRows, addedCepRangeRows, addedLaneLocationRows, addedLanePostcodeRows, addedLaneCepRangeRows, deletedLocationRows, deletedPostcodeRows, deletedLaneLocationRows, deletedLanePostcodeRows, productList, region, isSiteLine, len(rows), objectType)
	return exceptionRow, len(rows), processedRows, lcosErr
}

func validLocationLevelAndAddress(ctx utils.LCOSContext, locationLevel int, originCountry, originState, originCity, originDistrict, destinationCountry, destinationState, destinationCity, destinationDistrict, destinationPostcode, destinationCepRangeLeft, destinationCepRangeRight string, productID, region string, cbFlag bool, updateEvent uint8) (int, int, int, int, *lcos_error.LCOSError) {

	level := locationLevel
	var originLocationID, destinationLocationID, cepRangeLeft, cepRangeRight int
	// for cb channel, origin address need to be empty when update event is one of TWS Inbound / TWS Outbound
	if !cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
		if originCountry != "" || originState != "" || originCity != "" || originDistrict != "" {
			errMsg := fmt.Sprintf("product:[%s] origin [country, state, city, district] must be blank for update event:[Pickup Done / TWS Inbound / TWS Outbound]", productID)
			return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
		}
	}

	// for cep range region, destination postcode need to be empty
	if cdt.IsCepRangeCountry(region) && destinationPostcode != "" {
		errMsg := fmt.Sprintf("destination postcode must be blank when region:[%s] is cep range region", region)
		return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}
	// for non cep range region, cep range has to be blank
	if !cdt.IsCepRangeCountry(region) && (destinationCepRangeLeft != "" || destinationCepRangeRight != "") {
		errMsg := fmt.Sprintf("cep range left and right must be blank when region:[%s] is non cep range region", region)
		return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}

	if level != int(constant.CDTPostcode) && len(destinationPostcode) > 0 {
		return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "postcode need to be empty when location level is not postcode")
	}
	if level != int(constant.CepRange) && (len(destinationCepRangeLeft) > 0 || len(destinationCepRangeRight) > 0) {
		return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cep range initial and final need to be empty when location level is not cep range")
	}

	switch level {
	case int(constant.Country):
		if !cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
			if region != destinationCountry || destinationState != "" || destinationCity != "" || destinationDistrict != "" || destinationPostcode != "" {
				errMsg := fmt.Sprintf("product:[%s] destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		} else {
			if (region != destinationCountry || region != originCountry) || (originState != "" || destinationState != "") || (originCity != "" || destinationCity != "") || (originDistrict != "" || destinationDistrict != "") || destinationPostcode != "" {
				errMsg := fmt.Sprintf("location is not valid:[%s]", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		}
	case int(constant.State):
		if !cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
			if region != destinationCountry || destinationState == "" || destinationCity != "" || destinationDistrict != "" || destinationPostcode != "" {
				errMsg := fmt.Sprintf("product:[%s] destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		} else {
			if (region != destinationCountry || region != originCountry) || (originState == "" || destinationState == "") || (originCity != "" || destinationCity != "") || (originDistrict != "" || destinationDistrict != "") || destinationPostcode != "" { // for state level, only state can be non-empty
				errMsg := fmt.Sprintf("product:[%s] origin or destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		}
	case int(constant.City):
		if !cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
			if region != destinationCountry || destinationState == "" || destinationCity == "" || destinationDistrict != "" || destinationPostcode != "" {
				errMsg := fmt.Sprintf("product:[%s] destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		} else {
			if (region != destinationCountry || region != originCountry) || (originState == "" || destinationState == "") || (originCity == "" || destinationCity == "") || (originDistrict != "" || destinationDistrict != "") || destinationPostcode != "" {
				errMsg := fmt.Sprintf("product:[%s] origin or destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		}
	case int(constant.District):
		if !cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
			if region != destinationCountry || destinationState == "" || destinationCity == "" || destinationDistrict == "" || destinationPostcode != "" {
				errMsg := fmt.Sprintf("product:[%s] destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		} else {
			if (region != destinationCountry || region != originCountry) || (originState == "" || destinationState == "") || (originCity == "" || destinationCity == "") || (originDistrict == "" || destinationDistrict == "") || destinationPostcode != "" {
				errMsg := fmt.Sprintf("product:[%s] origin or destination district is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		}
	case int(constant.CDTPostcode): // for postcode upload
		if !cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
			if region != destinationCountry || destinationState != "" || destinationCity != "" || destinationDistrict != "" || destinationPostcode == "" { // postcode is required
				errMsg := fmt.Sprintf("product:[%s] destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		} else {
			if region != destinationCountry || region != originCountry || destinationState != "" || destinationCity != "" || destinationDistrict != "" || destinationPostcode == "" { // postcode is required
				errMsg := fmt.Sprintf("product:[%s] origin or destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		}
	case int(constant.CepRange): // for cep range upload
		if !cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
			if region != destinationCountry || destinationState != "" || destinationCity != "" || destinationDistrict != "" || destinationCepRangeLeft == "" || destinationCepRangeRight == "" {
				errMsg := fmt.Sprintf("product:[%s] destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		} else {
			if (region != destinationCountry || region != originCountry) || destinationState != "" || destinationCity != "" || destinationDistrict != "" || destinationCepRangeLeft == "" || destinationCepRangeRight == "" {
				errMsg := fmt.Sprintf("product:[%s] destination location is not valid", productID)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
		}
		destCodeStart, err := strconv.Atoi(strings.TrimSpace(destinationCepRangeLeft))
		if err != nil {
			errMsg := fmt.Sprintf("invalid cdt destination cep range initial, value=%s", destinationCepRangeLeft)
			return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
		}
		destCodeEnd, err := strconv.Atoi(strings.TrimSpace(destinationCepRangeRight))
		if err != nil {
			errMsg := fmt.Sprintf("invalid cdt destination cep range final, value=%s", destinationCepRangeRight)
			return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
		}
		if destCodeStart > destCodeEnd {
			errMsg := fmt.Sprintf("destination postcode start exceed destination postcode end, start=%s, end=%s", destinationCepRangeLeft, destinationCepRangeRight)
			return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
		}
		cepRangeLeft = destCodeStart
		cepRangeRight = destCodeEnd
	default: // level is not right
		return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("location level is not valid, level=%d", level))
	}
	if level >= int(constant.State) {
		if level < int(constant.CepRange) {
			destLoc, lErr := address_service.LocationServer.GetLocationInfoByName(ctx, destinationCountry, destinationState, destinationCity, destinationDistrict, "")
			if lErr != nil {
				errMsg := fmt.Sprintf("can't convert destination [%v, %v, %v] to a location_id", destinationState, destinationCity, destinationDistrict)
				return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
			}
			for _, locationId := range []int{destLoc.StreetLocationId, destLoc.DistrictLocationId, destLoc.CityLocationId, destLoc.StateLocationId} {
				if locationId > 0 {
					destinationLocationID = locationId
					break
				}
			}
		}

		if cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
			// for postcode level, origin location could be country level, set origin location id to 0
			if originState == "" && originCity == "" && originDistrict == "" {
				originLocationID = 0
			} else {
				originLoc, lErr := address_service.LocationServer.GetLocationInfoByName(ctx, originCountry, originState, originCity, originDistrict, "")
				if lErr != nil {
					errMsg := fmt.Sprintf("can't convert origin [%s, %s, %s] to a location_id", originState, originCity, originDistrict)
					return 0, 0, 0, 0, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
				}
				for _, locationId := range []int{originLoc.StreetLocationId, originLoc.DistrictLocationId, originLoc.CityLocationId, originLoc.StateLocationId} {
					if locationId > 0 {
						originLocationID = locationId
						break
					}
				}
			}
		}
	}
	return originLocationID, destinationLocationID, cepRangeLeft, cepRangeRight, nil
}

// validRowData
func validRowData(ctx utils.LCOSContext, objectType uint8, row *MergeCdtUploadStruct, region string, channelLevelMap map[string]bool, channelInfoMap map[string]*channel_service.Channel, isSiteLine uint8) (*manual_update_rule.CdtManualUpdateLocationDataTab, *manual_update_rule.CdtManualUpdateZipcodeDataTab, *manual_update_rule.CdtManualUpdatePostcodeDataTab, *lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab, *lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab, *lane_manual_update_rule.LaneCdtManualUpdateCepRangeDataTab, bool, string) {
	level, err := strconv.Atoi(row.LocationLevel)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, false, err.Error()
	}

	if flag, ok := channelLevelMap[row.ProductID]; !ok || !flag {
		errMsg := fmt.Sprintf("The country-level CDT for product id:[%s], is_site_line:[%d], event:[Shipped Out] does not exist, please add it in upload file", row.ProductID, isSiteLine)
		return nil, nil, nil, nil, nil, nil, false, errMsg
	}

	var cbFlag bool
	// 检查当前product是否可以在当前国家获取
	if _, ok := channelInfoMap[row.ProductID]; !ok {
		errMsg := fmt.Sprintf("channel:[%s] not found in current region:[%s]", row.ProductID, region)
		return nil, nil, nil, nil, nil, nil, false, errMsg
	}
	if channelInfoMap[row.ProductID].CBType == 0 {
		cbFlag = false
	} else {
		cbFlag = true
	}

	// valid update event
	updateEvent, updateEventErr := edd_constant.GetUpdateEvent(row.UpdateEvent, cbFlag)
	if updateEventErr != nil {
		return nil, nil, nil, nil, nil, nil, false, updateEventErr.Error()
	}

	originLocationID, destinationLocationID, cepRangeLeft, cepRangeRight, lcosErr := validLocationLevelAndAddress(ctx, level, row.OriginCountry, row.OriginState, row.OriginCity, row.OriginDistrict, row.DestinationCountry, row.DestinationState, row.DestinationCity, row.DestinationDistrict, row.DestinationPostcode, row.DestinationCEPInitial, row.DestinationCEPFinal, row.ProductID, region, cbFlag, updateEvent)
	if lcosErr != nil {
		return nil, nil, nil, nil, nil, nil, false, lcosErr.Msg
	}

	leadTimeMin, err1 := strconv.ParseFloat(row.LeadTimeMin, 64)
	if err1 != nil {
		return nil, nil, nil, nil, nil, nil, false, err1.Error()
	}
	leadTimeMax, err1 := strconv.ParseFloat(row.LeadTimeMax, 64)
	if err1 != nil {
		return nil, nil, nil, nil, nil, nil, false, err1.Error()
	}

	if leadTimeMin > leadTimeMax {
		objectTypeText := edd_constant.ObjectTypeMap[objectType]
		return nil, nil, nil, nil, nil, nil, false, fmt.Sprintf("%s_MIN should not be larger than field: %s_MAX", objectTypeText, objectTypeText)
	}

	// check whether leader time max over threshold
	threshold := config.GetDataNotifyThreshold(ctx, row.ProductID, row.OriginCountry != row.DestinationCountry)
	if leadTimeMax >= float64(threshold) {
		return nil, nil, nil, nil, nil, nil, false, fmt.Sprintf("The value of CDT exceeds the threshold: [%d]", threshold)
	}

	var addCodeFlag bool
	trimedActionCode := strings.TrimSpace(row.ActionCode)
	// for cep range region, action code need to be empty
	if cdt.IsCepRangeCountry(region) {
		if trimedActionCode != "" {
			return nil, nil, nil, nil, nil, nil, false, fmt.Sprintf("action code has to be empty when region:[%s] is cep range region", region)
		} else {
			// set default to "add"
			trimedActionCode = "1"
		}
	}
	if trimedActionCode == "1" {
		addCodeFlag = true
	} else if trimedActionCode == "-1" {
		addCodeFlag = false
	} else { // not valid action code
		errMsg := "action code is not valid"
		logger.CtxLogInfof(ctx, errMsg)
		return nil, nil, nil, nil, nil, nil, false, errMsg
	}

	extraDataBytes, err := jsoniter.Marshal(row.CdtExtraData)
	if err != nil {
		errMsg := fmt.Sprintf("CdtExtraData marshal fail, err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return nil, nil, nil, nil, nil, nil, false, errMsg
	}
	cdtExtraData := string(extraDataBytes)

	// 对于Product的CDT，如果是shipped out系列的，在checkout的时候会查到，都要存入mysql；其他条件的才存入hbase
	if row.Type == edd_constant.ProductCdtTypeText && edd_constant.GetSeriesUpdateEvent(updateEvent) == edd_constant.ShippedOut {
		// product cdt
		switch level {
		case int(constant.CDTPostcode):
			cdtData := &manual_update_rule.CdtManualUpdateZipcodeDataTab{
				LocationLevel:       level,
				OriginLocationId:    originLocationID,
				ProductId:           row.ProductID,
				DestinationPostcode: strings.TrimSpace(row.DestinationPostcode),
				ExtraData:           "",
				IsSiteLine:          isSiteLine,
				Region:              region,
				ObjectType:          objectType,
				UpdateEvent:         updateEvent,
				CdtExtraData:        cdtExtraData,
			}
			cdtData.LeadTimeMin = leadTimeMin
			cdtData.LeadTimeMax = leadTimeMax
			return nil, cdtData, nil, nil, nil, nil, addCodeFlag, ""
		case int(constant.CepRange):
			cdtData := &manual_update_rule.CdtManualUpdatePostcodeDataTab{
				LocationLevel:              level,
				OriginLocationId:           originLocationID,
				ProductId:                  row.ProductID,
				DestinationPostcodeInitial: cepRangeLeft,
				DestinationPostcodeFinal:   cepRangeRight,
				ExtraData:                  "",
				IsSiteLine:                 isSiteLine,
				Region:                     region,
				ObjectType:                 objectType,
				UpdateEvent:                updateEvent,
				CdtExtraData:               cdtExtraData,
			}
			cdtData.LeadTimeMin = leadTimeMin
			cdtData.LeadTimeMax = leadTimeMax
			return nil, nil, cdtData, nil, nil, nil, false, ""
		default:
			cdtData := &manual_update_rule.CdtManualUpdateLocationDataTab{
				LocationLevel:         level,
				OriginLocationId:      originLocationID,
				DestinationLocationId: destinationLocationID,
				ProductId:             row.ProductID,
				IsSiteLine:            isSiteLine,
				Region:                region,
				ObjectType:            objectType,
				UpdateEvent:           updateEvent,
				CdtExtraData:          cdtExtraData,
			}
			cdtData.LeadTimeMin = leadTimeMin
			cdtData.LeadTimeMax = leadTimeMax

			return cdtData, nil, nil, nil, nil, nil, addCodeFlag, ""
		}
	} else {
		// lane cdt
		switch level {
		case int(constant.CDTPostcode):
			cdtData := &lane_manual_update_rule.LaneCdtManualUpdatePostCodeDataTab{
				LocationLevel:       level,
				OriginLocationId:    originLocationID,
				ProductId:           row.ProductID,
				LaneCode:            row.LaneCode,
				UpdateEvent:         updateEvent,
				DestinationPostcode: strings.TrimSpace(row.DestinationPostcode),
				IsSiteLine:          isSiteLine,
				Region:              region,
				CdtExtraData:        cdtExtraData,
			}
			cdtData.LeadTimeMin = leadTimeMin
			cdtData.LeadTimeMax = leadTimeMax
			return nil, nil, nil, nil, cdtData, nil, addCodeFlag, ""
		case int(constant.CepRange):
			cdtData := &lane_manual_update_rule.LaneCdtManualUpdateCepRangeDataTab{
				LocationLevel:              level,
				OriginLocationId:           originLocationID,
				ProductId:                  row.ProductID,
				LaneCode:                   row.LaneCode,
				UpdateEvent:                updateEvent,
				DestinationPostcodeInitial: cepRangeLeft,
				DestinationPostcodeFinal:   cepRangeRight,
				IsSiteLine:                 isSiteLine,
				Region:                     region,
				CdtExtraData:               cdtExtraData,
			}
			cdtData.LeadTimeMin = leadTimeMin
			cdtData.LeadTimeMax = leadTimeMax
			return nil, nil, nil, nil, nil, cdtData, addCodeFlag, ""
		default:
			cdtData := &lane_manual_update_rule.LaneCdtManualUpdateLocationDataTab{
				LocationLevel:         level,
				OriginLocationId:      originLocationID,
				DestinationLocationId: destinationLocationID,
				ProductId:             row.ProductID,
				LaneCode:              row.LaneCode,
				UpdateEvent:           updateEvent,
				IsSiteLine:            isSiteLine,
				Region:                region,
				CdtExtraData:          cdtExtraData,
			}
			cdtData.LeadTimeMin = leadTimeMin
			cdtData.LeadTimeMax = leadTimeMax
			return nil, nil, nil, cdtData, nil, nil, addCodeFlag, ""
		}
	}
}

func (m *manualUpdateService) postHandleUploadCdt(ctx utils.LCOSContext, objectType uint8, cdtStatus int, batchId, operator, fileName, remark string, failData []*ErrorRowMessage) *lcos_error.LCOSError {
	status, ok := constant.StatusMsgMap[cdtStatus]
	if !ok {
		status = "unknown_status"
	}
	object := "CDT"
	if objectType == edd_constant.LeadTimeObject {
		object = "Lead Time"
	}
	subject := fmt.Sprintf("Manual Update %s - {%s} - {%s}", object, fileName, status)
	var exceptionResult = struct {
		Data []EmailTable
	}{}
	var table = []EmailTable{}
	if failData != nil {
		for _, row := range failData {
			var tmp = EmailTable{
				Row:    strconv.Itoa(row.Index),
				ErrMsg: row.ErrorMessage,
			}
			table = append(table, tmp)
		}
		exceptionResult.Data = table
	}
	if exceptionResult.Data != nil {
		remark = ""
	}
	var tableTemp = `
	<table border="1">
		<tr>
		<th>Row</th>
		<th>Error Message</th>
		</tr>
		{{range .Data}}
		<tr>
			<td>{{.Row}}</td>
			<td>{{.ErrMsg}}</td>
		</tr>{{end}}
	</table>`
	t := template.Must(template.New("").Parse(tableTemp))
	var tmpOut bytes.Buffer
	if err := t.Execute(&tmpOut, &exceptionResult); err != nil {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("parse template:%v", err))
	}
	var temp = `
	<html>
		Batch_ID: %s<br/>
		status: %v.<br/>
		link_url: %s<br/>
		remark: %s<br/>
		%v
	</html>
	`
	body := fmt.Sprintf(temp, batchId, status, EmailLinkMap[utils.GetEnv(ctx)], remark, tmpOut.String())
	sErr := emailhelper.SendEmail([]string{operator}, subject, body)
	if sErr != nil {
		logger.CtxLogErrorf(ctx, "send email error:%v", sErr)
	}
	return sErr

}

func (m *manualUpdateService) ConvertLocationIdToLocation2(ctx utils.LCOSContext, originId, destId, level int, cdt *CdtData, cbFlag bool, updateEvent uint8) *lcos_error.LCOSError {
	destinationLoc, err := address_service.LocationServer.GetLocationInfoById(ctx, destId)
	if err != nil {
		return err
	}

	cdt.DestinationCountry = destinationLoc.Country
	if int8(level) != constant.Country {
		if int8(level) >= constant.State {
			cdt.DestinationState = destinationLoc.State
		}
		if int8(level) >= constant.City {
			cdt.DestinationCity = destinationLoc.City
		}
		if int8(level) >= constant.District {
			cdt.DestinationDistrict = destinationLoc.District
		}
		if cdt_common.IsOriginLocationNeeded(cbFlag, updateEvent) {
			originLoc, err := address_service.LocationServer.GetLocationInfoById(ctx, originId)
			if err != nil {
				return err
			}
			cdt.OriginCountry = originLoc.Country
			if int8(level) >= constant.State {
				cdt.OriginState = originLoc.State
			}
			if int8(level) >= constant.City {
				cdt.OriginCity = originLoc.City
			}
			if int8(level) >= constant.District {
				cdt.OriginDistrict = originLoc.District
			}
		}
	} else {
		return lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "parse cdt location data error")
	}

	return nil
}

func (m *manualUpdateService) FillCdtLocationInfo(ctx utils.LCOSContext, orgLocId, destLocId int, data *CdtData) *lcos_error.LCOSError {
	if destLocId != 0 {
		destLoc, err := address_service.LocationServer.GetLocationInfoById(ctx, destLocId)
		if err != nil {
			return err
		}
		data.DestinationCountry = destLoc.Country
		data.DestinationState = destLoc.State
		data.DestinationCity = destLoc.City
		data.DestinationDistrict = destLoc.District
	}
	if orgLocId != 0 {
		orgLoc, err := address_service.LocationServer.GetLocationInfoById(ctx, orgLocId)
		if err != nil {
			return err
		}
		data.OriginCountry = orgLoc.Country
		data.OriginState = orgLoc.State
		data.OriginCity = orgLoc.City
		data.OriginDistrict = orgLoc.District
	}
	return nil
}

// 上传的表格文件中的表头，与代码中的字段之间的映射，忽略大小写
var cdtFileTileMapToCodeTitle = map[string]string{
	"location_level":          "location_level",
	"origin_country":          "origin_country",
	"origin_state":            "origin_state",
	"origin_city":             "origin_city",
	"origin_district":         "origin_district",
	"destination_country":     "destination_country",
	"destination_state":       "destination_state",
	"destionation_city":       "destination_city", // 这个在旧模板是错误拼写的title
	"destination_city":        "destination_city", // 这个为了兼容旧模板的拼写错误
	"destination_district":    "destination_district",
	"destination_postcode":    "destination_postcode",
	"destination_cep_initial": "destination_cep_initial",
	"destination_cep_final":   "destination_cep_final",
	"product_id":              "product_id",
	"cdt_event":               "cdt_event",
	"day_of_week_group":       "day_of_week_group",
	"hour_of_event_time":      "hour_of_event_time",
	"edt_cdt_min":             "lead_time_min",
	"edt_cdt_max":             "lead_time_max",
	"action_code":             "action_code",
}

// 处理表头，识别出表格中每一列对应代码中的什么字段
func handleFileTitleSequence(ctx utils.LCOSContext, objectType uint8, titles []string) []string {
	resultList := make([]string, len(titles))
	for index, fileTitle := range titles {
		codeTitle := ""
		ok := false
		if objectType == edd_constant.CdtObject {
			codeTitle, ok = cdtFileTileMapToCodeTitle[strings.ToLower(fileTitle)]
		} else {
			codeTitle, ok = leadFileTileMapToCodeTitle[strings.ToLower(fileTitle)]
		}

		if !ok {
			logger.CtxLogErrorf(ctx, "unrecognized file title[%s]|columns[%c]", fileTitle, index+'A')
			continue
		}

		resultList[index] = codeTitle
	}

	return resultList
}

func parseFromRowToStructForCDT(row []string, titleList []string) (*RawCdtUploadStructForCdt, error) {
	tmp := make(map[string]interface{})
	result := &RawCdtUploadStructForCdt{}
	for index, item := range row {
		if index < len(titleList) {
			tmp[titleList[index]] = strings.TrimSpace(item)
		}
	}

	tmpResult, err := jsoniter.Marshal(tmp)
	if err != nil {
		return nil, err
	} else {
		err = jsoniter.Unmarshal(tmpResult, result)
		if err != nil {
			return nil, err
		}
	}
	return result, nil
}

var leadFileTileMapToCodeTitle = map[string]string{
	"type":                    "type",
	"location_level":          "location_level",
	"origin_country":          "origin_country",
	"origin_state":            "origin_state",
	"origin_city":             "origin_city",
	"origin_district":         "origin_district",
	"destination_country":     "destination_country",
	"destination_state":       "destination_state",
	"destionation_city":       "destination_city", // 这个在旧模板是错误拼写的title
	"destination_city":        "destination_city", // 这个为了兼容旧模板的拼写错误
	"destination_district":    "destination_district",
	"destination_postcode":    "destination_postcode",
	"destination_cep_initial": "destination_cep_initial",
	"destination_cep_final":   "destination_cep_final",
	"product_id":              "product_id",
	"lane_id":                 "lane_code",
	"update_event":            "update_event",
	"day_of_week_group":       "day_of_week_group",
	"hour_of_event_time":      "hour_of_event_time",
	"edd_lead_time_min":       "lead_time_min",
	"edd_lead_time_max":       "lead_time_max",
	"action_code":             "action_code",
}

func parseFromRowToStructForLead(row []string, titleList []string) (*RawCdtUploadStructForLead, error) {
	tmp := make(map[string]interface{})
	result := &RawCdtUploadStructForLead{}
	for index, item := range row {
		if index < len(titleList) {
			tmp[titleList[index]] = strings.TrimSpace(item)
		}
	}

	tmpResult, err := jsoniter.Marshal(tmp)
	if err != nil {
		return nil, err
	} else {
		err = jsoniter.Unmarshal(tmpResult, result)
		if err != nil {
			return nil, err
		}
	}
	return result, nil
}

func (m *manualUpdateService) getAndCheckDayGroup(ctx utils.LCOSContext, row int, extraData *common_utils.CdtExtraData, destGroupName string) (*common_utils.DayGroup, *lcos_error.LCOSError) {
	if destGroupName == "" {
		// 如果用户填的空group，就认为是不区分group，即"1,2,3,4,5,6,7"
		destGroupName = DayEmptyStr
	}

	// 借助dayName判断是否要找的 destGroupName
	for _, group := range extraData.DayGroups {
		if group.DayName == destGroupName {
			return group, nil
		}
	}

	// 如果找不到，就和历史的group判断是否有交叉，有交叉就报错
	if dupGroupName := checkDuplicateDay(ctx, destGroupName, extraData.DayGroups); dupGroupName != "" {
		errMsg := fmt.Sprintf("Row %d and other rows have same route and event, but there are duplicate day groups [%s] and [%s]", row, destGroupName, dupGroupName)
		logger.CtxLogErrorf(ctx, errMsg)
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}

	// 创建新的group，并给name赋值，append到result.CdtExtraData中
	dayGroup := &common_utils.DayGroup{
		DayName: destGroupName,
	}
	err := dayGroup.InitDays()
	if err != nil {
		errMsg := fmt.Sprintf("Row %d %s", row, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}

	extraData.DayGroups = append(extraData.DayGroups, dayGroup)
	return dayGroup, nil
}

func (m *manualUpdateService) getAndCheckTimeBucket(ctx utils.LCOSContext, row int, group *common_utils.DayGroup, destTime string) (*common_utils.TimeBucket, *lcos_error.LCOSError) {
	// 借助timeName判断是否要找的 destTime
	for _, time := range group.TimeBuckets {
		if time.TimeName == destTime {
			return time, nil
		}
	}

	// 如果找不到，就创建新的bucket，并给name赋值，append到group.TimeBuckets中
	time := &common_utils.TimeBucket{
		TimeName: destTime,
	}
	err := time.InitHour()
	if err != nil {
		errMsg := fmt.Sprintf("Row %d has invalid time bucket [%s]", row, destTime)
		return nil, lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, errMsg)
	}

	group.TimeBuckets = append(group.TimeBuckets, time)
	return time, nil
}

func isEmptyDayGroup(dayGroupStr string) bool {
	if dayGroupStr == "" || dayGroupStr == DayEmptyStr {
		return true
	}
	return false
}

func isEmptyTimeBucket(timeStr string) bool {
	if timeStr == "" || timeStr == HourEmptyStr {
		return true
	}
	return false
}

func checkDuplicateDay(ctx utils.LCOSContext, destName string, groups []*common_utils.DayGroup) string {
	destDaysList := strings.Split(destName, ",")
	// 对于当前group的每一天，都去已有group中检查是否出现过
	for _, destDay := range destDaysList {
		for _, group := range groups {
			// 如果group中出现过destDay，则存在冲突，将冲突的DayName返回出去
			if strings.Contains(group.DayName, destDay) {
				return group.DayName
			}
		}
	}

	return ""
}

func parseLeadTime(min string, max string) (float64, float64, error) {
	leadTimeMin, err := strconv.ParseFloat(min, 64)
	if err != nil {
		return 0, 0, err
	}
	leadTimeMax, err := strconv.ParseFloat(max, 64)
	if err != nil {
		return 0, 0, err
	}

	if leadTimeMin > leadTimeMax {
		return 0, 0, fmt.Errorf("MIN should not be larger than MAX")
	}

	return leadTimeMin, leadTimeMax, nil
}
