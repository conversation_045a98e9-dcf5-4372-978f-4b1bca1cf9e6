package manual_update_rule

import "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"

type CdtManualUpdateRecord struct {
	BatchId    string `json:"batch_id"`
	FileName   string `json:"file_name"`
	FileUrl    string `json:"file_url"`
	ImportTime int    `json:"import_time"`
	Remark     string `json:"remark"`
	Status     int    `json:"status"`
	ObjectType uint8  `json:"object_type"`
}

// 暂存解析的内容
type RawCdtUploadStructForCdt struct { // general parse for cdt
	Row                   int    `json:"-"`
	LocationLevel         string `json:"location_level"`
	OriginCountry         string `json:"origin_country"`
	OriginState           string `json:"origin_state"`
	OriginCity            string `json:"origin_city"`
	OriginDistrict        string `json:"origin_district"`
	DestinationCountry    string `json:"destination_country"`
	DestinationState      string `json:"destination_state"`
	DestinationCity       string `json:"destination_city"`
	DestinationDistrict   string `json:"destination_district"`
	DestinationPostcode   string `json:"destination_postcode"`
	DestinationCEPInitial string `json:"destination_cep_initial"`
	DestinationCEPFinal   string `json:"destination_cep_final"`
	ProductID             string `json:"product_id"`
	DayOfWeekGroup        string `json:"day_of_week_group"`
	HourOfEventTime       string `json:"hour_of_event_time"`
	LeadTimeMin           string `json:"lead_time_min"`
	LeadTimeMax           string `json:"lead_time_max"`
	ActionCode            string `json:"action_code"`
	CDTEvent              string `json:"cdt_event"` // 对应excel文件里面的CDT_Event
}

type RawCdtUploadStructForLead struct { // general parse for Lead
	Row                   int    `json:"-"`
	Type                  string `json:"type"`
	LocationLevel         string `json:"location_level"`
	OriginCountry         string `json:"origin_country"`
	OriginState           string `json:"origin_state"`
	OriginCity            string `json:"origin_city"`
	OriginDistrict        string `json:"origin_district"`
	DestinationCountry    string `json:"destination_country"`
	DestinationState      string `json:"destination_state"`
	DestinationCity       string `json:"destination_city"`
	DestinationDistrict   string `json:"destination_district"`
	DestinationPostcode   string `json:"destination_postcode"`
	DestinationCEPInitial string `json:"destination_cep_initial"`
	DestinationCEPFinal   string `json:"destination_cep_final"`
	ProductID             string `json:"product_id"`
	LaneCode              string `json:"lane_code"`
	UpdateEvent           string `json:"update_event"`
	DayOfWeekGroup        string `json:"day_of_week_group"`
	HourOfEventTime       string `json:"hour_of_event_time"`
	LeadTimeMin           string `json:"lead_time_min"`
	LeadTimeMax           string `json:"lead_time_max"`
	ActionCode            string `json:"action_code"`
}

// 将多行 RawCdtUploadStructForCdt 或 RawCdtUploadStructForLead，聚合成一行 MergeCdtUploadStruct
type MergeCdtUploadStruct struct {
	Row                   int                       `json:"-"`
	Type                  string                    `json:"type"`
	LocationLevel         string                    `json:"location_level"`
	OriginCountry         string                    `json:"origin_country"`
	OriginState           string                    `json:"origin_state"`
	OriginCity            string                    `json:"origin_city"`
	OriginDistrict        string                    `json:"origin_district"`
	DestinationCountry    string                    `json:"destination_country"`
	DestinationState      string                    `json:"destination_state"`
	DestinationCity       string                    `json:"destination_city"`
	DestinationDistrict   string                    `json:"destination_district"`
	DestinationPostcode   string                    `json:"destination_postcode"`
	DestinationCEPInitial string                    `json:"destination_cep_initial"`
	DestinationCEPFinal   string                    `json:"destination_cep_final"`
	ProductID             string                    `json:"product_id"`
	LaneCode              string                    `json:"lane_code"`
	UpdateEvent           string                    `json:"update_event"`
	LeadTimeMin           string                    `json:"lead_time_min"`
	LeadTimeMax           string                    `json:"lead_time_max"`
	ActionCode            string                    `json:"action_code"`
	CdtExtraData          common_utils.CdtExtraData `json:"cdt_extra_data"`
}

// 存储解析过程中遇到的错误列
type ErrorRowMessage struct {
	Index        int
	ErrorMessage string
}

type EmailTable struct {
	Row    string
	ErrMsg string
}

// CdtData 导出excel的实体
type CdtData struct {
	CdtType               string `excel:"title:Type" json:"cdt_type"`
	Level                 string `excel:"title:Location_level" json:"location_level"`
	OriginCountry         string `excel:"title:Origin_country" json:"origin_country"`
	OriginState           string `excel:"title:Origin_state" json:"origin_state"`
	OriginCity            string `excel:"title:Origin_city" json:"origin_city"`
	OriginDistrict        string `excel:"title:Origin_district" json:"origin_district"`
	DestinationCountry    string `excel:"title:Destination_country" json:"destination_country"`
	DestinationState      string `excel:"title:Destination_state" json:"destination_state"`
	DestinationCity       string `excel:"title:Destination_city" json:"destination_city"`
	DestinationDistrict   string `excel:"title:Destination_district" json:"destination_district"`
	DestinationCEPInitial string `excel:"title:Destination_postcode" json:"destination_CEP_initial,omitempty"`
	DestinationCEPFinal   string `excel:"title:Destination_CEP_initial" json:"destination_CEP_final,omitempty"`
	DestinationPostcode   string `excel:"title:Destination_CEP_final" json:"destination_postcode,omitempty"`
	ProductId             string `excel:"title:Product_id" json:"channel_id"`
	LaneCode              string `excel:"title:Lane_id" json:"lane_code"`
	CDTEvent              string `excel:"title:CDT_Event" json:"cdt_event"`
	UpdateEvent           string `excel:"title:Update_event" json:"update_event"`
	CdtMin                string `excel:"title:EDT_CDT_min" json:"cdt_min"`
	CdtMax                string `excel:"title:EDT_CDT_max" json:"cdt_max"`
	LeadTimeMin           string `excel:"title:EDD_Lead_Time_min" json:"lead_time_min"`
	LeadTimeMax           string `excel:"title:EDD_Lead_Time_max" json:"lead_time_max"`

	CdtExtraData string
}

// SplitCdtData 导出excel的实体，会按照 CdtExtraData拆分成多行
type SplitCdtData struct {
	CdtType               string `excel:"title:Type" json:"cdt_type"`
	Level                 string `excel:"title:Location_level" json:"location_level"`
	OriginCountry         string `excel:"title:Origin_country" json:"origin_country"`
	OriginState           string `excel:"title:Origin_state" json:"origin_state"`
	OriginCity            string `excel:"title:Origin_city" json:"origin_city"`
	OriginDistrict        string `excel:"title:Origin_district" json:"origin_district"`
	DestinationCountry    string `excel:"title:Destination_country" json:"destination_country"`
	DestinationState      string `excel:"title:Destination_state" json:"destination_state"`
	DestinationCity       string `excel:"title:Destination_city" json:"destination_city"`
	DestinationDistrict   string `excel:"title:Destination_district" json:"destination_district"`
	DestinationCEPInitial string `excel:"title:Destination_postcode" json:"destination_CEP_initial,omitempty"`
	DestinationCEPFinal   string `excel:"title:Destination_CEP_initial" json:"destination_CEP_final,omitempty"`
	DestinationPostcode   string `excel:"title:Destination_CEP_final" json:"destination_postcode,omitempty"`
	ProductId             string `excel:"title:Product_id" json:"channel_id"`
	LaneCode              string `excel:"title:Lane_id" json:"lane_code"`
	CDTEvent              string `excel:"title:CDT_Event" json:"cdt_event"`
	UpdateEvent           string `excel:"title:Update_event" json:"update_event"`
	DayOfWeekGroup        string `excel:"title:day_of_week_group" json:"day_of_week_group"`
	HourOfEventTime       string `excel:"title:hour_of_event_time" json:"hour_of_event_time"`
	CdtMin                string `excel:"title:EDT_CDT_min" json:"cdt_min"`
	CdtMax                string `excel:"title:EDT_CDT_max" json:"cdt_max"`
	LeadTimeMin           string `excel:"title:EDD_Lead_Time_min" json:"lead_time_min"`
	LeadTimeMax           string `excel:"title:EDD_Lead_Time_max" json:"lead_time_max"`
}

type ListManualUpdateResponse struct {
	PageNo uint32                   `json:"pageno"`
	Count  uint32                   `json:"count"`
	Total  uint32                   `json:"total"`
	List   []*CdtManualUpdateRecord `json:"list"`
}

type MoudleCdtCheckRsponse struct {
	Result     bool   `json:"result"`
	ErrMessage string `json:"err_message"`
}
