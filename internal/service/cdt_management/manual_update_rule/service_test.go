package manual_update_rule

import (
	"context"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/channel_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	jsoniter "github.com/json-iterator/go"
)

func Test_manualUpdateService_doHandleParseAndImportDataForBR(t *testing.T) {
	t.Skip()
	ctx := utils.NewCommonCtx(context.Background())
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.TEST))
	_ = os.Setenv("ENV", strings.ToLower(cf.TEST))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	if err := chassis.Init(); err != nil {
		t.Fatalf("init chassis: %v", err)
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}

	if err = startup.InitLogger(c); err != nil {
		t.Fatalf("InitLogger Error:%v", err)
	}

	localcache.InitDataVersionInfo()

	type fields struct {
		manualUpdateDao manual_update_rule.CDTManualUpdateRuleTabDAO
	}
	type args struct {
		ctx            utils.LCOSContext
		filePath       string
		region         string
		isLM           uint8
		isSiteLine     uint8
		countryCdtMap  map[string]bool
		channelInfoMap map[string]*channel_service.Channel
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*ErrorRowMessage
		want1  int
		want2  *lcos_error.LCOSError
	}{
		{
			name:   "test manual update upload for BR",
			fields: fields{manualUpdateDao: manual_update_rule.NewCdtManualUpdateRuleDao(nil)},
			args: args{
				ctx:            utils.NewCommonCtx(context.Background()),
				filePath:       "/Users/<USER>/Desktop/LM_BR_CDT.xlsx",
				region:         "BR",
				isLM:           1,
				isSiteLine:     0,
				countryCdtMap:  nil,
				channelInfoMap: nil,
			},
			want:  nil,
			want1: 0,
			want2: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &manualUpdateService{
				manualUpdateDao: tt.fields.manualUpdateDao,
			}

			// fill channel info map
			var channelInfoMap map[string]*channel_service.Channel
			ctx := context.Background()
			region := tt.args.region
			if tt.args.isSiteLine == constant.ENABLED {
				channelInfoMap, _ = product_service.GetAllProducts(ctx, region)
			} else {
				channelInfoMap, _ = channel_service.GetAllChannels(ctx, region)
			}

			countryCdtMap, _ := m.manualUpdateDao.GetCountryCdtLocationData(utils.NewCommonCtx(ctx), region, tt.args.isSiteLine, tt.args.isLM)

			tt.args.channelInfoMap = channelInfoMap
			tt.args.countryCdtMap = countryCdtMap

			got, _, got1, got2 := m.doHandleParseAndImportDataForCepRangeRegions(tt.args.ctx, tt.args.filePath, tt.args.region, tt.args.isLM, tt.args.isSiteLine, tt.args.countryCdtMap, tt.args.channelInfoMap)
			if !reflect.DeepEqual(got2, tt.want2) {
				infoStr, _ := jsoniter.MarshalToString(got)
				t.Logf("doHandleParseAndImportDataForCepRangeRegions() got = %v", infoStr)
				t.Logf("doHandleParseAndImportDataForCepRangeRegions() got1 = %v", got1)
				t.Errorf("doHandleParseAndImportDataForCepRangeRegions() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}

func Test_manualUpdateService_doHandleParseAndImportData(t *testing.T) {
	ctx := utils.NewCommonCtx(context.Background())
	t.Skip()
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.TEST))
	_ = os.Setenv("ENV", strings.ToLower(cf.TEST))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))

	if err := chassis.Init(); err != nil {
		t.Fatalf("init chassis: %v", err)
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}

	if err = startup.InitLogger(c); err != nil {
		t.Fatalf("InitLogger Error:%v", err)
	}

	localcache.InitDataVersionInfo()

	type fields struct {
		manualUpdateDao manual_update_rule.CDTManualUpdateRuleTabDAO
	}
	type args struct {
		ctx            utils.LCOSContext
		filePath       string
		region         string
		isLM           uint8
		isSiteLine     uint8
		countryCdtMap  map[string]bool
		channelInfoMap map[string]*channel_service.Channel
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*ErrorRowMessage
		want1  int
		want2  *lcos_error.LCOSError
	}{
		{
			name:   "test manual update upload",
			fields: fields{manualUpdateDao: manual_update_rule.NewCdtManualUpdateRuleDao(nil)},
			args: args{
				ctx:            utils.NewCommonCtx(context.Background()),
				filePath:       "/Users/<USER>/Desktop/LM_CDT.xlsx",
				region:         "ID",
				isLM:           1,
				isSiteLine:     1,
				countryCdtMap:  nil,
				channelInfoMap: nil,
			},
			want:  nil,
			want1: 0,
			want2: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &manualUpdateService{
				manualUpdateDao: tt.fields.manualUpdateDao,
			}

			// fill channel info map
			var channelInfoMap map[string]*channel_service.Channel
			ctx := context.Background()
			region := tt.args.region
			if tt.args.isSiteLine == constant.ENABLED {
				channelInfoMap, _ = product_service.GetAllProducts(ctx, region)
			} else {
				channelInfoMap, _ = channel_service.GetAllChannels(ctx, region)
			}

			countryCdtMap, _ := m.manualUpdateDao.GetCountryCdtLocationData(utils.NewCommonCtx(ctx), region, tt.args.isSiteLine, tt.args.isLM)

			tt.args.channelInfoMap = channelInfoMap
			tt.args.countryCdtMap = countryCdtMap

			got, _, got1, got2 := m.doHandleParseAndImportData(tt.args.ctx, tt.args.filePath, tt.args.region, tt.args.isLM, tt.args.isSiteLine, tt.args.countryCdtMap, tt.args.channelInfoMap)
			if !reflect.DeepEqual(got2, tt.want2) {
				infoStr, _ := jsoniter.MarshalToString(got)
				t.Logf("doHandleParseAndImportData() got = %v", infoStr)
				t.Logf("doHandleParseAndImportData() got1 = %v", got1)
				t.Errorf("doHandleParseAndImportData() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}
