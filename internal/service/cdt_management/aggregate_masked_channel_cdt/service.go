package aggregate_masked_channel_cdt

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	commonConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	aggregate_masked_channnel_cdt2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/aggregate_masked_channel_cdt"
	aggregate_masked_channel_cdt3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcos_service"
	"strings"
)

type AggregateMaskedChannelCdtInterface interface {
	CreateAggregateMaskedChannelCdt(ctx utils.LCOSContext, request *aggregate_masked_channnel_cdt2.CreateAggregateMaskedChannelRequest) *lcos_error.LCOSError
	ListAggregateMaskedChannelCdt(ctx utils.LCOSContext, request *aggregate_masked_channnel_cdt2.ListAggregateMaskedChannelRequest) (interface{}, *lcos_error.LCOSError)
	GetAggregateMaskedChannelCdtById(ctx utils.LCOSContext, request *aggregate_masked_channnel_cdt2.RuleIdAggregateMaskedChannelRequest) (interface{}, *lcos_error.LCOSError)
	DisableAggregateMaskedChannelCdt(ctx *utils.HttpContext, request *aggregate_masked_channnel_cdt2.RuleIdAggregateMaskedChannelRequest) *lcos_error.LCOSError
}

type aggregateMaskedChannelCdt struct {
	aggregateMaskedChannelCdtDao aggregate_masked_channel_cdt3.AggregateMaskedChannelCdtDao
	abTestRuleDao                cdt_ab_test.CdtAbTestRuleDao
}

func NewAggregateMaskedChannelCdt(aggregateMaskedChannelCdtDao aggregate_masked_channel_cdt3.AggregateMaskedChannelCdtDao, abTestRuleDao cdt_ab_test.CdtAbTestRuleDao) *aggregateMaskedChannelCdt {
	return &aggregateMaskedChannelCdt{
		aggregateMaskedChannelCdtDao: aggregateMaskedChannelCdtDao,
		abTestRuleDao:                abTestRuleDao,
	}
}

var _ AggregateMaskedChannelCdtInterface = (*aggregateMaskedChannelCdt)(nil)

func (a *aggregateMaskedChannelCdt) CreateAggregateMaskedChannelCdt(ctx utils.LCOSContext, request *aggregate_masked_channnel_cdt2.CreateAggregateMaskedChannelRequest) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	operator := ctx.GetUserEmail()

	err := a.checkABTestRule(ctx, request.MaskedProductID)
	if err != nil {
		return err
	}

	//先搜索出统一时间生效的，所有的upcoming、 active规则
	existModels, lcosErr := a.aggregateMaskedChannelCdtDao.SearchAggregateMaskedChannelCdt(ctx, map[string]interface{}{"rule_status in": []int{edd_constant.Active, edd_constant.UpComing},
		"masked_product_id": request.MaskedProductID, "effective_time": request.EffectiveTime})
	if lcosErr != nil {
		return lcosErr
	}
	if len(existModels) != 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "existed active or upcoming in the same timezone,plz check|masking_channel_id=[%s]", request.MaskedProductID)
	}

	//如果所选的时间 < 当前时间, 则报错
	currentTimestamp := uint32(recorder.Now(ctx).Unix())
	if request.EffectiveTime < currentTimestamp {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("create fail,time has reached|request time=%d,now time =%d", request.EffectiveTime, currentTimestamp))
	}

	ruleStatus := edd_constant.UpComing
	model := &aggregate_masked_channel_cdt3.AggregateMaskedChannelCdtTab{
		MaskedProductID:     request.MaskedProductID,
		MaxCdtAggregateRule: *request.MaxCdtAggregateRule,
		MinCdtAggregateRule: *request.MinCdtAggregateRule,
		EffectiveTime:       request.EffectiveTime,
		RuleStatus:          int8(ruleStatus),
		Region:              region,
		Operator:            operator,
	}

	return a.aggregateMaskedChannelCdtDao.CreateAggregateMaskedChannelCdt(ctx, model)
}

func (a *aggregateMaskedChannelCdt) ListAggregateMaskedChannelCdt(ctx utils.LCOSContext, request *aggregate_masked_channnel_cdt2.ListAggregateMaskedChannelRequest) (interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	var pageNo uint32 = 1
	var Count uint32 = 10

	//先搜索出所有的active规则 避免有多条active的情况 一起检查
	activeModels, lcosErr := a.aggregateMaskedChannelCdtDao.SearchAggregateMaskedChannelCdt(ctx, map[string]interface{}{"rule_status": edd_constant.Active, "region": region})
	if lcosErr != nil {
		return nil, lcosErr
	}
	activeMChannelIdsMap := make(map[string][]uint64)
	for _, activeModel := range activeModels {
		activeMChannelIdsMap[activeModel.MaskedProductID] = append(activeMChannelIdsMap[activeModel.MaskedProductID], activeModel.Id)
	}

	upcomingModels, lcosErr := a.aggregateMaskedChannelCdtDao.SearchAggregateMaskedChannelCdt(ctx, map[string]interface{}{"rule_status": edd_constant.UpComing, "region": region})
	if lcosErr != nil {
		return nil, lcosErr
	}
	upcomingMChannelIdsMap := make(map[string][]uint64)
	for _, upcomingModel := range upcomingModels {
		upcomingMChannelIdsMap[upcomingModel.MaskedProductID] = append(upcomingMChannelIdsMap[upcomingModel.MaskedProductID], upcomingModel.Id)
	}

	shouldActiveMap := make(map[string]aggregate_masked_channnel_cdt2.ShouldActiveRule)
	// 获取当前的时间戳
	currentTimestamp := uint32(recorder.Now(ctx).Unix())

	var needExpiredIDList []uint64
	var needActiveIDList []uint64
	for _, upcomingModel := range upcomingModels {
		// 如果当前任务是upcoming，如果超过生效时间，则置为active
		if upcomingModel.NeedToBeUpdatedToActive(currentTimestamp) {
			if ruleInfo, ok := shouldActiveMap[upcomingModel.MaskedProductID]; ok {
				if ruleInfo.EffectiveTime < upcomingModel.EffectiveTime {
					//更新为最晚生效的那一条数据
					ruleInfo.EffectiveTime = upcomingModel.EffectiveTime
					ruleInfo.RuleId = upcomingModel.Id
				} else {
					//当前需要过期
					needExpiredIDList = append(needExpiredIDList, upcomingModel.Id)
				}
			} else {
				shouldActiveMap[upcomingModel.MaskedProductID] = aggregate_masked_channnel_cdt2.ShouldActiveRule{
					EffectiveTime: upcomingModel.EffectiveTime,
					RuleId:        upcomingModel.Id,
				}
			}
			//如果有历史active数据，置为expired，并记录needExpiredIdList
			if needExpiredIdList, ok := activeMChannelIdsMap[upcomingModel.MaskedProductID]; ok {
				needExpiredIDList = append(needExpiredIDList, needExpiredIdList...)
			}
		}
	}

	for _, activeRule := range shouldActiveMap {
		needActiveIDList = append(needActiveIDList, activeRule.RuleId)
	}
	lcosErr = a.aggregateMaskedChannelCdtDao.UpdateAggregateMaskedChannelCdt(ctx, map[string]interface{}{"id in": needActiveIDList}, map[string]interface{}{"rule_status": edd_constant.Active})
	if lcosErr != nil {
		return nil, lcosErr
	}
	lcosErr = a.aggregateMaskedChannelCdtDao.UpdateAggregateMaskedChannelCdt(ctx, map[string]interface{}{"id in": needExpiredIDList}, map[string]interface{}{"rule_status": edd_constant.Expired})
	if lcosErr != nil {
		return nil, lcosErr
	}

	if request.PageNo != nil {
		pageNo = *request.PageNo
	}
	if request.Count != nil {
		Count = *request.Count
	}
	queryMap := map[string]interface{}{
		"region": region,
	}
	if request.MaskedProductID != nil {
		queryMap["masked_product_id"] = *request.MaskedProductID
	}
	if request.RuleStatus != nil {
		queryMap["rule_status"] = *request.RuleStatus
	}

	models, total, lcosErr := a.aggregateMaskedChannelCdtDao.ListAllAggregateMaskedChannelCdtByPage(ctx, queryMap, pageNo, Count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	return map[string]interface{}{
		"list":   models,
		"pageno": pageNo,
		"count":  Count,
		"total":  total,
	}, nil
}

func (a *aggregateMaskedChannelCdt) GetAggregateMaskedChannelCdtById(ctx utils.LCOSContext, request *aggregate_masked_channnel_cdt2.RuleIdAggregateMaskedChannelRequest) (interface{}, *lcos_error.LCOSError) {
	models, lcosErr := a.aggregateMaskedChannelCdtDao.SearchAggregateMaskedChannelCdt(ctx, map[string]interface{}{"id": request.ID})
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(models) <= 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find aggregate masked channel fail|id=%d", request.ID))
	}
	return models[0], nil
}

func (a *aggregateMaskedChannelCdt) DisableAggregateMaskedChannelCdt(ctx *utils.HttpContext, request *aggregate_masked_channnel_cdt2.RuleIdAggregateMaskedChannelRequest) *lcos_error.LCOSError {
	// disable 规则前对灰度配置进行check
	models, lcosErr := a.aggregateMaskedChannelCdtDao.SearchAggregateMaskedChannelCdt(ctx, map[string]interface{}{"id": request.ID})
	if lcosErr != nil {
		return lcosErr
	}
	if len(models) <= 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find aggregate masked channel fail|id=%d", request.ID))
	}
	mChannelRuleModel := models[0]

	region := strings.ToUpper(ctx.GetCountry())

	lcosService := lcos_service.NewLCOSService(ctx, region)
	lcosResponse, lcosErr := lcosService.ListMChannelGreyConfigByRegion(ctx, region)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "ListMChannelGreyConfigByRegion error, region=%s, code=%d, msg=%s", region, lcosErr.RetCode, lcosErr.Msg)
		return lcosErr
	}

	for _, productGreyConfig := range lcosResponse.GetMchannelGreyConfigList() {
		if productGreyConfig.GetProductId() == mChannelRuleModel.MaskedProductID && productGreyConfig.GetPercentage() > 0 {
			//若当前mchannel 的apollo中灰度规则百分比大于0 需要报错
			_ = monitor.ReportEvent(commonConstant.CatModuleDisableMChannelHandle, commonConstant.EventNameDisableMChannel, commonConstant.StatusError, "disable mchannel")
			return lcos_error.NewLCOSError(lcos_error.DeliveryPrefixErrorCode, "Can not disable this aggregate masked channel , Need BE side close toggle firstly , pls contact PM")
		}
	}

	lcosErr = a.aggregateMaskedChannelCdtDao.UpdateAggregateMaskedChannelCdt(ctx, map[string]interface{}{"id": request.ID}, map[string]interface{}{"rule_status": edd_constant.Expired})
	if lcosErr != nil {
		return lcosErr
	}
	return nil
}

func (a *aggregateMaskedChannelCdt) checkABTestRule(ctx utils.LCOSContext, productId string) *lcos_error.LCOSError {
	// M chanel 只在edt使用，所以 object_type 为 CdtObject
	abTestRuleList, err := a.abTestRuleDao.ListCdtAbTestRuleByParams(ctx,
		map[string]interface{}{
			"product_id":  productId,
			"object_type": edd_constant.CdtObject,
			"rule_status": edd_constant.AbTestRuleStatusActive},
	)

	if err != nil {
		return err
	}

	if len(abTestRuleList) != 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf(
			"for M channel [%v], there is an active CDT AB testing rule, so you are not allowed to create a aggregated  rule at the same time", productId))
	}

	return nil
}
