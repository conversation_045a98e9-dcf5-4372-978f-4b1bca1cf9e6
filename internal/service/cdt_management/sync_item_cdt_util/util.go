package sync_item_cdt_util

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/redislibv2"
	"time"
)

func NewCdtSyncPipeline(extraClusterList []string) CdtSyncPipeline {
	pipelineList := make([]redis.Pipeliner, 0, len(extraClusterList)+1)
	pipelineList = append(pipelineList, redislibv2.GetItemCardEdtRedisClient().Pipeline())
	for _, clusterName := range extraClusterList {
		pipelineList = append(pipelineList, redislibv2.GetRedisClient(clusterName).Pipeline())
	}
	return pipelineList
}

type CdtSyncPipeline []redis.Pipeliner

func (m CdtSyncPipeline) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) {
	for _, pipeline := range m {
		_ = pipeline.Set(ctx, key, value, expiration)
	}
}

func (m CdtSyncPipeline) Exec(ctx context.Context) error {
	for _, pipeline := range m {
		if _, err := pipeline.Exec(ctx); err != nil {
			return err
		}
	}
	return nil
}
