package automated_volume

import (
	automated_volume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
)

type AutomatedVolumeGenerationRuleTabResponse struct {
	automated_volume.AutomatedVolumeGenerationRuleTab
	ExcludedDaysArray []*automated_volume2.ExcludedDay `json:"excluded_days_array"`
}
