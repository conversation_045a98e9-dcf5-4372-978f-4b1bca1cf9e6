package automated_volume

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/item_card_edt_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	automated_volume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/automated_volume"
	automated_volume_request "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	automated_volume3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	cdt_common2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/data_ssc"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seatalk"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
	"time"
)

type AutomatedVolumeInterface interface {
	CreateAutomatedVolume(ctx utils.LCOSContext, request *automated_volume2.CreateAutomatedVolumeRuleRequest) *lcos_error.LCOSError
	ListAutomatedVolume(ctx utils.LCOSContext, request *automated_volume2.ListAutomatedVolumeRuleRequest) (interface{}, *lcos_error.LCOSError)
	GetAutomatedVolumeById(ctx utils.LCOSContext, request *automated_volume2.DetailAutomatedVolumeRuleRequest) (interface{}, *lcos_error.LCOSError)
	ParseDataFileAndImportData(ctx utils.LCOSContext, request *automated_volume2.ImportAutomatedVolumeRuleRequest) *lcos_error.LCOSError
	DeleteAutomatedVolumeData(ctx utils.LCOSContext, region string, batchNum, expiredPeriodNum int) *lcos_error.LCOSError
	GetVolumeVersionByAutoVolumeRuleID(ctx utils.LCOSContext, ruleId uint64) (uint32, *lcos_error.LCOSError)
	GetVolumeDataByVolumeVersion(ctx utils.LCOSContext, ruleId uint64, volumeVersion uint32) ([]*automated_volume_generation_data.AutomatedVolumeGenerationDataTab, *lcos_error.LCOSError)
	SyncAutoVolumeDataToItemCodis(ctx utils.LCOSContext, region string, ruleId uint64) *lcos_error.LCOSError
}

type automatedVolume struct {
	automatedVolumeGenerationRuleDao automated_volume3.AutomatedVolumeGenerationRuleDao
	automatedVolumeGenerationDataDao automated_volume_generation_data.AutomatedVolumeGenerationDataDao
}

func NewAutomatedVolumeGenerationRule(automatedVolumeGenerationRuleDao automated_volume3.AutomatedVolumeGenerationRuleDao,
	automatedVolumeGenerationDataDao automated_volume_generation_data.AutomatedVolumeGenerationDataDao) *automatedVolume {
	return &automatedVolume{
		automatedVolumeGenerationRuleDao: automatedVolumeGenerationRuleDao,
		automatedVolumeGenerationDataDao: automatedVolumeGenerationDataDao,
	}
}

var _ AutomatedVolumeInterface = (*automatedVolume)(nil)

func (a *automatedVolume) CreateAutomatedVolume(ctx utils.LCOSContext, request *automated_volume2.CreateAutomatedVolumeRuleRequest) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	operator := ctx.GetUserEmail()
	excludedDays := automated_volume2.GetExcludedDaysStr(request.ExcludedDaysArray)
	groupBy := edd_constant.GroupByFChannel //F-channel维度统计单量
	channelType := int8(edd_constant.MChannel)
	if request.ChannelType != nil {
		channelType = *request.ChannelType
	}
	if channelType == edd_constant.FChannel {
		groupBy = edd_constant.GroupByLaneCode
	}
	model := &automated_volume3.AutomatedVolumeGenerationRuleTab{
		ProductID:                request.ProductID,
		GroupBy:                  int8(groupBy),
		ChannelType:              channelType,
		OriginLocationLevel:      *request.OriginLocationLevel,
		DestinationLocationLevel: *request.DestinationLocationLevel,
		TimePeriod:               *request.TimePeriod,
		Frequency:                *request.Frequency,
		ExcludedDays:             excludedDays,
		Region:                   region,
		Operator:                 operator,
	}
	if channelType == edd_constant.MChannel {
		fc := func() *lcos_error.LCOSError {
			lcosErr := a.automatedVolumeGenerationRuleDao.CreateAutomatedVolumeGenerationRule(ctx, model)
			if lcosErr != nil {
				return lcosErr
			}
			dataRequest := &data_ssc.TriggerVolumeRunJob{
				ProductID:                request.ProductID,
				AutomatedVolumeRuleID:    model.Id,
				OriginLocationLevel:      *request.OriginLocationLevel,
				DestinationLocationLevel: *request.DestinationLocationLevel,
				TimePeriod:               *request.TimePeriod,
				Frequency:                *request.Frequency,
				ExcludedDays:             excludedDays,
				Region:                   region,
				GroupBy:                  int8(groupBy),
			}
			_, lcosErr = data_ssc.TriggerVolumeRunJobTask(ctx, dataRequest)
			if lcosErr != nil {
				return lcosErr
			}
			return nil
		}
		return ctx.Transaction(fc)
	}
	//	f-channel 类型 不调用data接口只落db
	return a.automatedVolumeGenerationRuleDao.CreateAutomatedVolumeGenerationRule(ctx, model)
}

func (a *automatedVolume) ListAutomatedVolume(ctx utils.LCOSContext, request *automated_volume2.ListAutomatedVolumeRuleRequest) (interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())
	var pageNo uint32 = 1
	var Count uint32 = 10

	if request.PageNo != nil {
		pageNo = *request.PageNo
	}
	if request.Count != nil {
		Count = *request.Count
	}

	queryMap := map[string]interface{}{
		"region": region,
	}
	if request.ProductID != nil {
		queryMap["product_id"] = *request.ProductID
	}
	if request.RuleStatus != nil {
		queryMap["rule_status"] = *request.RuleStatus
	}

	models, total, lcosErr := a.automatedVolumeGenerationRuleDao.ListAllAutomatedVolumeGenerationRule(ctx, queryMap, pageNo, Count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	//转化数据库中的model 返回前端
	list := make([]*AutomatedVolumeGenerationRuleTabResponse, 0, len(models))
	for _, m := range models {
		item := &AutomatedVolumeGenerationRuleTabResponse{
			AutomatedVolumeGenerationRuleTab: *m,
			ExcludedDaysArray:                automated_volume_request.GetExcludedDaysArray(m.ExcludedDays),
		}
		list = append(list, item)
	}

	return map[string]interface{}{
		"list":   list,
		"pageno": pageNo,
		"count":  Count,
		"total":  total,
	}, nil
}

func (a *automatedVolume) GetAutomatedVolumeById(ctx utils.LCOSContext, request *automated_volume2.DetailAutomatedVolumeRuleRequest) (interface{}, *lcos_error.LCOSError) {
	models, lcosErr := a.automatedVolumeGenerationRuleDao.SearchAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"id": request.ID})

	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(models) <= 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find automated volume|id=%d", request.ID))
	}

	//转化数据库中的model 返回前端
	resp := &AutomatedVolumeGenerationRuleTabResponse{
		AutomatedVolumeGenerationRuleTab: *models[0],
		ExcludedDaysArray:                automated_volume_request.GetExcludedDaysArray(models[0].ExcludedDays),
	}
	return resp, nil
}

func (a *automatedVolume) parseDataFailAndImportDataForCodeZero(ctx utils.LCOSContext, fileUrl string, updateTime uint32, region string) *lcos_error.LCOSError {
	autoVolumeRuleIdMap := map[uint64]*automated_volume3.AutomatedVolumeGenerationRuleTab{} // 用于存储auto rule的id映射
	// data传输成功，需要
	// 1. 解析csv文件
	// 2. 更新对应单量统计规则的next_update_time,首次推送需要更新db数据
	// 3. 创建单量统计规则的version信息

	activeProductRuleMap := map[string]uint64{}
	activeRules, lcosErr := a.automatedVolumeGenerationRuleDao.SearchAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"rule_status": edd_constant.Active, "region": region})
	if lcosErr != nil {
		return lcosErr
	}
	for _, activeRule := range activeRules {
		if activeRuleId, ok := activeProductRuleMap[activeRule.ProductID]; !ok {
			activeProductRuleMap[activeRule.ProductID] = activeRule.Id
		} else {
			if activeRuleId != activeRule.Id {
				logger.CtxLogErrorf(ctx, "[parse auto volume data, get two active versions[%d]and[%d],for product_id[%s]", activeRuleId, activeRule.Id, activeRule.ProductID)
			}
		}
	}

	ruleVolumeDataMap, resultVersionMap, autoVolumeIDList, lcosErr := parseFileToStructData(ctx, fileUrl)
	if lcosErr != nil {
		return lcosErr
	}

	// 获取本次上传所对应的单量统计规则
	autoVolumeRules, lcosErr := a.automatedVolumeGenerationRuleDao.SearchAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"id in": autoVolumeIDList, "region": region})
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "[parse auto volume data, search db error]:%v", lcosErr.Msg)
		return lcosErr
	}

	// 将auto volume rule存储为map，方便更新的时候查询，并且校验这些单量统计规则的next update time都等于传入的update_time，否则需要报错
	for _, rule := range autoVolumeRules {
		if updateTime == 0 || (rule.IsValidUpdateTime(updateTime)) { // 对于首次失败的任务，其next update time可能已经更新
			//计算该更新的rule
			autoVolumeRuleIdMap[rule.Id] = rule
		} else {
			logger.CtxLogErrorf(ctx, "[auto volume data]the next update time for rule:[%v] is [%v] instead of [%v]", rule.Id, rule.NextUpdateTime, updateTime)
		}
	}

	// 对于满足更新时间的rule，循环resultMap创建数据AutoVolumeData数据
	for _autoVolumeRuleID, dataList := range ruleVolumeDataMap {
		if _rule, ok := autoVolumeRuleIdMap[_autoVolumeRuleID]; ok {
			lcosErr = a.automatedVolumeGenerationDataDao.CreateAutomatedVolumeGenerationData(ctx, dataList, _rule)
			logger.CtxLogInfof(ctx, "create auto volume update data length=%v", len(dataList))
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "create auto volume update data error|error=%v", lcosErr.Msg)
				return lcosErr
			}
		}
	}

	currentTimestamp := uint32(recorder.Now(ctx).Unix())
	// 更新对应的单量统计规则的next_update_time

	for _autoRuleID, _autoVolumeRule := range autoVolumeRuleIdMap {
		currentTime := pickup.GetCurrentTime(ctx, region)
		newDayTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1+int(_autoVolumeRule.Frequency), 0, 0, 0, 0, currentTime.Location())
		newDayTimestamp := uint32(newDayTime.Unix())
		// 首次更新会更新next update time 以及生效状态
		if updateTime != 0 {
			lcosErr = a.automatedVolumeGenerationRuleDao.UpdateAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"id": _autoRuleID, "region": region}, map[string]interface{}{"last_update_time": updateTime, "next_update_time": updateTime + _autoVolumeRule.Frequency*24*3600})
		} else {
			if _autoVolumeRule.RuleStatus == int8(edd_constant.Calculating) {
				lcosErr = a.automatedVolumeGenerationRuleDao.UpdateAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"id": _autoRuleID, "region": region}, map[string]interface{}{"rule_status": edd_constant.Active, "next_update_time": newDayTimestamp})
			}
		}
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "update auto volume update rule error|error=%v", lcosErr.Msg)
			return lcosErr
		}
	}

	needExpiredRuleIdList := []uint64{}
	// 循环创建需要的version信息
	var versionDatas []*automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab
	for autoRuleID, version := range resultVersionMap {
		if _autoRule, ok := autoVolumeRuleIdMap[autoRuleID]; ok {
			versionData := &automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab{
				AutomatedVolumeRuleID: autoRuleID,
				VolumeVersion:         version,
				EffectiveTime:         updateTime,
				ExpirationTime:        updateTime + _autoRule.Frequency*24*3600, // 以effective time+frequency*3600*24 作为expiration time
				IsFirstTime:           constant.FALSE,
				IsSuccess:             constant.TRUE,
			}
			// 首次更新
			currentTime := pickup.GetCurrentTime(ctx, region)
			newDayTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1+int(_autoRule.Frequency), 0, 0, 0, 0, currentTime.Location())
			newDayTimestamp := uint32(newDayTime.Unix())
			if updateTime == 0 {
				versionData.IsFirstTime = constant.TRUE
				versionData.EffectiveTime = currentTimestamp
				versionData.ExpirationTime = newDayTimestamp
			} else {
				versionData.IsFirstTime = constant.FALSE
			}
			versionDatas = append(versionDatas, versionData)

			//之前创建的生效的rule需要更新为过期的状态
			if lastRuleId, ok2 := activeProductRuleMap[_autoRule.ProductID]; ok2 {
				if lastRuleId != autoRuleID {
					needExpiredRuleIdList = append(needExpiredRuleIdList, lastRuleId)
				}
			}
		}
	}

	//data导入成功后，更新version表和 之前rule的状态
	fn := func() *lcos_error.LCOSError {
		if err := a.automatedVolumeGenerationDataDao.CreateAutomatedVolumeGenerationVersion(ctx, versionDatas); err != nil {
			logger.CtxLogErrorf(ctx, "create auto volume update data version error|error=%v", lcosErr.Msg)
			return err
		}
		if err := a.automatedVolumeGenerationRuleDao.UpdateAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"id in": needExpiredRuleIdList, "region": region}, map[string]interface{}{"rule_status": edd_constant.Expired}); err != nil {
			return err
		}
		return nil
	}
	return ctx.Transaction(fn)
}

func parseFileToStructData(ctx utils.LCOSContext, fileUrl string) (map[uint64][]*automated_volume_generation_data.AutomatedVolumeGenerationDataTab, map[uint64]uint32, []uint64, *lcos_error.LCOSError) {
	resultList, lcosErr := cdt_common2.ParseCSVFileLongTime(ctx, fileUrl)
	if lcosErr != nil {
		return nil, nil, nil, lcosErr
	}

	// 将csv中的内容传入数据库，见结果存ruleId->[]*AutomatedVolumeGenerationDataTab
	ruleVolumeDataMap := map[uint64][]*automated_volume_generation_data.AutomatedVolumeGenerationDataTab{}
	//ruleId->version
	resultVersionMap := map[uint64]uint32{}
	var autoVolumeIDList []uint64            // 用于推送的数据中的自动规则id
	autoVolumeRuleIDMap := map[uint64]bool{} // 用于去重

	for _, result := range resultList {
		//解析每一行数据 根据data 推送的数据来具体判断
		if len(result) < 10 {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "csv file has to get at least 10 columns")
		}
		autoVolumeRuleID, err := strconv.Atoi(result[0])
		if err != nil {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}

		maskedProductID := result[1]
		if maskedProductID == "" {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "masked_product_id cannot be empty")
		}

		fulfillmentProductID := result[2]
		if fulfillmentProductID == "" {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "fulfillment_product_id cannot be empty")
		}

		//laneCode := result[3]
		//if laneCode == "" {
		//	return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "lane_code cannot be empty")
		//}
		//lanecode本期不考虑 存储 git
		//laneCode := strings.ReplaceAll(strings.TrimSpace(result[9]), "/", "|") // 多段式LaneCode拼接格式有"/"和"|"两种，lcos统一存储为"|"格式

		originRegion := result[4]
		if originRegion == "" {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "origin region cannot be empty")
		}

		originLocationID, err := strconv.Atoi(result[5])
		if err != nil {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}

		destinationRegion := result[6]
		if destinationRegion == "" {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, "destination region cannot be empty")
		}

		destinationLocationID, err := strconv.Atoi(result[7])
		if err != nil {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}

		volumeVersion, err := strconv.Atoi(result[8])
		if err != nil {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}

		//id某些渠道月单量超过了亿
		volume, err := strconv.ParseUint(result[9], 10, 64)
		if err != nil {
			return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.ParseFileErrorCode, err.Error())
		}

		productVolumeData := &automated_volume_generation_data.AutomatedVolumeGenerationDataTab{
			AutomatedVolumeRuleID: uint64(autoVolumeRuleID),
			ProductID:             maskedProductID,
			FChannelID:            fulfillmentProductID,
			OriginRegion:          originRegion,
			OriginLocationID:      originLocationID,
			DestinationRegion:     destinationRegion,
			DestinationLocationID: destinationLocationID,
			Volume:                volume,
			VolumeVersion:         uint32(volumeVersion),
		}

		if _, ok := ruleVolumeDataMap[uint64(autoVolumeRuleID)]; !ok {
			ruleVolumeDataMap[uint64(autoVolumeRuleID)] = []*automated_volume_generation_data.AutomatedVolumeGenerationDataTab{}
		}
		ruleVolumeDataMap[uint64(autoVolumeRuleID)] = append(ruleVolumeDataMap[uint64(autoVolumeRuleID)], productVolumeData)

		// 检查当前的 autoVolumeRuleID 是否存在不同版本号
		if preVersion, ok := resultVersionMap[uint64(autoVolumeRuleID)]; !ok {
			resultVersionMap[uint64(autoVolumeRuleID)] = uint32(volumeVersion)
		} else {
			if preVersion != uint32(volumeVersion) {
				logger.CtxLogErrorf(ctx, "[auto volume data]auto volume rule id:[%v] has different versions", autoVolumeRuleID)
			}
		}

		// 去重单量统计规则id
		if _, ok := autoVolumeRuleIDMap[uint64(autoVolumeRuleID)]; !ok {
			autoVolumeRuleIDMap[uint64(autoVolumeRuleID)] = true
			autoVolumeIDList = append(autoVolumeIDList, uint64(autoVolumeRuleID))
		}
	}

	return ruleVolumeDataMap, resultVersionMap, autoVolumeIDList, nil
}

func (a *automatedVolume) parseDataFailAndImportDataForCodeNonZero(ctx utils.LCOSContext, updateTime uint32, region string, ruleId int) *lcos_error.LCOSError {
	// data传输失败，需要
	// 1. 获取next_update_time=updatetime||updatetime+update_time+3600*24的需要更新的单量规则
	// 2. 更新其next_update_time为update_time+3600*24
	// 3. 创建单量规则的version信息失败信息
	// 4. 是第一次失败 则更新auto rule status 为failed
	var automatedVolumeVersions []*automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab

	autoVolumeRules, lcosErr := a.automatedVolumeGenerationRuleDao.SearchAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"next_update_time": updateTime, "region": region, "id": ruleId})
	if lcosErr != nil {
		return lcosErr
	}
	logger.CtxLogInfof(ctx, "search volume rule db data is %s", autoVolumeRules)

	for _, autoVolumeRule := range autoVolumeRules {
		var isFirstTime uint8
		var expirationTime uint32 = 0
		if updateTime != 0 {
			isFirstTime = constant.FALSE
			expirationTime = updateTime + autoVolumeRule.Frequency*3600*24
			lcosErr = a.automatedVolumeGenerationRuleDao.UpdateAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"id": autoVolumeRule.Id, "region": region}, map[string]interface{}{"next_update_time": expirationTime})
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "update auto volume update rule error|error=%v", lcosErr.Msg)
				return lcosErr
			}

		} else {
			isFirstTime = constant.TRUE
			lcosErr = a.automatedVolumeGenerationRuleDao.UpdateAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"id": autoVolumeRule.Id, "region": region}, map[string]interface{}{"rule_status": edd_constant.Expired})
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "update auto volume update rule error|error=%v", lcosErr.Msg)
				return lcosErr
			}
		}

		automatedVolumeVersions = append(automatedVolumeVersions, &automated_volume_generation_data.AutomatedVolumeGenerationDataVersionTab{
			AutomatedVolumeRuleID: autoVolumeRule.Id,
			VolumeVersion:         0,
			EffectiveTime:         updateTime,
			ExpirationTime:        expirationTime,
			IsFirstTime:           isFirstTime,
			IsSuccess:             constant.FALSE,
		})
	}

	lcosErr = a.automatedVolumeGenerationDataDao.CreateAutomatedVolumeGenerationVersion(ctx, automatedVolumeVersions)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "create automated volume data version error|error=%v", lcosErr.Msg)
		return lcosErr
	}
	return nil
}

func (a *automatedVolume) ParseDataFileAndImportData(ctx utils.LCOSContext, request *automated_volume2.ImportAutomatedVolumeRuleRequest) *lcos_error.LCOSError {
	fileUrl := request.FileUrl
	updateTime := request.UpdateTime
	code := request.Code
	region := request.Region
	ruleId := request.RuleId
	logger.CtxLogInfof(ctx, "import volume, code is %d, rule_id is %d , region is %s,update_time is %d", code, ruleId, region, updateTime)

	// 对于需要在update_time更新的数据，当前时间必须要在【updateTime-3600*24，updateTime】之间  updateTime 是该任务应该更新的时间，
	nowTimestamp := utils.GetTimestamp(ctx)
	if updateTime != 0 && (nowTimestamp < updateTime-3600*24 || nowTimestamp > updateTime) {
		errMsg := fmt.Sprintf("not allowed to update volume [%v] data at [%v]", updateTime, nowTimestamp)
		logger.CtxLogErrorf(ctx, errMsg)
		_ = metrics.CounterIncr(constant.MetricVolumeDataFailureNotify, map[string]string{"rule_id": strconv.Itoa(ruleId), "file_url": fileUrl, "update_time": strconv.Itoa(int(updateTime)), "region": region, "code": strconv.Itoa(code), "error": errMsg})
		return lcos_error.NewLCOSError(lcos_error.NotAllowedToUpdateDataTimeErrorCode, errMsg)
	}

	if code == 0 {
		lcosErr := a.parseDataFailAndImportDataForCodeZero(ctx, fileUrl, updateTime, region)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "parse csv file error|error=%v", lcosErr.Msg)
			_ = metrics.CounterIncr(constant.MetricVolumeDataFailureNotify, map[string]string{"rule_id": strconv.Itoa(ruleId), "file_url": fileUrl, "update_time": strconv.Itoa(int(updateTime)), "region": region, "code": strconv.Itoa(code), "error": lcosErr.Msg})
			return lcosErr
		}
	} else {
		//在推送周期内 推送失败数据
		lcosErr := a.parseDataFailAndImportDataForCodeNonZero(ctx, updateTime, region, ruleId)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, lcosErr.Msg)
			_ = metrics.CounterIncr(constant.MetricVolumeDataFailureNotify, map[string]string{"rule_id": strconv.Itoa(ruleId), "file_url": fileUrl, "update_time": strconv.Itoa(int(updateTime)), "region": region, "code": strconv.Itoa(code), "error": lcosErr.Msg})
			return lcosErr
		} else {
			_ = metrics.CounterIncr(constant.MetricVolumeDataFailureNotify, map[string]string{"rule_id": strconv.Itoa(ruleId), "file_url": fileUrl, "update_time": strconv.Itoa(int(updateTime)), "region": region, "code": strconv.Itoa(code), "error": "DATA calculate failed"})
		}
	}
	return nil

}

func (a *automatedVolume) DeleteAutomatedVolumeData(ctx utils.LCOSContext, region string, batchNum, expiredPeriodNum int) *lcos_error.LCOSError {
	// 1. find all update rules that are active or disabled
	autoVolumeRules, lcosErr := a.automatedVolumeGenerationRuleDao.SearchAutomatedVolumeGenerationRule(ctx, map[string]interface{}{"rule_status in": []uint8{edd_constant.Active, edd_constant.Expired}})
	if lcosErr != nil {
		return lcosErr
	}

	// 2. find expired volume versions of each auto volume rule
	for _, autoVolumeRule := range autoVolumeRules {
		str, _ := jsoniter.MarshalToString(autoVolumeRule)
		logger.CtxLogInfof(ctx, "ready to clean auto volume rule|id=%d|rule detail:%s", autoVolumeRule.Id, str)

		// 找到最小ID
		minData, lcosErr := a.automatedVolumeGenerationDataDao.GetVolumeInfoByQuery(ctx, autoVolumeRule.Id, map[string]interface{}{}, 1)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "%v", lcosErr)
			continue
		}
		if len(minData) != 1 {
			logger.CtxLogErrorf(ctx, "get data limit 1 failed, data len:%v", len(minData))
			continue
		}
		// 取前batchNum条数据
		volumeGenerationDataTabs, lcosErr := a.automatedVolumeGenerationDataDao.GetVolumeInfoByQuery(ctx, autoVolumeRule.Id, map[string]interface{}{"id >=": minData[0].ID}, batchNum)

		// 处理因为主从同步延迟导致的版本误删除操作
		allVolumeVersion, lcosErr := a.automatedVolumeGenerationDataDao.GetAllVolumeVersionByAutoVolumeRuleIDDesc(ctx, autoVolumeRule.Id)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "%v", lcosErr)
			continue
		}
		// 根据前batchNum条数据判断，删除所有版本不存在但是在Data表存在的数据
		var noVersionDataIds []uint64
		volumeVersionMap := make(map[uint32]struct{})
		for _, v := range allVolumeVersion {
			volumeVersionMap[v] = struct{}{}
		}
		for _, volumeGenerationDataTab := range volumeGenerationDataTabs {
			_, exists := volumeVersionMap[volumeGenerationDataTab.VolumeVersion]
			if !exists {
				noVersionDataIds = append(noVersionDataIds, volumeGenerationDataTab.ID)
			}
		}
		if len(noVersionDataIds) > 0 {
			_, lcosErr = a.automatedVolumeGenerationDataDao.DeleteVolumeInfoByQuery(ctx, autoVolumeRule.Id, map[string]interface{}{"id in": noVersionDataIds}, batchNum)
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "delete volume data failed, casued by %v", lcosErr)
				continue
			}
			break
		}

		//查找version表中 某条rule对应的所有过期version
		expiredVersions, lcosErr := a.automatedVolumeGenerationDataDao.GetAllExpiredVersions(ctx, autoVolumeRule.Id, int(autoVolumeRule.Frequency), expiredPeriodNum)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "get all expired versions failed, casued by %v", lcosErr)
			continue
		}
		if len(expiredVersions) <= 0 {
			continue
		}
		logger.CtxLogInfof(ctx, "successfully get all expired versions|auto_volume_id=%d, expired_version=[%v]", autoVolumeRule.Id, expiredVersions)

		// 在内存处理数据,先构建Map
		volumeMap := make(map[uint32][]uint64)
		for _, volumeGenerationDataTab := range volumeGenerationDataTabs {
			volumeVersion := volumeGenerationDataTab.VolumeVersion
			id := volumeGenerationDataTab.ID
			volumeMap[volumeVersion] = append(volumeMap[volumeVersion], id)
		}
		var expireIds []uint64
		for _, expiredVersion := range expiredVersions {
			// 获取过期的数据Id
			expireIds = append(expireIds, volumeMap[expiredVersion]...)
		}
		rowsAffected, lcosErr := a.automatedVolumeGenerationDataDao.DeleteVolumeInfoByQuery(ctx, autoVolumeRule.Id, map[string]interface{}{"id in": expireIds}, batchNum)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "delete volume data failed, casued by %v", lcosErr)
			continue
		}
		logger.CtxLogInfof(ctx, "successfully delete expired volume data |auto_volume_id=%d, delete rows", autoVolumeRule.Id, rowsAffected)
		if rowsAffected == 0 {
			lcosErr = a.automatedVolumeGenerationDataDao.DeleteVolumeVersion(ctx, map[string]interface{}{"automated_volume_rule_id": autoVolumeRule.Id, "volume_version in": expiredVersions})
			if lcosErr != nil {
				logger.CtxLogErrorf(ctx, "delete volume version failed, casued by %v", lcosErr)
				return lcosErr
			}
			logger.CtxLogInfof(ctx, "successfully delete expire versions[%v]", expiredVersions)
		}
		break
	}
	return nil
}

func (a *automatedVolume) GetVolumeVersionByAutoVolumeRuleID(ctx utils.LCOSContext, ruleId uint64) (uint32, *lcos_error.LCOSError) {
	return a.automatedVolumeGenerationDataDao.GetVolumeVersionByAutoVolumeRuleID(ctx, ruleId)
}

func (a *automatedVolume) GetVolumeDataByVolumeVersion(ctx utils.LCOSContext, ruleId uint64, volumeVersion uint32) ([]*automated_volume_generation_data.AutomatedVolumeGenerationDataTab, *lcos_error.LCOSError) {
	return a.automatedVolumeGenerationDataDao.GetVolumeInfoByVolumeVersion(ctx, ruleId, map[string]interface{}{"volume_version": volumeVersion, "automated_volume_rule_id": ruleId})
}

func (a *automatedVolume) SyncAutoVolumeDataToItemCodis(ctx utils.LCOSContext, region string, ruleId uint64) *lcos_error.LCOSError {
	startTime := utils.GetTimestamp(ctx)
	taskName := fmt.Sprintf("sync_item_volume:auto:%s:%s:%d", utils.FormatTimestamp(startTime, constant.DateAndTimeFormat), region, ruleId)

	// 1. 获取需要同步的单量自动更新规则列表
	var rules []*automated_volume3.AutomatedVolumeGenerationRuleTab
	var err *lcos_error.LCOSError
	if ruleId != 0 {
		rules, err = a.automatedVolumeGenerationRuleDao.SearchAutomatedVolumeGenerationRule(ctx, map[string]interface{}{
			"id": ruleId,
		})
	} else {
		queryMap := map[string]interface{}{
			"rule_status in": []uint8{edd_constant.UpComing, edd_constant.Active},
		}
		if region != "" {
			queryMap["region"] = region
		}
		rules, err = a.automatedVolumeGenerationRuleDao.SearchAutomatedVolumeGenerationRule(ctx, queryMap)
	}
	if err != nil {
		_ = monitor.AwesomeReportEvent(ctx, taskName, "failed", constant.StatusError, err.Msg)

		message := fmt.Sprintf(
			item_card_edt_constant.SyncCdtAutoVolumeDataFailedMessage,
			utils.GetEnv(ctx),
			region,
			ruleId,
			utils.FormatTimestamp(startTime, constant.DateAndTimeFormat),
			utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
			err.Msg,
		)
		cfg := config.GetMutableConf(ctx).ItemCardSceneConfig.SeatalkNotifyConfig
		_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, true)
		return err
	}

	// 2. 遍历同步 单量自动更新规则 当前正在生效的版本数据
	extraSyncCodisList := config.GetExtraCdtSyncCodisList(ctx)
	var failedRuleIdList, successRuleIdList []string
	var errList []*lcos_error.LCOSError
	var total int
	for _, rule := range rules {
		logger.CtxLogInfof(ctx, "ready to sync auto volume data to item codis|rule_id=%d", rule.Id)

		// 2.1 获取自动更新规则当前正在生效的cdt version
		volumeVersion, err := a.GetVolumeVersionByAutoVolumeRuleID(ctx, rule.Id)
		if err != nil {
			logger.CtxLogErrorf(ctx, "sync cdt auto volume data failed, get volume version error|rule_id=%d, cause=%s", rule.Id, err.Msg)
			failedRuleIdList = append(failedRuleIdList, strconv.Itoa(int(rule.Id)))
			errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncItemCdtError, "sync cdt auto volume data failed, get volume version error|rule_id=%d, cause=%s", rule.Id, err.Msg))
			_ = monitor.AwesomeReportEvent(ctx, taskName, strconv.Itoa(int(rule.Id)), constant.StatusError, err.Msg)
			continue
		}

		// 2.2 获取单量自动更新规则版本数据。
		dataList, err := a.GetVolumeDataByVolumeVersion(ctx, rule.Id, volumeVersion)
		if err != nil {
			logger.CtxLogErrorf(ctx, "sync cdt auto volume data failed, get volume data error|rule_id=%d, volume_version=%d, cause=%s", rule.Id, volumeVersion, err.Msg)
			failedRuleIdList = append(failedRuleIdList, strconv.Itoa(int(rule.Id)))
			errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncItemCdtError, "sync cdt auto volume data failed, get volume data error|rule_id=%d, cause=%s", rule.Id, err.Msg))
			_ = monitor.AwesomeReportEvent(ctx, taskName, strconv.Itoa(int(rule.Id)), constant.StatusError, err.Msg)
			continue
		}

		// 2.3 分批将单量自动更新规则数据同步到codis
		success, err := a.automatedVolumeGenerationDataDao.SyncAutoVolumeDataToItemCodisInDB(ctx, rule, dataList, extraSyncCodisList)
		if err != nil {
			logger.CtxLogErrorf(ctx, "sync cdt auto volume data failed, sync volume data error|rule_id=%d, volume_version=%d, success=%d, cause=%s", rule.Id, volumeVersion, success, err.Msg)
			failedRuleIdList = append(failedRuleIdList, strconv.Itoa(int(rule.Id)))
			errList = append(errList, lcos_error.NewLCOSErrorf(lcos_error.SyncItemCdtError, "sync cdt auto volume data failed, sync volume data error|rule_id=%d, volume_version=%d, success=%d, cause=%s", rule.Id, volumeVersion, success, err.Msg))
			_ = monitor.AwesomeReportEvent(ctx, taskName, strconv.Itoa(int(rule.Id)), constant.StatusError, err.Msg)
			continue
		}
		_ = monitor.AwesomeReportEvent(ctx, taskName, strconv.Itoa(int(rule.Id)), constant.StatusSuccess, strconv.Itoa(success))
		_ = metrics.GaugeSet(constant.MetricsSyncItemVolumeDataReport, float64(success), map[string]string{
			"rule_id":     strconv.FormatUint(rule.Id, 10),
			"product_id":  rule.ProductID,
			"volume_type": item_card_edt_constant.MetricLabelVolumeTypeAutoUpdate,
		})

		successRuleIdList = append(successRuleIdList, strconv.Itoa(int(rule.Id)))
		total += success
	}
	_ = monitor.AwesomeReportEvent(ctx, taskName, "done", constant.StatusSuccess, strconv.Itoa(total))

	message := fmt.Sprintf(
		item_card_edt_constant.SyncCdtAutoVolumeDataNotifyMessage,
		utils.GetEnv(ctx),
		region,
		ruleId,
		utils.FormatTimestamp(startTime, constant.DateAndTimeFormat),
		utils.FormatTimestamp(utils.GetTimestamp(ctx), constant.DateAndTimeFormat),
		len(rules),
		strings.Join(successRuleIdList, ","),
		strings.Join(failedRuleIdList, ","),
		total,
		strings.Join(extraSyncCodisList, ","),
	)
	cfg := config.GetMutableConf(ctx).ItemCardSceneConfig.SeatalkNotifyConfig
	var atAll bool
	if len(failedRuleIdList) != 0 {
		atAll = true
	}
	_ = seatalk.NotifyWithTextMessage(ctx, cfg.Webhook, message, nil, atAll)

	if len(errList) != 0 {
		errMsg := lcos_error.GetMessageFromListWithSep(errList, "\n")
		return lcos_error.NewLCOSError(lcos_error.SyncItemVolumeError, errMsg)
	}
	return nil
}
