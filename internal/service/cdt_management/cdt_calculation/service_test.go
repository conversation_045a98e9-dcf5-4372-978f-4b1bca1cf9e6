package auto_update_rule

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis/handler"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
)

func Test_cdtCalculationService_getQuery(t *testing.T) {
	//t.Skip()

	config.MutableConf = &cf.MutableConfig{
		MaxLoopConfig: cf.MaxLoopConfig{
			MaxLoopSizeForLocationLevel: 10,
		},
	}

	type args struct {
		ctx                      utils.LCOSContext
		productID                string
		isSiteLine               uint8
		isLM                     uint8
		isCB                     uint8
		tplUniqueKey             string
		sellerAddr               *cdt_calculation.AddressInfo
		buyerAddr                *cdt_calculation.AddressInfo
		region                   string
		originLocationLevel      int
		destinationLocationLevel int
		fullQuery                bool
		updateEvent              uint8
		laneCodeFlag             bool
		laneCode                 string
	}

	tests := []struct {
		name  string
		args  args
		want  int
		want1 *lcos_error.LCOSError
	}{

		// local product
		{
			name: "test auto update rule with origin location level city, destination state level city, with postcode empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.State),
				fullQuery:                false,
			},
			want:  6,
			want1: nil,
		},
		{
			name: "test auto update rule with origin location level city, destination city level city, with postcode empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.City),
				fullQuery:                false,
			},
			want:  9,
			want1: nil,
		},
		{
			name: "test auto update rule with origin location level city, destination postcode level district, with postcode empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.CDTPostcode),
				fullQuery:                false,
			},
			want:  16,
			want1: nil,
		},
		{
			name: "test auto update rule with origin location level city, destination postcode level district, with postcode not empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.CDTPostcode),
				fullQuery:                false,
			},
			want:  20,
			want1: nil,
		},
		{
			name: "test auto update rule with origin location level city, destination location level district, with postcode empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.District),
				fullQuery:                false,
			},
			want:  12,
			want1: nil,
		},
		{
			name: "test auto update rule with origin location level city, destination location level district",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.District),
				fullQuery:                false,
			},
			want:  12,
			want1: nil,
		},
		{
			name: "test auto update rule with origin location level district, destination location level district",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.District),
				destinationLocationLevel: int(constant.District),
				fullQuery:                false,
			},
			want:  16,
			want1: nil,
		},
		{
			name: "test auto update rule with origin location level 2, destination location level 9",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.District),
				destinationLocationLevel: int(constant.CDTPostcode),
				fullQuery:                false,
			},
			want:  20,
			want1: nil,
		},
		{
			name: "test auto update rule with origin location level 2, destination location level 9",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.District),
				destinationLocationLevel: int(constant.CepRange),
				fullQuery:                false,
			},
			want:  20,
			want1: nil,
		},
		{
			name: "test manual update rule with origin location level 0, destination location level 0, and postcode is not empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      0,
				destinationLocationLevel: 0,
				fullQuery:                true,
			},
			want:  20,
			want1: nil,
		},
		{
			name: "test manual update rule with origin location level 0, destination location level 0, and postcode is empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         0,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				originLocationLevel:      0,
				destinationLocationLevel: 0,
				fullQuery:                true,
			},
			want:  16,
			want1: nil,
		},

		// cb product
		{
			name: "test cb auto update rule with origin location level city, destination postcode level city, with postcode empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.City),
				fullQuery:                false,
			},
			want:  3,
			want1: nil,
		},
		{
			name: "test cb auto update rule with origin location level city, destination postcode level district, with postcode empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				originLocationLevel:      int(constant.Country),
				destinationLocationLevel: int(constant.CDTPostcode),
				fullQuery:                false,
			},
			want:  4,
			want1: nil,
		},
		{
			name: "test cb auto update rule with origin location level city, destination postcode level district, with postcode not empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.CDTPostcode),
				fullQuery:                false,
			},
			want:  5,
			want1: nil,
		},
		{
			name: "test cb auto update rule with origin location level city, destination location level district, with postcode empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString(""),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.District),
				fullQuery:                false,
			},
			want:  4,
			want1: nil,
		},
		{
			name: "test cb auto update rule with origin location level city, destination location level district",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.City),
				destinationLocationLevel: int(constant.District),
				fullQuery:                false,
			},
			want:  4,
			want1: nil,
		},
		{
			name: "test cb auto update rule with origin location level district, destination location level district",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.Country),
				destinationLocationLevel: int(constant.District),
				fullQuery:                false,
			},
			want:  4,
			want1: nil,
		},
		{
			name: "test cb auto update rule with origin location level district, destination location level postcode",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.District),
				destinationLocationLevel: int(constant.CDTPostcode),
				fullQuery:                false,
			},
			want:  5,
			want1: nil,
		},
		{
			name: "test cb auto update rule with origin location level district, destination location level postcode",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      int(constant.District),
				destinationLocationLevel: int(constant.CepRange),
				fullQuery:                false,
			},
			want:  5,
			want1: nil,
		},
		{
			name: "test cb manual update rule with origin location level 0, destination location level 0, and postcode is not empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
					PostalCode:         utils.NewString("test"),
				},
				originLocationLevel:      0,
				destinationLocationLevel: 0,
				fullQuery:                true,
			},
			want:  5,
			want1: nil,
		},
		{
			name: "test cb manual update rule with origin location level 0, destination location level 0, and postcode is empty",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				originLocationLevel:      0,
				destinationLocationLevel: 0,
				fullQuery:                true,
			},
			want:  4,
			want1: nil,
		},

		// SPLN-28302 add cb product with some update events could have origin location
		{
			name: "cb product, update event is destination inbound, lane code is enabled, location level is state",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				originLocationLevel:      0,
				destinationLocationLevel: 0,
				fullQuery:                false,
				laneCode:                 "test-lanecode",
				laneCodeFlag:             true,
				updateEvent:              edd_constant.DestinationInbound,
			},
			want:  4,
			want1: nil,
		},
		{
			name: "cb product, update event is lm hub inbound, lane code is enabled, location level is district",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				originLocationLevel:      2,
				destinationLocationLevel: 2,
				fullQuery:                false,
				laneCode:                 "test-lanecode",
				laneCodeFlag:             true,
				updateEvent:              edd_constant.LMHubInbound,
			},
			want:  16,
			want1: nil,
		},
		{
			name: "cb product, update event is tws outbound, lane code is enabled, location level is district",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				originLocationLevel:      2,
				destinationLocationLevel: 2,
				fullQuery:                false,
				laneCode:                 "test-lanecode",
				laneCodeFlag:             true,
				updateEvent:              edd_constant.TWSOutbound,
			},
			want:  4,
			want1: nil,
		},
		{
			name: "cb product, update event is tws inbound, lane code is enabled",
			args: args{ctx: utils.NewCommonCtx(context.Background()),
				productID:    "testProduct",
				isSiteLine:   1,
				isLM:         0,
				isCB:         1,
				tplUniqueKey: "test unique key",
				region:       "SG",
				buyerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				sellerAddr: &cdt_calculation.AddressInfo{
					StateLocationId:    utils.NewInt(1),
					CityLocationId:     utils.NewInt(2),
					DistrictLocationId: utils.NewInt(3),
				},
				originLocationLevel:      0,
				destinationLocationLevel: 0,
				fullQuery:                false,
				laneCode:                 "test-lanecode",
				laneCodeFlag:             true,
				updateEvent:              edd_constant.TWSInbound,
			},
			want:  2,
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CdtCalculationService{}
			got, got1 := c.getQuery(tt.args.ctx, edd_constant.CdtObject, tt.args.productID, tt.args.laneCode, tt.args.laneCodeFlag, tt.args.updateEvent, tt.args.isSiteLine, tt.args.isLM, tt.args.isCB, tt.args.tplUniqueKey, tt.args.sellerAddr, tt.args.buyerAddr, tt.args.region, tt.args.originLocationLevel, tt.args.destinationLocationLevel, tt.args.fullQuery, constant.CdtQueryDataSourceDB, false)
			if !reflect.DeepEqual(len(got), tt.want) {
				t.Errorf("getQuery() got = %v, want %v", len(got), tt.want)
			}
			t.Logf("getQuery() got = %v, want %v", len(got), tt.want)
			gotStr, _ := jsoniter.MarshalToString(got)
			t.Logf("getQuery() got = %v", gotStr)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("getQuery() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_cdtCalculationService_GetEDDInfo(t *testing.T) {
	t.Skip()
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "br")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	_, err := cf.InitMutableConfig(ctx)
	if err != nil {
		t.Fatal(err)
	}
	cfg, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatal(err)
	}
	err = startup.InitLibs(cfg)

	if err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}

	if err := startup.InitLayeredCache(cfg); err != nil {
		t.Fatalf("init layered cache error:%v", err)
	}

	autoUpdateDao := auto_update_rule.NewCdtAutoUpdateRuleDao()
	laneCdtAutoUpdateDataDao := lane_auto_update_data.NewLaneCdtAutoUpdateDataDao()
	laneCdtManualUpdateRuleDao := lane_manual_update_rule.NewLaneCdtManualUpdateRuleDao()
	laneCdtManualManipulationRuleDao := lane_manual_manipulation_rule.NewLaneCdtManualManipulationRuleDao()
	autoUpdateDataDao := auto_update_data.NewCdtAutoUpdateDataDao(laneCdtAutoUpdateDataDao)
	manualUpdateDao := manual_update_rule.NewCdtManualUpdateRuleDao(laneCdtManualUpdateRuleDao)
	cdtManualManipulationRuleDao := manual_manipulation_rule.NewCdtManualManipulationRuleDao(laneCdtManualManipulationRuleDao)
	slsHolidayDao := sls_holiday.NewSlsHolidayDao()
	lpsHolidayDao := lps_holiday.NewLPSHolidayDAO()
	tplLineIDRefDao := tpl_id_line_id_ref.NewTPLIDLineIDRefDAO()
	eddHistoryDao := edd_history.NewEddHistoryDao()

	cdtCalculationService := NewCdtCalculationService(autoUpdateDao, autoUpdateDataDao, manualUpdateDao, cdtManualManipulationRuleDao, slsHolidayDao, lpsHolidayDao, tplLineIDRefDao, eddHistoryDao, nil, nil, nil)

	// init cache
	dumpManager := cache.DumpManager{}
	cacheConfigs := []*localcache.Conf{
		//localcache.NewConf(constant.CDTManualUpdateLocationDataTabNamespace, dumpManager.DumpCDTManualUpdateLocationData, nil, &manual_update_rule.CdtManualUpdateLocationDataTab{}, 0),
		localcache.NewConf(constant.CDTManualUpdatePostcodeDataTabNamespace, dumpManager.DumpCDTManualUpdatePostcodeData, nil, &manual_update_rule.CdtManualUpdatePostcodeDataTab{}, 0),
		localcache.NewConf(constant.CDTManualUpdateRecordTabNamespace, dumpManager.DumpCDTManualUpdateRecordData, nil, &manual_update_rule.CdtManualUpdateRecordTab{}, 0),

		localcache.NewConf(constant.CDTManualManipulationLocationRuleTabNamespace, dumpManager.DumpCDTManualManipulationLocationRuleData, nil, &manual_manipulation_rule.CDTManualManipulationLocationRuleTab{}, 0),
		localcache.NewConf(constant.CDTManualManipulationPostcodeRuleTabNamespace, dumpManager.DumpCDTManualManipulationPostcodeRuleData, nil, &manual_manipulation_rule.CDTManualManipulationPostcodeRuleTab{}, 0),
		localcache.NewConf(constant.CDTManualManipulationZipcodeRuleTabNamespace, dumpManager.DumpCDTManualManipulationZipcodeRuleData, nil, &manual_manipulation_rule.CDTManualManipulationZipcodeRuleTab{}, 0),
		localcache.NewConf(constant.CDTManualManipulationRuleRecordTabNamespace, dumpManager.DumpCDTManualManipulationRuleRecordData, nil, &manual_manipulation_record.CDTManualManipulationRuleRecordTab{}, 0),

		localcache.NewConf(constant.CDTAutoUpdateRuleTabNamespace, dumpManager.DumpAutoUpdateRuleData, nil, &auto_update_rule.CDTAutoUpdateRuleTab{}, 0),
		localcache.NewConf(constant.CDTAutoUpdateCalculateDataVersionTabNamespace, dumpManager.DumpAutoUpdateCalculateDataVersion, nil, &auto_update_data.CDTAutoUpdateCalculateDataVersionTab{}, 0),

		// period task
		localcache.NewConf(constant.SlsHolidayNamespace, dumpManager.DumpSlsHoliday, nil, &sls_holiday.HolidayTab{}, 0),
		localcache.NewConf(constant.SlsRecurringHolidayNamespace, dumpManager.DumpSlsRecurringHoliday, nil, &sls_holiday.RecurringHolidayTab{}, 0),
		localcache.NewConf(constant.LPSHolidayNamespace, dumpManager.DumpLPSHoliday, nil, &lps_holiday.ProductHolidayTab{}, 0),
		localcache.NewConf(constant.LPSRecurringHolidayNamespace, dumpManager.DumpLPSRecurringHoliday, nil, &lps_holiday.ProductRecurringHolidayTab{}, 0),
	}

	if err := localcache.InitCacheManager(time.Hour, cacheConfigs...); err != nil {
		t.Fatal(err)
	}
	if err := localcache.LoadLocalCache(); err != nil {
		t.Fatal(err)
	}

	eventTime := 1669086474
	cdtMaxForLaneCode10066 := 0.2176
	cdtMaxFor10066 := 3.9437
	cdtMaxForLaneCode20077 := 0.0468
	cdtMaxForLaneCode20077WithPenang := 0.0785

	type args struct {
		ctx          utils.LCOSContext
		productsInfo *cdt_calculation.CdtProductInfo
		eventTime    int64
	}
	tests := []struct {
		name  string
		args  args
		want  int64
		want2 *lcos_error.LCOSError
	}{
		{
			name: "lane product 91003 with cep range",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				productsInfo: &cdt_calculation.CdtProductInfo{
					QueryID:    "1",
					ProductID:  "91003",
					IsCB:       0,
					IsSiteLine: 1,
					Region:     "BR",
					SellerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(0),
						CityLocationId:     utils.NewInt(0),
						DistrictLocationId: utils.NewInt(0),
					},
					BuyerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(0),
						CityLocationId:     utils.NewInt(0),
						DistrictLocationId: utils.NewInt(0),
						PostalCode:         utils.NewString("22"),
					},
					SkuInfo:  nil,
					LaneCode: "CF-CN-146|CL-BR-1421633",
					LineList: nil,
				},
				eventTime: int64(eventTime),
			},
			want:  int64(eventTime + 2*24*3600),
			want2: nil,
		},
		{
			name: "lane product 10066 with postcode",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				productsInfo: &cdt_calculation.CdtProductInfo{
					QueryID:    "1",
					ProductID:  "10066",
					IsCB:       0,
					IsSiteLine: 1,
					Region:     "SG",
					SellerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(4007553),
						CityLocationId:     utils.NewInt(4007554),
						DistrictLocationId: utils.NewInt(4007555),
					},
					BuyerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(4007553),
						CityLocationId:     utils.NewInt(4007554),
						DistrictLocationId: utils.NewInt(4007555),
						PostalCode:         utils.NewString("387391"),
					},
					SkuInfo:  nil,
					LaneCode: "L-SG-7",
					LineList: nil,
				},
				eventTime: int64(eventTime),
			},
			want:  int64(eventTime) + int64(float64(cdtMaxForLaneCode10066)*24*3600),
			want2: nil,
		},
		{
			name: "product 10066 with postcode",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				productsInfo: &cdt_calculation.CdtProductInfo{
					QueryID:    "1",
					ProductID:  "10066",
					IsCB:       0,
					IsSiteLine: 1,
					Region:     "SG",
					SellerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(4007553),
						CityLocationId:     utils.NewInt(4007554),
						DistrictLocationId: utils.NewInt(4007555),
					},
					BuyerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(4007553),
						CityLocationId:     utils.NewInt(4007554),
						DistrictLocationId: utils.NewInt(4007555),
						PostalCode:         utils.NewString("387391"),
					},
					SkuInfo:  nil,
					LaneCode: "",
					LineList: nil,
				},
				eventTime: int64(eventTime),
			},
			want:  int64(eventTime) + int64(float64(cdtMaxFor10066)*24*3600),
			want2: nil,
		},
		{
			name: "product 20077 with lane auto update rule and lane manual manipulation",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				productsInfo: &cdt_calculation.CdtProductInfo{
					QueryID:    "1",
					ProductID:  "20077",
					IsCB:       0,
					IsSiteLine: 1,
					Region:     "MY",
					SellerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(1000149),
						CityLocationId:     utils.NewInt(1000191),
						DistrictLocationId: utils.NewInt(1001986),
					},
					BuyerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(1000149),
						CityLocationId:     utils.NewInt(1000191),
						DistrictLocationId: utils.NewInt(1001986),
						PostalCode:         utils.NewString("91000"),
					},
					SkuInfo:  nil,
					LaneCode: "L-MY-9",
					LineList: nil,
				},
				eventTime: int64(eventTime),
			},
			want:  int64(eventTime) + int64(float64(cdtMaxForLaneCode20077)*24*3600) + 2*24*3600,
			want2: nil,
		},
		{
			name: "product 20077 with lane auto update rule and lane route manual manipulation",
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				productsInfo: &cdt_calculation.CdtProductInfo{
					QueryID:    "1",
					ProductID:  "20077",
					IsCB:       0,
					IsSiteLine: 1,
					Region:     "MY",
					SellerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(1000250),
						CityLocationId:     utils.NewInt(1000253),
						DistrictLocationId: utils.NewInt(0),
					},
					BuyerAddr: &cdt_calculation.AddressInfo{
						StateLocationId:    utils.NewInt(1000250),
						CityLocationId:     utils.NewInt(1000253),
						DistrictLocationId: utils.NewInt(0),
						PostalCode:         utils.NewString("91000"),
					},
					SkuInfo:  nil,
					LaneCode: "L-MY-9",
					LineList: nil,
				},
				eventTime: int64(eventTime),
			},
			want:  int64(eventTime) + int64(float64(cdtMaxForLaneCode20077WithPenang)*24*3600) + 4*24*3600,
			want2: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _, _, got2 := cdtCalculationService.GetEDDInfo(tt.args.ctx, tt.args.productsInfo, 0, tt.args.eventTime)
			if got != tt.want {
				t.Errorf("GetEDDInfo() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("GetEDDInfo() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}

func Test_cdtCalculationService_GetAllCdtHolidays(t *testing.T) {
	t.Skip()
	ctx := context.Background()
	_ = os.Setenv("CHASSIS_HOME", pathutil.GetProjectAbsolutePath())
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = os.Setenv("LOCAL_UNIQ_NAME", "-lei")
	_ = os.Setenv("ENV", "test")
	_ = os.Setenv("CID", "my")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))

	if err := chassis.Init(); err != nil {
		t.Fatalf("Init failed." + err.Error())
		return
	}

	_, err := cf.InitMutableConfig(ctx)
	if err != nil {
		t.Fatal(err)
	}
	cfg, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatal(err)
	}
	err = startup.InitLibs(cfg)

	if err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}

	if err := startup.InitLayeredCache(cfg); err != nil {
		t.Fatalf("init layered cache error:%v", err)
	}

	autoUpdateDao := auto_update_rule.NewCdtAutoUpdateRuleDao()
	laneCdtAutoUpdateDataDao := lane_auto_update_data.NewLaneCdtAutoUpdateDataDao()
	laneCdtManualUpdateRuleDao := lane_manual_update_rule.NewLaneCdtManualUpdateRuleDao()
	laneCdtManualManipulationRuleDao := lane_manual_manipulation_rule.NewLaneCdtManualManipulationRuleDao()
	autoUpdateDataDao := auto_update_data.NewCdtAutoUpdateDataDao(laneCdtAutoUpdateDataDao)
	manualUpdateDao := manual_update_rule.NewCdtManualUpdateRuleDao(laneCdtManualUpdateRuleDao)
	cdtManualManipulationRuleDao := manual_manipulation_rule.NewCdtManualManipulationRuleDao(laneCdtManualManipulationRuleDao)
	slsHolidayDao := sls_holiday.NewSlsHolidayDao()
	lpsHolidayDao := lps_holiday.NewLPSHolidayDAO()
	tplLineIDRefDao := tpl_id_line_id_ref.NewTPLIDLineIDRefDAO()
	eddHistoryDao := edd_history.NewEddHistoryDao()

	cdtCalculationService := NewCdtCalculationService(autoUpdateDao, autoUpdateDataDao, manualUpdateDao, cdtManualManipulationRuleDao, slsHolidayDao, lpsHolidayDao, tplLineIDRefDao, eddHistoryDao, nil, nil, nil)

	// init cache
	dumpManager := cache.DumpManager{}
	cacheConfigs := []*localcache.Conf{
		localcache.NewConf(constant.SlsHolidayNamespace, dumpManager.DumpSlsHoliday, nil, &sls_holiday.HolidayTab{}, 0),
		localcache.NewConf(constant.SlsRecurringHolidayNamespace, dumpManager.DumpSlsRecurringHoliday, nil, &sls_holiday.RecurringHolidayTab{}, 0),
		localcache.NewConf(constant.LPSHolidayNamespace, dumpManager.DumpLPSHoliday, nil, &lps_holiday.ProductHolidayTab{}, 0),
		localcache.NewConf(constant.LPSRecurringHolidayNamespace, dumpManager.DumpLPSRecurringHoliday, nil, &lps_holiday.ProductRecurringHolidayTab{}, 0),
	}

	if err := localcache.InitCacheManager(time.Hour, cacheConfigs...); err != nil {
		t.Fatal(err)
	}
	if err := localcache.LoadLocalCache(); err != nil {
		t.Fatal(err)
	}

	type fields struct {
		autoUpdateDao         auto_update_rule.CDTAutoUpdateRuleTabDAO
		autoUpdateDataDao     auto_update_data.CDTAutoUpdateDataDAO
		manualUpdateDao       manual_update_rule.CDTManualUpdateRuleTabDAO
		manualManipulationDao manual_manipulation_rule.CDTManualManipulationRuleTabDAO
		slsHolidayDao         sls_holiday.SlsHolidayDAO
		lpsHolidayDao         lps_holiday.LPSHolidayDAO
		tplLineIDRefDao       tpl_id_line_id_ref.TPLIDLineIDRefDAO
	}
	type args struct {
		ctx        utils.LCOSContext
		productID  string
		isSiteLine uint8
		stateID    int
		region     string
		lineIDList []*cdt_calculation.LineInfo
	}
	tests := []struct {
		name  string
		args  args
		want  []string
		want1 *lcos_error.LCOSError
	}{
		{
			name: "product 28018 with c_fm",
			args: args{
				ctx:        nil,
				productID:  "28016",
				isSiteLine: 1,
				stateID:    0,
				region:     "MY",
				lineIDList: []*cdt_calculation.LineInfo{
					{
						LineID:  "LMY11",
						SubType: constant.C_LM,
					},
					{
						LineID:  "LMY121323",
						SubType: constant.C_FM,
					},
				},
			},
			want:  nil,
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _, got1 := cdtCalculationService.GetAllCdtHolidays(tt.args.ctx, tt.args.productID, tt.args.isSiteLine, tt.args.stateID, tt.args.region, tt.args.lineIDList)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllCdtHolidays() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetAllCdtHolidays() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_cdtCalculationService_cdtManualManipulate(t *testing.T) {
	ctx := utils.NewCommonCtx(context.Background())
	_ = os.Setenv("ENV", "LOCAL")
	_ = os.Setenv("SSC_ENV", strings.ToLower(config.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))
	_ = chassis.Init(
		chassis.WithGRPCUnaryServerInterceptor(),
		chassis.WithDefaultProviderHandlerChain(
			"log-provider",
			handler.NameOfPrometheusMetricProvider,
		), chassis.WithDefaultConsumerHandlerChain(
			handler.NameOfPrometheusMetricConsumer,
		),
	)
	cfg, err := config.InitConfig(ctx)
	config.InitMutableConfig(ctx)
	assert.Nil(t, err, "init config error")
	err = dbhelper.SetLogisticsCoreServiceConnection(cfg.DBLogisticCoreService)
	assert.Nil(t, err, "init db error")
	cache.StartUp(cfg)

	svc := &CdtCalculationService{
		manualManipulationDao: manual_manipulation_rule.NewCdtManualManipulationRuleDao(lane_manual_manipulation_rule.NewLaneCdtManualManipulationRuleDao()),
	}

	type testCase struct {
		CdtInfo        *cdt_calculation.CdtInfo
		ProductId      string
		IsSiteLine     uint8
		IsCB           uint8
		Region         string
		Sellder        *cdt_calculation.AddressInfo
		Buyer          *cdt_calculation.AddressInfo
		ExpectedCdtMax float64
		ExpectedCdtMin float64
	}
	testCases := []testCase{
		{
			CdtInfo: &cdt_calculation.CdtInfo{
				LeadTimeMax: utils.NewFloat64(2.3),
				LeadTimeMin: utils.NewFloat64(1.1),
			},
			ProductId:      "90000004",
			IsSiteLine:     constant.TRUE,
			IsCB:           constant.FALSE,
			Region:         "sg",
			Sellder:        &cdt_calculation.AddressInfo{StateLocationId: utils.NewInt(1), CityLocationId: utils.NewInt(100002)},
			Buyer:          &cdt_calculation.AddressInfo{StateLocationId: utils.NewInt(110001)},
			ExpectedCdtMax: 5.7,
			ExpectedCdtMin: 3.5,
		},
	}
	time.Sleep(1 * time.Minute)
	for i, tc := range testCases {
		t.Run(fmt.Sprintf("case-%d", i), func(t *testing.T) {
			err := svc.cdtManualManipulate(ctx, edd_constant.CdtObject, tc.CdtInfo, tc.ProductId, "", false, edd_constant.PickupDone, tc.IsSiteLine, 0, tc.IsCB, tc.Region, "", tc.Sellder, tc.Buyer, constant.CdtQueryDataSourceDB, false)
			assert.Nil(t, err)
			assert.Equal(t, tc.ExpectedCdtMax, *tc.CdtInfo.LeadTimeMax)
			assert.Equal(t, tc.ExpectedCdtMin, *tc.CdtInfo.LeadTimeMin)
		})
	}
}

func Test_cdtCalculationService_GetEddFromWbc(t *testing.T) {
	ctx := utils.NewCommonCtx(context.Background())
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(config.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/api"))
	if err := chassis.Init(); err != nil {
		t.Fatal("init chassis error")
	}
	cfg, err := config.InitConfig(ctx)
	if err != nil {
		t.Fatal("init config error", err)
	}
	if _, err := config.InitMutableConfig(ctx); err != nil {
		t.Fatal("init mutable config error", err)
	}
	if err := dbhelper.SetLogisticsCoreServiceConnection(cfg.DBLogisticCoreService); err != nil {
		t.Fatal("init db error", err)
	}
	if err := cache.StartUp(cfg); err != nil {
		t.Fatal("init local cache error", err)
	}
	if err := startup.InitLayeredCache(cfg); err != nil {
		t.Fatal("init layered cache error", err)
	}

	svc := &CdtCalculationService{
		manualManipulationDao: manual_manipulation_rule.NewCdtManualManipulationRuleDao(lane_manual_manipulation_rule.NewLaneCdtManualManipulationRuleDao()),
		lpsHolidayDao:         lps_holiday.NewLPSHolidayDAO(),
	}

	product := &cdt_calculation.CdtProductInfo{
		QueryID:    "haobing.mu",
		ProductID:  "90020",
		IsCB:       constant.FALSE,
		IsSiteLine: constant.TRUE,
		Region:     "sg",
		SellerAddr: &cdt_calculation.AddressInfo{},
		BuyerAddr:  &cdt_calculation.AddressInfo{StateLocationId: utils.NewInt(0)},
	}
	eddCalculation, _, _ := svc.GetEddFromWbc(ctx, product, 4955471786374146276, recorder.Now(ctx).Unix())
	assert.Equal(t, 1.2, eddCalculation.LeadTimeMax)
	assert.Equal(t, 0., eddCalculation.EddMaxHolidayExt)
}

func TestCdtCalculationService_calculateAlgoEddHolidayExt(t *testing.T) {

	testTimeZone, _ := time.LoadLocation("Asia/Singapore")
	testDateTime := time.Date(2024, 7, 10, 23, 59, 59, 0, testTimeZone)

	type args struct {
		edd         int64
		holidayList []string
		leadTime    float64
		region      string
	}
	tests := []struct {
		name  string
		args  args
		want  float64
		want1 []string
	}{
		{
			name: "测试 delta < 0的情况，且当前不是holiday的情况", // 目前暂时不处于delta小于0的情况，此种情况，返回holiday extension=0
			args: args{
				edd:         testDateTime.Unix(),
				holidayList: []string{"2024-07-07", "2024-07-08", "2024-07-09"},
				leadTime:    -2,
				region:      "SG",
			},
			want:  0,
			want1: []string{},
		},
		{
			name: "测试 delta < 0的情况，且当前是holiday的情况",
			args: args{
				edd:         testDateTime.Unix(),
				holidayList: []string{"2024-07-07", "2024-07-08", "2024-07-09", "2024-07-10"},
				leadTime:    -2,
				region:      "SG",
			},
			want:  0,
			want1: []string{},
		},
		{
			name: "测试 delta = 0的情况，且当前是holiday的情况",
			args: args{
				edd:         testDateTime.Unix(),
				holidayList: []string{"2024-07-07", "2024-07-08", "2024-07-09", "2024-07-10"},
				leadTime:    0,
				region:      "SG",
			},
			want:  1,
			want1: []string{"2024-07-10"},
		},
		{
			name: "测试 delta = 0的情况，且当前是holiday的情况",
			args: args{
				edd:         testDateTime.Unix(),
				holidayList: []string{"2024-07-07", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11"},
				leadTime:    0,
				region:      "SG",
			},
			want:  2,
			want1: []string{"2024-07-10", "2024-07-11"},
		},
		{
			name: "测试 delta = 1的情况，且当前是holiday的情况",
			args: args{
				edd:         testDateTime.Unix(),
				holidayList: []string{"2024-07-07", "2024-07-08", "2024-07-09", "2024-07-10"},
				leadTime:    1,
				region:      "SG",
			},
			want:  1,
			want1: []string{"2024-07-10"},
		},
		{
			name: "测试 delta = 2的情况，且当前是holiday的情况",
			args: args{
				edd:         testDateTime.Unix(),
				holidayList: []string{"2024-07-07", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11"},
				leadTime:    1,
				region:      "SG",
			},
			want:  2,
			want1: []string{"2024-07-10", "2024-07-11"},
		},
		{
			name: "测试 delta = 1的情况，且当前不是holiday的情况",
			args: args{
				edd:         testDateTime.Unix(),
				holidayList: []string{"2024-07-07", "2024-07-08", "2024-07-09", "2024-07-11"},
				leadTime:    1,
				region:      "SG",
			},
			want:  1,
			want1: []string{"2024-07-11"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CdtCalculationService{}
			got, got1 := c.calculateAlgoEddHolidayExt(tt.args.edd, tt.args.holidayList, tt.args.leadTime, tt.args.region)
			assert.Equalf(t, tt.want, got, "calculateAlgoEddHolidayExt(%v, %v, %v, %v)", tt.args.edd, tt.args.holidayList, tt.args.leadTime, tt.args.region)
			assert.Equalf(t, tt.want1, got1, "calculateAlgoEddHolidayExt(%v, %v, %v, %v)", tt.args.edd, tt.args.holidayList, tt.args.leadTime, tt.args.region)
		})
	}
}
