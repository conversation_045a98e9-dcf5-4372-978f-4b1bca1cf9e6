package auto_update_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/pis_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type (
	singleManualCdtQueryProcess struct {
		QueryID string               // 唯一键
		ItemId  uint64               // 构造结果需要
		Param   ManualCdtQueryParam  // 入参
		Result  ManualCdtQueryResult // 结果
		Data    ManualCdtQueryData   // 中间过程数据
	}

	ManualCdtQueryParam struct {
		ObjectType      uint8
		ProductID       string
		MChannelID      string
		IsSiteLine      uint8
		IsLM            uint8
		IsCB            uint8
		Region          string
		TplUniqueKey    string
		LaneCode        string
		UpdateEvent     uint8
		SellerAddr      *cdt_calculation.AddressInfo
		BuyerAddr       *cdt_calculation.AddressInfo
		SkuInfo         *cdt_calculation.CdtSkuInfo
		NeedEqualLevels bool
		NeedVolume      uint8
		DataSource      uint8
		RequestTime     uint32
		CdtScene        pb.CdtScene
	}

	ManualCdtQueryData struct {
		CdtQueries                             []*cdt_calculation.CdtQuery
		LaneCodeFlag                           bool // 是否匹配lane维度数据
		AlreadyFindOneShippedOutEventEnumValue bool
		FoundInThirdParty                      bool // 是否通过Third Party获取到了Cdt Info
	}

	// CdtQueryResult 处理结果，如果Err不为空，即无法计算CDT结果
	ManualCdtQueryResult struct {
		CdtInfos         []*cdt_calculation.CdtInfo
		ReturnCdtQueries []*cdt_calculation.CdtQuery
		Err              *lcos_error.LCOSError
	}
)

// completed 是否已经处理完成
func (s singleManualCdtQueryProcess) completed() bool {
	// 通过ThirdParty查询并且查到了
	if s.Data.FoundInThirdParty && len(s.Result.CdtInfos) > 0 {
		return true
	}

	// 非MChannel并且已经查询到了，就代表结束
	if !s.Param.NeedEqualLevels && len(s.Result.CdtInfos) > 0 {
		return true
	}

	return false
}

func (s *singleManualCdtQueryProcess) popQuery(ctx utils.LCOSContext) (*cdt_calculation.CdtQuery, bool) {
	if len(s.Data.CdtQueries) == 0 {
		return nil, false
	}

	var popIndex = -1
	for index, cdtQuery := range s.Data.CdtQueries {
		// 如果 上一个cdtQueries 是updateEvent = （8，9，10，11）且已经找到一条cdtInfo，那么兜底的ship out就不用再查询
		// 这里有个前置条件是query兜底的ship out一定紧随updateEvent = （8，9，10，11）的query后面
		if cdtQuery.GetUpdateEvent() == edd_constant.ShippedOut && s.Data.AlreadyFindOneShippedOutEventEnumValue {
			s.Data.AlreadyFindOneShippedOutEventEnumValue = false
			continue
		}

		// 对于Postcode/Location检查下Query是否合法，非法则找下一个Query
		if cdtQuery.QueryType == constant.CdtQueryPostcode {
			if cdtQuery.DestinationPostcode == nil || *cdtQuery.DestinationPostcode == "" {
				logger.CtxLogErrorf(ctx, "not valid cdt query:%s", utils.MarshToStringWithoutError(cdtQuery))
				continue
			}
		} else if cdtQuery.QueryType == constant.CdtQueryLocation {
			if cdtQuery.DestinationLocationId == nil {
				logger.CtxLogErrorf(ctx, "not valid cdt query:%s", utils.MarshToStringWithoutError(cdtQuery))
				continue
			}
		}

		// 找到即break
		popIndex = index
		break
	}

	// 找不到的情况
	if popIndex == -1 {
		return nil, false
	}

	q := s.Data.CdtQueries[popIndex]
	s.Data.CdtQueries = s.Data.CdtQueries[popIndex+1:]

	return q, true
}

func (c *CdtCalculationService) ConstructManualCdtQueryProcess(
	productInfo *cdt_calculation.CdtProductInfo, objectType uint8, dataSource uint8,
) *singleManualCdtQueryProcess {
	return &singleManualCdtQueryProcess{
		QueryID: productInfo.QueryID,
		ItemId:  productInfo.ItemId,
		Param: ManualCdtQueryParam{
			ObjectType:      objectType,
			ProductID:       productInfo.ProductID,
			MChannelID:      productInfo.MChannelID,
			IsSiteLine:      productInfo.IsSiteLine,
			IsLM:            0,
			IsCB:            productInfo.IsCB,
			Region:          productInfo.Region,
			TplUniqueKey:    "",
			LaneCode:        productInfo.LaneCode,
			UpdateEvent:     productInfo.UpdateEvent,
			SellerAddr:      productInfo.SellerAddr,
			BuyerAddr:       productInfo.BuyerAddr,
			SkuInfo:         productInfo.SkuInfo,
			NeedEqualLevels: productInfo.NeedEqualLevel,
			NeedVolume:      productInfo.NeedVolume,
			DataSource:      dataSource,
			RequestTime:     productInfo.RequestTime,
			CdtScene:        productInfo.CdtScene,
		},
	}
}

// needGetCdtFromThirdParty 是否需要调用PIS获取CDT
func needGetCdtFromThirdParty(ctx utils.LCOSContext, p *singleManualCdtQueryProcess) bool {
	// 降级开关
	if cf.GetConf(ctx).Cdt.IsThirdPartyServiceDowngrade() {
		return false
	}

	// 需要有SKU Info
	if p.Param.SkuInfo == nil {
		return false
	}

	// 白名单
	return productInWhiteList(p.Param.ProductID, cf.GetConf(ctx).Cdt.GetThirdPartyCdtWhiteList())
}

func (c *CdtCalculationService) preHandleManualCdtProcess(ctx utils.LCOSContext, p *singleManualCdtQueryProcess) {
	param := p.Param

	if param.ObjectType == edd_constant.LeadTimeObject && len(param.LaneCode) > 0 {
		// 只有EDD场景（leadtime）且传了lane code才可以匹配lane维度数据
		p.Data.LaneCodeFlag = true
	}

	// check if need get cdt from thirdParty service
	if needGetCdtFromThirdParty(ctx, p) {
		pisClient := pis_service.NewPISService(ctx, param.Region)
		req := convertGetQuotationReq(param.ProductID, param.SellerAddr, param.BuyerAddr, param.SkuInfo)
		resp, err := pisClient.GetQuotation(ctx, ctx.GetRequestId(), req)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Get cdt_info from third_party service failed, err:%v", err)
		} else {
			returnedCdt := float64(resp.DeliveryEstimateTransitTimeBusinessDays + resp.DeliveryAdditionalTransitTimeBusinessDays)
			p.Result.CdtInfos = append(p.Result.CdtInfos, &cdt_calculation.CdtInfo{
				ProductId:   param.ProductID,
				IsSiteLine:  param.IsSiteLine,
				LeadTimeMin: utils.NewFloat64(returnedCdt),
				LeadTimeMax: utils.NewFloat64(returnedCdt), // SPLN-30962, make edt min same as edt max
			})
			p.Data.FoundInThirdParty = true

			// 如果在Third Party就找到Cdt Info不需要去找Manual Update了
			return
		}
	}

	queries, err := c.getQuery(ctx, param.ObjectType, param.ProductID, param.LaneCode, p.Data.LaneCodeFlag, param.UpdateEvent,
		param.IsSiteLine, param.IsLM, param.IsCB, param.TplUniqueKey, param.SellerAddr, param.BuyerAddr, param.Region,
		0, 0, true, param.DataSource, param.NeedEqualLevels)
	if err != nil {
		p.Result.Err = err
		return
	}

	p.Data.CdtQueries = queries
}

// postHandleManualCdtProcess 对CDT做Manual Manipulate
func (c *CdtCalculationService) postHandleManualCdtProcess(ctx utils.LCOSContext, p *singleManualCdtQueryProcess) {
	var (
		param        = p.Param
		cdtInfo      *cdt_calculation.CdtInfo
		realCdtQuery *cdt_calculation.CdtQuery // record which query is really is use for cdt
		midResult    = &cdt_calculation.CdtProcess{}
	)

	if len(p.Result.CdtInfos) > 0 && !param.NeedEqualLevels {
		if len(p.Result.CdtInfos) == 0 || len(p.Result.ReturnCdtQueries) == 0 {
			errMsg := fmt.Sprintf("get cdtInfos empty or get realCdtQueries empty|product_id=%v, is_site_line=%v,is_lm=%v",
				param.ProductID, param.IsSiteLine, param.IsLM)
			logger.CtxLogInfof(ctx, errMsg)
			p.Result.Err = lcos_error.NewLCOSError(lcos_error.NotFoundCdtInfoErrorCode, errMsg)
			return
		}

		cdtInfo = p.Result.CdtInfos[0]
		realCdtQuery = p.Result.ReturnCdtQueries[0]

		cdtInfo.SetIgnoreLeadTimeMinFlag(p.Param.ObjectType, cdtInfo.GetLeadTimeMin() < 0)
		// SPLN-23295 fill manual update info
		midResult.SetCdtQuery(realCdtQuery)
		midResult.SetManualUpdateProcess(cdtInfo)
		logger.CtxLogInfof(ctx, "successfully find manual update cdt info|cdt_query=[%s]|reply=[%s]",
			utils.MarshToStringWithoutError(realCdtQuery), utils.MarshToStringWithoutError(cdtInfo))
	}

	if p.Param.NeedEqualLevels {
		//对手动上传或者自动规则的结果进行修正
		for i, oriCdtInfo := range p.Result.CdtInfos {
			if err := c.cdtManualManipulate(ctx, param.ObjectType, oriCdtInfo, param.ProductID, param.LaneCode,
				p.Data.LaneCodeFlag, param.UpdateEvent, param.IsSiteLine, param.IsLM, param.IsCB, param.Region, param.TplUniqueKey,
				param.SellerAddr, param.BuyerAddr, param.DataSource, constant.NotNeedEqualLevels, param.RequestTime, param.CdtScene); err != nil {
				errMsg := fmt.Sprintf("cdt manual manipulate error: %s", err.Msg)
				logger.CtxLogErrorf(ctx, errMsg)
				p.Result.Err = lcos_error.NewLCOSError(err.RetCode, errMsg)
			}
			if oriCdtInfo != nil && oriCdtInfo.CdtProcess != nil {
				oriCdtInfo.CdtProcess.CdtQueryInfo = p.Result.ReturnCdtQueries[i]
			}
		}
	} else {
		if cdtInfo == nil {
			errMsg := fmt.Sprintf("query cdt info failed|product_id=%v, is_site_line=%v,is_lm=%v",
				param.ProductID, param.IsSiteLine, param.IsLM)
			logger.CtxLogInfof(ctx, errMsg)
			p.Result.Err = lcos_error.NewLCOSError(lcos_error.NotFoundCdtInfoErrorCode, errMsg)
			return
		}

		cdtInfo.CdtProcess = midResult
		if err := c.cdtManualManipulate(ctx, param.ObjectType, cdtInfo, param.ProductID, param.LaneCode,
			p.Data.LaneCodeFlag, param.UpdateEvent, param.IsSiteLine, param.IsLM, param.IsCB, param.Region, param.TplUniqueKey,
			param.SellerAddr, param.BuyerAddr, param.DataSource, constant.NotNeedEqualLevels, param.RequestTime, param.CdtScene); err != nil {
			errMsg := fmt.Sprintf("cdt manual manipulate error: %s", err.Msg)
			logger.CtxLogErrorf(ctx, errMsg)
			p.Result.Err = lcos_error.NewLCOSError(err.RetCode, errMsg)
		}
	}
}

// batchHandleManualCdtProcess 以MGet批量获取Manual Cdt
func (c *CdtCalculationService) batchHandleManualCdtProcess(ctx utils.LCOSContext, processList []*singleManualCdtQueryProcess) {
	// 1. 预处理，ThirdParty特殊处理和查找CdtQueries
	for _, p := range processList {
		c.preHandleManualCdtProcess(ctx, p)
	}

	// 2. MGet批量获取
	c.batchCalcManualCdt(ctx, processList)

	// 3. 后处理，包括找不到CdtInfo的Err处理和Manual Manipulate
	for _, p := range processList {
		c.postHandleManualCdtProcess(ctx, p)
	}
}

func (c *CdtCalculationService) batchCalcManualCdt(ctx utils.LCOSContext, processList []*singleManualCdtQueryProcess) {
	for i := 0; i < maxQueriesNum; i++ {
		var (
			daoProcessList = make([]*manual_update_rule.ManualCdtDaoProcess, 0)
			processMapping = make(map[*manual_update_rule.ManualCdtDaoProcess]*singleManualCdtQueryProcess)
		)

		// Dao层的批量计算Process组装
		for _, serviceProcess := range processList {
			if serviceProcess.completed() {
				continue
			}

			query, ok := serviceProcess.popQuery(ctx)
			if !ok {
				// 已经把query列表都清完都查不出来的情况，在外层会处理这种情况
				continue
			}

			daoProcess := manual_update_rule.NewManualCdtDaoProcess(
				serviceProcess.Param.ProductID, serviceProcess.Param.IsSiteLine,
				serviceProcess.Param.IsLM, serviceProcess.Param.Region, query, serviceProcess.Param.NeedEqualLevels,
			)
			daoProcessList = append(daoProcessList, daoProcess)
			processMapping[daoProcess] = serviceProcess
		}

		// 没有需要计算的则提前退出
		if len(daoProcessList) == 0 {
			break
		}

		// Dao层批量计算
		c.manualUpdateDao.BatchQueryCdtInfoUsingCache(ctx, daoProcessList)

		// Dao层的结果映射回Service层
		for daoProcess, serviceProcess := range processMapping {
			// 如果获取不到正式结果或者有报错的话就不需要映射回去，等下一轮query继续查
			if daoProcess.Result.Err != nil || daoProcess.Result.CdtInfo == nil {
				continue
			}

			// 这里是获取到了CdtInfo才需要映射回去
			serviceProcess.Data.AlreadyFindOneShippedOutEventEnumValue = daoProcess.Data.AlreadyFindOneShippedOutEventEnumValue
			serviceProcess.Result.CdtInfos = append(serviceProcess.Result.CdtInfos, daoProcess.Result.CdtInfo)
			serviceProcess.Result.ReturnCdtQueries = append(serviceProcess.Result.ReturnCdtQueries, daoProcess.Param.CdtQuery)
		}
	}
}
