package auto_update_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/rand_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	apollo_config "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	Logger "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"strings"
)

const maxQueriesNum = 10000

// CdtQueryParam 处理入参
type CdtQueryParam struct {
	ObjectType     uint8
	ProductID      string
	LaneCode       string
	LaneCodeFlag   bool
	UpdateEvent    uint8
	IsSiteLine     uint8
	IsLM           uint8
	IsCB           uint8
	TplUniqueKey   string
	SellerAddr     *cdt_calculation.AddressInfo
	BuyerAddr      *cdt_calculation.AddressInfo
	Region         string
	FullQuery      bool
	AutoUpdateRule *auto_update_rule.CDTAutoUpdateRuleTab
	DataSource     uint8
	needEqualLevel bool
	NeedVolume     uint8
	MChannelID     string
	RequestTime    uint32
	CdtScene       pb.CdtScene
}

// CdtQueryResult 处理结果，如果Err不为空，即无法计算CDT结果
type CdtQueryResult struct {
	CdtInfo  *cdt_calculation.CdtInfo
	CdtInfos []*cdt_calculation.CdtInfo
	Err      *lcos_error.LCOSError
}

type CdtQueryProcessData struct {
	Queries    []*cdt_calculation.CdtQuery
	CdtVersion uint32
}

type singleCdtQueryProcess struct {
	QueryID string              // 唯一键
	ItemId  uint64              // 构造结果需要
	Param   CdtQueryParam       // 入参
	Result  CdtQueryResult      // 结果
	Data    CdtQueryProcessData // 过程数据
	DaoData CdtQueryProcessData // 过程数据
}

func (s *singleCdtQueryProcess) GetAutoUpdateRuleId() uint64 {
	if s != nil && s.Param.AutoUpdateRule != nil {
		return s.Param.AutoUpdateRule.ID
	}
	return 0
}

func (s *singleCdtQueryProcess) GetCdtVersion() uint32 {
	if s != nil {
		return s.Data.CdtVersion
	}
	return 0
}

func (s *singleCdtQueryProcess) GetOriginLevelWithIndex(i int) int8 {
	if s != nil && i >= 0 && len(s.Data.Queries) > i && s.Data.Queries[i] != nil {
		return s.Data.Queries[i].OriginLocationLevel
	}
	return 0
}

func (s *singleCdtQueryProcess) GetDestLevelWithIndex(i int) int8 {
	if s != nil && i >= 0 && len(s.Data.Queries) > i && s.Data.Queries[i] != nil {
		return s.Data.Queries[i].DestinationLocationLevel
	}
	return 0
}

type singleVolumeQueryProcess struct {
	QueryID     string // 唯一键
	ItemId      uint64 // 构造结果需要
	ProductInfo *cdt_calculation.CdtProductInfo
	Param       VolumeQueryParam       // 入参
	Result      VolumeQueryResult      // 结果
	Data        VolumeQueryProcessData // 过程数据
}

func (s *singleVolumeQueryProcess) GetAutoVolumeUpdateRuleId() uint64 {
	if s != nil && s.Param.AutoVolumeUpdateRule != nil {
		return s.Param.AutoVolumeUpdateRule.Id
	}
	return 0
}

func (s *singleVolumeQueryProcess) GetVolumeVersion() uint32 {
	if s != nil {
		return s.Data.VolumeVersion
	}
	return 0
}

// VolumeQueryParam 处理入参
type VolumeQueryParam struct {
	ProductID            string
	SellerAddr           *cdt_calculation.AddressInfo
	BuyerAddr            *cdt_calculation.AddressInfo
	Region               string
	FullQuery            bool
	AutoVolumeUpdateRule *automated_volume.AutomatedVolumeGenerationRuleTab
	DataSource           uint8
	needEqualLevel       bool
	MChannelID           string
	VolumeVersion        uint32
}

// VolumeQueryResult 处理结果，如果Err不为空，即无法计算Volume结果
type VolumeQueryResult struct {
	VolumeInfos []*cdt_calculation.VolumeInfo
	Err         *lcos_error.LCOSError
}

type VolumeQueryProcessData struct {
	Queries       []*cdt_calculation.VolumeQuery
	VolumeVersion uint32
}

func newSingleCdtQueryProcess(
	queryID string, itemID uint64, objectType uint8, productID, laneCode string, laneCodeFlag bool, updateEvent uint8, isSiteLine uint8,
	isLM uint8, isCB uint8, tplUniqueKey string, sellerAddr, buyerAddr *cdt_calculation.AddressInfo, region string,
	fullQuery bool, dataSource uint8, autoUpdateRule *auto_update_rule.CDTAutoUpdateRuleTab,
	needEqualLevel bool, needVolume uint8, mChannelID string, requestTime uint32, cdtScene pb.CdtScene,
) *singleCdtQueryProcess {
	return &singleCdtQueryProcess{
		QueryID: queryID,
		ItemId:  itemID,
		Param: CdtQueryParam{
			ObjectType:     objectType,
			ProductID:      productID,
			LaneCode:       laneCode,
			LaneCodeFlag:   laneCodeFlag,
			UpdateEvent:    updateEvent,
			IsSiteLine:     isSiteLine,
			IsLM:           isLM,
			IsCB:           isCB,
			TplUniqueKey:   tplUniqueKey,
			SellerAddr:     sellerAddr,
			BuyerAddr:      buyerAddr,
			Region:         region,
			FullQuery:      fullQuery,
			AutoUpdateRule: autoUpdateRule,
			DataSource:     dataSource,
			needEqualLevel: needEqualLevel,
			NeedVolume:     needVolume,
			MChannelID:     mChannelID,
			RequestTime:    requestTime,
			CdtScene:       cdtScene,
		},
	}
}

func newSingleVolumeQueryProcess(
	queryID string, itemID uint64, productID string, sellerAddr, buyerAddr *cdt_calculation.AddressInfo, region string,
	dataSource uint8, autoVolumeUpdateRule *automated_volume.AutomatedVolumeGenerationRuleTab, needEqualLevel bool, mChannelID string, volumeVersion uint32,
	productInfo *cdt_calculation.CdtProductInfo,
) *singleVolumeQueryProcess {
	return &singleVolumeQueryProcess{
		QueryID:     queryID,
		ItemId:      itemID,
		ProductInfo: productInfo,
		Param: VolumeQueryParam{
			ProductID:            productID,
			SellerAddr:           sellerAddr,
			BuyerAddr:            buyerAddr,
			Region:               region,
			AutoVolumeUpdateRule: autoVolumeUpdateRule,
			DataSource:           dataSource,
			needEqualLevel:       needEqualLevel,
			MChannelID:           mChannelID,
			VolumeVersion:        volumeVersion,
		},
	}
}

// completed 是否已经处理完成
func (s singleCdtQueryProcess) completed() bool {
	return s.Result.Err != nil || s.Result.CdtInfo != nil
}

// popQuery
func (s *singleCdtQueryProcess) popQuery() (*cdt_calculation.CdtQuery, bool) {
	if len(s.Data.Queries) == 0 {
		return nil, false
	}

	q := s.Data.Queries[0]
	s.Data.Queries = s.Data.Queries[1:]

	return q, true
}

func (c *CdtCalculationService) batchHandleAutoCdtProcess(ctx utils.LCOSContext, processList []*singleCdtQueryProcess) {
	// 1.前置数据准备
	for _, p := range processList {
		c.preHandle(ctx, p)
	}

	// 2.Auto批量计算
	c.batchCalcAutoCdt(ctx, processList)

	// 3.后置结果处理
	for _, p := range processList {
		c.postHandle(ctx, p)
	}
}

func (c *CdtCalculationService) batchGetVolumeWithEqualLevel(ctx utils.LCOSContext, processList []*singleVolumeQueryProcess) {
	// 1.前置数据准备 获取所有的query  一个p对应所有的query
	totalQueryLen := 0
	for _, p := range processList {
		queryLen := c.preVolumeHandle(ctx, p)
		totalQueryLen += queryLen
	}

	// 2.单量批量计算
	c.batchGetVolume(ctx, totalQueryLen, processList)

	// 3.后置结果处理
	for _, p := range processList {
		c.postVolumeHandle(ctx, p)
	}
}

func (c *CdtCalculationService) batchHandleCalcCdtWithEqualLevel(ctx utils.LCOSContext, processList []*singleCdtQueryProcess) {
	// 1.前置数据准备 对每一个请求拼好query
	for _, p := range processList {
		c.preHandle(ctx, p)
	}

	// 2.Auto批量计算
	c.batchCalcAutoCdtWithEqualLevel(ctx, processList)

	// 3.手动修正与后置结果处理
	for _, p := range processList {
		c.postEqualLevelHandle(ctx, p)
	}
}

func (c *CdtCalculationService) preMChannelHandle(ctx utils.LCOSContext, productInfoList []*cdt_calculation.CdtProductInfo) ([]*cdt_calculation.CdtProductInfo, []*cdt_calculation.CdtProductInfo) {
	var productInfoListWithFChannelList []*cdt_calculation.CdtProductInfo
	var needVolumeProductInfoList []*cdt_calculation.CdtProductInfo
	// 循环获取rule并对非auto的做single处理，将auto的单独放到list里等待批量处理
	for _, singleProductInfo := range productInfoList {
		if len(singleProductInfo.FChannelList) != 0 {
			if config.GetEnv(ctx) == config.LIVE && strings.ToUpper("grpclivetest") == config.GetModuleName() {
				_ = monitor.AwesomeReportEvent(ctx, constant.AggregateCdtGetInfoItemCard, constant.CalcNeedMChannelCdt, constant.StatusSuccess, "")
			}

			abTestRule, _ := c.abTestService.GetActiveAbTestRuleByProductIdUsingCache(ctx, singleProductInfo.ProductID, edd_constant.CdtObject)
			hitGroupTag, skipAutoUpdateRule := getAbTestResult(singleProductInfo.GroupTag, abTestRule)

			for _, singleFChannelInfo := range singleProductInfo.FChannelList {
				needEqualLevels := constant.NotNeedEqualLevels
				if len(singleProductInfo.FChannelList) != 0 {
					needEqualLevels = constant.NeedEqualLevels
				}

				newSingleCdtProductInfo := &cdt_calculation.CdtProductInfo{
					QueryID:            singleProductInfo.QueryID,
					ProductID:          singleFChannelInfo.FChannelID,
					IsCB:               singleFChannelInfo.IsCB,
					IsSiteLine:         singleFChannelInfo.IsSiteLine,
					Region:             singleProductInfo.Region,
					SellerAddr:         singleProductInfo.SellerAddr,
					BuyerAddr:          singleProductInfo.BuyerAddr,
					SkuInfo:            singleProductInfo.SkuInfo,
					LaneCode:           singleProductInfo.LaneCode,
					LineList:           singleProductInfo.LineList,
					UpdateEvent:        singleProductInfo.UpdateEvent,
					NextEvent:          singleProductInfo.NextEvent,
					ItemId:             singleProductInfo.ItemId,
					NeedVolume:         singleProductInfo.NeedVolume,
					NeedEqualLevel:     needEqualLevels,
					MChannelID:         singleProductInfo.ProductID,
					GroupTag:           uint32(hitGroupTag),
					RequestTime:        singleProductInfo.RequestTime,
					SkipAutoUpdateRule: skipAutoUpdateRule,
					StartFCodeType:     singleProductInfo.StartFCodeType,
					CdtScene:           singleProductInfo.CdtScene,
				}

				productInfoListWithFChannelList = append(productInfoListWithFChannelList, newSingleCdtProductInfo)
				if singleProductInfo.NeedVolume == constant.NeedVolume {
					needVolumeProductInfoList = append(needVolumeProductInfoList, newSingleCdtProductInfo)
				}
			}
			if config.GetEnv(ctx) == config.LIVE && strings.ToUpper("grpclivetest") == config.GetModuleName() {
				_ = monitor.AwesomeReportEvent(ctx, constant.AggregateCdtGetInfoItemCard, constant.CalcNeedVolume, constant.StatusSuccess, "")
			}
		} else {
			if config.GetEnv(ctx) == config.LIVE && strings.ToUpper("grpclivetest") == config.GetModuleName() {
				_ = monitor.AwesomeReportEvent(ctx, constant.AggregateCdtGetInfoItemCard, constant.CalcOld, constant.StatusSuccess, "")
			}

			abTestRule, _ := c.abTestService.GetActiveAbTestRuleByProductIdUsingCache(ctx, singleProductInfo.ProductID, edd_constant.CdtObject)
			hitGroupTag, skipAutoUpdateRule := getAbTestResult(singleProductInfo.GroupTag, abTestRule)
			singleProductInfo.GroupTag = uint32(hitGroupTag)
			singleProductInfo.SkipAutoUpdateRule = skipAutoUpdateRule

			productInfoListWithFChannelList = append(productInfoListWithFChannelList, singleProductInfo)
		}
	}
	return productInfoListWithFChannelList, needVolumeProductInfoList
}

func (c *CdtCalculationService) batchGetVolume(ctx utils.LCOSContext, totalQueryLen int, processList []*singleVolumeQueryProcess) {
	var (
		daoVolumeEqualLevelProcessList = make([]*automated_volume_generation_data.GetVolumeInfoByVolumeVersionProcess, 0, totalQueryLen)
		processMapping                 = make(map[*singleVolumeQueryProcess][]*automated_volume_generation_data.GetVolumeInfoByVolumeVersionProcess, totalQueryLen)
	)

	for _, serviceEqualProcess := range processList {
		for _, query := range serviceEqualProcess.Data.Queries {
			daoVolumeEqualLevelProcess := automated_volume_generation_data.NewGetVolumeInfoByVolumeVersionProcess(serviceEqualProcess.Param.AutoVolumeUpdateRule, serviceEqualProcess.Data.VolumeVersion, query)
			daoVolumeEqualLevelProcessList = append(daoVolumeEqualLevelProcessList, daoVolumeEqualLevelProcess)
			processMapping[serviceEqualProcess] = append(processMapping[serviceEqualProcess], daoVolumeEqualLevelProcess)
		}
	}

	c.automatedVolumeGenerationDataDao.BatchGetEqualLevelVolumeInfoByCdtVersionUsingCache(ctx, daoVolumeEqualLevelProcessList)

	for serviceEqualProcess, daoEqualLevelProcessList := range processMapping {
		for _, daoEqualLevelProcess := range daoEqualLevelProcessList {
			// 拿到了fchannel 所有的cdtinfo ，映射回去
			serviceEqualProcess.Result.VolumeInfos = append(serviceEqualProcess.Result.VolumeInfos, daoEqualLevelProcess.Result.VolumeInfo)
		}
	}
}

func (c *CdtCalculationService) batchCalcAutoCdtWithEqualLevel(ctx utils.LCOSContext, processList []*singleCdtQueryProcess) {
	//每一个process 对应多个queries 都需要查询出所对应层级的cdt

	var (
		daoEqualLevelProcessList = make([]*auto_update_data.GetCdtInfoByCdtVersionProcess, 0)
		processMapping           = make(map[*singleCdtQueryProcess][]*auto_update_data.GetCdtInfoByCdtVersionProcess)
	)
	for _, serviceEqualProcess := range processList {
		preUpdateEvent := edd_constant.DefaultUpdateEvent
		for _, query := range serviceEqualProcess.Data.Queries {
			daoEqualLevelProcess := auto_update_data.NewGetCdtInfoByCdtVersionProcess(serviceEqualProcess.Param.AutoUpdateRule, serviceEqualProcess.Data.CdtVersion, query, preUpdateEvent)
			daoEqualLevelProcessList = append(daoEqualLevelProcessList, daoEqualLevelProcess)
			processMapping[serviceEqualProcess] = append(processMapping[serviceEqualProcess], daoEqualLevelProcess)
			preUpdateEvent = query.GetUpdateEvent()
		}
	}

	SwitchPercentage := apollo_config.GetUseNewFilterMethodConfig(ctx)
	if rand_util.CheckPercent(ctx, SwitchPercentage) {
		c.autoUpdateDataDao.BatchGetEqualLevelCdtInfoByCdtVersionUsingCacheV2(ctx, daoEqualLevelProcessList)
		for serviceEqualProcess, daoEqualLevelProcessList := range processMapping {
			filterMap := make(map[string]bool)
			for _, daoEqualLevelProcess := range daoEqualLevelProcessList {
				// 拿到了fchannel 所有的cdtinfo ，映射回去
				if daoEqualLevelProcess.Result.CdtInfo != nil && checkIfNeedAppend(daoEqualLevelProcess.Param.CdtQuery.OriginLocationLevel, daoEqualLevelProcess.Param.CdtQuery.DestinationLocationLevel, daoEqualLevelProcess.Param.CdtQuery.GetUpdateEvent(), filterMap) {
					serviceEqualProcess.Result.CdtInfos = append(serviceEqualProcess.Result.CdtInfos, daoEqualLevelProcess.Result.CdtInfo)
				} else {
					serviceEqualProcess.Result.CdtInfos = append(serviceEqualProcess.Result.CdtInfos, nil)
				}
			}
		}
	} else {
		// Dao层批量计算
		c.autoUpdateDataDao.BatchGetEqualLevelCdtInfoByCdtVersionUsingCache(ctx, daoEqualLevelProcessList)

		for serviceEqualProcess, daoEqualLevelProcessList := range processMapping {
			for _, daoEqualLevelProcess := range daoEqualLevelProcessList {
				// 拿到了fchannel 所有的cdtinfo ，映射回去
				serviceEqualProcess.Result.CdtInfos = append(serviceEqualProcess.Result.CdtInfos, daoEqualLevelProcess.Result.CdtInfo)
			}
		}
	}
}

func (c *CdtCalculationService) batchCalcAutoCdt(ctx utils.LCOSContext, processList []*singleCdtQueryProcess) {
	// 过载保护，正常最大的循环次数不超100
	for i := 0; i < maxQueriesNum; i++ {
		var (
			daoProcessList = make([]*auto_update_data.GetCdtInfoByCdtVersionProcess, 0)
			processMapping = make(map[*auto_update_data.GetCdtInfoByCdtVersionProcess]*singleCdtQueryProcess)
		)

		// Dao层的批量计算Process组装
		for _, serviceProcess := range processList {
			if serviceProcess.completed() {
				// 在之前的轮次就已经查出来，就不需要处理了
				continue
			}

			query, ok := serviceProcess.popQuery()
			if !ok {
				// 已经把query列表都清完都查不出来的情况，在外层会处理这种情况
				continue
			}

			daoProcess := auto_update_data.NewGetCdtInfoByCdtVersionProcess(serviceProcess.Param.AutoUpdateRule, serviceProcess.Data.CdtVersion, query, edd_constant.DefaultUpdateEvent)
			daoProcessList = append(daoProcessList, daoProcess)
			processMapping[daoProcess] = serviceProcess
		}

		// 没有需要计算的则提前退出
		if len(daoProcessList) == 0 {
			break
		}

		// Dao层批量计算
		c.autoUpdateDataDao.BatchGetCdtInfoByCdtVersionUsingCache(ctx, daoProcessList)

		// Dao层的结果映射回Service层
		for daoProcess, serviceProcess := range processMapping {
			// 如果获取不到正式结果或者有报错的话就不需要映射回去，等下一轮query继续查
			if daoProcess.Result.Err != nil || daoProcess.Result.CdtInfo == nil {
				logger.CtxLogInfof(ctx, "failed to find cdt info|cdt_query=[%s], auto_rule_id=%d, auto_update_version=%v",
					Logger.JsonStringForInfoLog(ctx, daoProcess.Param.CdtQuery), daoProcess.Param.AutoUpdateRule.ID, daoProcess.Param.CdtVersion)
				continue
			}

			logger.CtxLogInfof(ctx, "successfully find cdt info|cdt_query=[%s], auto_rule_id=%d, auto_update_version=%d|cdt_info=[%s]",
				Logger.JsonStringForInfoLog(ctx, daoProcess.Param.CdtQuery), daoProcess.Param.AutoUpdateRule.ID, daoProcess.Param.CdtVersion, Logger.JsonStringForInfoLog(ctx, daoProcess.Result.CdtInfo))
			// 这里是获取到了CdtInfo才需要映射回去
			serviceProcess.Result.CdtInfo = daoProcess.Result.CdtInfo
		}
	}
}

// preHandle 前置数据处理，准备CDT计算需要用到的参数
func (c *CdtCalculationService) preHandle(ctx utils.LCOSContext, process *singleCdtQueryProcess) (queryLen int) {
	param := process.Param

	// get queries by location level
	queries, err := c.getQuery(
		ctx, param.ObjectType, param.ProductID, param.LaneCode, param.LaneCodeFlag, param.UpdateEvent, param.IsSiteLine,
		param.IsLM, param.IsCB, param.TplUniqueKey, param.SellerAddr, param.BuyerAddr, param.Region,
		int(param.AutoUpdateRule.OriginLocationLevel), int(param.AutoUpdateRule.DestinationLocationLevel), param.FullQuery, param.DataSource, param.needEqualLevel,
	)
	if err != nil {
		process.Result.Err = err
		return 0
	}

	// for lane auto update cdt, need to add lane code empty query
	if param.LaneCodeFlag {
		productQueries, err := c.getQuery(
			ctx, param.ObjectType, param.ProductID, "", false, param.UpdateEvent, param.IsSiteLine,
			param.IsLM, param.IsCB, param.TplUniqueKey, param.SellerAddr, param.BuyerAddr, param.Region,
			int(param.AutoUpdateRule.OriginLocationLevel), int(param.AutoUpdateRule.DestinationLocationLevel), param.FullQuery, param.DataSource, param.needEqualLevel,
		)
		if err != nil {
			process.Result.Err = err
			return 0
		}
		queries = append(queries, productQueries...)
	}

	// 找到了有效的自动规则，需要计算其cdt
	cdtVersion, err := c.autoUpdateDataDao.GetCdtVersionByAutoUpdateRuleIDUsingCache(ctx, param.AutoUpdateRule.ID)
	if err != nil {
		process.Result.Err = err
		logger.CtxLogErrorf(ctx, "cannot find cdt version|auto_update_rule=%d, error=%v", param.AutoUpdateRule.ID, err.Msg)
		return 0
	}

	process.Data = CdtQueryProcessData{
		Queries:    queries,
		CdtVersion: cdtVersion,
	}
	queryLen = len(queries)
	return queryLen
}

// preVolumeHandle 前置数据处理，准备单量查询需要用到的参数, 返回 query 数目，避免后续切片拷贝
func (c *CdtCalculationService) preVolumeHandle(ctx utils.LCOSContext, process *singleVolumeQueryProcess) (queryLen int) {
	// get volume queries by location level
	queries, err := c.getFChannelVolumeQueries(ctx, process.ProductInfo, process.Param.DataSource)
	if err != nil {
		process.Result.Err = err
		return 0
	}

	process.Data = VolumeQueryProcessData{
		Queries:       queries,
		VolumeVersion: process.Param.VolumeVersion,
	}
	queryLen = len(queries)
	// 初始化
	process.Result = VolumeQueryResult{
		VolumeInfos: make([]*cdt_calculation.VolumeInfo, 0, queryLen),
		Err:         nil,
	}
	return queryLen
}

func (c *CdtCalculationService) postEqualLevelHandle(ctx utils.LCOSContext, process *singleCdtQueryProcess) {
	param := process.Param

	for i, oriCdtInfo := range process.Result.CdtInfos {
		if oriCdtInfo == nil {
			continue
		}
		//logger.CtxLogDebugf(ctx, "successfully find cdt info|cdt_query_src_level=[%d],cdt_query_dest_level=[%d], auto_rule_id=%d, auto_update_version=%d|cdt_info_min=[%f],cdt_info_max=[%f]",
		//	process.GetOriginLevelWithIndex(i), process.GetOriginLevelWithIndex(i), process.GetAutoUpdateRuleId(), process.GetCdtVersion(), oriCdtInfo.LeadTimeMin, oriCdtInfo.LeadTimeMax)

		if err := c.cdtManualManipulate(
			ctx, param.ObjectType, oriCdtInfo, param.ProductID, param.LaneCode, param.LaneCodeFlag,
			param.UpdateEvent, param.IsSiteLine, param.IsLM, param.IsCB, param.Region, process.Param.TplUniqueKey,
			param.SellerAddr, param.BuyerAddr, param.DataSource, constant.NotNeedEqualLevels, param.RequestTime,
			param.CdtScene,
		); err != nil {
			errMsg := fmt.Sprintf("cdt manual manipulate error: %s", err.Msg)
			// logger.CtxLogErrorf(ctx, errMsg)
			process.Result.Err = lcos_error.NewLCOSError(err.RetCode, errMsg)
		}
		if oriCdtInfo.CdtProcess != nil {
			oriCdtInfo.CdtProcess.CdtQueryInfo = process.Data.Queries[i]
		}
	}
}

func (c *CdtCalculationService) postVolumeHandle(ctx utils.LCOSContext, process *singleVolumeQueryProcess) {
	if len(process.Result.VolumeInfos) != len(process.Data.Queries) {
		logger.CtxLogErrorf(ctx, "")
		return
	}

	for i, oriVolumeInfo := range process.Result.VolumeInfos {
		if oriVolumeInfo == nil {
			continue
		}

		query := process.Data.Queries[i]
		oriVolumeInfo.OriginLocationLevel = query.OriginLocationLevel
		oriVolumeInfo.DestinationLocationLevel = query.DestinationLocationLevel

		logger.CtxLogInfof(ctx, "successfully find volume info|volume_query_src_level=[%d], volume_query_dest_level=[%d], auto_volume_id=%d, auto_volume_version=%d|volume_info=[%d]",
			oriVolumeInfo.OriginLocationLevel, oriVolumeInfo.DestinationLocationLevel, process.GetAutoVolumeUpdateRuleId(), process.GetVolumeVersion(), oriVolumeInfo.Volume)

	}
}

// postHandle 后置处理，包括查不出来的Error处理和Manual调整
func (c *CdtCalculationService) postHandle(ctx utils.LCOSContext, process *singleCdtQueryProcess) {
	param := process.Param

	// 既计算不出Cdt也没Error的情况，赋一个Not Found的Error
	if process.Result.CdtInfo == nil && process.Result.Err == nil {
		errMsg := fmt.Sprintf("query cdt info failed|product_id=%v, is_site_line=%v,is_lm=%v",
			param.ProductID, param.IsSiteLine, param.IsLM)
		logger.CtxLogInfof(ctx, errMsg)
		process.Result.Err = lcos_error.NewLCOSError(lcos_error.NotFoundCdtInfoErrorCode, errMsg)
		return
	}

	// 如果Auto计算不出有Error的则不需要进行Manual调整
	if process.Result.Err != nil {
		return
	}

	if err := c.cdtManualManipulate(
		ctx, param.ObjectType, process.Result.CdtInfo, param.ProductID, param.LaneCode, param.LaneCodeFlag, // nolint
		param.UpdateEvent, param.IsSiteLine, param.IsLM, param.IsCB, param.Region, process.Param.TplUniqueKey,
		param.SellerAddr, param.BuyerAddr, param.DataSource, constant.NotNeedEqualLevels, param.RequestTime,
		param.CdtScene,
	); err != nil {
		errMsg := fmt.Sprintf("cdt manual manipulate error: %s", err.Msg)
		logger.CtxLogErrorf(ctx, errMsg)
		process.Result.Err = lcos_error.NewLCOSError(err.RetCode, errMsg)
	}
}

func reportBatchItemCdt(err *lcos_error.LCOSError, updateEvent uint8, region string, productID string, laneCode string) {
	var (
		errorCode    = "0"
		errorMessage = ""
	)
	if err != nil {
		errorCode = fmt.Sprintf("%d", err.RetCode)
		errorMessage = err.Msg
	}
	updateEventString, _ := edd_constant.GetUpdateEventString(updateEvent)
	_ = metrics.CounterIncr(constant.MetricsBatchItemCdtReport, map[string]string{
		"func":          constant.CalcNonAutoCdt,
		"region":        strings.ToLower(region),
		"product_id":    productID,
		"update_event":  updateEventString,
		"lane_code":     laneCode,
		"error_code":    errorCode,
		"error_message": errorMessage,
	})
}

func getUniqueCdtInfoKey(originLocationLevel int8, destinationLocationId int8, updateEvent uint8) string {
	return utils.GenNormalKey(":", originLocationLevel, destinationLocationId, updateEvent)
}

func checkIfNeedAppend(originLocationLevel int8, destinationLocationId int8, updateEvent uint8, filterMap map[string]bool) bool {
	filterKey := getUniqueCdtInfoKey(originLocationLevel, destinationLocationId, updateEvent)
	if ok := filterMap[filterKey]; ok {
		return false
	}
	if updateEvent == edd_constant.ShippedOut {
		for _, updateEventTemp := range edd_constant.AllShippedOutEventEnumList {
			filterKey = getUniqueCdtInfoKey(originLocationLevel, destinationLocationId, updateEventTemp)
			if ok := filterMap[filterKey]; ok {
				return false
			}
		}
	}
	filterMap[filterKey] = true
	return true
}
