package edd_forecast_task

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_forecast_task"
	auto_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	edd_auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	edd_forecast_result2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_result"
	edd_forecast_task2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_task"
	cdt_common2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/data_ssc"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
)

type EDDForecastTaskInterface interface {
	CreateForecastTask(ctx utils.LCOSContext, request *edd_forecast_task.CreateEDDForecastTaskRequest) (interface{}, *lcos_error.LCOSError)
	ListEDDForecastTasks(ctx utils.LCOSContext, request *edd_forecast_task.ListEDDForecastTaskRequest) (interface{}, *lcos_error.LCOSError)
	DetailEDDForecastTask(ctx utils.LCOSContext, request *edd_forecast_task.DetailEDDForecastTaskRequest) (interface{}, *lcos_error.LCOSError)
	GetForecastTaskDeployInfo(ctx utils.LCOSContext, forecastTaskID uint64, deployRuleID uint32) (*edd_forecast_task2.EDDForecastTaskTab, *edd_forecast_task2.LeadConfig, *edd_forecast_task2.EDDAutoUpdateConfig, string, *lcos_error.LCOSError)
	UpdateEDDForecastTaskAfterDeployed(ctx utils.LCOSContext, forecastTask *edd_forecast_task2.EDDForecastTaskTab, toggleRuleID uint32, toggleRuleName string, deployOption uint8) (*edd_forecast_task.ExtraDeployInfo, *lcos_error.LCOSError)
}

type eddForecastTask struct {
	eddForecastTaskDao   edd_forecast_task2.EDDForecastTaskDao
	eddForecastResultDao edd_forecast_result2.EDDForecastResultDao
	autoUpdateRuleDao    auto_rule2.CDTAutoUpdateRuleTabDAO
	eddAutoUpdateRuleDao edd_auto_update_rule2.EddAutoUpdateRuleDao
}

// parse data from database to request to data
func parseEDDAutoUpdateListToRequestString(eddAutoUpdateList edd_forecast_task2.EDDAutoUpdateConfigList) string {
	eddAutoUpdateListRequest := make([]*data_ssc.SingleUpdateEvent, 0)
	for _, singleRule := range eddAutoUpdateList {
		for _, singleEvent := range singleRule.UpdateEvents {
			eddAutoUpdateListRequest = append(eddAutoUpdateListRequest, &data_ssc.SingleUpdateEvent{
				Event:               singleEvent.Event,
				AllowPreemptUpdate:  singleEvent.PreemptLateParcels,
				DeadlineMethod:      singleEvent.DeadlineMethod,
				ConfidenceLevel:     singleEvent.GetConfidenceLevel(),
				UpdateCondition:     singleEvent.UpdateCondition,
				MinValue:            singleEvent.MinValue,
				UpdateMethod:        singleEvent.UpdateMethod,
				MinAllowExtend:      singleEvent.GetMinAllowExtend(),
				MinAllowDeduct:      singleEvent.GetMinAllowDeduct(),
				MaxAllowExtend:      singleEvent.GetMaxAllowExtend(),
				MaxAllowDeduct:      singleEvent.GetMaxAllowDeduct(),
				RuleID:              strconv.Itoa(int(singleRule.RuleID)),
				MaxTimes:            singleRule.MaxTimes,
				EDDMinThresholdMin:  singleRule.EddMinThresholdMin,
				EDDMinThresholdMax:  singleRule.EddMinThresholdMax,
				EDDMaxThresholdMin:  singleRule.EddMaxThresholdMin,
				EDDMaxThresholdMax:  singleRule.EddMaxThresholdMax,
				CheckpointFrequency: singleRule.CheckpointFrequency,
			})
		}
	}
	return utils.MarshToStringWithoutError(eddAutoUpdateListRequest)
}

func parseTargetString(target *edd_forecast_task2.ForecastTargets) string {
	dataTarget := &data_ssc.ForecastTargets{
		InitialAccuracy: data_ssc.ForecastTargetMetrics{
			Metrics:  target.InitialAccuracy.GetMetrics(),
			Priority: target.InitialAccuracy.Priority,
		},
		InitialPrecision: data_ssc.ForecastTargetMetrics{
			Metrics:  target.InitialPrecision.GetMetrics(),
			Priority: target.InitialPrecision.Priority,
		},
		FinalAccuracy: data_ssc.ForecastTargetMetrics{
			Metrics:  target.FinalAccuracy.GetMetrics(),
			Priority: target.FinalAccuracy.Priority,
		},
		FinalPrecision: data_ssc.ForecastTargetMetrics{
			Metrics:  target.FinalPrecision.GetMetrics(),
			Priority: target.FinalPrecision.Priority,
		},
		AverageInitialEDDRange: data_ssc.ForecastTargetMetrics{
			Metrics:   target.AverageInitialEDDRange.GetMetrics(),
			Priority:  target.AverageInitialEDDRange.Priority,
			RangeDays: target.AverageInitialEDDRange.RangeDays,
		},
		AverageFinalEDDRange: data_ssc.ForecastTargetMetrics{
			Metrics:   target.AverageFinalEDDRange.GetMetrics(),
			Priority:  target.AverageFinalEDDRange.Priority,
			RangeDays: target.AverageFinalEDDRange.RangeDays,
		},
	}
	return utils.MarshToStringWithoutError(dataTarget)
}

func parseEdtTargetString(target *edd_forecast_task2.EdtForecastTargets) string {
	dataTarget := &data_ssc.ForecastEdtTargets{
		EDTAccuracy: data_ssc.ForecastTargetMetrics{
			Metrics:  target.EDTAccuracy.GetMetrics(),
			Priority: target.EDTAccuracy.Priority,
		},
		EDTPrecision: data_ssc.ForecastTargetMetrics{
			Metrics:  target.EDTPrecision.GetMetrics(),
			Priority: target.EDTPrecision.Priority,
		},
		AverageEdtRange: data_ssc.ForecastTargetMetrics{
			Metrics:   target.AverageEdtRange.GetMetrics(),
			Priority:  target.AverageEdtRange.Priority,
			RangeDays: target.AverageEdtRange.RangeDays,
		},
	}
	return utils.MarshToStringWithoutError(dataTarget)
}

func (e *eddForecastTask) CreateForecastTask(ctx utils.LCOSContext, request *edd_forecast_task.CreateEDDForecastTaskRequest) (interface{}, *lcos_error.LCOSError) {
	// 1. valid forecast rule
	// open transaction
	// 2. store in db
	// 3. request DATA api
	// end transaction
	region := strings.ToUpper(ctx.GetCountry())
	operator := ctx.GetUserEmail()

	// 当仿真edt时， 用户只能选择使用新配置且使用系统推荐规则 且必须计算CDTMin
	if request.ObjectType == edd_constant.CdtObject && (request.DataSource != edd_constant.UseNewSettings || request.Method != edd_constant.SystemRecommendsForecastRules || request.LeadConfig.CalculateEddMin != constant.TRUE) {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "When forecasting edt, the user can only choose to use the new configuration and use the system recommended rules|product_id=%s", request.ProductID)
	}

	maxEDDAutoUpdateRulesCount := config2.GetMaxEDDAutoUpdateRulesCountInOneForecastTask(ctx)
	if len(request.EDDAutoUpdateConfigList) > maxEDDAutoUpdateRulesCount {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot create edd auto update rule list more than %d", maxEDDAutoUpdateRulesCount)
	}
	if len(request.EDDAutoUpdateConfigList) <= 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "edd auto update rule list cannot be empty")
	}
	for _, singleRule := range request.EDDAutoUpdateConfigList {
		if errMsg := singleRule.UpdateEvents.CheckValid(); len(errMsg) > 0 {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "update rule:[%d] is not valid|error_msg=[%s]", singleRule.RuleID, errMsg)
		}
	}

	if lcosErr := e.checkDayGroup(ctx, request.LeadConfig.EventTimeLevel, request.LeadConfig.DayGroup); lcosErr != nil {
		return nil, lcosErr
	}

	// check data source, for Use-Existing-Setting, need to search DB for lead auto update rule
	var autoUpdateRule *auto_rule2.CDTAutoUpdateRuleTab
	if request.DataSource == edd_constant.UseExistingSettings {
		// SPLN-32530 目前cdt_type和object_type 含义一致，统一使用ObjectType来区分EDD/EDT
		autoUpdateRules, lcosErr := e.autoUpdateRuleDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"product_id": request.ProductID, "cdt_type": request.ObjectType, "status_id": constant.Active})
		if lcosErr != nil || len(autoUpdateRules) <= 0 {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find active lead auto update rule|product_id=%s", request.ProductID)
		}
		autoUpdateRule = autoUpdateRules[0]
		// set auto update rule to lead config
		request.LeadConfig = edd_forecast_task2.LeadConfig{
			ID:                          autoUpdateRule.ID,
			OriginLocationLevel:         autoUpdateRule.OriginLocationLevel,
			DestinationLocationLevel:    autoUpdateRule.DestinationLocationLevel,
			DestinationCepRangeFileName: autoUpdateRule.DestinationCepRangeFileName,
			DestinationPostcodeFileName: autoUpdateRule.DestinationPostcodeFileName,
			DestinationCepRangeUrl:      autoUpdateRule.GenerateDuplicatedUrl(),
			DestinationPostcodeUrl:      autoUpdateRule.GenerateDuplicatedUrl(),
			TimePeriod:                  autoUpdateRule.TimePeriod,
			ExcludedDaysArray:           auto_update_rule.GetExcludedDaysArray(autoUpdateRules[0].ExcludedDays),
			ThresholdNum:                autoUpdateRule.ThresholdNum,
			Frequency:                   autoUpdateRule.Frequency,
			MinPercentile:               autoUpdateRule.MinPercentile,
			MaxPercentile:               autoUpdateRule.MaxPercentile,
			RemoveFlag:                  autoUpdateRule.RemoveFlag,
			RemoveThresholdNum:          autoUpdateRule.RemoveThresholdNum,
			CalculateEddMin:             autoUpdateRule.CalculateEddMin,
			EddRangeLimit:               autoUpdateRule.EddRangeLimit,
			EventTimeLevel:              autoUpdateRule.EventTimeLevel,
			DayGroup:                    autoUpdateRule.DayGroup,
		}
	}
	// SPLN-30795
	if request.Method == edd_constant.SystemRecommendsForecastRules {
		if request.DataSource == edd_constant.UseExistingSettings {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "for system recommend, users are not allowed to use existing setting|product_id=%s", request.ProductID)
		}
		// for system recommend, need to set edd rule to default
		for i := 0; i < len(request.EDDAutoUpdateConfigList); i++ {
			for j := 0; j < len(request.EDDAutoUpdateConfigList[i].UpdateEvents); j++ {
				if request.EDDAutoUpdateConfigList[i].UpdateEvents[j].PreemptLateParcels == constant.TRUE {
					request.EDDAutoUpdateConfigList[i].UpdateEvents[j].SetToDefault()
				}
			}
		}
	}

	if request.LeadConfig.CalculateEddMin == constant.FALSE {
		request.LeadConfig.MinPercentile = 0
		request.LeadConfig.EddRangeLimit = 0
	}

	// store in db and call data api
	// sort update event
	request.EDDAutoUpdateConfigList.SortAllUpdateEvens(ctx)
	forecastTaskTab := &edd_forecast_task2.EDDForecastTaskTab{
		Region:                  region,
		ProductId:               request.ProductID,
		ProductName:             request.ProductName,
		CBType:                  request.CBType,
		MaskingType:             request.MaskingType,
		IntegratedType:          request.IntegratedType,
		TaskName:                request.TaskName,
		TestSetTimePeriodStart:  request.TestSetTimePeriodStart,
		TestSetTimePeriodEnd:    request.TestSetTimePeriodEnd,
		DataSource:              request.DataSource,
		LeadConfig:              request.LeadConfig,
		EddAutoUpdateConfigList: request.EDDAutoUpdateConfigList,
		TaskOperator:            operator,
		ForecastStatus:          edd_constant.Processing, // init to processing status
		Method:                  request.Method,
		ObjectType:              request.ObjectType,
	}
	// generate data api request
	dataRequest := &data_ssc.ForecastRunJob{
		ProductID:                forecastTaskTab.ProductId,
		ForecastTaskID:           forecastTaskTab.ID,
		IsCB:                     forecastTaskTab.CBType,
		MaskingType:              forecastTaskTab.MaskingType,
		OriginLocationLevel:      forecastTaskTab.LeadConfig.OriginLocationLevel,
		DestinationLocationLevel: forecastTaskTab.LeadConfig.DestinationLocationLevel,
		Frequency:                forecastTaskTab.LeadConfig.Frequency,
		MinPercentile:            forecastTaskTab.LeadConfig.GetMinPercentile(),
		MaxPercentile:            forecastTaskTab.LeadConfig.GetMaxPercentile(),
		ThresholdNum:             forecastTaskTab.LeadConfig.ThresholdNum,
		ExcludedDays:             auto_update_rule.GetExcludedDaysStr(forecastTaskTab.LeadConfig.ExcludedDaysArray),
		Region:                   region,
		TimePeriod:               forecastTaskTab.LeadConfig.TimePeriod,
		RemoveFlag:               forecastTaskTab.LeadConfig.RemoveFlag,
		RemoveThresholdNum:       forecastTaskTab.LeadConfig.RemoveThresholdNum,
		TestSetTimePeriodStart:   request.TestSetTimePeriodStart,
		TestSetTimePeriodEnd:     request.TestSetTimePeriodEnd,
		CdtType:                  forecastTaskTab.ObjectType,
		UpdateEventRuleList:      parseEDDAutoUpdateListToRequestString(forecastTaskTab.EddAutoUpdateConfigList),
	}

	// handle cep range and postcode list
	var cepRangeString, postcodeString string
	var postcodeFlag uint8
	if forecastTaskTab.LeadConfig.DestinationLocationLevel == constant.CepRange {
		postcodeFlag = 2
		var cepModels []*cdt_common2.CepRange
		var lcosErr *lcos_error.LCOSError
		if request.DataSource == edd_constant.UseExistingSettings {
			cepModels = auto_rule2.ParseFromCepRangeString(autoUpdateRule.DestinationCepRange)
		} else {
			cepModels, lcosErr = cdt_common2.ParseCepFile(ctx, forecastTaskTab.LeadConfig.DestinationCepRangeUrl, config2.GetCdtMaxAutoUpdateCepRangeCount(ctx))
		}
		// parse url
		if lcosErr != nil {
			return nil, lcosErr
		}
		// parse models to string
		var cepRangeList []string
		for _, cepModel := range cepModels {
			cepRangeList = append(cepRangeList, fmt.Sprintf("%v-%v", cepModel.CepLeft, cepModel.CepRight))
		}
		cepRangeString = strings.Join(cepRangeList, "#")
		dataRequest.CepRange = cepRangeString
	} else if forecastTaskTab.LeadConfig.DestinationLocationLevel == constant.CDTPostcode {
		postcodeFlag = 3
		var postcodeList []string
		var lcosErr *lcos_error.LCOSError
		if request.DataSource == edd_constant.UseExistingSettings {
			postcodeStructList, postcodeErr := e.autoUpdateRuleDao.GetAutoUpdateRulePostcodesByID(ctx, autoUpdateRule.ID)
			if postcodeErr != nil {
				return nil, postcodeErr
			}
			for _, singlePostcode := range postcodeStructList {
				postcodeList = append(postcodeList, singlePostcode.PostcodeLeft)
			}
		} else {
			postcodeList, lcosErr = cdt_common2.DownloadAndParsePostcode(ctx, forecastTaskTab.LeadConfig.DestinationPostcodeUrl)
		}
		if lcosErr != nil {
			return nil, lcosErr
		}
		postcodeString = strings.Join(postcodeList, ",")
		dataRequest.PostcodeList = postcodeString
	} else {
		postcodeFlag = 1
	}
	dataRequest.PostcodeFlag = uint8(postcodeFlag)

	// SPLN-30795
	if request.Method == edd_constant.SystemRecommendsForecastRules {
		if request.ObjectType == edd_constant.LeadTimeObject {
			if request.Targets == nil {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "targets are required when method is system recommend")
			}
			dataRequest.Targets = utils.NewString(parseTargetString(request.Targets))
		}

		if request.ObjectType == edd_constant.CdtObject {
			if request.EdtTargets == nil {
				return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "edt targets are required when method is system recommend")
			}
			dataRequest.EdtTargets = utils.NewString(parseEdtTargetString(request.EdtTargets))
		}

		dataRequest.Method = utils.NewUint8(request.Method)
		dataRequest.EventTimeLevel = utils.NewUint8(forecastTaskTab.LeadConfig.EventTimeLevel)
		dataRequest.DayGroup = utils.NewString(forecastTaskTab.LeadConfig.DayGroup)
		forecastTaskTab.TriggerJson = utils.MarshToStringWithoutError(dataRequest)
		forecastTaskTab.Targets = edd_forecast_task2.ParseToTargetString(request.Targets)
		forecastTaskTab.EdtTargets = edd_forecast_task2.ParseToEdtTargetString(request.EdtTargets)
	}

	fc := func() *lcos_error.LCOSError {
		lcosErr := e.eddForecastTaskDao.CreateEDDForecastTask(ctx, forecastTaskTab)
		if lcosErr != nil {
			return lcosErr
		}
		// add forecast task id
		if request.Method == edd_constant.UserDefinesForecastRules { // SPLN-30795 only for user define rules
			dataRequest.ForecastTaskID = forecastTaskTab.ID
			_, lcosErr = data_ssc.TriggerForecastJobTask(ctx, dataRequest)
			if lcosErr != nil {
				return lcosErr
			}
		}
		return nil
	}
	return nil, ctx.Transaction(fc)
}

func (e *eddForecastTask) ListEDDForecastTasks(ctx utils.LCOSContext, request *edd_forecast_task.ListEDDForecastTaskRequest) (interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())

	var pageNo uint32 = 1
	var pageCount uint32 = 10
	if request.PageNo != nil {
		pageNo = *request.PageNo
	}
	if request.Count != nil {
		pageCount = *request.Count
	}

	queryMap := map[string]interface{}{
		"region": region,
	}
	if request.ProductID != nil {
		queryMap["product_id"] = *request.ProductID
	}
	if request.ForecastStatus != nil {
		queryMap["forecast_status"] = *request.ForecastStatus
	}
	if request.DeployStatus != nil {
		queryMap["deploy_status"] = *request.DeployStatus
	}

	models, total, lcosErr := e.eddForecastTaskDao.ListEDDForecastTaskPaging(ctx, queryMap, pageNo, pageCount)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return map[string]interface{}{
		"page_no": pageNo,
		"count":   pageCount,
		"total":   total,
		"list":    models,
	}, nil
}

func (e *eddForecastTask) DetailEDDForecastTask(ctx utils.LCOSContext, request *edd_forecast_task.DetailEDDForecastTaskRequest) (interface{}, *lcos_error.LCOSError) {
	models, lcosErr := e.eddForecastTaskDao.SearchEDDForecastTasks(ctx, map[string]interface{}{"id": request.ID})
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(models) <= 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find edd auto update|id=%d", request.ID))
	}
	return models[0], nil
}

func findEDDAutoUpdateRule(eddAutoUpdateList edd_forecast_task2.EDDAutoUpdateConfigList, eddAutoUpdateRuleID uint32) *edd_forecast_task2.EDDAutoUpdateConfig {
	for _, singleRule := range eddAutoUpdateList {
		if singleRule.RuleID == eddAutoUpdateRuleID {
			return singleRule
		}
	}
	return nil
}

func parseRuleFromForecastResults(forecastTask *edd_forecast_task2.EDDForecastTaskTab, forecastResults []*edd_forecast_result2.EDDForecastResultTab, ruleID uint32) (*edd_forecast_task2.LeadConfig, *edd_forecast_task2.EDDAutoUpdateConfig, *lcos_error.LCOSError) {

	var forecastResult *edd_forecast_result2.EDDForecastResultTab

	// find the deployed result
	for _, singleResult := range forecastResults {
		if uint32(singleResult.RuleID) == ruleID {
			forecastResult = singleResult
			break
		}
	}
	if forecastResult == nil {
		return nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find forecast result by rule id:[%d]", ruleID)
	}

	leadTimeInfo := forecastResult.ForecastResult.LeadTimeInfo
	var rawEddAutoInfo []*data_ssc.SingleUpdateEvent
	if forecastResult.ForecastResult.EDDUpdateRule != nil {
		rawEddAutoInfo = forecastResult.ForecastResult.EDDUpdateRule.UpdateEventRuleList
	}
	if leadTimeInfo == nil {
		return nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find lead time info by rule id:[%d]", ruleID)
	}
	if len(rawEddAutoInfo) <= 0 {
		return nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find edd auto update rule info by rule id:[%d]", ruleID)
	}

	leadConfig := forecastTask.LeadConfig
	leadConfig.UUID = leadTimeInfo.UUID
	leadConfig.DataVersion = leadTimeInfo.DataVersion

	// parse the edd auto update rule
	returnedEddRule := forecastTask.EddAutoUpdateConfigList[0]
	returnedEddRule.RuleID = uint32(forecastResult.ForecastResult.RuleId)
	returnedEddRule.RuleName = forecastResult.ForecastResult.RuleName
	forecastResultMap := make(map[uint8]*data_ssc.SingleUpdateEvent)
	for _, tmpRule := range forecastResult.ForecastResult.EDDUpdateRule.UpdateEventRuleList { // nolint
		forecastResultMap[tmpRule.Event] = tmpRule
	}
	for i := 0; i < len(returnedEddRule.UpdateEvents); i++ {
		if resultRule, ok := forecastResultMap[returnedEddRule.UpdateEvents[i].Event]; !ok {
			return nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "event:[%d] exist in the forecast task, but not in forecast task result", returnedEddRule.UpdateEvents[i].Event)
		} else {
			returnedEddRule.UpdateEvents[i].DeadlineMethod = resultRule.DeadlineMethod
			returnedEddRule.UpdateEvents[i].ConfidenceLevel = resultRule.GetConfidenceLevel()
			returnedEddRule.UpdateEvents[i].UpdateCondition = resultRule.UpdateCondition
			returnedEddRule.UpdateEvents[i].UpdateMethod = resultRule.UpdateMethod
		}
	}

	return &leadConfig, returnedEddRule, nil

}

func (e *eddForecastTask) GetForecastTaskDeployInfo(ctx utils.LCOSContext, forecastTaskID uint64, deployRuleID uint32) (*edd_forecast_task2.EDDForecastTaskTab, *edd_forecast_task2.LeadConfig, *edd_forecast_task2.EDDAutoUpdateConfig, string, *lcos_error.LCOSError) {
	// 1. Check Forecast Task, if forecast status is not success, not allowed to deploy
	// 2. Check EDDAutoUpdate Rule, cannot be forward
	// 3. Return deploy info

	var leadConfig *edd_forecast_task2.LeadConfig
	var eddAutoUpdateRule *edd_forecast_task2.EDDAutoUpdateConfig
	var lcosErr *lcos_error.LCOSError
	var ruleName string

	region := strings.ToUpper(ctx.GetCountry())

	forecastTasks, lcosErr := e.eddForecastTaskDao.SearchEDDForecastTasks(ctx, map[string]interface{}{"id": forecastTaskID, "region": region})
	if lcosErr != nil || len(forecastTasks) <= 0 {
		return nil, nil, nil, "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "cannot find edd forecast task by id:[%d]", forecastTaskID)
	}
	forecastTask := forecastTasks[0]
	if forecastTask.DeployStatus != edd_constant.PendingDeploy {
		return nil, nil, nil, "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "forecast task has deployed|task_id=[%d]", forecastTaskID)
	}

	if forecastTask.ObjectType == edd_constant.LeadTimeObject {
		_, leadConfig, eddAutoUpdateRule, lcosErr = e.GetEDDForecastTaskDeployInfo(ctx, forecastTask, forecastTaskID, deployRuleID)
		ruleName = eddAutoUpdateRule.RuleName
	} else {
		var forecastResult *edd_forecast_result2.EDDForecastResultTab
		leadConfig, forecastResult, lcosErr = e.GetEDTForecastTaskDeployInfo(ctx, forecastTask, deployRuleID)
		ruleName = forecastResult.ForecastResult.RuleName
	}
	if lcosErr != nil {
		return nil, nil, nil, "", lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "get deploy info fail|task_id=[%d]|deploy_id=[%d]|err=[%v]", forecastTaskID, deployRuleID, lcosErr.Msg)
	}

	return forecastTask, leadConfig, eddAutoUpdateRule, ruleName, nil
}

func (e *eddForecastTask) GetEDDForecastTaskDeployInfo(ctx utils.LCOSContext, forecastTask *edd_forecast_task2.EDDForecastTaskTab, forecastTaskID uint64, deployRuleID uint32) (*edd_forecast_task2.EDDForecastTaskTab, *edd_forecast_task2.LeadConfig, *edd_forecast_task2.EDDAutoUpdateConfig, *lcos_error.LCOSError) {
	// 1. Check Forecast Task, if forecast status is not success, not allowed to deploy
	// 2. Check EDDAutoUpdate Rule, cannot be forward
	// 3. Return deploy info

	var leadConfig *edd_forecast_task2.LeadConfig
	var eddAutoUpdateRule *edd_forecast_task2.EDDAutoUpdateConfig
	var lcosErr *lcos_error.LCOSError

	region := strings.ToUpper(ctx.GetCountry())
	if forecastTask.IsUserDefineTask() {
		eddAutoUpdateRule = findEDDAutoUpdateRule(forecastTask.EddAutoUpdateConfigList, deployRuleID)
		leadConfig = &forecastTask.LeadConfig
	} else if forecastTask.IsSystemRecommendTask() {
		// find results first and find edd auto update and lead config
		forecastResults, lcosErr := e.eddForecastResultDao.SearchEDDForecastResults(ctx, map[string]interface{}{"forecast_task_id": forecastTaskID, "region": region})
		if lcosErr != nil {
			return nil, nil, nil, lcosErr
		}

		var parseRuleError *lcos_error.LCOSError
		leadConfig, eddAutoUpdateRule, parseRuleError = parseRuleFromForecastResults(forecastTask, forecastResults, deployRuleID)
		if parseRuleError != nil {
			return nil, nil, nil, parseRuleError
		}
	}
	if eddAutoUpdateRule == nil {
		return nil, nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find edd auto rule|forecast_task_id=[%d], rule_id=[%d]", forecastTaskID, deployRuleID)
	}
	if eddAutoUpdateRule.UpdateEvents.ContainsPreemptiveConfig() && !utils.ContainsUint32(edd_constant.AllowedCheckpointList, eddAutoUpdateRule.CheckpointFrequency) {
		return nil, nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "checkpoint_frequency:[%d] is not allowed when enable preemptive config|forecast_task_id=[%d], edd_auto_update_id=[%d]", eddAutoUpdateRule.CheckpointFrequency, forecastTaskID, deployRuleID)
	}

	// validate deploy info

	// check edd auto update rule whether is error
	if errMsg := eddAutoUpdateRule.UpdateEvents.CheckContainsErrorMethod(); len(errMsg) > 0 {
		return nil, nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "%s|forecast_task_id=[%d], edd_auto_update_id=[%d]", errMsg, forecastTaskID, deployRuleID)
	}

	// check forecast status of edd auto update rule
	results, lcosErr := e.eddForecastResultDao.SearchEDDForecastResults(ctx, map[string]interface{}{"forecast_task_id": forecastTask.ID, "rule_id": deployRuleID})
	if lcosErr != nil || len(results) <= 0 || results[0].RuleStatus != edd_constant.Success {
		return nil, nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "the forecast status of rule is not valid|forecast_task_id=%d, rule_id=%d", forecastTask.ID, deployRuleID)
	}

	return forecastTask, leadConfig, eddAutoUpdateRule, nil
}

func (e *eddForecastTask) GetEDTForecastTaskDeployInfo(ctx utils.LCOSContext, forecastTask *edd_forecast_task2.EDDForecastTaskTab, deployRuleID uint32) (*edd_forecast_task2.LeadConfig, *edd_forecast_result2.EDDForecastResultTab, *lcos_error.LCOSError) {
	// 1. Check Forecast Task, if forecast status is not success, not allowed to deploy
	// 2. Check EDDAutoUpdate Rule, cannot be forward
	// 3. Return deploy info

	var leadConfig *edd_forecast_task2.LeadConfig
	var lcosErr *lcos_error.LCOSError

	// check forecast status of edd auto update rule
	results, lcosErr := e.eddForecastResultDao.SearchEDDForecastResults(ctx, map[string]interface{}{"forecast_task_id": forecastTask.ID, "rule_id": deployRuleID})
	if lcosErr != nil || len(results) <= 0 || results[0].RuleStatus != edd_constant.Success {
		return nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "the forecast status of rule is not valid|forecast_task_id=%d, rule_id=%d", forecastTask.ID, deployRuleID)
	}

	var forecastResult *edd_forecast_result2.EDDForecastResultTab

	// find the deployed result
	for _, singleResult := range results {
		if uint32(singleResult.RuleID) == deployRuleID {
			forecastResult = singleResult
			break
		}
	}
	if forecastResult == nil {
		return nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find forecast result by rule id:[%d]", deployRuleID)
	}

	leadTimeInfo := forecastResult.ForecastResult.LeadTimeInfo
	if leadTimeInfo == nil {
		return nil, nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find lead time info by rule id:[%d]", deployRuleID)
	}

	leadConfig = &forecastTask.LeadConfig
	leadConfig.UUID = leadTimeInfo.UUID
	leadConfig.DataVersion = leadTimeInfo.DataVersion

	return leadConfig, forecastResult, nil
}

func (e *eddForecastTask) UpdateEDDForecastTaskAfterDeployed(ctx utils.LCOSContext, forecastTask *edd_forecast_task2.EDDForecastTaskTab, toggleRuleID uint32, toggleRuleName string, deployOption uint8) (*edd_forecast_task.ExtraDeployInfo, *lcos_error.LCOSError) {
	// 1. Get LeadAutoUpdateRule and EDDAutoUpdateRule
	// 2. Update ForecastTask status and extra info
	nowTime := utils.GetTimestamp(ctx)

	autoRules, lcosErr := e.autoUpdateRuleDao.SearchAutoUpdateRule(ctx, map[string]interface{}{"from_forecast_task_id": forecastTask.ID})
	if lcosErr != nil || len(autoRules) <= 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "deploy failed, cannot find lead auto update rule by forecast_task_id:[%d]", forecastTask.ID)
	}
	autoRule := autoRules[0]

	var eddAutoUpdateRuleID uint64
	if forecastTask.ObjectType == edd_constant.LeadTimeObject {
		eddRules, lcosErr := e.eddAutoUpdateRuleDao.ListAllEddAutoUpdateRules(ctx, map[string]interface{}{"from_forecast_task_id": forecastTask.ID})
		if lcosErr != nil || len(eddRules) <= 0 {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "deploy failed, cannot find edd auto update rule by forecast_task_id:[%d]", forecastTask.ID)
		}
		eddAutoUpdateRuleID = eddRules[0].Id
	}

	if forecastTask.IsUserDefineTask() && forecastTask.ObjectType == edd_constant.LeadTimeObject {
		toggleRule := findEDDAutoUpdateRule(forecastTask.EddAutoUpdateConfigList, toggleRuleID)
		if toggleRule == nil {
			return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot find edd auto update rule|forecast_task_id=%d, edd_auto_update_id=%d", forecastTask.ID, toggleRuleID)
		}
	}
	deployDetail := edd_forecast_task2.DeployDetail{
		ProductID:        autoRule.ProductID,
		ProductName:      autoRule.ProductName,
		ToggleRuleID:     toggleRuleID,
		ToggleRuleName:   toggleRuleName,
		EDDAutoUpdateID:  eddAutoUpdateRuleID,
		LeadAutoUpdateID: autoRule.ID,
	}
	return &edd_forecast_task.ExtraDeployInfo{
		EDDAutoUpdateRuleID:  eddAutoUpdateRuleID,
		LeadAutoUpdateRuleID: autoRule.ID,
	}, e.eddForecastTaskDao.UpdateEDDForecastTasks(ctx, map[string]interface{}{"id": forecastTask.ID}, map[string]interface{}{"deploy_status": edd_constant.Deployed, "deploy_option": deployOption, "deploy_complete_time": nowTime, "deploy_detail": deployDetail, "deploy_operator": ctx.GetUserEmail()})
}

func NewEDDForecastTask(eddForecastTaskDao edd_forecast_task2.EDDForecastTaskDao, eddForecastResultDao edd_forecast_result2.EDDForecastResultDao, autoUpdateRuleDao auto_rule2.CDTAutoUpdateRuleTabDAO, eddAutoUpdateRuleDao edd_auto_update_rule2.EddAutoUpdateRuleDao) *eddForecastTask {
	return &eddForecastTask{
		eddForecastTaskDao:   eddForecastTaskDao,
		eddForecastResultDao: eddForecastResultDao,
		autoUpdateRuleDao:    autoUpdateRuleDao,
		eddAutoUpdateRuleDao: eddAutoUpdateRuleDao,
	}
}

var _ EDDForecastTaskInterface = (*eddForecastTask)(nil)

func (e *eddForecastTask) checkDayGroup(ctx utils.LCOSContext, eventTimeLevel uint8, dayGroupStr string) *lcos_error.LCOSError {
	// eventTime 为 None 或者 TimeOfDay 时，dayGroup 应该为空
	if eventTimeLevel == constant.EventTimeLevelNone || eventTimeLevel == constant.EventTimeLevelTimeOfDay {
		if len(dayGroupStr) != 0 {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("event_time_level is [%d], day_group should be empty", eventTimeLevel))
		}
	}

	// 校验 day_group 格式，case [ [ 1, 2, 3 ], [ 4, 5, 6 ], [ 7 ] ]
	var dayGroup [][]uint8
	if err := jsoniter.Unmarshal([]byte(dayGroupStr), &dayGroup); err != nil && len(dayGroupStr) != 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("parse day_group:[%s] fail, err: %s", dayGroupStr, err.Error()))
	}

	return nil
}
