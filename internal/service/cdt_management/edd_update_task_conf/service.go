package edd_update_task_conf

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	config2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	edd_update_task_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_preview_task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/data_ssc"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/waybill_center_service"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"path"
	"regexp"
	"strconv"
	"strings"
)

// progress of preview
//  |---------Call From Data To Get SLS TN--------|------Filter By WBC------|-----Generate File------|
const DataTotalProgress = 70.0
const WaybillCenterTotalProgress = 90.0
const FullProgress = 100.0

type EDDUpdateTaskConfInterface interface {
	CreateUpdateEDDTaskConf(ctx utils.LCOSContext, request *edd_update_task_conf2.CreateEDDUpdateTaskConfRequest) (*edd_update_task_conf.EddUpdateTaskConfTab, *lcos_error.LCOSError)
	PreviewOrderInfo(ctx utils.LCOSContext, request *edd_update_task_conf2.PreviewOrderInfo) (interface{}, *lcos_error.LCOSError)
	ListUpdateEDDTaskConf(ctx utils.LCOSContext, request *edd_update_task_conf2.ListEDDUpdateTaskConfRequest) (interface{}, *lcos_error.LCOSError)
	GetUpdateEDDTaskConfByID(ctx utils.LCOSContext, request *edd_update_task_conf2.DetailEDDUpdateTaskConfIDRequest) (interface{}, *lcos_error.LCOSError)
	DeleteUpdateEDDTaskConfByID(ctx utils.LCOSContext, request *edd_update_task_conf2.DetailEDDUpdateTaskConfIDRequest) (interface{}, *lcos_error.LCOSError)
	UpdateEDDTaskConf(ctx utils.LCOSContext, query, updated map[string]interface{}) *lcos_error.LCOSError

	// for EDD Preview Task Record CRUD
	GetPreviewTaskRecord(ctx utils.LCOSContext, id uint64) (*edd_update_preview_task_record.EddUpdatePreviewTaskRecordTab, *lcos_error.LCOSError)
	CancelPreviewTask(ctx utils.LCOSContext, id uint64, operator string) (interface{}, *lcos_error.LCOSError)
}

type eddUpdateTaskConf struct {
	eddUpdateTaskConfDao edd_update_task_conf.EDDUpdateTaskConfTabDAO
	eddHistoryDao        edd_history.EddHistoryDAO
	s3Service            s3_service.S3Service
	eddUpdateRecordDao   edd_update_preview_task_record.EDDUpdatePreviewTaskRecordTabDAO
}

func (e *eddUpdateTaskConf) UpdateEDDTaskConf(ctx utils.LCOSContext, query, updated map[string]interface{}) *lcos_error.LCOSError {
	return e.eddUpdateTaskConfDao.UpdateEDDUpdateTaskConf(ctx, query, updated)
}

func (e *eddUpdateTaskConf) CreateUpdateEDDTaskConf(ctx utils.LCOSContext, request *edd_update_task_conf2.CreateEDDUpdateTaskConfRequest) (*edd_update_task_conf.EddUpdateTaskConfTab, *lcos_error.LCOSError) {
	updateTaskConf := &edd_update_task_conf.EddUpdateTaskConfTab{
		TaskName:          request.TaskName,
		Method:            request.Method,
		CalculationStatus: edd_constant.EDDCalculating,
		OrderFileUrl:      request.OrderFileUrl,
		FileName:          request.FileName,
		Operator:          ctx.GetUserEmail(),
		Region:            strings.ToUpper(ctx.GetCountry()),
	}

	// check whether need to add order filter info
	if request.Method == edd_constant.ByLine {
		if request.PreviewOrderInfo == nil {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "preview order info cannot be empty when filter type is by line")
		}
		updateTaskConf.ProductIDList = request.PreviewOrderInfo.ProductIDList
		updateTaskConf.EventTimeRangeStart = request.PreviewOrderInfo.EventTimeRangeStart
		updateTaskConf.EventTimeRangeEnd = request.PreviewOrderInfo.EventTimeRangeEnd
		updateTaskConf.OriginLocationLevel = request.PreviewOrderInfo.OriginLocationLevel
		updateTaskConf.OriginLocationList = request.PreviewOrderInfo.OriginLocationList
		updateTaskConf.DestinationLocationLevel = request.PreviewOrderInfo.DestinationLocationLevel
		updateTaskConf.DestinationLocationList = request.PreviewOrderInfo.DestinationLocationList
		updateTaskConf.DestinationCepRangeFileUrl = request.PreviewOrderInfo.DestinationCepRangeFileUrl
		updateTaskConf.DestinationCepRangeFileName = request.PreviewOrderInfo.DestinationCepRangeFileName
		updateTaskConf.DestinationPostcodeFileUrl = request.PreviewOrderInfo.DestinationPostcodeFileUrl
		updateTaskConf.DestinationPostcodeFileName = request.PreviewOrderInfo.DestinationPostcodeFileName
		updateTaskConf.EddMaxAdjustment = request.PreviewOrderInfo.EddMaxAdjustment
		updateTaskConf.EddMinAdjustment = request.PreviewOrderInfo.EddMinAdjustment

		updateTaskConf.EDDStartTime = request.PreviewOrderInfo.EDDStartTime
		updateTaskConf.EDDEndTime = request.PreviewOrderInfo.EDDEndTime
	}
	return updateTaskConf, e.eddUpdateTaskConfDao.BatchCreateEDDUpdateTaskConf(ctx, []*edd_update_task_conf.EddUpdateTaskConfTab{updateTaskConf})
}

func getUpperLocationIDList(locationInfo *cdt_calculation.AddressInfo, locationLevel int8) ([]int, *lcos_error.LCOSError) {
	switch locationLevel {
	case constant.State:
		if locationInfo.StateLocationId == nil || *locationInfo.StateLocationId == 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "state location id is not valid")
		}
		return []int{0, *locationInfo.StateLocationId}, nil
	case constant.City:
		if locationInfo.CityLocationId == nil || *locationInfo.CityLocationId == 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "city location id is not valid")
		}
		return []int{0, *locationInfo.StateLocationId, *locationInfo.CityLocationId}, nil
	case constant.District:
		if locationInfo.DistrictLocationId == nil || *locationInfo.DistrictLocationId == 0 {
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "district location id is not valid")
		}
		return []int{0, *locationInfo.StateLocationId, *locationInfo.CityLocationId, *locationInfo.DistrictLocationId}, nil
	default:
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "location level is not valid")
	}
}

func validCepRange(postcode string, cepRangeList []*cdt_common.CepRange) (bool, error) {
	postcode = utils.ValidPostcode(postcode)
	postcodeInt, err := strconv.Atoi(postcode)
	if err != nil {
		return false, err
	}
	for _, cepRange := range cepRangeList {
		if postcodeInt >= cepRange.CepLeft && postcodeInt <= cepRange.CepRight {
			return true, nil
		}
	}
	return false, nil
}

func (e *eddUpdateTaskConf) doPullFromDataTask(ctx utils.LCOSContext, taskRecordID uint64, request *data_ssc.PreviewOrderInfoRequest, cepRangeList []*cdt_common.CepRange, region string) ([]string, *lcos_error.LCOSError) {
	var allResult []*data_ssc.SlsTNStruct
	// 1. get total number
	firstBatch, total, lcosErr := data_ssc.GetSlsTNListFromDataPaging(ctx, request, 1, data_ssc.BatchSize)
	if lcosErr != nil {
		return nil, lcosErr
	}
	allResult = append(allResult, firstBatch...)
	ratio := float64(data_ssc.BatchSize) / float64(total)
	if ratio > 1.0 {
		ratio = 1.0
	}
	currentProgress := ratio * DataTotalProgress
	updateErr := e.updatePreviewTaskProgress(ctx, taskRecordID, int(currentProgress)) // update initial progress
	if updateErr != nil {
		return nil, updateErr
	}

	// 2. loop to get all data
	for page := 2; (page-1)*data_ssc.BatchSize < total; page++ {
		tmpBatch, _, lcosErr := data_ssc.GetSlsTNListFromDataPaging(ctx, request, page, data_ssc.BatchSize)
		if lcosErr != nil {
			return nil, lcosErr
		}
		allResult = append(allResult, tmpBatch...)

		ratio = float64(page*data_ssc.BatchSize) / float64(total)
		if ratio > 1.0 {
			ratio = 1.0
		}
		currentProgress = ratio * DataTotalProgress
		updateErr = e.updatePreviewTaskProgress(ctx, taskRecordID, int(currentProgress))
		if updateErr != nil {
			return nil, updateErr
		}
	}

	// 3. filter by cep range list
	// filter postcode by cep range
	filteredSlsTNList := make([]string, 0, len(allResult))
	for _, tmp := range allResult {
		if len(cepRangeList) > 0 {
			flag, err := validCepRange(tmp.Postcode, cepRangeList)
			if err != nil {
				logger.CtxLogInfof(ctx, "postcode is not valid|sls_tn=[%s], postcode=[%s], error=[%s]", tmp.SlsTN, tmp.Postcode, err.Error())
				continue
			}
			if !flag {
				logger.CtxLogInfof(ctx, "postcode not belong to input cep range list|sls_tn=[%s], postcode=[%s]", tmp.SlsTN, tmp.Postcode)
				continue
			}
		}
		filteredSlsTNList = append(filteredSlsTNList, tmp.SlsTN)
	}
	return filteredSlsTNList, nil
}

func (e *eddUpdateTaskConf) filterSLSTNByWaybillCenter(ctx utils.LCOSContext, taskRecordID uint64, filteredSlsTNList []string, productLineListMap map[string][]string, region string) ([]string, *lcos_error.LCOSError) {
	returnSLSTNList := make([]string, 0, len(filteredSlsTNList))
	waybillCenter := waybill_center_service.NewWaybillCenterService(ctx, region)
	orderMap, lcosErr := waybillCenter.BatchGetOrderInfoWithConcurrency(ctx, filteredSlsTNList, config2.GetEDDOnGoingConfig(ctx).WaybillCenterBatchSize, config2.GetEDDOnGoingConfig(ctx).WaybillCenterMaxConcurrency)
	if lcosErr != nil {
		return nil, lcosErr
	}
	for _, singleSlsTN := range filteredSlsTNList {
		if _, ok := orderMap[singleSlsTN]; !ok {
			logger.CtxLogInfof(ctx, "cannot find waybill on waybill center|sls_tn=%s", singleSlsTN)
			continue
		}
		if orderMap[singleSlsTN].Retcode != int(lcos_error.SuccessCode) {
			logger.CtxLogInfof(ctx, "request waybill center error, sls_tn=%s, error=%s", singleSlsTN, orderMap[singleSlsTN].Message)
			continue
		}

		waybillItem := orderMap[singleSlsTN]

		// check whether line still going
		if _, ok := productLineListMap[waybillItem.ProductID]; !ok {
			logger.CtxLogInfof(ctx, "current waybill belong to product:[%s], not in input productList", waybillItem.ProductID)
			continue
		}
		if !waybillItem.CheckLineWillGoingOnLines(productLineListMap[waybillItem.ProductID]) {
			logger.CtxLogInfof(ctx, "current waybill:[%s] will not going on line list:[%s], product_id:[%s]|waybill_info=[%s]", singleSlsTN, strings.Join(productLineListMap[waybillItem.ProductID], ","), waybillItem.ProductID, utils.MarshToStringWithoutError(orderMap[singleSlsTN]))
			continue
		}

		// check whether order is rts
		if msg, ok := orderMap[singleSlsTN].CheckValid(config2.GetFirstDeliveryAttemptTrackingCodeList(ctx), config2.GetReturnToSellerTrackingCodeList(ctx), config2.GetTerminalTrackingCodeList(ctx)); !ok {
			logger.CtxLogInfof(ctx, "current waybill:[%s] is rts order|error=%s", singleSlsTN, msg)
			continue
		}
		returnSLSTNList = append(returnSLSTNList, singleSlsTN)
	}
	// update progress to WaybillCenterTotalProgress
	updateErr := e.updatePreviewTaskProgress(ctx, taskRecordID, int(WaybillCenterTotalProgress))
	if updateErr != nil {
		return nil, updateErr
	}
	return returnSLSTNList, nil
}

func (e *eddUpdateTaskConf) generatePreviewResultAndUpload(ctx utils.LCOSContext, taskRecordID uint64, slsTNList []string, eddMinAdjustment, eddMaxAdjustment int, region string) *lcos_error.LCOSError {
	// upload to s3
	templateFilePath := path.Join(pathutil.GetProjectAbsolutePath(), "templates/xlsx/edd", edd_constant.ExportOrderTemplateName)
	f, err := excelize.OpenFile(templateFilePath)
	if err != nil {
		errMsg := fmt.Sprintf("cannot open file: %s", templateFilePath)
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}

	sheetName := f.GetSheetName(0) // 获取文件第一页的sheet name
	if sheetName == "" {
		sheetName = "Sheet1"
	}
	var err1, err2, err3, err4 error
	for index, slsTN := range slsTNList {
		err1 = f.SetCellValue(sheetName, fmt.Sprintf("A%d", index+2), slsTN)
		err2 = f.SetCellValue(sheetName, fmt.Sprintf("B%d", index+2), eddMinAdjustment)
		err3 = f.SetCellValue(sheetName, fmt.Sprintf("C%d", index+2), eddMaxAdjustment)
		err4 = f.SetCellValue(sheetName, fmt.Sprintf("D%d", index+2), 1)
		if err1 != nil || err2 != nil || err3 != nil || err4 != nil {
			return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "cannot generate file")
		}
	}

	// generate local file name to upload to s3
	// 以当前的时间戳生成临时文件
	tmpFileName := fmt.Sprintf("/tmp/%s-%s.xlsx", "preview-order", pickup.GetCurrentTime(ctx, region).Format("20060102150405"))

	// 将空格替换为中划线，防止文件名可能存在的空格，导致文件无法导出
	reg := regexp.MustCompile(" +")
	tmpFileName = reg.ReplaceAllString(tmpFileName, "-")

	err = f.SaveAs(tmpFileName)
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	// upload file to s3
	fileUrl, fileName, _, lcosErr := e.s3Service.UploadFileToS3(ctx, config2.GetConf(ctx).BasicServiceReportS3Config.AccessKeyID, config2.GetConf(ctx).BasicServiceReportS3Config.BucketKey, tmpFileName, config2.GetConf(ctx).BasicServiceReportS3Config.TimeOut, config2.GetConf(ctx).BasicServiceReportS3Config.ExpirationDays, edd_constant.RootEDDDirname)
	if lcosErr != nil {
		return lcosErr
	}

	// update task progress
	updateErr := e.eddUpdateRecordDao.UpdateEDDUpdatePreviewTaskRecord(ctx, map[string]interface{}{"id": taskRecordID}, map[string]interface{}{"progress": int(FullProgress), "file_url": fileUrl, "file_name": fileName})
	if updateErr != nil {
		return updateErr
	}
	return nil
}

func (e *eddUpdateTaskConf) doRunPreviewTask(ctx utils.LCOSContext, taskRecordID uint64, request *data_ssc.PreviewOrderInfoRequest, eddMinAdjustment, eddMaxAdjustment int, cepRangeList []*cdt_common.CepRange, productLineListMap map[string][]string, region string) *lcos_error.LCOSError {
	// 1. get sls tn list from data
	filteredSlsTNList, lcosErr := e.doPullFromDataTask(ctx, taskRecordID, request, cepRangeList, region)
	if lcosErr != nil {
		return lcosErr
	}

	// 2. filter sls tn by line from WBC
	filteredSlsTNList, lcosErr = e.filterSLSTNByWaybillCenter(ctx, taskRecordID, filteredSlsTNList, productLineListMap, region)
	if lcosErr != nil {
		return lcosErr
	}

	// 3. generate S3 report and upload
	return e.generatePreviewResultAndUpload(ctx, taskRecordID, filteredSlsTNList, eddMinAdjustment, eddMaxAdjustment, region)
}

func (e *eddUpdateTaskConf) runPreviewTask(ctx utils.LCOSContext, taskRecordID uint64, request *data_ssc.PreviewOrderInfoRequest, eddMinAdjustment, eddMaxAdjustment int, cepRangeList []*cdt_common.CepRange, productLineListMap map[string][]string, region string) *lcos_error.LCOSError {
	lcosErr := e.doRunPreviewTask(ctx, taskRecordID, request, eddMinAdjustment, eddMaxAdjustment, cepRangeList, productLineListMap, region)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "fail to run preview task|task_id=[%d], error_message=[%s]", taskRecordID, lcosErr.Msg)
		updateErr := e.updatePreviewTaskErrorMessage(ctx, taskRecordID, lcosErr.Msg)
		if updateErr != nil {
			logger.CtxLogErrorf(ctx, updateErr.Msg)
		}
	}
	return nil
}

func (e *eddUpdateTaskConf) PreviewOrderInfo(ctx utils.LCOSContext, request *edd_update_task_conf2.PreviewOrderInfo) (interface{}, *lcos_error.LCOSError) {

	// filter history
	region := ctx.GetCountry()

	if request.EventTimeRangeEnd-request.EventTimeRangeStart >= int64(config2.GetMaxRangeDaysForPreviewEDDPickupDoneTime(ctx)*24*3600) {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "the days range between EventTimeRangeEnd and EventTimeRangeStart is over max range days:[%d]", config2.GetMaxRangeDaysForPreviewEDDPickupDoneTime(ctx))
	}

	// parse cep range
	var cepRangeList []*cdt_common.CepRange
	postcodeMap := make(map[string]struct{})
	postcodeList := make([]string, 0)
	var lcosErr *lcos_error.LCOSError
	if request.DestinationLocationLevel == constant.CepRange {
		cepRangeList, lcosErr = cdt_common.ParseCepFile(ctx, request.DestinationCepRangeFileUrl, config2.GetCdtMaxAutoUpdateCepRangeCount(ctx))
		if lcosErr != nil {
			return nil, lcosErr
		}
	} else if request.DestinationLocationLevel == constant.CDTPostcode {
		tmpPostcodeList, lcosErr := cdt_common.DownloadAndParsePostcode(ctx, request.DestinationPostcodeFileUrl)
		if lcosErr != nil {
			return nil, lcosErr
		}
		for _, singlePostcode := range tmpPostcodeList {
			if _, ok := postcodeMap[singlePostcode]; !ok {
				postcodeList = append(postcodeList, singlePostcode)
				postcodeMap[singlePostcode] = struct{}{}
			}
		}
	}

	filteredLineListMap := make(map[string][]string)
	for _, singleProduct := range request.ProductIDList {
		filteredLineListMap[singleProduct.ProductID] = append(filteredLineListMap[singleProduct.ProductID], singleProduct.LineIDList...)
	}
	parseLocation := func(locationList []*edd_update_task_conf2.SingleLocationInfo, locationLevel int8) []string {
		var locationStringList []string
		for _, singleLocation := range locationList {
			if locationLevel != constant.Country {
				locationStringList = append(locationStringList, singleLocation.LocationID)
			}
		}
		return locationStringList
	}
	originLocationList := parseLocation(request.OriginLocationList, request.OriginLocationLevel)
	destinationLocationList := parseLocation(request.DestinationLocationList, request.DestinationLocationLevel)
	productList := make([]string, 0, len(filteredLineListMap))
	for productID := range filteredLineListMap {
		productList = append(productList, productID)
	}
	newRequest := &data_ssc.PreviewOrderInfoRequest{
		FulfillmentProductID:        productList,
		Region:                      region,
		EDDStartTime:                request.EDDStartTime,
		EDDEndTime:                  request.EDDEndTime,
		Postcode:                    postcodeList,
		EDDPickupTimestampStartTime: request.EventTimeRangeStart,
		EDDPickupTimestampEndTime:   request.EventTimeRangeEnd,
	}

	switch request.OriginLocationLevel {
	case constant.State:
		newRequest.OriginalStateLocationID = originLocationList
	case constant.City:
		newRequest.OriginalCityLocationID = originLocationList
	case constant.District:
		newRequest.OriginalDistrictLocationID = originLocationList
	}

	switch request.DestinationLocationLevel {
	case constant.State:
		newRequest.DestinationStateLocationID = destinationLocationList
	case constant.City:
		newRequest.DestinationCityLocationID = destinationLocationList
	case constant.District:
		newRequest.DestinationDistrictLocationID = destinationLocationList
	}

	// find the minimum and maximum value of cep range
	if request.DestinationLocationLevel == constant.CepRange && len(cepRangeList) > 0 {
		cepRangeMin, cepRangeMax := cepRangeList[0].CepLeft, cepRangeList[0].CepRight
		for _, singleCepRange := range cepRangeList {
			if singleCepRange.CepLeft < cepRangeMin {
				cepRangeMin = singleCepRange.CepLeft
			}
			if singleCepRange.CepRight > cepRangeMax {
				cepRangeMax = singleCepRange.CepRight
			}
		}
		newRequest.CepRange = fmt.Sprintf("%d#%d", cepRangeMin, cepRangeMax)
	}

	// create preview task record
	taskRecord := &edd_update_preview_task_record.EddUpdatePreviewTaskRecordTab{Region: region, TaskName: request.TaskName}
	createRecordErr := e.eddUpdateRecordDao.CreateEDDUpdatePreviewTaskRecord(ctx, taskRecord)
	if createRecordErr != nil {
		return nil, createRecordErr
	}

	// start to run task
	logger.CtxLogInfof(ctx, "ready to run preview task|task_id=%d", taskRecord.ID)
	go e.runPreviewTask(ctx, taskRecord.ID, newRequest, request.EddMinAdjustment, request.EddMaxAdjustment, cepRangeList, filteredLineListMap, region)
	return map[string]interface{}{
		"id": taskRecord.ID,
	}, nil
}

func (e *eddUpdateTaskConf) ListUpdateEDDTaskConf(ctx utils.LCOSContext, request *edd_update_task_conf2.ListEDDUpdateTaskConfRequest) (interface{}, *lcos_error.LCOSError) {
	var page uint32 = 1
	var count uint32 = 10
	queryMap := map[string]interface{}{
		"region": strings.ToUpper(ctx.GetCountry()),
	}
	if request.PageNo != nil && *request.PageNo != 0 {
		page = *request.PageNo
	}
	if request.Count != nil && *request.Count != 0 {
		count = *request.Count
	}
	if request.CalculationStatus != nil {
		queryMap["calculation_status"] = *request.CalculationStatus
	}
	if request.PushingStatus != nil {
		queryMap["pushing_status"] = *request.PushingStatus
	}
	if request.Method != nil {
		queryMap["method"] = *request.Method
	}
	models, total, lcosErr := e.eddUpdateTaskConfDao.ListEDDUpdateTaskConf(ctx, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return map[string]interface{}{
		"count":  count,
		"pageno": page,
		"list":   models,
		"total":  total,
	}, nil
}

func (e *eddUpdateTaskConf) GetUpdateEDDTaskConfByID(ctx utils.LCOSContext, request *edd_update_task_conf2.DetailEDDUpdateTaskConfIDRequest) (interface{}, *lcos_error.LCOSError) {
	models, lcosErr := e.eddUpdateTaskConfDao.SearchEDDUpdateTaskConf(ctx, map[string]interface{}{"id": request.ID})
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(models) <= 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find edd task conf by id:[%d]", request.ID))
	}
	return models[0], nil
}

func (e *eddUpdateTaskConf) DeleteUpdateEDDTaskConfByID(ctx utils.LCOSContext, request *edd_update_task_conf2.DetailEDDUpdateTaskConfIDRequest) (interface{}, *lcos_error.LCOSError) {
	// get update task
	model, lcosErr := e.GetUpdateEDDTaskConfByID(ctx, request)
	if lcosErr != nil {
		return nil, lcosErr
	}

	conf, _ := model.(*edd_update_task_conf.EddUpdateTaskConfTab)
	if utils.ContainsUint8([]uint8{edd_constant.EDDPushing, edd_constant.EDDPushingPartialSuccess, edd_constant.EDDPushingSuccess}, conf.PushingStatus) {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "current task not allowed to delete")
	}
	return nil, e.eddUpdateTaskConfDao.DeleteEDDUpdateTaskConfig(ctx, request.ID)
}

func (e *eddUpdateTaskConf) GetPreviewTaskRecord(ctx utils.LCOSContext, id uint64) (*edd_update_preview_task_record.EddUpdatePreviewTaskRecordTab, *lcos_error.LCOSError) {
	records, lcosErr := e.eddUpdateRecordDao.SearchEDDUpdateTaskConf(ctx, map[string]interface{}{"id": id})
	if lcosErr != nil {
		return nil, lcosErr
	}
	if len(records) <= 0 {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.DBReadWriteErrorCode, "cannot find record by id:[%d]", id)
	}
	return records[0], nil
}

func (e *eddUpdateTaskConf) CancelPreviewTask(ctx utils.LCOSContext, id uint64, operator string) (interface{}, *lcos_error.LCOSError) {
	return nil, e.updatePreviewTaskErrorMessage(ctx, id, fmt.Sprintf("task:[%d] was cancelled by operator:[%s]", id, operator))
}

func (e *eddUpdateTaskConf) updatePreviewTaskProgress(ctx utils.LCOSContext, id uint64, progress int) *lcos_error.LCOSError {
	// find the record first
	records, lcosErr := e.eddUpdateRecordDao.SearchEDDUpdateTaskConf(ctx, map[string]interface{}{"id": id})
	if lcosErr != nil {
		return lcosErr
	}
	if len(records) <= 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.DBReadWriteErrorCode, "cannot find preview task record:[%d]", id)
	}
	record := records[0]
	if record.Progress >= int(FullProgress) {
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "task:[%d] has finished, cannot update error message", id)
	}

	// for record whose error message is not empty, will not allowed to update progress
	if len(record.ErrorMessage) > 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.ServerErrorCode, "the record error message is not empty, not allowed to update progress|task_id=[%d]", id)
	}

	updateErr := e.eddUpdateRecordDao.UpdateEDDUpdatePreviewTaskRecord(ctx, map[string]interface{}{"id": id}, map[string]interface{}{"progress": progress})
	if updateErr != nil {
		return updateErr
	}
	return nil
}

func (e *eddUpdateTaskConf) updatePreviewTaskErrorMessage(ctx utils.LCOSContext, id uint64, errorMessage string) *lcos_error.LCOSError {
	// find the record first
	records, lcosErr := e.eddUpdateRecordDao.SearchEDDUpdateTaskConf(ctx, map[string]interface{}{"id": id})
	if lcosErr != nil {
		return lcosErr
	}
	if len(records) <= 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.DBReadWriteErrorCode, "cannot find preview task record:[%d]", id)
	}
	record := records[0]

	// for record whose error message is not empty, will not allowed to update errormessage
	if len(record.ErrorMessage) > 0 {
		return lcos_error.NewLCOSErrorf(lcos_error.DBReadWriteErrorCode, "the record error message is not empty, not allowed to update error message|task_id=[%d], error_message=[%s]", id, record.ErrorMessage)
	}

	updateErr := e.eddUpdateRecordDao.UpdateEDDUpdatePreviewTaskRecord(ctx, map[string]interface{}{"id": id}, map[string]interface{}{"error_message": errorMessage})
	if updateErr != nil {
		return updateErr
	}
	return nil
}

func NewEDDUpdateTaskConf(eddUpdateTaskConfDao edd_update_task_conf.EDDUpdateTaskConfTabDAO, eddHistoryDao edd_history.EddHistoryDAO, s3Service s3_service.S3Service, eddUpdateRecordDao edd_update_preview_task_record.EDDUpdatePreviewTaskRecordTabDAO) *eddUpdateTaskConf {
	return &eddUpdateTaskConf{
		eddUpdateTaskConfDao: eddUpdateTaskConfDao,
		eddHistoryDao:        eddHistoryDao,
		s3Service:            s3Service,
		eddUpdateRecordDao:   eddUpdateRecordDao,
	}
}

var _ EDDUpdateTaskConfInterface = (*eddUpdateTaskConf)(nil)
