package manual_manipulation_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_record"
	manual_manipulation_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_rule"
	cdt_common2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/product_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seatalk"
	"sort"
	"strconv"
	"strings"
)

type ManualManipulationServiceInterface interface {
	CreateManualManipulationRules(ctx utils.LCOSContext, request *manual_manipulation_rule.CreateManualManipulationRequest) *lcos_error.LCOSError
	DeleteManualManipulationRuleById(ctx utils.LCOSContext, request *manual_manipulation_rule.GetManualManipulationRequest) *lcos_error.LCOSError
	GetManualManipulationRuleByID(ctx utils.LCOSContext, id uint64) (*manual_manipulation_rule.CreateManualManipulationResponse, *lcos_error.LCOSError)
	ListManualManipulationByParamsPaging(ctx utils.LCOSContext, request *manual_manipulation_rule.ListManualManipulationRequest) (*manual_manipulation_rule.ListManualManipulationResponse, *lcos_error.LCOSError)
	GetCepRangeData(ctx utils.LCOSContext, request *manual_manipulation_rule.GetManualManipulationRequest) ([]*cdt_common2.CepRange, string, *lcos_error.LCOSError)
	GetPostcodeData(ctx utils.LCOSContext, request *manual_manipulation_rule.GetManualManipulationRequest) ([]string, string, *lcos_error.LCOSError)
	ToggleCdtManualManipulationRuleStatus(ctx utils.LCOSContext, request *manual_manipulation_rule.ToggleManualManipulationRuleStatusRequest) (*auto_update_rule.ActionResult, *lcos_error.LCOSError)
	// SPLN-24104, 查询product的lanecode信息
	ListProductLaneCodeInfo(ctx utils.LCOSContext, request *manual_manipulation_rule.ListProductLaneCodeReq) (*product_service.LaneCodeInfo, *lcos_error.LCOSError)
	ListLaneManualManipulationLocation(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationLocationRuleTab, *lcos_error.LCOSError)
	ListLaneManualManipulationPostCode(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationPostCodeRuleTab, *lcos_error.LCOSError)
	ListLaneManualManipulationCepRange(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationCepRangeRuleTab, *lcos_error.LCOSError)
	ListLaneManualManipulationRouteLocation(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteLocationRuleTab, *lcos_error.LCOSError)
	ListLaneManualManipulationRoutePostCode(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationRoutePostCodeRuleTab, *lcos_error.LCOSError)
	ListLaneManualManipulationRouteCepRange(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteCepRangeRuleTab, *lcos_error.LCOSError)
	ListManualManipulationRulesRecordByParamsPaging(ctx utils.LCOSContext, query map[string]interface{}, page, count uint32) ([]*manual_manipulation_record.CDTManualManipulationRuleRecordTab, uint32, *lcos_error.LCOSError)
	DeleteManualManipulationRouteLocationRuleByQuery(ctx utils.LCOSContext, recordId uint64, batchNum int) (int64, *lcos_error.LCOSError)
	DeleteManualManipulationRoutePostcodeRuleByQuery(ctx utils.LCOSContext, recordId uint64, batchNum int) (int64, *lcos_error.LCOSError)
	DeleteManualManipulationRouteZipcodeRuleByQuery(ctx utils.LCOSContext, recordId uint64, batchNum int) (int64, *lcos_error.LCOSError)
	DeleteManualManipulationRecordTabByID(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError
}

type manualManipulationService struct {
	manualManipulationDao     manual_manipulation_rule2.CDTManualManipulationRuleTabDAO
	laneManualManipulationDao lane_manual_manipulation_rule.LaneCdtManualManipulationRuleDAO
}

func NewManualManipulationService(manualManipulationDao manual_manipulation_rule2.CDTManualManipulationRuleTabDAO, laneManualManipulationDao lane_manual_manipulation_rule.LaneCdtManualManipulationRuleDAO) *manualManipulationService {
	return &manualManipulationService{
		manualManipulationDao:     manualManipulationDao,
		laneManualManipulationDao: laneManualManipulationDao,
	}
}

var _ ManualManipulationServiceInterface = (*manualManipulationService)(nil)

func (a *manualManipulationService) GetPostcodeData(ctx utils.LCOSContext, request *manual_manipulation_rule.GetManualManipulationRequest) ([]string, string, *lcos_error.LCOSError) {
	manualManipulationRule, lcosErr := a.GetManualManipulationRuleByID(ctx, request.ID)
	if lcosErr != nil {
		return nil, "", lcosErr
	}
	if manualManipulationRule.DestinationLocationLevel != constant.CDTPostcode {
		return nil, "", lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("manual manipulation is not postcode type|id=%d", request.ID))
	}
	if manualManipulationRule.CdtType == edd_constant.LaneCdtType {
		// SPLN-24104, 导出lane维度cdt的postcode数据
		models, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationPostCodeRule(ctx, ctx.GetCountry(), manualManipulationRule.ProductID, manualManipulationRule.LaneCode, request.ID)
		if lcosErr != nil {
			return nil, "", lcosErr
		}
		if len(models) == 0 {
			return nil, "", lcos_error.NewLCOSError(lcos_error.PostcodeNotFoundErrorCode, fmt.Sprintf("cannot find postcode data|id=%v", request.ID))
		}
		var fileName = manualManipulationRule.DestinationPostcodeFileName
		var postcodeList []string
		postcodeMap := map[string]bool{}
		for _, model := range models {
			if _, ok := postcodeMap[model.DestinationPostcode]; !ok {
				postcodeList = append(postcodeList, model.DestinationPostcode)
				postcodeMap[model.DestinationPostcode] = true
			}
		}
		return postcodeList, fileName, nil
	} else {
		models, lcosErr := a.manualManipulationDao.GetManualManipulationZipcodeRuleByRecordID(ctx, request.ID)
		if lcosErr != nil {
			return nil, "", lcosErr
		}
		if len(models) == 0 {
			return nil, "", lcos_error.NewLCOSError(lcos_error.PostcodeNotFoundErrorCode, fmt.Sprintf("cannot find postcode data|id=%v", request.ID))
		}
		var fileName = manualManipulationRule.DestinationPostcodeFileName
		var postcodeList []string
		postcodeMap := map[string]bool{}
		for _, model := range models {
			if _, ok := postcodeMap[model.DestinationPostcode]; !ok {
				postcodeList = append(postcodeList, model.DestinationPostcode)
				postcodeMap[model.DestinationPostcode] = true
			}
		}
		return postcodeList, fileName, nil
	}
}

func containsNonRegionLocation(locationList []*manual_manipulation_rule.SimpleLocation) bool {
	for _, location := range locationList {
		if location.LocationID != "0" {
			return true
		}
	}
	return false
}

func validateObjectType(cbType int8, objectType uint8, updateEvents []uint8, originLocationLevel int8, originLocationList []*manual_manipulation_rule.SimpleLocation) *lcos_error.LCOSError {
	// SPLN-28302
	// for CB channel and object type is CDT, origin location has to be empty
	if cbType == int8(constant.CBType) && objectType == edd_constant.CdtObject && (originLocationLevel != constant.Country || containsNonRegionLocation(originLocationList)) {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "origin location has to be empty when product is cb type and object type is CDT")
	}
	// for CB channel and object type is Lead, origin location has to be empty when update event are all TWS Inbound / TWS Outbound
	if objectType == edd_constant.LeadTimeObject {
		isOriginLocationAllowed := true
		for _, singleEvent := range updateEvents {
			if !cdt_common2.IsOriginLocationNeeded(cbType == int8(constant.CBType), singleEvent) {
				isOriginLocationAllowed = false
				break
			}
		}
		if (!isOriginLocationAllowed) && (originLocationLevel != constant.Country || containsNonRegionLocation(originLocationList)) {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "origin location has to be empty when product is cb type and object type is Lead and update event contains TWS Inbound / TWS Outbound")
		}
	}
	return nil
}

func (a *manualManipulationService) CreateManualManipulationRules(ctx utils.LCOSContext, request *manual_manipulation_rule.CreateManualManipulationRequest) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())

	dateBeginTime := utils.GetDateBeginTimestamp(ctx, region)
	nowTime := utils.GetTimestamp(ctx)

	if request.EffectiveDate < dateBeginTime || request.ExpirationDate <= nowTime || request.EffectiveDate > request.ExpirationDate {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "effective time has to be after today begin time and expiration time has to be after now time")
	}
	if len(request.ProductInfos) == 0 {
		return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "product id len is not same as product name or len is 0")
	}
	// SPLN-27376: don't support create manual manipulation rule with location mode anymore
	//if request.ObjectType == edd_constant.LeadTimeObject && request.Mode == constant.CreateByLocation && len(request.UpdateEventList) == 0 {
	//	return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "update event list cannot be empty when object type is lead")
	//}

	cbType := request.ProductInfos[0].CBType
	for _, singleProduct := range request.ProductInfos {
		if cbType != singleProduct.CBType {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "input product has to be all local or cb")
		}
	}
	// 校验origin location
	if lcosErr := validateObjectType(cbType, request.ObjectType, request.UpdateEventList, request.OriginLocationLevel, request.OriginLocationList); lcosErr != nil {
		return lcosErr
	}

	switch request.CdtType {
	case edd_constant.ProductCdtType:
		return a.createProductManualManipulationRulesByRoute(ctx, request)
	case edd_constant.LaneCdtType:
		return a.createLaneManualManipulationRulesByRoute(ctx, request)
	default:
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "invalid cdt type")
	}
}

// Deprecated: 创建手动修正规则不再支持location模式
func (a *manualManipulationService) createProductManualManipulationRules(ctx utils.LCOSContext, region string, request *manual_manipulation_rule.CreateManualManipulationRequest) *lcos_error.LCOSError {
	// 如果模式为route
	if request.Mode == constant.CreateByRoute {
		return a.createProductManualManipulationRulesByRoute(ctx, request)
	}

	var manualManipulationRecords []*manual_manipulation_record.CDTManualManipulationRuleRecordTab
	var manualManipulationLocationRules []manual_manipulation_rule2.CDTManualManipulationLocationRuleTab
	var manualManipulationPostcodeRules []manual_manipulation_rule2.CDTManualManipulationPostcodeRuleTab
	var manualManipulationZipcodeRules []manual_manipulation_rule2.CDTManualManipulationZipcodeRuleTab
	var updateEventList []uint8

	for i := range request.ProductInfos {
		productID := request.ProductInfos[i].ProductID
		productName := request.ProductInfos[i].ProductName
		CBType := request.ProductInfos[i].CBType
		maskingType := request.ProductInfos[i].MaskingType
		integratedType := request.ProductInfos[i].IntegratedType
		// 创建record信息
		if request.ObjectType == edd_constant.LeadTimeObject {
			updateEventList = request.UpdateEventList
			for _, singleEvent := range request.UpdateEventList {
				manualManipulationRecord := &manual_manipulation_record.CDTManualManipulationRuleRecordTab{
					CdtType:                     request.CdtType,
					ObjectType:                  request.ObjectType,
					UpdateEvent:                 singleEvent,
					ProductID:                   productID,
					ProductName:                 productName,
					CBType:                      CBType,
					MaskingType:                 maskingType,
					IntegratedType:              integratedType,
					TaskName:                    request.TaskName,
					IsSiteLine:                  request.IsSiteLine,
					StatusID:                    constant.Upcoming,
					OriginLocationLevel:         request.OriginLocationLevel,
					DestinationLocationLevel:    request.DestinationLocationLevel,
					DestinationCepRangeFileName: request.DestinationCepRangeFileName,
					DestinationPostcodeFileName: request.DestinationPostcodeFileName,
					CdtMinDelta:                 request.CDTMinDelta,
					CdtMaxDelta:                 request.CDTMaxDelta,
					EffectiveDate:               request.EffectiveDate,
					ExpirationDate:              request.ExpirationDate,
					Region:                      region,
					Mode:                        constant.CreateByLocation,
				}
				manualManipulationRecords = append(manualManipulationRecords, manualManipulationRecord)
			}
		} else {
			updateEventList = []uint8{edd_constant.ShippedOut} // consider all as pickup done
			manualManipulationRecord := &manual_manipulation_record.CDTManualManipulationRuleRecordTab{
				CdtType:                     request.CdtType,
				ObjectType:                  request.ObjectType,
				ProductID:                   productID,
				ProductName:                 productName,
				CBType:                      CBType,
				MaskingType:                 maskingType,
				IntegratedType:              integratedType,
				TaskName:                    request.TaskName,
				IsSiteLine:                  request.IsSiteLine,
				StatusID:                    constant.Upcoming,
				OriginLocationLevel:         request.OriginLocationLevel,
				DestinationLocationLevel:    request.DestinationLocationLevel,
				DestinationCepRangeFileName: request.DestinationCepRangeFileName,
				DestinationPostcodeFileName: request.DestinationPostcodeFileName,
				CdtMinDelta:                 request.CDTMinDelta,
				CdtMaxDelta:                 request.CDTMaxDelta,
				EffectiveDate:               request.EffectiveDate,
				ExpirationDate:              request.ExpirationDate,
				Region:                      region,
				Mode:                        constant.CreateByLocation,
			}
			manualManipulationRecords = append(manualManipulationRecords, manualManipulationRecord)
		}
	}
	// get max cdt manipulation counts
	maxManualManipulationCount := config.GetMaxCdtManualManipulationCount(ctx)
	// 检查是否传入了cep range url，传入了则需要按照cep range的方式解析，目前仅限br。
	if request.DestinationLocationLevel == constant.CepRange {

		var cepRangeList []*cdt_common2.CepRange
		var lcosErr *lcos_error.LCOSError

		if request.DestinationCepRangeUrl == "" || request.DestinationCepRangeFileName == "" {
			// 如果是cep range，文件url不能为空
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cep range file is empty")
		}

		if strings.HasPrefix(request.DestinationCepRangeUrl, constant.DUPLICATE_FROM_PREFIX) {
			tmpList := strings.Split(request.DestinationCepRangeUrl, ":")
			if len(tmpList) != 2 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination cep range file url is not valid")
			}
			duplicatedRecordID, err := strconv.Atoi(tmpList[1])
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
			}

			// 获取product维度cdt数据的ceprange信息
			postcodeModels, lcosErr := a.manualManipulationDao.GetManualManipulationPostcodeRuleByRecordID(ctx, uint64(duplicatedRecordID))
			if lcosErr != nil {
				return lcosErr
			}
			for _, postcodeModel := range postcodeModels {
				tmp := &cdt_common2.CepRange{
					CepLeft:  int(postcodeModel.DestinationPostcodeLeft),
					CepRight: int(postcodeModel.DestinationPostcodeRight),
				}
				if !cdt_common2.IsContainsCepRange(cepRangeList, tmp) {
					cepRangeList = append(cepRangeList, tmp)
				}
			}
		} else {
			// 解析文件传入
			cepRangeList, lcosErr = cdt_common2.ParseCepFile(ctx, request.DestinationCepRangeUrl, config.GetCdtMaxManualManipulationCepRangeCount(ctx))
			if lcosErr != nil {
				return lcosErr
			}
		}
		// 遍历传入的地址，找到最内层记为存入的location id列表
		originLocationList := request.OriginLocationList
		destinationLocationList := cepRangeList

		// check whether larger than max manual manipulations
		if len(manualManipulationRecords)*len(originLocationList)*len(destinationLocationList)*len(updateEventList) >= maxManualManipulationCount {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("total manual manipulation data count is larger than:[%d]", maxManualManipulationCount))
		}

		// 三层循环创建所需数据

		for _, originLocation := range originLocationList {
			for _, destinationLocation := range destinationLocationList {
				originLocationID, err := strconv.Atoi(originLocation.LocationID)
				if err != nil {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
				}
				manualManipulationPostcodeRules = append(manualManipulationPostcodeRules, manual_manipulation_rule2.CDTManualManipulationPostcodeRuleTab{
					OriginLocationID:            uint32(originLocationID),
					OriginLocationParentLevel:   originLocation.ParentLevelID,
					DestinationPostcodeLeft:     uint32(destinationLocation.CepLeft),
					DestinationPostcodeRight:    uint32(destinationLocation.CepRight),
					DestinationCepRangeFileName: request.DestinationCepRangeFileName,
				})
			}
		}

	} else if request.DestinationLocationLevel == constant.CDTPostcode {

		var postcodeList []string
		var lcosErr *lcos_error.LCOSError

		if request.DestinationPostcodeUrl == "" || request.DestinationPostcodeFileName == "" {
			// file url cannot be empty
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "postcode file is empty")
		}

		if strings.HasPrefix(request.DestinationPostcodeUrl, constant.DUPLICATE_FROM_PREFIX) {
			tmpList := strings.Split(request.DestinationPostcodeUrl, ":")
			if len(tmpList) != 2 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination postcode file url is not valid")
			}
			duplicatedRecordID, err := strconv.Atoi(tmpList[1])
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
			}
			postcodeModels, lcosErr := a.manualManipulationDao.GetManualManipulationZipcodeRuleByRecordID(ctx, uint64(duplicatedRecordID))
			if lcosErr != nil {
				return lcosErr
			}
			for _, postcodeModel := range postcodeModels {
				if !utils.InStringSlice(postcodeModel.DestinationPostcode, postcodeList) {
					postcodeList = append(postcodeList, postcodeModel.DestinationPostcode)
				}

			}
		} else {
			// 解析文件传入
			postcodeList, lcosErr = cdt_common2.DownloadAndParsePostcode(ctx, request.DestinationPostcodeUrl)
			if lcosErr != nil {
				return lcosErr
			}
		}
		// 遍历传入的地址，找到最内层记为存入的location id列表
		originLocationList := request.OriginLocationList
		destinationLocationList := postcodeList

		// check whether larger than max manual manipulations
		if len(manualManipulationRecords)*len(originLocationList)*len(destinationLocationList)*len(updateEventList) >= maxManualManipulationCount {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("total manual manipulation data count is larger than:[%d]", maxManualManipulationCount))
		}

		// 三层循环创建所需数据

		for _, originLocation := range originLocationList {
			for _, destinationLocation := range destinationLocationList {
				originLocationID, err := strconv.Atoi(originLocation.LocationID)
				if err != nil {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
				}
				manualManipulationZipcodeRules = append(manualManipulationZipcodeRules, manual_manipulation_rule2.CDTManualManipulationZipcodeRuleTab{
					Region:                    region,
					OriginLocationID:          uint32(originLocationID),
					OriginLocationParentLevel: originLocation.ParentLevelID,
					DestinationPostcode:       destinationLocation,
				})
			}
		}
	} else {
		// 遍历传入的地址，找到最内层记为存入的location id列表
		originLocationList := request.OriginLocationList
		destinationLocationList := request.DestinationLocationList

		// check whether larger than max manual manipulations
		if len(manualManipulationRecords)*len(originLocationList)*len(destinationLocationList)*len(updateEventList) >= maxManualManipulationCount {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("total manual manipulation data count is larger than:[%d]", maxManualManipulationCount))
		}

		// 三层循环创建所需数据

		for _, originLocation := range originLocationList {
			for _, destinationLocation := range destinationLocationList {
				originLocationID, err := strconv.Atoi(originLocation.LocationID)
				if err != nil {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
				}
				destinationLocationID, err := strconv.Atoi(destinationLocation.LocationID)
				if err != nil {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
				}
				manualManipulationLocationRules = append(manualManipulationLocationRules, manual_manipulation_rule2.CDTManualManipulationLocationRuleTab{
					OriginLocationID:               uint32(originLocationID),
					OriginLocationParentLevel:      originLocation.ParentLevelID,
					DestinationLocationID:          uint32(destinationLocationID),
					DestinationLocationParentLevel: destinationLocation.ParentLevelID,
				})
			}
		}

	}

	// 开启事务，创建手动修正规则
	fc := func() *lcos_error.LCOSError {
		for _, manualManipulationRecord := range manualManipulationRecords {
			var manualManipulationLocationDaoRules []*manual_manipulation_rule2.CDTManualManipulationLocationRuleTab
			var manualManipulationPostcodeDaoRules []*manual_manipulation_rule2.CDTManualManipulationPostcodeRuleTab
			var manualManipulationZipcodeDaoRules []*manual_manipulation_rule2.CDTManualManipulationZipcodeRuleTab
			model, lcosErr := a.manualManipulationDao.CreateManualManipulationRulesRecord(ctx, manualManipulationRecord)
			if lcosErr != nil {
				return lcosErr
			}
			// 填充id
			for _, _model := range manualManipulationPostcodeRules {
				tmp := &manual_manipulation_rule2.CDTManualManipulationPostcodeRuleTab{
					RecordID:                    model.ID,
					ProductID:                   model.ProductID,
					UpdateEvent:                 model.UpdateEvent,
					OriginLocationID:            _model.OriginLocationID,
					OriginLocationParentLevel:   _model.OriginLocationParentLevel,
					DestinationPostcodeLeft:     _model.DestinationPostcodeLeft,
					DestinationPostcodeRight:    _model.DestinationPostcodeRight,
					DestinationCepRangeFileName: request.DestinationCepRangeFileName,
				}
				manualManipulationPostcodeDaoRules = append(manualManipulationPostcodeDaoRules, tmp)
			}
			for _, _model := range manualManipulationLocationRules {
				tmp := &manual_manipulation_rule2.CDTManualManipulationLocationRuleTab{
					RecordID:                       model.ID,
					ProductID:                      model.ProductID,
					UpdateEvent:                    model.UpdateEvent,
					OriginLocationID:               _model.OriginLocationID,
					OriginLocationParentLevel:      _model.OriginLocationParentLevel,
					DestinationLocationID:          _model.DestinationLocationID,
					DestinationLocationParentLevel: _model.DestinationLocationParentLevel,
				}
				manualManipulationLocationDaoRules = append(manualManipulationLocationDaoRules, tmp)
			}
			for _, _model := range manualManipulationZipcodeRules {
				tmp := &manual_manipulation_rule2.CDTManualManipulationZipcodeRuleTab{
					RecordID:                  model.ID,
					ProductID:                 model.ProductID,
					Region:                    model.Region,
					UpdateEvent:               model.UpdateEvent,
					OriginLocationID:          _model.OriginLocationID,
					OriginLocationParentLevel: _model.OriginLocationParentLevel,
					DestinationPostcode:       _model.DestinationPostcode,
				}
				manualManipulationZipcodeDaoRules = append(manualManipulationZipcodeDaoRules, tmp)
			}
			if len(manualManipulationLocationRules) > 0 {
				_, lcosErr = a.manualManipulationDao.BatchCreateManualManipulationLocationRules(ctx, manualManipulationLocationDaoRules)
				if lcosErr != nil {
					return lcosErr
				}
			}

			if len(manualManipulationPostcodeRules) > 0 {
				_, lcosErr = a.manualManipulationDao.BatchCreateManualManipulationPostcodeRules(ctx, manualManipulationPostcodeDaoRules)
				if lcosErr != nil {
					return lcosErr
				}
			}

			if len(manualManipulationZipcodeRules) > 0 {
				_, lcosErr = a.manualManipulationDao.BatchCreateManualManipulationZipcodeRules(ctx, manualManipulationZipcodeDaoRules)
				if lcosErr != nil {
					return lcosErr
				}
			}
		}

		return nil
	}
	if lcosErr := ctx.Transaction(fc); lcosErr != nil {
		return lcosErr
	}
	return nil
}

// Deprecated: 创建手动修正规则不再支持location模式
func (a *manualManipulationService) createLaneManualManipulationRules(ctx utils.LCOSContext, region string, request *manual_manipulation_rule.CreateManualManipulationRequest) *lcos_error.LCOSError {
	// 如果模式为route
	if request.Mode == constant.CreateByRoute {
		return a.createLaneManualManipulationRulesByRoute(ctx, request) // SPLN-24104, route模式创建lane手动修正规则
	}

	var manualManipulationRecords []*manual_manipulation_record.CDTManualManipulationRuleRecordTab
	var laneManualManipulationLocationRules []lane_manual_manipulation_rule.LaneCDTManualManipulationLocationRuleTab
	var laneManualManipulationPostCodeRules []lane_manual_manipulation_rule.LaneCDTManualManipulationPostCodeRuleTab
	var laneManualManipulationCepRangeRules []lane_manual_manipulation_rule.LaneCDTManualManipulationCepRangeRuleTab
	var updateEventList []uint8

	for i := range request.ProductInfos {
		productID := request.ProductInfos[i].ProductID
		productName := request.ProductInfos[i].ProductName
		//laneCode := request.ProductInfos[i].LaneCode
		CBType := request.ProductInfos[i].CBType
		maskingType := request.ProductInfos[i].MaskingType
		integratedType := request.ProductInfos[i].IntegratedType
		// 创建record信息
		if request.ObjectType == edd_constant.LeadTimeObject {
			updateEventList = request.UpdateEventList
			for _, singleEvent := range request.UpdateEventList {
				for _, laneCode := range request.ProductInfos[i].LaneCode {
					manualManipulationRecord := &manual_manipulation_record.CDTManualManipulationRuleRecordTab{
						CdtType:                     request.CdtType,
						ObjectType:                  request.ObjectType,
						ProductID:                   productID,
						ProductName:                 productName,
						LaneCode:                    laneCode,
						UpdateEvent:                 singleEvent,
						CBType:                      CBType,
						MaskingType:                 maskingType,
						IntegratedType:              integratedType,
						TaskName:                    request.TaskName,
						IsSiteLine:                  request.IsSiteLine,
						StatusID:                    constant.Upcoming,
						OriginLocationLevel:         request.OriginLocationLevel,
						DestinationLocationLevel:    request.DestinationLocationLevel,
						DestinationCepRangeFileName: request.DestinationCepRangeFileName,
						DestinationPostcodeFileName: request.DestinationPostcodeFileName,
						CdtMinDelta:                 request.CDTMinDelta,
						CdtMaxDelta:                 request.CDTMaxDelta,
						EffectiveDate:               request.EffectiveDate,
						ExpirationDate:              request.ExpirationDate,
						Region:                      region,
						Mode:                        constant.CreateByLocation,
					}
					// 对于非lm的tpl list填充为只含一个空字符串的列表
					manualManipulationRecords = append(manualManipulationRecords, manualManipulationRecord)
				}
			}
		} else {
			for _, laneCode := range request.ProductInfos[i].LaneCode {
				manualManipulationRecord := &manual_manipulation_record.CDTManualManipulationRuleRecordTab{
					CdtType:                     request.CdtType,
					ObjectType:                  request.ObjectType,
					ProductID:                   productID,
					ProductName:                 productName,
					LaneCode:                    laneCode,
					CBType:                      CBType,
					MaskingType:                 maskingType,
					IntegratedType:              integratedType,
					TaskName:                    request.TaskName,
					IsSiteLine:                  request.IsSiteLine,
					StatusID:                    constant.Upcoming,
					OriginLocationLevel:         request.OriginLocationLevel,
					DestinationLocationLevel:    request.DestinationLocationLevel,
					DestinationCepRangeFileName: request.DestinationCepRangeFileName,
					DestinationPostcodeFileName: request.DestinationPostcodeFileName,
					CdtMinDelta:                 request.CDTMinDelta,
					CdtMaxDelta:                 request.CDTMaxDelta,
					EffectiveDate:               request.EffectiveDate,
					ExpirationDate:              request.ExpirationDate,
					Region:                      region,
					Mode:                        constant.CreateByLocation,
				}
				// 对于非lm的tpl list填充为只含一个空字符串的列表
				manualManipulationRecords = append(manualManipulationRecords, manualManipulationRecord)
			}
		}
	}

	// get max cdt manipulation counts
	maxManualManipulationCount := config.GetMaxCdtManualManipulationCount(ctx)

	// 检查是否传入了cep range url，传入了则需要按照cep range的方式解析，目前仅限br。
	if request.DestinationLocationLevel == constant.CepRange {
		var cepRangeList []*cdt_common2.CepRange
		var lcosErr *lcos_error.LCOSError

		if request.DestinationCepRangeUrl == "" || request.DestinationCepRangeFileName == "" {
			// 如果是cep range，文件url不能为空
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "cep range file is empty")
		}

		if strings.HasPrefix(request.DestinationCepRangeUrl, constant.DUPLICATE_FROM_PREFIX) {
			tmpList := strings.Split(request.DestinationCepRangeUrl, ":")
			if len(tmpList) != 2 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination cep range file url is not valid")
			}
			duplicatedRecordID, err := strconv.Atoi(tmpList[1])
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
			}

			// 获取lane维度cdt数据的ceprange信息
			record, lcosErr := a.manualManipulationDao.GetManualManipulationRecordTabByID(ctx, uint64(duplicatedRecordID))
			if lcosErr != nil {
				return lcosErr
			}
			laneCepRangeList, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationCepRangeRule(ctx, region, record.ProductID, record.LaneCode, record.ID)
			if lcosErr != nil {
				return lcosErr
			}
			for _, laneCepRange := range laneCepRangeList {
				tmp := &cdt_common2.CepRange{
					CepLeft:  int(laneCepRange.DestinationPostcodeLeft),
					CepRight: int(laneCepRange.DestinationPostcodeRight),
				}
				if !cdt_common2.IsContainsCepRange(cepRangeList, tmp) {
					cepRangeList = append(cepRangeList, tmp)
				}
			}
		} else {
			// 解析文件传入
			cepRangeList, lcosErr = cdt_common2.ParseCepFile(ctx, request.DestinationCepRangeUrl, config.GetCdtMaxManualManipulationCepRangeCount(ctx))
			if lcosErr != nil {
				return lcosErr
			}
		}
		// 遍历传入的地址，找到最内层记为存入的location id列表
		originLocationList := request.OriginLocationList
		destinationCepRangeList := cepRangeList

		// check whether larger than max manual manipulations
		if len(manualManipulationRecords)*len(originLocationList)*len(destinationCepRangeList)*len(updateEventList) >= maxManualManipulationCount {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("total manual manipulation data count is larger than:[%d]", maxManualManipulationCount))
		}

		// 三层循环创建所需数据
		for _, originLocation := range originLocationList {
			for _, destCepRange := range destinationCepRangeList {
				originLocationID, err := strconv.Atoi(originLocation.LocationID)
				if err != nil {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
				}
				laneManualManipulationCepRangeRules = append(laneManualManipulationCepRangeRules, lane_manual_manipulation_rule.LaneCDTManualManipulationCepRangeRuleTab{
					OriginLocationID:            uint32(originLocationID),
					OriginLocationParentLevel:   originLocation.ParentLevelID,
					DestinationPostcodeLeft:     uint32(destCepRange.CepLeft),
					DestinationPostcodeRight:    uint32(destCepRange.CepRight),
					DestinationCepRangeFileName: request.DestinationCepRangeFileName,
				})
			}
		}
	} else if request.DestinationLocationLevel == constant.CDTPostcode {
		var postCodeList []string
		var lcosErr *lcos_error.LCOSError
		if request.DestinationPostcodeUrl == "" || request.DestinationPostcodeFileName == "" {
			// file url cannot be empty
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "postcode file is empty")
		}
		if strings.HasPrefix(request.DestinationPostcodeUrl, constant.DUPLICATE_FROM_PREFIX) {
			tmpList := strings.Split(request.DestinationPostcodeUrl, ":")
			if len(tmpList) != 2 {
				return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "destination postcode file url is not valid")
			}
			duplicatedRecordID, err := strconv.Atoi(tmpList[1])
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
			}

			// 获取lane维度cdt数据的ceprange信息
			record, lcosErr := a.manualManipulationDao.GetManualManipulationRecordTabByID(ctx, uint64(duplicatedRecordID))
			if lcosErr != nil {
				return lcosErr
			}
			lanePostcodeList, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationPostCodeRule(ctx, region, record.ProductID, record.LaneCode, record.ID)
			if lcosErr != nil {
				return lcosErr
			}
			for _, lanePostcode := range lanePostcodeList {
				if !utils.InStringSlice(lanePostcode.DestinationPostcode, postCodeList) {
					postCodeList = append(postCodeList, lanePostcode.DestinationPostcode)
				}
			}
		} else {
			// 解析文件传入
			postCodeList, lcosErr = cdt_common2.DownloadAndParsePostcode(ctx, request.DestinationPostcodeUrl)
			if lcosErr != nil {
				return lcosErr
			}
		}
		// 遍历传入的地址，找到最内层记为存入的location id列表
		originLocationList := request.OriginLocationList
		destinationPostCodeList := postCodeList

		// check whether larger than max manual manipulations
		if len(manualManipulationRecords)*len(originLocationList)*len(destinationPostCodeList)*len(updateEventList) >= maxManualManipulationCount {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("total manual manipulation data count is larger than:[%d]", maxManualManipulationCount))
		}

		// Product、LaneCode等信息后续填充
		for _, originLocation := range originLocationList {
			for _, destPostCode := range destinationPostCodeList {
				originLocationID, err := strconv.Atoi(originLocation.LocationID)
				if err != nil {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
				}
				laneManualManipulationPostCodeRules = append(laneManualManipulationPostCodeRules, lane_manual_manipulation_rule.LaneCDTManualManipulationPostCodeRuleTab{
					Region:                    region,
					OriginLocationID:          uint32(originLocationID),
					OriginLocationParentLevel: originLocation.ParentLevelID,
					DestinationPostcode:       destPostCode,
				})
			}
		}
	} else {
		// 遍历传入的地址，找到最内层记为存入的location id列表
		originLocationList := request.OriginLocationList
		destinationLocationList := request.DestinationLocationList

		// check whether larger than max manual manipulations
		if len(manualManipulationRecords)*len(originLocationList)*len(destinationLocationList)*len(updateEventList) >= maxManualManipulationCount {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("total manual manipulation data count is larger than:[%d]", maxManualManipulationCount))
		}

		// product和lane信息后续填充
		for _, originLocation := range originLocationList {
			for _, destLocation := range destinationLocationList {
				originLocationID, err := strconv.Atoi(originLocation.LocationID)
				if err != nil {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
				}
				destinationLocationID, err := strconv.Atoi(destLocation.LocationID)
				if err != nil {
					return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
				}
				laneManualManipulationLocationRules = append(laneManualManipulationLocationRules, lane_manual_manipulation_rule.LaneCDTManualManipulationLocationRuleTab{
					OriginLocationID:               uint32(originLocationID),
					OriginLocationParentLevel:      originLocation.ParentLevelID,
					DestinationLocationID:          uint32(destinationLocationID),
					DestinationLocationParentLevel: destLocation.ParentLevelID,
				})
			}
		}
	}

	// 开启事务，创建手动修正规则
	fc := func() *lcos_error.LCOSError {
		for _, manualManipulationRecord := range manualManipulationRecords {
			var addLaneLocationRules []*lane_manual_manipulation_rule.LaneCDTManualManipulationLocationRuleTab
			var addLanePostcodeRules []*lane_manual_manipulation_rule.LaneCDTManualManipulationPostCodeRuleTab
			var addLaneCepRangeRules []*lane_manual_manipulation_rule.LaneCDTManualManipulationCepRangeRuleTab
			record, lcosErr := a.manualManipulationDao.CreateManualManipulationRulesRecord(ctx, manualManipulationRecord)
			if lcosErr != nil {
				return lcosErr
			}
			// 填充product、lane以及record等信息
			for _, model := range laneManualManipulationLocationRules {
				tmp := &lane_manual_manipulation_rule.LaneCDTManualManipulationLocationRuleTab{
					RecordID:                       record.ID,
					ProductID:                      record.ProductID,
					LaneCode:                       record.LaneCode,
					UpdateEvent:                    record.UpdateEvent,
					OriginLocationID:               model.OriginLocationID,
					OriginLocationParentLevel:      model.OriginLocationParentLevel,
					DestinationLocationID:          model.DestinationLocationID,
					DestinationLocationParentLevel: model.DestinationLocationParentLevel,
				}
				addLaneLocationRules = append(addLaneLocationRules, tmp)
			}
			for _, model := range laneManualManipulationPostCodeRules {
				tmp := &lane_manual_manipulation_rule.LaneCDTManualManipulationPostCodeRuleTab{
					RecordID:                  record.ID,
					ProductID:                 record.ProductID,
					LaneCode:                  record.LaneCode,
					UpdateEvent:               record.UpdateEvent,
					Region:                    record.Region,
					OriginLocationID:          model.OriginLocationID,
					OriginLocationParentLevel: model.OriginLocationParentLevel,
					DestinationPostcode:       model.DestinationPostcode,
				}
				addLanePostcodeRules = append(addLanePostcodeRules, tmp)
			}
			for _, model := range laneManualManipulationCepRangeRules {
				tmp := &lane_manual_manipulation_rule.LaneCDTManualManipulationCepRangeRuleTab{
					RecordID:                    record.ID,
					ProductID:                   record.ProductID,
					LaneCode:                    record.LaneCode,
					UpdateEvent:                 record.UpdateEvent,
					OriginLocationID:            model.OriginLocationID,
					OriginLocationParentLevel:   model.OriginLocationParentLevel,
					DestinationPostcodeLeft:     model.DestinationPostcodeLeft,
					DestinationPostcodeRight:    model.DestinationPostcodeRight,
					DestinationCepRangeFileName: request.DestinationCepRangeFileName,
				}
				addLaneCepRangeRules = append(addLaneCepRangeRules, tmp)
			}

			if len(addLaneLocationRules) > 0 {
				lcosErr = a.laneManualManipulationDao.BatchCreateLaneManualManipulationLocationRules(ctx, region, addLaneLocationRules)
				if lcosErr != nil {
					return lcosErr
				}
			}
			if len(addLanePostcodeRules) > 0 {
				lcosErr = a.laneManualManipulationDao.BatchCreateLaneManualManipulationPostCodeRules(ctx, region, addLanePostcodeRules)
				if lcosErr != nil {
					return lcosErr
				}
			}
			if len(addLaneCepRangeRules) > 0 {
				lcosErr = a.laneManualManipulationDao.BatchCreateLaneManualManipulationCepRangeRules(ctx, region, addLaneCepRangeRules)
				if lcosErr != nil {
					return lcosErr
				}
			}
		}
		return nil
	}
	if lcosErr := ctx.Transaction(fc); lcosErr != nil {
		return lcosErr
	}
	return nil
}

func (a *manualManipulationService) getExistManualManipulationRules(ctx utils.LCOSContext, request *manual_manipulation_rule.CreateManualManipulationRequest) []*manual_manipulation_record.CDTManualManipulationRuleRecordTab {
	region := strings.ToUpper(ctx.GetCountry())
	var queryMap = make(map[string]interface{})
	queryMap["region"] = region
	queryMap["status_id !="] = constant.Disable
	queryMap["mode"] = constant.CreateByRoute
	queryMap["cdt_type"] = request.CdtType
	queryMap["object_type"] = request.ObjectType
	existTabs := make([]*manual_manipulation_record.CDTManualManipulationRuleRecordTab, 0)
	for i := range request.ProductInfos {
		productId := request.ProductInfos[i].ProductID
		laneCode := request.ProductInfos[i].LaneCode
		queryMap["product_id"] = productId
		queryMap["is_lm"] = 0
		queryMap["lane_code in"] = laneCode
		general, lcosError := a.manualManipulationDao.SearchManualManipulationRuleRecordsGeneral(ctx, queryMap)
		if lcosError != nil {
			logger.LogErrorf("search manual record error query map is %s", queryMap)
			return existTabs
		}
		existTabs = append(existTabs, general...)
	}
	return existTabs
}

func sendCdtDeltaExceedThresholdAlertMessage(ctx utils.LCOSContext, taskList []*manual_manipulation_record.CDTManualManipulationRuleRecordTab, alertRowList []int, operator string) {
	if len(alertRowList) == 0 {
		return
	}

	// 1. 合并连续的行号，减少消息长度
	var (
		alertRowRangeList []string
		startRowId        int
		endRowId          int
	)
	sort.SliceStable(alertRowList, func(i, j int) bool {
		return alertRowList[i] < alertRowList[j]
	})
	for _, rowId := range alertRowList {
		if endRowId == 0 {
			startRowId, endRowId = rowId, rowId
			continue
		}
		if rowId == endRowId || rowId == endRowId+1 {
			// 行号连续，更新行号区间的end值
			endRowId = rowId
			continue
		}
		// 行号不连续，将之前连续的行号生成行号区间字符串，并重新开始统计
		if startRowId == endRowId {
			// "rowId"
			alertRowRangeList = append(alertRowRangeList, strconv.Itoa(startRowId))
		} else {
			// "startRowId-endRowId"
			alertRowRangeList = append(alertRowRangeList, fmt.Sprintf("%d-%d", startRowId, endRowId))
		}
		startRowId, endRowId = rowId, rowId
	}
	if endRowId != 0 {
		if startRowId == endRowId {
			alertRowRangeList = append(alertRowRangeList, strconv.Itoa(startRowId))
		} else {
			alertRowRangeList = append(alertRowRangeList, fmt.Sprintf("%d-%d", startRowId, endRowId))
		}
	}
	alertRowRangeText := strings.Join(alertRowRangeList, ",")

	// 2. 按region+task维度发送告警消息
	for _, task := range taskList {
		alertConf := config.GetCdtDeltaAlertConfig(ctx, task.ObjectType)
		if alertConf == nil {
			continue
		}

		message := fmt.Sprintf(edd_constant.CdtDeltaExceedThresholdAlertMessage,
			utils.GetEnv(ctx),
			task.Region,
			task.ID,
			task.TaskName,
			edd_constant.ObjectTypeMap[task.ObjectType],
			task.ProductID,
			utils.TimestampToStrWithCidNew(int64(task.EffectiveDate), constant.DateAndTimeFormat, task.Region),
			utils.TimestampToStrWithCidNew(int64(task.ExpirationDate), constant.DateAndTimeFormat, task.Region),
			operator,
			alertRowRangeText,
			task.RouteFileUrl,
		)
		_ = seatalk.NotifyWithTextMessage(ctx, alertConf.NotifyConfig.Webhook, message, nil, true)
	}
}

func (a *manualManipulationService) createProductManualManipulationRulesByRoute(ctx utils.LCOSContext, request *manual_manipulation_rule.CreateManualManipulationRequest) *lcos_error.LCOSError {
	// 将3pl分拆成不同的3pl列表。如果传入的is_lm为1，则tpl list不能为空
	region := strings.ToUpper(ctx.GetCountry())
	if request.ReplaceOld != constant.ENABLED {
		taskID := make([]uint64, 0)
		taskTip := make([]string, 0)
		existRules := a.getExistManualManipulationRules(ctx, request)
		if len(existRules) != 0 {
			for _, temp := range existRules {
				if (temp.ExpirationDate <= request.ExpirationDate && temp.ExpirationDate >= request.EffectiveDate) || (temp.EffectiveDate >= request.EffectiveDate && temp.EffectiveDate <= request.ExpirationDate) || (temp.EffectiveDate <= request.EffectiveDate && temp.ExpirationDate >= request.ExpirationDate) {
					taskID = append(taskID, temp.ID)
					tip := fmt.Sprintf("TaskId[%d]-ProductId[%s]", temp.ID, temp.ProductID)
					taskTip = append(taskTip, tip)
				}
			}
		}
		if len(taskID) > 0 {
			resultStr := strings.Join(taskTip, "&")
			return lcos_error.NewLCOSError(lcos_error.ChannelHasIntersectEffectTimePeriod, fmt.Sprintf("%s are within the same effective time period of this task. Submitting this task will automatically disable the old task. Proceed?", resultStr))
		}

	}

	var manualManipulationRecords []*manual_manipulation_record.CDTManualManipulationRuleRecordTab
	var manualManipulationLocationRules []manual_manipulation_rule2.CDTManualManipulationRouteLocationRuleTab
	var manualManipulationPostcodeRules []manual_manipulation_rule2.CDTManualManipulationRoutePostcodeRuleTab
	var manualManipulationZipcodeRules []manual_manipulation_rule2.CDTManualManipulationRouteZipcodeRuleTab
	reqProMap := make(map[string]*manual_manipulation_rule.CreateManualManipulationRequest)
	for i := range request.ProductInfos {
		productID := request.ProductInfos[i].ProductID
		req := *request
		req.ProductInfos = []manual_manipulation_rule.ProductInfoReq{request.ProductInfos[i]}
		reqProMap[productID] = &req
		productName := request.ProductInfos[i].ProductName
		CBType := request.ProductInfos[i].CBType
		maskingType := request.ProductInfos[i].MaskingType
		integratedType := request.ProductInfos[i].IntegratedType
		// 创建record信息
		manualManipulationRecord := &manual_manipulation_record.CDTManualManipulationRuleRecordTab{
			CdtType:                     request.CdtType,
			ObjectType:                  request.ObjectType,
			ProductID:                   productID,
			ProductName:                 productName,
			CBType:                      CBType,
			MaskingType:                 maskingType,
			IntegratedType:              integratedType,
			TaskName:                    request.TaskName,
			IsSiteLine:                  request.IsSiteLine,
			StatusID:                    constant.Upcoming,
			OriginLocationLevel:         request.OriginLocationLevel,
			DestinationLocationLevel:    request.DestinationLocationLevel,
			DestinationCepRangeFileName: request.DestinationCepRangeFileName,
			DestinationPostcodeFileName: request.DestinationPostcodeFileName,
			CdtMinDelta:                 request.CDTMinDelta,
			CdtMaxDelta:                 request.CDTMaxDelta,
			EffectiveDate:               request.EffectiveDate,
			ExpirationDate:              request.ExpirationDate,
			Region:                      region,
			RouteFileName:               request.RouteFileName,
			RouteFileUrl:                request.RouteFileUrl,
			Mode:                        constant.CreateByRoute,
		}
		manualManipulationRecords = append(manualManipulationRecords, manualManipulationRecord)
	}
	// 解析上传文件
	cbFlag := request.ProductInfos[0].CBType == int8(constant.CBType)
	routeModels, lcosError := cdt_common2.ParseRouteFile(ctx, request.RouteFileUrl, region, request.ObjectType, cbFlag)
	if lcosError != nil {
		logger.CtxLogErrorf(ctx, lcosError.Msg)
		return lcosError
	}

	var deltaExceedThresholdRowsList []int
	// 按origin_location_id+update_event对destination ceprange进行分组
	cepOverlapCheckMap := make(map[string][]cdt_common2.CepOverLap)
	for _, _model := range routeModels {
		var locationID uint64
		if _model.OriginLocationLevel == constant.Country {
			locationID = 0
		} else {
			locationInfo, lcosError := address_service.LocationServer.GetLocationInfoByName(ctx, _model.OriginRegion, _model.OriginState, _model.OriginCity, _model.OriginDistrict, "")
			if lcosError != nil {
				logger.LogErrorf("find location info error ,origin region is %s, origin state is %s, origin city is %s , origin district is %s", _model.OriginRegion, _model.OriginState, _model.OriginCity, _model.OriginDistrict)
				return lcosError
			}

			if locationInfo.StateLocationId != 0 {
				locationID = uint64(locationInfo.StateLocationId)
			}
			if locationInfo.CityLocationId != 0 {
				locationID = uint64(locationInfo.CityLocationId)
			}
			if locationInfo.DistrictLocationId != 0 {
				locationID = uint64(locationInfo.DistrictLocationId)
			}
			if locationInfo.StreetLocationId != 0 {
				locationID = uint64(locationInfo.StreetLocationId)
			}
		}

		if _model.DestinationLocationLevel == constant.CepRange {

			left, err := strconv.Atoi(_model.DestinationCEPRangeInitial)
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
			}

			right, err := strconv.Atoi(_model.DestinationCEPRangeFinal)
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
			}
			manualManipulationPostcodeRules = append(manualManipulationPostcodeRules, manual_manipulation_rule2.CDTManualManipulationRoutePostcodeRuleTab{
				UpdateEvent:              _model.UpdateEvent,
				OriginLocationID:         locationID,
				DestinationPostcodeLeft:  uint32(left),
				DestinationPostcodeRight: uint32(right),
				CdtMinDelta:              _model.MinDelta,
				CdtMaxDelta:              _model.MaxDelta,
			})

			overlapCheckKey := utils.GenKey(":", strconv.Itoa(int(locationID)), strconv.Itoa(int(_model.UpdateEvent)))
			cepOverlapCheckMap[overlapCheckKey] = append(cepOverlapCheckMap[overlapCheckKey], cdt_common2.CepOverLap{
				RowId: _model.Row,
				Left:  left,
				Right: right,
			})
		} else if _model.DestinationLocationLevel == constant.CDTPostcode {
			manualManipulationZipcodeRules = append(manualManipulationZipcodeRules, manual_manipulation_rule2.CDTManualManipulationRouteZipcodeRuleTab{
				UpdateEvent:         _model.UpdateEvent,
				Region:              region,
				OriginLocationID:    uint32(locationID),
				DestinationPostcode: _model.DestinationPostcode,
				CdtMinDelta:         _model.MinDelta,
				CdtMaxDelta:         _model.MaxDelta,
			})

		} else {
			// 循环创建所需数据
			var destinationLocationID uint64
			if _model.DestinationLocationLevel == constant.Country {
				destinationLocationID = 0
			} else {
				destinationLocationInfo, lcosError := address_service.LocationServer.GetLocationInfoByName(ctx, _model.DestinationRegion, _model.DestinationState, _model.DestinationCity, _model.DestinationDistrict, "")
				if lcosError != nil {
					return lcosError
				}

				if destinationLocationInfo.StateLocationId != 0 {
					destinationLocationID = uint64(destinationLocationInfo.StateLocationId)
				}
				if destinationLocationInfo.CityLocationId != 0 {
					destinationLocationID = uint64(destinationLocationInfo.CityLocationId)
				}
				if destinationLocationInfo.DistrictLocationId != 0 {
					destinationLocationID = uint64(destinationLocationInfo.DistrictLocationId)
				}
				if destinationLocationInfo.StreetLocationId != 0 {
					destinationLocationID = uint64(destinationLocationInfo.StreetLocationId)
				}
			}
			manualManipulationLocationRules = append(manualManipulationLocationRules, manual_manipulation_rule2.CDTManualManipulationRouteLocationRuleTab{
				UpdateEvent:           _model.UpdateEvent,
				OriginLocationID:      uint32(locationID),
				DestinationLocationID: uint32(destinationLocationID),
				CdtMinDelta:           _model.MinDelta,
				CdtMaxDelta:           _model.MaxDelta,
			})
		}

		if config.IsCdtDeltaExceedAlertThreshold(ctx, request.ObjectType, _model.MinDelta, _model.MaxDelta) {
			deltaExceedThresholdRowsList = append(deltaExceedThresholdRowsList, _model.Row)
		}
	}

	// cep overlap check
	for _, cepRangeList := range cepOverlapCheckMap {
		// 排序前，不重叠的情况只有[i, i] [j, j]和[j, j] [i, i]
		sort.SliceStable(cepRangeList, func(i, j int) bool {
			return cepRangeList[i].Left < cepRangeList[j].Left
		})
		// 排序后，由于i.Left < j.Left，所以不重叠的情况只剩[i, i] [j, j]，因此所有i.Right >= j.Left的均有重叠
		for i := 0; i < len(cepRangeList)-1; i++ {
			if cepRangeList[i].Right >= cepRangeList[i+1].Left {
				msg := fmt.Sprintf("CEP range has overlap between row %d and row %d.", cepRangeList[i].RowId, cepRangeList[i+1].RowId)
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, msg)
			}
		}
	}

	// 开启事务，创建手动修正规则
	fc := func() *lcos_error.LCOSError {
		for _, manualManipulationRecord := range manualManipulationRecords {
			var manualManipulationLocationDaoRules []*manual_manipulation_rule2.CDTManualManipulationRouteLocationRuleTab
			var manualManipulationPostcodeDaoRules []*manual_manipulation_rule2.CDTManualManipulationRoutePostcodeRuleTab
			var manualManipulationZipcodeDaoRules []*manual_manipulation_rule2.CDTManualManipulationRouteZipcodeRuleTab
			// 校验有重复的，需要将之前的数据置为disable
			if request.ReplaceOld == constant.ENABLED {
				// 选出该productid 进行去重校验
				//req := reqProMap[manualManipulationRecord.ProductID]
				req := *request
				proInfoReq := manual_manipulation_rule.ProductInfoReq{
					ProductID: manualManipulationRecord.ProductID,
				}
				req.ProductInfos = []manual_manipulation_rule.ProductInfoReq{
					proInfoReq,
				}
				existRules := a.getExistManualManipulationRules(ctx, &req)
				if len(existRules) != 0 {
					for _, temp := range existRules {
						if (temp.ExpirationDate <= request.ExpirationDate && temp.ExpirationDate >= request.EffectiveDate) || (temp.EffectiveDate >= request.EffectiveDate && temp.EffectiveDate <= request.ExpirationDate) || (temp.EffectiveDate <= request.EffectiveDate && temp.ExpirationDate >= request.ExpirationDate) {
							a.manualManipulationDao.UpdateManualManipulationRuleRecordsGeneral(ctx, map[string]interface{}{"id": temp.ID}, map[string]interface{}{"status_id": constant.Disable})
						}
					}
				}
			}
			model, lcosErr := a.manualManipulationDao.CreateManualManipulationRulesRecord(ctx, manualManipulationRecord)
			if lcosErr != nil {
				return lcosErr
			}
			// 填充id
			for _, _model := range manualManipulationPostcodeRules {
				tmp := &manual_manipulation_rule2.CDTManualManipulationRoutePostcodeRuleTab{
					RecordID:                  model.ID,
					ProductID:                 model.ProductID,
					OriginLocationID:          _model.OriginLocationID,
					OriginLocationParentLevel: _model.OriginLocationParentLevel,
					DestinationPostcodeLeft:   _model.DestinationPostcodeLeft,
					DestinationPostcodeRight:  _model.DestinationPostcodeRight,
					CdtMinDelta:               _model.CdtMinDelta,
					CdtMaxDelta:               _model.CdtMaxDelta,
					UpdateEvent:               _model.UpdateEvent,
				}
				manualManipulationPostcodeDaoRules = append(manualManipulationPostcodeDaoRules, tmp)
			}
			for _, _model := range manualManipulationLocationRules {
				tmp := &manual_manipulation_rule2.CDTManualManipulationRouteLocationRuleTab{
					RecordID:                       model.ID,
					ProductID:                      model.ProductID,
					OriginLocationID:               _model.OriginLocationID,
					OriginLocationParentLevel:      _model.OriginLocationParentLevel,
					DestinationLocationID:          _model.DestinationLocationID,
					DestinationLocationParentLevel: _model.DestinationLocationParentLevel,
					CdtMinDelta:                    _model.CdtMinDelta,
					CdtMaxDelta:                    _model.CdtMaxDelta,
					UpdateEvent:                    _model.UpdateEvent,
				}
				manualManipulationLocationDaoRules = append(manualManipulationLocationDaoRules, tmp)
			}
			for _, _model := range manualManipulationZipcodeRules {
				tmp := &manual_manipulation_rule2.CDTManualManipulationRouteZipcodeRuleTab{
					RecordID:                  model.ID,
					ProductID:                 model.ProductID,
					Region:                    _model.Region,
					OriginLocationID:          _model.OriginLocationID,
					OriginLocationParentLevel: _model.OriginLocationParentLevel,
					DestinationPostcode:       _model.DestinationPostcode,
					CdtMinDelta:               _model.CdtMinDelta,
					CdtMaxDelta:               _model.CdtMaxDelta,
					UpdateEvent:               _model.UpdateEvent,
				}
				manualManipulationZipcodeDaoRules = append(manualManipulationZipcodeDaoRules, tmp)
			}
			if len(manualManipulationLocationRules) > 0 {
				_, lcosErr = a.manualManipulationDao.BatchCreateManualManipulationRouteLocationRules(ctx, manualManipulationLocationDaoRules)
				if lcosErr != nil {
					return lcosErr
				}
			}

			if len(manualManipulationPostcodeRules) > 0 {
				_, lcosErr = a.manualManipulationDao.BatchCreateManualManipulationRoutePostcodeRules(ctx, manualManipulationPostcodeDaoRules)
				if lcosErr != nil {
					return lcosErr
				}
			}

			if len(manualManipulationZipcodeRules) > 0 {
				_, lcosErr = a.manualManipulationDao.BatchCreateManualManipulationRouteZipcodeRules(ctx, manualManipulationZipcodeDaoRules)
				if lcosErr != nil {
					return lcosErr
				}
			}
		}
		return nil
	}
	if lcosErr := ctx.Transaction(fc); lcosErr != nil {
		return lcosErr
	}

	// SPLN-35066 数据完成写入后，发送异常值告警消息
	sendCdtDeltaExceedThresholdAlertMessage(ctx, manualManipulationRecords, deltaExceedThresholdRowsList, ctx.GetUserEmail())
	return nil
}

// SPLN-24104: 支持lane维度cdt
func (a *manualManipulationService) createLaneManualManipulationRulesByRoute(ctx utils.LCOSContext, request *manual_manipulation_rule.CreateManualManipulationRequest) *lcos_error.LCOSError {
	// 将3pl分拆成不同的3pl列表。如果传入的is_lm为1，则tpl list不能为空
	region := strings.ToUpper(ctx.GetCountry())
	oldTaskID := make([]string, 0)
	existRules := a.getExistManualManipulationRules(ctx, request)
	if len(existRules) != 0 {
		for _, temp := range existRules {
			if (temp.ExpirationDate <= request.ExpirationDate && temp.ExpirationDate >= request.EffectiveDate) || (temp.EffectiveDate >= request.EffectiveDate && temp.EffectiveDate <= request.ExpirationDate) || (temp.EffectiveDate <= request.EffectiveDate && temp.ExpirationDate >= request.ExpirationDate) {
				oldTaskID = append(oldTaskID, strconv.FormatUint(temp.ID, 10))
			}
		}
	}
	if request.ReplaceOld != constant.ENABLED && len(oldTaskID) != 0 { // 首次提交如果有生效时间重叠的task，则报错返回告警
		idList := strings.Join(oldTaskID, ",")
		return lcos_error.NewLCOSErrorf(lcos_error.ChannelHasIntersectEffectTimePeriod, "The effective time of this task overlaps with the ones of the existing tasks task ID %s. If submitting, task ID %s will be disabled and the status of this task will be upcoming.", idList, idList)
	}

	var manualManipulationRecords []*manual_manipulation_record.CDTManualManipulationRuleRecordTab
	var laneManualManipulationLocationRules []lane_manual_manipulation_rule.LaneCDTManualManipulationRouteLocationRuleTab
	var laneManualManipulationPostCodeRules []lane_manual_manipulation_rule.LaneCDTManualManipulationRoutePostCodeRuleTab
	var laneManualManipulationCepRangeRules []lane_manual_manipulation_rule.LaneCDTManualManipulationRouteCepRangeRuleTab

	for i := range request.ProductInfos {
		productID := request.ProductInfos[i].ProductID
		productName := request.ProductInfos[i].ProductName
		//laneCode := request.ProductInfos[i].LaneCode
		CBType := request.ProductInfos[i].CBType
		maskingType := request.ProductInfos[i].MaskingType
		integratedType := request.ProductInfos[i].IntegratedType
		// 创建record信息
		for _, laneCode := range request.ProductInfos[i].LaneCode {
			manualManipulationRecord := &manual_manipulation_record.CDTManualManipulationRuleRecordTab{
				CdtType:                     request.CdtType,
				ObjectType:                  request.ObjectType,
				ProductID:                   productID,
				ProductName:                 productName,
				LaneCode:                    laneCode,
				CBType:                      CBType,
				MaskingType:                 maskingType,
				IntegratedType:              integratedType,
				TaskName:                    request.TaskName,
				IsSiteLine:                  request.IsSiteLine,
				StatusID:                    constant.Upcoming,
				OriginLocationLevel:         request.OriginLocationLevel,
				DestinationLocationLevel:    request.DestinationLocationLevel,
				DestinationCepRangeFileName: request.DestinationCepRangeFileName,
				DestinationPostcodeFileName: request.DestinationPostcodeFileName,
				CdtMinDelta:                 request.CDTMinDelta,
				CdtMaxDelta:                 request.CDTMaxDelta,
				EffectiveDate:               request.EffectiveDate,
				ExpirationDate:              request.ExpirationDate,
				Region:                      region,
				RouteFileName:               request.RouteFileName,
				RouteFileUrl:                request.RouteFileUrl,
				Mode:                        constant.CreateByRoute,
			}

			manualManipulationRecords = append(manualManipulationRecords, manualManipulationRecord)
		}
	}
	// 解析上传文件
	cbFlag := request.ProductInfos[0].CBType == int8(constant.CBType)
	routeModels, lcosError := cdt_common2.ParseRouteFile(ctx, request.RouteFileUrl, region, request.ObjectType, cbFlag)
	if lcosError != nil {
		return lcosError
	}

	var deltaExceedThresholdRowsList []int
	// 按origin_location_id+update_event对destination ceprange进行分组
	var hasCbNotLmEventFlag bool // 上传update event是否包含非cb lm event
	var hasCbLmEventFlag bool    // 上传update event是否包含cb lm event
	cepOverlapCheckMap := make(map[string][]cdt_common2.CepOverLap)
	for _, _model := range routeModels {
		var locationID uint64
		if _model.OriginLocationLevel == constant.Country {
			locationID = 0
		} else {
			locationInfo, lcosError := address_service.LocationServer.GetLocationInfoByName(ctx, _model.OriginRegion, _model.OriginState, _model.OriginCity, _model.OriginDistrict, "")
			if lcosError != nil {
				logger.LogErrorf("find location info error ,origin region is %s, origin state is %s, origin city is %s , origin district is %s", _model.OriginRegion, _model.OriginState, _model.OriginCity, _model.OriginDistrict)
				return lcosError
			}
			if locationInfo.StateLocationId != 0 {
				locationID = uint64(locationInfo.StateLocationId)
			}
			if locationInfo.CityLocationId != 0 {
				locationID = uint64(locationInfo.CityLocationId)
			}
			if locationInfo.DistrictLocationId != 0 {
				locationID = uint64(locationInfo.DistrictLocationId)
			}
			if locationInfo.StreetLocationId != 0 {
				locationID = uint64(locationInfo.StreetLocationId)
			}
		}

		if _model.DestinationLocationLevel == constant.CepRange {
			left, err := strconv.Atoi(_model.DestinationCEPRangeInitial)
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
			}
			right, err := strconv.Atoi(_model.DestinationCEPRangeFinal)
			if err != nil {
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, err.Error())
			}
			laneManualManipulationCepRangeRules = append(laneManualManipulationCepRangeRules, lane_manual_manipulation_rule.LaneCDTManualManipulationRouteCepRangeRuleTab{
				UpdateEvent:              _model.UpdateEvent,
				OriginLocationID:         uint32(locationID),
				DestinationPostcodeLeft:  uint32(left),
				DestinationPostcodeRight: uint32(right),
				CdtMinDelta:              _model.MinDelta,
				CdtMaxDelta:              _model.MaxDelta,
			})

			overlapCheckKey := utils.GenKey(":", strconv.Itoa(int(locationID)), strconv.Itoa(int(_model.UpdateEvent)))
			cepOverlapCheckMap[overlapCheckKey] = append(cepOverlapCheckMap[overlapCheckKey], cdt_common2.CepOverLap{
				RowId: _model.Row,
				Left:  left,
				Right: right,
			})
		} else if _model.DestinationLocationLevel == constant.CDTPostcode {
			laneManualManipulationPostCodeRules = append(laneManualManipulationPostCodeRules, lane_manual_manipulation_rule.LaneCDTManualManipulationRoutePostCodeRuleTab{
				UpdateEvent:         _model.UpdateEvent,
				Region:              region,
				OriginLocationID:    uint32(locationID),
				DestinationPostcode: _model.DestinationPostcode,
				CdtMinDelta:         _model.MinDelta,
				CdtMaxDelta:         _model.MaxDelta,
			})
		} else {
			var destinationLocationID uint64
			if _model.DestinationLocationLevel == constant.Country {
				destinationLocationID = 0
			} else {
				destinationLocationInfo, lcosError := address_service.LocationServer.GetLocationInfoByName(ctx, _model.DestinationRegion, _model.DestinationState, _model.DestinationCity, _model.DestinationDistrict, "")
				if lcosError != nil {
					return lcosError
				}
				if destinationLocationInfo.StateLocationId != 0 {
					destinationLocationID = uint64(destinationLocationInfo.StateLocationId)
				}
				if destinationLocationInfo.CityLocationId != 0 {
					destinationLocationID = uint64(destinationLocationInfo.CityLocationId)
				}
				if destinationLocationInfo.DistrictLocationId != 0 {
					destinationLocationID = uint64(destinationLocationInfo.DistrictLocationId)
				}
				if destinationLocationInfo.StreetLocationId != 0 {
					destinationLocationID = uint64(destinationLocationInfo.StreetLocationId)
				}
			}
			laneManualManipulationLocationRules = append(laneManualManipulationLocationRules, lane_manual_manipulation_rule.LaneCDTManualManipulationRouteLocationRuleTab{
				UpdateEvent:           _model.UpdateEvent,
				OriginLocationID:      uint32(locationID),
				DestinationLocationID: uint32(destinationLocationID),
				CdtMinDelta:           _model.MinDelta,
				CdtMaxDelta:           _model.MaxDelta,
			})
		}

		if cbFlag {
			// 校验上传文件中是否包含cb lm的update event
			if cdt_common2.IsCbLmEvent(_model.UpdateEvent) {
				hasCbLmEventFlag = true
			} else {
				hasCbNotLmEventFlag = true
			}
		}

		if config.IsCdtDeltaExceedAlertThreshold(ctx, request.ObjectType, _model.MinDelta, _model.MaxDelta) {
			deltaExceedThresholdRowsList = append(deltaExceedThresholdRowsList, _model.Row)
		}
	}

	// cep overlap check
	for _, cepRangeList := range cepOverlapCheckMap {
		// 排序前，不重叠的情况只有[i, i] [j, j]和[j, j] [i, i]
		sort.SliceStable(cepRangeList, func(i, j int) bool {
			return cepRangeList[i].Left < cepRangeList[j].Left
		})
		// 排序后，由于i.Left < j.Left，所以不重叠的情况只剩[i, i] [j, j]，因此所有i.Right >= j.Left的均有重叠
		for i := 0; i < len(cepRangeList)-1; i++ {
			if cepRangeList[i].Right >= cepRangeList[i+1].Left {
				msg := fmt.Sprintf("CEP range has overlap between row %d and row %d.", cepRangeList[i].RowId, cepRangeList[i+1].RowId)
				return lcos_error.NewLCOSError(lcos_error.UploadFileErrorCode, msg)
			}
		}
	}

	// 开启事务，创建手动修正规则
	fc := func() *lcos_error.LCOSError {
		for _, manualManipulationRecord := range manualManipulationRecords {
			var addLaneLocationRules []*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteLocationRuleTab
			var addLanePostcodeRules []*lane_manual_manipulation_rule.LaneCDTManualManipulationRoutePostCodeRuleTab
			var addLaneCepRangeRules []*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteCepRangeRuleTab
			// 二次确认后，如果有生效时间重叠的task，将其状态置为disable
			if request.ReplaceOld == constant.ENABLED && len(oldTaskID) != 0 {
				for _, taskId := range oldTaskID {
					a.manualManipulationDao.UpdateManualManipulationRuleRecordsGeneral(ctx, map[string]interface{}{"id": taskId}, map[string]interface{}{"status_id": constant.Disable})
				}
			}

			var record *manual_manipulation_record.CDTManualManipulationRuleRecordTab
			var cbLmRecord *manual_manipulation_record.CDTManualManipulationRuleRecordTab
			var lcosErr *lcos_error.LCOSError

			if !cbFlag || hasCbNotLmEventFlag {
				record, lcosErr = a.manualManipulationDao.CreateManualManipulationRulesRecord(ctx, manualManipulationRecord)
				if lcosErr != nil {
					return lcosErr
				}
			}
			if hasCbLmEventFlag {
				manualManipulationRecordCopy := *manualManipulationRecord
				laneCodeList := strings.Split(manualManipulationRecordCopy.LaneCode, "|")
				cbLmLaneCode := laneCodeList[len(laneCodeList)-1]
				manualManipulationRecordCopy.ID = 0 // 复制record id置0，避免主键冲突
				manualManipulationRecordCopy.LaneCode = cbLmLaneCode

				cbLmRecord, lcosErr = a.manualManipulationDao.CreateManualManipulationRulesRecord(ctx, &manualManipulationRecordCopy)
				if lcosErr != nil {
					return lcosErr
				}
			}
			// 填充product、lane以及record信息
			for _, model := range laneManualManipulationLocationRules {
				tmp := &lane_manual_manipulation_rule.LaneCDTManualManipulationRouteLocationRuleTab{
					UpdateEvent:                    model.UpdateEvent,
					OriginLocationID:               model.OriginLocationID,
					OriginLocationParentLevel:      model.OriginLocationParentLevel,
					DestinationLocationID:          model.DestinationLocationID,
					DestinationLocationParentLevel: model.DestinationLocationParentLevel,
					CdtMinDelta:                    model.CdtMinDelta,
					CdtMaxDelta:                    model.CdtMaxDelta,
					CbLmDelta:                      model.CbLmDelta,
				}
				if cbFlag && cdt_common2.IsCbLmEvent(tmp.UpdateEvent) {
					tmp.RecordID = cbLmRecord.ID
					tmp.ProductID = cbLmRecord.ProductID
					tmp.LaneCode = cbLmRecord.LaneCode
				} else {
					tmp.RecordID = record.ID
					tmp.ProductID = record.ProductID
					tmp.LaneCode = record.LaneCode
				}
				addLaneLocationRules = append(addLaneLocationRules, tmp)
			}
			for _, model := range laneManualManipulationPostCodeRules {
				tmp := &lane_manual_manipulation_rule.LaneCDTManualManipulationRoutePostCodeRuleTab{
					UpdateEvent:               model.UpdateEvent,
					Region:                    model.Region,
					OriginLocationID:          model.OriginLocationID,
					OriginLocationParentLevel: model.OriginLocationParentLevel,
					DestinationPostcode:       model.DestinationPostcode,
					CdtMinDelta:               model.CdtMinDelta,
					CdtMaxDelta:               model.CdtMaxDelta,
					CbLmDelta:                 model.CbLmDelta,
				}
				if cbFlag && cdt_common2.IsCbLmEvent(tmp.UpdateEvent) {
					tmp.RecordID = cbLmRecord.ID
					tmp.ProductID = cbLmRecord.ProductID
					tmp.LaneCode = cbLmRecord.LaneCode
				} else {
					tmp.RecordID = record.ID
					tmp.ProductID = record.ProductID
					tmp.LaneCode = record.LaneCode
				}
				addLanePostcodeRules = append(addLanePostcodeRules, tmp)
			}
			for _, model := range laneManualManipulationCepRangeRules {
				tmp := &lane_manual_manipulation_rule.LaneCDTManualManipulationRouteCepRangeRuleTab{
					UpdateEvent:               model.UpdateEvent,
					OriginLocationID:          model.OriginLocationID,
					OriginLocationParentLevel: model.OriginLocationParentLevel,
					DestinationPostcodeLeft:   model.DestinationPostcodeLeft,
					DestinationPostcodeRight:  model.DestinationPostcodeRight,
					CdtMinDelta:               model.CdtMinDelta,
					CdtMaxDelta:               model.CdtMaxDelta,
					CbLmDelta:                 model.CbLmDelta,
				}
				if cbFlag && cdt_common2.IsCbLmEvent(tmp.UpdateEvent) {
					tmp.RecordID = cbLmRecord.ID
					tmp.ProductID = cbLmRecord.ProductID
					tmp.LaneCode = cbLmRecord.LaneCode
				} else {
					tmp.RecordID = record.ID
					tmp.ProductID = record.ProductID
					tmp.LaneCode = record.LaneCode
				}
				addLaneCepRangeRules = append(addLaneCepRangeRules, tmp)
			}
			if len(addLaneLocationRules) > 0 {
				lcosErr = a.laneManualManipulationDao.BatchCreateLaneManualManipulationRouteLocationRules(ctx, region, addLaneLocationRules)
				if lcosErr != nil {
					return lcosErr
				}
			}
			if len(addLanePostcodeRules) > 0 {
				lcosErr = a.laneManualManipulationDao.BatchCreateLaneManualManipulationRoutePostCodeRules(ctx, region, addLanePostcodeRules)
				if lcosErr != nil {
					return lcosErr
				}
			}
			if len(addLaneCepRangeRules) > 0 {
				lcosErr = a.laneManualManipulationDao.BatchCreateLaneManualManipulationRouteCepRangeRules(ctx, region, addLaneCepRangeRules)
				if lcosErr != nil {
					return lcosErr
				}
			}
		}
		return nil
	}
	if lcosErr := ctx.Transaction(fc); lcosErr != nil {
		return lcosErr
	}

	// SPLN-35066 数据完成写入后，发送异常值告警消息
	sendCdtDeltaExceedThresholdAlertMessage(ctx, manualManipulationRecords, deltaExceedThresholdRowsList, ctx.GetUserEmail())
	return nil
}

func (a *manualManipulationService) DeleteManualManipulationRuleById(ctx utils.LCOSContext, request *manual_manipulation_rule.GetManualManipulationRequest) *lcos_error.LCOSError {
	region := strings.ToUpper(ctx.GetCountry())
	// 1. 查找该rule 然后看下mode 是 location 还是route
	nowTime := utils.GetTimestamp(ctx)
	// 获取record信息
	recordTab, lcosErr := a.manualManipulationDao.GetManualManipulationRecordTabByID(ctx, request.ID)
	if lcosErr != nil {
		return lcosErr
	}

	// 先判断状态是不是disable 如果不是，则不允许删除
	if recordTab.StatusID != constant.Disable && (recordTab.ExpirationDate > nowTime) {
		return lcos_error.NewLCOSError(lcos_error.ManiRuleOnlyDisableAllowDelete, "only disable status can be delete")
	}
	if recordTab.Mode == constant.CreateByLocation {
		if recordTab.CdtType == edd_constant.LaneCdtType {
			if err := a.laneManualManipulationDao.DeleteLaneManualManipulationLocationRuleByRecordID(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID); err != nil {
				return err
			}
			if err := a.laneManualManipulationDao.DeleteLaneManualManipulationPostCodeRuleByRecordID(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID); err != nil {
				return err
			}
			if err := a.laneManualManipulationDao.DeleteLaneManualManipulationCepRangeRuleByRecordID(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID); err != nil {
				return err
			}
			if err := a.manualManipulationDao.DeleteManualManipulationRecordTabByID(ctx, recordTab.ID); err != nil {
				return err
			}
		} else {
			fc := func() *lcos_error.LCOSError {
				// 删除 logistic_cdt_manual_manipulation_location_rule_tab by record id
				lcosErr = a.manualManipulationDao.DeleteManualManipulationLocationRuleByRecordID(ctx, recordTab.ID)
				if lcosErr != nil {
					return lcosErr
				}
				// delete logistic_cdt_manual_manipulation_postcode_rule_tab by record id
				lcosErr = a.manualManipulationDao.DeleteManualManipulationPostcodeRuleByRecordID(ctx, recordTab.ID)
				if lcosErr != nil {
					return lcosErr
				}
				// delete logistic_cdt_manual_manipulation_zipcode_rule_tab by record id
				lcosErr = a.manualManipulationDao.DeleteManualManipulationZipcodeRuleByRecordID(ctx, recordTab.ID)
				if lcosErr != nil {
					return lcosErr
				}
				// delete logistic_cdt_manual_manipulation_rule_record_tab by id
				lcosErr = a.manualManipulationDao.DeleteManualManipulationRecordTabByID(ctx, recordTab.ID)
				if lcosErr != nil {
					return lcosErr
				}
				return nil
			}
			if err := ctx.Transaction(fc); err != nil {
				return err
			}
		}
	} else if recordTab.Mode == constant.CreateByRoute {
		if recordTab.CdtType == edd_constant.LaneCdtType {
			if err := a.laneManualManipulationDao.DeleteLaneManualManipulationRouteLocationRuleByRecordID(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID); err != nil {
				return err
			}
			if err := a.laneManualManipulationDao.DeleteLaneManualManipulationRoutePostCodeRuleByRecordID(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID); err != nil {
				return err
			}
			if err := a.laneManualManipulationDao.DeleteLaneManualManipulationRouteCepRangeRuleByRecordID(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID); err != nil {
				return err
			}
			if err := a.manualManipulationDao.DeleteManualManipulationRecordTabByID(ctx, recordTab.ID); err != nil {
				return err
			}
		} else {
			fc := func() *lcos_error.LCOSError {
				// 删除 logistic_cdt_manual_manipulation_location_rule_tab by record id
				lcosErr = a.manualManipulationDao.DeleteManualManipulationRouteLocationRuleByRecordID(ctx, recordTab.ID)
				if lcosErr != nil {
					return lcosErr
				}
				// delete logistic_cdt_manual_manipulation_postcode_rule_tab by record id
				lcosErr = a.manualManipulationDao.DeleteManualManipulationRoutePostcodeRuleByRecordID(ctx, recordTab.ID)
				if lcosErr != nil {
					return lcosErr
				}
				// delete logistic_cdt_manual_manipulation_zipcode_rule_tab by record id
				lcosErr = a.manualManipulationDao.DeleteManualManipulationRouteZipcodeRuleByRecordID(ctx, recordTab.ID)
				if lcosErr != nil {
					return lcosErr
				}
				// delete logistic_cdt_manual_manipulation_rule_record_tab by id
				lcosErr = a.manualManipulationDao.DeleteManualManipulationRecordTabByID(ctx, recordTab.ID)
				if lcosErr != nil {
					return lcosErr
				}
				return nil
			}
			if err := ctx.Transaction(fc); err != nil {
				return err
			}
		}
	}
	return nil
}

func (a *manualManipulationService) GetManualManipulationRuleByID(ctx utils.LCOSContext, id uint64) (*manual_manipulation_rule.CreateManualManipulationResponse, *lcos_error.LCOSError) {
	response := &manual_manipulation_rule.CreateManualManipulationResponse{}
	var locationInfoMap = map[string]bool{}
	var destinationInfoMap = map[string]bool{}

	// 获取record信息
	recordTab, lcosErr := a.manualManipulationDao.GetManualManipulationRecordTabByID(ctx, id)
	if lcosErr != nil {
		return nil, lcosErr
	}
	response.CdtType = recordTab.CdtType
	response.ObjectType = recordTab.ObjectType
	response.UpdateEvent = recordTab.UpdateEvent
	response.ProductID = recordTab.ProductID
	response.ProductName = recordTab.ProductName
	response.LaneCode = recordTab.LaneCode
	response.TaskName = recordTab.TaskName
	response.CBType = recordTab.CBType
	response.MaskingType = recordTab.MaskingType
	response.IntegratedType = recordTab.IntegratedType
	response.IsSiteLine = recordTab.IsSiteLine
	response.IsLM = recordTab.IsLM
	response.OriginLocationLevel = recordTab.OriginLocationLevel
	response.DestinationLocationLevel = recordTab.DestinationLocationLevel
	response.EffectiveDate = recordTab.EffectiveDate
	response.ExpirationDate = recordTab.ExpirationDate
	response.CDTMinDelta = recordTab.CdtMinDelta
	response.CDTMaxDelta = recordTab.CdtMaxDelta
	response.Mode = recordTab.Mode
	response.RouteFileName = recordTab.RouteFileName
	response.RouteFileUrl = recordTab.RouteFileUrl
	region := ctx.GetCountry()

	if recordTab.Mode == constant.CreateByRoute {
		return response, nil
	}

	// 根据record tab的destination location level来确定是cep range还是location
	if recordTab.DestinationLocationLevel == constant.CepRange {
		if recordTab.CdtType == edd_constant.LaneCdtType {
			// SPLN-24104, 填充lane维度数据信息
			laneCepRangeModels, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationCepRangeRule(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(laneCepRangeModels) == 0 {
				return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find lane manual manipulation ceprange rule|id=%v", id))
			}
			for _, laneCepRangeModel := range laneCepRangeModels {
				originLocationID := strconv.Itoa(int(laneCepRangeModel.OriginLocationID))
				if _, ok := locationInfoMap[originLocationID]; !ok {
					response.OriginLocationList = append(response.OriginLocationList, &manual_manipulation_rule.SimpleLocation{
						ParentLevelID: laneCepRangeModel.OriginLocationParentLevel,
						LocationID:    originLocationID,
					})
					locationInfoMap[originLocationID] = true
				}
			}
		} else {
			postcodeModels, lcosErr := a.manualManipulationDao.GetManualManipulationPostcodeRuleByRecordID(ctx, id)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(postcodeModels) == 0 {
				return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation postcode rule|id=%v", id))
			}
			for _, postcodeModel := range postcodeModels {
				originLocationID := strconv.Itoa(int(postcodeModel.OriginLocationID))
				if _, ok := locationInfoMap[originLocationID]; !ok {
					response.OriginLocationList = append(response.OriginLocationList, &manual_manipulation_rule.SimpleLocation{
						ParentLevelID: postcodeModel.OriginLocationParentLevel,
						LocationID:    originLocationID,
					})
					locationInfoMap[originLocationID] = true
				}
			}
		}
		response.DestinationCepRangeUrl = fmt.Sprintf("%v:%v", constant.DUPLICATE_FROM_PREFIX, recordTab.ID)
		response.DestinationCepRangeFileName = recordTab.DestinationCepRangeFileName
	} else if recordTab.DestinationLocationLevel == constant.CDTPostcode {
		if recordTab.CdtType == edd_constant.LaneCdtType {
			// SPLN-24104, 填充lane维度数据信息
			lanePostCodeModels, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationPostCodeRule(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(lanePostCodeModels) == 0 {
				return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation zipcode rule|id=%v", id))
			}
			for _, lanePostCodeModel := range lanePostCodeModels {
				originLocationID := strconv.Itoa(int(lanePostCodeModel.OriginLocationID))
				if _, ok := locationInfoMap[originLocationID]; !ok {
					response.OriginLocationList = append(response.OriginLocationList, &manual_manipulation_rule.SimpleLocation{
						ParentLevelID: lanePostCodeModel.OriginLocationParentLevel,
						LocationID:    originLocationID,
					})
					locationInfoMap[originLocationID] = true
				}
			}
		} else {
			postcodeModels, lcosErr := a.manualManipulationDao.GetManualManipulationZipcodeRuleByRecordID(ctx, id)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(postcodeModels) == 0 {
				return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation zipcode rule|id=%v", id))
			}
			for _, postcodeModel := range postcodeModels {
				originLocationID := strconv.Itoa(int(postcodeModel.OriginLocationID))
				if _, ok := locationInfoMap[originLocationID]; !ok {
					response.OriginLocationList = append(response.OriginLocationList, &manual_manipulation_rule.SimpleLocation{
						ParentLevelID: postcodeModel.OriginLocationParentLevel,
						LocationID:    originLocationID,
					})
					locationInfoMap[originLocationID] = true
				}
			}
		}
		response.DestinationPostcodeUrl = fmt.Sprintf("%v:%v", constant.DUPLICATE_FROM_PREFIX, recordTab.ID)
		response.DestinationPostcodeFileName = recordTab.DestinationPostcodeFileName
	} else {
		if recordTab.CdtType == edd_constant.LaneCdtType {
			// SPLN-24104, 填充lane维度数据信息
			laneLocationModels, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationLocationRule(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(laneLocationModels) == 0 {
				return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find lane manual manipulation location rule|id=%v", id))
			}
			for _, laneLocationModel := range laneLocationModels {

				// 填充location 信息
				originLocationID := strconv.Itoa(int(laneLocationModel.OriginLocationID))
				destinationLocationID := strconv.Itoa(int(laneLocationModel.DestinationLocationID))

				if _, ok := locationInfoMap[originLocationID]; !ok {
					response.OriginLocationList = append(response.OriginLocationList, &manual_manipulation_rule.SimpleLocation{
						ParentLevelID: laneLocationModel.OriginLocationParentLevel,
						LocationID:    originLocationID,
					})
					locationInfoMap[originLocationID] = true
				}
				if _, ok := destinationInfoMap[destinationLocationID]; !ok {
					response.DestinationLocationList = append(response.DestinationLocationList, &manual_manipulation_rule.SimpleLocation{
						ParentLevelID: laneLocationModel.DestinationLocationParentLevel,
						LocationID:    destinationLocationID,
					})
					destinationInfoMap[destinationLocationID] = true
				}
			}
		} else {
			locationModels, lcosErr := a.manualManipulationDao.GetManualManipulationLocationRuleByRecordID(ctx, id)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(locationModels) == 0 {
				return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find lane manual manipulation location rule|id=%v", id))
			}
			for _, locationModel := range locationModels {

				// 填充location 信息
				originLocationID := strconv.Itoa(int(locationModel.OriginLocationID))
				destinationLocationID := strconv.Itoa(int(locationModel.DestinationLocationID))

				if _, ok := locationInfoMap[originLocationID]; !ok {
					response.OriginLocationList = append(response.OriginLocationList, &manual_manipulation_rule.SimpleLocation{
						ParentLevelID: locationModel.OriginLocationParentLevel,
						LocationID:    originLocationID,
					})
					locationInfoMap[originLocationID] = true
				}
				if _, ok := destinationInfoMap[destinationLocationID]; !ok {
					response.DestinationLocationList = append(response.DestinationLocationList, &manual_manipulation_rule.SimpleLocation{
						ParentLevelID: locationModel.DestinationLocationParentLevel,
						LocationID:    destinationLocationID,
					})
					destinationInfoMap[destinationLocationID] = true
				}
			}
		}
	}
	return response, nil
}

func (a *manualManipulationService) ListManualManipulationByParamsPaging(ctx utils.LCOSContext, request *manual_manipulation_rule.ListManualManipulationRequest) (*manual_manipulation_rule.ListManualManipulationResponse, *lcos_error.LCOSError) {
	nowTime := utils.GetTimestamp(ctx)

	var page uint32 = 1
	var count uint32 = 10

	region := strings.ToUpper(ctx.GetCountry())

	// 参数解析
	var queryMap = make(map[string]interface{})
	queryMap["region"] = region

	if request.CdtType != nil {
		queryMap["cdt_type"] = *request.CdtType
	}
	if request.ProductID != nil {
		queryMap["product_id"] = *request.ProductID
	}
	if request.ObjectType != nil {
		queryMap["object_type"] = *request.ObjectType
	}

	// -1表示All，所以检索条件需要加上-1
	if request.IntegratedType != nil {
		queryMap["integrated_type in"] = []int8{int8(*request.IntegratedType), -1}
	}
	if request.MaskingType != nil {
		queryMap["masking_type in"] = []int8{int8(*request.MaskingType), -1}
	}
	if request.CBType != nil {
		queryMap["cb_type in"] = []int8{int8(*request.CBType), -1}
	}
	if request.StatusID != nil {
		for key, value := range cdt_common2.GenerateQueryForStatusForManualManipulationRule(*request.StatusID, nowTime) {
			queryMap[key] = value
		}
	}
	queryMap["is_site_line"] = *request.IsSiteLine
	queryMap["is_lm"] = 0
	if request.PageNo != nil {
		page = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}

	nowTimestamp := utils.GetTimestamp(ctx)

	// 查询数据库
	recordTabs, total, lcosErr := a.manualManipulationDao.ListManualManipulationRulesRecordByParamsPaging(ctx, queryMap, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}

	for _, model := range recordTabs {
		// 如果当前任务为upcoming或者active，过期需要设置为disable。如果是upcoming，超过effective，且没有过期需要设置为active
		if model.NeedToBeUpdatedToDisable(nowTimestamp) {
			model.StatusID = constant.Disable
			// 开启协程更新数据，不阻塞主流程
			go a.manualManipulationDao.UpdateManualManipulationRuleRecordsGeneral(ctx, map[string]interface{}{"id": model.ID}, map[string]interface{}{"status_id": constant.Disable})

		}
		if model.NeedToBeUpdatedToActive(nowTimestamp) {
			model.StatusID = constant.Active
			go a.manualManipulationDao.UpdateManualManipulationRuleRecordsGeneral(ctx, map[string]interface{}{"id": model.ID}, map[string]interface{}{"status_id": constant.Active})
		}
	}

	response := &manual_manipulation_rule.ListManualManipulationResponse{
		PageNo: page,
		Count:  count,
		Total:  total,
		List:   recordTabs,
	}
	return response, nil
}

func (a *manualManipulationService) GetCepRangeData(ctx utils.LCOSContext, request *manual_manipulation_rule.GetManualManipulationRequest) ([]*cdt_common2.CepRange, string, *lcos_error.LCOSError) {
	// check manual manipulation rule is cep range
	manualManipulationRule, lcosErr := a.GetManualManipulationRuleByID(ctx, request.ID)
	if lcosErr != nil {
		return nil, "", lcosErr
	}
	if manualManipulationRule.DestinationLocationLevel != constant.CepRange {
		return nil, "", lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("manual manipulation is not cep range type|id=%d", request.ID))
	}
	if manualManipulationRule.CdtType == edd_constant.LaneCdtType {
		// SPLN-24104, 支持导出lane维度cdt的ceprange数据
		models, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationCepRangeRule(ctx, ctx.GetCountry(), manualManipulationRule.ProductID, manualManipulationRule.LaneCode, request.ID)
		if lcosErr != nil {
			return nil, "", lcosErr
		}
		if len(models) == 0 {
			return nil, "", lcos_error.NewLCOSError(lcos_error.CepRangeNotFoundErrorCode, fmt.Sprintf("cannot find cep range data|id=%v", request.ID))
		}
		var fileName = manualManipulationRule.DestinationCepRangeFileName
		var returnModels []*cdt_common2.CepRange
		cepRangeMap := map[string]bool{}
		for _, model := range models {
			if _, ok := cepRangeMap[fmt.Sprintf("%d:%d", model.DestinationPostcodeLeft, model.DestinationPostcodeRight)]; !ok {
				returnModels = append(returnModels, &cdt_common2.CepRange{
					CepLeft:  int(model.DestinationPostcodeLeft),
					CepRight: int(model.DestinationPostcodeRight),
				})
				cepRangeMap[fmt.Sprintf("%d:%d", model.DestinationPostcodeLeft, model.DestinationPostcodeRight)] = true
			}
		}
		return returnModels, fileName, nil
	} else {
		// 导出product维度ceprange数据
		models, lcosErr := a.manualManipulationDao.GetManualManipulationPostcodeRuleByRecordID(ctx, request.ID)
		if lcosErr != nil {
			return nil, "", lcosErr
		}
		if len(models) == 0 {
			return nil, "", lcos_error.NewLCOSError(lcos_error.CepRangeNotFoundErrorCode, fmt.Sprintf("cannot find cep range data|id=%v", request.ID))
		}
		var fileName = manualManipulationRule.DestinationCepRangeFileName
		var returnModels []*cdt_common2.CepRange
		cepRangeMap := map[string]bool{}
		for _, model := range models {
			if _, ok := cepRangeMap[fmt.Sprintf("%d:%d", model.DestinationPostcodeLeft, model.DestinationPostcodeRight)]; !ok {
				returnModels = append(returnModels, &cdt_common2.CepRange{
					CepLeft:  int(model.DestinationPostcodeLeft),
					CepRight: int(model.DestinationPostcodeRight),
				})
				cepRangeMap[fmt.Sprintf("%d:%d", model.DestinationPostcodeLeft, model.DestinationPostcodeRight)] = true
			}
		}
		return returnModels, fileName, nil
	}
}

func (a *manualManipulationService) ToggleCdtManualManipulationRuleStatus(ctx utils.LCOSContext, request *manual_manipulation_rule.ToggleManualManipulationRuleStatusRequest) (*auto_update_rule.ActionResult, *lcos_error.LCOSError) {
	region := strings.ToUpper(ctx.GetCountry())

	var nowTimestamp = utils.GetTimestamp(ctx) // 当前时间戳

	// 先获取当前的Record
	recordTab, lcosErr := a.manualManipulationDao.GetManualManipulationRecordTabByID(ctx, request.ID)
	if lcosErr != nil {
		return nil, lcosErr
	}
	if recordTab == nil {
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation rule|id=%v", request.ID))
	}

	if recordTab.Mode == constant.CreateByRoute {
		if recordTab.CdtType == edd_constant.LaneCdtType {
			var flag bool
			routeLocationRules, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationRouteLocationRule(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(routeLocationRules) != 0 {
				flag = true
			}
			if !flag {
				routePostcodeRules, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationRoutePostCodeRule(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(routePostcodeRules) != 0 {
					flag = true
				}
			}

			if !flag {
				routeCepRangeRules, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationRouteCepRangeRule(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(routeCepRangeRules) != 0 {
					flag = true
				}
			}

			if !flag {
				return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation postcode or location or ceprange rule|id:%v", request.ID))
			}
		} else {
			var flag bool
			routeLocationRules, lcosErr := a.manualManipulationDao.GetManualManipulationRouteLocationRuleByRecordID(ctx, recordTab.ID)
			if lcosErr != nil {
				return nil, lcosErr
			}
			if len(routeLocationRules) != 0 {
				flag = true
			}
			if !flag {
				routePostcodeRules, lcosErr := a.manualManipulationDao.GetManualManipulationRoutePostcodeRuleByRecordID(ctx, recordTab.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(routePostcodeRules) != 0 {
					flag = true
				}
			}

			if !flag {
				routeZipcodeRules, lcosErr := a.manualManipulationDao.GetManualManipulationRouteZipcodeRuleByRecordID(ctx, recordTab.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(routeZipcodeRules) != 0 {
					flag = true
				}
			}

			if !flag {
				return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation postcode or location or zipcode rule|id:%v", request.ID))
			}
		}

	} else {
		if recordTab.CdtType == edd_constant.LaneCdtType {
			if recordTab.DestinationLocationLevel == constant.CepRange {
				// 获取cep range
				manualUpdatePostcodeRules, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationCepRangeRule(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(manualUpdatePostcodeRules) == 0 {
					return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation cep range rule|id:%v", request.ID))
				}
			} else if recordTab.DestinationLocationLevel == constant.CDTPostcode {
				// 获取cep range
				manualUpdateZipcodeRules, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationPostCodeRule(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(manualUpdateZipcodeRules) == 0 {
					return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation postcode rule|id:%v", request.ID))
				}

			} else {
				manualUpdateLocationRules, lcosErr := a.laneManualManipulationDao.GetLaneManualManipulationLocationRule(ctx, region, recordTab.ProductID, recordTab.LaneCode, recordTab.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(manualUpdateLocationRules) == 0 {
					return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation location rule|id:%v", request.ID))
				}
			}
		} else {
			if recordTab.DestinationLocationLevel == constant.CepRange {
				// 获取cep range
				manualUpdatePostcodeRules, lcosErr := a.manualManipulationDao.GetManualManipulationPostcodeRuleByRecordID(ctx, request.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(manualUpdatePostcodeRules) == 0 {
					return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation cep range rule|id:%v", request.ID))
				}
			} else if recordTab.DestinationLocationLevel == constant.CDTPostcode {
				// 获取cep range
				manualUpdateZipcodeRules, lcosErr := a.manualManipulationDao.GetManualManipulationZipcodeRuleByRecordID(ctx, request.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(manualUpdateZipcodeRules) == 0 {
					return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation postcode rule|id:%v", request.ID))
				}

			} else {
				manualUpdateLocationRules, lcosErr := a.manualManipulationDao.GetManualManipulationLocationRuleByRecordID(ctx, request.ID)
				if lcosErr != nil {
					return nil, lcosErr
				}
				if len(manualUpdateLocationRules) == 0 {
					return nil, lcos_error.NewLCOSError(lcos_error.NotFoundManualManipulationRuleErrorCode, fmt.Sprintf("cannot find manual manipulation location rule|id:%v", request.ID))
				}
			}
		}
	}

	switch request.ActionCode {
	case constant.CHECK_CAN_DISABLE:
		// 只有upcoming和active且没有过期的任务可以被disable
		if !(recordTab.IsUpcoming(nowTimestamp) || recordTab.IsActive(nowTimestamp)) {
			return &auto_update_rule.ActionResult{
				ActionResult: auto_update_rule.ACTION_NOT_ALLOWED,
				Message:      "current task is not allowed to be disabled",
			}, nil
		}
		return &auto_update_rule.ActionResult{
			ActionResult: auto_update_rule.ACTION_IS_ALLOWED,
			Message:      "Are you sure you want to disable the Active/Upcoming job?",
		}, nil
	case constant.DISABLE:
		// 只有upcoming和active任务且没有过期的任务可以被disable
		if !(recordTab.IsActive(nowTimestamp) || recordTab.IsUpcoming(nowTimestamp)) {
			return nil, lcos_error.NewLCOSError(lcos_error.TaskNotAllowedToDisableErrorCode, "current task is not allowed to be disabled")
		}

		// 更新任务的状态
		lcosErr = a.manualManipulationDao.UpdateManualManipulationRuleRecordsGeneral(ctx, map[string]interface{}{"id": request.ID, "region": region}, map[string]interface{}{"status_id": constant.Disable})
		if lcosErr != nil {
			return nil, lcosErr
		}
		return nil, nil
	default:
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "current action code is not valid")
	}
}

func (a *manualManipulationService) ListProductLaneCodeInfo(ctx utils.LCOSContext, request *manual_manipulation_rule.ListProductLaneCodeReq) (*product_service.LaneCodeInfo, *lcos_error.LCOSError) {
	region := ctx.GetCountry()
	return product_service.GetLaneCodeInfoByProductId(ctx, region, request.ProductID)
}

func (a *manualManipulationService) ListLaneManualManipulationLocation(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationLocationRuleTab, *lcos_error.LCOSError) {
	return a.laneManualManipulationDao.GetLaneManualManipulationLocationRule(ctx, region, productId, laneCode, recordId)
}

func (a *manualManipulationService) ListLaneManualManipulationPostCode(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationPostCodeRuleTab, *lcos_error.LCOSError) {
	return a.laneManualManipulationDao.GetLaneManualManipulationPostCodeRule(ctx, region, productId, laneCode, recordId)
}

func (a *manualManipulationService) ListLaneManualManipulationCepRange(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationCepRangeRuleTab, *lcos_error.LCOSError) {
	return a.laneManualManipulationDao.GetLaneManualManipulationCepRangeRule(ctx, region, productId, laneCode, recordId)
}

func (a *manualManipulationService) ListLaneManualManipulationRouteLocation(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteLocationRuleTab, *lcos_error.LCOSError) {
	return a.laneManualManipulationDao.GetLaneManualManipulationRouteLocationRule(ctx, region, productId, laneCode, recordId)
}

func (a *manualManipulationService) ListLaneManualManipulationRoutePostCode(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationRoutePostCodeRuleTab, *lcos_error.LCOSError) {
	return a.laneManualManipulationDao.GetLaneManualManipulationRoutePostCodeRule(ctx, region, productId, laneCode, recordId)
}

func (a *manualManipulationService) ListLaneManualManipulationRouteCepRange(ctx utils.LCOSContext, region string, recordId uint64, productId, laneCode string) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteCepRangeRuleTab, *lcos_error.LCOSError) {
	return a.laneManualManipulationDao.GetLaneManualManipulationRouteCepRangeRule(ctx, region, productId, laneCode, recordId)
}

func (a *manualManipulationService) ListManualManipulationRulesRecordByParamsPaging(ctx utils.LCOSContext, query map[string]interface{}, page, count uint32) ([]*manual_manipulation_record.CDTManualManipulationRuleRecordTab, uint32, *lcos_error.LCOSError) {
	return a.manualManipulationDao.ListManualManipulationRulesRecordByParamsPaging(ctx, query, page, count)
}

func (a *manualManipulationService) DeleteManualManipulationRouteLocationRuleByQuery(ctx utils.LCOSContext, recordId uint64, batchNum int) (int64, *lcos_error.LCOSError) {
	return a.manualManipulationDao.DeleteManualManipulationRouteLocationRuleByQuery(ctx, map[string]interface{}{"record_id": recordId}, batchNum)
}
func (a *manualManipulationService) DeleteManualManipulationRoutePostcodeRuleByQuery(ctx utils.LCOSContext, recordId uint64, batchNum int) (int64, *lcos_error.LCOSError) {
	return a.manualManipulationDao.DeleteManualManipulationRoutePostcodeRuleByQuery(ctx, map[string]interface{}{"record_id": recordId}, batchNum)
}
func (a *manualManipulationService) DeleteManualManipulationRouteZipcodeRuleByQuery(ctx utils.LCOSContext, recordId uint64, batchNum int) (int64, *lcos_error.LCOSError) {
	return a.manualManipulationDao.DeleteManualManipulationRouteZipcodeRuleByQuery(ctx, map[string]interface{}{"record_id": recordId}, batchNum)
}

func (a *manualManipulationService) DeleteManualManipulationRecordTabByID(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return a.manualManipulationDao.DeleteManualManipulationRecordTabByID(ctx, id)
}
