package manual_manipulation_rule

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_record"
	manual_manipulation_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

// MockCDTManualManipulationRuleTabDAO mocks the DAO interface
type MockCDTManualManipulationRuleTabDAO struct {
	mock.Mock
}

func (m *MockCDTManualManipulationRuleTabDAO) ListManualManipulationRulesRecordByParamsPaging(ctx utils.LCOSContext, query map[string]interface{}, page, count uint32) ([]*manual_manipulation_record.CDTManualManipulationRuleRecordTab, uint32, *lcos_error.LCOSError) {
	args := m.Called(ctx, query, page, count)
	return args.Get(0).([]*manual_manipulation_record.CDTManualManipulationRuleRecordTab), args.Get(1).(uint32), args.Get(2).(*lcos_error.LCOSError)
}

func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationRouteLocationRuleByQuery(ctx utils.LCOSContext, queryMap map[string]interface{}, batchNum int) (int64, *lcos_error.LCOSError) {
	args := m.Called(ctx, queryMap, batchNum)
	return args.Get(0).(int64), args.Get(1).(*lcos_error.LCOSError)
}

func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationRoutePostcodeRuleByQuery(ctx utils.LCOSContext, queryMap map[string]interface{}, batchNum int) (int64, *lcos_error.LCOSError) {
	args := m.Called(ctx, queryMap, batchNum)
	return args.Get(0).(int64), args.Get(1).(*lcos_error.LCOSError)
}

func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationRouteZipcodeRuleByQuery(ctx utils.LCOSContext, queryMap map[string]interface{}, batchNum int) (int64, *lcos_error.LCOSError) {
	args := m.Called(ctx, queryMap, batchNum)
	return args.Get(0).(int64), args.Get(1).(*lcos_error.LCOSError)
}

func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationRecordTabByID(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	args := m.Called(ctx, id)
	return args.Get(0).(*lcos_error.LCOSError)
}

// 以下方法用于实现接口，但在测试中不会被调用
func (m *MockCDTManualManipulationRuleTabDAO) CreateManualManipulationRulesRecord(ctx utils.LCOSContext, record *manual_manipulation_record.CDTManualManipulationRuleRecordTab) (*manual_manipulation_record.CDTManualManipulationRuleRecordTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) BatchCreateManualManipulationLocationRules(ctx utils.LCOSContext, rules []*manual_manipulation_rule2.CDTManualManipulationLocationRuleTab) ([]*manual_manipulation_rule2.CDTManualManipulationLocationRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) BatchCreateManualManipulationPostcodeRules(ctx utils.LCOSContext, rules []*manual_manipulation_rule2.CDTManualManipulationPostcodeRuleTab) ([]*manual_manipulation_rule2.CDTManualManipulationPostcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) GetManualManipulationRecordTabByID(ctx utils.LCOSContext, id uint64) (*manual_manipulation_record.CDTManualManipulationRuleRecordTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) GetManualManipulationLocationRuleByRecordID(ctx utils.LCOSContext, id uint64) ([]*manual_manipulation_rule2.CDTManualManipulationLocationRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) GetManualManipulationPostcodeRuleByRecordID(ctx utils.LCOSContext, id uint64) ([]*manual_manipulation_rule2.CDTManualManipulationPostcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) SearchManualManipulationPostcodeRuleGeneral(ctx utils.LCOSContext, query map[string]interface{}) ([]*manual_manipulation_rule2.CDTManualManipulationPostcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) SearchManualManipulationLocationRuleGeneral(ctx utils.LCOSContext, query map[string]interface{}) ([]*manual_manipulation_rule2.CDTManualManipulationLocationRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) SearchManualManipulationRuleRecordsGeneral(ctx utils.LCOSContext, query map[string]interface{}) ([]*manual_manipulation_record.CDTManualManipulationRuleRecordTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) UpdateManualManipulationRuleRecordsGeneral(ctx utils.LCOSContext, query map[string]interface{}, updatedData map[string]interface{}) *lcos_error.LCOSError {
	return nil
}
func (m *MockCDTManualManipulationRuleTabDAO) QueryManualManipulationUsingCache(ctx utils.LCOSContext, cdtQuerys []*cdt_calculation.CdtQuery) (*cdt_calculation.CdtManualManipulation, *cdt_calculation.CdtQuery, *lcos_error.LCOSError) {
	return nil, nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) QueryManualManipulationRouteUsingCache(ctx utils.LCOSContext, region, productId string, cdtQuerys []*cdt_calculation.CdtQuery, requestTime uint32, cdtScene pb.CdtScene) (*cdt_calculation.CdtManualManipulation, *cdt_calculation.CdtQuery, *lcos_error.LCOSError) {
	return nil, nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) BatchCreateManualManipulationZipcodeRules(ctx utils.LCOSContext, rules []*manual_manipulation_rule2.CDTManualManipulationZipcodeRuleTab) ([]*manual_manipulation_rule2.CDTManualManipulationZipcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) GetManualManipulationZipcodeRuleByRecordID(ctx utils.LCOSContext, id uint64) ([]*manual_manipulation_rule2.CDTManualManipulationZipcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) SearchManualManipulationZipcodeRuleGeneral(ctx utils.LCOSContext, query map[string]interface{}) ([]*manual_manipulation_rule2.CDTManualManipulationZipcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationLocationRuleByRecordID(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationPostcodeRuleByRecordID(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationZipcodeRuleByRecordID(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationRouteLocationRuleByRecordID(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationRoutePostcodeRuleByRecordID(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockCDTManualManipulationRuleTabDAO) DeleteManualManipulationRouteZipcodeRuleByRecordID(ctx utils.LCOSContext, id uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockCDTManualManipulationRuleTabDAO) BatchCreateManualManipulationRouteLocationRules(ctx utils.LCOSContext, rules []*manual_manipulation_rule2.CDTManualManipulationRouteLocationRuleTab) ([]*manual_manipulation_rule2.CDTManualManipulationRouteLocationRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) BatchCreateManualManipulationRoutePostcodeRules(ctx utils.LCOSContext, rules []*manual_manipulation_rule2.CDTManualManipulationRoutePostcodeRuleTab) ([]*manual_manipulation_rule2.CDTManualManipulationRoutePostcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) BatchCreateManualManipulationRouteZipcodeRules(ctx utils.LCOSContext, rules []*manual_manipulation_rule2.CDTManualManipulationRouteZipcodeRuleTab) ([]*manual_manipulation_rule2.CDTManualManipulationRouteZipcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) GetManualManipulationRouteLocationRuleByRecordID(ctx utils.LCOSContext, id uint64) ([]*manual_manipulation_rule2.CDTManualManipulationRouteLocationRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) GetManualManipulationRoutePostcodeRuleByRecordID(ctx utils.LCOSContext, id uint64) ([]*manual_manipulation_rule2.CDTManualManipulationRoutePostcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) GetManualManipulationRouteZipcodeRuleByRecordID(ctx utils.LCOSContext, id uint64) ([]*manual_manipulation_rule2.CDTManualManipulationRouteZipcodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockCDTManualManipulationRuleTabDAO) GetManualManipulationRecord(ctx utils.LCOSContext, recordID uint64) (*manual_manipulation_record.CDTManualManipulationRuleRecordTab, *lcos_error.LCOSError) {
	return nil, nil
}

// MockLaneCdtManualManipulationRuleDAO mocks the lane DAO interface
type MockLaneCdtManualManipulationRuleDAO struct {
	mock.Mock
}

// 实现完整的LaneCdtManualManipulationRuleDAO接口
func (m *MockLaneCdtManualManipulationRuleDAO) GetLaneManualManipulationLocationRule(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationLocationRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetLaneManualManipulationPostCodeRule(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationPostCodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetLaneManualManipulationCepRangeRule(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationCepRangeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetLaneManualManipulationRouteLocationRule(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteLocationRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetLaneManualManipulationRoutePostCodeRule(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationRoutePostCodeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetLaneManualManipulationRouteCepRangeRule(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) ([]*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteCepRangeRuleTab, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) BatchCreateLaneManualManipulationLocationRules(ctx utils.LCOSContext, region string, rules []*lane_manual_manipulation_rule.LaneCDTManualManipulationLocationRuleTab) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) BatchCreateLaneManualManipulationPostCodeRules(ctx utils.LCOSContext, region string, rules []*lane_manual_manipulation_rule.LaneCDTManualManipulationPostCodeRuleTab) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) BatchCreateLaneManualManipulationCepRangeRules(ctx utils.LCOSContext, region string, rules []*lane_manual_manipulation_rule.LaneCDTManualManipulationCepRangeRuleTab) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) BatchCreateLaneManualManipulationRouteLocationRules(ctx utils.LCOSContext, region string, rules []*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteLocationRuleTab) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) BatchCreateLaneManualManipulationRoutePostCodeRules(ctx utils.LCOSContext, region string, rules []*lane_manual_manipulation_rule.LaneCDTManualManipulationRoutePostCodeRuleTab) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) BatchCreateLaneManualManipulationRouteCepRangeRules(ctx utils.LCOSContext, region string, rules []*lane_manual_manipulation_rule.LaneCDTManualManipulationRouteCepRangeRuleTab) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) DeleteLaneManualManipulationLocationRuleByRecordID(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) DeleteLaneManualManipulationPostCodeRuleByRecordID(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) DeleteLaneManualManipulationCepRangeRuleByRecordID(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) DeleteLaneManualManipulationRouteLocationRuleByRecordID(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) DeleteLaneManualManipulationRoutePostCodeRuleByRecordID(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) DeleteLaneManualManipulationRouteCepRangeRuleByRecordID(ctx utils.LCOSContext, region, productId, laneCode string, recordId uint64) *lcos_error.LCOSError {
	return nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetLocationManualManipulationUsingLayerCache(ctx utils.LCOSContext, cdtQuery *cdt_calculation.CdtQuery) ([]*cdt_calculation.CdtManualManipulation, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetPostcodeManualManipulationUsingLayerCache(ctx utils.LCOSContext, cdtQuery *cdt_calculation.CdtQuery) ([]*cdt_calculation.CdtManualManipulation, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetCepRangeManualManipulationUsingLayerCache(ctx utils.LCOSContext, cdtQuery *cdt_calculation.CdtQuery) ([]*cdt_calculation.CdtManualManipulation, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetRouteLocationManualManipulationUsingLayerCache(ctx utils.LCOSContext, cdtQuery *cdt_calculation.CdtQuery) ([]*cdt_calculation.CdtManualManipulation, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetRoutePostcodeManualManipulationUsingLayerCache(ctx utils.LCOSContext, cdtQuery *cdt_calculation.CdtQuery) ([]*cdt_calculation.CdtManualManipulation, *lcos_error.LCOSError) {
	return nil, nil
}
func (m *MockLaneCdtManualManipulationRuleDAO) GetRouteCepRangeManualManipulationUsingLayerCache(ctx utils.LCOSContext, cdtQuery *cdt_calculation.CdtQuery) ([]*cdt_calculation.CdtManualManipulation, *lcos_error.LCOSError) {
	return nil, nil
}

func TestManualManipulationService_ListManualManipulationRulesRecordByParamsPaging(t *testing.T) {
	// 准备测试数据
	mockDAO := &MockCDTManualManipulationRuleTabDAO{}
	mockLaneDAO := &MockLaneCdtManualManipulationRuleDAO{}

	service := &manualManipulationService{
		manualManipulationDao:     mockDAO,
		laneManualManipulationDao: mockLaneDAO,
	}

	ctx := utils.NewCommonCtx(context.Background())
	testQuery := map[string]interface{}{
		"status_id": 1,
		"region":    "SG",
	}
	testPage := uint32(1)
	testCount := uint32(10)

	// 创建期望的返回数据
	expectedRecords := []*manual_manipulation_record.CDTManualManipulationRuleRecordTab{
		{
			ID:          1,
			ProductID:   "90001",
			ProductName: "Test Product",
			Region:      "SG",
		},
	}
	expectedTotal := uint32(1)

	tests := []struct {
		name          string
		setupMock     func()
		expectedError bool
		expectedCount int
		description   string
	}{
		{
			name: "成功查询记录",
			setupMock: func() {
				mockDAO.On("ListManualManipulationRulesRecordByParamsPaging", ctx, testQuery, testPage, testCount).
					Return(expectedRecords, expectedTotal, (*lcos_error.LCOSError)(nil)).Once()
			},
			expectedError: false,
			expectedCount: 1,
			description:   "正常查询应该返回预期的记录",
		},
		{
			name: "查询失败",
			setupMock: func() {
				mockDAO.On("ListManualManipulationRulesRecordByParamsPaging", ctx, testQuery, testPage, testCount).
					Return([]*manual_manipulation_record.CDTManualManipulationRuleRecordTab{}, uint32(0),
						lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "database error")).Once()
			},
			expectedError: true,
			expectedCount: 0,
			description:   "当DAO返回错误时，应该正确传递错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置mock
			tt.setupMock()

			// 执行测试
			records, total, err := service.ListManualManipulationRulesRecordByParamsPaging(ctx, testQuery, testPage, testCount)

			// 验证结果
			if tt.expectedError {
				assert.NotNil(t, err, tt.description)
				assert.Equal(t, 0, len(records), "错误情况下应该返回空记录")
			} else {
				assert.Nil(t, err, tt.description)
				assert.Equal(t, tt.expectedCount, len(records), "应该返回正确数量的记录")
				assert.Equal(t, expectedTotal, total, "应该返回正确的总数")
			}

			// 验证mock调用
			mockDAO.AssertExpectations(t)
		})
	}
}

func TestManualManipulationService_DeleteManualManipulationRouteLocationRuleByQuery(t *testing.T) {
	mockDAO := &MockCDTManualManipulationRuleTabDAO{}
	mockLaneDAO := &MockLaneCdtManualManipulationRuleDAO{}

	service := &manualManipulationService{
		manualManipulationDao:     mockDAO,
		laneManualManipulationDao: mockLaneDAO,
	}

	ctx := utils.NewCommonCtx(context.Background())
	testRecordId := uint64(123)
	testBatchNum := 1000
	expectedQuery := map[string]interface{}{"record_id": testRecordId}

	tests := []struct {
		name          string
		setupMock     func()
		expectedRows  int64
		expectedError bool
		description   string
	}{
		{
			name: "成功删除记录",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRouteLocationRuleByQuery", ctx, expectedQuery, testBatchNum).
					Return(int64(5), (*lcos_error.LCOSError)(nil)).Once()
			},
			expectedRows:  5,
			expectedError: false,
			description:   "成功删除时应该返回删除的行数",
		},
		{
			name: "删除失败",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRouteLocationRuleByQuery", ctx, expectedQuery, testBatchNum).
					Return(int64(0), lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "delete failed")).Once()
			},
			expectedRows:  0,
			expectedError: true,
			description:   "删除失败时应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置mock
			tt.setupMock()

			// 执行测试
			rows, err := service.DeleteManualManipulationRouteLocationRuleByQuery(ctx, testRecordId, testBatchNum)

			// 验证结果
			if tt.expectedError {
				assert.NotNil(t, err, tt.description)
			} else {
				assert.Nil(t, err, tt.description)
				assert.Equal(t, tt.expectedRows, rows, "应该返回正确的删除行数")
			}

			// 验证mock调用
			mockDAO.AssertExpectations(t)
		})
	}
}

func TestManualManipulationService_DeleteManualManipulationRoutePostcodeRuleByQuery(t *testing.T) {
	mockDAO := &MockCDTManualManipulationRuleTabDAO{}
	mockLaneDAO := &MockLaneCdtManualManipulationRuleDAO{}

	service := &manualManipulationService{
		manualManipulationDao:     mockDAO,
		laneManualManipulationDao: mockLaneDAO,
	}

	ctx := utils.NewCommonCtx(context.Background())
	testRecordId := uint64(456)
	testBatchNum := 500
	expectedQuery := map[string]interface{}{"record_id": testRecordId}

	tests := []struct {
		name          string
		setupMock     func()
		expectedRows  int64
		expectedError bool
		description   string
	}{
		{
			name: "成功删除Postcode规则",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRoutePostcodeRuleByQuery", ctx, expectedQuery, testBatchNum).
					Return(int64(3), (*lcos_error.LCOSError)(nil)).Once()
			},
			expectedRows:  3,
			expectedError: false,
			description:   "成功删除Postcode规则时应该返回删除的行数",
		},
		{
			name: "删除操作失败",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRoutePostcodeRuleByQuery", ctx, expectedQuery, testBatchNum).
					Return(int64(0), lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "postcode delete failed")).Once()
			},
			expectedRows:  0,
			expectedError: true,
			description:   "删除失败时应该正确传递错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置mock
			tt.setupMock()

			// 执行测试
			rows, err := service.DeleteManualManipulationRoutePostcodeRuleByQuery(ctx, testRecordId, testBatchNum)

			// 验证结果
			if tt.expectedError {
				assert.NotNil(t, err, tt.description)
			} else {
				assert.Nil(t, err, tt.description)
				assert.Equal(t, tt.expectedRows, rows, "应该返回正确的删除行数")
			}

			// 验证mock调用
			mockDAO.AssertExpectations(t)
		})
	}
}

func TestManualManipulationService_DeleteManualManipulationRouteZipcodeRuleByQuery(t *testing.T) {
	mockDAO := &MockCDTManualManipulationRuleTabDAO{}
	mockLaneDAO := &MockLaneCdtManualManipulationRuleDAO{}

	service := &manualManipulationService{
		manualManipulationDao:     mockDAO,
		laneManualManipulationDao: mockLaneDAO,
	}

	ctx := utils.NewCommonCtx(context.Background())
	testRecordId := uint64(789)
	testBatchNum := 200
	expectedQuery := map[string]interface{}{"record_id": testRecordId}

	tests := []struct {
		name          string
		setupMock     func()
		expectedRows  int64
		expectedError bool
		description   string
	}{
		{
			name: "成功删除Zipcode规则",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRouteZipcodeRuleByQuery", ctx, expectedQuery, testBatchNum).
					Return(int64(7), (*lcos_error.LCOSError)(nil)).Once()
			},
			expectedRows:  7,
			expectedError: false,
			description:   "成功删除Zipcode规则时应该返回删除的行数",
		},
		{
			name: "无记录需要删除",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRouteZipcodeRuleByQuery", ctx, expectedQuery, testBatchNum).
					Return(int64(0), (*lcos_error.LCOSError)(nil)).Once()
			},
			expectedRows:  0,
			expectedError: false,
			description:   "无记录删除时应该返回0",
		},
		{
			name: "删除操作异常",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRouteZipcodeRuleByQuery", ctx, expectedQuery, testBatchNum).
					Return(int64(0), lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "zipcode delete error")).Once()
			},
			expectedRows:  0,
			expectedError: true,
			description:   "删除异常时应该正确传递错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置mock
			tt.setupMock()

			// 执行测试
			rows, err := service.DeleteManualManipulationRouteZipcodeRuleByQuery(ctx, testRecordId, testBatchNum)

			// 验证结果
			if tt.expectedError {
				assert.NotNil(t, err, tt.description)
			} else {
				assert.Nil(t, err, tt.description)
				assert.Equal(t, tt.expectedRows, rows, "应该返回正确的删除行数")
			}

			// 验证mock调用
			mockDAO.AssertExpectations(t)
		})
	}
}

func TestManualManipulationService_DeleteManualManipulationRecordTabByID(t *testing.T) {
	mockDAO := &MockCDTManualManipulationRuleTabDAO{}
	mockLaneDAO := &MockLaneCdtManualManipulationRuleDAO{}

	service := &manualManipulationService{
		manualManipulationDao:     mockDAO,
		laneManualManipulationDao: mockLaneDAO,
	}

	ctx := utils.NewCommonCtx(context.Background())
	testRecordId := uint64(999)

	tests := []struct {
		name          string
		setupMock     func()
		expectedError bool
		description   string
	}{
		{
			name: "成功删除记录",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRecordTabByID", ctx, testRecordId).
					Return((*lcos_error.LCOSError)(nil)).Once()
			},
			expectedError: false,
			description:   "成功删除记录时应该返回nil",
		},
		{
			name: "删除记录失败",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRecordTabByID", ctx, testRecordId).
					Return(lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "record delete failed")).Once()
			},
			expectedError: true,
			description:   "删除记录失败时应该返回错误",
		},
		{
			name: "记录不存在",
			setupMock: func() {
				mockDAO.On("DeleteManualManipulationRecordTabByID", ctx, testRecordId).
					Return(lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "record not found")).Once()
			},
			expectedError: true,
			description:   "记录不存在时应该返回相应错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置mock
			tt.setupMock()

			// 执行测试
			err := service.DeleteManualManipulationRecordTabByID(ctx, testRecordId)

			// 验证结果
			if tt.expectedError {
				assert.NotNil(t, err, tt.description)
			} else {
				assert.Nil(t, err, tt.description)
			}

			// 验证mock调用
			mockDAO.AssertExpectations(t)
		})
	}
}

// 测试参数传递的正确性
func TestParameterPassing(t *testing.T) {
	mockDAO := &MockCDTManualManipulationRuleTabDAO{}
	mockLaneDAO := &MockLaneCdtManualManipulationRuleDAO{}

	service := &manualManipulationService{
		manualManipulationDao:     mockDAO,
		laneManualManipulationDao: mockLaneDAO,
	}

	ctx := utils.NewCommonCtx(context.Background())

	t.Run("验证Delete方法的参数传递", func(t *testing.T) {
		recordId := uint64(12345)
		batchNum := 500

		// 验证DeleteManualManipulationRouteLocationRuleByQuery的参数传递
		expectedQuery := map[string]interface{}{"record_id": recordId}
		mockDAO.On("DeleteManualManipulationRouteLocationRuleByQuery", ctx, expectedQuery, batchNum).
			Return(int64(1), (*lcos_error.LCOSError)(nil)).Once()

		_, _ = service.DeleteManualManipulationRouteLocationRuleByQuery(ctx, recordId, batchNum)

		// 验证调用参数
		mockDAO.AssertCalled(t, "DeleteManualManipulationRouteLocationRuleByQuery", ctx, expectedQuery, batchNum)
		mockDAO.AssertExpectations(t)
	})
}
