package edd_delay_queue

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/delay_queue"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/redislibv2"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	jsoniter "github.com/json-iterator/go"
	"hash/crc32"
	"strconv"
	"strings"
	"time"
)

const (
	queueNamePrefix = "edd_waybill_delay_queue"
)

type EddWaybillDelayQueue interface {
	GetQueueNameByBucket(region string, bucket uint32) string
	GetQueueNameBySlsTN(ctx context.Context, region string, slsTN string) string
	PushEddWaybill(ctx utils.LCOSContext, region string, waybill *EddWaybill) *lcos_error.LCOSError
	RemoveEddWaybill(ctx utils.LCOSContext, region string, waybill *EddWaybill) *lcos_error.LCOSError
	RemoveEddWaybillByQueueName(ctx utils.LCOSContext, queueName string, waybill *EddWaybill) *lcos_error.LCOSError
	PollEddWaybill(ctx utils.LCOSContext, queueName string) (*EddWaybill, *lcos_error.LCOSError)
	PopEddWayBill(ctx utils.LCOSContext, queueName string) (*EddWaybill, *lcos_error.LCOSError)
	ListAllEddWaybill(ctx utils.LCOSContext, region string) (map[string][]*EddWaybill, *lcos_error.LCOSError)
	ListEddWayBillByQueue(ctx utils.LCOSContext, queueName string) ([]*EddWaybill, *lcos_error.LCOSError)
	GetCapacityByQueue(ctx utils.LCOSContext, queueName string) (int64, *lcos_error.LCOSError)

	LockBucket(ctx utils.LCOSContext, region string, bucket uint32, operator string) *lcos_error.LCOSError
	UnlockBucket(ctx utils.LCOSContext, region string, bucket uint32, operator string) *lcos_error.LCOSError
}

var _ EddWaybillDelayQueue = (*eddWaybillDelayQueue)(nil)

func NewEddWaybillDelayQueue() *eddWaybillDelayQueue {
	return &eddWaybillDelayQueue{
		delayQueue: delay_queue.NewZSetQueue(redislibv2.GetEddRedisClient()),
	}
}

type eddWaybillDelayQueue struct {
	delayQueue delay_queue.ZSetQueue
}

func (e *eddWaybillDelayQueue) GetQueueNameByBucket(region string, bucket uint32) string {
	region = strings.ToUpper(region)
	return utils.GenKey(":", region, queueNamePrefix, strconv.FormatUint(uint64(bucket), 10))
}

func (e *eddWaybillDelayQueue) GetQueueNameBySlsTN(ctx context.Context, region, slsTN string) string {
	bucket := crc32.ChecksumIEEE([]byte(slsTN)) % config.GetEddDelayQueueWriteBucketSize(ctx)
	return e.GetQueueNameByBucket(region, bucket)
}

func (e *eddWaybillDelayQueue) PushEddWaybill(ctx utils.LCOSContext, region string, waybill *EddWaybill) *lcos_error.LCOSError {
	if waybill == nil {
		return nil
	}

	queueName := e.GetQueueNameBySlsTN(ctx, region, waybill.SlsTN)
	value, _ := jsoniter.MarshalToString(waybill)
	return e.delayQueue.Push(ctx, queueName, value, float64(waybill.DDL))
}

func (e *eddWaybillDelayQueue) RemoveEddWaybill(ctx utils.LCOSContext, region string, waybill *EddWaybill) *lcos_error.LCOSError {
	if waybill == nil {
		return nil
	}

	queueName := e.GetQueueNameBySlsTN(ctx, region, waybill.SlsTN)
	value, _ := jsoniter.MarshalToString(waybill)
	return e.delayQueue.Remove(ctx, queueName, value)
}

func (e *eddWaybillDelayQueue) RemoveEddWaybillByQueueName(ctx utils.LCOSContext, queueName string, waybill *EddWaybill) *lcos_error.LCOSError {
	if waybill == nil {
		return nil
	}
	value, _ := jsoniter.MarshalToString(waybill)
	return e.delayQueue.Remove(ctx, queueName, value)
}

func (e *eddWaybillDelayQueue) PollEddWaybill(ctx utils.LCOSContext, queueName string) (*EddWaybill, *lcos_error.LCOSError) {
	value, _, err := e.delayQueue.Poll(ctx, queueName)
	if err != nil {
		return nil, err
	}
	if len(value) == 0 {
		return nil, nil
	}

	var waybill *EddWaybill
	if err := jsoniter.UnmarshalFromString(value, &waybill); err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "unmarshal edd waybill error: %s", err.Error())
	}
	return waybill, nil
}

func (e *eddWaybillDelayQueue) PopEddWayBill(ctx utils.LCOSContext, queueName string) (*EddWaybill, *lcos_error.LCOSError) {
	value, _, err := e.delayQueue.Pop(ctx, queueName)
	if err != nil {
		return nil, err
	}
	if len(value) == 0 {
		return nil, nil
	}

	var waybill *EddWaybill
	if err := jsoniter.UnmarshalFromString(value, &waybill); err != nil {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "unmarshal edd waybill error: %s", err.Error())
	}
	return waybill, nil
}

func (e *eddWaybillDelayQueue) ListAllEddWaybill(ctx utils.LCOSContext, region string) (map[string][]*EddWaybill, *lcos_error.LCOSError) {
	ret := make(map[string][]*EddWaybill)

	var bucket uint32
	for ; bucket < config.GetEddDelayQueueReadBucketSize(ctx); bucket++ {
		queueName := e.GetQueueNameByBucket(region, bucket)
		list, err := e.ListEddWayBillByQueue(ctx, queueName)
		if err != nil {
			return nil, err
		}
		if len(list) == 0 {
			continue
		}
		ret[queueName] = list
	}

	return ret, nil
}

func (e *eddWaybillDelayQueue) ListEddWayBillByQueue(ctx utils.LCOSContext, queueName string) ([]*EddWaybill, *lcos_error.LCOSError) {
	jsonList, err := e.delayQueue.List(ctx, queueName)
	if err != nil {
		return nil, err
	}
	ret := make([]*EddWaybill, 0, len(jsonList))
	for _, value := range jsonList {
		var waybill *EddWaybill
		if err := jsoniter.UnmarshalFromString(value, &waybill); err != nil {
			continue
		}
		ret = append(ret, waybill)
	}
	return ret, nil
}

func (e *eddWaybillDelayQueue) GetCapacityByQueue(ctx utils.LCOSContext, queueName string) (int64, *lcos_error.LCOSError) {
	return e.delayQueue.Len(ctx, queueName)
}

func (e *eddWaybillDelayQueue) LockBucket(ctx utils.LCOSContext, region string, bucket uint32, operator string) *lcos_error.LCOSError {
	bucketKey := utils.GenKey(":", "bucket", region, strconv.FormatUint(uint64(bucket), 10))

	if err := redislibv2.GetEddRedisClient().SetNX(ctx, bucketKey, operator, time.Hour).Err(); err != nil {
		return lcos_error.NewLCOSError(lcos_error.RedisReadWriteErrorCode, err.Error())
	}
	return nil
}

func (e *eddWaybillDelayQueue) UnlockBucket(ctx utils.LCOSContext, region string, bucket uint32, operator string) *lcos_error.LCOSError {
	bucketKey := utils.GenKey(":", "bucket", region, strconv.FormatUint(uint64(bucket), 10))

	client := redislibv2.GetEddRedisClient()
	holder, err := client.Get(ctx, bucketKey).Result()
	if err != nil {
		return lcos_error.NewLCOSError(lcos_error.RedisReadWriteErrorCode, err.Error())
	}
	if operator != holder {
		// 操作者不是锁的持有者，那么释放锁失败
		return lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "cannot unlock bucket, bucket holder is %s", holder)
	}

	if err := client.Del(ctx, bucketKey).Err(); err != nil {
		return lcos_error.NewLCOSError(lcos_error.RedisReadWriteErrorCode, err.Error())
	}
	return nil
}
