package edd_delay_queue

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"testing"
)

func Test_eddWaybillDelayQueue_GetQueueNameBySlsTN(t *testing.T) {

	config.MutableConf = &config.MutableConfig{
		EDDPreemptiveConfig: config.EDDPreemptiveConfig{
			DelayQueue: config.EDDDelayQueueConfig{
				WriteBucketSize: 10,
			},
		},
	}

	e := &eddWaybillDelayQueue{}
	t.Log(e.GetQueueNameBySlsTN("TH", "TH238908706225ST"))
}
