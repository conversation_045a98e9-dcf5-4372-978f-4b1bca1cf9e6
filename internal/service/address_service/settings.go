package address_service

// 通过location获取region
var LOCATION_ID_RANGE = map[string][]int{
	"ID": {0, 999999},
	"MY": {1000000, 1999999},
	"TH": {2000000, 2999999},
	"TW": {3000000, 3999999},
	"SG": {4000000, 4999999},
	"PH": {5000000, 5999999},
	"VN": {6000000, 6999999},
	"IR": {7000000, 7999999},
	"MM": {8000000, 8999999},
	"CN": {9000000, 9999999},
	"KR": {10000000, 10999999},
	"BR": {11000000, 11999999},
	"JP": {14000001, 14999999},
	"MX": {12000000, 12999999},
	"CO": {15000000, 15999999},
	"CL": {16000000, 16999999},
	"AR": {17000000, 17999999},
	"PL": {18000000, 18999999},
	"ES": {19000000, 19999999},
	"FR": {20000000, 20999999},
	"IN": {21000000, 21999999},
}

func GetRegionByLocationID(locationID int) string {
	for region, value := range LOCATION_ID_RANGE {
		if value[0] < locationID && locationID < value[1] {
			return region
		}
	}
	return ""
}
