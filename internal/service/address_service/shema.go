package address_service

type LocationInfo struct {
	ParentId           int    `json:"parent_id"`
	StateLocationId    int    `json:"state_location_id"`
	CityLocationId     int    `json:"city_location_id"`
	DistrictLocationId int    `json:"district_location_id,omitempty"`
	StreetLocationId   int    `json:"street_location_id,omitempty"`
	State              string `json:"state"`
	City               string `json:"city"`
	District           string `json:"district,omitempty"`
	Street             string `json:"street,omitempty"`
	Country            string `json:"country"`
}

func (l *LocationInfo) GetLocationId() int {
	if l.StreetLocationId != 0 {
		return l.StreetLocationId
	}
	if l.DistrictLocationId != 0 {
		return l.DistrictLocationId
	}
	if l.CityLocationId != 0 {
		return l.CityLocationId
	}
	if l.StateLocationId != 0 {
		return l.StateLocationId
	}
	return 0
}
