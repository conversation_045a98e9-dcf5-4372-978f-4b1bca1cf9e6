package address_service

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"
)

func TestGetLocationInfoByName(t *testing.T) {
	os.Setenv("ENV", "TEST")
	l := NewLocationCache()
	current := recorder.Now(ctx).UnixNano()
	results, lcosErr := l.GetLocationInfoByName(context.Background(), "MX", "Veracruz", "", "", "")
	if lcosErr != nil {
		t.<PERSON>("%v", lcosErr.Msg)
	}
	fmt.Printf("result:[%v], time takes:[%d] millo seconds\n", results, (recorder.Now(ctx).UnixNano()-current)/10e6)

	time.Sleep(10 * time.Second)
	t.Log("==================================================")
	results, lcosErr = l.GetLocationInfoByName(context.Background(), "VN", "Thanh Hóa", "", "", "")
	if lcosErr != nil {
		t.<PERSON>("%v", lcosErr.Msg)
	}
	fmt.Printf("result:[%v], time takes:[%d] millo seconds\n", results, (recorder.Now(ctx).UnixNano()-current)/10e6)
}
