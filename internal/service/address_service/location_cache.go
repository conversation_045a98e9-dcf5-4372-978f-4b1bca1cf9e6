package address_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	"strings"
	"sync"
)

const ExpireTime = 24 * 3600
const minDuration = 60 * 10 // 离上一次更新时间小于十分钟时，则无需再次更新

// 缓存从地址服务获取的location信息，用于解析文件或者导出文件
type locationCache struct {
	rw                sync.RWMutex
	lastUpdateTimeMap map[string]uint32                                //  region->updateTime 记录上一次更新的时间
	locationIdMap     map[string]map[int]*ops_service.LocationInfoItem //存储地址id信息，格式为region->location_id->*LocationInfoItem的格式
	locationNameIDMap map[string]map[string]int                        // 存储地址id和地址名称的映射，格式为region->street|district|city|state -> location id
}

func NewLocationCache() *locationCache {
	return &locationCache{
		lastUpdateTimeMap: map[string]uint32{},
		locationIdMap:     map[string]map[int]*ops_service.LocationInfoItem{},
		locationNameIDMap: map[string]map[string]int{},
	}
}

var LocationServer = NewLocationCache()

type locationInterface interface {
	GetLocationInfoByName(ctx context.Context, country, state, city, district, street string) (*LocationInfo, *lcos_error.LCOSError)
	GetLocationInfoById(ctx context.Context, locationId int) (*LocationInfo, *lcos_error.LCOSError)
}

var _ locationInterface = (*locationCache)(nil)

// loadLocationDataByRegion 向缓存中加入某个国家的location信息
func (l *locationCache) loadLocationDataByRegion(ctx context.Context, country string) *lcos_error.LCOSError {
	country = strings.ToUpper(country)
	locationIdMap := map[int]*ops_service.LocationInfoItem{}
	locationNameIDMap := map[string]int{}

	// 请求location地址服务获取信息
	requestParams := &ops_service.GetAllLocationDataRequest{
		Country: strings.ToUpper(country),
	}
	results, lcosErr := ops_service.GetAllLocations(ctx, requestParams)
	if lcosErr != nil {
		return lcosErr
	}

	// 循环results结果，将其保存为 location_id->*LocationInfoItem的格式
	for _, item := range results {
		locationIdMap[item.LocationId] = item
	}

	// 循环results结果，将其保存为 street|district|city|state -> location id的形式，方便未来通过名称查询
	for _, item := range results {
		// 循环只到parent id为0
		tmpItem := item
		parentID := tmpItem.ParentId
		nameKey := item.Name

		isBreak := false
		maxLoopSize := config.GetMaxLoopSizeForLocationLevel(ctx)
		var loopIndex int
		for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
			if parentID == 0 {
				break
			}
			// 找不到parent id对应的location信息，则跳过
			if resultItem, ok := locationIdMap[parentID]; !ok {
				// 此时的location id是被打断的，所以需要设置flag，以跳过将其放在location name map中的动作
				isBreak = true
				break
			} else {
				tmpItem = resultItem
				nameKey += "|" + resultItem.Name
				parentID = tmpItem.ParentId
			}
		}
		if isBreak || loopIndex >= maxLoopSize {
			if loopIndex >= maxLoopSize {
				logger.CtxLogErrorf(ctx, "loop over max size, max_size=%d", maxLoopSize)
			}
			continue
		}
		// 将名称key加上国家组成最终的key
		nameKey += "|" + country
		locationNameIDMap[nameKey] = item.LocationId
	}
	l.rw.Lock()
	defer l.rw.Unlock()
	l.lastUpdateTimeMap[country] = uint32(recorder.Now(ctx).Unix())
	l.locationNameIDMap[country] = locationNameIDMap
	l.locationIdMap[country] = locationIdMap
	return nil
}

func getLocationIDByKeyAndRegion(locationNameIdMap map[string]map[string]int, country string, key string) (int, bool) {
	locationNameMap, ok1 := locationNameIdMap[country]
	if !ok1 {
		return 0, false
	}
	locationID, ok2 := locationNameMap[key]
	return locationID, ok2
}

func getLocationInfoByKeyAndRegion(locationIdMap map[string]map[int]*ops_service.LocationInfoItem, country string, locationID int) (*ops_service.LocationInfoItem, bool) {
	locationNameMap, ok1 := locationIdMap[country]
	if !ok1 {
		return nil, false
	}
	locationInfo, ok2 := locationNameMap[locationID]
	return locationInfo, ok2
}

func (l *locationCache) GetLocationInfoByName(ctx context.Context, country, state, city, district, street string) (*LocationInfo, *lcos_error.LCOSError) {
	if country == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "country is required")
	}
	country = strings.ToUpper(country)

	// sg的特殊逻辑
	if strings.ToUpper(country) == "SG" && state == "" &&
		city == "" && district == "" && street == "" {
		return &LocationInfo{
			Country:            country,
			StateLocationId:    constant.SGCommonStateId,
			CityLocationId:     constant.SGCommonCityId,
			DistrictLocationId: constant.SGCommonDistrictId,
			StreetLocationId:   constant.SGCommonStreetId,
		}, nil
	}

	// 组成name key查询，
	queryNameKey := ""
	if street != "" {
		queryNameKey += "|" + street
	}
	if district != "" {
		queryNameKey += "|" + district
	}
	if city != "" {
		queryNameKey += "|" + city
	}
	if state != "" {
		queryNameKey += "|" + state
	}
	queryNameKey += "|" + country

	// 将左右两边的|删除
	queryNameKey = strings.Trim(queryNameKey, "|")

	var locationID int
	var ok bool
	var locationItem *ops_service.LocationInfoItem
	currentTime := utils.GetTimestamp(ctx)

	// 如果location数据已经过期一天，则先刷新缓存
	if lastUpdateTime, ok := l.lastUpdateTimeMap[country]; !ok || lastUpdateTime == 0 || currentTime-lastUpdateTime > ExpireTime {
		l.loadLocationDataByRegion(ctx, country)
	}

	// 第一次查询，如果查询不到，则先尝试更新缓存
	l.rw.RLock()
	locationID, ok = getLocationIDByKeyAndRegion(l.locationNameIDMap, country, queryNameKey)
	l.rw.RUnlock()
	if !ok {
		// 如果十分钟前加载过数据，则直接报错
		if lastUpdateTime, ok := l.lastUpdateTimeMap[country]; ok && lastUpdateTime != 0 && currentTime-lastUpdateTime <= minDuration {
			return nil, lcos_error.NewLCOSError(lcos_error.CdtAddressNotFoundErrorCode, fmt.Sprintf("cannot find address:[location_name:%v]", queryNameKey))
		}
		l.loadLocationDataByRegion(ctx, country)
		// 第二次查询，如果查询不到，则报错
		l.rw.RLock()
		locationID, ok = getLocationIDByKeyAndRegion(l.locationNameIDMap, country, queryNameKey)
		l.rw.RUnlock()
		if !ok {
			return nil, lcos_error.NewLCOSError(lcos_error.CdtAddressNotFoundErrorCode, fmt.Sprintf("cannot find address:[country:%v, state:%v, city:%v, district:%v, stree:%v]", country, state, city, district, street))
		}
	}

	l.rw.RLock()
	defer l.rw.RUnlock()
	locationItem, ok = getLocationInfoByKeyAndRegion(l.locationIdMap, country, locationID)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.CdtAddressNotFoundErrorCode, fmt.Sprintf("cannot find address:[locaion_id:%v]", locationID))
	} else {
		returnLocation := &LocationInfo{
			ParentId:           locationItem.ParentId,
			StateLocationId:    0,
			CityLocationId:     0,
			DistrictLocationId: 0,
			StreetLocationId:   0,
			State:              state,
			City:               city,
			District:           district,
			Street:             street,
			Country:            country,
		}
		tmpLocationItem := locationItem
		tmpParentID := tmpLocationItem.ParentId
		maxLoopSize := config.GetMaxLoopSizeForLocationLevel(ctx)
		var loopIndex int
		for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
			if tmpParentID == 0 {
				returnLocation.StateLocationId = tmpLocationItem.LocationId
				break
			}
			switch tmpLocationItem.Level {
			case 0:
				returnLocation.StateLocationId = tmpLocationItem.LocationId
			case 1:
				returnLocation.CityLocationId = tmpLocationItem.LocationId
			case 2:
				returnLocation.DistrictLocationId = tmpLocationItem.LocationId
			case 3:
				returnLocation.StreetLocationId = tmpLocationItem.LocationId
			}
			tmpLocationItem = l.locationIdMap[country][tmpParentID]
			tmpParentID = tmpLocationItem.ParentId
		}
		if loopIndex >= maxLoopSize {
			return nil, lcos_error.NewLCOSError(lcos_error.LoopOverMaxSize, fmt.Sprintf("loop over max size, max_size=%d", maxLoopSize))
		}
		return returnLocation, nil
	}
}

func (l *locationCache) GetLocationInfoById(ctx context.Context, locationId int) (*LocationInfo, *lcos_error.LCOSError) {
	// 通过location id获取region信息
	region := GetRegionByLocationID(locationId)
	if region == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("cannot find region for location_id:[%v]", locationId))
	}
	region = strings.ToUpper(region)

	var locationInfo *ops_service.LocationInfoItem

	currentTime := utils.GetTimestamp(ctx)
	// 如果location数据已经过期一天，则先刷新缓存
	if lastUpdateTime, ok := l.lastUpdateTimeMap[region]; !ok || lastUpdateTime == 0 || currentTime-lastUpdateTime > ExpireTime {
		l.loadLocationDataByRegion(ctx, region)
	}

	// 第一次查询，如果查询不到，则先尝试更新缓存
	l.rw.RLock()
	locationInfo, ok := getLocationInfoByKeyAndRegion(l.locationIdMap, region, locationId)
	l.rw.RUnlock()
	if !ok {
		// 如果十分钟前加载过数据，则直接报错
		currentTime := utils.GetTimestamp(ctx)
		if lastUpdateTime, ok := l.lastUpdateTimeMap[region]; ok || currentTime-lastUpdateTime <= minDuration {
			return nil, lcos_error.NewLCOSError(lcos_error.CdtAddressNotFoundErrorCode, fmt.Sprintf("cannot find address:[location_id:%v]", locationId))
		}
		l.loadLocationDataByRegion(ctx, region)
		// 第二次查询，如果查询不到，则报错
		l.rw.RLock()
		locationInfo, ok = getLocationInfoByKeyAndRegion(l.locationIdMap, region, locationId)
		l.rw.RUnlock()
		if !ok {
			return nil, lcos_error.NewLCOSError(lcos_error.CdtAddressNotFoundErrorCode, fmt.Sprintf("cannot find address:[location_id:%v]", locationId))
		}
	}

	// 按照层级数向上遍历，获取名称信息
	returnLocation := &LocationInfo{ParentId: locationInfo.ParentId}
	returnLocation.Country = region
	tmpLocationItem := locationInfo
	tmpParentID := tmpLocationItem.ParentId
	maxLoopSize := config.GetMaxLoopSizeForLocationLevel(ctx)
	var loopIndex int
	for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
		if tmpParentID == 0 {
			returnLocation.StateLocationId = tmpLocationItem.LocationId
			returnLocation.State = tmpLocationItem.Name
			break
		}
		switch tmpLocationItem.Level {
		case 0:
			returnLocation.StateLocationId = tmpLocationItem.LocationId
			returnLocation.State = tmpLocationItem.Name
		case 1:
			returnLocation.CityLocationId = tmpLocationItem.LocationId
			returnLocation.City = tmpLocationItem.Name
		case 2:
			returnLocation.DistrictLocationId = tmpLocationItem.LocationId
			returnLocation.District = tmpLocationItem.Name
		case 3:
			returnLocation.StreetLocationId = tmpLocationItem.LocationId
			returnLocation.Street = tmpLocationItem.Name
		}
		tmpLocationItem = l.locationIdMap[region][tmpParentID]
		tmpParentID = tmpLocationItem.ParentId
	}
	if loopIndex >= maxLoopSize {
		return nil, lcos_error.NewLCOSError(lcos_error.LoopOverMaxSize, fmt.Sprintf("loop over max size, max_size=%d", maxLoopSize))
	}
	return returnLocation, nil

}
