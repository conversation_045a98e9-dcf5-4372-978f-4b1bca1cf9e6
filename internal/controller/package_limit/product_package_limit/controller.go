package product_package_limit

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/package_limit_util"
	common_protocol2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/product_package_limit"
)

type AdminProductPackageLimitController interface {
	BatchCreatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchUpdatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CreateOrUpdatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetPackageLimitByProductId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminProductPackageLimitController struct {
	packageLimitService service.ProductPackageLimitServiceInterface
}

func NewAdminProductPackageLimitController(m service.ProductPackageLimitServiceInterface) *adminProductPackageLimitController {
	return &adminProductPackageLimitController{packageLimitService: m}
}

func (api *adminProductPackageLimitController) BatchCreatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	originRequest, _ := rawRequest.(*product_package_limit.CreateOrUpdateProductPackageLimitRequest)
	newRequest := api.genAllPackageLimitDataRequest(originRequest)

	if err := api.checkAndFillBatchCreateOrUpdateParam(newRequest); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	s, err := api.packageLimitService.BatchCreatePackageLimitModel(c, newRequest)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (api *adminProductPackageLimitController) BatchUpdatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	originRequest, _ := rawRequest.(*product_package_limit.CreateOrUpdateProductPackageLimitRequest)
	newRequest := api.genAllPackageLimitDataRequest(originRequest)

	if err := api.checkAndFillBatchCreateOrUpdateParam(newRequest); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	s, err := api.packageLimitService.BatchUpdatePackageLimitModel(c, newRequest)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (api *adminProductPackageLimitController) CreateOrUpdatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	originRequest, _ := rawRequest.(*product_package_limit.CreateOrUpdateProductPackageLimitRequest)
	newRequest := api.genAllPackageLimitDataRequest(originRequest)

	if err := api.checkAndFillBatchCreateOrUpdateParam(newRequest); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	s, err := api.packageLimitService.CreateOrUpdatePackageLimitModel(c, newRequest)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (api *adminProductPackageLimitController) GetPackageLimitByProductId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*common_protocol.ProductIdRequest)

	s, err := api.packageLimitService.GetPackageLimitByProductId(c, request.ProductId)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (api *adminProductPackageLimitController) checkAndFillBatchCreateOrUpdateParam(param *product_package_limit.CreateOrUpdateProductPackageLimitRequest) error {

	// 校验有效的产品重量限制rule_type不能超过一条
	if err := package_limit_util.CheckProductWeightLimit(param); err != nil {
		return err
	}
	for i := 0; i < len(param.PackageLimits); i++ {
		limitFlag := *param.PackageLimits[i].LimitFlag
		ruleType := param.PackageLimits[i].RuleType
		if limitFlag == constant.ENABLED {
			if err := package_limit_util.CheckPackageLimitParam(ruleType, param.PackageLimits[i]); err != nil {
				return err
			}
			package_limit_util.FillPackageLimitParam(ruleType, param.PackageLimits[i])
		}
	}
	return nil
}

// 前端只传需要限制的数据，不需要限制的后端需要自动补全数据，向后端存储不限制
func (api *adminProductPackageLimitController) genAllPackageLimitDataRequest(param *product_package_limit.CreateOrUpdateProductPackageLimitRequest) *product_package_limit.CreateOrUpdateProductPackageLimitRequest {
	var packageLimitList []*common_protocol2.SingleLimitData

	packageLimitMap := make(map[uint8]*common_protocol2.SingleLimitData)

	for i := 0; i < len(param.PackageLimits); i++ {
		ruleType := param.PackageLimits[i].RuleType
		packageLimitMap[ruleType] = param.PackageLimits[i]
	}

	defaultFlag := constant.DISABLED
	for ruleType := range constant.LinePackageLimitRuleType {
		if packageLimitMap[ruleType] != nil {
			packageLimitList = append(packageLimitList, packageLimitMap[ruleType])
		} else {
			packageLimitList = append(packageLimitList, &common_protocol2.SingleLimitData{
				RuleType:  ruleType,
				LimitFlag: &defaultFlag,
			})
		}
	}

	return &product_package_limit.CreateOrUpdateProductPackageLimitRequest{
		ProductId:          param.ProductId,
		Region:             param.Region,
		PackageLimits:      packageLimitList,
		UseParcelLibrary:   param.UseParcelLibrary,
		ByPass:             param.ByPass,
		ByPassPurchaseTime: param.ByPassPurchaseTime,
	}
}
