package parcel_library

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/parcel_library_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/gogo/protobuf/proto"
)

type LogisticParcelLibraryController interface {
	NotifyLogisticParcelLibraryVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	// debug接口
	GetSkusCombinationId(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchCreateOrUpdateParcelLibraryData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchGetParcelLibraryData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewLogisticParcelLibraryController(svc parcel_library.LogisticParcelLibraryService) *logisticParcelLibraryController {
	return &logisticParcelLibraryController{
		svc: svc,
	}
}

type logisticParcelLibraryController struct {
	svc parcel_library.LogisticParcelLibraryService
}

func (l *logisticParcelLibraryController) NotifyLogisticParcelLibraryVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*parcel_library_protocol.NotifyParcelLibraryVersionRequest)

	if err := l.svc.NotifyLogisticParcelLibraryVersion(ctx, req.Region, req.FileUrlList); err != nil {
		return nil, err
	}
	return nil, nil
}

func (l *logisticParcelLibraryController) GetSkusCombinationId(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*parcel_library_protocol.GetSkusCombinationIdRequest)

	skuInfos := make([]*pb.SkuInfo, 0, len(req.SkuInfos))
	for _, skuInfo := range req.SkuInfos {
		skuInfos = append(skuInfos, &pb.SkuInfo{
			ItemId:   proto.Uint64(skuInfo.ItemId),
			ModelId:  proto.Uint64(skuInfo.ModelId),
			Quantity: proto.Uint32(skuInfo.Quantity),
		})
	}
	combinationId, err := l.svc.GetCombinationIdFromSkuList(ctx, skuInfos)
	if err != nil {
		return 0, err
	}
	return combinationId, nil
}

func (l *logisticParcelLibraryController) BatchCreateOrUpdateParcelLibraryData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*parcel_library_protocol.BatchCreateOrUpdateParcelLibraryDataRequest)

	for _, data := range req.DataList {
		data.Region = req.Region
	}
	updated, err := l.svc.BatchCreateOrUpdateParcelLibraryData(ctx, req.Region, req.DataList)
	if err != nil {
		return nil, err
	}
	return updated, nil
}

func (l *logisticParcelLibraryController) BatchGetParcelLibraryData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*parcel_library_protocol.BatchGetParcelLibraryDataRequest)

	retMap, err := l.svc.BatchGetParcelLibraryData(ctx, req.Region, req.SkusCombinationIdList)
	if err != nil {
		return nil, err
	}
	return retMap, nil
}
