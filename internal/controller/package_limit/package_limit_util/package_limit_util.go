package package_limit_util

import (
	"errors"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	common_protocol2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/product_package_limit"
	"math"
)

func CheckPackageLimitParam(ruleType uint8, param *common_protocol2.SingleLimitData) error {
	if param.Formula == 0 {
		return errors.New("formula is null")
	}
	// TODO: 整理每个rule可以使用的公式
	// TODO: check公式是否在rule可以使用的公式list中
	return nil
}

func FillPackageLimitParam(ruleType uint8, param *common_protocol2.SingleLimitData) {
	if ruleType != constant.ActualWeightAndVolumetricWeightRatioRule {
		if param.MaxSymbol == 0 {
			param.MaxSymbol = constant.LessThanOrEqual
		}
		if param.MinSymbol == 0 {
			param.MinSymbol = constant.GreatThanOrEqual
		}
	}
	if (ruleType == constant.SizeSumExtremumRule || ruleType == constant.MaxSizeExtremumRule) && param.MaxSize == 0 {
		param.MaxSize = float64(math.MaxUint32)
	}
	if (ruleType == constant.ActualWeightAndVolumetricWeightExtremumRule || ruleType == constant.ActualWeightExtremumRule ||
		ruleType == constant.VolumetricWeightExtremumRule) && param.MaxWeight == 0 {
		param.MaxWeight = float64(math.MaxUint32)
	}
	if ruleType == constant.MaxLengthExtremumRule && param.MaxLength == 0 {
		param.MaxLength = float64(math.MaxUint32)
	}
	if ruleType == constant.MaxWidthExtremumRule && param.MaxWidth == 0 {
		param.MaxWidth = float64(math.MaxUint32)
	}
	if ruleType == constant.HeightSumExtremumRule && param.MaxHeight == 0 {
		param.MaxHeight = float64(math.MaxUint32)
	}
}

func CheckProductWeightLimit(param *product_package_limit.CreateOrUpdateProductPackageLimitRequest) error {
	var counts = 0
	for _, packageLimit := range param.PackageLimits {
		if *packageLimit.LimitFlag == constant.ENABLED &&
			(packageLimit.RuleType >= constant.ActualWeightExtremumRule && packageLimit.RuleType <= constant.ActualWeightAndVolumetricWeightRatioRule) {
			counts++
		}
	}
	if counts > 1 {
		return errors.New("product weight limit more than one")
	}
	return nil
}
