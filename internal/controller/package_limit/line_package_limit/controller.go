package line_package_limit

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/controller/package_limit/package_limit_util"
	common_protocol2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/line_package_limit"
)

type AdminLinePackageLimitController interface {
	BatchCreatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchCreatePackageLimitsWithDraft(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchUpdatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CreateOrUpdatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetPackageLimitByLineId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeletePackageLimitByLineId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadPackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminLinePackageLimitController struct {
	packageLimitService service.LinePackageLimitServiceInterface
}

func NewAdminLinePackageLimitController(m service.LinePackageLimitServiceInterface) *adminLinePackageLimitController {
	return &adminLinePackageLimitController{packageLimitService: m}
}

func (api *adminLinePackageLimitController) BatchCreatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	originRequest, _ := rawRequest.(*line_package_limit.CreateOrUpdateLinePackageLimitRequest)
	newRequest := api.genAllPackageLimitDataRequest(originRequest)

	if err := api.checkAndFillBatchCreateOrUpdateParam(newRequest); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	s, err := api.packageLimitService.BatchCreatePackageLimitModel(c, newRequest)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (api *adminLinePackageLimitController) BatchCreatePackageLimitsWithDraft(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	originRequest, _ := rawRequest.(*line_package_limit.CreateOrUpdateLinePackageLimitRequest)
	newRequest := api.genAllPackageLimitDataRequest(originRequest)

	if err := api.checkAndFillBatchCreateOrUpdateParam(newRequest); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	s, err := api.packageLimitService.BatchCreatePackageLimitModelWithDraft(c, newRequest)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (api *adminLinePackageLimitController) BatchUpdatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	originRequest, _ := rawRequest.(*line_package_limit.CreateOrUpdateLinePackageLimitRequest)
	newRequest := api.genAllPackageLimitDataRequest(originRequest)
	if err := api.checkAndFillBatchCreateOrUpdateParam(newRequest); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	email := c.GetUserEmail()
	newRequest.Operator = email
	logger.CtxLogDebugf(c, "user email:%s", email)

	s, err := api.packageLimitService.BatchUpdatePackageLimitModel(c, newRequest)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (api *adminLinePackageLimitController) CreateOrUpdatePackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	originRequest, _ := rawRequest.(*line_package_limit.CreateOrUpdateLinePackageLimitRequest)
	newRequest := api.genAllPackageLimitDataRequest(originRequest)

	if err := api.checkAndFillBatchCreateOrUpdateParam(newRequest); err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	s, err := api.packageLimitService.CreateOrUpdatePackageLimitModel(c, newRequest)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (api *adminLinePackageLimitController) GetPackageLimitByLineId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*common_protocol.LineIdRequest)
	s, err := api.packageLimitService.GetPackageLimitByLineId(c, request.LineId)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (api *adminLinePackageLimitController) DeletePackageLimitByLineId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*common_protocol.LineIdRequest)

	err := api.packageLimitService.DeletePackageLimitByLineId(c, request.LineId)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

func (api *adminLinePackageLimitController) checkAndFillBatchCreateOrUpdateParam(param *line_package_limit.CreateOrUpdateLinePackageLimitRequest) error {
	for i := 0; i < len(param.PackageLimits); i++ {
		limitFlag := *param.PackageLimits[i].LimitFlag
		ruleType := param.PackageLimits[i].RuleType
		if limitFlag == constant.ENABLED {
			if err := package_limit_util.CheckPackageLimitParam(ruleType, param.PackageLimits[i]); err != nil {
				return err
			}
			package_limit_util.FillPackageLimitParam(ruleType, param.PackageLimits[i])
		}
	}
	return nil
}

func (api *adminLinePackageLimitController) UploadPackageLimits(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.LinePackageLimitUploadRequest)
	err := api.packageLimitService.UploadPackageLimitModel(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// 前端只传需要限制的数据，不需要限制的后端需要自动补全数据，向后端存储不限制
func (api *adminLinePackageLimitController) genAllPackageLimitDataRequest(param *line_package_limit.CreateOrUpdateLinePackageLimitRequest) *line_package_limit.CreateOrUpdateLinePackageLimitRequest {
	var packageLimitList []*common_protocol2.SingleLimitData

	packageLimitMap := make(map[uint8]*common_protocol2.SingleLimitData)

	for i := 0; i < len(param.PackageLimits); i++ {
		ruleType := param.PackageLimits[i].RuleType
		packageLimitMap[ruleType] = param.PackageLimits[i]
	}

	defaultFlag := constant.DISABLED
	for ruleType := range constant.LinePackageLimitRuleType {
		if packageLimitMap[ruleType] != nil {
			packageLimitList = append(packageLimitList, packageLimitMap[ruleType])
		} else {
			packageLimitList = append(packageLimitList, &common_protocol2.SingleLimitData{
				RuleType:  ruleType,
				LimitFlag: &defaultFlag,
			})
		}
	}

	return &line_package_limit.CreateOrUpdateLinePackageLimitRequest{
		LineId:             param.LineId,
		Region:             param.Region,
		PackageLimits:      packageLimitList,
		UseParcelLibrary:   param.UseParcelLibrary,
		ByPass:             param.ByPass,
		ByPassPurchaseTime: param.ByPassPurchaseTime,
	}
}
