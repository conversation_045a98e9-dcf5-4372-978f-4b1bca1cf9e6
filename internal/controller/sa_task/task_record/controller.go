package task_record

import (
	"fmt"
	config2 "git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/redislib"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	task_record2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/sa_task/task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	task_record3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sa_task/task_record"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/sa_task/task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"
	jsoniter "github.com/json-iterator/go"
	uuid "github.com/satori/go.uuid"
	"strings"
	"time"
)

type AdminSATaskRecordControllerInterface interface {
	ListSATaskRecord(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetAllTaskNames(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetAllTaskOperator(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	ConfirmUpdate(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminSATaskRecordController struct {
	saTaskRecordService task_record.SATaskRecordServiceInterface
	postcodeSAService   basic_postcode.LineBasicServiceablePostcodeServiceInterface
}

func (a *adminSATaskRecordController) uploadPostcodeSA(c *utils.HttpContext, fileUrl string, taskRecord *task_record3.LogisticSATaskRecordTab, lockKey, lockValue string) *lcos_error.LCOSError {

	// 函数结束后需要解锁
	defer func(ctx *utils.HttpContext, lockKey, lockValue string, taskConfID uint64) {
		// 检查是否为当前进程加的锁
		redisValue, err := redislib.Get(c, lockKey)
		if err == nil && string(redisValue) == lockValue {
			err := redislib.Delete(c, lockKey)
			if err != nil {
				// 需要将解锁失败加入监控
				_ = monitor.AwesomeReportEvent(c, constant.UnLockFailed, fmt.Sprintf("sa_task_conf_id:%d", taskConfID), constant.StatusError, "unlock failed")
			}
		}
	}(c, lockKey, lockValue, taskRecord.TaskConfID)

	// 上传postcode服务范围数据
	postcodeUploadRequest := &common_protocol.UploadFileRequest{
		FileUrl: fileUrl,
		Region:  taskRecord.Region,
	}
	allRows, _, lcosErr := a.postcodeSAService.ParseAndImportBasicPostcodeSA(c, postcodeUploadRequest, serviceable_util.LineBasicPostcodeHeader, false, 0)
	if lcosErr != nil {
		// 1. 生成错误文件
		errorFileUrl, errorFileName, errorFileSize, errorFilelcosErr := a.saTaskRecordService.GenerateErrorFile(c, lcosErr.Msg, taskRecord.AutoUpdateName, taskRecord.Region, allRows)
		if errorFilelcosErr != nil {
			return errorFilelcosErr
		}

		fileInfo := task_record2.FileInfo{
			URL:  errorFileUrl,
			Name: errorFileName,
			Size: errorFileSize,
		}
		fileInfoString, _ := jsoniter.MarshalToString(fileInfo)
		// 2. 更新任务状态
		updateTaskLcosErr := a.saTaskRecordService.UpdateSATaskRecord(c, map[string]interface{}{"task_status": constant.SaTaskResumeFailed, "error_file_info": fileInfoString, "error_msg": lcosErr.Msg, "last_operation_time": utils.GetTimestamp(c), "operator": c.GetUserEmail()}, map[string]interface{}{"id": taskRecord.ID})
		if updateTaskLcosErr != nil {
			logger.CtxLogErrorf(c, updateTaskLcosErr.Msg)
			return updateTaskLcosErr
		}
		// 3. 发送邮件
		sendEmailLcosError := a.saTaskRecordService.SendMail(c, taskRecord, false, 0, 0, lcosErr.Msg, constant2.SceneFailToWriteToDBWhenCompareFailed)
		if sendEmailLcosError != nil {
			logger.CtxLogErrorf(c, sendEmailLcosError.Msg)
		}
	} else {
		// 更新任务状态为resume-complete
		updateTaskLcosErr := a.saTaskRecordService.UpdateSATaskRecord(c, map[string]interface{}{"task_status": constant.SaTaskResumeCompleted, "last_operation_time": utils.GetTimestamp(c), "operator": c.GetUserEmail()}, map[string]interface{}{"id": taskRecord.ID})
		if updateTaskLcosErr != nil {
			logger.CtxLogErrorf(c, updateTaskLcosErr.Msg)
			return updateTaskLcosErr
		}
	}
	return nil
}

func (a *adminSATaskRecordController) ConfirmUpdate(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	region := strings.ToUpper(c.GetCountry())

	request := rawRequest.(*task_record2.GetTaskRecordRequest)
	records, lcosErr := a.saTaskRecordService.SearchSATaskRecord(c, map[string]interface{}{"id": request.ID, "region": region})
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}
	if len(records) == 0 {
		errMsg := fmt.Sprintf("not found task record|id=%d", request.ID)
		logger.CtxLogErrorf(c, errMsg)
		return nil, lcos_error.NewLCOSError(lcos_error.NotFoundSATaskRecordErrorCode, errMsg)
	}

	taskRecord := records[0]

	// 解析文件url
	var fileInfo *task_record2.FileInfo
	err := jsoniter.Unmarshal([]byte(taskRecord.UpdatedFileInfo), &fileInfo)
	if err != nil || fileInfo == nil || fileInfo.URL == "" {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, "server error")
	}
	fileUrl := fileInfo.URL

	// 在上传前，确认当前record未过期
	if taskRecord.IsExpired == constant.TRUE {
		errMsg := fmt.Sprintf("This update has expired, please check the result of the next update|task_id=%d", taskRecord.ID)
		logger.CtxLogErrorf(c, errMsg)
		return nil, lcos_error.NewLCOSError(lcos_error.SATaskRecordExpiredErrorCode, errMsg)
	}

	// 获取当前分布式锁，保证一个taskConf只有一条任务在运行
	lockKey := fmt.Sprintf("sa_task_lock_key:task_conf_id:%d", taskRecord.TaskConfID)
	// 产生随机数，防止任务超时后，误删除其他任务的锁
	randomKey := uuid.NewV1().String() // nolint
	// 获取锁的过期时间
	timeout := config2.GetInt("mutable_application.sa_task.distributed_key.timeout", 3600) // nolint
	err = redislib.SetNX(c, lockKey, randomKey, time.Duration(timeout)*time.Second)
	// 加锁失败
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SATaskLockErrorCode, fmt.Sprintf("cannot acquire lock|task_conf_id=%d", taskRecord.TaskConfID))
	}

	// 异步上传服务范围文件
	go a.uploadPostcodeSA(c, fileUrl, taskRecord, lockKey, randomKey)
	return nil, nil
}

func (a *adminSATaskRecordController) GetAllTaskNames(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	return a.saTaskRecordService.GetAllTaskNames(c)
}

func (a *adminSATaskRecordController) GetAllTaskOperator(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	return a.saTaskRecordService.GetTaskOperators(c)
}

func (a *adminSATaskRecordController) ListSATaskRecord(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*task_record2.ListTaskRecordRequest)
	return a.saTaskRecordService.ListSATaskRecordPaging(c, request)
}

func NewAdminSATaskRecordController(saTaskRecordService task_record.SATaskRecordServiceInterface, postcodeSAService basic_postcode.LineBasicServiceablePostcodeServiceInterface) *adminSATaskRecordController {
	return &adminSATaskRecordController{
		saTaskRecordService: saTaskRecordService,
		postcodeSAService:   postcodeSAService,
	}
}

var _ AdminSATaskRecordControllerInterface = (*adminSATaskRecordController)(nil)
