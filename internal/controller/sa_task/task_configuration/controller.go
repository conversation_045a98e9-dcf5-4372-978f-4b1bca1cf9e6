package task_configuration

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	task_configuration2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/sa_task/task_configuration"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/sa_task/task_configuration"
)

type AdminSATaskConfigurationControllerInterface interface {
	CreateSATaskConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateSATaskConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListSATaskConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetSATaskConfDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ToggleSATaskStatus(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	GetMaxDeletionCount(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminSATaskConfigurationController struct {
	saTaskConfigurationService task_configuration.SATaskConfigurationServiceInterface
}

func (a *adminSATaskConfigurationController) GetMaxDeletionCount(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*task_configuration2.GetTaskConfigurationDetailRequest)
	return a.saTaskConfigurationService.GetMaxDeletionCount(c, request.ID)
}

func (a *adminSATaskConfigurationController) ToggleSATaskStatus(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*task_configuration2.GetTaskConfigurationDetailRequest)
	lcosErr := a.saTaskConfigurationService.ToggleSaTaskStatus(c, request.ID)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}
	return nil, nil
}

func (a *adminSATaskConfigurationController) GetSATaskConfDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*task_configuration2.GetTaskConfigurationDetailRequest)
	result, lcosErr := a.saTaskConfigurationService.GetSaTaskDetail(c, request.ID)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}
	return result, nil
}

func (a *adminSATaskConfigurationController) CreateSATaskConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*task_configuration2.CreateTaskConfigurationRequest)
	lcosErr := a.saTaskConfigurationService.CreateSATaskConfiguration(c, request)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}
	return nil, nil
}

func (a *adminSATaskConfigurationController) UpdateSATaskConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*task_configuration2.UpdateTaskConfigurationRequest)
	lcosErr := a.saTaskConfigurationService.UpdateSATaskConfiguration(c, request)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}
	return nil, nil
}

func (a *adminSATaskConfigurationController) ListSATaskConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*task_configuration2.ListTaskConfigurationRequest)
	results, lcosErr := a.saTaskConfigurationService.ListSATaskConfigurationPaging(c, request)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}
	return results, nil
}

func NewAdminSATaskConfigurationController(saTaskConfigurationService task_configuration.SATaskConfigurationServiceInterface) *adminSATaskConfigurationController {
	return &adminSATaskConfigurationController{saTaskConfigurationService: saTaskConfigurationService}
}

var _ AdminSATaskConfigurationControllerInterface = (*adminSATaskConfigurationController)(nil)
