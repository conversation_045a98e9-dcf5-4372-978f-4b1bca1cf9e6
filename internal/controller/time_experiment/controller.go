package time_experiment

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/time_experiment_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/time_experiment"
)

type TimeExperimentController interface {
	CreateABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ViewABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	EditABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CreateEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ViewEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	EditEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type TimeExperimentControllerImpl struct {
	TimeExperimentService time_experiment.TimeExperimentService
}

func NewTimeExperimentControllerImpl(timeExperimentService time_experiment.TimeExperimentService) *TimeExperimentControllerImpl {
	return &TimeExperimentControllerImpl{
		TimeExperimentService: timeExperimentService,
	}
}

func (t *TimeExperimentControllerImpl) CreateABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.RouteGroupCreateRequest)
	err := t.TimeExperimentService.CreateABTestRouteGroup(c, request)
	return nil, err
}

func (t *TimeExperimentControllerImpl) ListABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.RouteGroupListRequest)
	rsp, err := t.TimeExperimentService.ListABTestRouteGroup(c, request)
	return rsp, err
}

func (t *TimeExperimentControllerImpl) ViewABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.RouteGroupViewRequest)
	rsp, err := t.TimeExperimentService.ViewABTestRouteGroup(c, request)
	return rsp, err
}

func (t *TimeExperimentControllerImpl) EditABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.RouteGroupEditRequest)
	err := t.TimeExperimentService.EditABTestRouteGroup(c, request)
	return nil, err
}

func (t *TimeExperimentControllerImpl) DeleteABTestRouteGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.RouteGroupDeleteRequest)
	err := t.TimeExperimentService.DeleteABTestRouteGroup(c, request)
	return nil, err
}

func (t *TimeExperimentControllerImpl) CreateEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.EdtStrategyCreateRequest)
	err := t.TimeExperimentService.CreateEdtStrategyRule(c, request)
	return nil, err
}

func (t *TimeExperimentControllerImpl) ListEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.EdtStrategyListRequest)
	rsp, err := t.TimeExperimentService.ListEdtStrategyRule(c, request)
	return rsp, err
}

func (t *TimeExperimentControllerImpl) ViewEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.EdtStrategyViewRequest)
	rsp, err := t.TimeExperimentService.ViewEdtStrategyRule(c, request)
	return rsp, err
}

func (t *TimeExperimentControllerImpl) EditEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.EdtStrategyEditRequest)
	err := t.TimeExperimentService.EditEdtStrategyRule(c, request)
	return nil, err
}

func (t *TimeExperimentControllerImpl) DeleteEdtStrategyRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*time_experiment_protocol.EdtStrategyDeleteRequest)
	err := t.TimeExperimentService.DeleteEdtStrategyRule(c, request)
	return nil, err
}
