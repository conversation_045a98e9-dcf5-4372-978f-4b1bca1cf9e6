package e_fence

import (
	"fmt"
	_ "fmt"
	"strconv"
	"strings"

	_ "git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/geopolygon"
	eFenceConstant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/e_fence"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/e_fence"
	"github.com/tealeg/xlsx"
)

type EFenceController interface {
	TestGetGeoHash(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	TestTransferPolygonToMesh(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	TestCheckPolygonCoverGeohash(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	TestIsCoordinateInsidePolygon(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	TestParsePolygon2GeoJson(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DebugRegenerateMesh(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListAllZonesByPage(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportAllZones(c *utils.HttpContext, rawRequest interface{})
	ListLocationWhiteList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportLocationWhiteList(c *utils.HttpContext, rawRequest interface{})
	UploadLocationWhiteList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteLocationWhiteList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetLineToggleList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateLineToggle(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteLineToggle(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetLineToggleLayer(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type eFenceController struct {
	eFenceService e_fence.EFenceServiceInterface
}

func NewEFenceController(zoneService e_fence.EFenceServiceInterface) *eFenceController {
	return &eFenceController{
		eFenceService: zoneService,
	}
}

func (e *eFenceController) ListAllZonesByPage(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.ListZoneRequest)
	if request.PageNo == 0 {
		request.PageNo = 1
	}
	if request.Count == 0 {
		request.Count = 10
	}
	region := c.GetCountry()
	dataList, total, err := e.eFenceService.ListZoneInUsageByPage(c, region, request.ZoneId, request.ZoneName, request.LayerId, request.PageNo, request.Count)
	if err != nil {
		return nil, err
	}

	resultList := make([]*protocol.ZoneItem, len(dataList))
	for i, data := range dataList {
		resultList[i] = &protocol.ZoneItem{
			ZoneId:       data.ZoneId,
			ZoneName:     data.ZoneName,
			StationId:    data.StationId,
			StationName:  data.StationName,
			StationType:  e_fence.TransferStationType(data.StationType),
			LastSyncTime: data.MTime,
			Operator:     data.Operator,

			LayerId:   data.LayerId,
			LayerName: data.LayerName,
			Id:        strconv.FormatUint(data.ID, 10),
		}
	}

	return map[string]interface{}{
		"page_no": request.PageNo,
		"count":   request.Count,
		"total":   total,
		"list":    resultList,
	}, nil
}

func (e *eFenceController) TestGetGeoHash(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, ok := rawRequest.(*protocol.GetGeoHashReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}
	gHash, err := geopolygon.GetGeoHash(request.Lng, request.Lat, request.Size)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SdkGeoHashError, err.Error())
	}
	return &protocol.GetGeoHashResp{GeoHash: gHash}, nil
}

func (e *eFenceController) TestTransferPolygonToMesh(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, ok := rawRequest.(*protocol.TransferPolygonToMeshReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}
	res, err := geopolygon.TransferPolygonToMesh(request.PolyDetail, request.Min, request.Max, request.PipMode)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SdkGeoHashError, err.Error())
	}
	return res, nil
}

func (e *eFenceController) TestCheckPolygonCoverGeohash(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, ok := rawRequest.(*protocol.CheckPolygonCoverGeohashReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}
	res, err := geopolygon.CheckPolygonCoverGeoHash(request.GeoHash, request.PolyDetail)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SdkGeoHashError, err.Error())
	}
	return &protocol.CheckPolygonCoverGeohashResp{Result: res}, nil
}

func (e *eFenceController) TestIsCoordinateInsidePolygon(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, ok := rawRequest.(*protocol.IsCoordinateInsidePolygonReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}
	res, err := geopolygon.IsCoordinateInsidePolygon(request.Lng, request.Lat, request.PolyDetail)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SdkGeoHashError, err.Error())
	}
	return &protocol.IsCoordinateInsidePolygonResp{Result: res}, nil
}

func (e *eFenceController) TestParsePolygon2GeoJson(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*protocol.ParsePolygon2GeoJsonReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}
	region := c.GetCountry()

	geojson, err := e.eFenceService.ParsePolygonAndMesh2GeoJson(c, region, req.ZoneId, req.Version, req.LayerId, req.IncludeMesh, req.IncludeGeoHash)
	if err != nil {
		return nil, err
	}
	return geojson, nil
}

func (e *eFenceController) DebugRegenerateMesh(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*protocol.RegenerateMeshReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}
	region := c.GetCountry()

	if err := e.eFenceService.RegenerateMesh(c, region, req.ZoneId, req.Version, req.LayerId, req.PipMode, req.RefreshCache); err != nil {
		return nil, err
	}
	return nil, nil
}

func (e *eFenceController) ExportAllZones(c *utils.HttpContext, rawRequest interface{}) {
	region := c.GetCountry()
	resultFile, lcosErr := e.eFenceService.ExportZoneFile(c, region)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lcosErr.Msg)
		return
	}

	fileName := fmt.Sprintf("hub_zone_data_%d.xlsx", utils.GetTimestamp(c))
	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	if err := resultFile.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "export failed, err=%s", err.Error())
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, err.Error())
		return
	}
}

func (e *eFenceController) ListLocationWhiteList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*protocol.ListLocationWhitelistReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}

	data, err := e.eFenceService.ListLocationWhiteListByPage(c, req)
	if err != nil {
		logger.CtxLogErrorf(c, "query whitelist fail|err[%s]", err.Msg)
		return nil, err
	}
	return data, nil
}

func (e *eFenceController) ExportLocationWhiteList(c *utils.HttpContext, rawRequest interface{}) {
	// 查询Location id list
	queryMap := make(map[string]interface{})
	queryMap["region"] = strings.ToUpper(c.GetCountry())
	list, err := e.eFenceService.ListAllLocationWhiteList(c, queryMap)
	if err != nil {
		logger.CtxLogErrorf(c, "ExportLocationWhiteList|query white fail|err[%s]", err.Msg)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, err.Msg)
		return
	}
	// 数据准备
	titles := eFenceConstant.EFenceWhitelistExportFileTitle
	var excelList []*e_fence.EFenceWhitelistExport
	var data []interface{}
	for _, l := range list {

		layerName, err := e.eFenceService.GetLayerNameByLayerId(c, l.LayerId)
		if err != nil {
			logger.CtxLogErrorf(c, "GetLayerNameByLayerId|query white fail|err[%s]", err.Msg)
			http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, err.Msg)
			return
		}
		excelList = append(excelList, &e_fence.EFenceWhitelistExport{
			Country:  strings.ToUpper(c.GetCountry()),
			State:    l.State,
			City:     l.City,
			District: l.District,
			Street:   l.Street,

			LayerId: fmt.Sprintf("%s - %s", l.LayerId, layerName),
		})
	}
	for _, singleData := range excelList {
		data = append(data, singleData)
	}

	// 文件导出
	fileName := fmt.Sprintf("whitelist_location_for_lm_hub_data_%s_%d.xlsx", strings.ToUpper(c.GetCountry()), recorder.Now(c).Unix())
	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")
	file := xlsx.NewFile()

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, titles, data); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

}

func (e *eFenceController) UploadLocationWhiteList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*protocol.UploadLocationWhitelistReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}

	err := e.eFenceService.UploadLocationWhiteList(c, req.FileUrl)
	if err != nil {
		logger.CtxLogErrorf(c, "batch update location whitelist fail|err[%s]", err.Msg)
		return nil, err
	}

	return nil, nil
}

func (e *eFenceController) DeleteLocationWhiteList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*protocol.DeleteLocationWhitelistReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}
	err := e.eFenceService.DeleteLocationWhitelistById(c, req.LocationId, req.LayerId)
	if err != nil {
		logger.CtxLogErrorf(c, "Controller|delete whitelist fail|locationId[%d]", req.LocationId)
		return nil, err
	}
	return nil, nil
}

func (e *eFenceController) GetLineToggleList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*protocol.GetLineToggleListReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}
	resp, err := e.eFenceService.ListLineToggle(c, req)
	if err != nil {
		logger.CtxLogErrorf(c, "get line toggle list fail|err[%s]", err.Msg)
		return nil, err
	}
	return resp, nil
}

func (e *eFenceController) UpdateLineToggle(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*protocol.UpdateLineToggleReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}

	req.LayerId = e_fence.GetRealLayerId(c.GetCountry(), req.LayerId)

	err := e.eFenceService.UpdateLineToggle(c, req)
	if err != nil {
		logger.CtxLogErrorf(c, "update line toggle list fail|err[%s]", err.Msg)
		return nil, err
	}
	return nil, nil
}

func (e *eFenceController) DeleteLineToggle(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*protocol.DeleteLineToggleReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}

	err := e.eFenceService.DeleteLineToggle(c, req)
	if err != nil {
		logger.CtxLogErrorf(c, "delete line toggle list fail|err[%s]", err.Msg)
		return nil, err
	}
	return nil, nil
}

func (e *eFenceController) GetLineToggleLayer(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*protocol.GetLineToggleLayerReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "transfer request fail")
	}

	result, err := e.eFenceService.GetLineToggleLayer(c, req)
	if err != nil {
		logger.CtxLogErrorf(c, "get line toggle layer fail|err[%s]", err.Msg)
		return nil, err
	}
	return result, nil
}
