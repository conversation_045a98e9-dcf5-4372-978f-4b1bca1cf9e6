package station

import (
	"fmt"

	"github.com/go-playground/validator/v10"

	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pis_protocol"
	station_protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/station"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/station"
)

type StationController interface {
	BatchSyncStation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListStation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetStation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportStation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchGetDistanceFromCache(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchGetDistanceCacheByAddrId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SyncDistanceData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	HandleStationBuyerRelation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	RefreshStationInfoInRedis(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetCacheVal(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateStationVolume(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetDistancePartition(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CalculateBuyerStationDistance(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	AddressRevision(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	AddressRevisionDataInsert(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	AddressRevisionHandle(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type stationController struct {
	StationService       station.StationService
	StationVolumeService station.StationVolumeService
	validate             *validator.Validate
}

func NewStationController(stationService station.StationService, StationVolumeService station.StationVolumeService) *stationController {
	return &stationController{
		StationService:       stationService,
		StationVolumeService: StationVolumeService,
		validate:             validator.New(),
	}
}

func (s *stationController) BatchSyncStation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pis_protocol.PISCommonRequest)
	// // 设置日志id，方便查看相关日志
	// ctx := context.WithValue(c.Ctx, constant2.RequestIdKey, "|"+request.ReqHeader.RequestID)
	// c.Ctx = ctx
	var data pis_protocol.BatchSyncStationReq
	err := jsoniter.UnmarshalFromString(request.BusinessData, &data)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("param error:%s", err.Error()))
	}
	// param validation
	err = s.validate.Struct(&data)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	return s.StationService.BatchSyncStation(c, &data)
}

func (s *stationController) AddressRevision(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pis_protocol.PISCommonRequest)
	var data pis_protocol.AddressRevisionReq
	err := jsoniter.UnmarshalFromString(request.BusinessData, &data)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("param error:%s", err.Error()))
	}
	// param validation
	err = s.validate.Struct(&data)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}
	lErr := s.StationService.SyncAddressRevision(c, &data)
	return nil, lErr
}

func (s *stationController) ListStation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.ListStationRequest)
	return s.StationService.ListStation(c, request)
}

func (s *stationController) GetStation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.GetStationRequest)
	return s.StationService.GetStation(c, request)
}

func (s *stationController) ExportStation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.ExportStationRequest)
	return s.StationService.ExportStation(c, request)
}

func (s *stationController) BatchGetDistanceFromCache(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	// 通过三级地址获取买家-站点距离缓存
	request, _ := rawRequest.(*station_protocol.BatchGetDistanceReq)
	return s.StationService.BatchGetDistanceFromCache(c, request)
}

func (s *stationController) BatchGetDistanceCacheByAddrId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.BatchGetDistanceReq)
	return s.StationService.BatchGetDistanceCacheByAddrId(c, request)
}

func (s *stationController) SyncDistanceData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.SyncDistanceDataReq)
	err := s.StationService.SyncDistanceData(c, request)
	return nil, err
}

func (s *stationController) HandleStationBuyerRelation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.SyncDistanceDataReq)
	err := s.StationService.HandleStationBuyerRelation(c, request)
	return nil, err
}

func (s *stationController) RefreshStationInfoInRedis(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.RefreshStationInfoInRedisReq)
	return s.StationService.RefreshStationInfoInRedis(c, request)
}

func (s *stationController) GetCacheVal(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.QueryCacheValReq)
	return s.StationService.GetCacheVal(c, request)
}

func (s *stationController) UpdateStationVolume(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.StationVolumeReq)
	err := s.StationVolumeService.UpdateStationVolume(c, request.VolumeReq, request.Region)
	return nil, err
}

func (s *stationController) GetDistancePartition(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.GetDistancePartitionReq)
	return s.StationService.GetDistancePartition(c, request)
}

func (s *stationController) CalculateBuyerStationDistance(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.CalculateStationBuyerDistance)
	err := s.StationService.CalculateStationBuyerDistance(ctx, request)
	return nil, err
}

func (s *stationController) AddressRevisionHandle(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*station_protocol.HandleAddressRevisionJobReq)
	err := s.StationService.AddressRevisionHandle(ctx, request)
	return nil, err
}

func (s *stationController) AddressRevisionDataInsert(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*schema.AddressRevisionDataInsertMsg)
	err := s.StationService.AddressRevisionDataInsert(ctx, request)
	return nil, err
}
