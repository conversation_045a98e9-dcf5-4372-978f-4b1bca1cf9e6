package auto_update_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"regexp"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	auto_update_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_rule"
	"github.com/tealeg/xlsx"
)

type AdminCdtAutoRuleController interface {
	GetTimePeriod(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// SPLN-24104: 创建自动规则新增cdt_type字段
	CreateCdtAutoUpdateRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// SPLN-24104: 支持基于cdt_type的条件查询
	ListCdtAutoUpdateRuleByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// SPLN-24104: 返回新增cdt_type字段
	GetCdtAutoUpdateRuleByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteCdtAutoUpdateRuleByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// SPLN-24104: 自动规则状态校验支持lane维度数据
	ToggleCdtAutoUpdateRuleStatus(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportCdtAutoUpdateRuleCepRangeByID(c *utils.HttpContext, rawRequest interface{})
	ExportCdtAutoUpdatePostcodeByID(c *utils.HttpContext, rawRequest interface{})
	// SPLN-24104: 支持导出lane维度cdt数据
	ExportCdtAutoUpdateID(c *utils.HttpContext, rawRequest interface{})

	UpdateProductCtime(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	// SPLN-24104: 支持导入lane维度cdt数据
	ParseAndImportCdtAutoUpdateData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListLaneAutoUpdateData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type cdtAutoRuleController struct {
	cdtAutoService           auto_update_rule.AutoUpdateServiceInterface
	cdtAutoUpdateDataService auto_update_rule3.AutoUpdateDataServiceInterface
}

func NewCdtAutoRuleController(cdtAutoService auto_update_rule.AutoUpdateServiceInterface, cdtAutoUpdateDataService auto_update_rule3.AutoUpdateDataServiceInterface) *cdtAutoRuleController {
	return &cdtAutoRuleController{
		cdtAutoService:           cdtAutoService,
		cdtAutoUpdateDataService: cdtAutoUpdateDataService,
	}
}

var _ AdminCdtAutoRuleController = (*cdtAutoRuleController)(nil)

func (c2 *cdtAutoRuleController) CreateCdtAutoUpdateRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*auto_update_rule2.CreateAutoRuleRequest)
	lcosErr := c2.cdtAutoService.CreateAutoUpdateRule(c, request)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}
	return nil, nil
}

func (c2 *cdtAutoRuleController) ListCdtAutoUpdateRuleByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*auto_update_rule2.ListAutoRuleRequest)
	response, lcosErr := c2.cdtAutoService.ListAutoUpdateRuleByParamsPaging(c, request)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return response, lcosErr

}

func (c2 *cdtAutoRuleController) GetCdtAutoUpdateRuleByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*auto_update_rule2.GetAutoRuleRequest)
	model, lcosErr := c2.cdtAutoService.GetAutoUpdateRuleByID(c, request)
	if lcosErr != nil {
		return nil, lcosErr
	}
	response := &auto_update_rule2.CreateAutoRuleRequest{
		ProductID:                model.ProductID,
		ProductName:              model.ProductName,
		CdtType:                  model.CdtType,
		CBType:                   model.CBType,
		MaskingType:              model.MaskingType,
		IntegratedType:           model.IntegratedType,
		IsSiteLine:               model.IsSiteLine,
		IsLM:                     model.IsLM,
		OriginLocationLevel:      model.OriginLocationLevel,
		DestinationLocationLevel: model.DestinationLocationLevel,
		TimePeriod:               model.TimePeriod,
		ExcludedDays:             model.ExcludedDays,
		ThresholdNum:             model.ThresholdNum,
		Frequency:                model.Frequency,
		MinPercentile:            model.MinPercentile,
		MaxPercentile:            model.MaxPercentile,
		LmMaxPercentile:          model.LmMaxPercentile,
		RemoveFlag:               model.RemoveFlag,
		RemoveThresholdNum:       model.RemoveThresholdNum,
		ExcludedDaysArray:        auto_update_rule2.GetExcludedDaysArray(model.ExcludedDays),
		CalculateEddMin:          model.CalculateEddMin,
		EddRangeLimit:            model.EddRangeLimit,
		PreemptiveConfig:         model.PreemptiveConfig,
		FromForecastTaskID:       model.FromForecastTaskID,

		// SPLN-30795
		TestGroupTag:        model.TestGroupTag,
		ForecastMethod:      model.ForecastMethod,
		ForecastDataVersion: model.ForecastDataVersion,
		ForecastDataUUID:    model.ForecastDataUUID,
		EventTimeLevel:      model.EventTimeLevel,
		DayGroup:            model.DayGroup,
		StartEventFCode:     model.StartEventFCode,
	}

	// set destination cep range url or postcode url decided by destination location level
	if model.DestinationLocationLevel == constant.CepRange {
		response.DestinationCepRangeUrl = fmt.Sprintf("%v:%v", constant.DUPLICATE_FROM_PREFIX, model.ID) // detail的接口需要标识从哪里传过来，方便创建的时候直接获取老的内容解析
		response.DestinationCepRangeFileName = model.DestinationCepRangeFileName
	} else if model.DestinationLocationLevel == constant.CDTPostcode {
		response.DestinationPostcodeUrl = fmt.Sprintf("%v:%v", constant.DUPLICATE_FROM_PREFIX, model.ID)
		response.DestinationPostcodeFileName = model.DestinationPostcodeFileName
	}
	return response, nil
}

func (c2 *cdtAutoRuleController) ToggleCdtAutoUpdateRuleStatus(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*auto_update_rule2.ToggleAutoUpdateRuleStatusRequest)
	response, lcosErr := c2.cdtAutoService.ToggleAutoUpdateRuleStatus(c, request)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return response, nil
}

func (c2 *cdtAutoRuleController) DeleteCdtAutoUpdateRuleByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*auto_update_rule2.DeleteAutoRuleRequest)
	lcosErr := c2.cdtAutoService.DeleteAutoUpdateRuleByID(c, request)
	return nil, lcosErr
}

func (c2 *cdtAutoRuleController) ExportCdtAutoUpdatePostcodeByID(c *utils.HttpContext, rawRequest interface{}) {
	request, _ := rawRequest.(*auto_update_rule2.GetAutoRuleRequest)
	response, lcosErr := c2.cdtAutoService.GetAutoUpdateRuleByID(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lcosErr.Msg)
		return
	}

	if response.DestinationLocationLevel != constant.CDTPostcode {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, fmt.Sprintf("auto update rule is not postcode|auto_update_id=%d", request.ID))
		return
	}

	postcodeList, lcosErr := c2.cdtAutoService.GetPostcodeListByAutoUpdateRuleID(c, request.ID)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lcosErr.Msg)
		return
	}
	if len(postcodeList) == 0 {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "postcode is empty")
		return
	}

	fileName := response.DestinationPostcodeFileName

	excelTiles := []string{"Postcode"}
	var excelRawData []interface{}

	for _, postcode := range postcodeList {
		excelRawData = append(excelRawData, &manual_manipulation_rule.ExcelRawOfPostcode{
			Postcode: postcode.PostcodeLeft,
		})
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTiles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (c2 *cdtAutoRuleController) ExportCdtAutoUpdateRuleCepRangeByID(c *utils.HttpContext, rawRequest interface{}) {
	request, _ := rawRequest.(*auto_update_rule2.GetAutoRuleRequest)
	response, lcosErr := c2.cdtAutoService.GetAutoUpdateRuleByID(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lcosErr.Msg)
		return
	}

	if response.DestinationLocationLevel != constant.CepRange {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, fmt.Sprintf("auto update rule is not cep range|auto_update_id=%d", response.ID))
		return
	}

	cepRange := response.DestinationCepRange
	cepRangeList := strings.Split(cepRange, "#")
	if len(cepRangeList) == 0 {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "cep range is empty")
		return
	}

	fileName := response.DestinationCepRangeFileName

	excelTiles := []string{"Destination CEP initial", "Destination CEP end"}
	var excelRawData []interface{}

	for _, singleCepString := range cepRangeList {
		cepStringList := strings.Split(singleCepString, "-")
		excelRawData = append(excelRawData, &manual_manipulation_rule.ExcelRawOfCepRange{
			DestinationCepInitial: cepStringList[0],
			DestinationCepEnd:     cepStringList[1],
		})
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTiles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (c2 *cdtAutoRuleController) ExportCdtAutoUpdateID(c *utils.HttpContext, rawRequest interface{}) {

	region := c.GetCountry()

	request, _ := rawRequest.(*auto_update_rule2.GetAutoRuleRequest)

	// 获取cdt的结果信息
	autoUpdateRuleTab, lcosErr := c2.cdtAutoService.GetAutoUpdateRuleByID(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lcosErr.Msg)
		return
	}

	// 只有状态为draft，upcoming，active的任务才有可能导出
	if !(autoUpdateRuleTab.StatusID == constant.Draft || autoUpdateRuleTab.StatusID == constant.Upcoming || autoUpdateRuleTab.StatusID == constant.Active) {
		http.GenErrorResponseWithParam(c.Context, lcos_error.SchemaParamsErrorCode, nil, "task status is not allowed to exported")
		return
	}

	// 获取版本信息
	cdtVersion, lcosErr := c2.cdtAutoUpdateDataService.GetCdtVersionByAutoUpdateRuleID(c, autoUpdateRuleTab.ID)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcosErr.RetCode, nil, lcosErr.Msg)
		return
	}
	// 通过版本信息获取需要导出的数据
	productCdtData, lcosErr := c2.cdtAutoUpdateDataService.GetCdtCalculationDataByCdtVersion(c, autoUpdateRuleTab.ID, cdtVersion)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcosErr.RetCode, nil, lcosErr.Msg)
		return
	}
	laneCdtData, lcosErr := c2.cdtAutoUpdateDataService.GetLaneCdtDataByCdtVersion(c, region, autoUpdateRuleTab.ID, cdtVersion)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcosErr.RetCode, nil, lcosErr.Msg)
		return
	}

	fileName := fmt.Sprintf("auto_update_calculation_data_%v_%v.xlsx", autoUpdateRuleTab.ProductID, autoUpdateRuleTab.ProductName)
	// 将空格替换为下划线
	reg := regexp.MustCompile(" +")
	fileName = reg.ReplaceAllString(fileName, "_")
	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")
	file := xlsx.NewFile()

	var excelTiles = auto_update_rule2.ExcelRawOfAutoUpdateResultTitles

	var excelRawData []interface{}

	for _, model := range productCdtData {
		// 获取地址信息
		var originLocation = &address_service.LocationInfo{}
		var destinationLocation = &address_service.LocationInfo{}
		if model.OriginLocationID != 0 {
			originLocation, lcosErr = address_service.LocationServer.GetLocationInfoById(c, model.OriginLocationID)
			if lcosErr != nil {
				// 解析不到地址则跳过，且输出错误日志
				logger.CtxLogErrorf(c, "parse origin location id:[%d] error:%v", model.OriginLocationID, lcosErr.Msg)
				continue
			}
		}
		if model.DestinationLocationID != 0 {
			destinationLocation, lcosErr = address_service.LocationServer.GetLocationInfoById(c, model.DestinationLocationID)
			if lcosErr != nil {
				// 解析不到地址则跳过，且输出错误日志
				logger.CtxLogErrorf(c, "parse destination location id:[%d] error:%v", model.DestinationLocationID, lcosErr.Msg)
				continue
			}
		}
		destinationCepInitial := ""
		if model.DestinationCepInitial != 0 {
			destinationCepInitial = strconv.Itoa(model.DestinationCepInitial)
		}
		destinationCepFinal := ""
		if model.DestinationCepFinal != 0 {
			destinationCepFinal = strconv.Itoa(model.DestinationCepFinal)
		}
		updateEvent := ""
		if updateEventString, err := edd_constant.GetUpdateEventString(model.UpdateEvent); err == nil {
			updateEvent = updateEventString
		}

		tmpExportedData := &auto_update_rule2.ExcelRawOfAutoUpdateResult{
			OriginCountry:         model.OriginRegion,
			OriginState:           originLocation.State,
			OriginCity:            originLocation.City,
			OriginDistrict:        originLocation.District,
			DestinationCountry:    model.DestinationRegion,
			DestinationState:      destinationLocation.State,
			DestinationCity:       destinationLocation.City,
			DestinationDistrict:   destinationLocation.District,
			DestinationCepInitial: destinationCepInitial,
			DestinationCepFinal:   destinationCepFinal,
			DestinationPostcode:   model.DestinationPostcode,
			LeadTimeMin:           fmt.Sprintf("%v", model.LeadTimeMin),
			LeadTimeMax:           fmt.Sprintf("%v", model.LeadTimeMax),
			ProductID:             model.ProductID,
			UpdateEvent:           updateEvent,
			AutoUpdateID:          strconv.FormatUint(model.AutoUpdateRuleID, 10),
			CdtVersion:            strconv.Itoa(int(model.CdtVersion)),
			DDLBackwardCDT:        fmt.Sprintf("%v", model.DDLBackwardCDT),
			CdtExtraData:          model.CdtExtraData,
		}

		if autoUpdateRuleTab.CdtType == edd_constant.CdtObject {
			tmpExportedData.DDLBackwardCDT = "-"
		}

		excelRawData = append(excelRawData, tmpExportedData)
	}

	for _, model := range laneCdtData {
		// 获取地址信息
		var originLocation = &address_service.LocationInfo{}
		var destinationLocation = &address_service.LocationInfo{}
		if model.OriginLocationID != 0 {
			originLocation, lcosErr = address_service.LocationServer.GetLocationInfoById(c, model.OriginLocationID)
			if lcosErr != nil {
				// 解析不到地址则跳过，且输出错误日志
				logger.CtxLogErrorf(c, "parse origin location id error:%v", lcosErr.Msg)
				continue
			}
		}
		if model.DestinationLocationID != 0 {
			destinationLocation, lcosErr = address_service.LocationServer.GetLocationInfoById(c, model.DestinationLocationID)
			if lcosErr != nil {
				// 解析不到地址则跳过，且输出错误日志
				logger.CtxLogErrorf(c, "parse destination location id error:%v", lcosErr.Msg)
				continue
			}
		}
		destinationCepInitial := ""
		if model.DestinationCepInitial != 0 {
			destinationCepInitial = strconv.Itoa(model.DestinationCepInitial)
		}
		destinationCepFinal := ""
		if model.DestinationCepFinal != 0 {
			destinationCepFinal = strconv.Itoa(model.DestinationCepFinal)
		}
		updateEvent := ""
		if updateEventString, err := edd_constant.GetUpdateEventString(model.UpdateEvent); err == nil {
			updateEvent = updateEventString
		}

		tmpExportedData := &auto_update_rule2.ExcelRawOfAutoUpdateResult{
			OriginCountry:         model.OriginRegion,
			OriginState:           originLocation.State,
			OriginCity:            originLocation.City,
			OriginDistrict:        originLocation.District,
			DestinationCountry:    model.DestinationRegion,
			DestinationState:      destinationLocation.State,
			DestinationCity:       destinationLocation.City,
			DestinationDistrict:   destinationLocation.District,
			DestinationCepInitial: destinationCepInitial,
			DestinationCepFinal:   destinationCepFinal,
			DestinationPostcode:   model.DestinationPostcode,
			ProductID:             model.ProductID,
			LaneID:                model.LaneCode,
			UpdateEvent:           updateEvent,
			LeadTimeMin:           fmt.Sprintf("%v", model.LeadTimeMin),
			LeadTimeMax:           fmt.Sprintf("%v", model.LeadTimeMax),
			AutoUpdateID:          strconv.FormatUint(model.AutoUpdateRuleID, 10),
			CdtVersion:            strconv.Itoa(int(model.CdtVersion)),
			DDLBackwardCDT:        fmt.Sprintf("%v", model.DDLBackwardCDT),
			CdtExtraData:          model.CdtExtraData,
		}
		if autoUpdateRuleTab.CdtType == edd_constant.CdtObject {
			tmpExportedData.DDLBackwardCDT = "-"
		}
		excelRawData = append(excelRawData, tmpExportedData)
	}

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTiles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (c2 *cdtAutoRuleController) GetTimePeriod(c *utils.HttpContext,
	rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*auto_update_rule2.GetTimePeriod)
	return c2.cdtAutoService.GetTimePeriod(c, request)
}

func (c2 *cdtAutoRuleController) ParseAndImportCdtAutoUpdateData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*auto_update_rule2.AutoUpdateDataNotify)
	// 异步解析任务
	go c2.cdtAutoUpdateDataService.ParseDataFileAndImportData(c, request)
	return nil, nil
}

func (c2 *cdtAutoRuleController) UpdateProductCtime(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*auto_update_rule2.UpdateProductCtimeRequest)
	go c2.cdtAutoService.BatchCreateOrUpdateProductCtime(c, request)
	return nil, nil
}

func (c2 *cdtAutoRuleController) ListLaneAutoUpdateData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*auto_update_rule2.ListLaneAutoUpdateDataRequest)
	region := c.GetCountry()
	return c2.cdtAutoUpdateDataService.GetLaneCdtDataByCdtVersion(c, region, req.RuleId, req.CdtVersion)
}
