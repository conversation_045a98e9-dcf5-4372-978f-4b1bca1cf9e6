package forecast_task

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/auto_update_rule"
	edd_auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_auto_update_rule"
	edd_forecast_task_pb "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_forecast_task"
	auto_update_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	edd_forecast_task2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_forecast_task"
)

type AdminEDDForecastTaskController interface {
	CreateEDDForecastTask(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEDDForecastTasks(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DetailEDDForecastTask(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeployEDDForecastRules(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type eddForecastTaskController struct {
	eddForecastTaskService   edd_forecast_task.EDDForecastTaskInterface
	cdtAutoUpdateRuleService auto_update_rule.AutoUpdateServiceInterface
	eddAutoUpdateRuleService edd_auto_update_rule.EDDAutoUpdateRuleInterface
}

func NewEDDForecastTaskController(eddForecastTaskService edd_forecast_task.EDDForecastTaskInterface, cdtAutoUpdateRuleService auto_update_rule.AutoUpdateServiceInterface, eddAutoUpdateRuleService edd_auto_update_rule.EDDAutoUpdateRuleInterface) *eddForecastTaskController {
	return &eddForecastTaskController{
		eddForecastTaskService:   eddForecastTaskService,
		cdtAutoUpdateRuleService: cdtAutoUpdateRuleService,
		eddAutoUpdateRuleService: eddAutoUpdateRuleService,
	}
}

func (e *eddForecastTaskController) CreateEDDForecastTask(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_forecast_task_pb.CreateEDDForecastTaskRequest)
	_, lcosErr := e.eddForecastTaskService.CreateForecastTask(ctx, request)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		return nil, lcosErr
	}
	return nil, nil
}

func (e *eddForecastTaskController) ListEDDForecastTasks(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_forecast_task_pb.ListEDDForecastTaskRequest)
	return e.eddForecastTaskService.ListEDDForecastTasks(ctx, request)
}

func (e *eddForecastTaskController) DetailEDDForecastTask(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_forecast_task_pb.DetailEDDForecastTaskRequest)
	return e.eddForecastTaskService.DetailEDDForecastTask(ctx, request)
}

func transferUpdateEventListToPreemptiveConfig(eddConfig *edd_forecast_task2.EDDAutoUpdateConfig) auto_update_rule3.PreemptiveConfig {
	preemptiveConfig := make([]auto_update_rule3.SingleUpdateEvent, 0, len(eddConfig.UpdateEvents))
	for _, event := range eddConfig.UpdateEvents {
		preemptiveConfig = append(preemptiveConfig, auto_update_rule3.SingleUpdateEvent{
			Event:              event.Event,
			DeadlineMethod:     event.DeadlineMethod,
			PreemptLateParcels: event.PreemptLateParcels,
			ConfidenceLevel:    event.ConfidenceLevel,
		})
	}
	return preemptiveConfig
}

func parseLeadConfigToAutoUpdateRuleRequest(forecastTask *edd_forecast_task2.EDDForecastTaskTab, leadConfig *edd_forecast_task2.LeadConfig, testGroupTag uint8, objectType uint8) *auto_update_rule2.CreateAutoRuleRequest {
	request := &auto_update_rule2.CreateAutoRuleRequest{
		CdtType:                     objectType,
		IsSiteLine:                  constant.TRUE,
		IsLM:                        constant.FALSE,
		OriginLocationLevel:         leadConfig.OriginLocationLevel,
		DestinationLocationLevel:    leadConfig.DestinationLocationLevel,
		DestinationCepRangeUrl:      leadConfig.DestinationCepRangeUrl,
		DestinationCepRangeFileName: leadConfig.DestinationCepRangeFileName,
		DestinationPostcodeUrl:      leadConfig.DestinationPostcodeUrl,
		DestinationPostcodeFileName: leadConfig.DestinationPostcodeFileName,
		TimePeriod:                  leadConfig.TimePeriod,
		ThresholdNum:                leadConfig.ThresholdNum,
		Frequency:                   leadConfig.Frequency,
		MinPercentile:               leadConfig.MinPercentile,
		MaxPercentile:               leadConfig.MaxPercentile,
		RemoveFlag:                  leadConfig.RemoveFlag,
		RemoveThresholdNum:          leadConfig.RemoveThresholdNum,
		ExcludedDaysArray:           leadConfig.ExcludedDaysArray,
		ProductInfos: []auto_update_rule2.ProductInfoReq{
			{
				ProductID:      forecastTask.ProductId,
				ProductName:    forecastTask.ProductName,
				CBType:         forecastTask.CBType,
				MaskingType:    forecastTask.MaskingType,
				IntegratedType: forecastTask.IntegratedType,
			},
		},
		CalculateEddMin:    leadConfig.CalculateEddMin,
		EddRangeLimit:      leadConfig.EddRangeLimit,
		FromForecastTaskID: forecastTask.ID,
		TestGroupTag:       testGroupTag,
		EventTimeLevel:     forecastTask.LeadConfig.EventTimeLevel,
		DayGroup:           forecastTask.LeadConfig.DayGroup,
	}

	// SPLN-30795 fill in extra data
	if forecastTask.IsSystemRecommendTask() {
		request.ForecastMethod = edd_constant.SystemRecommendsForecastRules
		request.ForecastDataVersion = leadConfig.DataVersion
		request.ForecastDataUUID = leadConfig.UUID
	}
	return request
}

func parseEddAutoUpdateRuleToEDDAutoUpdateRequest(forecastTask *edd_forecast_task2.EDDForecastTaskTab, config *edd_forecast_task2.EDDAutoUpdateConfig) *edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest {
	request := &edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest{
		ProductIDList: []*edd_auto_update_rule2.SingleProductInfo{
			{
				ProductID:   forecastTask.ProductId,
				ProductName: forecastTask.ProductName,
			},
		},
		EddMaxThresholdMin:  config.EddMaxThresholdMin,
		EddMaxThresholdMax:  config.EddMaxThresholdMax,
		EddMinThresholdMin:  config.EddMinThresholdMin,
		EddMinThresholdMax:  config.EddMinThresholdMax,
		MaxTimes:            config.MaxTimes,
		UpdateEvents:        config.UpdateEvents,
		CheckpointFrequency: config.CheckpointFrequency,
		FromForecastTaskID:  forecastTask.ID,
	}
	return request
}

func (e *eddForecastTaskController) DeployEDDForecastRules(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	// 1. Get Deploy Info, including LeadConfig and eddAutoUpdateRule
	// 2. Deploy to live
	// 3. Update Forecast task status

	request, _ := rawRequest.(*edd_forecast_task_pb.DeployEDDForecastTaskRequest)

	forecastTask, leadConfig, eddAutoUpdateConfig, ruleName, lcosErr := e.eddForecastTaskService.GetForecastTaskDeployInfo(ctx, request.ForecastTaskID, request.DeployRuleID)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		return nil, lcosErr
	}

	// check result status of edd auto update rule is success

	leadRequest := parseLeadConfigToAutoUpdateRuleRequest(forecastTask, leadConfig, request.TestGroupTag, forecastTask.ObjectType)

	// 对于EDD的仿真，部署的时候需要创建EDD 自动更新规则；EDT没有这个
	if forecastTask.ObjectType == edd_constant.LeadTimeObject && eddAutoUpdateConfig != nil {
		leadRequest.PreemptiveConfig = transferUpdateEventListToPreemptiveConfig(eddAutoUpdateConfig)
	}

	// call to create lead auto update rule
	lcosErr = e.cdtAutoUpdateRuleService.CreateAutoUpdateRule(ctx, leadRequest)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		return nil, lcosErr
	}

	// 对于EDD的仿真，部署的时候需要创建EDD 自动更新规则；EDT没有这个
	if forecastTask.ObjectType == edd_constant.LeadTimeObject {
		eddAutoUpdateRuleRequest := parseEddAutoUpdateRuleToEDDAutoUpdateRequest(forecastTask, eddAutoUpdateConfig)

		// call to create edd auto update rule
		lcosErr = e.eddAutoUpdateRuleService.CreateEDDAutoUpdateRule(ctx, eddAutoUpdateRuleRequest)
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, lcosErr.Msg)
			return nil, lcosErr
		}
	}

	deployInfo, lcosErr := e.eddForecastTaskService.UpdateEDDForecastTaskAfterDeployed(ctx, forecastTask, request.DeployRuleID, ruleName, request.DeployOption)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		return nil, lcosErr
	}
	return deployInfo, nil
}

var _ AdminEDDForecastTaskController = (*eddForecastTaskController)(nil)
