package edd_delay_queue

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_delay_queue_api"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_delay_queue"
)

type EddDelayQueueDebugController interface {
	GetQueueName(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	PushEddWayBill(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	RemoveEddWayBill(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEddWayBill(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewEddDelayQueueDebugController(delayQueue edd_delay_queue.EddWaybillDelayQueue) *eddDelayQueueDebugController {
	return &eddDelayQueueDebugController{
		delayQueue: delayQueue,
	}
}

type eddDelayQueueDebugController struct {
	delayQueue edd_delay_queue.EddWaybillDelayQueue
}

func (e *eddDelayQueueDebugController) GetQueueName(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*edd_delay_queue_api.GetQueueNameRequest)
	return e.delayQueue.GetQueueNameBySlsTN(ctx, req.Region, req.SlsTN), nil
}

func (e *eddDelayQueueDebugController) PushEddWayBill(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*edd_delay_queue_api.EddWayBillRequest)
	waybill := &edd_delay_queue.EddWaybill{
		SlsTN:       req.SlsTN,
		UpdateEvent: req.UpdateEvent,
		DDL:         req.DDL,
	}
	if err := e.delayQueue.PushEddWaybill(ctx, req.Region, waybill); err != nil {
		return nil, err
	}
	return nil, nil
}

func (e *eddDelayQueueDebugController) RemoveEddWayBill(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*edd_delay_queue_api.EddWayBillRequest)
	waybill := &edd_delay_queue.EddWaybill{
		SlsTN:       req.SlsTN,
		UpdateEvent: req.UpdateEvent,
		DDL:         req.DDL,
	}
	if err := e.delayQueue.RemoveEddWaybill(ctx, req.Region, waybill); err != nil {
		return nil, err
	}
	return nil, nil
}

func (e *eddDelayQueueDebugController) ListEddWayBill(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*edd_delay_queue_api.ListEddWayBillRequest)
	if len(req.QueueName) != 0 {
		ret, err := e.delayQueue.ListEddWayBillByQueue(ctx, req.QueueName)
		if err != nil {
			return nil, err
		}
		return map[string][]*edd_delay_queue.EddWaybill{req.QueueName: ret}, nil
	} else {
		ret, err := e.delayQueue.ListAllEddWaybill(ctx, req.Region)
		if err != nil {
			return nil, err
		}
		return ret, nil
	}
}
