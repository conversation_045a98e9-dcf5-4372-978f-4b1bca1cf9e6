package manual_manipulation_rule

import (
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/manual_manipulation_rule"
	manual_manipulation_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_manipulation_rule"
	"github.com/tealeg/xlsx"
)

type AdminCdtManualManipulationRuleController interface {
	CreateManualManipulationRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteManualManipulationRuleById(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListCdtManualManipulationByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetManualManipulationRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportCepRangeFileByUUID(c *utils.HttpContext, rawRequest interface{})
	ExportPostcodeFileByUUID(c *utils.HttpContext, rawRequest interface{})
	ToggleManualManipulationStatus(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	// SPLN-24104, 提供给前端查询product的lanecode信息
	ListProductLaneCode(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListLaneManualManipulationRules(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type cdtManualManipulationController struct {
	cdtManualManipulationService manual_manipulation_rule2.ManualManipulationServiceInterface
}

func NewCdtManualManipulationController(cdtManualManipulationService manual_manipulation_rule2.ManualManipulationServiceInterface) *cdtManualManipulationController {
	return &cdtManualManipulationController{
		cdtManualManipulationService: cdtManualManipulationService,
	}
}

var _ AdminCdtManualManipulationRuleController = (*cdtManualManipulationController)(nil)

func (c2 *cdtManualManipulationController) CreateManualManipulationRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_manipulation_rule.CreateManualManipulationRequest)
	lcosErr := c2.cdtManualManipulationService.CreateManualManipulationRules(c, request)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}
	return nil, nil
}

func (c2 *cdtManualManipulationController) DeleteManualManipulationRuleById(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_manipulation_rule.GetManualManipulationRequest)
	lcosErr := c2.cdtManualManipulationService.DeleteManualManipulationRuleById(c, request)
	return nil, lcosErr
}

func (c2 *cdtManualManipulationController) ExportPostcodeFileByUUID(c *utils.HttpContext, rawRequest interface{}) {
	request, _ := rawRequest.(*manual_manipulation_rule.GetManualManipulationRequest)
	postcodeList, fileName, lcosErr := c2.cdtManualManipulationService.GetPostcodeData(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "postcode is empty")
		return
	}

	excelTiles := []string{"Postcode"}
	var excelRawData []interface{}
	for _, model := range postcodeList {
		excelRawData = append(excelRawData, &manual_manipulation_rule.ExcelRawOfPostcode{
			Postcode: model,
		})
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTiles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

}

func (c2 *cdtManualManipulationController) ListCdtManualManipulationByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_manipulation_rule.ListManualManipulationRequest)
	return c2.cdtManualManipulationService.ListManualManipulationByParamsPaging(c, request)
}

func (c2 *cdtManualManipulationController) GetManualManipulationRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_manipulation_rule.GetManualManipulationRequest)
	return c2.cdtManualManipulationService.GetManualManipulationRuleByID(c, request.ID)
}

func (c2 *cdtManualManipulationController) ExportCepRangeFileByUUID(c *utils.HttpContext, rawRequest interface{}) {
	request, _ := rawRequest.(*manual_manipulation_rule.GetManualManipulationRequest)
	cepRangeModels, fileName, lcosErr := c2.cdtManualManipulationService.GetCepRangeData(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "cep range is empty")
		return
	}

	excelTiles := []string{"Destination CEP initial", "Destination CEP end"}
	var excelRawData []interface{}
	for _, model := range cepRangeModels {
		excelRawData = append(excelRawData, &manual_manipulation_rule.ExcelRawOfCepRange{
			DestinationCepInitial: strconv.Itoa(model.CepLeft),
			DestinationCepEnd:     strconv.Itoa(model.CepRight),
		})
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTiles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

}

func (c2 *cdtManualManipulationController) ToggleManualManipulationStatus(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_manipulation_rule.ToggleManualManipulationRuleStatusRequest)
	response, lcosErr := c2.cdtManualManipulationService.ToggleCdtManualManipulationRuleStatus(c, request)
	return response, lcosErr
}

func (c2 *cdtManualManipulationController) ListProductLaneCode(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_manipulation_rule.ListProductLaneCodeReq)
	return c2.cdtManualManipulationService.ListProductLaneCodeInfo(ctx, request)
}

func (c2 *cdtManualManipulationController) ListLaneManualManipulationRules(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*manual_manipulation_rule.ListLaneManualManipulationRuleRequest)
	region := ctx.GetCountry()
	if req.IsRoute == constant.FALSE {
		switch req.RuleType {
		case constant.LOCATION:
			return c2.cdtManualManipulationService.ListLaneManualManipulationLocation(ctx, region, req.RecordId, req.ProductId, req.LaneCode)
		case constant.POSTCODE:
			return c2.cdtManualManipulationService.ListLaneManualManipulationPostCode(ctx, region, req.RecordId, req.ProductId, req.LaneCode)
		case constant.CEPRANGE:
			return c2.cdtManualManipulationService.ListLaneManualManipulationCepRange(ctx, region, req.RecordId, req.ProductId, req.LaneCode)
		default:
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "invalid rule type")
		}
	} else {
		switch req.RuleType {
		case constant.LOCATION:
			return c2.cdtManualManipulationService.ListLaneManualManipulationRouteLocation(ctx, region, req.RecordId, req.ProductId, req.LaneCode)
		case constant.POSTCODE:
			return c2.cdtManualManipulationService.ListLaneManualManipulationRoutePostCode(ctx, region, req.RecordId, req.ProductId, req.LaneCode)
		case constant.CEPRANGE:
			return c2.cdtManualManipulationService.ListLaneManualManipulationRouteCepRange(ctx, region, req.RecordId, req.ProductId, req.LaneCode)
		default:
			return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "invalid rule type")
		}
	}
}
