package forecast_task_result

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	edd_forecast_task_result_pb "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_forecast_result"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_forecast_result"
)

type AdminEDDForecastTaskResultController interface {
	ImportEDDForecastTaskResult(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DetailEDDForecastTaskResult(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportEDDForecastTaskResult(ctx *utils.HttpContext, rawRequest interface{})
	CalculateEDDBySDK(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	// SPLN-30795
	GetDeployInfo(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type eddForecastTaskResultController struct {
	eddForecastTaskResultService edd_forecast_result.EDDForecastTaskResultInterface
}

func (e *eddForecastTaskResultController) ImportEDDForecastTaskResult(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_forecast_task_result_pb.ImportEDDForecastTaskResultRequest)
	return nil, e.eddForecastTaskResultService.ImportEDDForecastTaskResult(ctx, request)
}

func (e *eddForecastTaskResultController) DetailEDDForecastTaskResult(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_forecast_task_result_pb.DetailEDDForecastTaskResultRequest)
	return e.eddForecastTaskResultService.DetailEDDForecastTaskResult(ctx, request)
}

func (e *eddForecastTaskResultController) ExportEDDForecastTaskResult(ctx *utils.HttpContext, rawRequest interface{}) {
	request, _ := rawRequest.(*edd_forecast_task_result_pb.ExportEDDForecastTaskResultRequest)
	resultFile, lcosErr := e.eddForecastTaskResultService.ExportEDDForecastTaskResult(ctx, request.ForecastTaskID, request.Region)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		http.GenErrorResponseWithParam(ctx.Context, lcos_error.DownloadFileErrorCode, nil, lcosErr.Msg)
		return
	}

	fileName := fmt.Sprintf("forecast_%d_result.xlsx", request.ForecastTaskID)
	ctx.Context.AddHeader("Content-Type", "application/octet-stream")
	ctx.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	ctx.Context.AddHeader("Content-Transfer-Encoding", "binary")

	if err := resultFile.Write(ctx.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(ctx, "export failed, err=%s", err.Error())
		http.GenErrorResponseWithParam(ctx.Context, lcos_error.DownloadFileErrorCode, nil, err.Error())
		return
	}
}

func (e *eddForecastTaskResultController) CalculateEDDBySDK(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_forecast_task_result_pb.CalculateEDDBySDKRequest)
	return e.eddForecastTaskResultService.CalculateEDDByDataSDK(ctx, request)
}

func (e *eddForecastTaskResultController) GetDeployInfo(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_forecast_task_result_pb.GetDeployInfoRequest)
	return e.eddForecastTaskResultService.GetDeployInfo(ctx, request.ForecastTaskID, request.Region)
}

func NewEDDForecastTaskResultController(eddForecastTaskResultService edd_forecast_result.EDDForecastTaskResultInterface) *eddForecastTaskResultController {
	return &eddForecastTaskResultController{
		eddForecastTaskResultService: eddForecastTaskResultService,
	}
}

var _ AdminEDDForecastTaskResultController = (*eddForecastTaskResultController)(nil)
