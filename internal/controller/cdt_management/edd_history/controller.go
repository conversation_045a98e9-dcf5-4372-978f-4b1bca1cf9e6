package edd_history

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	edd_history2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_history"
)

type AdminEDDHistoryController interface {
	ListEDDHistories(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type eddHistoryController struct {
	eddHistoryService edd_history.EDDHistoryInterface
}

func (e *eddHistoryController) ListEDDHistories(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_history2.ListEDDHistoryRequest)
	return e.eddHistoryService.ListEDDHistories(c, request)
}

func NewEDDHistoryController(eddHistoryService edd_history.EDDHistoryInterface) *eddHistoryController {
	return &eddHistoryController{
		eddHistoryService: eddHistoryService,
	}
}

var _ AdminEDDHistoryController = (*eddHistoryController)(nil)
