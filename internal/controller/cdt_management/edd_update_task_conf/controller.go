package edd_update_task_conf

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	edd_update_rule_conf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_update_task_conf"
	edd_update_task_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/saturnprovider"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_update_task_conf"
	jsoniter "github.com/json-iterator/go"
)

type AdminEDDUpdateTaskConfController interface {
	CreateEDDUpdateTaskConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	PreviewOrderInfo(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	TriggerEDDUpdateTask(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEDDUpdateTaskConfByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetEDDUpdateTaskConfByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteUpdateTaskConfByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	// SPLN-27978
	GetPreviewTaskRecord(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CancelPreviewTaskRecord(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type eddUpdateTaskConfController struct {
	eddUpdateTaskConfService edd_update_task_conf.EDDUpdateTaskConfInterface
}

func (e *eddUpdateTaskConfController) sendCalculationTask(ctx utils.LCOSContext, eddUpdateTaskConf *edd_update_task_conf2.EddUpdateTaskConfTab, requestID string) error {

	request := &schema.CalculationEventData{
		EDDTaskID:    eddUpdateTaskConf.ID,
		OrderFileUrl: eddUpdateTaskConf.OrderFileUrl,
		RequestID:    requestID,
		Operator:     ctx.GetUserEmail(),
	}

	data, _ := jsoniter.Marshal(request)
	// 任务名与接受的异步任务名相同
	producer, err := saturnprovider.GetSaturnProducer(task.GenerateCdtTaskDomain(ctx, ctx.GetCountry()))
	if err != nil {
		return err
	}
	return producer.SendMessage(ctx, 1, constant.EDDCalculationTask, data)
}

func (e *eddUpdateTaskConfController) sendPushingTask(ctx utils.LCOSContext, eddUpdateTaskConf *edd_update_task_conf2.EddUpdateTaskConfTab, requestID string) error {

	request := &schema.PushingEventData{
		EDDTaskID:      eddUpdateTaskConf.ID,
		EDDInfoListUrl: eddUpdateTaskConf.CalculationResult,
		RequestID:      requestID,
		EddTaskConf:    eddUpdateTaskConf,
		Operator:       ctx.GetUserEmail(),
	}

	data, _ := jsoniter.Marshal(request)
	// 任务名与接受的异步任务名相同
	producer, err := saturnprovider.GetSaturnProducer(task.GenerateCdtTaskDomain(ctx, ctx.GetCountry()))
	if err != nil {
		return err
	}
	return producer.SendMessage(ctx, 1, constant.EDDPushingTask, data)
}

func (e *eddUpdateTaskConfController) CreateEDDUpdateTaskConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	logID := c.GetRequestId()
	request, _ := rawRequest.(*edd_update_rule_conf.CreateEDDUpdateTaskConfRequest)
	eddUpdateConf, lcosErr := e.eddUpdateTaskConfService.CreateUpdateEDDTaskConf(c, request)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}
	err := e.sendCalculationTask(c, eddUpdateConf, logID)
	if err != nil {
		logger.CtxLogErrorf(c, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return nil, nil
}

func (e *eddUpdateTaskConfController) PreviewOrderInfo(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf.PreviewOrderInfo)
	return e.eddUpdateTaskConfService.PreviewOrderInfo(c, request)
}

func (e *eddUpdateTaskConfController) TriggerEDDUpdateTask(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	logID := c.GetRequestId()
	request, _ := rawRequest.(*edd_update_rule_conf.DetailEDDUpdateTaskConfIDRequest)

	// check whether edd could be found
	eddUpdateConf, lcosErr := e.eddUpdateTaskConfService.GetUpdateEDDTaskConfByID(c, &edd_update_rule_conf.DetailEDDUpdateTaskConfIDRequest{ID: request.ID})
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcosErr
	}

	// only calculation status is success will allow do update
	eddUpdateConfStruct, ok := eddUpdateConf.(*edd_update_task_conf2.EddUpdateTaskConfTab)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "edd update conf not valid")
	}

	// check status
	if !utils.ContainsUint8([]uint8{edd_constant.EDDCalculatingSuccess, edd_constant.EDDCalculatingPartialSuccess}, eddUpdateConfStruct.CalculationStatus) {
		errMsg := fmt.Sprintf("calculating status of edd update task conf is not all success or partial success|edd_task_id=%v", eddUpdateConfStruct.ID)
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}

	if eddUpdateConfStruct.PushingStatus != edd_constant.EDDDummy {
		errMsg := fmt.Sprintf("task has been pushed before|edd_task_id=%v", eddUpdateConfStruct.ID)
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, errMsg)
	}

	// check whether expired
	nowTime := utils.GetTimestamp(c.Ctx)
	if int64(nowTime)-eddUpdateConfStruct.CalculationEndTime >= int64(config.GetEDDOnGoingConfig(c.Ctx).MaxMinutesBetweenCalculatingAndPushing)*60 {

		// update task to expired
		if lcosErr = e.eddUpdateTaskConfService.UpdateEDDTaskConf(c, map[string]interface{}{"id": eddUpdateConfStruct.ID}, map[string]interface{}{"pushing_status": edd_constant.EDDPushingExpire}); lcosErr != nil {
			return nil, lcosErr
		}

		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("task expired|edd_task_id=%v", eddUpdateConfStruct.ID))
	}

	// send message
	err := e.sendPushingTask(c, eddUpdateConfStruct, logID)
	if err != nil {
		logger.CtxLogErrorf(c, err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}

	// update status into updating
	if lcosErr = e.eddUpdateTaskConfService.UpdateEDDTaskConf(c, map[string]interface{}{"id": eddUpdateConfStruct.ID}, map[string]interface{}{"pushing_status": edd_constant.EDDPushing}); lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, lcosErr.Msg)
	}
	return nil, nil
}

func (e *eddUpdateTaskConfController) ListEDDUpdateTaskConfByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf.ListEDDUpdateTaskConfRequest)
	return e.eddUpdateTaskConfService.ListUpdateEDDTaskConf(c, request)
}

func (e *eddUpdateTaskConfController) GetEDDUpdateTaskConfByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf.DetailEDDUpdateTaskConfIDRequest)
	return e.eddUpdateTaskConfService.GetUpdateEDDTaskConfByID(c, request)
}

func (e *eddUpdateTaskConfController) DeleteUpdateTaskConfByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf.DetailEDDUpdateTaskConfIDRequest)
	return e.eddUpdateTaskConfService.DeleteUpdateEDDTaskConfByID(c, request)
}

func (e *eddUpdateTaskConfController) GetPreviewTaskRecord(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf.GetPreviewTaskRequest)
	return e.eddUpdateTaskConfService.GetPreviewTaskRecord(c, request.ID)
}

func (e *eddUpdateTaskConfController) CancelPreviewTaskRecord(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf.GetPreviewTaskRequest)
	return e.eddUpdateTaskConfService.CancelPreviewTask(c, request.ID, c.GetUserEmail())
}

func NewEDDUpdateTaskConfController(eddUpdateTaskConfService edd_update_task_conf.EDDUpdateTaskConfInterface) *eddUpdateTaskConfController {
	return &eddUpdateTaskConfController{
		eddUpdateTaskConfService: eddUpdateTaskConfService,
	}
}

var _ AdminEDDUpdateTaskConfController = (*eddUpdateTaskConfController)(nil)
