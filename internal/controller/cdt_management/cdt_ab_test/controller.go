package cdt_ab_test

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	httpUtils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_ab_test_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_ab_test"
)

type CdtAbTestController interface {
	CreateCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError)
	ListCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError)
	EnableCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError)
	DeployCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError)
	GetAbTestAvailableRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewCdtAbTestController(svc cdt_ab_test.CdtAbTestService) *cdtAbTestController {
	return &cdtAbTestController{
		svc: svc,
	}
}

type cdtAbTestController struct {
	svc cdt_ab_test.CdtAbTestService
}

func (c *cdtAbTestController) CreateCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawReqeust.(*cdt_ab_test_protocol.CreateCdtAbTestRuleRequest)
	if err := c.svc.CreateCdtAbTestRule(ctx, req.RuleName, req.ProductId, req.ObjectType, req.TestGroupList); err != nil {
		return nil, err
	}
	return nil, nil
}

func (c *cdtAbTestController) UpdateCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawReqeust.(*cdt_ab_test_protocol.UpdateCdtAbTestRuleRequest)
	if err := c.svc.UpdateCdtAbTestRule(ctx, req.RuleId, req.RuleName, req.ProductId, req.TestGroupList); err != nil {
		return nil, err
	}
	return nil, nil
}

func (c *cdtAbTestController) DeleteCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawReqeust.(*cdt_ab_test_protocol.DeleteCdtAbTestRuleRequest)
	if err := c.svc.DeleteCdtAbTestRule(ctx, req.RuleId); err != nil {
		return nil, err
	}
	return nil, nil
}

func (c *cdtAbTestController) ListCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawReqeust.(*cdt_ab_test_protocol.ListCdtAbTestRuleRequest)
	if req.PageNo == 0 {
		req.PageNo = constant.DefaultPageNo
	}
	if req.PageSize == 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.DefaultMaxPageSize {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "page size should not be larger than %d", constant.DefaultMaxPageSize)
	}

	ret, total, err := c.svc.ListCdtAbTestRuleByParamsWithPaging(ctx, req.RuleId, req.ProductId, req.PageNo, req.PageSize)
	if err != nil {
		return nil, err
	}
	return httpUtils.GeneratePagingQueryData(ret, total, req.PageNo, uint32(len(ret))), nil
}

func (c *cdtAbTestController) EnableCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawReqeust.(*cdt_ab_test_protocol.EnableCdtAbTestRuleRequest)
	if err := c.svc.EnableCdtAbTestRule(ctx, req.RuleId); err != nil {
		return nil, err
	}
	return nil, nil
}

func (c *cdtAbTestController) DeployCdtAbTestRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawReqeust.(*cdt_ab_test_protocol.DeployCdtAbTestRuleRequest)
	if err := c.svc.DeployCdtAbTestRule(ctx, req.RuleId, req.GroupTag); err != nil {
		return nil, err
	}
	return nil, nil
}

func (c *cdtAbTestController) GetAbTestAvailableRule(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawReqeust.(*cdt_ab_test_protocol.GetAbTestAvailableRuleRequest)

	ret := make([]*cdt_ab_test_protocol.GetAbTestAvailableRuleResponse, 0, config.GetCdtAbTestGroupNumLimitMax())
	for i := 0; i < config.GetCdtAbTestGroupNumLimitMax(); i++ {
		groupTag := uint8(i)
		autoUpdateRuleList, manualUpdateAvailable, err := c.svc.GetCdtAbTestAvailableRule(ctx, req.ProductId, groupTag, req.ObjectType)
		if err != nil {
			return nil, err
		}
		ret = append(ret, &cdt_ab_test_protocol.GetAbTestAvailableRuleResponse{
			AutoUpdateRuleList:    autoUpdateRuleList,
			ManualUpdateAvailable: manualUpdateAvailable,
		})
	}
	return ret, nil
}
