package cdt_overview

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	cdt_overview2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_overview"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_overview"
)

type AdminCdtRuleOverviewController interface {
	ListRecordByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type cdtRuleOverviewController struct {
	cdtRuleOverviewService cdt_overview.CdtRuleOverviewServiceInterface
}

func NewCdtRuleOverviewController(cdtRuleOverviewService cdt_overview.CdtRuleOverviewServiceInterface) *cdtRuleOverviewController {
	return &cdtRuleOverviewController{
		cdtRuleOverviewService: cdtRuleOverviewService,
	}
}

var _ AdminCdtRuleOverviewController = (*cdtRuleOverviewController)(nil)

func (c2 *cdtRuleOverviewController) ListRecordByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*cdt_overview2.ListRecordRequest)
	return c2.cdtRuleOverviewService.ListRecordByParams(c, request)
}
