package edd_update_rule_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	edd_update_rule_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_update_rule_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_update_rule_conf"
)

type AdminEDDUpdateRuleConfController interface {
	CreateEDDUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEDDUpdateRuleConfByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetEDDUpdateRuleConfByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateEDDUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteEDDUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type eddUpdateRuleConfController struct {
	eddUpdateRuleConfService edd_update_rule_conf.EDDUpdateRuleConfInterface
}

func (e *eddUpdateRuleConfController) GetEDDUpdateRuleConfByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf2.GetEDDUpdateRuleConfRequest)
	return e.eddUpdateRuleConfService.GetUpdateEDDConfigByID(c, request)
}

func (e *eddUpdateRuleConfController) CreateEDDUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf2.CreateEDDUpdateRuleConfRequest)
	return nil, e.eddUpdateRuleConfService.CreateUpdateEDDConfig(c, request)
}

func (e *eddUpdateRuleConfController) ListEDDUpdateRuleConfByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf2.ListEDDUpdateRuleConfRequest)
	return e.eddUpdateRuleConfService.ListUpdateEDDConfig(c, request)
}

func (e *eddUpdateRuleConfController) UpdateEDDUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf2.UpdateEDDUpdateRuleConfRequest)
	return nil, e.eddUpdateRuleConfService.UpdateUpdateEDDConfig(c, request)
}

func (e *eddUpdateRuleConfController) DeleteEDDUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_update_rule_conf2.DeleteEDDUpdateRuleConfRequest)
	return nil, e.eddUpdateRuleConfService.DeleteUpdateEDDConfig(c, request)
}

func NewEDDUpdateRuleConfController(eddUpdateRuleConfService edd_update_rule_conf.EDDUpdateRuleConfInterface) *eddUpdateRuleConfController {
	return &eddUpdateRuleConfController{
		eddUpdateRuleConfService: eddUpdateRuleConfService,
	}
}

var _ AdminEDDUpdateRuleConfController = (*eddUpdateRuleConfController)(nil)
