package edd_model_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	algo_model_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/algo_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/algo_model_conf"
)

type AdminEDDModelConfController interface {
	ListEDDModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEDDSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEDDRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetDetailEDDModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeployEDDSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	RevokeEDDSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeployEDDRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	RevokeEDDRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ToggleEDDModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	QueryAllMetaData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeployAllMetaData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ReplaceRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ReplaceRouteRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ClearRouteRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListMetaDataSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListRouteMetaDataSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetMetaDataSimulationDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type EDDModelConfController struct {
	algoModelConfService algo_model_conf.AlgoModelConfServiceInterface
}

func (e *EDDModelConfController) ListEDDModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoModelConfRequest)
	return e.algoModelConfService.ListAlgoModelConf(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) ListEDDSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoSimulationRequest)
	return e.algoModelConfService.ListAlgoSimulation(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) ListEDDRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoSimulationRequest)
	return e.algoModelConfService.ListRouteAlgoSimulation(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) GetDetailEDDModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.GetDetailAlgoModelConfRequest)
	return e.algoModelConfService.GetDetailAlgoModelConf(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) DeployEDDSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.DeployAlgoSimulationRequest)
	return nil, e.algoModelConfService.DeployAlgoSimulation(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) RevokeEDDSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.RevokeAlgoSimulationRequest)
	return nil, e.algoModelConfService.RevokeAlgoSimulation(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) DeployEDDRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.DeployAlgoSimulationRequest)
	return nil, e.algoModelConfService.DeployRouteAlgoSimulation(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) RevokeEDDRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.RevokeAlgoSimulationRequest)
	return nil, e.algoModelConfService.RevokeRouteAlgoSimulation(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) ToggleEDDModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ToggleAlgoModelConfRequest)
	return nil, e.algoModelConfService.ToggleAlgoModelConf(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) QueryAllMetaData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	return e.algoModelConfService.QueryAllMetadata(c, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) DeployAllMetaData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.DeployAllMetaDataRequest)
	return nil, e.algoModelConfService.DeployAllMetadata(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) ListRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListRecommendMetaDataRequest)
	return e.algoModelConfService.ListAllRecommendMetadata(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) ReplaceRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ReplaceRecommendMetaDataRequest)
	return nil, e.algoModelConfService.ReplaceRecommendMetadata(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) ReplaceRouteRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ReplaceRouteRecommendMetaDataRequest)
	return nil, e.algoModelConfService.ReplaceRouteRecommendMetadata(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) ClearRouteRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ClearRecommendMetaDataRequest)
	return nil, e.algoModelConfService.ClearRouteRecommendMetadata(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) ListMetaDataSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoSimulationRequest)
	return e.algoModelConfService.ListAlgoSimulation(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) ListRouteMetaDataSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoSimulationRequest)
	return e.algoModelConfService.ListRouteAlgoSimulation(c, request, edd_constant.EDDModelType)
}

func (e *EDDModelConfController) GetMetaDataSimulationDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.GetDetailAlgoModelConfRequest)
	return e.algoModelConfService.GetDetailMetaData(c, request, edd_constant.EDDModelType)
}

func NewEDDModelConfController(algoModelConfService algo_model_conf.AlgoModelConfServiceInterface) *EDDModelConfController {
	return &EDDModelConfController{
		algoModelConfService: algoModelConfService,
	}
}

var _ AdminEDDModelConfController = (*EDDModelConfController)(nil)
