package automated_volume

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	automated_volume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/automated_volume"
)

type AdminAutomatedVolumeRuleController interface {
	CreateAutomatedVolumeRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListAutomatedVolumeRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DetailAutomatedVolumeRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ParseAndImportAutomatedVolumeRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type automatedVolumeRuleController struct {
	automatedVolumeService automated_volume.AutomatedVolumeInterface
}

func NewAutomatedVolumeRuleController(automatedVolumeService automated_volume.AutomatedVolumeInterface) *automatedVolumeRuleController {
	return &automatedVolumeRuleController{
		automatedVolumeService: automatedVolumeService,
	}
}

var _ AdminAutomatedVolumeRuleController = (*automatedVolumeRuleController)(nil)

func (a *automatedVolumeRuleController) CreateAutomatedVolumeRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*automated_volume2.CreateAutomatedVolumeRuleRequest)
	return nil, a.automatedVolumeService.CreateAutomatedVolume(c, request)
}

func (a *automatedVolumeRuleController) ListAutomatedVolumeRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*automated_volume2.ListAutomatedVolumeRuleRequest)
	return a.automatedVolumeService.ListAutomatedVolume(c, request)
}

func (a *automatedVolumeRuleController) DetailAutomatedVolumeRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*automated_volume2.DetailAutomatedVolumeRuleRequest)
	return a.automatedVolumeService.GetAutomatedVolumeById(c, request)
}

func (a *automatedVolumeRuleController) ParseAndImportAutomatedVolumeRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*automated_volume2.ImportAutomatedVolumeRuleRequest)
	// 异步解析任务
	go a.automatedVolumeService.ParseDataFileAndImportData(c, request)
	return nil, nil
}
