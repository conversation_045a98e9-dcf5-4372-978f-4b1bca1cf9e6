package edd_auto_update_rule

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	edd_auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_auto_update_rule"
)

type AdminEDDAutoUpdateRuleController interface {
	CreateEDDAutoUpdateRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEDDAutoUpdateRulesByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetEDDAutoUpdateRuleByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateEDDAutoUpdateRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteEDDAutoUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ToggleEDDAutoUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	// SPLN-29072 add check can create api
	CheckCanCreateEDDAutoUpdateRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type eddAutoUpdateRuleConfController struct {
	eddAutoUpdateRuleConfService edd_auto_update_rule.EDDAutoUpdateRuleInterface
}

func (e *eddAutoUpdateRuleConfController) CreateEDDAutoUpdateRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest)
	return nil, e.eddAutoUpdateRuleConfService.CreateEDDAutoUpdateRule(c, request)
}

func (e *eddAutoUpdateRuleConfController) ListEDDAutoUpdateRulesByParams(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_auto_update_rule2.ListEDDAutoUpdateRuleRequest)
	return e.eddAutoUpdateRuleConfService.ListEDDAutoUpdateRules(c, request)
}

func (e *eddAutoUpdateRuleConfController) GetEDDAutoUpdateRuleByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_auto_update_rule2.GetEDDAutoUpdateRuleRequest)
	return e.eddAutoUpdateRuleConfService.GetEDDAutoUpdateRuleByID(c, request)
}

func (e *eddAutoUpdateRuleConfController) UpdateEDDAutoUpdateRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_auto_update_rule2.UpdateEDDAutoUpdateRuleRequest)
	return nil, e.eddAutoUpdateRuleConfService.UpdateEDDAutoUpdateRule(c, request)
}

func (e *eddAutoUpdateRuleConfController) DeleteEDDAutoUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_auto_update_rule2.GetEDDAutoUpdateRuleRequest)
	return nil, e.eddAutoUpdateRuleConfService.DeleteEDDAutoUpdateRule(c, request)
}

func (e *eddAutoUpdateRuleConfController) ToggleEDDAutoUpdateRuleConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_auto_update_rule2.ToggleEDDAutoUpdateStatusRequest)
	return nil, e.eddAutoUpdateRuleConfService.ToggleEDDAutoUpdateRuleStatus(c, request)
}

func (e *eddAutoUpdateRuleConfController) CheckCanCreateEDDAutoUpdateRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edd_auto_update_rule2.CreateEDDAutoUpdateRuleRequest)
	return nil, e.eddAutoUpdateRuleConfService.CheckCanCreateEDDAutoUpdateRule(c, request)
}

func NewEDDAutoUpdateRuleConfController(eddAutoUpdateRuleConfService edd_auto_update_rule.EDDAutoUpdateRuleInterface) *eddAutoUpdateRuleConfController {
	return &eddAutoUpdateRuleConfController{
		eddAutoUpdateRuleConfService: eddAutoUpdateRuleConfService,
	}
}

var _ AdminEDDAutoUpdateRuleController = (*eddAutoUpdateRuleConfController)(nil)
