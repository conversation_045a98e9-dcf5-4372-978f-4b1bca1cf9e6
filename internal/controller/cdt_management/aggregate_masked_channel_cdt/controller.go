package aggregate_masked_channel_cdt

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	aggregate_masked_channnel_cdt2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/aggregate_masked_channel_cdt"
)

type AdminAggregateMaskedChannelCdtController interface {
	CreateAggregateMaskedChannelCdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListAggregateMaskedChannelCdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DetailAggregateMaskedChannelCdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DisableAggregateMaskedChannelCdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type aggregateMaskedChannelCdtController struct {
	aggregateMaskedChannelCdtService aggregate_masked_channel_cdt.AggregateMaskedChannelCdtInterface
}

func NewAdminAggregateMaskedChannelCdtController(aggregateMaskedChannelCdtService aggregate_masked_channel_cdt.AggregateMaskedChannelCdtInterface) *aggregateMaskedChannelCdtController {
	return &aggregateMaskedChannelCdtController{
		aggregateMaskedChannelCdtService: aggregateMaskedChannelCdtService,
	}
}

var _ AdminAggregateMaskedChannelCdtController = (*aggregateMaskedChannelCdtController)(nil)

func (e *aggregateMaskedChannelCdtController) CreateAggregateMaskedChannelCdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*aggregate_masked_channnel_cdt2.CreateAggregateMaskedChannelRequest)
	return nil, e.aggregateMaskedChannelCdtService.CreateAggregateMaskedChannelCdt(c, request)
}

func (e *aggregateMaskedChannelCdtController) ListAggregateMaskedChannelCdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*aggregate_masked_channnel_cdt2.ListAggregateMaskedChannelRequest)
	return e.aggregateMaskedChannelCdtService.ListAggregateMaskedChannelCdt(c, request)
}

func (e *aggregateMaskedChannelCdtController) DetailAggregateMaskedChannelCdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*aggregate_masked_channnel_cdt2.RuleIdAggregateMaskedChannelRequest)
	return e.aggregateMaskedChannelCdtService.GetAggregateMaskedChannelCdtById(c, request)
}

func (e *aggregateMaskedChannelCdtController) DisableAggregateMaskedChannelCdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*aggregate_masked_channnel_cdt2.RuleIdAggregateMaskedChannelRequest)
	return nil, e.aggregateMaskedChannelCdtService.DisableAggregateMaskedChannelCdt(c, request)
}
