package edt_model_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	algo_model_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/algo_model_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/algo_model_conf"
)

type AdminEDTModelConfController interface {
	ListEDTModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEDTSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEDTRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetDetailEDTModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeployEDTSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	RevokeEDTSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeployEDTRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	RevokeEDTRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ToggleEDTModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	QueryAllMetaData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeployAllMetaData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ReplaceRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ReplaceRouteRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ClearRouteRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListMetaDataSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListRouteMetaDataSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetMetaDataSimulationDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type EDTModelConfController struct {
	algoModelConfService algo_model_conf.AlgoModelConfServiceInterface
}

func (e *EDTModelConfController) ListEDTModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoModelConfRequest)
	return e.algoModelConfService.ListAlgoModelConf(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) ListEDTSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoSimulationRequest)
	return e.algoModelConfService.ListAlgoSimulation(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) ListEDTRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoSimulationRequest)
	return e.algoModelConfService.ListRouteAlgoSimulation(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) GetDetailEDTModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.GetDetailAlgoModelConfRequest)
	return e.algoModelConfService.GetDetailAlgoModelConf(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) DeployEDTSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.DeployAlgoSimulationRequest)
	return nil, e.algoModelConfService.DeployAlgoSimulation(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) RevokeEDTSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.RevokeAlgoSimulationRequest)
	return nil, e.algoModelConfService.RevokeAlgoSimulation(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) DeployEDTRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.DeployAlgoSimulationRequest)
	return nil, e.algoModelConfService.DeployRouteAlgoSimulation(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) RevokeEDTRouteSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.RevokeAlgoSimulationRequest)
	return nil, e.algoModelConfService.RevokeRouteAlgoSimulation(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) ToggleEDTModelConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ToggleAlgoModelConfRequest)
	return nil, e.algoModelConfService.ToggleAlgoModelConf(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) QueryAllMetaData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	return e.algoModelConfService.QueryAllMetadata(c, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) DeployAllMetaData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.DeployAllMetaDataRequest)
	return nil, e.algoModelConfService.DeployAllMetadata(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) ListRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListRecommendMetaDataRequest)
	return e.algoModelConfService.ListAllRecommendMetadata(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) ReplaceRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ReplaceRecommendMetaDataRequest)
	return nil, e.algoModelConfService.ReplaceRecommendMetadata(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) ReplaceRouteRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ReplaceRouteRecommendMetaDataRequest)
	return nil, e.algoModelConfService.ReplaceRouteRecommendMetadata(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) ClearRouteRecommendMeta(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ClearRecommendMetaDataRequest)
	return nil, e.algoModelConfService.ClearRouteRecommendMetadata(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) ListMetaDataSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoSimulationRequest)
	return e.algoModelConfService.ListAlgoSimulation(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) ListRouteMetaDataSimulation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.ListAlgoSimulationRequest)
	return e.algoModelConfService.ListRouteAlgoSimulation(c, request, edd_constant.EDTModelType)
}

func (e *EDTModelConfController) GetMetaDataSimulationDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*algo_model_conf2.GetDetailAlgoModelConfRequest)
	return e.algoModelConfService.GetDetailMetaData(c, request, edd_constant.EDTModelType)
}

func NewEDTModelConfController(algoModelConfService algo_model_conf.AlgoModelConfServiceInterface) *EDTModelConfController {
	return &EDTModelConfController{
		algoModelConfService: algoModelConfService,
	}
}

var _ AdminEDTModelConfController = (*EDTModelConfController)(nil)
