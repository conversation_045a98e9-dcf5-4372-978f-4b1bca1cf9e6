package manual_update_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	manual_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_update_rule"
	"github.com/tealeg/xlsx"
)

type AdminCdtManualUpdateRuleController interface {
	// TODO SPLN-24104: 导入添加single产品多段式lane code校验
	// SPLN-24104: 支持导入lane手动cdt数据
	ImportCdtManualUpdateData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// SPLN-24104: 支持导出lane手动cdt数据
	ExportCdtManualUpdateData(c *utils.HttpContext, rawRequest interface{})
	ListAllCdtProducts(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListAllCdtRecordsPaging(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SyncCdtManualLocationDataToRedis(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// SPLN-24104: 支持lane维度cdt数据
	CheckManualCdteExistsByProduct(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// SPLN-24104: 支持删除lane手动cdt数据
	DeleteCdtByProduct(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListLaneManualUpdateData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type cdtManualUpdateController struct {
	cdtManualUpdateService manual_update_rule.ManualUpdateServiceInterface
}

func NewCdtManualUpdateController(cdtManualUpdateService manual_update_rule.ManualUpdateServiceInterface) *cdtManualUpdateController {
	return &cdtManualUpdateController{
		cdtManualUpdateService: cdtManualUpdateService,
	}
}

var _ AdminCdtManualUpdateRuleController = (*cdtManualUpdateController)(nil)

func (c2 *cdtManualUpdateController) ImportCdtManualUpdateData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_update_rule2.ImportCdtDataRequest)
	batchID, lcosErr := c2.cdtManualUpdateService.ImportCdtManualUpdateData(c, request)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return map[string]interface{}{"batch_id": batchID}, lcosErr
}

func (c2 *cdtManualUpdateController) SyncCdtManualLocationDataToRedis(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	logger.LogInfof("SyncCdtManualLocationDataToRedis start")
	lcosErr := c2.cdtManualUpdateService.SyncCdtManualLocationDataToRedis(c)
	logger.LogInfof("SyncCdtManualLocationDataToRedis end")
	if lcosErr != nil {
		return nil, lcosErr
	}
	return nil, lcosErr
}

func (c2 *cdtManualUpdateController) ExportCdtManualUpdateData(c *utils.HttpContext, rawRequest interface{}) {
	region := c.GetCountry()
	request, _ := rawRequest.(*manual_update_rule2.ExportCdtDataRequest)
	exportData, lcosErr := c2.cdtManualUpdateService.GetDataForExport(c, request, region)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lcosErr.Msg)
		return
	}

	// 将数据按照 CdtExtraData 中的 DayGroup 和 TimeBucket 展开成多行
	splitExportData, lcosErr := splitExportData(c, exportData, request.ObjectType)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, lcosErr.Msg)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lcosErr.Msg)
		return
	}

	var titles []string
	if request.ObjectType == edd_constant.CdtObject {
		titles = CdtExportHeader
	} else {
		titles = LeadTimeExportHeader
	}
	var data []interface{}
	for _, singleData := range splitExportData {
		data = append(data, singleData)
	}

	// 导出文件
	fileName := fmt.Sprintf("%s-Export-%s-v%d.xlsx", edd_constant.ObjectTypeMap[request.ObjectType], request.ProductID, recorder.Now(c).UnixNano()/1e6)
	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")
	file := xlsx.NewFile()

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, titles, data); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (c2 *cdtManualUpdateController) ListAllCdtProducts(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_update_rule2.ListAllProductList)
	return c2.cdtManualUpdateService.ListCdtProductList(c, request)
}

func (c2 *cdtManualUpdateController) ListAllCdtRecordsPaging(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_update_rule2.ListManualUpdateRecordsPaging)
	response, lcosErr := c2.cdtManualUpdateService.ListCdtRecordPaging(c, request)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return response, nil
}

func (c2 *cdtManualUpdateController) CheckManualCdteExistsByProduct(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_update_rule2.MoudleCheckOfCdtByProductRequest)
	return c2.cdtManualUpdateService.CheckManualCdtExistsByProduct(c, request)
}

func (c2 *cdtManualUpdateController) DeleteCdtByProduct(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*manual_update_rule2.MoudleCheckOfCdtByProductRequest)
	return c2.cdtManualUpdateService.DeleteManualCdtByProduct(c, request)
}

func (c2 *cdtManualUpdateController) ListLaneManualUpdateData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*manual_update_rule2.ListLaneManualUpdateDataRequest)
	region := c.GetCountry()
	switch req.RuleType {
	case constant.LOCATION:
		return c2.cdtManualUpdateService.ListLaneManualUpdateLocationData(c, region, req.ProductId, req.IsSiteLine)
	case constant.POSTCODE:
		return c2.cdtManualUpdateService.ListLaneManualUpdatePostCodeData(c, region, req.ProductId, req.IsSiteLine)
	case constant.CEPRANGE:
		return c2.cdtManualUpdateService.ListLaneManualUpdateCepRangeData(c, region, req.ProductId, req.IsSiteLine)
	default:
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "invalid rule type")
	}
}
