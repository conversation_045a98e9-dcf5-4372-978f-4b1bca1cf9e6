package manual_update_rule

var CdtExportHeader = []string{
	"Type",
	"Location_level",
	"Origin_country",
	"Origin_state",
	"Origin_city",
	"Origin_district",
	"Destination_country",
	"Destination_state",
	"Destination_city",
	"Destination_district",
	"Destination_postcode",
	"Destination_CEP_initial",
	"Destination_CEP_final",
	"Product_id",
	"CDT_Event",
	"day_of_week_group",
	"hour_of_event_time",
	"EDT_CDT_min",
	"EDT_CDT_max",
}

var LeadTimeExportHeader = []string{
	"Type",
	"Location_level",
	"Origin_country",
	"Origin_state",
	"Origin_city",
	"Origin_district",
	"Destination_country",
	"Destination_state",
	"Destination_city",
	"Destination_district",
	"Destination_postcode",
	"Destination_CEP_initial",
	"Destination_CEP_final",
	"Product_id",
	"Lane_id",
	"Update_event",
	"day_of_week_group",
	"hour_of_event_time",
	"EDD_Lead_Time_min",
	"EDD_Lead_Time_max",
}
