package manual_update_rule

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/edd_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_update_rule"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

func splitExportData(ctx utils.LCOSContext, mergeList []*manual_update_rule.CdtData, objectType uint8) ([]*manual_update_rule.SplitCdtData, *lcos_error.LCOSError) {
	splitList := make([]*manual_update_rule.SplitCdtData, 0)

	appendSplitData := func(originData *manual_update_rule.CdtData, day, hour, min, max string) {
		// 空或者"0"表示没有数据，不需要导出
		if (min == "" || min == "0") && (max == "" || max == "0") {
			return
		}

		splitData := &manual_update_rule.SplitCdtData{
			CdtType:               originData.CdtType,
			Level:                 originData.Level,
			OriginCountry:         originData.OriginCountry,
			OriginState:           originData.OriginState,
			OriginCity:            originData.OriginCity,
			OriginDistrict:        originData.OriginDistrict,
			DestinationCountry:    originData.DestinationCountry,
			DestinationState:      originData.DestinationState,
			DestinationCity:       originData.DestinationCity,
			DestinationDistrict:   originData.DestinationDistrict,
			DestinationCEPInitial: originData.DestinationCEPInitial,
			DestinationCEPFinal:   originData.DestinationCEPFinal,
			DestinationPostcode:   originData.DestinationPostcode,
			ProductId:             originData.ProductId,
			LaneCode:              originData.LaneCode,
			CDTEvent:              originData.CDTEvent,
			UpdateEvent:           originData.UpdateEvent,
			DayOfWeekGroup:        day,
			HourOfEventTime:       hour,
			CdtMin:                min,
			CdtMax:                max,
			LeadTimeMin:           min,
			LeadTimeMax:           max,
		}
		splitList = append(splitList, splitData)
	}

	for lineNum, mergeData := range mergeList {
		// 处理最外层
		if objectType == edd_constant.CdtObject {
			appendSplitData(mergeData, "", "", mergeData.CdtMin, mergeData.CdtMax)
		} else {
			appendSplitData(mergeData, "", "", mergeData.LeadTimeMin, mergeData.LeadTimeMax)
		}

		if len(mergeData.CdtExtraData) == 0 {
			continue
		}

		// 反序列化 extra data
		var extraData common_utils.CdtExtraData
		err := jsoniter.UnmarshalFromString(mergeData.CdtExtraData, &extraData)
		if err != nil {
			logger.CtxLogErrorf(ctx, "row:%d unmarshal error: %s", lineNum, err.Error())
			continue
		}

		// 处理day层
		for _, dayGroup := range extraData.DayGroups {
			dayMin := strconv.FormatFloat(dayGroup.LeadTimeMin, 'f', -1, 64)
			dayMax := strconv.FormatFloat(dayGroup.LeadTimeMax, 'f', -1, 64)
			appendSplitData(mergeData, dayGroup.GetDayName(), "", dayMin, dayMax)

			// 对每个day处理time层
			for _, time := range dayGroup.TimeBuckets {
				timeMin := strconv.FormatFloat(time.LeadTimeMin, 'f', -1, 64)
				timeMax := strconv.FormatFloat(time.LeadTimeMax, 'f', -1, 64)
				appendSplitData(mergeData, dayGroup.GetDayName(), time.GetTimeName(), timeMin, timeMax)
			}
		}
	}

	return splitList, nil
}
