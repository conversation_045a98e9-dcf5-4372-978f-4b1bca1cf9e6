package data_version

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/data_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/data_version"
)

type DataVersionController interface {
	ListDataVersionLog(ctx *utils.HttpContext, req interface{}) (interface{}, *lcos_error.LCOSError)

	IncrDataVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewDataVersionController(svc data_version.DataVersionService) *dataVersionController {
	return &dataVersionController{
		svc: svc,
	}
}

type dataVersionController struct {
	svc data_version.DataVersionService
}

func (d *dataVersionController) ListDataVersionLog(ctx *utils.HttpContext, req interface{}) (interface{}, *lcos_error.LCOSError) {
	cvtReq := validatedGetDataVersionReq(req)
	data, total, err := d.svc.QueryDataVersionLogList(ctx, cvtReq)
	if err != nil {
		return nil, err
	}
	res := map[string]interface{}{
		"list":      data,
		"page_no":   cvtReq.PageNo,
		"page_size": cvtReq.PageSize,
		"total":     total,
	}
	return res, nil
}

func validatedGetDataVersionReq(req interface{}) *protocol.ListDataVersionLogRequest {
	cvtReq, _ := req.(*protocol.ListDataVersionLogRequest)
	if cvtReq.DataName == nil {
		cvtReq.DataName = utils.NewString("")
	}
	if cvtReq.StartTime == nil {
		cvtReq.StartTime = utils.NewUint32(0)
	}
	if cvtReq.EndTime == nil {
		cvtReq.EndTime = utils.NewUint32(0)
	}
	if cvtReq.PageNo <= 0 {
		cvtReq.PageNo = 1
	}
	if cvtReq.PageSize <= 0 {
		cvtReq.PageSize = 10
	}
	return cvtReq
}

func (d *dataVersionController) IncrDataVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.IncrDataVersionLogRequest)

	newVersion, err := d.svc.IncrDataVersion(ctx, req.Region, req.DataName)
	if err != nil {
		return nil, err
	}
	return newVersion, nil
}
