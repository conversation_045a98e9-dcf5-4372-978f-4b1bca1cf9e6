package order_account_mapping

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/spx_serviceable_area_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/order_account_mapping"
)

type OrderAccountMappingController interface {
	CreateOrderAccountMapping(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOrderAccountMapping(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOrderAccountMapping(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListOrderAccountMapping(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type orderAccountMappingController struct {
	service order_account_mapping.OrderAccountMappingService
}

func NewOrderAccountMappingController(service order_account_mapping.OrderAccountMappingService) *orderAccountMappingController {
	return &orderAccountMappingController{
		service: service,
	}
}

func (o *orderAccountMappingController) CreateOrderAccountMapping(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.CreateOrderAccountMappingRequest)
	if err := o.service.CreateOrderAccountMapping(ctx, ctx.GetCountry(), req); err != nil {
		return nil, err
	}
	return nil, nil
}

func (o *orderAccountMappingController) UpdateOrderAccountMapping(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.UpdateOrderAccountMappingReqeust)

	if err := o.service.UpdateOrderAccountMapping(ctx, ctx.GetCountry(), req); err != nil {
		return nil, err
	}
	return nil, nil
}

func (o *orderAccountMappingController) DeleteOrderAccountMapping(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.DeleteOrderAccountMappingRequest)
	if err := o.service.DeleteOrderAccountMapping(ctx, req.GetMappingId()); err != nil {
		return nil, err
	}
	return nil, nil
}

func (o *orderAccountMappingController) ListOrderAccountMapping(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.ListOrderAccountMappingRequest)

	if req.PageNo == 0 {
		req.PageNo = constant.DefaultPageNo
	}
	if req.PageSize == 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > config.GetQueryMaxPageSize(ctx) {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "page size exceeds max limit %d", config.GetQueryMaxPageSize(ctx))
	}

	list, total, err := o.service.ListOrderAccountMappingByPaging(ctx, ctx.GetCountry(), req)
	if err != nil {
		return nil, err
	}
	return http.GeneratePagingQueryData(list, total, req.PageNo, req.PageSize), nil
}
