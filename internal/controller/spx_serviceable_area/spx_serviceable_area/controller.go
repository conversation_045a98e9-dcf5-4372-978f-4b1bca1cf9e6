package spx_serviceable_area

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/spx_serviceable_area_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area_compare"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spx_service"
)

type SpxServiceableAreaController interface {
	// SPX基础配置信息查询
	ListAllOrderAccount(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetLocationType(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// SPX服务范围版本通知
	CreateSpxServiceableAreaVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	EnableSpxServiceableAreaVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CancelSpxServiceableAreaVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// SPX服务范围对比
	ListSpxServiceableAreaCompareTask(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	TriggerSpxServiceableAreaCompare(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CancelSpxServiceableAreaCompare(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError)
	// debug接口，可以由dev手动触发
	PullSpxServiceableAreaData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ClearExpiredSpxServiceableArea(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateSpxServiceableArea(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type spxServiceableAreaController struct {
	spxSAService       spx_serviceable_area.SpxServiceableAreaService
	compareTaskService spx_serviceable_area_compare.SpxServiceableAreaCompareService
}

func NewSpxServiceableAreaController(spxSAService spx_serviceable_area.SpxServiceableAreaService, compareTaskService spx_serviceable_area_compare.SpxServiceableAreaCompareService) *spxServiceableAreaController {
	return &spxServiceableAreaController{
		spxSAService:       spxSAService,
		compareTaskService: compareTaskService,
	}
}

func (s *spxServiceableAreaController) ListAllOrderAccount(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	orderAccountList, err := s.spxSAService.ListAllOrderAccount(ctx, ctx.GetCountry())
	if err != nil {
		return nil, err
	}
	return &protocol.ListAllOrderAccountResponse{
		OrderAccountList: orderAccountList,
	}, nil
}

func (s *spxServiceableAreaController) GetLocationType(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	locationType, err := s.spxSAService.GetLocationType(ctx, ctx.GetCountry())
	if err != nil {
		return nil, err
	}
	return &protocol.GetLocationTypeResponse{
		LocationType: locationType,
	}, nil
}

func (s *spxServiceableAreaController) CreateSpxServiceableAreaVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*spx_service.ServiceableAreaScheduledPlanning)
	if err := s.spxSAService.CreateSpxServiceableAreaVersion(ctx, req.Region, req); err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *spxServiceableAreaController) EnableSpxServiceableAreaVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*spx_service.ServiceableAreaScheduledPlanning)
	if err := s.spxSAService.EnableSpxServiceableAreaVersion(ctx, req.Region, req); err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *spxServiceableAreaController) CancelSpxServiceableAreaVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*spx_service.ServiceableAreaScheduledPlanning)
	if err := s.spxSAService.CancelSpxServiceableAreaVersion(ctx, req.Region, req.PlanningId); err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *spxServiceableAreaController) ListSpxServiceableAreaCompareTask(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.ListSpxServiceableAreaCompareTaskRequest)
	if req.GetPageNo() == 0 {
		req.PageNo = constant.DefaultPageNo
	}
	if req.GetPageSize() == 0 {
		req.PageSize = constant.DefaultPageSize
	}

	ret, total, err := s.compareTaskService.ListSpxServiceableAreaCompareTaskWithPaging(ctx, ctx.GetCountry(), req)
	if err != nil {
		return nil, err
	}
	return http.GeneratePagingQueryData(ret, total, req.GetPageNo(), req.GetPageSize()), nil
}

func (s *spxServiceableAreaController) TriggerSpxServiceableAreaCompare(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.SpxServiceableAreaCompareRequest)
	if err := s.compareTaskService.TriggerSpxServiceableAreaCompare(ctx, req.TaskId, req.ScheduledJobList); err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *spxServiceableAreaController) CancelSpxServiceableAreaCompare(ctx *utils.HttpContext, rawReqeust interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawReqeust.(*protocol.SpxServiceableAreaCompareRequest)
	if err := s.compareTaskService.CancelSpxServiceableAreaCompare(ctx, req.TaskId); err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *spxServiceableAreaController) PullSpxServiceableAreaData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*spx_service.ServiceableAreaScheduledPlanning)
	if err := s.spxSAService.PullSpxServiceableAreaData(ctx, req.Region, req.PlanningId); err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *spxServiceableAreaController) ClearExpiredSpxServiceableArea(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	if err := s.spxSAService.ClearExpiredSpxServiceableArea(ctx); err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *spxServiceableAreaController) UpdateSpxServiceableArea(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.UpdateSpxServiceableAreaRequest)
	return nil, s.spxSAService.UpdateSpxServiceableAreaData(ctx, req.Region, req.PlanningId, req.Data)
}
