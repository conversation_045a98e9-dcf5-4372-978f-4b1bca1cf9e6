package controller

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/branch_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/branch_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pis_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/saturnprovider"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/saturn/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/branch"
	jsoniter "github.com/json-iterator/go"
)

type BranchController interface {
	ListBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BranchGroupDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CreateBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SyncBranchInfo(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListBranch(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BranchDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SearchBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	AllBranchSupply(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetSyncBranchSupplyType(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SyncBranchFileData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SyncBranchFileDataForPIS(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SyncBranchData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	// branch 导出导出 SPLN-21665
	UploadBranch(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportBranch(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListBranchTaskRecord(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type branchController struct {
	branchService branch.BranchService
}

func (b *branchController) sendUploadTask(ctx utils.LCOSContext, request *protocol.UploadBranchRequest, logID string, taskID uint64, supplyName string, defaultBranchType uint8) error {
	msg := schema.UploadTaskMessage{
		FileUrl:           request.FileUrl,
		SupplyType:        request.SupplyType,
		SupplyName:        supplyName,
		DefaultBranchType: defaultBranchType,
		Region:            ctx.GetCountry(),
		LogID:             logID,
		TaskID:            taskID,
		Operator:          ctx.GetUserEmail(),
	}
	data, _ := jsoniter.Marshal(msg)
	// 任务名与接受的异步任务名相同
	producer, err := saturnprovider.GetSaturnProducer(config.GetLCOSSaturn(ctx).DomainName)
	if err != nil {
		return err
	}
	return producer.SendMessage(ctx, 1, constant.BranchUploadTask, data)
}

func (b *branchController) sendExportTask(ctx utils.LCOSContext, request *protocol.ExportBranchRequest, logID string, taskID uint64) error {
	msg := schema.ExportTaskMessage{
		QueryRequest: request,
		Region:       ctx.GetCountry(),
		LogID:        logID,
		TaskID:       taskID,
	}
	data, _ := jsoniter.Marshal(msg)
	// 任务名与接受的异步任务名相同
	producer, err := saturnprovider.GetSaturnProducer(config.GetLCOSSaturn(ctx).DomainName)
	if err != nil {
		return err
	}
	return producer.SendMessage(ctx, 1, constant.BranchExportTask, data)
}

func (b *branchController) UploadBranch(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	logID := c.GetRequestId()
	request := rawRequest.(*protocol.UploadBranchRequest)

	// 创建task record
	nowTimestamp := utils.GetTimestamp(c.Ctx)

	// 新建task record用于追踪任务状态
	taskRecord := &branch_task_record.LogisticBranchTaskRecordTab{
		OperationType:  branch_constant.BranchTaskOperationTypeImport,
		TaskStatus:     branch_constant.BranchTaskStatusProcessing, // 新建任务状态为processing
		Operator:       c.GetUserEmail(),
		OperationTime:  nowTimestamp,
		TaskUpdateTime: nowTimestamp,
		Region:         c.GetCountry(),
	}

	// 获取supply name和default branch type
	supplyName, defaultBranchType, lcosErr := b.branchService.GetSupplyNameAndDefaultBranchType(c, request.SupplyType, c.GetCountry())
	if lcosErr != nil {
		errMsg := fmt.Sprintf("cannot get valid supply name and branch type|error=%s", lcosErr.Msg)
		logger.CtxLogErrorf(c, errMsg)
		return nil, lcosErr
	}

	taskRecord.SupplyType = fmt.Sprintf("%d-%s", request.SupplyType, supplyName)

	lcosErr = b.branchService.CreateBranchTaskRecord(c, taskRecord)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, "cannot create task record:%s", lcosErr.Msg)
		return nil, lcosErr
	}

	err := b.sendUploadTask(c, request, logID, taskRecord.ID, supplyName, defaultBranchType)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return nil, nil
}

func (b *branchController) ExportBranch(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	logID := c.GetRequestId()
	request := rawRequest.(*protocol.ExportBranchRequest)

	// 创建对应的record
	nowTimestamp := utils.GetTimestamp(c.Ctx)

	// 新建task record用于追踪任务状态
	taskRecord := &branch_task_record.LogisticBranchTaskRecordTab{
		OperationType:  branch_constant.BranchTaskOperationTypeExport,
		TaskStatus:     branch_constant.BranchTaskStatusProcessing, // 新建任务状态为processing
		Operator:       c.GetUserEmail(),
		OperationTime:  nowTimestamp,
		TaskUpdateTime: nowTimestamp,
		Region:         c.GetCountry(),
	}
	lcosErr := b.branchService.CreateBranchTaskRecord(c, taskRecord)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, "cannot create task record:%s", lcosErr.Msg)
		return nil, lcosErr
	}

	err := b.sendExportTask(c, request, logID, taskRecord.ID)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, err.Error())
	}
	return nil, nil

}

func (b *branchController) ListBranchTaskRecord(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.ListBranchTaskRecordsRequest)
	var page uint32 = 1
	var count uint32 = 10
	if request.PageNo != nil {
		page = *request.PageNo
	}
	if request.Count != nil {
		count = *request.Count
	}
	models, total, lcosErr := b.branchService.ListBranchTaskRecords(c, page, count)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return map[string]interface{}{
		"pageno": page,
		"count":  count,
		"total":  total,
		"list":   models,
	}, nil
}

func NewBranchController(branchService branch.BranchService) *branchController {
	return &branchController{
		branchService: branchService,
	}
}

func (b *branchController) ListBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.ListBranchGroupRequest)
	if request.PageNo == 0 {
		request.PageNo = 1
	}
	if request.Count == 0 {
		request.Count = 10
	}
	branchGroups, total, err := b.branchService.ListBranchGroup(c, request)
	if err != nil {
		return nil, err
	}
	return map[string]interface{}{
		"pageno": request.PageNo,
		"count":  request.Count,
		"total":  total,
		"list":   branchGroups,
	}, nil
}

func (b *branchController) BranchGroupDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.GetBranchGroupRequest)
	return b.branchService.GetBranchGroupInfo(c, request.BranchGroupID)
}

func (b *branchController) CreateBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.CreateBranchGroupRequest)
	return b.branchService.CreateBranchGroup(c, request)
}

func (b *branchController) UpdateBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.UpdateBranchGroupRequest)
	return b.branchService.UpdateBranchGroup(c, request)
}

func (b *branchController) ListBranch(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.ListBranchRequest)
	if request.PageNo == 0 {
		request.PageNo = 1
	}
	if request.Count == 0 {
		request.Count = 10
	}
	branchs, total, err := b.branchService.ListBranch(c, request)
	if err != nil {
		return nil, err
	}

	var list interface{} = branchs
	if branchs == nil {
		list = []struct{}{}
	}

	return map[string]interface{}{
		"pageno": request.PageNo,
		"count":  request.Count,
		"total":  total,
		"list":   list,
	}, nil
}

func (b *branchController) BranchDetail(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.GetBranchRequest)
	return b.branchService.GetBranch(c, request.BranchID)
}

func (b *branchController) SyncBranchInfo(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.SyncBranchInfoRequest)
	return struct{}{}, b.branchService.SendSyncBranchCommand(c, request.BranchGroupID)
}

func (b *branchController) AllBranchSupply(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.GetAllBranchSupplyRequest)
	list, err := b.branchService.AllBranchSupply(c, request.Region)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"list": list,
	}, nil
}

func (b *branchController) GetSyncBranchSupplyType(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.GetSyncBranchSupplyTypeRequest)
	list, err := b.branchService.GetSyncBranchSupplyType(c, request.Region)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"branch_supply_type": list,
	}, nil
}

func (b *branchController) SyncBranchFileData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.SyncBranchFileDataRequest)
	s3Config := config.GetConf(c.Ctx).LCSS3Config
	err := b.branchService.SyncBranchInfoFromChannel(c, request.BranchSupplyType, request.Region, request.RemoteFilePath, c.GetRequestId(), s3Config.Endpoint, s3Config.AccessKeyID, s3Config.BucketKey, false)
	if err != nil {
		return nil, err
	}

	return struct{}{}, err
}

func (b *branchController) SyncBranchFileDataForPIS(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pis_protocol.PISCommonRequest)

	// 设置日志id，方便查看相关日志
	ctx := context.WithValue(c.Ctx, constant2.RequestIdKey, "|"+request.ReqHeader.RequestID)
	c.Ctx = ctx

	// 解析business data
	type branchBusinessData struct {
		RemoteFilePath string `json:"remote_file_path"`
		ResourceID     int    `json:"resource_id"`
		Region         string `json:"region"`
	}
	var data branchBusinessData
	err := jsoniter.Unmarshal([]byte(request.BusinessData), &data)
	if err != nil {
		errMsg := fmt.Sprintf("cannot unmarsh business data|error=%s", err.Error())
		return nil, lcos_error.NewLCOSError(lcos_error.ServerErrorCode, errMsg)
	}

	// 将resource id转为supply type
	supplyType, err := config.GetSupplyTypeByResourceID(ctx, data.ResourceID, data.Region)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, err.Error())
	}

	supplyTypeInfoStr, _ := jsoniter.MarshalToString(supplyType)

	logger.CtxLogInfof(c, "get supply type info:[%s] for resource_id:[%d]", supplyTypeInfoStr, data.ResourceID)

	pisS3Config := config.GetConf(ctx).PISS3Config
	if config.GetUseLatamPisS3Config(ctx, data.Region) {
		pisS3Config = config.GetConf(ctx).LatamPISS3Config
	}
	lcosErr := b.branchService.SyncBranchInfoFromChannel(c, supplyType.BranchSupplyType, data.Region, data.RemoteFilePath, request.ReqHeader.RequestID, pisS3Config.Endpoint, pisS3Config.AccessKeyID, pisS3Config.BucketKey, supplyType.IncrementalFlag)
	if lcosErr != nil {
		return nil, lcosErr
	}

	return struct{}{}, lcosErr

}

func (b *branchController) DeleteBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.DeleteBranchGroupRequest)
	return nil, b.branchService.DeleteBranchGroup(c, request)
}

func (b *branchController) SearchBranchGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*protocol.SearchBranchGroupRequest)

	branchGroupInfo, lcosError := b.branchService.GetBranchGroupInfoByGroupId(c, request.BranchGroupID)
	if lcosError != nil {
		return nil, lcosError
	}
	return branchGroupInfo, nil
}

func (b *branchController) SyncBranchData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pis_protocol.SyncBranchDataRequest)

	// 设置日志id，方便查看相关日志
	ctx := context.WithValue(c.Ctx, constant2.RequestIdKey, "|"+request.ReqHeader.RequestID)
	c.Ctx = ctx

	type branchBusinessData struct {
		BranchList       []pis_protocol.BranchData `json:"branch_list"`
		BranchSupplyType uint32                    `json:"branch_supply_type"`
		Region           string                    `json:"region"`
		OperationTime    uint32                    `json:"operation_time"`
	}

	var data branchBusinessData
	err := jsoniter.UnmarshalFromString(request.BusinessData, &data)
	if err != nil {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("param error:%s", err.Error()))
	}

	resp, lcosErr := b.branchService.SyncBranchData(c, data.BranchSupplyType, data.Region, request.ReqHeader.RequestID, data.BranchList, data.OperationTime)
	if lcosErr != nil {
		return nil, lcosErr
	}

	return resp, lcosErr

}

var _ BranchController = (*branchController)(nil)
