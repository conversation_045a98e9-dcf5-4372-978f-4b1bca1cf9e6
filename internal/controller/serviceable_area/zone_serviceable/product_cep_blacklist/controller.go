package product_cep_blacklist

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/zone_serviceable_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/zone_serviceable/product_cep_blacklist"
)

type ProductCepBlacklistController interface {
	ImportLogisticProductCepBlacklist(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportLogisticProductCepBlacklist(ctx *utils.HttpContext, rawRequest interface{})
}

type productCepBlacklistControllerImpl struct {
	cepBlacklistService product_cep_blacklist.LogisticProductCepBlacklistService
}

func NewProductCepBlacklistController(cepBlacklistService product_cep_blacklist.LogisticProductCepBlacklistService) *productCepBlacklistControllerImpl {
	return &productCepBlacklistControllerImpl{
		cepBlacklistService: cepBlacklistService,
	}
}

func (s *productCepBlacklistControllerImpl) ImportLogisticProductCepBlacklist(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*zone_serviceable_protocol.ImportCepBlacklistRequest)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "invalid request schema")
	}
	if err := s.cepBlacklistService.ImportLogisticProductCepBlacklist(ctx, req.FileUrl); err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *productCepBlacklistControllerImpl) ExportLogisticProductCepBlacklist(ctx *utils.HttpContext, rawRequest interface{}) {
	req, ok := rawRequest.(*zone_serviceable_protocol.ExportCepBlacklistRequest)
	if !ok {
		http.GenErrorResponseWithParam(ctx.Context, lcos_error.SchemaParamsErrorCode, nil, "request invalid")
		return
	}
	file, err := s.cepBlacklistService.ExportLogisticProductCepBlacklist(ctx, req.ProductId)
	if err != nil {
		http.GenErrorResponseWithParam(ctx.Context, lcos_error.SchemaParamsErrorCode, nil, err.Msg)
		return
	}
	http.WriteXlsxFileResponse(ctx.Context, file, fmt.Sprintf("%d_cep_blacklist.xlsx", req.ProductId))
}
