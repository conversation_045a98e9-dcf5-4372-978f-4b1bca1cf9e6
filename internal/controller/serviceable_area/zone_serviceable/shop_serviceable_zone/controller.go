package shop_serviceable_zone

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/zone_serviceable_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/zone_serviceable/shop_serviceable_zone"
)

type ShopServiceableZoneController interface {
	UpdateShopServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListShopServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	QuerySellerServiceableZoneInfo(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewShopServiceableZoneController(svc shop_serviceable_zone.ShopServiceableZoneService) *shopServiceableZoneController {
	return &shopServiceableZoneController{
		svc: svc,
	}
}

type shopServiceableZoneController struct {
	svc shop_serviceable_zone.ShopServiceableZoneService
}

func (s *shopServiceableZoneController) UpdateShopServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.UpdateShopServiceableZoneRequest)
	resp, err := s.svc.UpdateShopServiceableZone(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *shopServiceableZoneController) ListShopServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ListShopServiceableZoneRequest)
	zoneList, err := s.svc.ListShopServiceableZone(ctx, req.ShopId, req.ProductId)
	if err != nil {
		return nil, err
	}
	return &zone_serviceable_protocol.ListShopServiceableZoneResponse{
		ZoneList: zoneList,
	}, nil
}

func (s *shopServiceableZoneController) QuerySellerServiceableZoneInfo(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.QuerySellerServiceableZoneInfoRequest)
	resp, err := s.svc.QuerySellerServiceableZoneInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
