package product_serviceable_zone

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/shop_serviceable_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/zone_serviceable_protocol"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/zone_serviceable/product_serviceable_zone"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/zone_serviceable/product_serviceable_zone"
	"github.com/tealeg/xlsx"
)

type ProductServiceableZoneController interface {
	ImportProductServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListProductServiceableZoneWithPaging(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListProductServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListProductServiceableZoneWithCepGroup(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListProductServiceableZoneAsGeoJson(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	ImportProductServiceableCepGroup(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportProductServiceableCepGroup(ctx *utils.HttpContext, rawRequest interface{})
}

func NewProductServiceableZoneController(svc service.ProductServiceableZoneService) *productServiceableZoneController {
	return &productServiceableZoneController{
		svc: svc,
	}
}

type productServiceableZoneController struct {
	svc service.ProductServiceableZoneService
}

func (p *productServiceableZoneController) ImportProductServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ImportProductServiceableZoneRequest)
	if err := p.svc.ImportProductServiceableZone(ctx, req.ProductId, req.MetroRegion, req.UploadFileUrl); err != nil {
		return nil, err
	}
	return nil, nil
}

func (p *productServiceableZoneController) ListProductServiceableZoneWithPaging(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*zone_serviceable_protocol.ListProductServiceableZoneWithPagingRequest)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "invalid request schema")
	}

	dataList, total, err := p.svc.ListProductServiceableZoneWithPaging(ctx, req)
	if err != nil {
		return nil, err
	}
	retList := make([]*zone_serviceable_protocol.ListProductServiceableZoneWithPagingResponse, 0, len(dataList))
	for _, data := range dataList {
		ret := &zone_serviceable_protocol.ListProductServiceableZoneWithPagingResponse{
			ProductId:      data.ProductId,
			ZoneName:       data.ZoneName,
			MetroRegion:    data.MetroRegion,
			StateName:      data.StateName,
			ContainsCities: data.ContainsCities,
		}
		if req.IncludePolygon {
			ret.Polygon = &data.Polygon
		}
		retList = append(retList, ret)
	}
	return http.GeneratePagingQueryData(retList, total, req.PageNo, req.PageSize), nil
}

func (p *productServiceableZoneController) ListProductServiceableZone(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ListProductServiceableZoneRequest)

	sellerStateName := req.SellerStateName
	if sellerStateName == "" {
		sellerStateName = req.CityName // 兼容性问题。seller_state_name字段用于取代city_name字段，如果seller_state_name没传，则取city_name字段
	}

	dataList, err := p.svc.ListProductServiceableZone(ctx, req.ProductId, sellerStateName, req.SellerCityName)
	if err != nil {
		return nil, err
	}
	return &zone_serviceable_protocol.ListProductServiceableZoneResponse{
		ZoneList: dataList,
	}, nil
}

func (p *productServiceableZoneController) ListProductServiceableZoneWithCepGroup(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ListProductServiceableZoneRequest)
	dataList, err := p.svc.ListProductServiceableZoneWithCepGroup(ctx, req.ProductId, "", "")
	if err != nil {
		return nil, err
	}

	zoneNameList := make([]string, 0, len(dataList))
	zoneList := make([]*zone_serviceable_protocol.ProductServiceableZone, 0, len(dataList))
	for _, data := range dataList {
		zoneNameList = append(zoneNameList, data.ZoneName)
		zoneList = append(zoneList, &zone_serviceable_protocol.ProductServiceableZone{
			ProductId: data.ProductId,
			StateName: data.MetroRegion, // zone表存储的city_name字段，实际为metro region，对外返回为state_name（历史遗留的命名问题
			ZoneName:  data.ZoneName,
		})
	}
	// SPLN-36909 加上DDE模式下卖家所属的zone和metro region
	zoneNameList = append(zoneNameList, shop_serviceable_constant.DDEModeZoneName)
	zoneList = append(zoneList, &zone_serviceable_protocol.ProductServiceableZone{
		ProductId: req.ProductId,
		StateName: shop_serviceable_constant.DDEModeMetroRegion,
		ZoneName:  shop_serviceable_constant.DDEModeZoneName,
	})

	return &zone_serviceable_protocol.ListProductServiceableZoneNameResponse{
		ZoneNameList: zoneNameList,
		ZoneList:     zoneList,
	}, nil
}

func (p *productServiceableZoneController) ListProductServiceableZoneAsGeoJson(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ListProductServiceableZoneRequest)
	dataList, err := p.svc.ListProductServiceableZone(ctx, req.ProductId, req.SellerStateName, req.SellerCityName)
	if err != nil {
		return nil, err
	}
	polygonList := make([]model.GeoPolygon, 0, len(dataList))
	for _, data := range dataList {
		polygonList = append(polygonList, data.Polygon)
	}
	return map[string]interface{}{
		"type":     "FeatureCollection",
		"features": polygonList,
	}, nil
}

func (p *productServiceableZoneController) ImportProductServiceableCepGroup(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ImportProductServiceableCepGroupRequest)
	if err := p.svc.ImportProductServiceableCepGroup(ctx, req.UploadFileUrl); err != nil {
		return nil, err
	}
	return nil, nil
}

func (p *productServiceableZoneController) ExportProductServiceableCepGroup(ctx *utils.HttpContext, rawRequest interface{}) {
	req, _ := rawRequest.(*zone_serviceable_protocol.ExportProductServiceableCepGroupRequest)
	dataList, lcosErr := p.svc.ListProductServiceableCepGroup(ctx, req.ProductId)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(ctx.Context, lcosErr.RetCode, nil, lcosErr.Msg)
		return
	}

	rows := make([]interface{}, 0, len(dataList))
	for _, data := range dataList {
		rows = append(rows, &productServiceableCepGroupExcelRow{
			ChannelId:  data.ProductId,
			CepInitial: data.CepInitial,
			CepFinal:   data.CepFinal,
			GroupName:  data.ZoneName,
		})
	}

	file := xlsx.NewFile()
	if err := excel.WriteTitleAndStruct(file, "Sheet1", productServiceableCepGroupExcelTitles, rows); err != nil {
		http.GenErrorResponseWithParam(ctx.Context, lcos_error.ServerErrorCode, nil, fmt.Sprintf("generate export file error, cause: %s", err.Error()))
		return
	}
	http.WriteXlsxFileResponse(ctx.Context, file, "cep_group.xlsx")
}
