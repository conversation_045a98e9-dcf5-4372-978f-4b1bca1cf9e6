package predefined_route_serviceable

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/predefined_route_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/predefined_route_serviceable"
)

type LogisticPredefinedRouteController interface {
	CreateLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{})
	ListLogisticPredefinedRouteCode(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewLogisticPredefinedRouteController(svc predefined_route_serviceable.LogisticLinePredefinedRouteService) *logisticPredefinedRouteControllerImpl {
	return &logisticPredefinedRouteControllerImpl{
		svc: svc,
	}
}

type logisticPredefinedRouteControllerImpl struct {
	svc predefined_route_serviceable.LogisticLinePredefinedRouteService
}

func (l *logisticPredefinedRouteControllerImpl) CreateLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*predefined_route_protocol.CreateLinePredefinedRouteReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "request invalid")
	}
	return nil, l.svc.CreateLogisticLinePredefinedRoute(ctx, req)
}

func (l *logisticPredefinedRouteControllerImpl) UpdateLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*predefined_route_protocol.UpdateLinePredefinedRouteReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "request invalid")
	}
	return nil, l.svc.UpdateLogisticLinePredefinedRoute(ctx, req)
}

func (l *logisticPredefinedRouteControllerImpl) DeleteLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*predefined_route_protocol.DeleteLinePredefinedRouteReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "request invalid")
	}
	return nil, l.svc.DeleteLogisticLinePredefinedRoute(ctx, req)
}

func (l *logisticPredefinedRouteControllerImpl) ListLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*predefined_route_protocol.ListLinePredefinedRouteReq)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "request invalid")
	}
	if err := http.ValidatePagingQuery(ctx, req); err != nil {
		return nil, err
	}

	list, total, err := l.svc.ListLogisticLinePredefinedRoute(ctx, req)
	if err != nil {
		return nil, err
	}
	return http.GeneratePagingQueryData(list, total, req.GetPageNo(), req.GetPageSize()), nil
}

func (l *logisticPredefinedRouteControllerImpl) UploadLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, ok := rawRequest.(*common_protocol.UploadFileRequest)
	if !ok {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "request invalid")
	}
	return nil, l.svc.UploadLogisticLinePredefinedRoute(ctx, req)
}

func (l *logisticPredefinedRouteControllerImpl) ExportLogisticPredefinedRoute(ctx *utils.HttpContext, rawRequest interface{}) {
	req, ok := rawRequest.(*predefined_route_protocol.ExportLinePredefinedRouteReq)
	if !ok {
		http.GenErrorResponseWithParam(ctx.Context, lcos_error.SchemaParamsErrorCode, nil, "request invalid")
		return
	}

	file, err := l.svc.ExportLogisticLinePredefinedRoute(ctx, req)
	if err != nil {
		http.GenErrorResponseWithParam(ctx.Context, lcos_error.SchemaParamsErrorCode, nil, err.Msg)
		return
	}
	http.WriteXlsxFileResponse(ctx.Context, file, "predefined_route.xlsx")
}

func (l *logisticPredefinedRouteControllerImpl) ListLogisticPredefinedRouteCode(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	return l.svc.ListAllLogisticLinePredefinedRouteCode(ctx)
}
