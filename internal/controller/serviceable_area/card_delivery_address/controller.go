package card_delivery_address

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/card_delivery_address_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/common_card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/seabank_service"
	"strconv"
)

type CardDeliveryAddressController interface {
	// 推送全量数据
	NotifyCardDeliveryAddressAllData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// 推送数据
	NotifyCardDeliveryAddressFileData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// 推送指定版本数据
	NotifyCardDeliveryAddressChangeVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	// 下载指定版本的增量地址数据
	DownloadCardDeliveryAddressChangeVersionData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	GetCardDeliveryAddressDownloadFilePath(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewCardDeliveryAddressController(basicLocationService basic_location.LineBasicServiceableLocationServiceInterface, cardDeliveryAddressService card_delivery_address.CardDeliveryAddressService, commonCardDeliveryAddressService common_card_delivery_address.CommonCardDeliveryAddressService) *cardDeliveryAddressController {
	return &cardDeliveryAddressController{
		basicLocationService:             basicLocationService,
		cardDeliveryAddressService:       cardDeliveryAddressService,
		commonCardDeliveryAddressService: commonCardDeliveryAddressService,
	}
}

type cardDeliveryAddressController struct {
	basicLocationService             basic_location.LineBasicServiceableLocationServiceInterface
	cardDeliveryAddressService       card_delivery_address.CardDeliveryAddressService
	commonCardDeliveryAddressService common_card_delivery_address.CommonCardDeliveryAddressService
}

func (c *cardDeliveryAddressController) NotifyCardDeliveryAddressAllData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	return nil, c.basicLocationService.NotifyCardDeliveryAddressAllData(ctx)
}

func (c *cardDeliveryAddressController) NotifyCardDeliveryAddressFileData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*card_delivery_address_protocol.NotifyCardDeliveryAddressFileDataRequest)
	return nil, seabank_service.NewSeabankGatewayService(ctx, req.Region).NotifyCardDeliveryAddressUpdate(ctx, "FILE", req.RemoteFilePath)
}

func (c *cardDeliveryAddressController) NotifyCardDeliveryAddressChangeVersion(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*card_delivery_address_protocol.NotifyCardDeliveryAddressChangeVersionRequest)
	version, err := c.cardDeliveryAddressService.GetCardDeliveryAddressChangeVersionById(ctx, req.VersionId)
	if err != nil {
		return nil, err
	}
	return nil, seabank_service.NewSeabankGatewayService(ctx, version.Region).NotifyCardDeliveryAddressUpdate(ctx, strconv.FormatUint(version.Id, 10), version.RemoteFilePath)
}

func (c *cardDeliveryAddressController) DownloadCardDeliveryAddressChangeVersionData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*card_delivery_address_protocol.NotifyCardDeliveryAddressChangeVersionRequest)
	version, err := c.cardDeliveryAddressService.GetCardDeliveryAddressChangeVersionById(ctx, req.VersionId)
	if err != nil {
		return nil, err
	}
	cfg := config.GetConf(ctx).LCOSS3Config
	return s3_service.NewS3Service().GetFileUrlByFilePathForUss2(ctx, cfg.AccessKeyID, cfg.BucketKey, version.RemoteFilePath, cfg.ExpirationDays)
}

func (c *cardDeliveryAddressController) GetCardDeliveryAddressDownloadFilePath(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*card_delivery_address_protocol.GetCardDeliveryAddressDownloadFilePathRequest)

	path, hashCode, sysErr := c.commonCardDeliveryAddressService.UploadCardDeliveryAddress(ctx, req)
	if sysErr != nil {
		return nil, sysErr
	}

	resp := &card_delivery_address_protocol.ListCardDeliveryAddressDownloadFileResponse{
		DownloadPath: path,
		FileHashCode: hashCode,
	}

	return resp, nil
}
