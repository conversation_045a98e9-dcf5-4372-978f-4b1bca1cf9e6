package basic_location

type excelRawofLocation struct {
	LineId                string `excel:"title:Line ID"`
	Region                string `excel:"title:Region"`
	CollectDeliverGroupId string `excel:"title:Collect Deliver Group Id"`
	LocationId            int    `excel:"title:Location ID"`
	State                 string `excel:"title:State"`
	City                  string `excel:"title:City"`
	District              string `excel:"title:District"`
	Street                string `excel:"title:Street"`
	SupportPickup         int    `excel:"title:Support Pickup"`
	SupportCodPickup      int    `excel:"title:Support COD Pickup"`
	SupportDeliver        int    `excel:"title:Support Deliver"`
	SupportCodDeliver     int    `excel:"title:Support COD Deliver"`
	SupportTradeInDeliver int    `excel:"title:Support Trade In Deliver"`
}

// Deprecated
type excelRawOfOriginLocation struct {
	LineId             string `excel:"title:Line ID"`
	Region             string `excel:"title:Region"`
	CollectServiceType string `excel:"title:Collect Service Type"`
	LocationId         int    `excel:"title:Location ID"`
	State              string `excel:"title:State"`
	City               string `excel:"title:City"`
	District           string `excel:"title:District"`
	Street             string `excel:"title:Street"`
	SupportPickup      int    `excel:"title:Support Pickup"`
	SupportCodPickup   int    `excel:"title:Support COD Pickup"`
}

// Deprecated
type excelRawOfDestLocation struct {
	LineId             string `excel:"title:Line ID"`
	Region             string `excel:"title:Region"`
	DeliverCollectType string `excel:"title:Deliver Service Type"`
	LocationId         int    `excel:"title:Location ID"`
	State              string `excel:"title:State"`
	City               string `excel:"title:City"`
	District           string `excel:"title:District"`
	Street             string `excel:"title:Street"`
	SupportDeliver     int    `excel:"title:Support Deliver"`
	SupportCodDeliver  int    `excel:"title:Support COD Deliver"`
}
