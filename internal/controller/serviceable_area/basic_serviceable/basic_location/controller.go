package basic_location

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/basic_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_location"
	"github.com/tealeg/xlsx"
)

type AdminLineBasicServiceableLocationController interface {
	CreateBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchCreateBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CreateBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchCreateBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetBasicServiceableOriginLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetBasicServiceableDestLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{})
	DownloadBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{})
	UploadBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	CreateBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchCreateBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetBasicServiceableLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{})
	UploadBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SearchAllFourLevelAddress(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminLineBasicServiceableLocationController struct {
	service service.LineBasicServiceableLocationServiceInterface
}

func NewAdminLineBasicServiceableLocationController(m service.LineBasicServiceableLocationServiceInterface) *adminLineBasicServiceableLocationController {
	return &adminLineBasicServiceableLocationController{service: m}
}

func (controller *adminLineBasicServiceableLocationController) CreateBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateBasicLocationRequest)
	s, err := controller.service.CreateBasicServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}
	return s, err
}

func (controller *adminLineBasicServiceableLocationController) BatchCreateBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.BatchCreateBasicLocationRequest)
	s, err := controller.service.BatchCreateBasicServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}
	return s, err
}

func (controller *adminLineBasicServiceableLocationController) UpdateBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UpdateBasicLocationRequest)
	s, err := controller.service.UpdateBasicServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}
	return s, err
}

func (controller *adminLineBasicServiceableLocationController) GetBasicServiceableLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.GetBasicLocationListRequest)
	s, err := controller.service.GetBasicServiceableLocationList(c, request)
	if err != nil {
		return nil, err
	}
	return s, err
}

func (controller *adminLineBasicServiceableLocationController) DeleteBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteBasicLocationRequest)
	err := controller.service.DeleteBasicServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

func (controller *adminLineBasicServiceableLocationController) DownloadBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.GetAllBasicLocationRequest)
	c.ClientType = utils.GetClientType(request.LLSResource)
	models, err := controller.service.GetAllBasicServiceableLocationList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}
	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawofLocation{
			LineId:                model.LineId,
			Region:                model.Region,
			CollectDeliverGroupId: model.CollectDeliverGroupId,
			LocationId:            int(model.LocationId),
			State:                 model.State,
			City:                  model.City,
			District:              model.District,
			Street:                model.Street,
			SupportPickup:         int(*model.CanPickup),
			SupportCodPickup:      int(*model.CanCodPickup),
			SupportDeliver:        int(*model.CanDeliver),
			SupportCodDeliver:     int(*model.CanCodDeliver),
			SupportTradeInDeliver: int(model.SupportTradeIn),
		})
	}

	fileName := "basic_location.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Collect Deliver Group Id", "Location ID", "State", "City", "District", "Street", "Support Pickup", "Support COD Pickup", "Support Deliver", "Support COD Deliver", "Support Trade In Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (controller *adminLineBasicServiceableLocationController) UploadBasicServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)
	err := controller.service.UploadBasicServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

func (controller *adminLineBasicServiceableLocationController) SearchAllFourLevelAddress(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.SearchAllFourLevelAddressRequest)
	s, err := controller.service.SearchAllFourLevelAddress(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) CreateBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateBasicOriginLocationRequest)

	s, err := controller.service.CreateBasicServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) BatchCreateBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.BatchCreateBasicOriginLocationRequest)

	s, err := controller.service.BatchCreateBasicServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) CreateBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateBasicDestLocationRequest)

	s, err := controller.service.CreateBasicServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) BatchCreateBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.BatchCreateBasicDestLocationRequest)

	s, err := controller.service.BatchCreateBasicServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) UpdateBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UpdateBasicOriginLocationRequest)

	s, err := controller.service.UpdateBasicServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) UpdateBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UpdateBasicDestLocationRequest)

	s, err := controller.service.UpdateBasicServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) GetBasicServiceableOriginLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.GetBasicOriginLocationListRequest)

	s, err := controller.service.GetBasicServiceableOriginLocationList(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) GetBasicServiceableDestLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.GetBasicDestLocationListRequest)

	s, err := controller.service.GetBasicServiceableDestLocationList(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) DeleteBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteBasicLocationRequest)

	err := controller.service.DeleteBasicServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) DeleteBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteBasicLocationRequest)

	err := controller.service.DeleteBasicServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) DownloadBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.GetAllBasicOriginLocationRequest)

	models, err := controller.service.GetAllBasicServiceableOriginLocationList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		collectServiceType := ""
		if model.CollectServiceType == constant.PICKUP {
			collectServiceType = "Pickup"
		} else {
			collectServiceType = "B2C"
		}
		excelRawData = append(excelRawData, &excelRawOfOriginLocation{
			LineId:             model.LineId,
			Region:             model.Region,
			CollectServiceType: collectServiceType,
			LocationId:         int(model.LocationId),
			State:              model.State,
			City:               model.City,
			District:           model.District,
			Street:             model.Street,
			SupportPickup:      int(*model.CanPickup),
			SupportCodPickup:   int(*model.CanCodPickup),
		})
	}

	fileName := "basic_origin_location.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Collect Service Type", "Location ID", "State", "City", "District", "Street", "Support Pickup", "Support COD Pickup",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) DownloadBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.GetAllBasicDestLocationRequest)

	models, err := controller.service.GetAllBasicServiceableDestLocationList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		deliverServiceType := ""
		if model.DeliverServiceType == constant.TOHOME {
			deliverServiceType = "To Home"
		} else {
			deliverServiceType = "To Site"
		}
		excelRawData = append(excelRawData, &excelRawOfDestLocation{
			LineId:             model.LineId,
			Region:             model.Region,
			DeliverCollectType: deliverServiceType,
			LocationId:         int(model.LocationId),
			State:              model.State,
			City:               model.City,
			District:           model.District,
			Street:             model.Street,
			SupportDeliver:     int(*model.CanDeliver),
			SupportCodDeliver:  int(*model.CanCodDeliver),
		})
	}

	fileName := "basic_dest_location.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Deliver Service Type", "Location ID", "State", "City", "District", "Street", "Support Deliver", "Support COD Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) UploadBasicServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)

	err := controller.service.UploadBasicServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineBasicServiceableLocationController) UploadBasicServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)

	err := controller.service.UploadBasicServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
