package basic_postcode

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/basic_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_postcode"
	"github.com/tealeg/xlsx"
)

type AdminLineBasicServiceablePostcodeController interface {
	CreateBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CreateBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetBasicServiceableOriginPostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetBasicServiceableDestPostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{})
	DownloadBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{})
	UploadBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	CreateBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetBasicServiceablePostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{})
	UploadBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminLineBasicServiceablePostcodeController struct {
	service service.LineBasicServiceablePostcodeServiceInterface
}

func NewAdminLineBasicServiceablePostcodeController(m service.LineBasicServiceablePostcodeServiceInterface) *adminLineBasicServiceablePostcodeController {
	return &adminLineBasicServiceablePostcodeController{service: m}
}

func (controller *adminLineBasicServiceablePostcodeController) CreateBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateBasicPostcodeRequest)
	s, err := controller.service.CreateBasicServiceablePostcode(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (controller *adminLineBasicServiceablePostcodeController) UpdateBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UpdateBasicPostcodeRequest)
	s, err := controller.service.UpdateBasicServiceablePostcode(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (controller *adminLineBasicServiceablePostcodeController) GetBasicServiceablePostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.GetBasicPostcodeListRequest)
	s, err := controller.service.GetBasicServiceablePostcodeList(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (controller *adminLineBasicServiceablePostcodeController) DeleteBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteBasicPostcodeRequest)
	err := controller.service.DeleteBasicServiceablePostcode(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (controller *adminLineBasicServiceablePostcodeController) DownloadBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.GetAllBasicPostcodeRequest)
	c.ClientType = utils.GetClientType(request.LLSResource)

	models, err := controller.service.GetAllBasicServiceablePostcodeList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawOfPostcode{
			LineId:                model.LineId,
			Region:                model.Region,
			CollectDeliverGroupId: model.CollectDeliverGroupId,
			Postcode:              model.Postcode,
			SupportPickup:         int(*model.CanPickup),
			SupportCodPickup:      int(*model.CanCodPickup),
			SupportDeliver:        int(*model.CanDeliver),
			SupportCodDeliver:     int(*model.CanCodDeliver),
			SupportTradeIn:        int(model.SupportTradeIn),
		})
	}

	fileName := "basic_postcode.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Collect Deliver Group Id", "Postcode", "Support Pickup", "Support COD Pickup", "Support Deliver", "Support COD Deliver", "Support Trade In Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (controller *adminLineBasicServiceablePostcodeController) UploadBasicServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)
	if err := controller.service.UploadBasicServiceablePostcode(c, request); err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) CreateBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateBasicOriginPostcodeRequest)

	s, err := controller.service.CreateBasicServiceableOriginPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) CreateBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateBasicDestPostcodeRequest)

	s, err := controller.service.CreateBasicServiceableDestPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) UpdateBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UpdateBasicOriginPostcodeRequest)

	s, err := controller.service.UpdateBasicServiceableOriginPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) UpdateBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UpdateBasicDestPostcodeRequest)

	s, err := controller.service.UpdateBasicServiceableDestPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) GetBasicServiceableOriginPostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.GetBasicOriginPostcodeListRequest)

	s, err := controller.service.GetBasicServiceableOriginPostcodeList(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) GetBasicServiceableDestPostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.GetBasicDestPostcodeListRequest)

	s, err := controller.service.GetBasicServiceableDestPostcodeList(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) DeleteBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteBasicPostcodeRequest)

	err := controller.service.DeleteBasicServiceableOriginPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) DeleteBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteBasicPostcodeRequest)

	err := controller.service.DeleteBasicServiceableDestPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) DownloadBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.GetAllBasicOriginPostcodeRequest)

	models, err := controller.service.GetAllBasicServiceableOriginPostcodeList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		collectServiceType := ""
		if model.CollectServiceType == constant.PICKUP {
			collectServiceType = "Pickup"
		} else {
			collectServiceType = "B2C"
		}
		excelRawData = append(excelRawData, &excelRawOfOriginPostcode{
			LineId:             model.LineId,
			Region:             model.Region,
			CollectServiceType: collectServiceType,
			Postcode:           model.Postcode,
			SupportPickup:      int(*model.CanPickup),
			SupportCodPickup:   int(*model.CanCodPickup),
		})
	}

	fileName := "basic_origin_postcode.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Collect Service Type", "Postcode", "Support Pickup", "Support COD Pickup",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) DownloadBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.GetAllBasicDestPostcodeRequest)

	models, err := controller.service.GetAllBasicServiceableDestPostcodeList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		deliverServiceType := ""
		if model.DeliverServiceType == constant.TOHOME {
			deliverServiceType = "To Home"
		} else {
			deliverServiceType = "To Site"
		}
		excelRawData = append(excelRawData, &excelRawOfDestPostcode{
			LineId:             model.LineId,
			Region:             model.Region,
			DeliverCollectType: deliverServiceType,
			Postcode:           model.Postcode,
			SupportDeliver:     int(*model.CanDeliver),
			SupportCodDeliver:  int(*model.CanCodDeliver),
		})
	}

	fileName := "basic_dest_postcode.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Deliver Service Type", "Postcode", "Support Deliver", "Support COD Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) UploadBasicServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)

	err := controller.service.UploadBasicServiceableOriginPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineBasicServiceablePostcodeController) UploadBasicServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)

	err := controller.service.UploadBasicServiceableDestPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
