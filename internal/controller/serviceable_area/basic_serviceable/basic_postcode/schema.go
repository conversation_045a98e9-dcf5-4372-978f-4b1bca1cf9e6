package basic_postcode

type excelRawOfPostcode struct {
	LineId                string `excel:"title:Line ID"`
	Region                string `excel:"title:Region"`
	CollectDeliverGroupId string `excel:"title:Collect Deliver Group Id"`
	Postcode              string `excel:"title:Postcode"`
	SupportPickup         int    `excel:"title:Support Pickup"`
	SupportCodPickup      int    `excel:"title:Support COD Pickup"`
	SupportDeliver        int    `excel:"title:Support Deliver"`
	SupportCodDeliver     int    `excel:"title:Support COD Deliver"`
	SupportTradeIn        int    `excel:"title:Support Trade In Deliver"`
}

// Deprecated
type excelRawOfOriginPostcode struct {
	LineId             string `excel:"title:Line ID"`
	Region             string `excel:"title:Region"`
	CollectServiceType string `excel:"title:Collect Service Type"`
	Postcode           string `excel:"title:Postcode"`
	SupportPickup      int    `excel:"title:Support Pickup"`
	SupportCodPickup   int    `excel:"title:Support COD Pickup"`
}

// Deprecated
type excelRawOfDestPostcode struct {
	LineId             string `excel:"title:Line ID"`
	Region             string `excel:"title:Region"`
	DeliverCollectType string `excel:"title:Deliver Service Type"`
	Postcode           string `excel:"title:Postcode"`
	SupportDeliver     int    `excel:"title:Support Deliver"`
	SupportCodDeliver  int    `excel:"title:Support COD Deliver"`
}
