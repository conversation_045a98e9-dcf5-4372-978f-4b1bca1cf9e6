package basic_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"github.com/tealeg/xlsx"
	"math"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/basic_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	areaRouteService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/area_route_serviceable"
	basicCepRangeService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_cep_range"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_conf"
	basicLocationService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_location"
	basicPostcodeService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_postcode"
	operatorCepRangeService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_cep_range"
	operationLocationService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_location"
	operationPostcodeService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_postcode"
	"github.com/jinzhu/copier"
)

type AdminLineBasicServiceableConfController interface {
	CreateBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetBasicServiceableConfList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteAllServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SimplePutBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchGetBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CheckCollectDeliverGroupWithLine(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportLinesUsingLocationChecked(c *utils.HttpContext, rawRequest interface{})
}

type adminLineBasicServiceableConfController struct {
	service                  service.LineBasicServiceableConfServiceInterface
	basicCepRangeService     basicCepRangeService.LineBasicServiceableCepRangeServiceInterface
	operatorCepRangeService  operatorCepRangeService.LineOperationServiceableCepRangeServiceInterface
	basicLocationService     basicLocationService.LineBasicServiceableLocationServiceInterface
	basicPostcodeService     basicPostcodeService.LineBasicServiceablePostcodeServiceInterface
	operationPostcodeService operationPostcodeService.LineOperationServiceablePostcodeServiceInterface
	operationLocationService operationLocationService.LineOperationServiceableLocationServiceInterface
	areaRouteService         areaRouteService.LineServiceableRouteServiceInterface
}

func NewAdminLineBasicServiceableConfController(
	m service.LineBasicServiceableConfServiceInterface,
	basicCepRangeService basicCepRangeService.LineBasicServiceableCepRangeServiceInterface,
	operatorCepRangeService operatorCepRangeService.LineOperationServiceableCepRangeServiceInterface,
	basicLocationService basicLocationService.LineBasicServiceableLocationServiceInterface,
	basicPostcodeService basicPostcodeService.LineBasicServiceablePostcodeServiceInterface,
	operationPostcodeService operationPostcodeService.LineOperationServiceablePostcodeServiceInterface,
	operationLocationService operationLocationService.LineOperationServiceableLocationServiceInterface,
	areaRouteService areaRouteService.LineServiceableRouteServiceInterface,
) *adminLineBasicServiceableConfController {
	return &adminLineBasicServiceableConfController{
		service:                  m,
		basicCepRangeService:     basicCepRangeService,
		operatorCepRangeService:  operatorCepRangeService,
		basicLocationService:     basicLocationService,
		basicPostcodeService:     basicPostcodeService,
		operationPostcodeService: operationPostcodeService,
		operationLocationService: operationLocationService,
		areaRouteService:         areaRouteService,
	}
}

func (controller *adminLineBasicServiceableConfController) CreateBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateOrUpdateBasicServiceableConfRequest)

	if err := checkAndFillCreateOrUpdateParam(request); err != nil {
		return nil, err
	}

	s, err := controller.service.CreateBasicServiceableConf(c, request, true)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (controller *adminLineBasicServiceableConfController) UpdateBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateOrUpdateBasicServiceableConfRequest)

	if err := checkAndFillCreateOrUpdateParam(request); err != nil {
		return nil, err
	}

	s, err := controller.service.UpdateBasicServiceableConf(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (controller *adminLineBasicServiceableConfController) SimplePutBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.SimplePutBasicServiceableConfRequest)
	var createOrUpdateRequest basic_serviceable.CreateOrUpdateBasicServiceableConfRequest
	_ = copier.Copy(&createOrUpdateRequest, request)
	defaultZero := uint8(0)
	createOrUpdateRequest.IsCheckRoute = &defaultZero
	createOrUpdateRequest.DefaultPickupEnabled = &defaultZero
	createOrUpdateRequest.DefaultCodPickupEnabled = &defaultZero
	createOrUpdateRequest.DefaultDeliverEnabled = &defaultZero
	createOrUpdateRequest.DefaultCodDeliverEnabled = &defaultZero
	createOrUpdateRequest.DefaultPdpPostcode = &defaultZero
	createOrUpdateRequest.OriginServiceableType = 1
	createOrUpdateRequest.DestServiceableType = 1
	createOrUpdateRequest.IsCheckBasicServiceable = &defaultZero
	createOrUpdateRequest.IsCheckOperationServiceable = &defaultZero
	createOrUpdateRequest.IsCheckPredefinedRoute = defaultZero

	if err := checkAndFillCreateOrUpdateParam(&createOrUpdateRequest); err != nil {
		return nil, err
	}

	s, err := controller.service.SimplePutBasicServiceableConf(c, &createOrUpdateRequest)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (controller *adminLineBasicServiceableConfController) GetBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.LineIdRequest)

	s, err := controller.service.GetBasicServiceableConfByLineId(c, request.LineId)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (controller *adminLineBasicServiceableConfController) BatchGetBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.LineIdsRequest)

	s, err := controller.service.GetBasicServiceableConfByLineIds(c, request.LineIds)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"list": s,
	}, nil
}

func (controller *adminLineBasicServiceableConfController) GetBasicServiceableConfList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.BasicServiceableConfListRequest)

	s, err := controller.service.GetBasicServiceableConfList(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (controller *adminLineBasicServiceableConfController) DeleteBasicServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.LineIdRequest)

	err := controller.service.DeleteBasicServiceableConf(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

func checkAndFillCreateOrUpdateParam(param *basic_serviceable.CreateOrUpdateBasicServiceableConfRequest) *lcos_error.LCOSError {
	var defaultMinValue uint32 = 0
	var defaultMaxValue uint32 = math.MaxUint32
	defaultMinDistanceOperator := constant.GreatThanOrEqual
	defaultMaxDistanceOperator := constant.LessThanOrEqual
	if param.MinDistance == nil {
		param.MinDistance = &defaultMinValue
	}
	if param.MaxDistance == nil {
		param.MaxDistance = &defaultMaxValue
	}
	if param.MinDistanceOperator == nil {
		param.MinDistanceOperator = &defaultMinDistanceOperator
	} else {
		if *param.MinDistanceOperator != constant.GreatThan && *param.MinDistanceOperator != constant.GreatThanOrEqual {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "min distance operator must be 1 or 2")
		}
	}
	if param.MaxDistanceOperator == nil {
		param.MaxDistanceOperator = &defaultMaxDistanceOperator
	} else {
		if *param.MaxDistanceOperator != constant.LessThan && *param.MaxDistanceOperator != constant.LessThanOrEqual {
			return lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "max distance operator must be 3 or 4")
		}
	}

	for _, businessAbility := range param.BusinessAbility {
		if businessAbility.MaxCapacity == nil {
			businessAbility.MaxCapacity = &defaultMinValue
		}
	}

	return nil
}

func (controller *adminLineBasicServiceableConfController) CheckCollectDeliverGroupWithLine(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CheckCollectDeliverGroupRequest)

	return controller.service.CheckCollectDeliverGroupWithLine(c, request.LineId, request.GroupId)
}

func (controller *adminLineBasicServiceableConfController) ExportLinesUsingLocationChecked(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.ExportLinesUsingLocationChecked)

	models, err := controller.service.GetAllLinesUsingLocationCheckedList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data err is :"+err.Msg)
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawOfSACheckList{
			LineIDAndName:  model.LineId + " " + model.LineName,
			ProductID:      model.ProductID,
			ValidatedLevel: model.ValidatedLevel,
			ErrorMessage:   model.ErrorMessage,
		})
	}

	fileName := "Serviceabl_Area_check_list.xlsx"
	var excelTitles = []string{
		"Line id+Line name", "Product id", "Product validation level", "Error Message",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "Sheet1", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

}

func (controller *adminLineBasicServiceableConfController) DeleteAllServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.LineIdRequest)
	logger.LogInfof("try delete all serviceableConf! line_id:%s, user:%s", request.LineId, c.GetUserEmail())
	err := controller.service.DeleteBasicServiceableConf(c, request)
	if err != nil {
		return nil, err
	}
	err = controller.basicCepRangeService.DeleteBasicServiceableCepRange(c, map[string]interface{}{"line_id": request.LineId})
	if err != nil {
		return nil, err
	}
	err = controller.operatorCepRangeService.DeleteOperationServiceableCepRange(c, map[string]interface{}{"line_id": request.LineId})
	if err != nil {
		return nil, err
	}

	err = controller.basicLocationService.DeleteBasicServiceableLocationByLineId(c, request.LineId)
	if err != nil {
		return nil, err
	}

	err = controller.basicPostcodeService.DeleteBasicServiceablePostcodeByLineId(c, request.LineId)
	if err != nil {
		return nil, err
	}

	err = controller.operationPostcodeService.DeleteOperationServiceablePostcodeByLineId(c, request.LineId)
	if err != nil {
		return nil, err
	}

	err = controller.operationLocationService.DeleteOperationServiceableLocationByLineId(c, request.LineId)
	if err != nil {
		return nil, err
	}

	err = controller.areaRouteService.DeleteLineRouteByLineId(c, request.LineId)
	if err != nil {
		return nil, err
	}

	return nil, nil

}
