package basic_cep_range

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/basic_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_cep_range"
	"github.com/tealeg/xlsx"
)

type AdminLineBasicServiceableCepRangeController interface {
	CreateBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{})
}

type adminLineBasicServiceableCepRangeController struct {
	service service.LineBasicServiceableCepRangeServiceInterface
}

func NewAdminLineBasicServiceableCepRangeController(m service.LineBasicServiceableCepRangeServiceInterface) *adminLineBasicServiceableCepRangeController {
	return &adminLineBasicServiceableCepRangeController{service: m}
}

func (a *adminLineBasicServiceableCepRangeController) CreateBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.BasicCepRangeCreateRequest)
	return a.service.CreateBasicServiceableCepRange(c, request)
}

func (a *adminLineBasicServiceableCepRangeController) UpdateBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.BasicCepRangeUpdateRequest)
	return a.service.UpdateBasicServiceableCepRange(c, request)
}

func (a *adminLineBasicServiceableCepRangeController) ListBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.BasicCepRangeListRequest)
	return a.service.ListBasicServiceableCepRange(c, request)
}

func (a *adminLineBasicServiceableCepRangeController) DeleteBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	return nil, a.service.DeleteBasicServiceableCepRange(c, map[string]interface{}{"id": request.ID})
}

func (a *adminLineBasicServiceableCepRangeController) UploadBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)
	return nil, a.service.UploadBasicServiceableCepRange(c, request)
}

func (a *adminLineBasicServiceableCepRangeController) DownloadBasicServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.BasicCepRangeDownloadRequest)
	paramMap, _ := utils.Struct2map(request)
	if request.CepCode != nil {
		paramMap["initial <="] = request.CepCode
		paramMap["final >="] = request.CepCode
	}
	models, err := a.service.GetAllBasicServiceableCepRangeList(c, paramMap)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, model)
	}

	fileName := "basic_cep_range.xlsx"
	var excelTitles = []string{
		"*Line ID", "*Group ID", "*Region", "*CEP Initial", "*CEP Final", "*Support Pickup", "*Support COD Pickup", "*Support Deliver", "*Support COD Deliver", "*Support Trade In Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}
