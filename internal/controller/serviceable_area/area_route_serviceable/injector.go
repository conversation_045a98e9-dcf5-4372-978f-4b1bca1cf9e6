package area_route_serviceable

import "github.com/google/wire"

/*
* @Author: yajun.han
* @Date: 2020/8/5 12:05 上午
* @Name：area
* @Description:
 */

var AdminLineServiceableAreaProviderSet = wire.NewSet(
	NewAdminLineServiceableAreaController,
	wire.Bind(new(AdminLineServiceableAreaController), new(*adminLineServiceableAreaController)),
)

var AdminLineServiceableRouteProviderSet = wire.NewSet(
	NewAdminLineServiceableRouteController,
	wire.Bind(new(AdminLineServiceableRouteController), new(*adminLineServiceableRouteController)),
)
