package area_route_serviceable

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/area_route_serviceable"
	"github.com/tealeg/xlsx"
)

/*
* @Author: yajun.han
* @Date: 2020/8/5 12:50 上午
* @Name：area_route_serviceable
* @Description:
 */

type AdminLineServiceableAreaController interface {
	Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetAll(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListAreaLocationRef(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteAreaLocationRef(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadAreaLocationRef(c *utils.HttpContext, rawRequest interface{})
	UploadAreaLocationRef(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminLineServiceableAreaController struct {
	service service.LineServiceableAreaServiceInterface
}

func NewAdminLineServiceableAreaController(m service.LineServiceableAreaServiceInterface) *adminLineServiceableAreaController {
	return &adminLineServiceableAreaController{service: m}
}

func (controller *adminLineServiceableAreaController) Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.CreateAreaRequest)
	area, err := controller.service.Create(c, request)
	if err != nil {
		return nil, err
	}
	return area, nil
}
func (controller *adminLineServiceableAreaController) Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	return nil, controller.service.Delete(c, request)
}
func (controller *adminLineServiceableAreaController) GetByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	return controller.service.GetByID(c, request)
}
func (controller *adminLineServiceableAreaController) List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.SearchAreaRequest)
	return controller.service.List(c, request)
}
func (controller *adminLineServiceableAreaController) GetAll(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.GetAllAreaRequest)
	return controller.service.GetAll(c, request)
}
func (controller *adminLineServiceableAreaController) ListAreaLocationRef(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.SearchAreaLocationRefRequest)
	return controller.service.ListAreaRef(c, request)
}
func (controller *adminLineServiceableAreaController) DeleteAreaLocationRef(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.DeleteAreaLocationRef)
	return nil, controller.service.DeleteAreaRef(c, request)
}
func (controller *adminLineServiceableAreaController) DownloadAreaLocationRef(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*area_route_serviceable.GetAllAreaLocationRefRequest)

	models, err := controller.service.GetAllAreaRef(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawOfAreaLocation{
			AreaId:     int(model.AreaID),
			Region:     model.Region,
			LocationId: int(model.LocationID),
			State:      model.State,
			City:       model.City,
			District:   model.District,
		})
	}

	fileName := "area_location.xlsx"
	var excelTitles = []string{
		"Area ID", "Region", "Location ID", "State", "City", "District",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (controller *adminLineServiceableAreaController) UploadAreaLocationRef(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)
	return nil, controller.service.UploadAreaRef(c, request)
}
