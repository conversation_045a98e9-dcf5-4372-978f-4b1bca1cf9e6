package area_route_serviceable

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/area_route_serviceable"
	"github.com/tealeg/xlsx"
)

/*
* @Author: yajun.han
* @Date: 2020/8/5 12:50 上午
* @Name：area_route_serviceable
* @Description:
 */

type AdminLineServiceableRouteController interface {
	Create(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Delete(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Update(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetByID(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	List(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	CreateLineRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteLineRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateLineRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetLineRouteByID(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListLineRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	UploadLineRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadLineRoute(ctx *utils.HttpContext, rawRequest interface{})
}

type adminLineServiceableRouteController struct {
	service service.LineServiceableRouteServiceInterface
}

func NewAdminLineServiceableRouteController(m service.LineServiceableRouteServiceInterface) *adminLineServiceableRouteController {
	return &adminLineServiceableRouteController{service: m}
}

func (controller *adminLineServiceableRouteController) CreateLineRoute(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.CreateLineRouteRequest)
	area, err := controller.service.CreateLineRoute(c, request)
	if err != nil {
		return nil, err
	}
	return area, nil
}

func (controller *adminLineServiceableRouteController) DeleteLineRoute(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.NewIDRequest)
	return nil, controller.service.DeleteLineRoute(c, request)
}

func (controller *adminLineServiceableRouteController) UpdateLineRoute(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.UpdateLineRouteRequest)
	area, err := controller.service.UpdateLineRoute(c, request)
	if err != nil {
		return nil, err
	}
	return area, nil
}

func (controller *adminLineServiceableRouteController) GetLineRouteByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.IDLineRouteRequest)
	return controller.service.GetLineRouteByID(c, request)
}

func (controller *adminLineServiceableRouteController) ListLineRoute(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.SearchLineRouteRequest)
	return controller.service.ListLineRoute(c, request)
}

// Deprecated
func (controller *adminLineServiceableRouteController) Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.CreateRouteRequest)
	area, err := controller.service.Create(c, request)
	if err != nil {
		return nil, err
	}
	return area, nil
}

// Deprecated
func (controller *adminLineServiceableRouteController) Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	return nil, controller.service.Delete(c, request)
}

// Deprecated
func (controller *adminLineServiceableRouteController) Update(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.UpdateRouteRequest)
	area, err := controller.service.Update(c, request)
	if err != nil {
		return nil, err
	}
	return area, nil
}

// Deprecated
func (controller *adminLineServiceableRouteController) GetByID(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	return controller.service.GetByID(c, request)
}

// Deprecated
func (controller *adminLineServiceableRouteController) List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.SearchRouteRequest)
	return controller.service.List(c, request)
}

func (controller *adminLineServiceableRouteController) UploadLineRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*area_route_serviceable.UploadRouteRequest)
	return nil, controller.service.Upload(ctx, request)
}
func (controller *adminLineServiceableRouteController) DownloadLineRoute(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*area_route_serviceable.SearchAllLineRouteRequest)
	models, lcosErr := controller.service.GetRouteDataWithoutPaging(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lcosErr.Msg)
		return
	}
	routeType := *request.RouteType
	fileName := "serviceable-basic-zone-route-export.xlsx"
	excelTiles := []string{"Category", "Line ID", "Group ID", "Type", "From Area/Zone Area", "To Area"}
	var excelRawData []interface{}

	for _, data := range models {
		tmpExcelData := &excelRawOfRoute{
			LineID:   data.LineID,
			GroupID:  data.CollectDeliverGroupId,
			FromArea: data.FromAreaName,
			ToArea:   data.ToAreaName,
		}
		if routeType == constant.ROUTE_SUPPORTED {
			tmpExcelData.RouteType = "Supported"
		} else {
			tmpExcelData.RouteType = "Unsupported"
		}
		if data.FromAreaID == data.ToAreaID {
			tmpExcelData.Category = "Zone"
		} else {
			tmpExcelData.Category = "Route"
		}
		excelRawData = append(excelRawData, tmpExcelData)
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTiles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}
