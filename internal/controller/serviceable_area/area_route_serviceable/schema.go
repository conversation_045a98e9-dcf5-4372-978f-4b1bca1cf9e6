package area_route_serviceable

type excelRawOfAreaLocation struct {
	AreaId     int    `excel:"title:Area ID"`
	Region     string `excel:"title:Region"`
	LocationId int    `excel:"title:Location ID"`
	State      string `excel:"title:State"`
	City       string `excel:"title:City"`
	District   string `excel:"title:District"`
	Street     string `excel:"title:Street"`
}

type excelRawOfRoute struct {
	Category  string `excel:"title:Category"`
	LineID    string `excel:"title:Line ID"`
	GroupID   string `excel:"title:Group ID"`
	RouteType string `excel:"title:Type"`
	FromArea  string `excel:"title:From Area/Zone Area"`
	ToArea    string `excel:"title:To Area"`
}
