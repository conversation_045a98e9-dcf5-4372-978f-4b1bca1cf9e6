package operation_cep_range

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_cep_range"
	"github.com/tealeg/xlsx"
)

type AdminLineOperationServiceableCepRangeController interface {
	CreateOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{})
}

type adminLineOperationServiceableCepRangeController struct {
	service service.LineOperationServiceableCepRangeServiceInterface
}

func NewAdminLineOperationServiceableCepRangeController(m service.LineOperationServiceableCepRangeServiceInterface) *adminLineOperationServiceableCepRangeController {
	return &adminLineOperationServiceableCepRangeController{service: m}
}

func (a *adminLineOperationServiceableCepRangeController) CreateOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.OperationCepRangeCreateRequest)
	return a.service.CreateOperationServiceableCepRange(c, request)
}

func (a *adminLineOperationServiceableCepRangeController) UpdateOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.OperationCepRangeUpdateRequest)
	return a.service.UpdateOperationServiceableCepRange(c, request)
}

func (a *adminLineOperationServiceableCepRangeController) ListOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.OperationCepRangeListRequest)
	return a.service.ListOperationServiceableCepRange(c, request)
}

func (a *adminLineOperationServiceableCepRangeController) DeleteOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	return nil, a.service.DeleteOperationServiceableCepRange(c, map[string]interface{}{"id": request.ID})
}

func (a *adminLineOperationServiceableCepRangeController) UploadOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)
	return nil, a.service.UploadOperationServiceableCepRange(c, request)
}

func (a *adminLineOperationServiceableCepRangeController) DownloadOperationServiceableCepRange(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*operation_serviceable.OperationCepRangeDownloadRequest)
	paramMap, _ := utils.Struct2map(request)
	if request.CepCode != nil {
		paramMap["initial <="] = request.CepCode
		paramMap["final >="] = request.CepCode
	}
	models, err := a.service.GetAllOperationServiceableCepRangeList(c, paramMap)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, model)
	}
	fileName := "operation_cep_range.xlsx"
	var excelTitles = []string{
		"*Line ID", "*Group ID", "*Region", "*CEP Initial", "*CEP Final", "*Ban Pickup", "*Ban COD Pickup", "*Ban Deliver", "*Ban COD Deliver", "*Ban Trade In Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}
