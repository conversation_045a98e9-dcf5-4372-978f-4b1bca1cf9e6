package operation_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_conf"
)

type AdminLineOperationServiceableConfController interface {
	CreateOperationServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOperationServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetOperationServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetOperationServiceableConfList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOperationServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminLineOperationServiceableConfController struct {
	service service.LineOperationServiceableConfServiceInterface
}

func NewAdminLineOperationServiceableConfController(m service.LineOperationServiceableConfServiceInterface) *adminLineOperationServiceableConfController {
	return &adminLineOperationServiceableConfController{service: m}
}

func (controller *adminLineOperationServiceableConfController) CreateOperationServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.CreateOrUpdateOperationServiceableConfRequest)
	s, err := controller.service.CreateOperationServiceableConf(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (controller *adminLineOperationServiceableConfController) UpdateOperationServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.CreateOrUpdateOperationServiceableConfRequest)
	s, err := controller.service.UpdateOperationServiceableConf(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (controller *adminLineOperationServiceableConfController) GetOperationServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.LineIdRequest)
	s, err := controller.service.GetOperationServiceableConfByLineID(c, request.LineId)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (controller *adminLineOperationServiceableConfController) GetOperationServiceableConfList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.OperationServiceableConfListRequest)
	s, err := controller.service.GetOperationServiceableConfList(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (controller *adminLineOperationServiceableConfController) DeleteOperationServiceableConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.LineIdRequest)
	err := controller.service.DeleteOperationServiceableConf(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
