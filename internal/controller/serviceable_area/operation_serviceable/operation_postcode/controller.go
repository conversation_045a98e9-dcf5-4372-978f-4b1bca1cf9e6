package operation_postcode

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_postcode"
	"github.com/tealeg/xlsx"
)

type AdminLineOperationServiceablePostcodeController interface {
	CreateOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CreateOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetOperationServiceableOriginPostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetOperationServiceableDestPostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{})
	DownloadOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{})
	UploadOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	CreateOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetOperationServiceablePostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{})
	UploadOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SyncSearchPostcodeToBasicserviceable(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminLineOperationServiceablePostcodeController struct {
	service service.LineOperationServiceablePostcodeServiceInterface
}

func NewAdminLineOperationServiceablePostcodeController(m service.LineOperationServiceablePostcodeServiceInterface) *adminLineOperationServiceablePostcodeController {
	return &adminLineOperationServiceablePostcodeController{service: m}
}

func (api *adminLineOperationServiceablePostcodeController) CreateOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.CreateOperationPostcodeRequest)
	s, err := api.service.CreateOperationServiceablePostcode(c, request)
	if err != nil {
		return nil, err
	}
	return s, err
}

func (api *adminLineOperationServiceablePostcodeController) UpdateOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.UpdateOperationPostcodeRequest)
	s, err := api.service.UpdateOperationServiceablePostcode(c, request)
	if err != nil {
		return nil, err
	}
	return s, err
}

func (api *adminLineOperationServiceablePostcodeController) GetOperationServiceablePostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.GetOperationPostcodeListRequest)
	s, err := api.service.GetOperationServiceablePostcodeList(c, request)
	if err != nil {
		return nil, err
	}
	return s, err
}

func (api *adminLineOperationServiceablePostcodeController) DeleteOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	err := api.service.DeleteOperationServiceablePostcode(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (api *adminLineOperationServiceablePostcodeController) DownloadOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*operation_serviceable.GetAllOperationPostcodeRequest)

	models, err := api.service.GetAllOperationServiceablePostcodeList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawOfPostcode{
			LineId:                model.LineID,
			Region:                model.Region,
			CollectDeliverGroupId: model.CollectDeliverGroupId,
			Postcode:              model.Postcode,
			DisablePickup:         int(*model.DisablePickup),
			DisableCodPickup:      int(*model.DisableCodPickup),
			DisableDeliver:        int(*model.DisableDeliver),
			DisableCodDeliver:     int(*model.DisableCodDeliver),
			DisableTradeIn:        int(model.DisableTradeIn),
		})
	}

	fileName := "operation_postcode.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Collect Deliver Group Id", "Postcode", "Ban Pickup", "Ban COD Pickup", "Ban Deliver", "Ban COD Deliver", "Ban Trade In Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (api *adminLineOperationServiceablePostcodeController) UploadOperationServiceablePostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)
	err := api.service.UploadOperationServiceablePostcode(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (api *adminLineOperationServiceablePostcodeController) SyncSearchPostcodeToBasicserviceable(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.GetAllOperationPostcodeRequest)
	err := api.service.SyncSearchPostcodeToBasicserviceable(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) CreateOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.CreateOperationOriginPostcodeRequest)
	s, err := api.service.CreateOperationServiceableOriginPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) CreateOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.CreateOperationDestPostcodeRequest)
	s, err := api.service.CreateOperationServiceableDestPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) UpdateOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.UpdateOperationOriginPostcodeRequest)
	s, err := api.service.UpdateOperationServiceableOriginPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) UpdateOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.UpdateOperationDestPostcodeRequest)
	s, err := api.service.UpdateOperationServiceableDestPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) GetOperationServiceableOriginPostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.GetOperationOriginPostcodeListRequest)
	s, err := api.service.GetOperationServiceableOriginPostcodeList(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) GetOperationServiceableDestPostcodeList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.GetOperationDestPostcodeListRequest)
	s, err := api.service.GetOperationServiceableDestPostcodeList(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) DeleteOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	err := api.service.DeleteOperationServiceableOriginPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) DeleteOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	err := api.service.DeleteOperationServiceableDestPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) DownloadOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*operation_serviceable.GetAllOperationOriginPostcodeRequest)

	models, err := api.service.GetAllOperationServiceableOriginPostcodeList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		collectServiceType := ""
		if model.CollectServiceType == uint64(constant.PICKUP) {
			collectServiceType = "Pickup"
		} else {
			collectServiceType = "B2C"
		}
		excelRawData = append(excelRawData, &excelRawOfOriginPostcode{
			LineId:             model.LineID,
			Region:             model.Region,
			CollectServiceType: collectServiceType,
			Postcode:           model.Postcode,
			DisablePickup:      int(*model.DisablePickup),
			DisableCodPickup:   int(*model.DisableCodPickup),
		})
	}

	fileName := "operation_origin_postcode.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Collect Service Type", "Postcode", "Ban Pickup", "Ban COD Pickup",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) DownloadOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*operation_serviceable.GetAllOperationDestPostcodeRequest)

	models, err := api.service.GetAllOperationServiceableDestPostcodeList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		deliverServiceType := ""
		if model.DeliverServiceType == uint64(constant.PICKUP) {
			deliverServiceType = "Pickup"
		} else {
			deliverServiceType = "B2C"
		}
		excelRawData = append(excelRawData, &excelRawOfDestPostcode{
			LineId:             model.LineID,
			Region:             model.Region,
			DeliverCollectType: deliverServiceType,
			Postcode:           model.Postcode,
			DisableDeliver:     int(*model.DisableDeliver),
			DisableCodDeliver:  int(*model.DisableCodDeliver),
		})
	}

	fileName := "operation_dest_postcode.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Deliver Service Type", "Postcode", "Ban Deliver", "Ban COD Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) UploadOperationServiceableOriginPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)

	err := api.service.UploadOperationServiceableOriginPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (api *adminLineOperationServiceablePostcodeController) UploadOperationServiceableDestPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)

	err := api.service.UploadOperationServiceableDestPostcode(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
