package operation_postcode

type excelRawOfPostcode struct {
	LineId                string `excel:"title:Line ID"`
	Region                string `excel:"title:Region"`
	CollectDeliverGroupId string `excel:"title:Collect Deliver Group Id"`
	Postcode              string `excel:"title:Postcode"`
	DisablePickup         int    `excel:"title:Ban Pickup"`
	DisableCodPickup      int    `excel:"title:Ban COD Pickup"`
	DisableDeliver        int    `excel:"title:Ban Deliver"`
	DisableCodDeliver     int    `excel:"title:Ban COD Deliver"`
	DisableTradeIn        int    `excel:"title:Ban Trade In Deliver"`
}

// Deprecated
type excelRawOfOriginPostcode struct {
	LineId             string `excel:"title:Line ID"`
	Region             string `excel:"title:Region"`
	CollectServiceType string `excel:"title:Collect Service Type"`
	Postcode           string `excel:"title:Postcode"`
	DisablePickup      int    `excel:"title:Ban Pickup"`
	DisableCodPickup   int    `excel:"title:Ban COD Pickup"`
}

// Deprecated
type excelRawOfDestPostcode struct {
	LineId             string `excel:"title:Line ID"`
	Region             string `excel:"title:Region"`
	DeliverCollectType string `excel:"title:Deliver Service Type"`
	Postcode           string `excel:"title:Postcode"`
	DisableDeliver     int    `excel:"title:Ban Deliver"`
	DisableCodDeliver  int    `excel:"title:Ban COD Deliver"`
}
