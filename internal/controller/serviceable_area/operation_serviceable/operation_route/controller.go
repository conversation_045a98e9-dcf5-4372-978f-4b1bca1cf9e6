package operation_route

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_route"
	"github.com/tealeg/xlsx"
)

type AdminLineOperationRouteController interface {
	CreateOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetLineOperationRouteByID(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListLineOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadLineOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadLineOperationRoute(ctx *utils.HttpContext, rawRequest interface{})
	SyncSearchRouteToBasicServiceable(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminLineOperationRouteController struct {
	service service.LineOperationRouteServiceInterface
}

func (controller *adminLineOperationRouteController) CreateOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.CreateOperationRouteRequest)
	s, err := controller.service.CreateOperationRoute(ctx, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (controller *adminLineOperationRouteController) UpdateOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.UpdateLineOperationRouteRequest)
	s, err := controller.service.UpdateOperationRoute(ctx, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (controller *adminLineOperationRouteController) DeleteOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	err := controller.service.DeleteOperationRoute(ctx, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (controller *adminLineOperationRouteController) GetLineOperationRouteByID(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	s, err := controller.service.GetOperationRoute(ctx, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (controller *adminLineOperationRouteController) ListLineOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.SearchLineOperationRouteRequest)
	models, err := controller.service.GetOperationRouteList(ctx, request)
	if err != nil {
		return nil, err
	}
	return models, nil
}

func (controller *adminLineOperationRouteController) UploadLineOperationRoute(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.UploadOperationRouteRequest)
	err := controller.service.UploadOperationRoute(ctx, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (controller *adminLineOperationRouteController) DownloadLineOperationRoute(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*operation_serviceable.SearchAllLineOperationRouteRequest)
	models, err := controller.service.GetAllOperationRouteList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	// parse data into excel file
	var excelRawData []interface{}
	for _, data := range models {
		tmpRawRoute := &excelRawOfOpsRoute{
			LineID:          data.LineID,
			GroupID:         data.CollectDeliverGroupId,
			Region:          data.Region,
			DisableFromArea: data.DisableFromAreaName,
			DisableToArea:   data.DisableToAreaName,
		}
		if data.DisableFromAreaID == data.DisableToAreaID {
			tmpRawRoute.Category = "Zone"
		} else {
			tmpRawRoute.Category = "Route"
		}
		excelRawData = append(excelRawData, tmpRawRoute)
	}
	fileName := "serviceable-operation-zone-route-export.xlsx"
	var excelTitles = []string{
		"Category", "Line ID", "Group ID", "Region", "Ban From Area", "Ban To Area",
	}
	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()
	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

}

func (controller *adminLineOperationRouteController) SyncSearchRouteToBasicServiceable(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.SearchAllLineOperationRouteRequest)
	err := controller.service.SyncSearchOperationRouteToBasicRoute(ctx, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func NewAdminLineOperationRouteController(m service.LineOperationRouteServiceInterface) *adminLineOperationRouteController {
	return &adminLineOperationRouteController{
		service: m,
	}
}
