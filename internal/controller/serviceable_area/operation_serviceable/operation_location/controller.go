package operation_location

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/operation_serviceable"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_location"
	"github.com/tealeg/xlsx"
)

type AdminLineOperationServiceableLocationController interface {
	CreateOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchCreateOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CreateOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchCreateOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetOperationServiceableOriginLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetOperationServiceableDestLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{})
	DownloadOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{})
	UploadOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	CreateOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchCreateOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetOperationServiceableLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DownloadOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{})
	UploadOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SyncSearchLocationToBasicserviceable(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SearchAllFourLevelAddress(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminLineOperationServiceableLocationController struct {
	service service.LineOperationServiceableLocationServiceInterface
}

func NewAdminLineOperationServiceableLocationController(m service.LineOperationServiceableLocationServiceInterface) *adminLineOperationServiceableLocationController {
	return &adminLineOperationServiceableLocationController{service: m}
}

func (controller *adminLineOperationServiceableLocationController) CreateOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.CreateOperationLocationRequest)
	s, err := controller.service.CreateOperationServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func (controller *adminLineOperationServiceableLocationController) BatchCreateOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.BatchCreateOperationLocationRequest)
	s, err := controller.service.BatchCreateOperationServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (controller *adminLineOperationServiceableLocationController) UpdateOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.UpdateOperationLocationRequest)
	s, err := controller.service.UpdateOperationServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil

}

func (controller *adminLineOperationServiceableLocationController) GetOperationServiceableLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.GetOperationLocationListRequest)
	s, err := controller.service.GetOperationServiceableLocationList(c, request)
	if err != nil {
		return s, nil
	}
	return s, nil
}

func (controller *adminLineOperationServiceableLocationController) DeleteOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	err := controller.service.DeleteOperationServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (controller *adminLineOperationServiceableLocationController) DownloadOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*operation_serviceable.GetAllOperationLocationRequest)
	models, err := controller.service.GetAllOperationServiceableLocationList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawofLocation{
			LineId:                model.LineID,
			Region:                model.Region,
			CollectDeliverGroupId: model.CollectDeliverGroupId,
			LocationId:            int(model.LocationID),
			State:                 model.State,
			City:                  model.City,
			District:              model.District,
			Street:                model.Street,
			DisablePickup:         int(*model.DisablePickup),
			DisableCodPickup:      int(*model.DisableCodPickup),
			DisableDeliver:        int(*model.DisableDeliver),
			DisableCodDeliver:     int(*model.DisableCodDeliver),
			DisableTradeIn:        int(model.DisableTradeIn),
		})
	}

	fileName := "operation_location.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Collect Deliver Group Id", "Location ID", "State", "City", "District", "Street", "Ban Pickup", "Ban COD Pickup", "Ban Deliver", "Ban COD Deliver", "Ban Trade In Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (controller *adminLineOperationServiceableLocationController) UploadOperationServiceableLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)
	err := controller.service.UploadOperationServiceableLocation(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (controller *adminLineOperationServiceableLocationController) SyncSearchLocationToBasicserviceable(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.GetAllOperationLocationRequest)
	err := controller.service.SyncSearchLocationToBasicserviceable(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (controller *adminLineOperationServiceableLocationController) SearchAllFourLevelAddress(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.SearchAllFourLevelAddressRequest)
	s, err := controller.service.SearchAllFourLevelAddress(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) CreateOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.CreateOperationOriginLocationRequest)
	s, err := controller.service.CreateOperationServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) BatchCreateOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.BatchCreateOperationOriginLocationRequest)
	s, err := controller.service.BatchCreateOperationServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) CreateOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.CreateOperationDestLocationRequest)
	s, err := controller.service.CreateOperationServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) BatchCreateOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.BatchCreateOperationDestLocationRequest)
	s, err := controller.service.BatchCreateOperationServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) UpdateOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.UpdateOperationOriginLocationRequest)
	s, err := controller.service.UpdateOperationServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) UpdateOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.UpdateOperationDestLocationRequest)
	s, err := controller.service.UpdateOperationServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) GetOperationServiceableOriginLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.GetOperationOriginLocationListRequest)
	s, err := controller.service.GetOperationServiceableOriginLocationList(c, request)
	if err != nil {
		return nil, err
	}
	return s, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) GetOperationServiceableDestLocationList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*operation_serviceable.GetOperationDestLocationListRequest)
	s, err := controller.service.GetOperationServiceableDestLocationList(c, request)
	if err != nil {
		return nil, err
	}

	return s, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) DeleteOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	err := controller.service.DeleteOperationServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) DeleteOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)
	err := controller.service.DeleteOperationServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) DownloadOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*operation_serviceable.GetAllOperationOriginLocationRequest)

	models, err := controller.service.GetAllOperationServiceableOriginLocationList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		collectServiceType := ""
		if model.CollectServiceType == uint64(constant.PICKUP) {
			collectServiceType = "Pickup"
		} else {
			collectServiceType = "B2C"
		}
		excelRawData = append(excelRawData, &excelRawOfOriginLocation{
			LineId:             model.LineID,
			Region:             model.Region,
			CollectServiceType: collectServiceType,
			LocationId:         int(model.LocationID),
			State:              model.State,
			City:               model.City,
			District:           model.District,
			Street:             model.Street,
			DisablePickup:      int(*model.DisablePickup),
			DisableCodPickup:   int(*model.DisableCodPickup),
		})
	}

	fileName := "operation_origin_location.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Collect Service Type", "Location ID", "State", "City", "District", "Street", "Ban Pickup", "Ban COD Pickup",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) DownloadOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*operation_serviceable.GetAllOperationDestLocationRequest)

	models, err := controller.service.GetAllOperationServiceableDestLocationList(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	var excelRawData []interface{}
	for _, model := range models {
		deliverServiceType := ""
		if model.DeliverServiceType == uint64(constant.PICKUP) {
			deliverServiceType = "Pickup"
		} else {
			deliverServiceType = "B2C"
		}
		excelRawData = append(excelRawData, &excelRawOfDestLocation{
			LineId:             model.LineID,
			Region:             model.Region,
			DeliverCollectType: deliverServiceType,
			LocationId:         int(model.LocationID),
			State:              model.State,
			City:               model.City,
			District:           model.District,
			Street:             model.Street,
			DisableDeliver:     int(*model.DisableDeliver),
			DisableCodDeliver:  int(*model.DisableCodDeliver),
		})
	}

	fileName := "operation_dest_location.xlsx"
	var excelTitles = []string{
		"Line ID", "Region", "Deliver Service Type", "Location ID", "State", "City", "District", "Street", "Ban Deliver", "Ban COD Deliver",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) UploadOperationServiceableOriginLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)

	err := controller.service.UploadOperationServiceableOriginLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Deprecated
func (controller *adminLineOperationServiceableLocationController) UploadOperationServiceableDestLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.UploadFileRequest)

	err := controller.service.UploadOperationServiceableDestLocation(c, request)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
