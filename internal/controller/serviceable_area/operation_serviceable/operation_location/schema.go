package operation_location

type excelRawofLocation struct {
	LineId                string `excel:"title:Line ID"`
	Region                string `excel:"title:Region"`
	CollectDeliverGroupId string `excel:"title:Collect Deliver Group Id"`
	LocationId            int    `excel:"title:Location ID"`
	State                 string `excel:"title:State"`
	City                  string `excel:"title:City"`
	District              string `excel:"title:District"`
	Street                string `excel:"title:Street"`
	DisablePickup         int    `excel:"title:Ban Pickup"`
	DisableCodPickup      int    `excel:"title:Ban COD Pickup"`
	DisableDeliver        int    `excel:"title:Ban Deliver"`
	DisableCodDeliver     int    `excel:"title:Ban COD Deliver"`
	DisableTradeIn        int    `excel:"title:Ban Trade In Deliver"`
}

// Deprecated
type excelRawOfOriginLocation struct {
	LineId             string `excel:"title:Line ID"`
	Region             string `excel:"title:Region"`
	CollectServiceType string `excel:"title:Collect Service Type"`
	LocationId         int    `excel:"title:Location ID"`
	State              string `excel:"title:State"`
	City               string `excel:"title:City"`
	District           string `excel:"title:District"`
	Street             string `excel:"title:Street"`
	DisablePickup      int    `excel:"title:Ban Pickup"`
	DisableCodPickup   int    `excel:"title:Ban COD Pickup"`
}

// Deprecated
type excelRawOfDestLocation struct {
	LineId             string `excel:"title:Line ID"`
	Region             string `excel:"title:Region"`
	DeliverCollectType string `excel:"title:Deliver Service Type"`
	LocationId         int    `excel:"title:Location ID"`
	State              string `excel:"title:State"`
	City               string `excel:"title:City"`
	District           string `excel:"title:District"`
	Street             string `excel:"title:Street"`
	DisableDeliver     int    `excel:"title:Ban Deliver"`
	DisableCodDeliver  int    `excel:"title:Ban COD Deliver"`
}
