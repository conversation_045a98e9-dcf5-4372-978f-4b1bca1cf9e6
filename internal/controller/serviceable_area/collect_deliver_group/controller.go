package collect_deliver_group

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/collect_deliver_group"
)

type CollectDeliverGroupController interface {
	// 获取所有collect deliver group
	GetAllCollectDeliverGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type collectDeliverGroupController struct {
	collectDeliverGroupService collect_deliver_group.CollectDeliverGroupServiceInterface
}

func NewCollectDeliverGroupController(collectDeliverGroupService collect_deliver_group.CollectDeliverGroupServiceInterface) *collectDeliverGroupController {
	return &collectDeliverGroupController{
		collectDeliverGroupService: collectDeliverGroupService,
	}
}

func (controller *collectDeliverGroupController) GetAllCollectDeliverGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	return controller.collectDeliverGroupService.GetAllCollectDeliverGroup(c)
}
