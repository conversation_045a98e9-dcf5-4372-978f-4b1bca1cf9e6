package geo_distance_controller

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/geo_distance_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/geo_distance_service"
	"git.garena.com/shopee/bg-logistics/logistics/sls-location-plugin/geo_distance"
)

type LogisticProductGeoDistanceController interface {
	CreateOrUpdateProductGeoDistanceConf(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteProductGeoDistanceConf(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetProductGeoDistanceConf(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	GetGeoDistance(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchGetGeoDistance(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type logisticProductGeoDistanceControllerImpl struct {
	service geo_distance_service.LogisticProductGeoDistanceService
}

func NewLogisticProductGeoDistanceController(service geo_distance_service.LogisticProductGeoDistanceService) *logisticProductGeoDistanceControllerImpl {
	return &logisticProductGeoDistanceControllerImpl{
		service: service,
	}
}

func (l *logisticProductGeoDistanceControllerImpl) CreateOrUpdateProductGeoDistanceConf(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*geo_distance_protocol.CreateOrUpdateProductGeoDistanceConfRequest)

	if err := l.service.CreateOrUpdateProductGeoDistanceConf(ctx, req.ProductId, req.GeoDistanceMode, req.GeoDistanceAvoid); err != nil {
		return nil, err
	}
	return nil, nil
}

func (l *logisticProductGeoDistanceControllerImpl) DeleteProductGeoDistanceConf(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*geo_distance_protocol.DeleteProductGeoDistanceConfRequest)

	if err := l.service.DeleteProductGeoDistanceConf(ctx, req.ProductId); err != nil {
		return nil, err
	}
	return nil, nil
}

func (l *logisticProductGeoDistanceControllerImpl) GetProductGeoDistanceConf(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*geo_distance_protocol.GetProductGeoDistanceConfRequest)

	return l.service.GetProductGeoDistanceConf(ctx, req.ProductId)
}

func (l *logisticProductGeoDistanceControllerImpl) GetGeoDistance(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*geo_distance.GetGeoDistanceRequest)

	return l.service.GetGeoDistance(ctx, req)
}

func (l *logisticProductGeoDistanceControllerImpl) BatchGetGeoDistance(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*geo_distance.BatchGetGeoDistanceRequest)

	return l.service.BatchGetGeoDistance(ctx, req)
}
