package site_serviceable_area_postcode

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_postcode"
	"github.com/tealeg/xlsx"
)

type AdminSiteServiceableAreaPostcodeControllerInterface interface {
	CreateSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{})
}

type adminSiteServiceableAreaPostcodeController struct {
	siteServiceableAreaPostcodeService site_serviceable_area_postcode.SiteServiceableAreaPostcodeInterface
}

func (a *adminSiteServiceableAreaPostcodeController) UploadSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UploadSiteServiceableAreaPostcodeRequest)
	return nil, a.siteServiceableAreaPostcodeService.UploadSiteServiceableAreaPostcode(c, request)
}

func (a *adminSiteServiceableAreaPostcodeController) ExportSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.ExportSiteServiceableAreaPostcode)
	models, lcosErr := a.siteServiceableAreaPostcodeService.ListAllSiteServiceableAreaPostcode(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}
	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawOfSSAPostcode{
			SiteID:        model.SiteId,
			ActualPointID: model.ActualPointId,
			Region:        model.Region,
			Postcode:      model.Postcode,
		})
	}

	fileName := fmt.Sprintf("site_serviceable_area_postcode_%s.xlsx", *request.SiteID)
	var excelTitles = []string{
		"Site ID", "Actual Point ID", "Region", "Postcode",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "Sheet1", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (a *adminSiteServiceableAreaPostcodeController) CreateSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateSiteServiceableAreaPostcode)
	return nil, a.siteServiceableAreaPostcodeService.CreateSiteServiceableAreaPostcode(c, request)
}

func (a *adminSiteServiceableAreaPostcodeController) ListSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.ListSiteServiceableAreaPostcode)
	return a.siteServiceableAreaPostcodeService.ListSiteServiceableAreaPostcode(c, request)
}

func (a *adminSiteServiceableAreaPostcodeController) DeleteSiteServiceableAreaPostcode(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteSiteServiceableAreaPostcode)
	return nil, a.siteServiceableAreaPostcodeService.DeleteSiteServiceableAreaPostcode(c, request.ID, request.SiteID)
}

func NewAdminSiteServiceableAreaPostcodeController(siteServiceableAreaPostcodeService site_serviceable_area_postcode.SiteServiceableAreaPostcodeInterface) *adminSiteServiceableAreaPostcodeController {
	return &adminSiteServiceableAreaPostcodeController{siteServiceableAreaPostcodeService: siteServiceableAreaPostcodeService}
}

var _ AdminSiteServiceableAreaPostcodeControllerInterface = (*adminSiteServiceableAreaPostcodeController)(nil)
