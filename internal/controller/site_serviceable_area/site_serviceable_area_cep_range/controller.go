package site_serviceable_area_cep_range

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_cep_range"
	"github.com/tealeg/xlsx"
	"strconv"
)

type AdminSiteServiceableAreaCepRangeControllerInterface interface {
	CreateSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{})
}

type adminSiteServiceableAreaCepRangeController struct {
	siteServiceableAreaCepRangeService site_serviceable_area_cep_range.SiteServiceableAreaCepRangeInterface
}

func (a *adminSiteServiceableAreaCepRangeController) UploadSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UploadSiteServiceableAreaCepRangeRequest)
	return nil, a.siteServiceableAreaCepRangeService.UploadSiteServiceableAreaCepRange(c, request)
}

func (a *adminSiteServiceableAreaCepRangeController) ExportSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.ExportSiteServiceableAreaCepRange)
	models, lcosErr := a.siteServiceableAreaCepRangeService.ListAllSiteServiceableAreaCepRange(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}
	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawOfSSACepRange{
			SiteID:        model.SiteId,
			ActualPointID: model.ActualPointId,
			Region:        model.Region,
			CepInitial:    strconv.Itoa(model.CepInitial),
			CepFinal:      strconv.Itoa(model.CepFinal),
		})
	}

	fileName := fmt.Sprintf("site_serviceable_area_cep_range_%s.xlsx", *request.SiteID)
	var excelTitles = []string{
		"Site ID", "Actual Point ID", "Region", "Cep Initial", "Cep Final",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "Sheet1", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (a *adminSiteServiceableAreaCepRangeController) CreateSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateSiteServiceableAreaCepRange)
	return nil, a.siteServiceableAreaCepRangeService.CreateSiteServiceableAreaCepRange(c, request)
}

func (a *adminSiteServiceableAreaCepRangeController) ListSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.ListSiteServiceableAreaCepRange)
	return a.siteServiceableAreaCepRangeService.ListSiteServiceableAreaCepRange(c, request)
}

func (a *adminSiteServiceableAreaCepRangeController) DeleteSiteServiceableAreaCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteSiteServiceableAreaCepRange)
	return nil, a.siteServiceableAreaCepRangeService.DeleteSiteServiceableAreaCepRange(c, request.ID, request.SiteID)
}

func NewAdminSiteServiceableAreaCepRangeController(siteServiceableAreaCepRangeService site_serviceable_area_cep_range.SiteServiceableAreaCepRangeInterface) *adminSiteServiceableAreaCepRangeController {
	return &adminSiteServiceableAreaCepRangeController{siteServiceableAreaCepRangeService: siteServiceableAreaCepRangeService}
}

var _ AdminSiteServiceableAreaCepRangeControllerInterface = (*adminSiteServiceableAreaCepRangeController)(nil)
