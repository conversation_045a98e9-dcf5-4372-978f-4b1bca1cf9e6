package site_serviceable_area_location

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_location"
	"github.com/tealeg/xlsx"
)

type AdminSiteServiceableAreaLocationControllerInterface interface {
	CreateSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{})
	SyncLocationData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminSiteServiceableAreaLocationController struct {
	siteServiceableAreaLocationService site_serviceable_area_location.SiteServiceableAreaLocationInterface
}

func (a *adminSiteServiceableAreaLocationController) ExportSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*basic_serviceable.ExportSiteServiceableAreaLocation)
	models, lcosErr := a.siteServiceableAreaLocationService.ListAllSiteServiceableAreaLocation(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}
	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawOfSSALocation{
			SiteID:        model.SiteId,
			ActualPointID: model.ActualPointId,
			Region:        model.Region,
			State:         model.State,
			City:          model.City,
			District:      model.District,
			Street:        model.Street,
		})
	}

	fileName := fmt.Sprintf("site_serviceable_area_location_%s.xlsx", *request.SiteID)
	var excelTitles = []string{
		"Site ID", "Actual Point ID", "Region", "State", "City", "District", "Street",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "Sheet1", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

}

func (a *adminSiteServiceableAreaLocationController) UploadSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UploadSiteServiceableAreaLocationRequest)
	return nil, a.siteServiceableAreaLocationService.UploadSiteServiceableAreaLocation(c, request)
}

func (a *adminSiteServiceableAreaLocationController) CreateSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateSiteServiceableAreaLocation)
	return nil, a.siteServiceableAreaLocationService.CreateSiteServiceableAreaLocation(c, request)
}

func (a *adminSiteServiceableAreaLocationController) ListSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.ListSiteServiceableAreaLocation)
	return a.siteServiceableAreaLocationService.ListSiteServiceableAreaLocation(c, request)
}

func (a *adminSiteServiceableAreaLocationController) DeleteSiteServiceableAreaLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteSiteServiceableAreaLocation)
	return nil, a.siteServiceableAreaLocationService.DeleteSiteServiceableAreaLocation(c, request.ID, request.SiteID)
}

func (a *adminSiteServiceableAreaLocationController) SyncLocationData(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*basic_serviceable.SyncLocationDataRequest)
	return nil, a.siteServiceableAreaLocationService.SyncSiteSALocation(c, req.Region)
}

func NewAdminSiteServiceableAreaLocationController(siteServiceableAreaLocationService site_serviceable_area_location.SiteServiceableAreaLocationInterface) *adminSiteServiceableAreaLocationController {
	return &adminSiteServiceableAreaLocationController{siteServiceableAreaLocationService: siteServiceableAreaLocationService}
}

var _ AdminSiteServiceableAreaLocationControllerInterface = (*adminSiteServiceableAreaLocationController)(nil)
