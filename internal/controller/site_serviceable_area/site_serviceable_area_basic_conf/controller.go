package site_service_area_basic_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	basic_serviceable "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_basic_conf"
)

type AdminSiteServiceableAreaBasicConfControllerInterface interface {
	CreateSiteServiceableAreaBasicConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateSiteServiceableAreaBasicConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListSiteServiceableAreaBasicConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteSiteServiceableAreaBasicConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type adminSiteServiceableAreaBasicConfController struct {
	siteServiceableAreaBasicConfService site_serviceable_area_basic_conf.SiteServiceableAreaBasicConfInterface
}

func (a *adminSiteServiceableAreaBasicConfController) CreateSiteServiceableAreaBasicConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.CreateSiteServiceableAreaBasicConf)
	return nil, a.siteServiceableAreaBasicConfService.CreateSiteServiceableAreaBasicConf(c, request)
}

func (a *adminSiteServiceableAreaBasicConfController) UpdateSiteServiceableAreaBasicConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.UpdateSiteServiceableAreaBasicConf)
	return nil, a.siteServiceableAreaBasicConfService.UpdateSiteServiceableAreaBasicConf(c, request)
}

func (a *adminSiteServiceableAreaBasicConfController) ListSiteServiceableAreaBasicConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.ListSiteServiceableAreaBasicConf)
	return a.siteServiceableAreaBasicConfService.ListSiteServiceableAreaBasicConf(c, request)
}

func (a *adminSiteServiceableAreaBasicConfController) DeleteSiteServiceableAreaBasicConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*basic_serviceable.DeleteSiteServiceableAreaBasicConf)
	return nil, a.siteServiceableAreaBasicConfService.DeleteSiteServiceableAreaBasicConf(c, request.ID)
}

func NewAdminSiteServiceableAreaBasicConfController(siteServiceableAreaBasicConfService site_serviceable_area_basic_conf.SiteServiceableAreaBasicConfInterface) *adminSiteServiceableAreaBasicConfController {
	return &adminSiteServiceableAreaBasicConfController{siteServiceableAreaBasicConfService: siteServiceableAreaBasicConfService}
}

var _ AdminSiteServiceableAreaBasicConfControllerInterface = (*adminSiteServiceableAreaBasicConfController)(nil)
