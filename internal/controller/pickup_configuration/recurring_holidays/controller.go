package recurring_holidays

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	holidays3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/recurring_holidays"
	holidays2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/recurring_holidays"
)

type AdminLineRecurringHolidaysController interface {
	Update(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type lineRecurringHolidaysController struct {
	recurringHolidayService holidays2.RecurringHolidaysServiceInterface
}

func NewLineRecurringHolidaysController(m holidays2.RecurringHolidaysServiceInterface) *lineRecurringHolidaysController {
	return &lineRecurringHolidaysController{
		recurringHolidayService: m,
	}
}

func (s *lineRecurringHolidaysController) Update(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*holidays3.UpdateRecurringHolidayRequest)
	_, err := s.recurringHolidayService.Update(c, request)
	if err != nil {
		return nil, err
	}
	return request, nil
}
