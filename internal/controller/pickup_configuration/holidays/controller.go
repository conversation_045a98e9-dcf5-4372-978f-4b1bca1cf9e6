package holidays

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/holidays"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/recurring_holiday"
	holiday2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/holidays"
	pickup_configuration "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/ops_service"
	"github.com/tealeg/xlsx"
	"sort"
	"strings"
)

type AdminLineHolidaysController interface {
	Import(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Export(c *utils.HttpContext, rawRequest interface{})
	Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type lineHolidaysController struct {
	holidayService holiday2.LogisticHolidaysServiceInterface
}

func NewLineHolidaysController(m holiday2.LogisticHolidaysServiceInterface) *lineHolidaysController {
	return &lineHolidaysController{
		holidayService: m,
	}
}

func GenerateDataForStateCountryToWriteToExcel(c *utils.HttpContext, stateCountryMap map[string][]*holiday.LogisticHolidayTab) (sheetNames []string, titles [][]string, datas []map[string]interface{}, llsErr *lcos_error.LCOSError) {
	for region, value := range stateCountryMap {
		var title = []string{"Pickup Group ID", "Pickup Region", "Holiday"}
		sheetNames = append(sheetNames, fmt.Sprintf("holiday-%s-bystate", region))
		// 请求地址服务，将地址的location信息缓存
		var stateNameToLocationIdMap = make(map[string]interface{})
		requestParams := &ops_service.GetLocationInfoBySubLevelRequest{
			Country:    region,
			LocationId: 0, //  取整个国家信息
			SubLevel:   1, //  取国家的省信息
		}
		//SPLN-28639去除locationgrpc开关
		stateLocationInfo, err := ops_service.GetLocationInfoBySubLevel(c, requestParams)
		if err != nil {
			return nil, nil, nil, err
		}
		for _, i := range stateLocationInfo {
			stateNameToLocationIdMap[i.Name] = i.LocationId
			title = append(title, i.Name)
		}
		titles = append(titles, title)
		/*
			   需要对数据进行处理，转为map形式，以适应动态生成表格的需求
			   holidays = {
					 "spx_first_mile": {
						   "MY": {
								 "2020-01-09": {
									 "Sarawak": 1,
									 "Kedah": 1,
								 }
						   }
					 }
			   }
		*/
		var holidaysMap = make(map[string]interface{})
		for _, item := range value {
			if _, ok := holidaysMap[item.PickupGroupID]; !ok {
				holidaysMap[item.PickupGroupID] = make(map[string]interface{})
			}
			if _, ok := holidaysMap[item.PickupGroupID].(map[string]interface{})[region]; !ok {
				holidaysMap[item.PickupGroupID].(map[string]interface{})[region] = make(map[string]interface{})
			}
			dateString := item.DateString
			if _, ok := holidaysMap[item.PickupGroupID].(map[string]interface{})[region].(map[string]interface{})[dateString]; !ok {
				holidaysMap[item.PickupGroupID].(map[string]interface{})[region].(map[string]interface{})[dateString] = make(map[string]interface{})
			}
			holidaysMap[item.PickupGroupID].(map[string]interface{})[region].(map[string]interface{})[dateString].(map[string]interface{})[item.StateName] = 1
		}
		datas = append(datas, holidaysMap)
	}
	return sheetNames, titles, datas, nil
}

func fillWeekend(weekends []*recurring_holiday.RecurringHolidayTab) WeekendDay {
	result := WeekendDay{
		Monday:    "0",
		Tuesday:   "0",
		Wednesday: "0",
		Thursday:  "0",
		Friday:    "0",
		Saturday:  "0",
		Sunday:    "0",
	}
	for _, weekend := range weekends {
		switch weekend.WeekDay {
		case 1:
			result.Monday = "1"
		case 2:
			result.Tuesday = "1"
		case 3:
			result.Wednesday = "1"
		case 4:
			result.Thursday = "1"
		case 5:
			result.Friday = "1"
		case 6:
			result.Saturday = "1"
		case 7:
			result.Sunday = "1"
		}
	}
	return result
}

// fillWeekendTypeWeekend locationUnique可以是state name和zipcode
func fillWeekendTypeWeekend(weekends []*recurring_holiday.RecurringHolidayTab, weekendType uint8, pickupGroupID string, locationUnique string) interface{} {
	weekDay := fillWeekend(weekends)
	switch weekendType {
	case constant.Normal:
		return &excelRawOfWeekendNormal{
			PickupGroupID: pickupGroupID,
			Monday:        weekDay.Monday,
			Tuesday:       weekDay.Tuesday,
			Wednesday:     weekDay.Wednesday,
			Thursday:      weekDay.Thursday,
			Friday:        weekDay.Friday,
			Saturday:      weekDay.Saturday,
			Sunday:        weekDay.Sunday,
		}
	case constant.ByZipcode:
		return &excelRawOfWeekendZipcode{
			PickupGroupID: pickupGroupID,
			Zipcode:       locationUnique,
			Monday:        weekDay.Monday,
			Tuesday:       weekDay.Tuesday,
			Wednesday:     weekDay.Wednesday,
			Thursday:      weekDay.Thursday,
			Friday:        weekDay.Friday,
			Saturday:      weekDay.Saturday,
			Sunday:        weekDay.Sunday,
		}
	case constant.ByState:
		return &excelRawOfWeekendState{
			PickupGroupID: pickupGroupID,
			State:         locationUnique,
			Monday:        weekDay.Monday,
			Tuesday:       weekDay.Tuesday,
			Wednesday:     weekDay.Wednesday,
			Thursday:      weekDay.Thursday,
			Friday:        weekDay.Friday,
			Saturday:      weekDay.Saturday,
			Sunday:        weekDay.Sunday,
		}
	}
	return nil
}

// weekendsMap: region->normal/byzipcode/bystate->(pickupgroup#postcode)/(pickupgroup#state name)/pickupgroup->weekends
func GenerateDataForWeekends(weekendsMap map[string]map[uint8]map[string][]*recurring_holiday.RecurringHolidayTab) ([]string, [][]string, [][]interface{}, *lcos_error.LCOSError) {
	titlesMap := map[uint8][]string{
		constant.ByState:   {"Pickup Group ID", "State", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		constant.ByZipcode: {"Pickup Group ID", "Zipcode", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
		constant.Normal:    {"Pickup Group ID", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
	}
	var sheetNames []string
	var titles [][]string
	var datas [][]interface{}

	// sheetName -> []interface{}
	dataMap := map[string][]interface{}{}
	for region, weekendTypeToWeekendMap := range weekendsMap {
		for _, weekendType := range constant.WeekendTypeList {
			if keyToWeekendsMap, ok1 := weekendTypeToWeekendMap[weekendType]; ok1 {
				sheetName := fmt.Sprintf("weekend-%v-%v", region, strings.ToLower(constant.WeekendType[weekendType]))
				sheetNames = append(sheetNames, sheetName)
				titles = append(titles, titlesMap[weekendType])
				// 将输出结果排序
				var keyList []string
				for key := range keyToWeekendsMap {
					keyList = append(keyList, key)
				}
				sort.Strings(keyList)
				for _, key := range keyList {
					weekends := keyToWeekendsMap[key]
					var pickupGroupID, locationUnique string
					tmpList := strings.Split(key, "#")
					if len(tmpList) == 0 {
						return nil, nil, nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "key is not valid")
					}
					pickupGroupID = tmpList[0]
					if len(tmpList) == 2 {
						locationUnique = tmpList[1]
					}
					singleData := fillWeekendTypeWeekend(weekends, weekendType, pickupGroupID, locationUnique)
					if _, ok2 := dataMap[sheetName]; !ok2 {
						dataMap[sheetName] = []interface{}{}
					}
					if singleData != nil {
						dataMap[sheetName] = append(dataMap[sheetName], singleData)
					}
				}
			}
		}
	}

	// 将dataMap转为列表，用于导出
	for _, sheetName := range sheetNames {
		datas = append(datas, dataMap[sheetName])
	}
	return sheetNames, titles, datas, nil
}

func (s *lineHolidaysController) Import(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*holidays.ImportHolidayRequest)
	return nil, s.holidayService.Import(c, request)
}

func (s *lineHolidaysController) Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*holidays.DeleteHolidayRequest)
	return nil, s.holidayService.Delete(c, request)
}

func (s *lineHolidaysController) Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*holidays.CreateHolidayRequest)
	return s.holidayService.Create(c, request)
}

func (s *lineHolidaysController) List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*holidays.ListHolidayRequest)
	return s.holidayService.List(c, request)
}

func (s *lineHolidaysController) Export(c *utils.HttpContext, rawRequest interface{}) {
	request, _ := rawRequest.(*holidays.ListHolidayRequest)

	// CB 场景下，需要前端指明 destination region
	destinationRegion := request.DestinationRegion
	if destinationRegion == "" {
		destinationRegion = strings.ToUpper(c.GetCountry())
	}

	// 获取数据，导出只需要导出holiday的信息
	pickupGroups, _holidays, _weekends, err := s.holidayService.GetHolidaysAndWeekends(c, request)
	if err != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}
	// 将pickupGroup转为map，以供查询
	pickupGroupMap := map[string]*pickup_configuration.SinglePickupGroupInfo{}
	for _, item := range pickupGroups {
		pickupGroupMap[item.PickupGroupID] = item
	}

	// 导出excel相关初始配置
	fileName := fmt.Sprintf("non-working-day-%s.xlsx", destinationRegion)
	file := xlsx.NewFile()
	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	// excel sheet的第一个sheet为非state country内容
	nonStateHolidays := make([]*holiday.LogisticHolidayTab, 0, 100)

	// 将stateCountry的信息按照国家聚合
	var stateCountryHolidayMap = make(map[string][]*holiday.LogisticHolidayTab)
	for _, singleHoliday := range _holidays {
		if _, ok := pickupGroupMap[singleHoliday.PickupGroupID]; !ok {
			logger.CtxLogErrorf(c, "cannot find pickup group|pickup_group_id=%s", singleHoliday.PickupGroupID)
			http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
			return
		}
		// 检查holiday中的stateID是否为0来确认当前的信息是否为state country的内容
		if singleHoliday.StateId == 0 {
			nonStateHolidays = append(nonStateHolidays, singleHoliday)
		} else {
			if _, ok := stateCountryHolidayMap[pickupGroupMap[singleHoliday.PickupGroupID].OriginRegion]; !ok {
				stateCountryHolidayMap[pickupGroupMap[singleHoliday.PickupGroupID].OriginRegion] = make([]*holiday.LogisticHolidayTab, 0, 100)
			}
			stateCountryHolidayMap[pickupGroupMap[singleHoliday.PickupGroupID].OriginRegion] = append(stateCountryHolidayMap[pickupGroupMap[singleHoliday.PickupGroupID].OriginRegion], singleHoliday)
		}
	}
	var excelHolidayData []interface{}
	for _, model := range nonStateHolidays {
		excelHolidayData = append(excelHolidayData, &excelRawOfHoliday{
			PickupGroupID: model.PickupGroupID,
			PickupRegion:  pickupGroupMap[model.PickupGroupID].OriginRegion,
			Holiday:       model.DateString,
		})
	}
	var excelTitles = []string{
		"Pickup Group ID", "Pickup Region", "Holiday",
	}
	// 排序excelHolidayData数据
	sort.Slice(excelHolidayData, func(i, j int) bool {
		dataI := excelHolidayData[i].(*excelRawOfHoliday)
		dataJ := excelHolidayData[j].(*excelRawOfHoliday)
		if dataI.PickupGroupID < dataJ.PickupGroupID {
			return true
		} else if dataI.PickupGroupID > dataJ.PickupGroupID {
			return false
		} else {
			if dataI.Holiday < dataJ.Holiday {
				return true
			} else if dataI.Holiday > dataJ.Holiday {
				return false
			} else {
				return true
			}
		}
	})
	var sheetName = "holiday" // 非state国家的假日信息
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTitles, excelHolidayData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	/*
		写入state国家的数据
		1. 遍历stateCountryHolidayMap
		2. 请求地址服务，获取stateNames，用于组成titles
		3. 按照map格式写入数据，写入excel
	*/
	sheetNames, titles, datas, err := GenerateDataForStateCountryToWriteToExcel(c, stateCountryHolidayMap)
	if err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
	if err := excel.WriteTitleAndStructForMultipleSheetsForStateCountry(file, sheetNames, titles, datas); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	/*
		1. 将weekend封装为region->normal/byzipcode/bystate->(pickupgroup#postcode)/(pickupgroup#state name)/pickupgroup->weekends的格式
		2. 将对应的map导出为Excel的格式
		3. 循环导出excel
	*/
	var weekendsMap = map[string]map[uint8]map[string][]*recurring_holiday.RecurringHolidayTab{}
	for _, weekend := range _weekends {
		if _, ok := pickupGroupMap[weekend.PickupGroupID]; !ok {
			logger.CtxLogErrorf(c, "cannot find pickup group|pickup_group_id=%s", weekend.PickupGroupID)
			http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
			return
		}
		originRegion := pickupGroupMap[weekend.PickupGroupID].OriginRegion
		if _, ok1 := weekendsMap[originRegion]; !ok1 {
			weekendsMap[originRegion] = map[uint8]map[string][]*recurring_holiday.RecurringHolidayTab{}
		}
		if weekend.ZipCode != "" {
			if _, ok1 := weekendsMap[originRegion][constant.ByZipcode]; !ok1 {
				weekendsMap[originRegion][constant.ByZipcode] = map[string][]*recurring_holiday.RecurringHolidayTab{}
			}
			key := fmt.Sprintf("%v#%v", weekend.PickupGroupID, weekend.ZipCode)
			weekendsMap[originRegion][constant.ByZipcode][key] = append(weekendsMap[originRegion][constant.ByZipcode][key], weekend)
		} else if weekend.StateName != "" && weekend.StateId != 0 {
			if _, ok1 := weekendsMap[originRegion][constant.ByState]; !ok1 {
				weekendsMap[originRegion][constant.ByState] = map[string][]*recurring_holiday.RecurringHolidayTab{}
			}
			key := fmt.Sprintf("%v#%v", weekend.PickupGroupID, weekend.StateName)
			weekendsMap[originRegion][constant.ByState][key] = append(weekendsMap[originRegion][constant.ByState][key], weekend)
		} else {
			if _, ok1 := weekendsMap[originRegion][constant.Normal]; !ok1 {
				weekendsMap[originRegion][constant.Normal] = map[string][]*recurring_holiday.RecurringHolidayTab{}
			}
			weekendsMap[originRegion][constant.Normal][weekend.PickupGroupID] = append(weekendsMap[originRegion][constant.Normal][weekend.PickupGroupID], weekend)
		}
	}

	weekendSheetNames, weekendTitles, weekendDatas, lcosErr := GenerateDataForWeekends(weekendsMap)
	if lcosErr != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", lcosErr)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
	for index := range weekendSheetNames {
		if err := excel.WriteTitleAndStruct(file, weekendSheetNames[index], weekendTitles[index], weekendDatas[index]); err != nil {
			logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
			http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
			return
		}
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}
