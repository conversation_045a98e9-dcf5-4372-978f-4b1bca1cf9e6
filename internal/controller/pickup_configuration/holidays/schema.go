package holidays

type excelRawOfHoliday struct {
	PickupGroupID string `excel:"title:Pickup Group ID"`
	PickupRegion  string `excel:"title:Pickup Region"`
	Holiday       string `excel:"title:Holiday"`
}

type WeekendDay struct {
	Monday    string `excel:"title:Monday"`
	Tuesday   string `excel:"title:Tuesday"`
	Wednesday string `excel:"title:Wednesday"`
	Thursday  string `excel:"title:Thursday"`
	Friday    string `excel:"title:Friday"`
	Saturday  string `excel:"title:Saturday"`
	Sunday    string `excel:"title:Sunday"`
}

type excelRawOfWeekendNormal struct {
	PickupGroupID string `excel:"title:Pickup Group ID"`
	Monday        string `excel:"title:Monday"`
	Tuesday       string `excel:"title:Tuesday"`
	Wednesday     string `excel:"title:Wednesday"`
	Thursday      string `excel:"title:Thursday"`
	Friday        string `excel:"title:Friday"`
	Saturday      string `excel:"title:Saturday"`
	Sunday        string `excel:"title:Sunday"`
}

type excelRawOfWeekendState struct {
	PickupGroupID string `excel:"title:Pickup Group ID"`
	State         string `excel:"title:State"`
	Monday        string `excel:"title:Monday"`
	Tuesday       string `excel:"title:Tuesday"`
	Wednesday     string `excel:"title:Wednesday"`
	Thursday      string `excel:"title:Thursday"`
	Friday        string `excel:"title:Friday"`
	Saturday      string `excel:"title:Saturday"`
	Sunday        string `excel:"title:Sunday"`
}

type excelRawOfWeekendZipcode struct {
	PickupGroupID string `excel:"title:Pickup Group ID"`
	Zipcode       string `excel:"title:Zipcode"`
	Monday        string `excel:"title:Monday"`
	Tuesday       string `excel:"title:Tuesday"`
	Wednesday     string `excel:"title:Wednesday"`
	Thursday      string `excel:"title:Thursday"`
	Friday        string `excel:"title:Friday"`
	Saturday      string `excel:"title:Saturday"`
	Sunday        string `excel:"title:Sunday"`
}
