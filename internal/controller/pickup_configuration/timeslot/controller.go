package timeslot

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/timeslot"
)

type AdminPickupTimeslotController interface {
	CreateOrUpdate(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type linePickupTimeslotController struct {
	linePickupTimeslotService timeslot.PickupTimeslotServiceInterface
}

func NewLinePickupTimeslotController(m timeslot.PickupTimeslotServiceInterface) *linePickupTimeslotController {
	return &linePickupTimeslotController{
		linePickupTimeslotService: m,
	}
}

func (l *linePickupTimeslotController) CreateOrUpdate(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*timeslot2.CreateOrUpdateTimeslotRequest)
	return nil, l.linePickupTimeslotService.CreateOrUpdate(c, request)
}

func (l *linePickupTimeslotController) Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*timeslot2.DeleteTimeslotRequest)
	return nil, l.linePickupTimeslotService.Delete(c, request)
}

func (l *linePickupTimeslotController) List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*timeslot2.ListTimeslotRequest)
	return l.linePickupTimeslotService.List(c, request)
}
