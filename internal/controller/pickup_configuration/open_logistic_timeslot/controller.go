package open_logistic_timeslot

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/open_logistic_timeslot"
	timeslot3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/open_logistic_timeslot"
)

type AdminOpenLogisticPickupTimeslotController interface {
	CreateOrUpdate(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type openLogisticPickupTimeslotController struct {
	pickupTimeslotService timeslot3.OpenLogisticPickupTimeslotServiceInterface
}

func NewOpenLogisticPickupTimeslotController(pickupTimeslotService timeslot3.OpenLogisticPickupTimeslotServiceInterface) *openLogisticPickupTimeslotController {
	return &openLogisticPickupTimeslotController{
		pickupTimeslotService: pickupTimeslotService,
	}
}

func (l *openLogisticPickupTimeslotController) CreateOrUpdate(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*open_logistic_timeslot.CreateOrUpdateOpenLogisticTimeslotRequest)
	return nil, l.pickupTimeslotService.CreateOrUpdate(c, request)
}

func (l *openLogisticPickupTimeslotController) Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*open_logistic_timeslot.DeleteOpenLogisticTimeslotRequest)
	return nil, l.pickupTimeslotService.Delete(c, request)
}

func (l *openLogisticPickupTimeslotController) List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*open_logistic_timeslot.ListOpenLogisticTimeslotRequest)
	return l.pickupTimeslotService.List(c, request)
}
