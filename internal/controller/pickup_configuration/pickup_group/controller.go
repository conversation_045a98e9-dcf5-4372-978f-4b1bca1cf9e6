package pickup_group

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	pickup_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/pickup_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_group"
)

type AdminPickupGroupController interface {
	Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Update(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateWithDraft(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetByGroupId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	All(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	AllPickupLines(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SearchPickupGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type pickupGroupController struct {
	pickupGroupService pickup_group.PickupGroupServiceInterface
}

func NewPickupGroupController(pickupGroupService pickup_group.PickupGroupServiceInterface) *pickupGroupController {
	return &pickupGroupController{
		pickupGroupService: pickupGroupService,
	}
}

func (p *pickupGroupController) Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_group2.CreatePickupGroupRequest)
	return p.pickupGroupService.CreatePickupGroup(c, request)
}

func (p *pickupGroupController) Update(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_group2.UpdatePickupGroupRequest)
	return p.pickupGroupService.UpdatePickupGroup(c, request)
}

func (p *pickupGroupController) UpdateWithDraft(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_group2.UpdatePickupGroupRequest)
	return p.pickupGroupService.UpdatePickupGroupWithDraft(c, request)
}

func (p *pickupGroupController) Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_group2.DeletePickupGroupRequest)
	return nil, p.pickupGroupService.DeletePickupGroup(c, request)
}

func (p *pickupGroupController) GetByGroupId(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_group2.GetPickupGroupRequest)
	return p.pickupGroupService.GetPickupGroupByGroupId(c, request)
}

func (p *pickupGroupController) List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_group2.ListPickupGroupRequest)
	// 填充page和count
	if request.PageNo == nil {
		var page uint32 = 1
		request.PageNo = &page
	}
	if request.Count == nil {
		var count uint32 = 10
		request.Count = &count
	}
	return p.pickupGroupService.ListPickupGroup(c, request)
}

func (p *pickupGroupController) All(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_group2.ListAllPickupGroupRequest)
	return p.pickupGroupService.ListAllPickupGroups(c, request)
}

func (p *pickupGroupController) AllPickupLines(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_group2.ListAllPickupLinesRequest)
	return p.pickupGroupService.ListAllPickupLines(c, request)
}

func (p *pickupGroupController) SearchPickupGroup(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_group2.SearchPickupGroupRequest)
	return p.pickupGroupService.SearchPickupGroup(c, request)
}
