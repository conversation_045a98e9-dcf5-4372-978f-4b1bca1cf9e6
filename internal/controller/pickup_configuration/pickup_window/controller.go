package pickup_window

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/pickup_window"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_window"
)

type PickupWindowController interface {
	GetNonworkingDays(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewPickupWindowController(service pickup_window.PickupWindowService) *pickupWindowController {
	return &pickupWindowController{
		service: service,
	}
}

type pickupWindowController struct {
	service pickup_window.PickupWindowService
}

func (p *pickupWindowController) GetNonworkingDays(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.GetNonworkingDaysRequest)

	if req.StartTime > req.EndTime {
		return nil, lcos_error.NewLCOSErrorf(lcos_error.SchemaParamsErrorCode, "start_time[%d] should not be larger than end_time[%d]", req.StartTime, req.EndTime)
	}

	nonworkingDays, err := p.service.GetNonworkingDays(ctx, req.Region, req.PickupGroupId, req.StartTime, req.EndTime)
	if err != nil {
		return nil, err
	}
	return &protocol.GetNonworkingDaysResponse{
		NonworkingDays:     len(nonworkingDays),
		NonworkingDaysList: nonworkingDays,
	}, nil
}
