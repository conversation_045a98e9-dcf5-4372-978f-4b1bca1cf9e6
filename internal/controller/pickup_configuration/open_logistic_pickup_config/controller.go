package open_logistic_pickup_config

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/open_logistic_pickup_config"
	pickup_configuration2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/open_logistic_pickup_config"
)

type AdminOpenLogisticPickupConfigController interface {
	Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Update(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListByMerchanTypes(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteByMerchanType(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type openLogisticPickupConfigController struct {
	pickupConfigService pickup_configuration2.OpenLogisticPickupConfigServiceInterface
}

func NewOpenLogisticPickupConfigController(pickupConfigService pickup_configuration2.OpenLogisticPickupConfigServiceInterface) *openLogisticPickupConfigController {
	return &openLogisticPickupConfigController{
		pickupConfigService: pickupConfigService,
	}
}

func (l *openLogisticPickupConfigController) Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*open_logistic_pickup_config.CreateOpenLogisticPickupConfigRequest)
	return l.pickupConfigService.Create(c, request)
}

func (l *openLogisticPickupConfigController) Update(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*open_logistic_pickup_config.UpdateOpenLogisticPickupConfigRequest)
	return l.pickupConfigService.Update(c, request)
}

func (l *openLogisticPickupConfigController) Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*open_logistic_pickup_config.DeleteOpenLogisticPickupConfigRequest)
	return nil, l.pickupConfigService.Delete(c, request)
}

func (l *openLogisticPickupConfigController) List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*open_logistic_pickup_config.ListOpenLogisticPickupConfigRequest)
	return l.pickupConfigService.List(c, request)
}

func (l *openLogisticPickupConfigController) ListByMerchanTypes(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*open_logistic_pickup_config.GetOpenLogisticPickupConfigByMerchantTypeRequest)
	return l.pickupConfigService.ListByMerchantTypes(c, request)
}

func (l *openLogisticPickupConfigController) DeleteByMerchanType(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*open_logistic_pickup_config.DeleteOpenLogisticPickupConfigByMerchantTypeRequest)
	return nil, l.pickupConfigService.DeleteByMerchantType(c, request)
}
