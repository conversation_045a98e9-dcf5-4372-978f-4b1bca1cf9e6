package pickup_config

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/pickup_configuration/pickup_config"
	pickup_configuration "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_config"
)

type AdminPickupConfigController interface {
	Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Update(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	Disable(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type pickupConfigController struct {
	pickupConfigService pickup_configuration.PickupConfigServiceInterface
}

func NewPickupConfigController(m pickup_configuration.PickupConfigServiceInterface) *pickupConfigController {
	return &pickupConfigController{
		pickupConfigService: m,
	}
}

func (l *pickupConfigController) Create(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_config.CreatePickupConfigRequest)
	return l.pickupConfigService.Create(c, request)
}

func (l *pickupConfigController) Update(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_config.UpdatePickupConfigRequest)
	return l.pickupConfigService.Update(c, request)
}

func (l *pickupConfigController) Delete(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_config.DeletePickupConfigRequest)
	return nil, l.pickupConfigService.Delete(c, request)
}

func (l *pickupConfigController) List(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_config.ListPickupConfigRequest)
	return l.pickupConfigService.List(c, request)
}

func (l *pickupConfigController) Disable(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*pickup_config.DisablePickupConfigRequest)
	if err := l.pickupConfigService.Disable(c, request); err != nil {
		return nil, err
	}
	return nil, nil
}
