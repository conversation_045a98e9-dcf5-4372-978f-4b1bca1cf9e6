package tpl_id_line_id_ref

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	tpl_id_line_id_ref2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/tpl_id_line_id_ref"
	"strings"
)

type AdminTplIDLineIDRefController interface {
	List3plInfoList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type tplIDLineIDRefController struct {
	tplIDLineIDRefService tpl_id_line_id_ref.TPLIDLineIDRefInterface
}

func NewTplIDLineIDRefController(tplIDLineIDRefService tpl_id_line_id_ref.TPLIDLineIDRefInterface) *tplIDLineIDRefController {
	return &tplIDLineIDRefController{
		tplIDLineIDRefService: tplIDLineIDRefService,
	}
}

var _ AdminTplIDLineIDRefController = (*tplIDLineIDRefController)(nil)

func (t *tplIDLineIDRefController) List3plInfoList(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*tpl_id_line_id_ref2.List3plInfo)
	channelList := strings.Split(request.ProductIDs, ",")
	if len(channelList) == 0 {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, "channel list cannot be empty")
	}
	tplInfoList, lcosErr := t.tplIDLineIDRefService.BatchGet3plInfoList(c, channelList)
	if lcosErr != nil {
		return nil, lcosErr
	}
	return tplInfoList, nil
}
