package delivery_instruction_whitelist_location

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	delivery_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/delivery_instruction"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_instruction_whitelist_location"
	"github.com/tealeg/xlsx"
)

type AdminDeliveryInstructionWhitelistLocationControllerInterface interface {
	ListDeliveryInstructionWhitelistLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteDeliveryInstructionWhitelistLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadDeliveryInstructionWhitelistLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportDeliveryInstructionWhitelistLocation(c *utils.HttpContext, rawRequest interface{})
}

type deliveryInstructionWhitelistLocationController struct {
	DeliveryInstructionWhitelistLocationService delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationInterface
}

func (d *deliveryInstructionWhitelistLocationController) ListDeliveryInstructionWhitelistLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*whitelist.ListDeliveryInstructionWhitelistLocation)
	return d.DeliveryInstructionWhitelistLocationService.ListDeliveryInstructionWhitelistLocation(c, request)
}

func (d *deliveryInstructionWhitelistLocationController) DeleteDeliveryInstructionWhitelistLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*whitelist.DeleteDeliveryInstructionWhitelistLocation)
	return nil, d.DeliveryInstructionWhitelistLocationService.DeleteDeliveryInstructionWhitelistLocation(c, request.ID)
}

func (d *deliveryInstructionWhitelistLocationController) UploadDeliveryInstructionWhitelistLocation(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*whitelist.UploadDeliveryInstructionWhitelistLocationRequest)
	return nil, d.DeliveryInstructionWhitelistLocationService.UploadDeliveryInstructionWhitelistLocation(c, request)
}

func (d *deliveryInstructionWhitelistLocationController) ExportDeliveryInstructionWhitelistLocation(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*whitelist.ExportDeliveryInstructionWhitelistLocation)
	models, lcosErr := d.DeliveryInstructionWhitelistLocationService.ListAllDeliveryInstructionWhitelistLocation(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	categoryNameMap := map[int8]string{
		delivery_constant.DeliveryMethod:  delivery_constant.DeliveryMethodName,
		delivery_constant.LogisticSupport: delivery_constant.LogisticSupportName,
		delivery_constant.ContactMethod:   delivery_constant.ContactMethodName,
	}
	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawOfDIWhitelistLocation{
			LineID:   model.LineId,
			Category: categoryNameMap[model.Category],
			State:    model.State,
			City:     model.City,
			District: model.District,
		})
	}

	fileName := "delivery_instruction_whitelist_location.xlsx"
	var excelTitles = []string{
		"Line id", "Category", "State", "City", "District",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "Sheet1", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func NewAdminDeliveryInstructionWhitelistLocationController(DeliveryInstructionWhitelistLocationService delivery_instruction_whitelist_location.DeliveryInstructionWhitelistLocationInterface) *deliveryInstructionWhitelistLocationController {
	return &deliveryInstructionWhitelistLocationController{
		DeliveryInstructionWhitelistLocationService: DeliveryInstructionWhitelistLocationService,
	}
}

var _ AdminDeliveryInstructionWhitelistLocationControllerInterface = (*deliveryInstructionWhitelistLocationController)(nil)
