package delivery_instruction_conf

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	delivery_method2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/delivery_method"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_method"
)

type AdminDeliveryInstructionConfController interface {
	CreateDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	EditDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DetailDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	OptionListDeliveryInstruction(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type deliveryInstructionConfController struct {
	deliveryMethodService delivery_method.DeliveryMethodInterface
}

func (e *deliveryInstructionConfController) CreateDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*delivery_method2.EditDeliveryInstructionRequest)
	return nil, e.deliveryMethodService.CreateDeliveryMethod(c, request)
}

func (e *deliveryInstructionConfController) EditDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*delivery_method2.EditDeliveryInstructionRequest)
	return nil, e.deliveryMethodService.EditDeliveryMethod(c, request)
}

func (e *deliveryInstructionConfController) ListDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*delivery_method2.ListDeliveryInstructionRequest)
	return e.deliveryMethodService.ListDeliveryMethod(c, request)
}

func (e *deliveryInstructionConfController) DetailDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*delivery_method2.RuleIdDeliveryInstructionRequest)
	return e.deliveryMethodService.DetailDeliveryMethod(c, request)
}

func (e *deliveryInstructionConfController) DeleteDeliveryInstructionConf(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*delivery_method2.RuleIdDeliveryInstructionRequest)
	return nil, e.deliveryMethodService.DeleteDeliveryMethod(c, request)
}

func (e *deliveryInstructionConfController) OptionListDeliveryInstruction(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	return e.deliveryMethodService.OptionListDeliveryInstructionConf(c)
}

func NewAdminDeliveryInstructionConfController(deliveryMethodService delivery_method.DeliveryMethodInterface) *deliveryInstructionConfController {
	return &deliveryInstructionConfController{
		deliveryMethodService: deliveryMethodService,
	}
}

var _ AdminDeliveryInstructionConfController = (*deliveryInstructionConfController)(nil)
