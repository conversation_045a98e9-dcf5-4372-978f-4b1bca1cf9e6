package delivery_instruction_whitelist_cep_range

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	delivery_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/delivery_instruction"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/delivery_instruction_protocol/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_instruction_whitelist_cep_range"
	"github.com/tealeg/xlsx"
	"strconv"
)

type AdminDeliveryInstructionWhitelistCepRangeControllerInterface interface {
	ListDeliveryInstructionWhitelistCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteDeliveryInstructionWhitelistCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadDeliveryInstructionWhitelistCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportDeliveryInstructionWhitelistCepRange(c *utils.HttpContext, rawRequest interface{})
}

type adminDeliveryInstructionWhitelistCepRangeController struct {
	deliveryInstructionWhitelistCepRangeService delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeInterface
}

func (a *adminDeliveryInstructionWhitelistCepRangeController) ListDeliveryInstructionWhitelistCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*whitelist.ListDeliveryInstructionWhitelistCepRange)
	return a.deliveryInstructionWhitelistCepRangeService.ListDeliveryInstructionWhitelistCepRange(c, request)
}

func (a *adminDeliveryInstructionWhitelistCepRangeController) DeleteDeliveryInstructionWhitelistCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*whitelist.DeleteDeliveryInstructionWhitelistCepRange)
	return nil, a.deliveryInstructionWhitelistCepRangeService.DeleteDeliveryInstructionWhitelistCepRange(c, request.ID)
}

func (a *adminDeliveryInstructionWhitelistCepRangeController) UploadDeliveryInstructionWhitelistCepRange(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*whitelist.UploadDeliveryInstructionWhitelistCepRangeRequest)
	return nil, a.deliveryInstructionWhitelistCepRangeService.UploadDeliveryInstructionWhitelistCepRange(c, request)
}

func (a *adminDeliveryInstructionWhitelistCepRangeController) ExportDeliveryInstructionWhitelistCepRange(c *utils.HttpContext, rawRequest interface{}) {
	request := rawRequest.(*whitelist.ExportDeliveryInstructionWhitelistCepRange)
	models, lcosErr := a.deliveryInstructionWhitelistCepRangeService.ListAllDeliveryInstructionWhitelistCepRange(c, request)
	if lcosErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in query data")
		return
	}

	categoryNameMap := map[int8]string{
		delivery_constant.DeliveryMethod:  delivery_constant.DeliveryMethodName,
		delivery_constant.LogisticSupport: delivery_constant.LogisticSupportName,
		delivery_constant.ContactMethod:   delivery_constant.ContactMethodName,
	}
	var excelRawData []interface{}
	for _, model := range models {
		excelRawData = append(excelRawData, &excelRawOfDIWhitelistCepRange{
			LineID:     model.LineId,
			Category:   categoryNameMap[model.Category],
			CepInitial: strconv.FormatUint(model.Initial, 10),
			CepFinal:   strconv.FormatUint(model.Final, 10),
		})
	}
	fileName := "delivery_instruction_whitelist_cep_range.xlsx"
	var excelTitles = []string{
		"Line id", "Category", "CEP Initial", "CEP Final",
	}

	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")

	file := xlsx.NewFile()

	if err := excel.WriteTitleAndStruct(file, "Sheet1", excelTitles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func NewAdminDeliveryInstructionWhitelistCepRangeController(deliveryInstructionWhitelistCepRangeService delivery_instruction_whitelist_cep_range.DeliveryInstructionWhitelistCepRangeInterface) *adminDeliveryInstructionWhitelistCepRangeController {
	return &adminDeliveryInstructionWhitelistCepRangeController{deliveryInstructionWhitelistCepRangeService: deliveryInstructionWhitelistCepRangeService}
}

var _ AdminDeliveryInstructionWhitelistCepRangeControllerInterface = (*adminDeliveryInstructionWhitelistCepRangeController)(nil)
