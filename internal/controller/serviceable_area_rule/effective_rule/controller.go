package effective_rule

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	sa_rule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area_rule"
)

type AdminServiceableEffectiveRuleController interface {
	GetEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DetailEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	BatchUpdateEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetBatchUpdateRuleInfo(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CheckEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)

	GetLaneCodes(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetProductIds(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetFulfillmentModels(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type serviceableEffectiveRuleController struct {
	service serviceable_area_rule.ServiceableAreaRuleServiceInterface
}

func (controller *serviceableEffectiveRuleController) GetBatchUpdateRuleInfo(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.GetRuleInfoRequest)

	ruleInfo, err := controller.service.GetBatchUpdateRuleInfo(c, request)
	if err != nil {
		return nil, err
	}
	return ruleInfo, nil
}

func (controller *serviceableEffectiveRuleController) GetEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.ListEffectiveRuleRequest)

	r, err := controller.service.GetServiceableEffectiveRuleList(c, request)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (controller *serviceableEffectiveRuleController) DetailEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)

	r, err := controller.service.GetServiceableEffectiveRule(c, request)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (controller *serviceableEffectiveRuleController) UpdateEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.UpdateEffectiveRuleRequest)

	r, err := controller.service.UpdateServiceableEffectiveRule(c, request)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (controller *serviceableEffectiveRuleController) BatchUpdateEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.BatchUpdateEffectiveRuleRequest)
	err := controller.service.BatchUpdateServiceableEffectiveRule(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (controller *serviceableEffectiveRuleController) GetLaneCodes(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.ListLaneCodeRequest)
	laneCodeList, err := controller.service.GetLaneCodeList(c, request)
	if err != nil {
		return nil, err
	}
	return laneCodeList, nil
}

func (controller *serviceableEffectiveRuleController) GetProductIds(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	productIds, err := controller.service.GetProductIdList(c)
	if err != nil {
		return nil, err
	}
	return productIds, nil
}

func (controller *serviceableEffectiveRuleController) GetFulfillmentModels(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.ListFulfillmentModelRequest)

	models, err := controller.service.GetFulfillmentModelList(c, request)
	if err != nil {
		return nil, err
	}
	return models, nil
}

func (controller *serviceableEffectiveRuleController) CheckEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.CheckEffectiveRuleRequest)
	err := controller.service.CheckEffectiveRule(c, request)
	return nil, err
}

func (controller *serviceableEffectiveRuleController) ExportEffectiveRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.ExportEffectiveRuleRequest)
	models, err := controller.service.ExportEffectiveRule(c, request)
	return models, err
}

func NewServiceableEffectiveRuleController(s serviceable_area_rule.ServiceableAreaRuleServiceInterface) *serviceableEffectiveRuleController {
	return &serviceableEffectiveRuleController{
		service: s,
	}
}
