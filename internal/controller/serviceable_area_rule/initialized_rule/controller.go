package initialized_rule

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	sa_rule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area_rule"
)

type AdminServiceableInitializedRuleController interface {
	GetServiceableInitializedRules(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DetailServiceableInitializedRules(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CreateServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DisableServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	CheckServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetServiceableInitializedRuleIds(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type serviceableInitializedRuleController struct {
	service serviceable_area_rule.ServiceableAreaRuleServiceInterface
}

func (controller *serviceableInitializedRuleController) GetServiceableInitializedRules(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.ListInitializedRuleRequest)

	r, err := controller.service.GetServiceableInitializedRuleList(c, request)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (controller *serviceableInitializedRuleController) DetailServiceableInitializedRules(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)

	r, err := controller.service.GetServiceableInitializedRule(c, request)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (controller *serviceableInitializedRuleController) CreateServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.CreateInitializedRuleRequest)
	r, err := controller.service.CreateServiceableInitializedRule(c, request)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (controller *serviceableInitializedRuleController) UpdateServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.UpdateInitializedRuleRequest)

	r, err := controller.service.UpdateServiceableInitializedRule(c, request)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (controller *serviceableInitializedRuleController) DeleteServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)

	err := controller.service.DeleteServiceableInitializedRule(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (controller *serviceableInitializedRuleController) DisableServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*common_protocol.IDRequest)

	err := controller.service.DisableServiceableInitializedRule(c, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (controller *serviceableInitializedRuleController) CheckServiceableInitializedRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request := rawRequest.(*sa_rule.CheckInitializedRuleExistRequest)

	r, err := controller.service.CheckServiceableInitializedRuleExist(c, request)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (controller *serviceableInitializedRuleController) GetServiceableInitializedRuleIds(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	r, err := controller.service.GetRuleIdList(c)
	if err != nil {
		return nil, err
	}
	return r, nil
}

func NewServiceableInitializedRuleController(s serviceable_area_rule.ServiceableAreaRuleServiceInterface) *serviceableInitializedRuleController {
	return &serviceableInitializedRuleController{
		service: s,
	}
}
