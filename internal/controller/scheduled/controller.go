package scheduled

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/scheduled_constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/serviceable_area/common_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_util"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/scheduled_protocol"
	model "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
)

type AdminScheduledController interface {
	UpdateScheduledJob(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListScheduledJob(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type scheduledController struct {
	scheduledService     scheduled.ScheduledService
	basicLocationService basic_location.LineBasicServiceableLocationServiceInterface
	basicPostcodeService basic_postcode.LineBasicServiceablePostcodeServiceInterface
}

func NewAdminScheduledController(scheduledService scheduled.ScheduledService, basicLocationService basic_location.LineBasicServiceableLocationServiceInterface, basicPostcodeService basic_postcode.LineBasicServiceablePostcodeServiceInterface) *scheduledController {
	return &scheduledController{
		scheduledService:     scheduledService,
		basicLocationService: basicLocationService,
		basicPostcodeService: basicPostcodeService,
	}
}

func (s *scheduledController) UpdateScheduledJob(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*scheduled_protocol.UpdateScheduledJobRequest)
	job, err := s.scheduledService.UpdateScheduledJob(ctx, req)
	if err != nil {
		return nil, err
	}

	// 特殊逻辑，基础层Location和Postcode的服务范围上传任务如果修改了生效时间，那么需要重新触发文件校验
	go func(ctx utils.LCOSContext, req *scheduled_protocol.UpdateScheduledJobRequest, job *model.Scheduled) {
		if req.ScheduledTime == 0 {
			// 没有修改scheduled_time，不处理
			return
		}
		if job.ScheduledModule != scheduled_constant.ThreePLServiceableArea {
			// 非基础层服务范围上传任务，不处理
			return
		}
		if !utils.CheckInUint8(job.ScheduledType, []uint8{scheduled_constant.Location, scheduled_constant.PostCode}) {
			// 非Location或Postcode服务范围上传任务，不处理
			return
		}

		if err := s.scheduledService.ResetScheduledJobStatus(ctx, job); err != nil {
			logger.CtxLogErrorf(ctx, "reset scheduled job status error|task_id=%d, cause=%s", job.ID, err.Msg)
			return
		}
		fileUrl := s.scheduledService.RefreshFileUrl(ctx, job.FileUrl)
		parseFileReq := &common_protocol.UploadFileRequest{
			Region:  job.Region,
			FileUrl: fileUrl,
		}
		var parseFileResult excel.ParseFileResult
		if job.ScheduledType == scheduled_constant.Location {
			parseFileResult, _ = s.basicLocationService.ParseAndImportBasicLocationSA(ctx, parseFileReq, serviceable_util.LineBasicLocationHeader, true, job.ScheduledTime)
		} else {
			_, parseFileResult, _ = s.basicPostcodeService.ParseAndImportBasicPostcodeSA(ctx, parseFileReq, serviceable_util.LineBasicPostcodeHeader, true, job.ScheduledTime)
		}
		if err := s.scheduledService.UpdateValidateResult(ctx, job, parseFileResult); err != nil {
			logger.CtxLogErrorf(ctx, "scheduled job update validate result error|job_id=%d, cause=%s", job.ID, err.Msg)
		}
	}(ctx, req, job)

	return nil, nil
}

func (s *scheduledController) ListScheduledJob(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*scheduled_protocol.ListScheduledJobRequest)
	if req.PageNo == 0 {
		req.PageNo = constant.DefaultPageNo
	}
	if req.PageSize == 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > config.GetQueryMaxPageSize(ctx) {
		return nil, lcos_error.NewLCOSError(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("Page size cannot over %d", config.GetQueryMaxPageSize(ctx)))
	}
	minScheduledTimeFrom := utils.GetTimestamp(ctx) - config.GetScheduledJobConfig(ctx).GetListableTimeBeforeNow() // ops只能查询最早两周内的任务记录，时间可配置
	if req.ScheduledTimeFrom < minScheduledTimeFrom {
		req.ScheduledTimeFrom = minScheduledTimeFrom
	}
	req.Region = ctx.GetCountry()
	jobList, total, err := s.scheduledService.ListScheduledJob(ctx, req, false)
	if err != nil {
		return nil, err
	}
	// 刷新返回给前端的文件URL
	for _, job := range jobList {
		job.FileUrl = s.scheduledService.RefreshFileUrl(ctx, job.FileUrl)
	}
	resp := &scheduled_protocol.ListScheduledJobResponse{
		List:     jobList,
		PageNo:   req.PageNo,
		PageSize: req.PageSize,
		Total:    total,
	}
	return resp, nil
}
