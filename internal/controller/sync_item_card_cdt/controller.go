package sync_item_card_cdt

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/sync_item_card_cdt_protocol"
	auto_update_rule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_update_rule"
)

type SyncItemCardCdtController interface {
	SyncAutoUpdateData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	SyncManualUpdateData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewSyncItemCardCdtController(auto auto_update_rule.AutoUpdateDataServiceInterface, manual manual_update_rule.ManualUpdateServiceInterface) *syncItemCardCdtController {
	return &syncItemCardCdtController{
		autoUpdateDataService:   auto,
		manualUpdateDataService: manual,
	}
}

type syncItemCardCdtController struct {
	autoUpdateDataService   auto_update_rule.AutoUpdateDataServiceInterface
	manualUpdateDataService manual_update_rule.ManualUpdateServiceInterface
}

func (s *syncItemCardCdtController) SyncAutoUpdateData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.SyncAutoUpdateCdtRequest)
	go func(region string, ruleId uint64) {
		if err := s.autoUpdateDataService.SyncAutoUpdateDataToItemCodis(ctx, region, ruleId); err != nil {
			logger.CtxLogErrorf(ctx, "sync auto update data to item codis error: %s", err.Msg)
		}
	}(req.Region, req.RuleId)
	return nil, nil
}

func (s *syncItemCardCdtController) SyncManualUpdateData(ctx *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := rawRequest.(*protocol.SyncManualUpdateCdtRequest)
	go func(region, productId string) {
		if err := s.manualUpdateDataService.SyncCdtManualUpdateDataToItemCodis(ctx, region, productId); err != nil {
			logger.CtxLogErrorf(ctx, "sync manual update data to item codis error: %s", err.Msg)
		}
	}(req.Region, req.ProductId)
	return nil, nil
}
