package edt_rule

type excelRouteFixEdt struct {
	ProductID        string `excel:"title:Product ID"`
	ClientGroupID    string `excel:"title:Client Group ID"`
	FixApt           string `excel:"title:Fixed APT"`
	FixCdt           string `excel:"title:Fixed CDT"`
	CutoffTime       string `excel:"title:Cutoff Time"`
	SenderRegion     string `excel:"title:Sender Region"`
	SenderState      string `excel:"title:Sender State"`
	SenderCity       string `excel:"title:Sender City"`
	SenderDistrict   string `excel:"title:Sender District"`
	SenderStreet     string `excel:"title:Sender Street"`
	ReceiverRegion   string `excel:"title:Receiver Region"`
	ReceiverState    string `excel:"title:Receiver State"`
	ReceiverCity     string `excel:"title:Receiver City"`
	ReceiverDistrict string `excel:"title:Receiver District"`
	ReceiverStreet   string `excel:"title:Receiver Street"`
	Operator         string `excel:"title:Operator"`
	EffectiveTime    string `excel:"title:Effective time"`
}

type excelCepRouteFixEdt struct {
	ProductID          string `excel:"title:Product ID"`
	ClientGroupID      string `excel:"title:Client Group ID"`
	CutoffTime         string `excel:"title:Cutoff Time"`
	SenderRegion       string `excel:"title:Sender Region"`
	SenderCepInitial   string `excel:"title:Sender Cep Initial"`
	SenderCepFinal     string `excel:"title:Sender Cep Final"`
	ReceiverRegion     string `excel:"title:Receiver Region"`
	ReceiverCepInitial string `excel:"title:Receiver Cep Initial"`
	ReceiverCepFinal   string `excel:"title:Receiver Cep Final"`
	FixApt             string `excel:"title:Fixed APT"`
	FixCdt             string `excel:"title:Fixed CDT"`
}
