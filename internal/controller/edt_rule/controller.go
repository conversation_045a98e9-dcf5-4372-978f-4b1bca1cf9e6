package edt_rule

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/excel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/edt_rule"
	edt_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_rule"
	edt_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/edt_rule"
	"github.com/tealeg/xlsx"
	"regexp"
	"strconv"
	"strings"
)

var (
	routeFixedEdtTitles = []string{"Product ID", "Client Group ID", "Sender Region", "Sender State", "Sender City", "Sender District", "Sender Street",
		"Receiver Region", "Receiver State", "Receiver City", "Receiver District", "Receiver Street", "Fixed APT", "Fixed CDT", "Cutoff Time", "Operator", "Effective time"}
	cepRouteFixedEdtTitles = []string{"Product ID", "Client Group ID", "Cutoff Time", "Sender Region", "Sender Cep Initial", "Sender Cep Final", "Receiver Region",
		"Receiver Cep Initial", "Receiver Cep Final", "Fixed APT", "Fixed CDT"}
)

type EdtRuleController interface {
	CreateEdtRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ListEdtRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	GetEdtRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	DisableEdtRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportRouteFixedEdt(c *utils.HttpContext, rawRequest interface{})
	ListRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	UploadCepRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
	ExportCepRouteFixedEdt(c *utils.HttpContext, rawRequest interface{})
	ListCepRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError)
}

type EdtRuleControllerImpl struct {
	EdtRuleService edt_rule2.EdtRuleService
}

func NewEdtRuleControllerImpl(EdtRuleService edt_rule2.EdtRuleService) *EdtRuleControllerImpl {
	return &EdtRuleControllerImpl{
		EdtRuleService: EdtRuleService,
	}
}

func (i *EdtRuleControllerImpl) CreateEdtRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edt_rule.CreateEdtRuleRequest)
	err := i.EdtRuleService.CreateEdtRule(c, request)
	return nil, err
}

func (i *EdtRuleControllerImpl) ListEdtRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edt_rule.ListEdtRuleRequest)
	return i.EdtRuleService.ListEdtRule(c, request)
}

func (i *EdtRuleControllerImpl) GetEdtRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edt_rule.GetRuleDetailRequest)
	return i.EdtRuleService.GetEdtRule(c, request)
}

func (i *EdtRuleControllerImpl) DisableEdtRule(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edt_rule.GetRuleDetailRequest)
	return nil, i.EdtRuleService.DisableEdtRule(c, request)
}

func (i *EdtRuleControllerImpl) UploadRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edt_rule.UploadRouteFixedEdtReq)
	err := i.EdtRuleService.UploadRouteFixedEdt(c, request, strings.ToUpper(c.GetCountry()), c.GetUserName())
	return nil, err
}

func (i *EdtRuleControllerImpl) ExportRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) {
	request, _ := rawRequest.(*edt_rule.ListRouteFixedEdtReq)
	tabs, lErr := i.EdtRuleService.SearchRouteFixedEdt(c, request, strings.ToUpper(c.GetCountry()))
	if lErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lErr.Msg)
		return
	}
	// 导出
	resp := convertRouteFixedEdt(tabs)
	if len(resp) == 0 {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "find empty cutoff time from db")
		return
	}

	fileName := fmt.Sprintf("export_RouteFixEdt_%v.xlsx", utils.GetCurrentLocalTimeStampByRegion(c, strings.ToUpper(c.GetCountry())))
	// 将空格替换为下划线
	reg := regexp.MustCompile(" +")
	fileName = reg.ReplaceAllString(fileName, "_")
	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")
	file := xlsx.NewFile()

	var (
		excelTiles   = routeFixedEdtTitles
		excelRawData []interface{}
	)

	for _, unit := range resp {
		excelRawData = append(excelRawData, &excelRouteFixEdt{
			ProductID:        strconv.Itoa(unit.ProductID),
			ClientGroupID:    unit.ClientGroupID,
			SenderRegion:     unit.SenderRegion,
			SenderState:      unit.SenderState,
			SenderCity:       unit.SenderCity,
			SenderDistrict:   unit.SenderDistrict,
			SenderStreet:     unit.SenderStreet,
			ReceiverRegion:   unit.ReceiverRegion,
			ReceiverState:    unit.ReceiverState,
			ReceiverCity:     unit.ReceiverCity,
			ReceiverDistrict: unit.ReceiverDistrict,
			ReceiverStreet:   unit.ReceiverStreet,
			FixApt:           strconv.FormatInt(unit.FixApt, 10),
			FixCdt:           strconv.FormatInt(unit.FixCdt, 10),
			CutoffTime:       unit.CutoffTime,
			Operator:         unit.Operator,
			EffectiveTime:    utils.FormatTimestamp(uint32(unit.EffectiveStartTime), constant.DateAndTimeFormat),
		})
	}

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTiles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (i *EdtRuleControllerImpl) ListRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edt_rule.ListRouteFixedEdtReq)
	page, size := utils.GenPageAndCount(request.PageNo, request.Count)
	tabs, total, err := i.EdtRuleService.ListRouteFixedEdt(c, request, strings.ToUpper(c.GetCountry()), page, size)
	if err != nil {
		return nil, err
	}
	response := edt_rule.ListRouteFixedEdtResp{
		List:   convertRouteFixedEdt(tabs),
		Total:  total,
		PageNo: &page,
		Count:  &size,
	}
	return response, nil
}

func convertRouteFixedEdt(tabs []edt_rule3.LogisticRouteFixedGroupEdtTab) []edt_rule.RouteFixedEdtUnit {
	result := make([]edt_rule.RouteFixedEdtUnit, 0)
	for _, tab := range tabs {
		h, m, s := utils.GetHourMinuteAndSeconds(tab.CutoffTime)
		result = append(result, edt_rule.RouteFixedEdtUnit{
			ProductID:          tab.ProductId,
			ClientGroupID:      tab.ClientGroupId,
			Operator:           tab.Operator,
			EffectiveStartTime: tab.Ctime,
			FixCdt:             tab.FixCdt,
			FixApt:             tab.FixApt,
			CutoffTime:         fmt.Sprintf("%02d:%02d:%02d", h, m, s),
			SenderRegion:       tab.SenderRegion,
			SenderState:        tab.SenderState,
			SenderCity:         tab.SenderCity,
			SenderDistrict:     tab.SenderDistrict,
			SenderStreet:       tab.SenderStreet,
			ReceiverRegion:     tab.ReceiverRegion,
			ReceiverState:      tab.ReceiverState,
			ReceiverCity:       tab.ReceiverCity,
			ReceiverDistrict:   tab.ReceiverDistrict,
			ReceiverStreet:     tab.ReceiverStreet,
		})
	}

	return result
}

func (i *EdtRuleControllerImpl) UploadCepRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edt_rule.UploadCepRouteFixedEdtReq)
	err := i.EdtRuleService.UploadCepRouteFixedEdt(c, request, strings.ToUpper(c.GetCountry()), c.GetUserName())
	return nil, err
}

func (i *EdtRuleControllerImpl) ExportCepRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) {
	request, _ := rawRequest.(*edt_rule.ListCepRouteFixedEdtReq)
	tabs, lErr := i.EdtRuleService.SearchCepRouteFixedEdt(c, request, strings.ToUpper(c.GetCountry()))
	if lErr != nil {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, lErr.Msg)
		return
	}
	// 导出
	resp := convertCepRouteFixedEdt(tabs)
	if len(resp) == 0 {
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "find empty data from db")
		return
	}

	fileName := fmt.Sprintf("export_Cep_RouteFixEdt_%v.xlsx", utils.GetCurrentLocalTimeStampByRegion(c, strings.ToUpper(c.GetCountry())))
	// 将空格替换为下划线
	reg := regexp.MustCompile(" +")
	fileName = reg.ReplaceAllString(fileName, "_")
	c.Context.AddHeader("Content-Type", "application/octet-stream")
	c.Context.AddHeader("Content-Disposition", "attachment; filename="+fileName)
	c.Context.AddHeader("Content-Transfer-Encoding", "binary")
	file := xlsx.NewFile()

	var (
		excelTiles   = cepRouteFixedEdtTitles
		excelRawData []interface{}
	)

	for _, unit := range resp {
		excelRawData = append(excelRawData, &excelCepRouteFixEdt{
			ProductID:          strconv.Itoa(unit.ProductID),
			ClientGroupID:      unit.ClientGroupID,
			CutoffTime:         unit.CutoffTime,
			SenderRegion:       unit.SenderRegion,
			SenderCepInitial:   utils.IntPointerToString(unit.SenderCepInitial),
			SenderCepFinal:     utils.IntPointerToString(unit.SenderCepFinal),
			ReceiverRegion:     unit.ReceiverRegion,
			ReceiverCepInitial: utils.IntPointerToString(unit.ReceiverCepInitial),
			ReceiverCepFinal:   utils.IntPointerToString(unit.ReceiverCepFinal),
			FixApt:             strconv.FormatInt(unit.FixApt, 10),
			FixCdt:             strconv.FormatInt(unit.FixCdt, 10),
		})
	}

	sheetName := "Sheet1"
	if err := excel.WriteTitleAndStruct(file, sheetName, excelTiles, excelRawData); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}

	if err := file.Write(c.Context.Response().ResponseWriter); err != nil {
		logger.CtxLogErrorf(c, "Export|failed,err=%v", err)
		http.GenErrorResponseWithParam(c.Context, lcos_error.DownloadFileErrorCode, nil, "error in generate excel")
		return
	}
}

func (i *EdtRuleControllerImpl) ListCepRouteFixedEdt(c *utils.HttpContext, rawRequest interface{}) (interface{}, *lcos_error.LCOSError) {
	request, _ := rawRequest.(*edt_rule.ListCepRouteFixedEdtReq)
	page, size := utils.GenPageAndCount(request.PageNo, request.Count)
	tabs, total, err := i.EdtRuleService.ListCepRouteFixedEdt(c, request, strings.ToUpper(c.GetCountry()), page, size)
	if err != nil {
		return nil, err
	}
	response := edt_rule.ListCepRouteFixedEdtResp{
		List:   convertCepRouteFixedEdt(tabs),
		Total:  total,
		PageNo: &page,
		Count:  &size,
	}
	return response, nil
}

func convertCepRouteFixedEdt(tabs []*edt_rule3.LogisticCepRouteFixedGroupEdtTab) []edt_rule.CepRouteFixedEdtUnit {
	result := make([]edt_rule.CepRouteFixedEdtUnit, 0)
	for _, tab := range tabs {
		h, m, s := utils.GetHourMinuteAndSeconds(tab.CutoffTime)
		result = append(result, edt_rule.CepRouteFixedEdtUnit{
			ProductID:          tab.ProductId,
			ClientGroupID:      tab.ClientGroupId,
			Operator:           tab.Operator,
			EffectiveTime:      tab.Ctime,
			FixCdt:             tab.FixCdt,
			FixApt:             tab.FixApt,
			CutoffTime:         fmt.Sprintf("%02d:%02d:%02d", h, m, s),
			SenderRegion:       tab.SenderRegion,
			SenderCepInitial:   tab.GetSenderCepInitial(),
			SenderCepFinal:     tab.GetSenderCepFinal(),
			ReceiverRegion:     tab.ReceiverRegion,
			ReceiverCepInitial: tab.GetReceiverCepInitial(),
			ReceiverCepFinal:   tab.GetReceiverCepFinal(),
		})
	}

	return result
}
