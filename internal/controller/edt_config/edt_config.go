package edt_config

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/edt_config_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/edt_config_service"
)

type EdtConfigController interface {
	CreateProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError)
	UpdateProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError)
	DeleteProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError)

	GetProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError)
	ListProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError)
}

func NewEdtConfigController(edtConfigService edt_config_service.EdtConfigService) *edtConfigController {
	return &edtConfigController{
		EdtConfigService: edtConfigService,
	}
}

type edtConfigController struct {
	EdtConfigService edt_config_service.EdtConfigService
}

func (e *edtConfigController) CreateProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := request.(*edt_config_protocol.CreateProductEdtConfigRequest)
	err := e.EdtConfigService.CreateProductEdtConfig(c, req)
	return nil, err
}

func (e *edtConfigController) UpdateProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := request.(*edt_config_protocol.UpdateProductEdtConfigRequest)
	err := e.EdtConfigService.UpdateProductEdtConfig(c, req)
	return nil, err
}

func (e *edtConfigController) DeleteProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := request.(*edt_config_protocol.DeleteProductEdtConfigRequest)
	err := e.EdtConfigService.DeleteProductEdtConfig(c, req.Id)
	return nil, err
}

func (e *edtConfigController) GetProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := request.(*edt_config_protocol.GetProductEdtConfigRequest)
	resp, err := e.EdtConfigService.GetProductEdtConfig(c, req.Id)
	return resp, err
}

func (e *edtConfigController) ListProductEdtConfig(c *utils.HttpContext, request interface{}) (interface{}, *lcos_error.LCOSError) {
	req, _ := request.(*edt_config_protocol.ListProductEdtConfigRequest)
	resp, err := e.EdtConfigService.ListProductEdtConfig(c, req)
	return resp, err
}
