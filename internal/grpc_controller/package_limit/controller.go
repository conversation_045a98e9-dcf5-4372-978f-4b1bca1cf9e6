package package_limit

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	Logger "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	calculateWeightService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/calculate_weight"
	lineService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/line_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/package_limit_util"
	productService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/product_package_limit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"strconv"
)

type grpcPackageLimitController struct {
	pb.UnimplementedLcosWeightLimitServiceServer

	lineService            lineService.LinePackageLimitServiceInterface
	productService         productService.ProductPackageLimitServiceInterface
	calculateWeightService calculateWeightService.CalculateWeightServiceInterface
}

func NewGrpcPackageLimitController(m lineService.LinePackageLimitServiceInterface,
	productService productService.ProductPackageLimitServiceInterface,
	calculateWeightService calculateWeightService.CalculateWeightServiceInterface) *grpcPackageLimitController {
	return &grpcPackageLimitController{
		lineService:            m,
		productService:         productService,
		calculateWeightService: calculateWeightService,
	}
}

// @core
func (c *grpcPackageLimitController) CheckLineRule(ctx context.Context, req *pb.CheckLineRuleRequest) (*pb.CheckLineRuleResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	checkInfos := make([]*pb.SingleCheckLineRuleResult, 0, len(req.LineId))

	// 如果传了SubPackageInfo，并且SubPackageInfo数据都valid，使用SubPackageInfo替代SkuInfo进行计算
	// 目前用于合单流程
	calculateSku := req.GetSkuInfo()
	if package_limit_util.CheckSubPackageInfoValid(req.GetSubPackageInfo()) {
		calculateSku = package_limit_util.ConvertSubPackageInfo(req.GetSubPackageInfo())
		logger.LogInfof("SubPackageInfo is valid, use SubPackageInfo instead of SkuInfo")
	}

	if err := c.calculateWeightService.CheckSkuParams(ctx, calculateSku...); err != nil {
		return &pb.CheckLineRuleResponse{
			RespHeader:    http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			RuleLimitInfo: checkInfos,
		}, nil
	}

	isAllSuccess := true
	failedLine := make([]string, 0, len(req.LineId))
	for _, lineId := range req.LineId {
		checkInfo, err := c.lineService.CheckLineRule(lcosCtx, lineId, calculateSku, req.OrderInfo, req.GetBuyerPurchaseTime())
		resultLineId := lineId
		if err != nil {
			_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(err.RetCode)), "line": lineId})
			checkResult := uint32(constant.FAILED)
			checkInfos = append(checkInfos, &pb.SingleCheckLineRuleResult{
				LineId:      &resultLineId,
				CheckResult: &checkResult,
			})
			isAllSuccess = false
			failedLine = append(failedLine, resultLineId)
		} else {
			_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(*checkInfo.CheckResult)), "line": lineId})
			checkInfos = append(checkInfos, checkInfo)
			if *checkInfo.CheckResult == uint32(constant.FAILED) {
				isAllSuccess = false
				failedLine = append(failedLine, lineId)
			}
		}
	}
	if isAllSuccess {
		return &pb.CheckLineRuleResponse{
			RespHeader:    http.GrpcSuccessRespHeader(),
			RuleLimitInfo: checkInfos,
		}, nil
	} else {
		if len(failedLine) == len(req.LineId) {
			return &pb.CheckLineRuleResponse{
				RespHeader:    http.GrpcErrorRespHeaderWithParam(lcos_error.CheckPackageLimitFullyFailedErrorCode, "all line check package limit failed"),
				RuleLimitInfo: checkInfos,
			}, nil
		} else {
			return &pb.CheckLineRuleResponse{
				RespHeader:    http.GrpcErrorRespHeaderWithParam(lcos_error.CheckPackageLimitPartialFailedErrorCode, fmt.Sprintf("some line check package limit failed, failed line list: %s", failedLine)),
				RuleLimitInfo: checkInfos,
			}, nil
		}
	}
}

// @core
func (c *grpcPackageLimitController) BatchCheckLineRule(ctx context.Context, req *pb.BatchCheckLineRuleRequest) (*pb.BatchCheckLineRuleResponse, error) {
	var result = make(map[string]*pb.CheckLineRuleResponse)
	// 封装请求参数
	for _, item := range req.GetCheckLineRuleList() {
		if item.GetUniqueId() == "" {
			return &pb.BatchCheckLineRuleResponse{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is required"),
			}, nil
		}
		singleRequest := &pb.CheckLineRuleRequest{
			ReqHeader:         req.GetReqHeader(),
			SkuInfo:           item.GetSkuInfo(),
			LineId:            item.GetLineId(),
			OrderInfo:         item.GetOrderInfo(),
			SubPackageInfo:    item.GetSubPackageInfo(),
			BuyerPurchaseTime: item.BuyerPurchaseTime,
		}
		response, lcosErr := c.CheckLineRule(ctx, singleRequest)
		if lcosErr != nil {
			result[item.GetUniqueId()] = &pb.CheckLineRuleResponse{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.ServerErrorCode, lcosErr.Error()),
			}
		} else {
			response.RespHeader.RequestId = req.GetReqHeader().RequestId
			result[item.GetUniqueId()] = response
		}
	}
	return &pb.BatchCheckLineRuleResponse{
		RespHeader:             http.GrpcSuccessRespHeader(),
		CheckLineRuleResultMap: result,
	}, nil
}

// @core
func (c *grpcPackageLimitController) GetLineRule(ctx context.Context, req *pb.GetLineRuleRequest) (*pb.GetLineRuleResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	ruleInfo, err := c.lineService.GetLineRule(lcosCtx, *req.LineId)
	if err != nil {
		return &pb.GetLineRuleResponse{
			RespHeader:     http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			RuleInfoDetail: nil,
		}, nil
	}

	return &pb.GetLineRuleResponse{
		RespHeader:     http.GrpcSuccessRespHeader(),
		RuleInfoDetail: ruleInfo,
	}, nil
}

// @core
func (c *grpcPackageLimitController) BatchGetLineRule(ctx context.Context, req *pb.BatchGetLineRuleRequest) (*pb.BatchGetLineRuleResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	ruleInfoList := make([]*pb.LineRuleInfo, 0, len(req.LineId))
	for _, reqLineId := range req.LineId {
		lineId := reqLineId
		ruleInfo, err := c.lineService.GetLineRule(lcosCtx, lineId)
		if err == nil {
			ruleInfoList = append(ruleInfoList, &pb.LineRuleInfo{
				LineId:         &lineId,
				RuleInfoDetail: ruleInfo,
			})
		}
	}

	return &pb.BatchGetLineRuleResponse{
		RespHeader:   http.GrpcSuccessRespHeader(),
		LineRuleInfo: ruleInfoList,
	}, nil
}

// @core
func (c *grpcPackageLimitController) CalculateWeight(ctx context.Context, req *pb.CalculateWeightRequest) (*pb.CalculateWeightResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	response, err := c.calculateWeightService.CalculateWeight(lcosCtx, req)
	if err != nil {
		return &pb.CalculateWeightResponse{
			RespHeader:   http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			ResultWeight: nil,
		}, nil
	}
	return response, nil
}

// @core
func (c *grpcPackageLimitController) GetProductRule(ctx context.Context, req *pb.GetProductRuleRequest) (*pb.GetProductRuleResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	ruleInfo, err := c.productService.GetProductRule(lcosCtx, *req.ProductId)
	if err != nil {
		return &pb.GetProductRuleResponse{
			RespHeader:     http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			RuleInfoDetail: nil,
		}, nil
	}

	return &pb.GetProductRuleResponse{
		RespHeader:     http.GrpcSuccessRespHeader(),
		RuleInfoDetail: ruleInfo,
	}, nil
}

// @core
func (c *grpcPackageLimitController) CheckProductRule(ctx context.Context, req *pb.CheckProductRuleRequest) (*pb.CheckProductRuleResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	checkInfos := make([]*pb.SingleCheckProductRuleResult, 0, len(req.ProductId))

	// 如果传了SubPackageInfo，并且SubPackageInfo数据都valid，使用SubPackageInfo替代SkuInfo进行计算
	// 目前用于合单流程
	calculateSku := req.GetSkuInfo()
	if package_limit_util.CheckSubPackageInfoValid(req.GetSubPackageInfo()) {
		calculateSku = package_limit_util.ConvertSubPackageInfo(req.GetSubPackageInfo())
		logger.LogInfof("SubPackageInfo is valid, use SubPackageInfo instead of SkuInfo")
	}

	if err := c.calculateWeightService.CheckSkuParams(ctx, calculateSku...); err != nil {
		return &pb.CheckProductRuleResponse{
			RespHeader:    http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			RuleLimitInfo: checkInfos,
		}, nil
	}

	isAllSuccess := true
	var failedProduct []string
	for _, productId := range req.ProductId {
		checkInfo, err := c.productService.CheckProductRule(lcosCtx, productId, calculateSku, req.OrderInfo, req.GetBuyerPurchaseTime())
		resultProductId := productId
		if err != nil {
			_ = metrics.CounterIncr(constant.MetricProductStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(err.RetCode)), "product": resultProductId})
			checkResult := uint32(constant.FAILED)
			checkInfos = append(checkInfos, &pb.SingleCheckProductRuleResult{
				ProductId:   &resultProductId,
				CheckResult: &checkResult,
			})
			isAllSuccess = false
			failedProduct = append(failedProduct, resultProductId)
		} else {
			_ = metrics.CounterIncr(constant.MetricProductStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(*checkInfo.CheckResult)), "product": resultProductId})
			checkInfos = append(checkInfos, checkInfo)
			if *checkInfo.CheckResult == uint32(constant.FAILED) {
				isAllSuccess = false
				failedProduct = append(failedProduct, resultProductId)
			}
		}
	}
	if isAllSuccess {
		return &pb.CheckProductRuleResponse{
			RespHeader:    http.GrpcSuccessRespHeader(),
			RuleLimitInfo: checkInfos,
		}, nil
	} else {
		if len(failedProduct) == len(req.ProductId) {
			return &pb.CheckProductRuleResponse{
				RespHeader:    http.GrpcErrorRespHeaderWithParam(lcos_error.CheckPackageLimitFullyFailedErrorCode, "all product check package limit failed"),
				RuleLimitInfo: checkInfos,
			}, nil
		} else {
			return &pb.CheckProductRuleResponse{
				RespHeader:    http.GrpcErrorRespHeaderWithParam(lcos_error.CheckPackageLimitPartialFailedErrorCode, fmt.Sprintf("some product check package limit failed, failed product list: %s", failedProduct)),
				RuleLimitInfo: checkInfos,
			}, nil
		}
	}
}

// @core
func (c *grpcPackageLimitController) BatchCheckProductRule(ctx context.Context, req *pb.BatchCheckProductRuleRequest) (*pb.BatchCheckProductRuleResponse, error) {
	// 循环调用得到结果，并且将结果封装为map
	results := &pb.BatchCheckProductRuleResponse{
		RespHeader:                http.GrpcSuccessRespHeader(),
		CheckProductRuleResultMap: map[string]*pb.CheckProductRuleResponse{},
	}
	for _, singleCheckItem := range req.GetProductRules() {
		// unique id必填，且不能为空
		if len(singleCheckItem.GetUniqueId()) == 0 {
			return &pb.BatchCheckProductRuleResponse{
				RespHeader:                http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is required and cannot be empty"),
				CheckProductRuleResultMap: nil,
			}, nil
		}
		tmpRequest := &pb.CheckProductRuleRequest{
			ReqHeader:         req.GetReqHeader(),
			SkuInfo:           singleCheckItem.GetSkuInfo(),
			ProductId:         singleCheckItem.GetProductId(),
			OrderInfo:         singleCheckItem.GetOrderInfo(),
			SubPackageInfo:    singleCheckItem.GetSubPackageInfo(),
			BuyerPurchaseTime: singleCheckItem.BuyerPurchaseTime,
		}
		tmpResult, _ := c.CheckProductRule(ctx, tmpRequest)
		// 填充request id
		tmpResult.RespHeader.RequestId = req.GetReqHeader().RequestId
		results.CheckProductRuleResultMap[singleCheckItem.GetUniqueId()] = tmpResult
	}
	return results, nil
}

// 合单场景，提供给算法侧做组合包裹限重规则校验，独立接口便于限流
// @core
func (c *grpcPackageLimitController) BatchCheckProductRuleForAlgo(ctx context.Context, req *pb.BatchCheckProductRuleRequest) (*pb.BatchCheckProductRuleResponse, error) {
	return c.BatchCheckProductRule(ctx, req)
}

func (c *grpcPackageLimitController) CheckProductRuleForShoppingCart(ctx context.Context, req *pb.SingleProductRule) (*pb.CheckProductRuleForShoppingCartResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)

	calculateSku := req.GetSkuInfo()
	if package_limit_util.CheckSubPackageInfoValid(req.GetSubPackageInfo()) {
		calculateSku = package_limit_util.ConvertSubPackageInfo(req.GetSubPackageInfo())
		logger.LogInfof("SubPackageInfo is valid, use SubPackageInfo instead of SkuInfo")
	}

	if err := c.calculateWeightService.CheckSkuParams(ctx, calculateSku...); err != nil {
		return &pb.CheckProductRuleForShoppingCartResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	resultMap := make([]*pb.SingleProductCheckResult, 0, len(req.ProductId))
	for _, productId := range req.ProductId {
		checkResult, err := c.productService.CheckProductRuleAndCalculateVolumetricWeight(lcosCtx, productId, calculateSku, req.OrderInfo)
		if err != nil {
			resultMap = append(resultMap, &pb.SingleProductCheckResult{
				ProductId:   utils.NewString(productId),
				CheckResult: utils.NewUint32(uint32(constant.FAILED)),
			})
			_ = metrics.CounterIncr(constant.MetricProductStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(err.RetCode)), "product": productId})
		} else {
			resultMap = append(resultMap, checkResult)
			_ = metrics.CounterIncr(constant.MetricProductStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(checkResult.GetCheckResult())), "product": productId})
		}
	}
	return &pb.CheckProductRuleForShoppingCartResponse{
		RespHeader:         http.GrpcSuccessRespHeader(),
		ProductCheckResult: resultMap,
	}, nil
}

func (c *grpcPackageLimitController) BatchCheckProductRuleForShoppingCart(ctx context.Context, req *pb.BatchCheckProductRuleForShoppingCartRequest) (*pb.BatchCheckProductRuleForShoppingCartResponse, error) {
	resultMap := make(map[string]*pb.CheckProductRuleForShoppingCartResponse, len(req.GetProductRules()))
	for _, singleCheckItem := range req.GetProductRules() {
		if len(singleCheckItem.GetUniqueId()) == 0 {
			return &pb.BatchCheckProductRuleForShoppingCartResponse{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is required and cannot be empty"),
			}, nil
		}

		singleCheckResult, _ := c.CheckProductRuleForShoppingCart(ctx, singleCheckItem)
		singleCheckResult.RespHeader.RequestId = req.GetReqHeader().RequestId
		resultMap[singleCheckItem.GetUniqueId()] = singleCheckResult
	}
	return &pb.BatchCheckProductRuleForShoppingCartResponse{
		RespHeader:                http.GrpcSuccessRespHeader(),
		CheckProductRuleResultMap: resultMap,
	}, nil
}

// @core
func (c *grpcPackageLimitController) BatchCalculateWeight(ctx context.Context, req *pb.BatchCalculateWeightRequest) (*pb.BatchCalculateWeightResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	response, err := c.calculateWeightService.BatchCalculateWeight(lcosCtx, req)
	if err != nil {
		return &pb.BatchCalculateWeightResponse{
			RespHeader:      http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			CalculateResult: nil,
		}, nil
	}
	return response, nil
}

// 批量获取产品的校验重量
// @core
func (c *grpcPackageLimitController) BatchCalculateProductValidateWeight(ctx context.Context, req *pb.BatchCalculateProductValidateWeightRequest) (*pb.BatchCalculateProductValidateWeightResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	response, err := c.calculateWeightService.BatchCalculateProductValidateWeight(lcosCtx, req)
	if err != nil {
		return &pb.BatchCalculateProductValidateWeightResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			ResultList: nil,
		}, nil
	}
	return response, nil
}

// 批量获取产品rule详情
// @core
func (c *grpcPackageLimitController) BatchGetProductRule(ctx context.Context, req *pb.BatchGetProductRuleRequest) (*pb.BatchGetProductRuleResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	productLimitMap, err := c.productService.BatchGetProductRule(lcosCtx, req.ProductIdList)
	if err != nil {
		return &pb.BatchGetProductRuleResponse{
			RespHeader:         http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			RuleInfoDetailList: nil,
		}, nil
	}
	returnResult := &pb.BatchGetProductRuleResponse{
		RespHeader:         http.GrpcSuccessRespHeader(),
		RuleInfoDetailList: map[string]*pb.RealRuleInfoDetailList{},
	}
	for productID, limitList := range productLimitMap {
		realRuleInfoDetailList := &pb.RealRuleInfoDetailList{
			RuleInfoDetail: []*pb.RealRuleInfoDetail{},
		}
		for _, limit := range limitList {
			ruleType := uint32(limit.RuleType)
			limitFlag := uint32(limit.LimitFlag)
			maxSymbol := uint32(limit.MaxSymbol)
			minSymbol := uint32(limit.MinSymbol)
			sortFlag := uint32(limit.SortFlag)
			realRuleInfoDetailList.RuleInfoDetail = append(realRuleInfoDetailList.RuleInfoDetail, &pb.RealRuleInfoDetail{
				Id:                     &limit.ID,
				ProductId:              &limit.ProductId,
				Region:                 &limit.Region,
				RuleType:               &ruleType,
				LimitFlag:              &limitFlag,
				MaxWeight:              &limit.MaxWeight,
				MinWeight:              &limit.MinWeight,
				MaxSize:                &limit.MaxSize,
				MinSize:                &limit.MinSize,
				MaxLength:              &limit.MaxLength,
				MinLength:              &limit.MinLength,
				MaxWidth:               &limit.MaxWidth,
				MinWidth:               &limit.MinWidth,
				MaxHeight:              &limit.MaxHeight,
				MinHeight:              &limit.MinHeight,
				MaxSymbol:              &maxSymbol,
				MinSymbol:              &minSymbol,
				VolumetricFactor:       &limit.VolumetricFactor,
				SortFlag:               &sortFlag,
				Formula:                &limit.Formula,
				ConditionFormulaParams: &limit.ConditionFormulaParams,
			})
		}
		returnResult.RuleInfoDetailList[productID] = realRuleInfoDetailList
	}
	return returnResult, nil
}

// 获取TW 重量size信息
// @core
func (c *grpcPackageLimitController) GetTWSizeInfos(ctx context.Context, req *pb.GetTWSizeInfosRequest) (*pb.GetTWSizeInfosResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	response, err := c.productService.GetTWSizeInfos(lcosCtx, req)
	if err != nil {
		return &pb.GetTWSizeInfosResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			SizeInfos:  nil,
		}, nil
	}
	return response, nil
}

// @core
func (c *grpcPackageLimitController) BatchGetProductSideLimits(ctx context.Context, req *pb.ProductSideLimitsRequest) (*pb.ProductSideLimitsResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	limits, err := c.productService.GetProductSideLimits(lcosCtx, req.ProductIdList)
	if err != nil {
		return &pb.ProductSideLimitsResponse{
			RespHeader:     http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			SizeLimitsInfo: nil,
		}, nil
	}
	return &pb.ProductSideLimitsResponse{
		RespHeader:     http.GrpcSuccessRespHeader(),
		SizeLimitsInfo: limits,
	}, nil
}

// parse line rule result
func getLaneCodeResult(results []*pb.SingleCheckLineRuleResult) (int32, string, uint32) {
	resultCode := lcos_error.SuccessCode
	message := ""
	var ruleType uint32
	for _, result := range results {
		if result.GetCheckResult() != uint32(lcos_error.SuccessCode) {
			resultCode = lcos_error.CheckPackageLimitFullyFailedErrorCode
			if len(result.GetLimitDetail()) > 0 {
				for _, limit := range result.GetLimitDetail() {
					if limit.GetReason() != "success" {
						message = limit.GetReason()
						ruleType = limit.GetRuleType()
						break
					}
				}
			}
			break
		}
	}
	return resultCode, message, ruleType
}

// @core
func (c *grpcPackageLimitController) BatchCheckLaneRule(ctx context.Context, request *pb.BatchCheckLaneRuleRequest) (*pb.BatchCheckLaneRuleResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	checkLaneRuleResultMap := make(map[string]*pb.CheckLaneRuleResponse, len(request.GetCheckLaneRuleList()))

	for _, singleLaneCodeList := range request.GetCheckLaneRuleList() {
		logger.CtxLogInfof(ctx, "ready to request lfs for lane code info|lane_code=[%s], region=[%s]", Logger.JsonStringForDebugLog(ctx, singleLaneCodeList.GetLaneCodeList()))
		lfsService := lfs_service.NewLFSService(ctx, singleLaneCodeList.GetRegion())
		laneCodeMap, lcosErr := lfsService.GetLaneCodeMapWithGreySwitch(lcosCtx, singleLaneCodeList.GetLaneCodeList())
		if lcosErr != nil {
			logger.CtxLogErrorf(ctx, "request lfs error|error=%s", lcosErr.Msg)
		} else {
			logger.CtxLogInfof(ctx, "successfully request lfs for lane code info|lane_code=[%s], region=[%s]", Logger.JsonStringForDebugLog(ctx, singleLaneCodeList.GetLaneCodeList()))
		}

		var laneCodeResultMap = make(map[string]*pb.SingleCheckLaneRuleResult)

		for _, singleLaneCodeInfo := range singleLaneCodeList.GetLaneCodeList() {
			if laneCodeInfo, ok := laneCodeMap[singleLaneCodeInfo]; !ok {
				laneCodeResultMap[singleLaneCodeInfo] = &pb.SingleCheckLaneRuleResult{
					LaneCode: utils.NewString(singleLaneCodeInfo),
					Code:     utils.NewInt32(lcos_error.NotFoundLaneCodeErrorCode),
					Message:  utils.NewString("lane code not found"),
				}
			} else if laneCodeInfo.ErrCode != int(lcos_error.SuccessCode) {
				laneCodeResultMap[singleLaneCodeInfo] = &pb.SingleCheckLaneRuleResult{
					LaneCode: utils.NewString(singleLaneCodeInfo),
					Code:     utils.NewInt32(int32(laneCodeInfo.ErrCode)),
					Message:  utils.NewString(laneCodeInfo.Message),
				}
			} else {
				lineList := make([]string, 0, len(laneCodeInfo.Composes))
				for _, singleComposed := range laneCodeInfo.Composes {
					if singleComposed.IsLineRuleCheck() {
						lineList = append(lineList, singleComposed.ResourceID)
					}
				}

				checkLineRuleRequest := &pb.CheckLineRuleRequest{
					ReqHeader:         request.GetReqHeader(),
					SkuInfo:           singleLaneCodeList.GetSkuInfo(),
					LineId:            lineList,
					BuyerPurchaseTime: singleLaneCodeList.BuyerPurchaseTime,
				}
				if singleLaneCodeList.GetSubPackageInfo() != nil {
					checkLineRuleRequest.SubPackageInfo = singleLaneCodeList.SubPackageInfo
				}

				// call batch line rule
				rule, _ := c.CheckLineRule(ctx, checkLineRuleRequest)

				// get lane code result info. Return code is 0, if all line succeeded. Return code is non-zero, if one of line result failed
				code, message, ruleType := getLaneCodeResult(rule.GetRuleLimitInfo())

				laneCodeResultMap[singleLaneCodeInfo] = &pb.SingleCheckLaneRuleResult{
					LaneCode:    utils.NewString(singleLaneCodeInfo),
					Code:        utils.NewInt32(code),
					Message:     utils.NewString(message),
					RuleType:    utils.NewUint32(ruleType),
					LineRuleRes: rule.GetRuleLimitInfo(),
				}
			}
		}

		checkLaneRuleResultMap[singleLaneCodeList.GetUniqueId()] = &pb.CheckLaneRuleResponse{
			RuleLimitInfoMap: laneCodeResultMap,
		}
	}
	return &pb.BatchCheckLaneRuleResponse{
		RespHeader:             http.GrpcSuccessRespHeader(),
		CheckLaneRuleResultMap: checkLaneRuleResultMap,
	}, nil
}

// @core
func (c *grpcPackageLimitController) CalculateFormula(ctx context.Context, request *pb.CalculateFormulaRequest) (*pb.CalculateFormulaResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	response, err := c.calculateWeightService.CalculateFormula(lcosCtx, request)
	if err != nil {
		return &pb.CalculateFormulaResponse{
			RespHeader:    http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			FormulaResult: nil,
		}, nil
	}
	return response, nil
}

// @core
func (c *grpcPackageLimitController) BatchCheckProductRuleForParcelLib(ctx context.Context, request *pb.BatchCheckProductRuleForParcelLibRequest) (*pb.BatchCheckProductRuleForParcelLibResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	response, err := c.productService.BatchCheckProductRuleForParcelLib(lcosCtx, request)
	if err != nil {
		return &pb.BatchCheckProductRuleForParcelLibResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			Results:    nil,
		}, nil
	}
	return response, nil
}
