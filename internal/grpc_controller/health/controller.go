package health

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type healthService struct{}

func NewHealthService() *healthService {
	return &healthService{}
}

func (h *healthService) Ping(ctx context.Context, req *pb.PingBody) (*pb.PingBody, error) {
	logger.LogInfof("Get ping message:%s\n", req.Str)
	reply := "pong"
	return &pb.PingBody{
		Str: &reply,
	}, nil
}
