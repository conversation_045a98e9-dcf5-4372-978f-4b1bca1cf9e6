package pickup_configuration

import (
	"context"

	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/common_utils"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_window_factory"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type pickupWindowController struct {
}

var _ pb.LcosPickupWindowServiceServer = (*pickupWindowController)(nil)

func NewPickupWindowController() *pickupWindowController {
	return &pickupWindowController{}
}

func generateEmptyPickupDays() *pb.PickupDays {
	var emptyString string
	return &pb.PickupDays{
		Days:          []*pb.Day{},
		Timeslots:     []*pb.Timeslot{},
		PickupGroupId: &emptyString,
	}
}

func generateEmptyTimeslots() *pb.TimeslotInfo {
	var empty uint32 = 0
	return &pb.TimeslotInfo{
		StartHour:   &empty,
		StartMinute: &empty,
		StartSecond: &empty,
		EndHour:     &empty,
		EndMinute:   &empty,
		EndSecond:   &empty,

		CutoffHour:   &empty,
		CutoffMinute: &empty,
		CutoffSecond: &empty,
	}
}

func (c *pickupWindowController) GetArrangedPickupDays(ctx context.Context, req *pb.GetArrangedPickupDaysRequest) (*pb.GetPickupDaysResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	// 请求参数封装
	volumes := make([]*common_utils.Volume, len(req.PickupInfo.Volumes))
	for i := 0; i < len(req.PickupInfo.Volumes); i++ {
		volumes[i] = &common_utils.Volume{
			Date:   req.PickupInfo.Volumes[i].GetDate(),
			Volume: req.PickupInfo.Volumes[i].GetVolume(),
		}
	}
	request := &common_utils.ArrangedPickupDaysRequest{
		ShipByDatetime:     req.PickupInfo.GetShipByDatetime(),
		LineIdList:         req.PickupInfo.GetLineIdList(),
		ReleaseTime:        req.PickupInfo.GetReleaseTime(),
		PayTime:            req.PickupInfo.GetPayTime(),
		DaysToShip:         req.PickupInfo.GetDaysToShip(),
		StateLocationId:    *req.PickupInfo.StateLocationId,
		Zipcode:            req.PickupInfo.GetZipcode(),
		Volumes:            volumes,
		BuyerTimeslotID:    req.PickupInfo.GetBuyerTimeslotId(),
		ClientGroupIDList:  req.PickupInfo.GetClientGroupIdList(),
		IsChangePickupDate: req.PickupInfo.GetIsChangePickupDate(),
		IsB2C:              req.PickupInfo.GetIsB2C(),
		Region:             req.PickupInfo.GetRegion(),
		Acl2Time:           req.PickupInfo.GetAcl2Time(),
		IsRapidSla:         req.PickupInfo.GetIsRapidSla(),
	}
	// 工厂方法返回service
	factory := pickup_window_factory.NewPickupWindowFactory(constant.LFS)
	service := factory.CreatePickupWindowService()
	result, llsErr := service.GetArrangedPickupDays(lcosCtx, request)
	if llsErr != nil {
		return &pb.GetPickupDaysResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(llsErr.RetCode, llsErr.Msg),
			PickupDays: generateEmptyPickupDays(),
		}, nil
	}
	// 封装返回结果
	days := make([]*pb.Day, len(result.Days))
	for index, item := range result.Days {
		days[index] = &pb.Day{}
		volumeLimit := uint32(item.VolumeLimit)
		days[index].Date = &item.Date
		days[index].Value = &item.Value
		days[index].VolumeLimit = &volumeLimit
	}

	timeslots := make([]*pb.Timeslot, len(result.Timeslots))
	for index, item := range result.Timeslots {
		slots := make([]*pb.Slot, len(item.PickupSlots))
		for _index, _item := range item.PickupSlots {
			slots[_index] = &pb.Slot{
				Value:            &_item.Value,
				Time:             &_item.SlotTime,
				StartTime:        &_item.StartTime,
				EndTime:          &_item.EndTime,
				SlotStartHour:    utils.NewUint32(_item.SlotStartHour),
				SlotCutoffHour:   utils.NewUint32(_item.SlotCutoffHour),
				SlotCutoffMinute: utils.NewUint32(_item.SlotCutoffMinute),
			}
		}
		timeslots[index] = &pb.Timeslot{
			Date:  &item.Date,
			Value: &item.Value,
			Slots: slots,
		}
	}
	returnedPickupDays := &pb.PickupDays{
		Days:             days,
		Timeslots:        timeslots,
		PickupGroupId:    &result.PickupGroupId,
		PickupCutoffHour: utils.NewUint32(result.PickupCutoffHour),
	}
	return &pb.GetPickupDaysResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		PickupDays: returnedPickupDays,
	}, nil
}

func (c *pickupWindowController) GetArrangedPickupDaysOpenLogistic(ctx context.Context, req *pb.GetArrangedPickupDaysOpenLogisticRequest) (*pb.GetPickupDaysResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	// 请求参数封装
	var startTime uint32
	if req.PickupInfo.StartTime != nil {
		startTime = *req.PickupInfo.StartTime
	} else {
		startTime = 0
	}
	var pickupLocationID int64
	if req.PickupInfo.PickupLocationId != nil {
		pickupLocationID = *req.PickupInfo.PickupLocationId
	} else {
		pickupLocationID = 0
	}
	var accountGroup uint32
	if req.PickupInfo.AccountGroup != nil {
		accountGroup = *req.PickupInfo.AccountGroup
	} else {
		accountGroup = 0
	}
	request := &common_utils.OpenLogisticArrangedPickupDaysRequest{
		AccountGroup:     accountGroup,
		LineIdList:       req.PickupInfo.LineIdList,
		MerchantType:     *req.PickupInfo.MerchantType,
		PickupLocationID: pickupLocationID,
		StartTime:        startTime,
	}
	// 工厂方法返回service
	factory := pickup_window_factory.NewPickupWindowFactory(constant.OPEN)
	service := factory.CreatePickupWindowService()
	result, llsErr := service.GetArrangedPickupDays(lcosCtx, request)
	if llsErr != nil {
		return &pb.GetPickupDaysResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(llsErr.RetCode, llsErr.Msg),
			PickupDays: generateEmptyPickupDays(),
		}, nil
	}
	// 封装返回结果
	days := make([]*pb.Day, len(result.Days))
	for index, item := range result.Days {
		days[index] = &pb.Day{}
		volumeLimit := uint32(item.VolumeLimit)
		days[index].Date = &item.Date
		days[index].Value = &item.Value
		days[index].VolumeLimit = &volumeLimit
	}

	timeslots := make([]*pb.Timeslot, len(result.Timeslots))
	for index, item := range result.Timeslots {
		slots := make([]*pb.Slot, len(item.PickupSlots))
		for _index, _item := range item.PickupSlots {
			slots[_index] = &pb.Slot{
				Value: &_item.Value,
				Time:  &_item.SlotTime,
			}
		}
		timeslots[index] = &pb.Timeslot{
			Date:  &item.Date,
			Value: &item.Value,
			Slots: slots,
		}
	}
	returnedPickupDays := &pb.PickupDays{
		Days:          days,
		Timeslots:     timeslots,
		PickupGroupId: &result.PickupGroupId,
	}
	return &pb.GetPickupDaysResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		PickupDays: returnedPickupDays,
	}, nil
}

func (c *pickupWindowController) GetReturnPickupDays(ctx context.Context, req *pb.GetReturnPickupDaysRequest) (*pb.GetPickupDaysResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	request := &common_utils.ReturnPickupDaysRequest{
		ShipByDays:      *req.PickupInfo.ShipByDays,
		LineIdList:      req.PickupInfo.LineIdList,
		StateLocationId: *req.PickupInfo.StateLocationId,
		Zipcode:         req.PickupInfo.GetZipcode(),
	}
	// 工厂方法返回service
	factory := pickup_window_factory.NewPickupWindowFactory(constant.LFS)
	service := factory.CreatePickupWindowService()
	result, llsErr := service.GetReturnedPickupDays(lcosCtx, request)
	if llsErr != nil {
		return &pb.GetPickupDaysResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(llsErr.RetCode, llsErr.Msg),
			PickupDays: generateEmptyPickupDays(),
		}, nil
	}
	// 封装返回结果
	days := make([]*pb.Day, len(result.Days))
	for index, item := range result.Days {
		days[index] = &pb.Day{}
		volumeLimit := uint32(item.VolumeLimit)
		days[index].Date = &item.Date
		days[index].Value = &item.Value
		days[index].VolumeLimit = &volumeLimit
	}

	timeslots := make([]*pb.Timeslot, len(result.Timeslots))
	for index, item := range result.Timeslots {
		timeslots[index] = &pb.Timeslot{}
		slots := make([]*pb.Slot, len(item.PickupSlots))
		for _index, _item := range item.PickupSlots {
			slots[_index] = &pb.Slot{}
			slots[_index].Value = &_item.Value
			slots[_index].Time = &_item.SlotTime
			slots[_index].SlotStartHour = utils.NewUint32(_item.SlotStartHour)
			slots[_index].SlotCutoffHour = utils.NewUint32(_item.SlotCutoffHour)
			slots[_index].SlotCutoffMinute = utils.NewUint32(_item.SlotCutoffMinute)
		}
		timeslots[index].Value = &item.Value
		timeslots[index].Date = &item.Date
		timeslots[index].Slots = slots
	}
	returnedPickupDays := &pb.PickupDays{
		Days:             days,
		Timeslots:        timeslots,
		PickupGroupId:    &result.PickupGroupId,
		PickupCutoffHour: utils.NewUint32(result.PickupCutoffHour),
	}
	return &pb.GetPickupDaysResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		PickupDays: returnedPickupDays,
	}, nil
}

func (c *pickupWindowController) CheckPickupTime(ctx context.Context, req *pb.CheckPickupTimeRequest) (*pb.CheckPickupTimeResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	var llsErr *lcos_error.LCOSError
	// 根据入参决定创建哪个服务
	if req.PickupInfo.AccountGroup != nil && req.PickupInfo.MerchantType != nil {
		request := &common_utils.CheckOpenLogisitcPickupTimeRequest{
			LineIdList:      req.PickupInfo.LineIdList,
			OriginRegion:    *req.PickupInfo.Country,
			StateLocationId: *req.PickupInfo.StateLocationId,
			PickupTime:      *req.PickupInfo.PickupTime,
			MerchantType:    *req.PickupInfo.MerchantType,
			AccountGroup:    *req.PickupInfo.AccountGroup,
		}
		// 工厂方法返回service
		factory := pickup_window_factory.NewPickupWindowFactory(constant.OPEN)
		service := factory.CreatePickupWindowService()
		llsErr = service.CheckPickupTime(lcosCtx, request)
	} else {
		request := &common_utils.CheckSiteLinePickupTimeRequest{
			LineIdList:        req.PickupInfo.LineIdList,
			OriginRegion:      *req.PickupInfo.Country,
			StateLocationId:   *req.PickupInfo.StateLocationId,
			PickupTime:        *req.PickupInfo.PickupTime,
			Zipcode:           req.PickupInfo.GetZipcode(),
			ClientGroupIDList: req.GetPickupInfo().GetClientGroupIdList(),
		}
		// 工厂方法返回service
		factory := pickup_window_factory.NewPickupWindowFactory(constant.LFS)
		service := factory.CreatePickupWindowService()
		llsErr = service.CheckPickupTime(lcosCtx, request)
	}
	if llsErr != nil {
		return &pb.CheckPickupTimeResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(llsErr.RetCode, llsErr.Msg),
		}, nil
	}
	return &pb.CheckPickupTimeResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

func (c *pickupWindowController) GetPickupTimeslot(ctx context.Context, req *pb.GetPickupTimeslotRequest) (*pb.GetPickupTimeslotsResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	var result *common_utils.SingleTimeslotInfo
	var llsErr *lcos_error.LCOSError
	// 通过入参决定是点线还是开放物流
	if req.MerchantType != nil && req.AccountGroup != nil {
		var startTime uint32
		if req.StartTime == nil {
			startTime = 0
		} else {
			startTime = *req.StartTime
		}
		request := &common_utils.GetSingleOpenLogisticPickupTimeslotRequest{
			LineIdList:        req.LineIdList,
			PickupTimeRangeId: *req.PickupTimeRangeId,
			MerchantType:      *req.MerchantType,
			AccountGroup:      *req.AccountGroup,
			StartTime:         startTime,
		}
		// 工厂方法返回service
		factory := pickup_window_factory.NewPickupWindowFactory(constant.OPEN)
		service := factory.CreatePickupWindowService()
		result, llsErr = service.GetPickupTimeslot(lcosCtx, request)
	} else {
		request := &common_utils.GetSingleSiteLinePickupTimeslotsRequest{
			LineIdList:        req.LineIdList,
			PickupTimeRangeId: *req.PickupTimeRangeId,
		}
		// 工厂方法返回service
		factory := pickup_window_factory.NewPickupWindowFactory(constant.LFS)
		service := factory.CreatePickupWindowService()
		result, llsErr = service.GetPickupTimeslot(lcosCtx, request)
	}
	if llsErr != nil {
		return &pb.GetPickupTimeslotsResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(llsErr.RetCode, llsErr.Msg),
			Timeslot:   generateEmptyTimeslots(),
		}, nil
	}

	return &pb.GetPickupTimeslotsResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		Timeslot: &pb.TimeslotInfo{
			StartHour:    &result.StartHour,
			StartMinute:  &result.StartMinute,
			StartSecond:  &result.StartSecond,
			EndHour:      &result.EndHour,
			EndMinute:    &result.EndMinute,
			EndSecond:    &result.EndSecond,
			CutoffHour:   proto.Uint32(result.CutoffHour),
			CutoffMinute: proto.Uint32(result.CutoffMinute),
			CutoffSecond: proto.Uint32(result.CutoffSecond),
		},
	}, nil

}

func (c *pickupWindowController) GetAllPickupGroups(ctx context.Context, req *pb.GetAllPickupGroupsRequest) (*pb.GetAllPickupGroupsResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	// 工厂方法返回service
	factory := pickup_window_factory.NewPickupWindowFactory(constant.LFS)
	service := factory.CreatePickupWindowService()
	results, llsErr := service.GetAllPickupGroups(lcosCtx)
	if llsErr != nil {
		return &pb.GetAllPickupGroupsResponse{
			RespHeader:   http.GrpcErrorRespHeaderWithParam(llsErr.RetCode, llsErr.Msg),
			PickupGroups: []*pb.PickupGroup{},
		}, nil
	}
	returnedResults := make([]*pb.PickupGroup, len(results))
	for index, item := range results {
		returnedResults[index] = &pb.PickupGroup{}
		returnedResults[index].LineIdList = item.LineIdList
		returnedResults[index].PickupGroupId = &item.PickupGroupId
		returnedResults[index].OriginRegion = &item.OriginRegion
		returnedResults[index].DestinationRegion = &item.DestinationRegion
		returnedResults[index].PickupType = &item.PickupType
		returnedResults[index].PickupGroupName = &item.PickupGroupName
		returnedResults[index].Ctime = &item.Ctime
		returnedResults[index].Mtime = &item.Mtime
	}
	return &pb.GetAllPickupGroupsResponse{
		RespHeader:   http.GrpcSuccessRespHeader(),
		PickupGroups: returnedResults,
	}, nil
}

func (c *pickupWindowController) QueryPickupTimeslots(ctx context.Context, req *pb.QueryPickupTimeslotsRequest) (*pb.QueryPickupTimeslotsResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	request := &common_utils.GetSiteLinePickupTimeslotsRequest{
		LineIdList:        req.LineIdList,
		PickupTimeRangeId: req.PickupTimeRangeId,
	}
	// 工厂方法返回service
	factory := pickup_window_factory.NewPickupWindowFactory(constant.LFS)
	service := factory.CreatePickupWindowService()
	results, llsErr := service.GetPickupTimeslots(lcosCtx, request)
	if llsErr != nil {
		return &pb.QueryPickupTimeslotsResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(llsErr.RetCode, llsErr.Msg),
			Timeslots:  []*pb.FullTimeslotInfo{},
		}, nil
	}
	returnedResults := make([]*pb.FullTimeslotInfo, len(results))
	for index, item := range results {
		status := uint32(item.EnableStatus)
		returnedResults[index] = &pb.FullTimeslotInfo{
			Id:              &item.ID,
			Type:            &item.TimeslotType,
			PickupCountry:   &item.OriginRegion,
			DeliverCountry:  &item.DestinationRegion,
			Value:           &item.TimeslotValue,
			StartTimeHour:   &item.StartTimeHour,
			StartTimeMin:    &item.StartTimeMin,
			StartTimeSecond: &item.StartTimeSecond,
			EndTimeHour:     &item.EndTimeHour,
			EndTimeMin:      &item.EndTimeMin,
			EndTimeSecond:   &item.EndTimeSecond,
			TimeSlotRemark:  &item.TimeslotRemark,
			Status:          &status,
			Ctime:           &item.CTime,
			Mtime:           &item.MTime,
		}

	}
	return &pb.QueryPickupTimeslotsResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		Timeslots:  returnedResults,
	}, nil
}

func (c *pickupWindowController) GetHolidays(ctx context.Context, req *pb.GetHolidaysRequest) (*pb.GetHolidaysResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	lineList := req.GetLineIdList()
	if lineList == nil {
		lineList = []string{}
	}
	request := &common_utils.GetHolidaysRequest{
		LineIdList:      lineList,
		Days:            int(req.GetDays()),
		StateLocationID: req.GetStateLocationId(),
		Zipcode:         req.GetZipcode(),
	}
	// 工厂方法返回service
	factory := pickup_window_factory.NewPickupWindowFactory(constant.LFS)
	service := factory.CreatePickupWindowService()
	results, llsErr := service.GetHolidays(lcosCtx, request)
	if llsErr != nil {
		return &pb.GetHolidaysResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(llsErr.RetCode, llsErr.Msg),
			Holidays:   []string{},
		}, nil
	}
	return &pb.GetHolidaysResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		Holidays:   results,
	}, nil
}

func (c *pickupWindowController) GetValidPickupConf(ctx context.Context, req *pb.GetValidPickupConfRequest) (*pb.GetValidPickupConfResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	request := &common_utils.GetValidPickupConfRequest{LineIdList: req.GetLineIdList()}
	// 工厂方法返回service
	factory := pickup_window_factory.NewPickupWindowFactory(constant.LFS)
	service := factory.CreatePickupWindowService()
	pickupConf, lcosErr := service.GetValidPickupConf(lcosCtx, request)
	if lcosErr != nil {
		return &pb.GetValidPickupConfResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
			PickupConf: nil,
		}, nil
	}
	isExtend := uint32(pickupConf.IsExtend)
	useReleaseTime := uint32(pickupConf.UseReleaseTime)
	isNonWorkingDayPickupAllowed := uint32(pickupConf.IsNonWorkingDayPickupAllowed)
	isHaveTimeSlot := uint32(pickupConf.IsHaveTimeSlot)
	enableStatus := uint32(pickupConf.EnableStatus)
	dailyControlStatus := uint32(pickupConf.DailyControlStatus)
	return &pb.GetValidPickupConfResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		PickupConf: &pb.PickupConf{
			Id:                           &pickupConf.ID,
			PickupGroupId:                &pickupConf.PickupGroupID,
			DestinationRegion:            &pickupConf.DestinationRegion,
			IsExtend:                     &isExtend,
			ExtendDays:                   &pickupConf.ExtendDays,
			UseReleaseTime:               &useReleaseTime,
			IsNonWorkingDayPickupAllowed: &isNonWorkingDayPickupAllowed,
			ReturnDays:                   &pickupConf.ReturnDays,
			AdvanceDays:                  &pickupConf.AdvanceDays,
			ReturnCutoffHour:             &pickupConf.ReturnCutoffHour,
			ReturnCutoffHourDays:         &pickupConf.ReturnCutoffHourDays,
			PickupDateFormat:             &pickupConf.PickupDateFormat,
			PickupCutoffHour:             &pickupConf.PickupCutoffHour,
			IsHaveTimeSlot:               &isHaveTimeSlot,
			SlotNum:                      &pickupConf.SlotNum,
			ExtendSlotNum:                &pickupConf.ExtendSlotNum,
			EffectiveTime:                &pickupConf.EffectiveTime,
			EnableStatus:                 &enableStatus,
			DailyMaxVolume:               &pickupConf.DailyMaxVolume,
			DailyControlStatus:           &dailyControlStatus,
			DailyMinDaysExtend:           &pickupConf.DailyMinDaysExtend,
			DailyControlBeginTime:        &pickupConf.DailyControlBeginTime,
			DailyControlEndTime:          &pickupConf.DailyControlEndTime,
			Ctime:                        &pickupConf.CTime,
			Mtime:                        &pickupConf.MTime,
		},
	}, nil
}

func (c *pickupWindowController) GetProductPickupDays(ctx context.Context, request *pb.GetProductPickupDaysRequest) (*pb.GetProductPickupDaysResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	lineList := request.GetPickupInfo().FmLines
	if lineList == nil {
		lineList = []string{}
	}

	// 工厂方法返回service
	factory := pickup_window_factory.NewPickupWindowFactory(constant.LFS)
	service := factory.CreatePickupWindowService()
	stateLocationID := int64(request.GetPickupInfo().GetStateId())
	req := &common_utils.GetPickupHolidaysRequest{
		LineIdList:      lineList,
		StateLocationID: stateLocationID,
		Zipcode:         request.GetPickupInfo().GetZipcode(),
	}
	holidays, lcosError := service.GetPickupHolidays(lcosCtx, req)
	if lcosError != nil {
		return &pb.GetProductPickupDaysResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcosError.RetCode, lcosError.Msg),
			PickupDays: nil,
		}, nil
	}

	return &pb.GetProductPickupDaysResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		PickupDays: &pb.ProductPickupDays{
			Status:                  &holidays.Status,
			PickupHolidays:          holidays.PickupHolidays,
			PickupRecurringHolidays: holidays.PickupRecurringHolidays,
		},
	}, nil
}
