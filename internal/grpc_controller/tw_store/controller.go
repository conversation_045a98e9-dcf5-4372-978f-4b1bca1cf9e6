package tw_store

import (
	"context"
	"fmt"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	tw_store2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tw_store"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/tw_store"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type twStoreController struct {
	pb.UnimplementedTWStoreServiceServer

	twStoreService tw_store.TWStoreService
}

// @core
func (t *twStoreController) SyncStoreByType(ctx context.Context, request *pb.SyncStoreByTypeRequest) (*pb.SyncStoreByTypeResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	lcosErr := t.twStoreService.ParseAndImportStores(lcosCtx, request.GetRemoteFilePath(), request.GetStoreType())
	if lcosErr != nil {
		// cat report
		interfaceName := fmt.Sprintf("store_type:%d, remote_file_path:%s", request.GetStoreType(), request.GetRemoteFilePath())
		_ = monitor.AwesomeReportEvent(ctx, constant.CatSyncStoreFailed, interfaceName, constant.StatusError, lcosErr.Msg)
		logger.CtxLogErrorf(ctx, "tw_store SyncStoreByType fail, err:%v", lcosErr)
		return &pb.SyncStoreByTypeResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
		}, nil
	}
	return &pb.SyncStoreByTypeResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

// @core
func (t *twStoreController) BatchGetStoreByStoreIDAndType(ctx context.Context, request *pb.BatchGetStoreByStoreIDRequest) (*pb.BatchGetStoreByStoreIDResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	resultMap := map[string]*pb.ConvenienceStore{}
	for _, singleRequest := range request.GetRequestList() {
		// unique id不能为空
		if singleRequest.GetUniqueId() == "" {
			return &pb.BatchGetStoreByStoreIDResponse{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is required"),
			}, nil
		}

		store, lcosErr := t.twStoreService.GetStoreByStoreIDAndType(lcosCtx, singleRequest.GetStoreId(), singleRequest.GetStoreType(), uint8(singleRequest.GetStatus()))
		if lcosErr != nil {
			logger.LogInfof("tw_store GetStoreByStoreIDAndType fail, err:%v", lcosErr)
		}

		if lcosErr == nil && store != nil {
			resultMap[singleRequest.GetUniqueId()] = convertStoreStructToPb(store)
		}
	}
	return &pb.BatchGetStoreByStoreIDResponse{
		RespHeader:   http.GrpcSuccessRespHeader(),
		ResponseDict: resultMap,
	}, nil
}

func (t *twStoreController) BatchGetStoreByStoreIdAndTypeWithoutStatus(ctx context.Context, request *pb.BatchGetStoreByStoreIDRequest) (*pb.BatchGetStoreByStoreIDResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	resultMap := make(map[string]*pb.ConvenienceStore, len(request.GetRequestList()))
	for _, singleRequest := range request.GetRequestList() {
		if len(singleRequest.GetUniqueId()) == 0 {
			return &pb.BatchGetStoreByStoreIDResponse{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is required"),
			}, nil
		}

		store, err := t.twStoreService.GetStoreByStoreIdAndTypeWithoutStatus(lcosCtx, singleRequest.GetStoreId(), singleRequest.GetStoreType())
		if err != nil {
			logger.LogInfof("tw_store GetStoreByStoreIDAndType fail, err:%v", err)
			continue
		}
		if store != nil {
			resultMap[singleRequest.GetUniqueId()] = convertStoreStructToPb(store)
		}
	}

	return &pb.BatchGetStoreByStoreIDResponse{
		RespHeader:   http.GrpcSuccessRespHeader(),
		ResponseDict: resultMap,
	}, nil
}

// @core
func (t *twStoreController) BatchGetStoreByAddressID(ctx context.Context, request *pb.BatchGetStoreByAddressIDRequest) (*pb.BatchGetStoreByStoreIDResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	resultMap := map[string]*pb.ConvenienceStore{}
	for _, addressID := range request.GetRequestList() {
		// address id不能为空
		addressIDInt, err := strconv.Atoi(addressID)
		if addressID == "" || err != nil {
			logger.LogInfof("tw_store convert address id to int fail, addressID:%s", addressID)
			return &pb.BatchGetStoreByStoreIDResponse{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, fmt.Sprintf("address id is not valid|address_id=%v", addressID)),
			}, nil
		}
		store, lcosErr := t.twStoreService.GetStoreByAddressID(lcosCtx, uint64(addressIDInt))
		if lcosErr != nil {
			logger.LogInfof("tw_store GetStoreByAddressID fail, err:%v", lcosErr)
		}
		if lcosErr == nil && store != nil {
			resultMap[addressID] = convertStoreStructToPb(store)
		}
	}
	return &pb.BatchGetStoreByStoreIDResponse{
		RespHeader:   http.GrpcSuccessRespHeader(),
		ResponseDict: resultMap,
	}, nil
}

// @core
func (t *twStoreController) BatchGetStoreByNewStoreIdAndType(ctx context.Context, reqeust *pb.BatchGetStoreByNewStoreIdAndTypeRequest) (*pb.BatchGetStoreByNewStoreIdAndTypeResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	res := map[string]*pb.ConvenienceStore{}
	for _, req := range reqeust.GetRequestList() {
		// check unique id is empty or not
		if req.GetUniqueId() == "" {
			logger.CtxLogInfof(lcosCtx, "single request unique id is empty")
			return &pb.BatchGetStoreByNewStoreIdAndTypeResponse{
				RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "unique id is empty"),
			}, nil
		}
		store, err := t.twStoreService.GetStoreByNewStoreIdAndType(lcosCtx, req.GetNewStoreId(), req.GetStoreType())
		if err != nil {
			logger.CtxLogInfof(lcosCtx, "cannot find store by new_store_id=%s and store_type=%d|request unique id=%s", req.GetNewStoreId(), req.GetStoreType(), req.GetUniqueId())
			continue
		}
		res[req.GetUniqueId()] = convertStoreStructToPb(store)
	}
	return &pb.BatchGetStoreByNewStoreIdAndTypeResponse{
		RespHeader:   http.GrpcSuccessRespHeader(),
		ResponseDict: res,
	}, nil
}

func convertStoreStructToPb(store *tw_store2.LogisticThirdPartyConvenienceStoreTab) *pb.ConvenienceStore {
	status := uint32(store.EnableStatus)
	districtID := uint64(store.DistrictID)
	isAllConsistency := uint32(store.IsAllConsistency)
	isVirtual := uint32(store.IsVirtual)
	return &pb.ConvenienceStore{
		Id:               &store.ID,
		StoreId:          &store.StoreID,
		NewStoreId:       &store.NewStoreID,
		Name:             &store.StoreName,
		Address:          &store.Address,
		StoreType:        &store.StoreType,
		CloseDate:        &store.CloseDate,
		EndDate:          &store.EndDate,
		Status:           &status,
		DistrictId:       &districtID,
		IsAllConsistency: &isAllConsistency,
		UpdateDate:       &store.UpdateDate,
		Phone:            &store.Phone,
		PostalCode:       &store.PostalCode,
		IsVirtual:        &isVirtual,
		City:             &store.City,
		Area:             &store.Area,
		District:         &store.District,
		DcroNo:           &store.DcroNo,
		RsNo:             &store.RsNo,
		OkArea:           &store.OkArea,
		ShipType:         &store.ShipType,
		PathNo:           &store.PathNo,
		AisleNo:          &store.AisleNo,
		GridNo:           &store.GridNo,
		MidType:          &store.MidType,
		Ctime:            &store.Ctime,
		Mtime:            &store.Mtime,
	}
}

func NewTwStoreController(twStoreService tw_store.TWStoreService) *twStoreController {
	return &twStoreController{
		twStoreService: twStoreService,
	}
}

var _ pb.TWStoreServiceServer = (*twStoreController)(nil)
