package geo_distance

import (
	"context"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/geo_distance_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/gogo/protobuf/proto"
)

var _ pb.GeoDistanceCalculateServiceServer = (*geoDistanceCalculateController)(nil)

func NewGeoDistanceCalculateController(service geo_distance_service.LogisticProductGeoDistanceService) *geoDistanceCalculateController {
	return &geoDistanceCalculateController{
		service: service,
	}
}

type geoDistanceCalculateController struct {
	pb.UnimplementedGeoDistanceCalculateServiceServer

	service geo_distance_service.LogisticProductGeoDistanceService
}

func (g *geoDistanceCalculateController) ListAllGeoClientConfig(c context.Context, request *pb.ListAllGeoClientConfigRequest) (*pb.ListAllGeoClientConfigResponse, error) {
	ctx := utils.NewCommonCtx(c)

	confList, err := g.service.ListAllGeoClient(ctx)
	if err != nil {
		return &pb.ListAllGeoClientConfigResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	clientList := make([]*pb.GeoClientConfig, 0, len(confList))
	for _, conf := range confList {
		clientList = append(clientList, &pb.GeoClientConfig{
			Project:      proto.String(conf.Project),
			Scenario:     proto.String(conf.Scenario),
			GeoUser:      proto.String(conf.GeoUser),
			GeoProject:   proto.String(conf.GeoProject),
			GeoClientKey: proto.String(conf.GeoClientKey),
		})
	}
	return &pb.ListAllGeoClientConfigResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		ClientList: clientList,
	}, nil
}

func (g *geoDistanceCalculateController) ListAllGeoDistanceConfig(c context.Context, request *pb.ListAllGeoDistanceConfigRequest) (*pb.ListAllGeoDistanceConfigResponse, error) {
	ctx := utils.NewCommonCtx(c)

	confList, err := g.service.ListAllProductGeoDistanceConf(ctx)
	if err != nil {
		return &pb.ListAllGeoDistanceConfigResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	productList := make([]*pb.ProductGeoDistanceConfig, 0, len(confList))
	for _, conf := range confList {
		productList = append(productList, &pb.ProductGeoDistanceConfig{
			ProductId:        proto.String(conf.ProductId),
			GeoDistanceMode:  proto.String(conf.GeoDistanceMode),
			GeoDistanceAvoid: conf.GeoDistanceAvoid,
		})
	}
	return &pb.ListAllGeoDistanceConfigResponse{
		RespHeader:  http.GrpcSuccessRespHeader(),
		ProductList: productList,
	}, nil
}
