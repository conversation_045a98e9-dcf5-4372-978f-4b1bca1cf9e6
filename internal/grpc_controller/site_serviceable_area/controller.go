package site_serviceable_area

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type siteServiceableAreaController struct {
	siteServiceAreaService site_serviceable_area.SiteServiceableAreaInterface
}

// @core
func (s *siteServiceableAreaController) BatchGetActualSiteIDList(ctx context.Context, request *pb.BatchGetActualSiteIDListRequest) (*pb.BatchGetActualSiteIDListResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)

	result := map[string]*pb.ActualSiteList{}
	for _, item := range request.GetSiteList() {
		// 封装单个请求
		singleRequest := &site_serviceable_area.GetSiteServiceableAreaBasicRequest{
			SiteID:             item.GetSiteId(),
			StateLocationID:    int(item.GetStateLocationId()),
			CityLocationID:     int(item.GetCityLocationId()),
			DistrictLocationID: int(item.GetDistrictLocationId()),
			StreetLocationID:   int(item.GetStreetLocationId()),
			Zipcode:            item.GetZipcode(),
			SkipPostcode:       int(item.GetSkipPostcode()),
			SkipCheck:          int(item.GetSkipCheck()),
		}
		actualPointList, lcosErr := s.siteServiceAreaService.GetSiteServiceableAreaList(lcosCtx, singleRequest)
		if lcosErr != nil {
			errMsg := fmt.Sprintf("get ssa error, error=%s|site_id=%s, state_location_id=%d, city_location_id=%d, district_location_id=%d, street_location_id=%d, zipcode=%s", lcosErr.Msg, item.GetSiteId(), int(item.GetStateLocationId()), int(item.GetCityLocationId()), int(item.GetDistrictLocationId()), int(item.GetStreetLocationId()), item.GetZipcode())
			logger.CtxLogErrorf(ctx, errMsg)
			result[item.GetUniqueId()] = &pb.ActualSiteList{
				ActualSiteIdList: []string{},
			}
		}
		result[item.GetUniqueId()] = &pb.ActualSiteList{
			ActualSiteIdList: actualPointList,
		}
	}
	return &pb.BatchGetActualSiteIDListResponse{
		RespHeader:    http.GrpcSuccessRespHeader(),
		ActualSiteMap: result,
	}, nil
}

var _ pb.LcosSiteServiceableAreaServiceServer = (*siteServiceableAreaController)(nil)

func NewSiteServiceableAreaController(siteServiceAreaService site_serviceable_area.SiteServiceableAreaInterface) *siteServiceableAreaController {
	return &siteServiceableAreaController{siteServiceAreaService: siteServiceAreaService}
}
