package pis_branch

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	jsoniter "github.com/json-iterator/go"
)

type pisBranchController struct {
	branchServer pb.BranchServiceServer
}

func (p *pisBranchController) GetBranchListByBranchGroupID(ctx context.Context, request *pb.DistributionRequest) (*pb.DistributionResponse, error) {
	businessData := &GetBranchListByBranchGroupRequest{}
	err := jsoniter.Unmarshal([]byte(request.GetBusinessData()), businessData)
	if err != nil {
		logger.CtxLogErrorf(ctx, "parsing business data error|error=%v", err.Error())
		return genDistributionResponse(lcos_error.ServerErrorCode, err.Error(), request.GetReqHeader().GetRequestId(), ""), nil
	}

	if businessData.BranchGroupID <= 0 {
		errMsg := fmt.Sprintf("branch group id is not valid|branch_group_id=%v", businessData.BranchGroupID)
		logger.CtxLogErrorf(ctx, "", errMsg)
		return genDistributionResponse(lcos_error.SchemaParamsErrorCode, errMsg, request.GetReqHeader().GetRequestId(), ""), nil
	}

	// 生成lcos的请求
	req := &pb.GetBranchListByBranchGroupIDRequest{
		ReqHeader:     genLcosReqHeader(ctx, request.GetReqHeader().GetRequestIp(), "PIS", ""),
		BranchGroupId: utils.NewUint32(businessData.BranchGroupID),
	}
	branchInfoListResp, _ := p.branchServer.GetBranchListByBranchGroupID(ctx, req)
	if branchInfoListResp.RespHeader.GetRetcode() != lcos_error.SuccessCode {
		logger.CtxLogErrorf(ctx, branchInfoListResp.RespHeader.GetMessage())
		return genDistributionResponse(branchInfoListResp.RespHeader.GetRetcode(), branchInfoListResp.RespHeader.GetMessage(), request.GetReqHeader().GetRequestId(), ""), nil
	}
	branchInfoListString, _ := jsoniter.MarshalToString(branchInfoListResp.BranchInfoList)
	return genDistributionResponse(lcos_error.SuccessCode, "", request.GetReqHeader().GetRequestId(), branchInfoListString), nil
}

func NewPISBranchController(branchServer pb.BranchServiceServer) *pisBranchController {
	return &pisBranchController{
		branchServer: branchServer,
	}
}

var _ pb.PISBranchServiceServer = (*pisBranchController)(nil)
