package pis_branch

import (
	"context"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

func genLcosReqHeader(ctx context.Context, requestID, account, token string) *lcos_protobuf.ReqHeader {
	currentTime := utils.GetTimestamp(ctx)
	ip := utils.GetLocalIp()
	reqId := requestID
	return &lcos_protobuf.ReqHeader{
		RequestId: &reqId,
		Account:   &account,
		Token:     &token,
		Timestamp: &currentTime,
		CallerIp:  ip,
	}
}

func genPISReqHeader(retcode int32, message, requestID string) *lcos_protobuf.PisRespHeader {
	return &lcos_protobuf.PisRespHeader{
		Retcode:   &retcode,
		Message:   &message,
		RequestId: &requestID,
	}
}

func genDistributionResponse(retcode int32, message, requestID string, returnData string) *lcos_protobuf.DistributionResponse {
	return &lcos_protobuf.DistributionResponse{
		RespHeader: genPISReqHeader(retcode, message, requestID),
		Data:       &returnData,
	}
}
