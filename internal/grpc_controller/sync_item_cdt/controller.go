package sync_item_cdt

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	auto_update_rule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_update_rule"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"strings"
)

func NewSyncItemCdtService(autoUpdateDataService auto_update_rule.AutoUpdateDataServiceInterface, manualUpdateDataService manual_update_rule.ManualUpdateServiceInterface,
	autoVolumeDataService automated_volume.AutomatedVolumeInterface) *syncItemCdtService {
	return &syncItemCdtService{
		autoUpdateDataService:   autoUpdateDataService,
		manualUpdateDataService: manualUpdateDataService,
		autoVolumeDataService:   autoVolumeDataService,
	}
}

type syncItemCdtService struct {
	autoUpdateDataService   auto_update_rule.AutoUpdateDataServiceInterface
	manualUpdateDataService manual_update_rule.ManualUpdateServiceInterface
	autoVolumeDataService   automated_volume.AutomatedVolumeInterface
}

func (s *syncItemCdtService) SyncAutoUpdateData(ctx context.Context, request *pb.SyncAutoUpdateDataRequest) (*pb.SyncAutoUpdateDataResponse, error) {
	reqId := request.GetReqHeader().GetRequestId()

	go func(logId, region string, ruleId uint64) {
		logCtx := logger.NewLogContext(context.Background(), logId)
		if err := s.autoUpdateDataService.SyncAutoUpdateDataToItemCodis(utils.NewCommonCtx(logCtx), region, ruleId); err != nil {
			logger.CtxLogErrorf(ctx, "sync cdt auto update data to item codis error|cause=%s", err.Msg)
		}
	}(reqId, request.GetRegion(), request.GetRuleId())

	return &pb.SyncAutoUpdateDataResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

func (s *syncItemCdtService) SyncManualUpdateData(ctx context.Context, request *pb.SyncManualUpdateDataRequest) (*pb.SyncManualUpdateDataResponse, error) {
	reqId := request.GetReqHeader().GetRequestId()

	go func(logId, region, productId string) {
		logCtx := logger.NewLogContext(context.Background(), logId)
		if err := s.manualUpdateDataService.SyncCdtManualUpdateDataToItemCodis(utils.NewCommonCtx(logCtx), region, productId); err != nil {
			logger.CtxLogErrorf(ctx, "sync cdt manual update data to item codis error|cause=%s", err.Msg)
		}
	}(reqId, request.GetRegion(), request.GetProductId())

	return &pb.SyncManualUpdateDataResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

func (s *syncItemCdtService) SyncAutoVolumeData(ctx context.Context, request *pb.SyncAutoVolumeDataRequest) (*pb.SyncAutoVolumeDataResponse, error) {
	reqId := request.GetReqHeader().GetRequestId()
	if cf.GetEnv(ctx) == cf.LIVE && cf.GetModuleName() != strings.ToUpper("grpclivetest") && cf.GetModuleName() != strings.ToUpper("livexx") {
		return &pb.SyncAutoVolumeDataResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.ParamsError, fmt.Sprintf("live grpc can't use this,env is %s, moduleis %s", cf.GetEnv(ctx), cf.GetModuleName())),
		}, nil
	}

	go func(logId, region string, ruleId uint64) {
		logCtx := logger.NewLogContext(context.Background(), logId)
		if err := s.autoVolumeDataService.SyncAutoVolumeDataToItemCodis(utils.NewCommonCtx(logCtx), region, ruleId); err != nil {
			logger.CtxLogErrorf(ctx, "sync volume auto update data to item codis error|cause=%s", err.Msg)
		}
	}(reqId, request.GetRegion(), request.GetRuleId())

	return &pb.SyncAutoVolumeDataResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}
