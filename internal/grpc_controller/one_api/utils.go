package package_limit

import (
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

func lineRuleItem2LcosSkuInfo(item *lcos_protobuf.ServiceableItem) *lcos_protobuf.SkuInfo {
	sku := &lcos_protobuf.SkuInfo{
		ItemPrice:    utils.NewFloat64(float64(item.GetItemPrice())),
		ItemPriceUsd: utils.NewFloat64(float64(item.GetItemPriceUsd())),
		Quantity:     item.Quantity,
		Length:       utils.NewFloat64(float64(item.GetLength())),
		Width:        utils.NewFloat64(float64(item.GetWidth())),
		Height:       utils.NewFloat64(float64(item.GetHeight())),
		Weight:       utils.NewFloat64(float64(item.GetWeight())),
	}
	sku.ItemId = item.ItemId
	if item.CategoryId != nil {
		sku.CategoryId = utils.NewUint64(uint64(item.GetCategoryId()))
	}
	//if item.GlobalCategoryId != nil {
	//	sku.GlobalCategoryId = item.GlobalCategoryId
	//}
	return sku
}

func batchLineRuleItem2LlsSkuInfo(items []*lcos_protobuf.ServiceableItem) []*lcos_protobuf.SkuInfo {
	var skus []*lcos_protobuf.SkuInfo
	for _, item := range items {
		skus = append(skus, lineRuleItem2LcosSkuInfo(item))
	}
	return skus
}
