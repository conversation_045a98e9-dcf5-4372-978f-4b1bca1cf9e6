package package_limit

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pickup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/branch_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"

	"github.com/gogo/protobuf/proto"
)

type grpcOneApiController struct {
	pb.UnimplementedLcosOneApiServiceServer

	pickupWindowServer     pb.LcosPickupWindowServiceServer
	serviceableServer      pb.LcosServiceableServiceServer
	weightLimitServer      pb.LcosWeightLimitServiceServer
	branchServer           pb.BranchServiceServer
	twStoreServer          pb.TWStoreServiceServer
	sceneServiceableServer pb.LcosSceneServiceableServer

	parcelLibraryService parcel_library.LogisticParcelLibraryService
}

func NewGrpcOneApiController(pickupWindowServer pb.LcosPickupWindowServiceServer, serviceableServer pb.LcosServiceableServiceServer, weightLimitServer pb.LcosWeightLimitServiceServer, branchServer pb.BranchServiceServer, twStoreServer pb.TWStoreServiceServer, sceneServiceableServer pb.LcosSceneServiceableServer, parcelLibraryService parcel_library.LogisticParcelLibraryService) *grpcOneApiController {
	return &grpcOneApiController{
		pickupWindowServer:     pickupWindowServer,
		serviceableServer:      serviceableServer,
		weightLimitServer:      weightLimitServer,
		branchServer:           branchServer,
		twStoreServer:          twStoreServer,
		sceneServiceableServer: sceneServiceableServer,
		parcelLibraryService:   parcelLibraryService,
	}
}

func (g *grpcOneApiController) GetAndCheckForInstallation(ctx context.Context, request *pb.OneApiForInstallationRequest) (*pb.OneApiForInstallationResponse, error) {
	results := &pb.OneApiForInstallationResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}
	checkResults := make([]*pb.InstallationCheckInfo, 0, len(request.InstallationInfo))
	for _, info := range request.GetInstallationInfo() {
		result := &pb.InstallationCheckInfo{
			UniqueId: info.UniqueId,
		}
		result.ProductCheckResults = make([]*pb.InstallationProductCheckResult, 0, len(info.Products))
		idxProductIdMap := make(map[int]string, len(info.Products))
		idxWeightLineIdsMap := make(map[int][]string, len(info.Products))
		// product check
		productCheckRequest := &pb.CheckProductRuleRequest{
			ReqHeader:         request.ReqHeader,
			SkuInfo:           []*pb.SkuInfo{info.SkuInfos},
			BuyerPurchaseTime: info.BuyerPurchaseTime,
		}

		// line check
		lineCheckRequest := &pb.CheckLineRuleRequest{
			ReqHeader:         request.ReqHeader,
			SkuInfo:           []*pb.SkuInfo{info.SkuInfos},
			BuyerPurchaseTime: info.BuyerPurchaseTime,
		}

		for i, product := range info.Products {
			productCheckRequest.ProductId = append(productCheckRequest.ProductId, product.GetProductId())
			lineCheckRequest.LineId = append(lineCheckRequest.LineId, product.GetLineIds()...)
			idxProductIdMap[i] = product.GetProductId()
			idxWeightLineIdsMap[i] = product.GetLineIds()
		}
		productCheckResult, err := g.weightLimitServer.CheckProductRule(ctx, productCheckRequest)
		if err != nil {
			return nil, err
		}
		lineCheckResult, err := g.weightLimitServer.CheckLineRule(ctx, lineCheckRequest)
		if err != nil {
			return nil, err
		}
		lineIdResultMap := make(map[string]*pb.SingleCheckLineRuleResult, len(info.Products))
		productIdResultMap := make(map[string]*pb.SingleCheckProductRuleResult, len(info.Products))
		for _, productRuleResult := range productCheckResult.RuleLimitInfo {
			productIdResultMap[productRuleResult.GetProductId()] = productRuleResult
		}
		for _, lineRuleResult := range lineCheckResult.RuleLimitInfo {
			lineIdResultMap[lineRuleResult.GetLineId()] = lineRuleResult
		}

		for i := 0; i < len(info.Products); i++ {
			thisLineResults := make([]*pb.SingleCheckLineRuleResult, 0, len(info.Products[i].LineIds))
			for j := 0; j < len(idxWeightLineIdsMap[i]); j++ {
				thisLineResults = append(thisLineResults, lineIdResultMap[idxWeightLineIdsMap[i][j]])
			}
			result.ProductCheckResults = append(result.ProductCheckResults, &pb.InstallationProductCheckResult{
				ProductResult: productIdResultMap[idxProductIdMap[i]],
				LineResults:   thisLineResults,
			})
		}

		// check sa
		if info.GetSkipSaCheck() == 0 {
			idxSALineIdsMap := make(map[int][]string, len(info.Products))
			saCheckRequest := &pb.BatchGetLineServiceableInfoRequest2{
				ReqHeader:          request.ReqHeader,
				ServiceableReqList: []*pb.SingleGetServiceableRequest2{},
			}
			for i, product := range info.Products {
				for _, serviceableInfo := range product.ServiceableInfos {
					idxSALineIdsMap[i] = append(idxSALineIdsMap[i], serviceableInfo.GetLineId())
					saCheckRequest.ServiceableReqList = append(saCheckRequest.ServiceableReqList, &pb.SingleGetServiceableRequest2{
						BaseInfo:    serviceableInfo,
						PickupInfo:  info.PickupInfo,
						DeliverInfo: info.DeliverInfo,
					})
				}
			}
			saCheckResult, err := g.serviceableServer.BatchGetLineServiceableArea2(ctx, saCheckRequest)
			if err != nil {
				return nil, err
			}
			lineIdSAMap := make(map[string]*pb.SingleGetServiceableResponse2, len(saCheckResult.ServiceableRespList))
			for _, saResult := range saCheckResult.ServiceableRespList {
				lineIdSAMap[saResult.GetLineId()] = saResult
			}
			for i := 0; i < len(info.Products); i++ {
				thisSAResults := make([]*pb.SingleGetServiceableResponse2, 0, len(info.Products[i].ServiceableInfos))
				for _, lineId := range idxSALineIdsMap[i] {
					tempResult := lineIdSAMap[lineId]
					_ = metrics.CounterIncr(constant.MetricInstallationLineStatus, map[string]string{
						"rsp_status":      strconv.Itoa(int(tempResult.GetItemCode())),
						"can_deliver":     strconv.Itoa(int(tempResult.GetServiceableInfo().GetCanDeliver())),
						"can_cod_deliver": strconv.Itoa(int(tempResult.GetServiceableInfo().GetCanCodDeliver())),
						"can_trade_in":    strconv.Itoa(int(tempResult.GetServiceableInfo().GetCanTradeIn())),
						"err_code":        tempResult.GetMessage(),
						"line":            tempResult.GetLineId(),
						"product":         info.Products[i].GetProductId(),
					})
					thisSAResults = append(thisSAResults, lineIdSAMap[lineId])
				}
				result.ProductCheckResults[i].SaResults = thisSAResults
			}
		}
		checkResults = append(checkResults, result)
	}
	results.CheckResults = checkResults
	return results, nil
}

const maxOneApiRequestBatch = 50 // one api的batch请求

var _ pb.LcosOneApiServiceServer = (*grpcOneApiController)(nil)

func transferTimeslotToPickupTime(pickupTime uint32, slotStartHour, slotStartMinute, slotStartSecond, slotEndHour, slotEndMinute, slotEndSecond uint32, region string) (uint32, uint32) {
	pickupDatetime := pickup.TransferTimeStampToTime(pickupTime, region)
	startPickupTime := time.Date(pickupDatetime.Year(), pickupDatetime.Month(), pickupDatetime.Day(), int(slotStartHour), int(slotStartMinute), int(slotStartSecond), 0, pickupDatetime.Location())
	endPickupTime := time.Date(pickupDatetime.Year(), pickupDatetime.Month(), pickupDatetime.Day(), int(slotEndHour), int(slotEndMinute), int(slotEndSecond), 0, pickupDatetime.Location())
	return uint32(startPickupTime.Unix()), uint32(endPickupTime.Unix())
}

// @core
func (g *grpcOneApiController) GetAndCheckCanCreateWaybillLine(ctx context.Context, request *pb.OneApiRequest) (*pb.OneApiResponse, error) {
	requestID := request.ReqHeader.GetRequestId()

	successResult := http.GrpcSuccessRespHeader()
	results := &pb.OneApiResponse{
		RespHeader:   successResult,
		CheckResults: map[string]*pb.OneApiCheckInfo{},
		GetResults: &pb.GetResults{
			TimeslotResults:   map[string]*pb.OneApiGetPickupTimeslotsResponse{},
			DestBranchResults: map[string]*pb.OneApiBranchInfo{},
			TwStoreResults:    map[string]*pb.BatchGetStoreByStoreIDResponse{},
		},
	}

	if len(request.OneApiInfo.CheckLineWaybillAttrList) > maxOneApiRequestBatch || len(request.OneApiInfo.GetLineWaybillAttrList) > maxOneApiRequestBatch {
		errCode := lcos_error.SchemaParamsErrorCode
		errMsg := fmt.Sprintf("CheckLineWaybillAttrList or GetLineWaybillAttrList cannot be larger than %v", maxOneApiRequestBatch)
		results.RespHeader.Retcode = &errCode
		results.RespHeader.Message = &errMsg
		return results, nil
	}

	// 先进行check的校验，LLS调用，seller发货的场景，跳过电子围栏校验
	skipElectricFence := true
	for _, checkItem := range request.OneApiInfo.CheckLineWaybillAttrList {
		checkItem.ServiceableInfo.SkipElectricFence = &skipElectricFence

		isCheckLineRule := checkItem.GetIsCheckLineRule()
		isCheckPickupTime := checkItem.GetIsCheckPickupTime()
		isCheckServiceable := checkItem.GetIsCheckServiceable()

		retcode := lcos_error.SchemaParamsErrorCode
		// 基本的参数校验
		if uint8(isCheckPickupTime) == constant.IsCheck && (checkItem.PickupTimeInfo == nil || len(checkItem.PickupTimeInfo.LineIdList) != 1 || checkItem.PickupTimeInfo.LineIdList[0] != checkItem.GetLineId()) {
			errMsg := "pickup_time_info cannot be empty when isCheckPickupTime is enabled"
			results.CheckResults[checkItem.GetReqNo()] = &pb.OneApiCheckInfo{
				Retcode: &retcode,
				Message: &errMsg,
			}
			continue
		}
		if uint8(isCheckServiceable) == constant.IsCheck && (checkItem.ServiceableInfo == nil || checkItem.PickupInfo == nil || checkItem.DeliverInfo == nil || checkItem.GetLineId() != checkItem.ServiceableInfo.GetLineId()) {
			errMsg := "serviceableInfo|pickupInfo|DeliverInfo cannot be empty when isCheckServiceable is enabled"
			results.CheckResults[checkItem.GetReqNo()] = &pb.OneApiCheckInfo{
				Retcode: &retcode,
				Message: &errMsg,
			}
			continue
		}

		// 检查服务范围
		if uint8(isCheckServiceable) == constant.IsCheck {
			response, _ := g.serviceableServer.CheckLineServiceableArea2(ctx, &pb.CheckLineServiceableAreaRequest2{
				ReqHeader:   request.GetReqHeader(),
				BaseInfo:    checkItem.ServiceableInfo,
				PickupInfo:  checkItem.PickupInfo,
				DeliverInfo: checkItem.DeliverInfo,
			})
			if response.RespHeader.GetRetcode() != lcos_error.SuccessCode {
				results.CheckResults[checkItem.GetReqNo()] = &pb.OneApiCheckInfo{
					Retcode: response.RespHeader.Retcode,
					Message: response.RespHeader.Message,
				}
				continue
			}
		}

		// 检查lineRule
		if uint8(isCheckLineRule) == constant.IsCheck {
			response, _ := g.weightLimitServer.CheckLineRule(ctx, &pb.CheckLineRuleRequest{
				ReqHeader:         request.GetReqHeader(),
				SkuInfo:           request.GetOneApiInfo().SkuInfo,
				LineId:            []string{checkItem.GetLineId()},
				OrderInfo:         request.OneApiInfo.OrderInfo,
				SubPackageInfo:    request.OneApiInfo.GetSubPackageInfo(),
				BuyerPurchaseTime: request.GetOneApiInfo().BuyerPurchaseTime,
			})

			if response.RespHeader.GetRetcode() != lcos_error.SuccessCode {
				// 拼装返回信息
				errMsgList := make([]string, 0)
				if len(response.RuleLimitInfo) > 0 && len(response.RuleLimitInfo[0].LimitDetail) > 0 {
					for _, singleCheckResult := range response.RuleLimitInfo[0].LimitDetail {
						if uint8(*singleCheckResult.SingleCheckResult) == constant.FAILED {
							errMsgList = append(errMsgList, fmt.Sprintf("rule_type:%v; reason:%v", *singleCheckResult.RuleType, *singleCheckResult.Reason))
						}
					}
				}
				errMsg := strings.Join(errMsgList, "|")
				results.CheckResults[checkItem.GetReqNo()] = &pb.OneApiCheckInfo{
					Retcode: response.RespHeader.Retcode,
					Message: &errMsg,
				}
				continue
			}
		}

		// 检查pickup time
		if uint8(isCheckPickupTime) == constant.IsCheck {

			var pickupTime = checkItem.PickupTimeInfo.GetPickupTime()

			// pickup time的校验，需要检查是否传入了pickup time range id，如果传入了，则表示为timeslot类型，需要先获取timeslot
			if checkItem.PickupTimeInfo.PickTimeRangeId != nil {
				// 调用获取到具体的pickup time
				response, _ := g.pickupWindowServer.GetPickupTimeslot(ctx, &pb.GetPickupTimeslotRequest{
					ReqHeader:         request.ReqHeader,
					LineIdList:        []string{checkItem.GetLineId()},
					PickupTimeRangeId: checkItem.PickupTimeInfo.PickTimeRangeId,
					StartTime:         checkItem.PickupTimeInfo.StartTime,
					MerchantType:      checkItem.PickupTimeInfo.MerchantType,
					AccountGroup:      checkItem.PickupTimeInfo.AccountGroup,
				})

				if response.RespHeader.GetRetcode() != lcos_error.SuccessCode && response.RespHeader.GetRetcode() != lcos_error.NotFoundAnyTimeslotErrorCode {
					results.CheckResults[checkItem.GetReqNo()] = &pb.OneApiCheckInfo{
						Retcode: response.RespHeader.Retcode,
						Message: response.RespHeader.Message,
					}
					continue
				} else {
					pickupTime, _ = transferTimeslotToPickupTime(pickupTime, response.Timeslot.GetStartHour(), response.Timeslot.GetStartMinute(), response.Timeslot.GetStartSecond(), response.Timeslot.GetEndHour(), response.Timeslot.GetEndMinute(), response.Timeslot.GetEndSecond(), checkItem.PickupTimeInfo.GetCountry())
				}
			}

			response, _ := g.pickupWindowServer.CheckPickupTime(ctx, &pb.CheckPickupTimeRequest{
				ReqHeader: request.ReqHeader,
				PickupInfo: &pb.CheckPickupTimeInfo{
					LineIdList:        []string{checkItem.GetLineId()},
					Country:           checkItem.PickupTimeInfo.Country,
					StateLocationId:   checkItem.PickupTimeInfo.StateLocationId,
					PickupTime:        &pickupTime,
					StartTime:         checkItem.PickupTimeInfo.StartTime,
					MerchantType:      checkItem.PickupTimeInfo.MerchantType,
					AccountGroup:      checkItem.PickupTimeInfo.AccountGroup,
					PickTimeRangeId:   checkItem.PickupTimeInfo.PickTimeRangeId,
					Zipcode:           checkItem.PickupTimeInfo.Zipcode,
					ClientGroupIdList: checkItem.PickupTimeInfo.ClientGroupIdList,
				},
			})
			if response.RespHeader.GetRetcode() != lcos_error.SuccessCode {
				results.CheckResults[checkItem.GetReqNo()] = &pb.OneApiCheckInfo{
					Retcode: response.RespHeader.Retcode,
					Message: response.RespHeader.Message,
				}
				continue
			}
		}

		// 所有check通过，将成功的信息添加到
		successCode := lcos_error.SuccessCode
		successMsg := ""
		results.CheckResults[checkItem.GetReqNo()] = &pb.OneApiCheckInfo{
			Retcode: &successCode,
			Message: &successMsg,
		}
	}

	// 在进行get的获取
	for _, getItem := range request.OneApiInfo.GetLineWaybillAttrList {
		if getItem.GetTimeslotInfo != nil {
			response, _ := g.pickupWindowServer.GetPickupTimeslot(ctx, &pb.GetPickupTimeslotRequest{
				ReqHeader:         request.ReqHeader,
				LineIdList:        []string{getItem.GetLineId()},
				PickupTimeRangeId: getItem.GetTimeslotInfo.PickupTimeRangeId,
				StartTime:         getItem.GetTimeslotInfo.StartTime,
				MerchantType:      getItem.GetTimeslotInfo.MerchantType,
				AccountGroup:      getItem.GetTimeslotInfo.AccountGroup,
			})
			tmpTimeslot := &pb.OneApiGetPickupTimeslotsResponse{
				Retcode: response.RespHeader.Retcode,
				Message: response.RespHeader.Message,
			}
			if response.RespHeader.GetRetcode() == lcos_error.SuccessCode {
				startPickupTime, endPickupTime := transferTimeslotToPickupTime(getItem.GetTimeslotInfo.GetPickupTime(), response.GetTimeslot().GetStartHour(), response.GetTimeslot().GetStartMinute(), response.GetTimeslot().GetStartSecond(), response.GetTimeslot().GetEndHour(), response.GetTimeslot().GetEndMinute(), response.GetTimeslot().GetEndSecond(), getItem.GetTimeslotInfo.GetCountry())
				tmpTimeslot.PickupTimeInfo = &pb.OneApiPickupTimeInfo{
					PickupStartTime: &startPickupTime,
					PickupEndTime:   &endPickupTime,
				}
			}
			results.GetResults.TimeslotResults[getItem.GetReqNo()] = tmpTimeslot
		}

		// dest branch info的获取
		if getItem.DestBranchGetReq != nil {
			var resp, localResp, remoteResp *pb.GetBranchInfoResponse

			switchConfig := config.GetMutableConf(ctx).BranchSwitch
			if !switchConfig.Switch || (startup.IsLcosProject() && switchConfig.NeedDoubleCall()) {
				// 不切换 或 double call，需要本地处理请求
				localResp, _ = g.branchServer.GetBranchInfo(ctx, &pb.GetBranchInfoRequest{
					ReqHeader:     request.ReqHeader,
					BranchId:      getItem.GetDestBranchGetReq().BranchId,
					BranchGroupId: []uint32{getItem.GetDestBranchGetReq().GetBranchGroupId()},
				})
				resp = localResp
			}
			if switchConfig.Switch {
				// 切换到branch，请求转发到branch service
				branchService := branch_service.NewBranchService()
				remoteResp, _ = branchService.GetBranchInfo(ctx, &pb.GetBranchInfoRequest{
					ReqHeader:     request.ReqHeader,
					BranchId:      getItem.GetDestBranchGetReq().BranchId,
					BranchGroupId: []uint32{getItem.GetDestBranchGetReq().GetBranchGroupId()},
				})
				remoteResp.GetRespHeader().RequestId = nil
				resp = remoteResp
			}
			if startup.IsLcosProject() && switchConfig.NeedDoubleCall() {
				// double call，对比本地处理结果和branch service返回结果
				localRespJson := utils.MarshToStringWithoutError(localResp)
				remoteRespJson := utils.MarshToStringWithoutError(remoteResp)

				if localRespJson == remoteRespJson {
					resp = remoteResp // 本地处理结果与branch service处理一致，返回branch service的结果
					_ = monitor.AwesomeReportEvent(ctx, constant.CatBranchSwitch, "GetAndCheckCanCreateWaybillLine|GetBranchInfo", constant.StatusSuccess, "")
				} else {
					resp = localResp // 本地处理结果与branch service处理不一致，返回本地处理的结果并上报差异
					diff := fmt.Sprintf("local resp: %s\nremote resp: %s", localRespJson, remoteRespJson)
					_ = monitor.AwesomeReportEvent(ctx, constant.CatBranchSwitch, "GetAndCheckCanCreateWaybillLine|GetBranchInfo", constant.StatusNotEqual, diff)
				}
			}

			results.GetGetResults().DestBranchResults[getItem.GetReqNo()] = &pb.OneApiBranchInfo{
				Retcode:    resp.RespHeader.Retcode,
				Message:    resp.RespHeader.Message,
				BranchInfo: resp.BranchInfo,
			}
		}

		// tw store info的获取
		if getItem.TwStoreGetReq != nil {
			var resp, localResp, remoteResp *pb.BatchGetStoreByStoreIDResponse

			switchConfig := config.GetMutableConf(ctx).BranchSwitch
			if !switchConfig.Switch || (startup.IsLcosProject() && switchConfig.NeedDoubleCall()) {
				// 不切换 或 double call，需要本地处理请求
				localResp, _ = g.twStoreServer.BatchGetStoreByAddressID(ctx, &pb.BatchGetStoreByAddressIDRequest{
					ReqHeader:   request.ReqHeader,
					RequestList: getItem.GetTwStoreGetReq().GetRequestList(),
				})
				resp = localResp
			}
			if switchConfig.Switch {
				// 切换到branch，请求转发到branch service
				branchService := branch_service.NewBranchService()
				remoteResp, _ = branchService.BatchGetStoreByAddressID(ctx, &pb.BatchGetStoreByAddressIDRequest{
					ReqHeader:   request.ReqHeader,
					RequestList: getItem.GetTwStoreGetReq().GetRequestList(),
				})
				remoteResp.GetRespHeader().RequestId = nil
				resp = remoteResp
			}
			if startup.IsLcosProject() && switchConfig.NeedDoubleCall() {
				// double call，对比本地处理结果和branch service返回结果
				localRespJson := utils.MarshToStringWithoutError(localResp)
				remoteRespJson := utils.MarshToStringWithoutError(remoteResp)

				if localRespJson == remoteRespJson {
					resp = remoteResp // 本地处理结果与branch service处理一致，返回branch service的结果
					_ = monitor.AwesomeReportEvent(ctx, constant.CatBranchSwitch, "GetAndCheckCanCreateWaybillLine|BatchGetStoreByAddressID", constant.StatusSuccess, "")
				} else {
					resp = localResp // 本地处理结果与branch service处理不一致，返回本地处理的结果并上报差异
					diff := fmt.Sprintf("local resp: %s\nremote resp: %s", localRespJson, remoteRespJson)
					_ = monitor.AwesomeReportEvent(ctx, constant.CatBranchSwitch, "GetAndCheckCanCreateWaybillLine|BatchGetStoreByAddressID", constant.StatusNotEqual, diff)
				}
			}

			resp.RespHeader.RequestId = &requestID
			results.GetResults.TwStoreResults[getItem.GetReqNo()] = resp
		}
	}
	return results, nil
}

// merge line rule result to serviceable area
func mergeLineRuleToServiceable(serviceableMap []*pb.ProductServiceableRsp, lineRuleMap map[string]*pb.CheckLaneRuleResponse) {
	for _, singleService := range serviceableMap {
		for _, singleLaneServiceable := range singleService.GetLaneList() {
			// check lane rule is ok
			if _, ok1 := lineRuleMap[singleService.GetUniqueId()]; ok1 {
				if rule, ok2 := lineRuleMap[singleService.GetUniqueId()].GetRuleLimitInfoMap()[singleLaneServiceable.GetLaneCode()]; ok2 {
					if rule.GetCode() != lcos_error.SuccessCode {
						singleLaneServiceable.Serviceable = &pb.AreaServiceable{
							Code:    utils.NewInt32(rule.GetCode()),
							Message: utils.NewString(rule.GetMessage()),
						}
					}
				}
			}
		}
	}
}

// @core
func (g *grpcOneApiController) GetAndCheckForCheckoutScene(ctx context.Context, scene *pb.OneApiRequestForCheckoutScene) (*pb.OneApiResponseForCheckoutScene, error) {
	results := &pb.OneApiResponseForCheckoutScene{
		RespHeader:                    http.GrpcSuccessRespHeader(),
		CheckProductRuleResultMap:     map[string]*pb.CheckProductRuleResponse{},
		CheckServiceableAreaResultMap: map[string]*pb.ProductServiceableRsp{},
	}

	// check product rule first
	if len(scene.GetOneApiCheckInfo().GetProductRulesList()) > 0 && scene.GetOneApiCheckInfo().GetIsCheckProductRule() > 0 {

		var productRuleList []*pb.SingleProductRule
		for _, singleItem := range scene.GetOneApiCheckInfo().GetProductRulesList() {
			productRuleList = append(productRuleList, &pb.SingleProductRule{
				UniqueId:          utils.NewString(singleItem.GetUniqueId()),
				SkuInfo:           singleItem.GetSkuInfo(),
				ProductId:         singleItem.GetProductId(),
				OrderInfo:         singleItem.GetOrderInfo(),
				BuyerPurchaseTime: singleItem.BuyerPurchaseTime,
			})
		}

		rule, _ := g.weightLimitServer.BatchCheckProductRule(ctx, &pb.BatchCheckProductRuleRequest{
			ReqHeader:    scene.GetReqHeader(),
			ProductRules: productRuleList,
		})
		results.CheckProductRuleResultMap = rule.CheckProductRuleResultMap
	}
	if len(scene.GetOneApiCheckInfo().GetProductServiceableList()) > 0 {
		var serviceableProductList []*pb.LaneAreaServiceabilityReq
		var laneRuleRequest []*pb.SingleCheckLaneRuleRequest
		for _, singleProduct := range scene.GetOneApiCheckInfo().GetProductServiceableList() {
			serviceableProductList = append(serviceableProductList, singleProduct.GetLaneServiceability())
			laneRuleRequest = append(laneRuleRequest, &pb.SingleCheckLaneRuleRequest{
				UniqueId:          utils.NewString(singleProduct.GetLaneServiceability().GetUniqueId()),
				Region:            utils.NewString(singleProduct.GetLaneServiceability().GetRegion()),
				SkuInfo:           singleProduct.GetSkuInfos(),
				LaneCodeList:      singleProduct.GetLaneServiceability().GetLaneCodes(),
				BuyerPurchaseTime: singleProduct.BuyerPurchaseTime,
			})
		}
		serviceable, _ := g.sceneServiceableServer.BatchCheckProductServiceable(ctx, &pb.BatchCheckProductServiceableReq{
			ReqHeader:   scene.GetReqHeader(),
			ProductList: serviceableProductList,
		})

		// check line rule
		laneRule, _ := g.weightLimitServer.BatchCheckLaneRule(ctx, &pb.BatchCheckLaneRuleRequest{
			ReqHeader:         scene.GetReqHeader(),
			CheckLaneRuleList: laneRuleRequest,
		})

		// merge line rule to serviceable
		mergeLineRuleToServiceable(serviceable.GetServiceableList(), laneRule.GetCheckLaneRuleResultMap())

		for _, serviceableInfo := range serviceable.GetServiceableList() {
			results.CheckServiceableAreaResultMap[serviceableInfo.GetUniqueId()] = serviceableInfo
		}

	}
	return results, nil
}

// @core
func (g *grpcOneApiController) GetAndCheckForCheckoutSceneV2(ctx context.Context, request *pb.OneApiRequestForCheckoutScene) (*pb.OneApiResponseForCheckoutScene, error) {
	results := &pb.OneApiResponseForCheckoutScene{
		RespHeader:                    http.GrpcSuccessRespHeader(),
		CheckProductRuleResultMap:     map[string]*pb.CheckProductRuleResponse{},
		CheckServiceableAreaResultMap: map[string]*pb.ProductServiceableRsp{},
		ParcelLibraryInfoMap:          make(map[string]*pb.ParcelLibraryInfo),
	}
	// check product rule first
	if len(request.GetOneApiCheckInfo().GetProductRulesList()) > 0 && request.GetOneApiCheckInfo().GetIsCheckProductRule() > 0 {
		productRuleList := make([]*pb.SingleProductRule, 0, len(request.GetOneApiCheckInfo().GetProductRulesList()))
		for _, singleItem := range request.GetOneApiCheckInfo().GetProductRulesList() {
			productRuleList = append(productRuleList, &pb.SingleProductRule{
				UniqueId:          utils.NewString(singleItem.GetUniqueId()),
				SkuInfo:           singleItem.GetSkuInfo(),
				ProductId:         singleItem.GetProductId(),
				OrderInfo:         singleItem.GetOrderInfo(),
				BuyerPurchaseTime: singleItem.BuyerPurchaseTime,
			})
		}

		rule, _ := g.weightLimitServer.BatchCheckProductRule(ctx, &pb.BatchCheckProductRuleRequest{
			ReqHeader:    request.GetReqHeader(),
			ProductRules: productRuleList,
		})
		results.CheckProductRuleResultMap = rule.CheckProductRuleResultMap
	}
	if len(request.GetOneApiCheckInfo().GetProductServiceableList()) > 0 {
		serviceableProductList := make([]*pb.LaneAreaServiceabilityReq, 0, len(request.GetOneApiCheckInfo().GetProductServiceableList()))
		laneRuleRequest := make([]*pb.SingleCheckLaneRuleRequest, 0, len(request.GetOneApiCheckInfo().GetProductServiceableList()))
		for _, singleProduct := range request.GetOneApiCheckInfo().GetProductServiceableList() {
			serviceableProductList = append(serviceableProductList, singleProduct.GetLaneServiceability())
			laneRuleRequest = append(laneRuleRequest, &pb.SingleCheckLaneRuleRequest{
				UniqueId:          utils.NewString(singleProduct.GetLaneServiceability().GetUniqueId()),
				Region:            utils.NewString(singleProduct.GetLaneServiceability().GetRegion()),
				SkuInfo:           singleProduct.GetSkuInfos(),
				LaneCodeList:      singleProduct.GetLaneServiceability().GetLaneCodes(),
				BuyerPurchaseTime: singleProduct.BuyerPurchaseTime,
			})
		}
		serviceable, _ := g.sceneServiceableServer.BatchCheckProductServiceableV2(ctx, &pb.BatchCheckProductServiceableReq{
			ReqHeader:   request.GetReqHeader(),
			ProductList: serviceableProductList,
		})

		// check line rule
		laneRule, _ := g.weightLimitServer.BatchCheckLaneRule(ctx, &pb.BatchCheckLaneRuleRequest{
			ReqHeader:         request.GetReqHeader(),
			CheckLaneRuleList: laneRuleRequest,
		})

		// merge line rule to serviceable
		mergeLineRuleToServiceable(serviceable.GetServiceableList(), laneRule.GetCheckLaneRuleResultMap())

		for _, serviceableInfo := range serviceable.GetServiceableList() {
			// 重排 actual_points 顺序，避免流量回放顺序差异
			for index, tmpLaneInfo := range serviceableInfo.GetLaneList() {
				actualPoints := tmpLaneInfo.GetServiceable().GetActualPoints()
				sort.Slice(tmpLaneInfo.GetServiceable().GetActualPoints(), func(i, j int) bool {
					return actualPoints[i].GetSiteId() < actualPoints[j].GetSiteId()
				})
				tmpLaneInfo.GetServiceable().ActualPoints = actualPoints
				serviceableInfo.GetLaneList()[index] = tmpLaneInfo
			}
			results.CheckServiceableAreaResultMap[serviceableInfo.GetUniqueId()] = serviceableInfo
		}
	}
	if len(request.GetOneApiCheckInfo().GetParcelLibraryQueries()) > 0 {
		dataMap, _ := g.parcelLibraryService.BatchGetLatestParcelLibraryDataBySkuInfos(utils.NewCommonCtx(ctx), request.GetOneApiCheckInfo().GetParcelLibraryQueries())
		for uniqueId, data := range dataMap {
			if data == nil {
				continue
			}

			results.ParcelLibraryInfoMap[uniqueId] = &pb.ParcelLibraryInfo{
				UniqueId:                      proto.String(uniqueId),
				AccurateLength:                proto.Float64(data.GetAccurateLength()),
				AccurateWidth:                 proto.Float64(data.GetAccurateWidth()),
				AccurateHeight:                proto.Float64(data.GetAccurateHeight()),
				AccurateWeight:                proto.Float64(data.GetAccurateWeight()),
				WeightFinAccuracy:             proto.Float64(data.WeightFinAccuracy),
				VolumetricFinAccuracy:         proto.Float64(data.VolumetricFinAccuracy),
				WeightEnhancedFinAccuracy:     proto.Float64(data.WeightEnhancedFinAccuracy),
				VolumetricEnhancedFinAccuracy: proto.Float64(data.VolumetricEnhancedFinAccuracy),
				IsBulkyParcel:                 proto.Int32(int32(data.IsBulkyParcel)),
			}
		}
	}

	return results, nil
}

// @core
func (g *grpcOneApiController) GetAndCheckForFulfillment(ctx context.Context, request *pb.OneApiRequestForFulfillment) (*pb.OneApiResponseForFulfillment, error) {
	results := &pb.OneApiResponseForFulfillment{
		RespHeader:                    http.GrpcSuccessRespHeader(),
		CheckServiceableAreaResultMap: map[string]*pb.ProductServiceableRsp{},
		CheckLaneRuleResultMap:        map[string]*pb.CheckLaneRuleResponse{},
	}
	if len(request.GetOneApiCheckInfo().GetProductServiceableList()) > 0 {
		var serviceableProductList []*pb.LaneAreaServiceabilityReq
		var laneRuleRequest []*pb.SingleCheckLaneRuleRequest
		for _, singleProduct := range request.GetOneApiCheckInfo().GetProductServiceableList() {
			serviceableProductList = append(serviceableProductList, singleProduct.GetLaneServiceability())
			laneRuleRequest = append(laneRuleRequest, &pb.SingleCheckLaneRuleRequest{
				UniqueId:          utils.NewString(singleProduct.GetLaneServiceability().GetUniqueId()),
				Region:            utils.NewString(singleProduct.GetLaneServiceability().GetRegion()),
				SkuInfo:           singleProduct.GetSkuInfos(),
				LaneCodeList:      singleProduct.GetLaneServiceability().GetLaneCodes(),
				SubPackageInfo:    singleProduct.SubPackageInfo,
				BuyerPurchaseTime: singleProduct.BuyerPurchaseTime,
			})
		}
		serviceable, _ := g.sceneServiceableServer.BatchCheckProductServiceableFulfillment(ctx, &pb.BatchCheckProductServiceableReq{
			ReqHeader:   request.GetReqHeader(),
			ProductList: serviceableProductList,
		})

		for _, serviceableInfo := range serviceable.GetServiceableList() {
			// 重排 actual_points 顺序，避免流量回放顺序差异
			for index, tmpLaneInfo := range serviceableInfo.GetLaneList() {
				actualPoints := tmpLaneInfo.GetServiceable().GetActualPoints()
				sort.Slice(tmpLaneInfo.GetServiceable().GetActualPoints(), func(i, j int) bool {
					return actualPoints[i].GetSiteId() < actualPoints[j].GetSiteId()
				})
				tmpLaneInfo.GetServiceable().ActualPoints = actualPoints
				serviceableInfo.GetLaneList()[index] = tmpLaneInfo
			}
			results.CheckServiceableAreaResultMap[serviceableInfo.GetUniqueId()] = serviceableInfo
		}
		laneRule, _ := g.weightLimitServer.BatchCheckLaneRule(ctx, &pb.BatchCheckLaneRuleRequest{
			ReqHeader:         request.GetReqHeader(),
			CheckLaneRuleList: laneRuleRequest,
		})

		for uniqId, laneRuleInfo := range laneRule.GetCheckLaneRuleResultMap() {
			if laneRuleInfo == nil {
				continue
			}
			// LineRuleRes 字段设置为空，减少 lps-task 反序列化消耗
			ruleLimitInfoMap := laneRuleInfo.GetRuleLimitInfoMap()
			for tmpLaneCode, singleCheckLaneRuleResult := range ruleLimitInfoMap {
				if singleCheckLaneRuleResult != nil {
					singleCheckLaneRuleResult.LineRuleRes = make([]*pb.SingleCheckLineRuleResult, 0)
					ruleLimitInfoMap[tmpLaneCode] = singleCheckLaneRuleResult
				}
			}
			laneRuleInfo.RuleLimitInfoMap = ruleLimitInfoMap
			results.CheckLaneRuleResultMap[uniqId] = laneRuleInfo
		}
	}
	return results, nil
}

// @core
func (g *grpcOneApiController) GetAndCheckCanRerouteLine(ctx context.Context, request *pb.OneApiRequestForReroute) (*pb.OneApiResponseForReroute, error) {
	results := &pb.OneApiResponseForReroute{
		RespHeader:      http.GrpcSuccessRespHeader(),
		RerouteCheckRsp: map[string]*pb.RerouteCheckRsp{},
	}

	if len(request.GetOneApiInfo().GetLaneServiceableList()) > 0 {
		var laneRuleRequest []*pb.SingleCheckLaneRuleRequest
		var serviceableProductList []*pb.LaneServiceableReq
		for _, singleReq := range request.GetOneApiInfo().GetLaneServiceableList() {
			serviceableProductList = append(serviceableProductList, singleReq)
			laneRuleRequest = append(laneRuleRequest, &pb.SingleCheckLaneRuleRequest{
				UniqueId:          utils.NewString(singleReq.GetUniqueId()),
				Region:            utils.NewString(singleReq.GetRegion()),
				SkuInfo:           request.GetOneApiInfo().GetSkuInfos(),
				LaneCodeList:      singleReq.GetLaneCodes(),
				BuyerPurchaseTime: request.GetOneApiInfo().BuyerPurchaseTime,
			})
		}
		serviceable, _ := g.sceneServiceableServer.BatchCheckLaneServiceableReroute(ctx, &pb.RerouteLaneServiceableReq{
			ReqHeader:           request.ReqHeader,
			LaneServiceableList: serviceableProductList,
		})
		if serviceable.GetRespHeader().GetRetcode() != lcos_error.SuccessCode {
			results.RespHeader.Retcode = serviceable.GetRespHeader().Retcode
			results.RespHeader.Message = serviceable.GetRespHeader().Message
			return results, nil
		}
		laneRule, _ := g.weightLimitServer.BatchCheckLaneRule(ctx, &pb.BatchCheckLaneRuleRequest{
			ReqHeader:         request.GetReqHeader(),
			CheckLaneRuleList: laneRuleRequest,
		})
		if laneRule.GetRespHeader().GetRetcode() != lcos_error.SuccessCode {
			results.RespHeader.Retcode = laneRule.GetRespHeader().Retcode
			results.RespHeader.Message = laneRule.GetRespHeader().Message
			return results, nil
		}
		for _, serviceableInfo := range serviceable.GetServiceableRsp() {
			laneCheckRsp := make([]*pb.LaneCheckResult, 0, len(serviceableInfo.ServiceableList))
			for _, laneSrv := range serviceableInfo.ServiceableList {
				tmpRes := &pb.LaneCheckResult{
					Code:          laneSrv.Code,
					Message:       laneSrv.Message,
					LaneCode:      laneSrv.LaneCode,
					LaneCodeGroup: laneSrv.LaneCodeGroup,
					Serviceable:   laneSrv.Serviceable,
				}
				rule, ok := laneRule.GetCheckLaneRuleResultMap()[serviceableInfo.GetUniqueId()]
				if ok {
					tmpRes.RuleLimitInfo = rule.GetRuleLimitInfoMap()[laneSrv.GetLaneCode()].GetLineRuleRes()
				}
				laneCheckRsp = append(laneCheckRsp, tmpRes)
			}
			results.RerouteCheckRsp[serviceableInfo.GetUniqueId()] = &pb.RerouteCheckRsp{
				LaneCheckResult: laneCheckRsp,
			}
		}

	}
	return results, nil
}
