package cdt_calculation

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestInitTimeBucketByDay(t *testing.T) {
	day := uint32(2)
	LeadTimeMin := float64(1.24)
	LeadTimeMax := float64(1.22)
	for _, dt := range initTimeBucketByDay(day, LeadTimeMin, LeadTimeMax) {
		println(dt.GetDay(), dt.GetStartHour(), dt.GetEndHour(), dt.GetCdtMax(), dt.GetCdtMin())
	}
}

// day存在，time不存在，day填充time
// day存在，time存在，time覆盖day
// day不存在，time存在，只展示存在的time
// day不存在，time不存在，返回空数组
// days存在，但是没有leadTime，存在有效timeBucket
// days存在，但是不在【1-7】之间
// days存在，但是没有leadTime，存在timeBucket，timeBucket数据非法（leadTime为0，hour不是%4=0）

func TestTransferDayGroupToDayAndTime(t *testing.T) {
	// 初始化 CdtExtraData 结构体
	extraData := &common_utils.CdtExtraData{}

	// 初始化 DayGroup 结构体
	dayGroup := &common_utils.DayGroup{
		DayName:     "Monday",
		Days:        []uint32{1},
		LeadTimeMin: 1.1,
		LeadTimeMax: 1.7,
		DDLBackward: 5.0,
	}

	timeBucket1 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    0,
		EndHour:      4,
		LeadTimeMin:  1,
		LeadTimeMax:  4,
		DDLBackward:  4.0,
	}

	timeBucket2 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    4,
		EndHour:      8,
		LeadTimeMin:  4,
		LeadTimeMax:  8,
		DDLBackward:  8.0,
	}

	// 将 TimeBucket 添加到 DayGroup 的 TimeBuckets 切片中
	dayGroup.TimeBuckets = append(dayGroup.TimeBuckets, timeBucket1)
	dayGroup.TimeBuckets = append(dayGroup.TimeBuckets, timeBucket2)

	// 将 DayGroup 添加到 CdtExtraData 的 DayGroups 切片中
	extraData.DayGroups = append(extraData.DayGroups, dayGroup)
	ctx := context.Background()
	mconfig := &cf.MutableConfig{}
	cf.SetMutableConfig(mconfig)
	cf.GetMutableConf(ctx).DayGroupAndTimeBucketConfig = cf.DayGroupAndTimeBucketConfig{
		UseDayGroupAndTimeBucket: 3,
	}
	length := 0
	for i, dt := range transferDayGroupToDayAndTime(context.Background(), extraData, "ID", 0, 0) {
		length++
		if i == 0 {
			assert.Equalf(t, timeBucket1.LeadTimeMin, dt.GetCdtMin(), "case length not equal expect, expect: %v, actual: %v", timeBucket1.LeadTimeMin, dt.GetCdtMin())
			assert.Equalf(t, timeBucket1.LeadTimeMax, dt.GetCdtMax(), "case length not equal expect, expect: %v, actual: %v", timeBucket1.LeadTimeMax, dt.GetCdtMax())
		}

		if i == 1 {
			assert.Equalf(t, timeBucket2.LeadTimeMin, dt.GetCdtMin(), "case length not equal expect, expect: %v, actual: %v", timeBucket2.LeadTimeMin, dt.GetCdtMin())
			assert.Equalf(t, timeBucket2.LeadTimeMax, dt.GetCdtMax(), "case length not equal expect, expect: %v, actual: %v", timeBucket2.LeadTimeMax, dt.GetCdtMax())
		}

		// 将 CdtExtraData 结构体转换为 JSON 字符串
		jsonData, err := json.Marshal(dt)
		if err != nil {
			fmt.Println("JSON Marshal error:", err)
			return
		}

		// 打印 JSON 字符串
		fmt.Println(string(jsonData))
	}

	assert.Equalf(t, 6, length, "case length not equal expect, expect: %v, actual: %v", 6, length)
}

func TestGetRealDayAndTimeList(t *testing.T) {
	// 初始化 CdtExtraData 结构体
	// 初始化 CdtExtraData 结构体
	extraData := &common_utils.CdtExtraData{}

	// 周一
	// 初始化 DayGroup 结构体
	dayGroup1 := &common_utils.DayGroup{
		DayName:     "Monday",
		Days:        []uint32{1},
		LeadTimeMin: 0,
		LeadTimeMax: 1.7,
		DDLBackward: 5.0,
	}

	timeBucket11 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    0,
		EndHour:      4,
		LeadTimeMin:  1,
		LeadTimeMax:  4,
		DDLBackward:  4.0,
	}

	timeBucket12 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    16,
		EndHour:      20,
		LeadTimeMin:  4,
		LeadTimeMax:  8,
		DDLBackward:  8.0,
	}
	// 将 TimeBucket 添加到 DayGroup 的 TimeBuckets 切片中
	dayGroup1.TimeBuckets = append(dayGroup1.TimeBuckets, timeBucket11)
	dayGroup1.TimeBuckets = append(dayGroup1.TimeBuckets, timeBucket12)

	// 将 DayGroup 添加到 CdtExtraData 的 DayGroups 切片中
	extraData.DayGroups = append(extraData.DayGroups, dayGroup1)

	// 周二
	// 初始化 DayGroup 结构体
	dayGroup2 := &common_utils.DayGroup{
		DayName:     "Monday",
		Days:        []uint32{2},
		LeadTimeMin: 1.9,
		LeadTimeMax: 1.7,
		DDLBackward: 5.0,
	}

	timeBucket21 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    0,
		EndHour:      4,
		LeadTimeMin:  1,
		LeadTimeMax:  4,
		DDLBackward:  4.0,
	}

	timeBucket22 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    16,
		EndHour:      20,
		LeadTimeMin:  4,
		LeadTimeMax:  8,
		DDLBackward:  8.0,
	}
	// 将 TimeBucket 添加到 DayGroup 的 TimeBuckets 切片中
	dayGroup2.TimeBuckets = append(dayGroup2.TimeBuckets, timeBucket21)
	dayGroup2.TimeBuckets = append(dayGroup2.TimeBuckets, timeBucket22)

	// 将 DayGroup 添加到 CdtExtraData 的 DayGroups 切片中
	extraData.DayGroups = append(extraData.DayGroups, dayGroup2)

	// 周三
	// 初始化 DayGroup 结构体
	dayGroup3 := &common_utils.DayGroup{
		DayName:     "Monday",
		Days:        []uint32{3},
		LeadTimeMin: 0,
		LeadTimeMax: 1.7,
		DDLBackward: 5.0,
	}

	timeBucket31 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    4,
		EndHour:      8,
		LeadTimeMin:  1,
		LeadTimeMax:  4,
		DDLBackward:  4.0,
	}

	timeBucket32 := &common_utils.TimeBucket{
		TimeName:     "Morning",
		DataTimeName: "0-4",
		StartHour:    16,
		EndHour:      20,
		LeadTimeMin:  4,
		LeadTimeMax:  8,
		DDLBackward:  8.0,
	}
	// 将 TimeBucket 添加到 DayGroup 的 TimeBuckets 切片中
	dayGroup3.TimeBuckets = append(dayGroup3.TimeBuckets, timeBucket31)
	dayGroup3.TimeBuckets = append(dayGroup3.TimeBuckets, timeBucket32)

	// 将 DayGroup 添加到 CdtExtraData 的 DayGroups 切片中
	extraData.DayGroups = append(extraData.DayGroups, dayGroup3)
	ctx := context.Background()
	mconfig := &cf.MutableConfig{}
	cf.SetMutableConfig(mconfig)
	cf.GetMutableConf(ctx).DayGroupAndTimeBucketConfig = cf.DayGroupAndTimeBucketConfig{
		UseDayGroupAndTimeBucket: 3,
	}
	dayAndTimeList := transferDayGroupToDayAndTime(context.Background(), extraData, "ID", 0, 0)
	for _, dt := range dayAndTimeList {
		// 将 CdtExtraData 结构体转换为 JSON 字符串
		jsonData, err := json.Marshal(dt)
		if err != nil {
			fmt.Println("JSON Marshal error:", err)
			return
		}

		// 打印 JSON 字符串
		fmt.Println(string(jsonData))
	}
	fmt.Println()
	fmt.Println()

	// startDay == endDay 且 startHour == endHour
	realDayAndTimeList := getRealDayAndTimeList(1, 1, 1, 1, dayAndTimeList)
	assert.Equalf(t, 1, len(realDayAndTimeList), "case not equal expect, expect: %v, actual: %v", 1, len(realDayAndTimeList))
	// startDay == endDay 且 startHour < endHour
	realDayAndTimeList = getRealDayAndTimeList(1, 1, 1, 20, dayAndTimeList)
	assert.Equalf(t, 6, len(realDayAndTimeList), "case not equal expect, expect: %v, actual: %v", 6, len(realDayAndTimeList))
	// startDay == endDay 且 startHour > endHour
	realDayAndTimeList = getRealDayAndTimeList(1, 1, 20, 1, dayAndTimeList)
	assert.Equalf(t, len(dayAndTimeList), len(realDayAndTimeList), "case not equal expect, expect: %v, actual: %v", len(dayAndTimeList), len(realDayAndTimeList))
	// startDay > endDay 且 startHour == endHour
	realDayAndTimeList = getRealDayAndTimeList(3, 1, 20, 20, dayAndTimeList)
	assert.Equalf(t, 7, len(realDayAndTimeList), "case not equal expect, expect: %v, actual: %v", 7, len(realDayAndTimeList))
	// startDay > endDay 且 startHour < endHour
	realDayAndTimeList = getRealDayAndTimeList(3, 1, 2, 20, dayAndTimeList)
	assert.Equalf(t, 12, len(realDayAndTimeList), "case not equal expect, expect: %v, actual: %v", 12, len(realDayAndTimeList))
	// startDay > endDay 且 startHour > endHour
	realDayAndTimeList = getRealDayAndTimeList(3, 1, 20, 2, dayAndTimeList)
	assert.Equalf(t, 2, len(realDayAndTimeList), "case not equal expect, expect: %v, actual: %v", 2, len(realDayAndTimeList))
	// startDay < endDay 且 startHour == endHour
	realDayAndTimeList = getRealDayAndTimeList(1, 2, 2, 2, dayAndTimeList)
	assert.Equalf(t, 7, len(realDayAndTimeList), "case not equal expect, expect: %v, actual: %v", 7, len(realDayAndTimeList))
	// startDay < endDay 且 startHour < endHour
	realDayAndTimeList = getRealDayAndTimeList(1, 2, 2, 3, dayAndTimeList)
	assert.Equalf(t, 7, len(realDayAndTimeList), "case not equal expect, expect: %v, actual: %v", 7, len(realDayAndTimeList))
	// startDay < endDay 且 startHour > endHour
	realDayAndTimeList = getRealDayAndTimeList(1, 2, 3, 2, dayAndTimeList)
	assert.Equalf(t, 7, len(realDayAndTimeList), "case not equal expect, expect: %v, actual: %v", 7, len(realDayAndTimeList))
	for _, dt := range realDayAndTimeList {
		// 将 CdtExtraData 结构体转换为 JSON 字符串
		jsonData, err := json.Marshal(dt)
		if err != nil {
			fmt.Println("JSON Marshal error:", err)
			return
		}

		// 打印 JSON 字符串
		fmt.Println(string(jsonData))
	}
	fmt.Println()
	fmt.Println()

	querId := "1"
	tmpCdtReply := &pb.CdtReply{
		QueryId:    &querId,
		DayAndTime: realDayAndTimeList,
	}

	jsonData, err := json.Marshal(tmpCdtReply)
	if err != nil {
		fmt.Println("JSON Marshal error:", err)
		return
	}

	// 打印 JSON 字符串
	fmt.Println(string(jsonData))
}

func Test_GetNeededTimeWindows(t *testing.T) {
	for startDay := uint32(0); startDay <= constant.DaysCount; startDay++ {
		for startHour := uint32(0); startHour < 24; startHour++ {
			for endDay := uint32(0); endDay <= constant.DaysCount; endDay++ {
				for endHour := uint32(0); endHour < 24; endHour++ {
					fmt.Println("----------")
					_, m := GetNeededTimeWindows(startDay, endDay, startHour, endHour)
					for k, v := range m {
						for v1, v2 := range v {
							if v2 > 5 {
								panic(v2)
							}
							fmt.Print(k, v1, v1+4, v2, "\n")
						}
					}
					fmt.Println("----------")
					fmt.Print("\n")
				}
			}
		}
	}
}

func Test_TransferDayGroupToDayAndTimeV2(t *testing.T) {
	// 初始化 CdtExtraData 结构体
	extraData := &common_utils.CdtExtraData{}
	extraDataStr := "{\"day_groups\":[{\"days\":[6],\"time\":[{\"start\":8,\"end\":12,\"min\":12.163938,\"max\":20.486164},{\"start\":16,\"end\":20,\"min\":11.719088,\"max\":18.853403},{\"start\":12,\"end\":16,\"min\":11.897602,\"max\":17.484604}]},{\"days\":[7],\"time\":[{\"start\":16,\"end\":20,\"min\":11.724032,\"max\":16.934101},{\"start\":8,\"end\":12,\"min\":11.328553,\"max\":18.266653}]},{\"days\":[1],\"time\":[{\"start\":16,\"end\":20,\"min\":10.7568865,\"max\":15.811126},{\"start\":20,\"end\":24,\"min\":10.663148,\"max\":18.743784},{\"start\":12,\"end\":16,\"min\":10.911688,\"max\":16.121115},{\"start\":8,\"end\":12,\"min\":10.937859,\"max\":17.743406}]},{\"days\":[2],\"time\":[{\"start\":12,\"end\":16,\"min\":10.078942,\"max\":15.130558},{\"start\":20,\"end\":24,\"min\":9.741526,\"max\":18.238922},{\"start\":16,\"end\":20,\"min\":9.897917,\"max\":15.819631},{\"start\":8,\"end\":12,\"min\":10.094528,\"max\":14.534164}]},{\"days\":[3],\"time\":[{\"start\":8,\"end\":12,\"min\":9.228354,\"max\":17.252777},{\"start\":12,\"end\":16,\"min\":9.041875,\"max\":17.12836},{\"start\":16,\"end\":20,\"min\":9.957674,\"max\":17.467112}]},{\"days\":[4],\"time\":[{\"start\":8,\"end\":12,\"min\":11.017794,\"max\":16.978666},{\"start\":12,\"end\":16,\"min\":10.09934,\"max\":18.602339},{\"start\":16,\"end\":20,\"min\":9.874599,\"max\":17.743328}]},{\"days\":[5],\"time\":[{\"start\":8,\"end\":12,\"min\":10.304676,\"max\":18.412083},{\"start\":16,\"end\":20,\"min\":10.120856,\"max\":17.687492},{\"start\":12,\"end\":16,\"min\":9.945032,\"max\":18.386896},{\"start\":20,\"end\":24,\"min\":9.745889,\"max\":17.922535}]}]}"
	_ = jsoniter.UnmarshalFromString(extraDataStr, extraData)

	ctx := context.Background()
	mconfig := &cf.MutableConfig{}
	cf.SetMutableConfig(mconfig)
	cf.MutableConf = mconfig
	cf.GetMutableConf(ctx).DayGroupAndTimeBucketConfig = cf.DayGroupAndTimeBucketConfig{
		RegionInfo: map[string]string{
			"MY": "3",
		},
	}
	length := 0

	var startDay, endDay, startHour, endHour uint32 = 2, 2, 10, 11
	var deltaMin, deltaMax float64 = 0, 3

	dayGroupAndTimeBucketCount, dayGroupAndTimeBucket := GetNeededTimeWindows(startDay, endDay, startHour, endHour)

	dts := transferDayGroupToDayAndTimeV2(context.Background(), extraData, "MY", deltaMin, deltaMax, dayGroupAndTimeBucketCount, dayGroupAndTimeBucket)
	for _, dt := range dts {
		length++

		// 将 CdtExtraData 结构体转换为 JSON 字符串
		jsonData, err := json.Marshal(dt)
		if err != nil {
			fmt.Println("JSON Marshal error:", err)
			return
		}

		// 打印 JSON 字符串
		fmt.Println(string(jsonData))
	}

	assert.Equalf(t, 6, length, "case length not equal expect, expect: %v, actual: %v", 6, length)
}
