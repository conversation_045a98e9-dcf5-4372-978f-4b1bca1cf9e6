package cdt_calculation

import pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"

type QueryMap struct {
	QueryId     string  `json:"query_id"`
	UpdateEvent uint32  `json:"update_event"`
	StartDay    *uint32 `json:"start_day"`
	StartHour   *uint32 `json:"start_time"`
	EndDay      *uint32 `json:"end_day"`
	EndHour     *uint32 `json:"end_time"`
	Region      string  `json:"region"`
	GroupTag    uint32  `json:"group_tag"`
}

func (q QueryMap) GetStartDay() uint32 {
	if q.StartDay == nil {
		return 0
	}
	return *q.StartDay
}

func (q QueryMap) GetStartHour() uint32 {
	if q.StartHour == nil {
		return 0
	}
	return *q.StartHour
}

func (q QueryMap) GetEndDay() uint32 {
	if q.EndDay == nil {
		return 0
	}
	return *q.EndDay
}

func (q QueryMap) GetEndHour() uint32 {
	if q.EndHour == nil {
		return 0
	}
	return *q.EndHour
}

func (q QueryMap) NeedDayGroupAndTimeBucket() bool {
	return q.StartHour != nil && q.EndHour != nil && q.StartDay != nil && q.EndDay != nil
}

type ByDayAndStartHour []*pb.DayAndTime

func (a ByDayAndStartHour) Len() int      { return len(a) }
func (a ByDayAndStartHour) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a ByDayAndStartHour) Less(i, j int) bool {
	if a[i].GetDay() != a[j].GetDay() {
		return a[i].GetDay() < a[j].GetDay()
	}
	return a[i].GetStartHour() < a[j].GetStartHour()
}
