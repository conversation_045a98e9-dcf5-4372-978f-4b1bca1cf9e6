package cdt_calculation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/day_group_and_time_bucket_constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/delta"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/math"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/cdt_management/cdt_calculation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/common_utils"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

func convertCdtSkuInfo(skus *pb.CdtSkus) *cdt_calculation.CdtSkuInfo {
	var skuInfo cdt_calculation.CdtSkuInfo
	skuInfo.ShopId = skus.GetShopId()
	for _, v := range skus.GetSkuItems() {
		skuInfo.SkuInfo = append(skuInfo.SkuInfo, &cdt_calculation.CdtItem{
			ItemId:    v.GetItemId(),
			Length:    v.GetLength(),
			Width:     v.GetWidth(),
			Height:    v.GetHeight(),
			Weight:    v.GetWeight(),
			Quantity:  v.GetQuantity(),
			ItemPrice: v.GetItemPrice(),
		})
	}
	return &skuInfo
}

func transferPbProductToProduct(productInfo *pb.ProductInfo) *cdt_calculation.CdtProductInfo {
	var sellerStateLocationID, sellerCityLocationID, sellerDistrictLocationID int
	var buyerStateLocationID, buyerCityLocationID, buyerDistrictLocationID int
	sellerStateLocationID = int(productInfo.GetSellerAddr().GetStateLocationId())
	sellerCityLocationID = int(productInfo.GetSellerAddr().GetCityLocationId())
	sellerDistrictLocationID = int(productInfo.GetSellerAddr().GetDistrictLocationId())
	buyerStateLocationID = int(productInfo.GetBuyerAddr().GetStateLocationId())
	buyerCityLocationID = int(productInfo.GetBuyerAddr().GetCityLocationId())
	buyerDistrictLocationID = int(productInfo.GetBuyerAddr().GetDistrictLocationId())
	productItem := &cdt_calculation.CdtProductInfo{
		QueryID:     productInfo.GetQueryId(),
		ProductID:   productInfo.GetProductId(),
		LaneCode:    productInfo.GetLaneCode(),
		UpdateEvent: uint8(productInfo.GetUpdateEvent()),
		NextEvent:   uint8(productInfo.GetNextEvent()),
		IsCB:        uint8(productInfo.GetIsCb()),
		IsSiteLine:  uint8(productInfo.GetIsSiteLine()),
		Region:      productInfo.GetRegion(),
		SellerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(sellerStateLocationID),
			CityLocationId:     utils.NewInt(sellerCityLocationID),
			DistrictLocationId: utils.NewInt(sellerDistrictLocationID),
			PostalCode:         utils.NewString(productInfo.GetSellerAddr().GetPostcode()),
		},
		BuyerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(buyerStateLocationID),
			CityLocationId:     utils.NewInt(buyerCityLocationID),
			DistrictLocationId: utils.NewInt(buyerDistrictLocationID),
			PostalCode:         utils.NewString(productInfo.GetBuyerAddr().GetPostcode()),
		},
		ItemId:         productInfo.GetItemId(),
		BuyerId:        productInfo.GetBuyerId(),
		Scenario:       uint8(productInfo.GetScenario()),
		NeedVolume:     uint8(productInfo.GetNeedVolume()),
		GroupTag:       productInfo.GetGroupTag(),
		StartFCodeType: uint8(productInfo.GetStartFcodeType()),
		RequestTime:    productInfo.GetRequestTime(),
		CdtScene:       productInfo.GetScene(),
	}
	//if skuInfo not nil,fill skuInfo
	if productInfo.GetSkuInfo() != nil {
		productItem.SkuInfo = convertCdtSkuInfo(productInfo.GetSkuInfo())
	}
	var lineList []*cdt_calculation.LineInfo
	for _, singleLineInfo := range productInfo.GetLineList() {
		lineList = append(lineList, &cdt_calculation.LineInfo{
			LineID:  singleLineInfo.GetLineId(),
			SubType: singleLineInfo.GetLineSubType(),
		})
	}
	productItem.LineList = lineList

	var fChannelList []*cdt_calculation.FChannelInfo
	for _, fChannelInfo := range productInfo.GetAvailableFulfillmentChannelIds() {
		fChannelList = append(fChannelList, &cdt_calculation.FChannelInfo{
			FChannelID: fChannelInfo.GetFulfillmentChannelId(),
			IsCB:       uint8(fChannelInfo.GetIsCb()),
			IsSiteLine: uint8(fChannelInfo.GetIsSiteLine()),
		})
	}
	productItem.FChannelList = fChannelList
	return productItem
}

func transferPbSimpleProductToProduct(productInfo *pb.SimpleProductInfo) *cdt_calculation.CdtProductInfo {
	var sellerStateLocationID, sellerCityLocationID, sellerDistrictLocationID int
	var buyerStateLocationID, buyerCityLocationID, buyerDistrictLocationID int
	sellerStateLocationID = int(productInfo.GetSellerAddr().GetStateLocationId())
	sellerCityLocationID = int(productInfo.GetSellerAddr().GetCityLocationId())
	sellerDistrictLocationID = int(productInfo.GetSellerAddr().GetDistrictLocationId())
	buyerStateLocationID = int(productInfo.GetBuyerAddr().GetStateLocationId())
	buyerCityLocationID = int(productInfo.GetBuyerAddr().GetCityLocationId())
	buyerDistrictLocationID = int(productInfo.GetBuyerAddr().GetDistrictLocationId())
	productItem := &cdt_calculation.CdtProductInfo{
		QueryID:     productInfo.GetQueryId(),
		ProductID:   productInfo.GetProductId(),
		UpdateEvent: uint8(productInfo.GetUpdateEvent()),
		IsCB:        uint8(productInfo.GetIsCb()),
		IsSiteLine:  uint8(productInfo.GetIsSiteLine()),
		Region:      productInfo.GetRegion(),
		SellerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(sellerStateLocationID),
			CityLocationId:     utils.NewInt(sellerCityLocationID),
			DistrictLocationId: utils.NewInt(sellerDistrictLocationID),
			PostalCode:         utils.NewString(productInfo.GetSellerAddr().GetPostcode()),
		},
		BuyerAddr: &cdt_calculation.AddressInfo{
			StateLocationId:    utils.NewInt(buyerStateLocationID),
			CityLocationId:     utils.NewInt(buyerCityLocationID),
			DistrictLocationId: utils.NewInt(buyerDistrictLocationID),
			PostalCode:         utils.NewString(productInfo.GetBuyerAddr().GetPostcode()),
		},
		RequestTime: productInfo.GetRequestTime(),
		CdtScene:    productInfo.GetScene(),
	}
	return productItem
}

func CreateQueryIdInfoMap(queryId string, updateEvent uint32, startDay, startHour, endDay, endHour *uint32, groupTag uint32, region string) QueryMap {
	return QueryMap{
		QueryId:     queryId,
		UpdateEvent: updateEvent,
		StartDay:    startDay,
		StartHour:   startHour,
		EndDay:      endDay,
		EndHour:     endHour,
		Region:      region,
		GroupTag:    groupTag,
	}
}

func transferDayGroupToDayAndTime(ctx context.Context, dayGroups *common_utils.CdtExtraData, region string, deltaMin, deltaMax float64) []*pb.DayAndTime {
	var dayAndTimes []*pb.DayAndTime
	if dayGroups == nil {
		return dayAndTimes
	}
	if !config.GetNeedUseGroupDayAndTimeBucketConf(ctx, region, day_group_and_time_bucket_constant.EdtUse) {
		return dayAndTimes
	}
	for _, dayGroup := range dayGroups.DayGroups {
		if dayGroup == nil {
			continue
		}

		for _, day := range dayGroup.Days {
			// day 需要在1-7之间, 周一是1，周日是7
			if !checkDayValid(day) {
				continue
			}

			var initDayAndTimes []*pb.DayAndTime
			leadTimeMax := roundLeadTimeWithGreySwitch(ctx, dayGroup.LeadTimeMax)
			leadTimeMin := roundLeadTimeWithGreySwitch(ctx, dayGroup.LeadTimeMin)
			existDayGroupLeadTime := false
			// dayGroup leadTime 必须为非0正数 才会有dayGroup维度的兜底值
			if leadTimeMax > 0 && leadTimeMin >= 0 {
				leadTimeMin, leadTimeMax = delta.CalculateByDelta(leadTimeMin, leadTimeMax, deltaMin, deltaMax)
				initDayAndTimes = initTimeBucketByDay(day, leadTimeMin, leadTimeMax)
				existDayGroupLeadTime = true
			}

			// 如果没有dayGroup维度的leadTime min/max，则直接添加新数据至dayAndTimes, 否则在兜底的initDayAndTimes上修改leadTime min/max
			if existDayGroupLeadTime {
				for _, timeBucket := range dayGroup.TimeBuckets {
					if timeBucket == nil {
						continue
					}
					if checkTimeBucketValid(timeBucket.LeadTimeMin, timeBucket.LeadTimeMax, timeBucket.StartHour, timeBucket.EndHour) {
						timeBucketTemp := *timeBucket
						index := timeBucketTemp.EndHour/constant.TimeInterval - 1
						timeBucketTemp.LeadTimeMin, timeBucketTemp.LeadTimeMax = delta.CalculateByDelta(timeBucketTemp.LeadTimeMin, timeBucketTemp.LeadTimeMax, deltaMin, deltaMax)
						timeLeadTimeMin := roundLeadTimeWithGreySwitch(ctx, timeBucketTemp.LeadTimeMin)
						timeLeadTimeMax := roundLeadTimeWithGreySwitch(ctx, timeBucketTemp.LeadTimeMax)
						initDayAndTimes[index].CdtMin = utils.NewFloat64(timeLeadTimeMin)
						initDayAndTimes[index].CdtMax = utils.NewFloat64(timeLeadTimeMax)
					}
				}
				dayAndTimes = append(dayAndTimes, initDayAndTimes...)
			} else {
				for _, timeBucket := range dayGroup.TimeBuckets {
					if timeBucket == nil {
						continue
					}
					if checkTimeBucketValid(timeBucket.LeadTimeMin, timeBucket.LeadTimeMax, timeBucket.StartHour, timeBucket.EndHour) {
						timeBucketTemp := *timeBucket
						timeBucketTemp.LeadTimeMin, timeBucketTemp.LeadTimeMax = delta.CalculateByDelta(timeBucketTemp.LeadTimeMin, timeBucketTemp.LeadTimeMax, deltaMin, deltaMax)
						timeLeadTimeMin := roundLeadTimeWithGreySwitch(ctx, timeBucketTemp.LeadTimeMin)
						timeLeadTimeMax := roundLeadTimeWithGreySwitch(ctx, timeBucketTemp.LeadTimeMax)
						dayAndTime := &pb.DayAndTime{
							Day:       utils.NewUint32(day),
							StartHour: utils.NewUint32(timeBucket.StartHour),
							EndHour:   utils.NewUint32(timeBucket.EndHour),
							CdtMin:    utils.NewFloat64(timeLeadTimeMin),
							CdtMax:    utils.NewFloat64(timeLeadTimeMax),
						}
						dayAndTimes = append(dayAndTimes, dayAndTime)
					}
				}
			}
		}
	}

	return dayAndTimes
}

func transferDayGroupToDayAndTimeV2(ctx context.Context, dayGroups *common_utils.CdtExtraData, region string, deltaMin, deltaMax float64,
	dayAndTimeBucketLen int, neededDayAndBucketMap map[uint32]map[uint32]int) []*pb.DayAndTime {
	if dayGroups == nil || len(neededDayAndBucketMap) == 0 {
		return nil
	}

	dayAndTimes := make([]*pb.DayAndTime, 0, dayAndTimeBucketLen)
	tempDayAndTimes := make([]*pb.DayAndTime, constant.OneDayBucketsCount)
	for _, dayGroup := range dayGroups.DayGroups {
		if dayGroup == nil {
			continue
		}

		leadTimeMax := roundLeadTimeWithGreySwitch(ctx, dayGroup.LeadTimeMax)
		leadTimeMin := roundLeadTimeWithGreySwitch(ctx, dayGroup.LeadTimeMin)
		var existDayGroupLeadTime bool
		// dayGroup leadTime 必须为非0正数 才会有dayGroup维度的兜底值
		if leadTimeMax > 0 && leadTimeMin >= 0 {
			leadTimeMin, leadTimeMax = delta.CalculateByDelta(leadTimeMin, leadTimeMax, deltaMin, deltaMax)
			existDayGroupLeadTime = true
		}

		// 初始化
		for _, day := range dayGroup.Days {
			// day 需要在1-7之间
			if !checkDayValid(day) {
				continue
			}
			needTimeStartMap, ok := neededDayAndBucketMap[day]
			// 请求不需要此天
			if !ok || len(needTimeStartMap) == 0 {
				continue
			}

			// 如果没有dayGroup维度的leadTime min/max，则直接添加新数据至dayAndTimes, 否则在兜底的initDayAndTimes上修改leadTime min/max
			if existDayGroupLeadTime {
				initDayAndTimes := initTimeBucketByDayWithNeeded(tempDayAndTimes[:len(needTimeStartMap)], day, leadTimeMin, leadTimeMax, needTimeStartMap)
				for _, timeBucket := range dayGroup.TimeBuckets {
					if timeBucket == nil {
						continue
					}
					index, startHourOk := needTimeStartMap[timeBucket.StartHour]
					if !startHourOk {
						continue
					}
					if checkTimeBucketValid(timeBucket.LeadTimeMin, timeBucket.LeadTimeMax, timeBucket.StartHour, timeBucket.EndHour) {
						timeBucketTemp := *timeBucket
						timeBucketTemp.LeadTimeMin, timeBucketTemp.LeadTimeMax = delta.CalculateByDelta(timeBucketTemp.LeadTimeMin, timeBucketTemp.LeadTimeMax, deltaMin, deltaMax)
						timeLeadTimeMin := roundLeadTimeWithGreySwitch(ctx, timeBucketTemp.LeadTimeMin)
						timeLeadTimeMax := roundLeadTimeWithGreySwitch(ctx, timeBucketTemp.LeadTimeMax)
						initDayAndTimes[index].CdtMin = utils.NewFloat64(timeLeadTimeMin)
						initDayAndTimes[index].CdtMax = utils.NewFloat64(timeLeadTimeMax)
					}
				}
				dayAndTimes = append(dayAndTimes, initDayAndTimes...)
			} else {
				for _, timeBucket := range dayGroup.TimeBuckets {
					if timeBucket == nil {
						continue
					}
					_, startHourOk := needTimeStartMap[timeBucket.StartHour]
					if !startHourOk {
						continue
					}
					if checkTimeBucketValid(timeBucket.LeadTimeMin, timeBucket.LeadTimeMax, timeBucket.StartHour, timeBucket.EndHour) {
						timeBucketTemp := *timeBucket
						timeBucketTemp.LeadTimeMin, timeBucketTemp.LeadTimeMax = delta.CalculateByDelta(timeBucketTemp.LeadTimeMin, timeBucketTemp.LeadTimeMax, deltaMin, deltaMax)
						timeLeadTimeMin := roundLeadTimeWithGreySwitch(ctx, timeBucketTemp.LeadTimeMin)
						timeLeadTimeMax := roundLeadTimeWithGreySwitch(ctx, timeBucketTemp.LeadTimeMax)
						dayAndTime := &pb.DayAndTime{
							Day:       utils.NewUint32(day),
							StartHour: utils.NewUint32(timeBucket.StartHour),
							EndHour:   utils.NewUint32(timeBucket.EndHour),
							CdtMin:    utils.NewFloat64(timeLeadTimeMin),
							CdtMax:    utils.NewFloat64(timeLeadTimeMax),
						}
						dayAndTimes = append(dayAndTimes, dayAndTime)
					}
				}
			}
		}
	}

	return dayAndTimes
}

func roundLeadTimeWithGreySwitch(ctx context.Context, leadTime float64) float64 {
	return math.RoundnToEven(leadTime, 4)
}

func checkDayValid(day uint32) bool {
	return day > 0 && day < 8
}

func checkTimeBucketValid(leadTimeMin, leadTimeMax float64, startHour, endHour uint32) bool {
	return leadTimeMin >= 0 && leadTimeMax > 0 && startHour%constant.TimeInterval == 0 && endHour%constant.TimeInterval == 0 && startHour <= constant.TwentyFourOClock && endHour <= constant.TwentyFourOClock
}

func initTimeBucketByDay(day uint32, LeadTimeMin, LeadTimeMax float64) []*pb.DayAndTime {
	dayIntervalNum := constant.TwentyFourOClock / constant.TimeInterval // 一天六个时间段
	dayAndTimes := make([]*pb.DayAndTime, 0, dayIntervalNum)
	for i := 0; i < dayIntervalNum; i++ {
		startHour := uint32(i * constant.TimeInterval)
		endHour := uint32((i + 1) * constant.TimeInterval)
		dayAndTime := &pb.DayAndTime{
			Day:       utils.NewUint32(day),
			StartHour: utils.NewUint32(startHour),
			EndHour:   utils.NewUint32(endHour),
			CdtMin:    utils.NewFloat64(LeadTimeMin),
			CdtMax:    utils.NewFloat64(LeadTimeMax),
		}
		dayAndTimes = append(dayAndTimes, dayAndTime)
	}
	return dayAndTimes
}

func initTimeBucketByDayWithNeeded(initDayAndTimes []*pb.DayAndTime, day uint32, LeadTimeMin, LeadTimeMax float64, needTimeStartMap map[uint32]int) []*pb.DayAndTime {
	for startHour, index := range needTimeStartMap {
		endHour := startHour + constant.TimeInterval
		dayAndTime := &pb.DayAndTime{
			Day:       utils.NewUint32(day),
			StartHour: utils.NewUint32(startHour),
			EndHour:   utils.NewUint32(endHour),
			CdtMin:    utils.NewFloat64(LeadTimeMin),
			CdtMax:    utils.NewFloat64(LeadTimeMax),
		}
		initDayAndTimes[index] = dayAndTime
	}
	return initDayAndTimes
}

func getRealDayAndTimeList(startDay, endDay, startHour, endHour uint32, dayAndTimeList []*pb.DayAndTime) []*pb.DayAndTime {
	// 对于异常数据返回所有从db里找到的数据
	if startHour == constant.TwentyFourOClock || endHour == constant.TwentyFourOClock || (startDay == endDay && startHour > endHour) {
		return dayAndTimeList
	}
	tensDigits := uint32(10)
	// 下面startNum，endNum是便于取符合条件的dayAndTimeList区间的值做的逻辑
	// 将day作为后续比较的两位数的十位，hour除以4作为两位数的个位，十位的取值区间在【1-7】，个位的取值区间在【0-6】
	startNum := startDay*tensDigits + (startHour / constant.TimeInterval)
	endNum := endDay*tensDigits + (endHour / constant.TimeInterval)
	var realDayAndTimeList []*pb.DayAndTime
	// 正常连续的情况
	for _, dayAndTime := range dayAndTimeList {
		num := dayAndTime.GetDay()*tensDigits + (dayAndTime.GetStartHour() / constant.TimeInterval)
		if startNum <= endNum && startNum <= num && num <= endNum {
			realDayAndTimeList = append(realDayAndTimeList, dayAndTime)
		} else if startNum > endNum && (num <= endNum || num >= startNum) {
			realDayAndTimeList = append(realDayAndTimeList, dayAndTime)
		}
	}

	return realDayAndTimeList
}

func GetNeededTimeWindows(startDay, endDay, startHour, endHour uint32) (int, map[uint32]map[uint32]int) {
	// 调整终止时间，如果起始时间晚于终止时间
	if (startDay > endDay) || (startDay == endDay && startHour >= endHour) {
		endDay += constant.DaysCount
	}
	// 创建一个 map 来存储每一天的时间窗口
	timeWindows := make(map[uint32]map[uint32]int)

	// 计算起始时间和终止时间的总小时数, +constant.DaysCount 避免 -1 越界
	startTotalHours := (startDay+constant.DaysCount-1)*constant.TwentyFourOClock + startHour
	endTotalHours := (endDay+constant.DaysCount-1)*constant.TwentyFourOClock + endHour

	// 遍历每个小时，确定每一天的时间窗口
	windowSize := 0
	for totalHours := startTotalHours; totalHours < endTotalHours; totalHours++ {
		realHours := totalHours % (constant.TwentyFourOClock * constant.DaysCount)
		intervalDay := realHours / constant.TwentyFourOClock
		intervalHour := realHours % constant.TwentyFourOClock
		window := intervalHour / 4      // 计算时间窗口
		startHourOfWindow := window * 4 // 计算窗口的起始小时

		// 初始化时间窗口的 map
		if timeWindows[intervalDay+1] == nil {
			timeWindows[intervalDay+1] = make(map[uint32]int)
		}

		// 检查窗口是否已被添加
		if _, exists := timeWindows[intervalDay+1][startHourOfWindow]; !exists {
			// 将窗口的起始小时和编号添加到对应的天中
			windowSize++
			index := len(timeWindows[intervalDay+1])
			timeWindows[intervalDay+1][startHourOfWindow] = index
		}
	}

	return windowSize, timeWindows
}
