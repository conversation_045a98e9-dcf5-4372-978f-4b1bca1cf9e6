package tpl_id_line_id_ref

import (
	"context"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/tpl_id_line_id_ref"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type tplIDLineIDRefController struct {
	tplIDLineIDRefService tpl_id_line_id_ref.TPLIDLineIDRefInterface
}

var _ pb.LcosTPLIDLineIDRefServiceServer = (*tplIDLineIDRefController)(nil)

func NewTplIDLineIDRefController(tplIDLineIDRefService tpl_id_line_id_ref.TPLIDLineIDRefInterface) *tplIDLineIDRefController {
	return &tplIDLineIDRefController{
		tplIDLineIDRefService: tplIDLineIDRefService,
	}
}

func (t tplIDLineIDRefController) QueryThreePLIDLineIDRefs(ctx context.Context, request *pb.QueryThreePLIDLineIDRefRequest) (*pb.QueryThreePLIDLineIDRefResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	refs, lcosErr := t.tplIDLineIDRefService.QueryTPLIDLineIDRef(lcosCtx, request.LineId, request.ThreePlId)
	if lcosErr != nil {
		return &pb.QueryThreePLIDLineIDRefResponse{
			RespHeader:          http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
			ThreePlIdLineIdRefs: nil,
		}, nil
	}
	var returnRefList []*pb.ThreePLIDLineIDRef
	for _, ref := range refs {
		crossBorderType := uint32(ref.CrossBorderType)
		isVirtual := uint32(ref.IsVirtual)
		progress := uint32(ref.Progress)
		returnRefList = append(returnRefList, &pb.ThreePLIDLineIDRef{
			Id:                  &ref.ID,
			ThreePlId:           &ref.ThreePLID,
			LcsChannelId:        &ref.LcsChannelID,
			ThreePlName:         &ref.ThreePLName,
			ThreePlType:         &ref.ThreePLType,
			ThreePlSubType:      &ref.ThreePLSubType,
			ChannelType:         &ref.ChannelType,
			LineId:              &ref.LineID,
			LineName:            &ref.LineName,
			LaneCode:            &ref.LaneCode,
			CrossBorderType:     &crossBorderType,
			IsVirtual:           &isVirtual,
			ShippingChannelId:   &ref.ShippingChannelID,
			ShippingChannelName: &ref.ShippingChannelName,
			Progress:            &progress,
			Region:              &ref.Region,
		})
	}
	return &pb.QueryThreePLIDLineIDRefResponse{
		RespHeader:          http.GrpcSuccessRespHeader(),
		ThreePlIdLineIdRefs: returnRefList,
	}, nil
}
