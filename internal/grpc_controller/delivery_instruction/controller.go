package delivery_instruction

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	delivery_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/delivery_instruction"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/delivery_instrcution/delivery_method"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"strings"
)

type deliveryInstructionController struct {
	deliveryMethodService delivery_method.DeliveryMethodInterface
}

var _ pb.LcosDeliveryInstructionServiceServer = (*deliveryInstructionController)(nil)

func NewDeliveryInstructionController(deliveryMethodService delivery_method.DeliveryMethodInterface) *deliveryInstructionController {
	return &deliveryInstructionController{
		deliveryMethodService: deliveryMethodService,
	}
}

//提供给lps的接口1-productid
func (c *deliveryInstructionController) BatchGetDeliveryInstructionByProductID(ctx context.Context, request *pb.BatchGetDeliveryInstructionByProductIDRequest) (*pb.DeliveryInstructionByProductIDResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	if len(request.GetProductId()) > delivery_constant.MaxCount {
		return &pb.DeliveryInstructionByProductIDResponse{
			RespHeader:                  http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "param over max"),
			DeliveryInstructionRespList: nil,
		}, nil
	}
	deliveryInstructionRespList, err := c.deliveryMethodService.GetDeliveryInstructionInfoByProductIdsInOnCache(lcosCtx, request.GetProductId())
	if err != nil {
		logger.CtxLogErrorf(lcosCtx, "cannot get deliveryInstructionRespList, err:[%s]", err.Msg)
		return &pb.DeliveryInstructionByProductIDResponse{
			RespHeader:                  http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			DeliveryInstructionRespList: nil,
		}, nil
	}
	return &pb.DeliveryInstructionByProductIDResponse{
		RespHeader:                  http.GrpcSuccessRespHeader(),
		DeliveryInstructionRespList: deliveryInstructionRespList,
	}, nil
}

//提供给lps的方法2-region
func (c *deliveryInstructionController) ListDeliveryInstructionByRegion(ctx context.Context, request *pb.ListDeliveryInstructionByRegionRequest) (*pb.DeliveryInstructionByRegionResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	if request.DeliveryInstructionReq.GetCount() > delivery_constant.MaxCount || request.DeliveryInstructionReq.GetPageno() > delivery_constant.MaxCount {
		return &pb.DeliveryInstructionByRegionResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "param over max"),
			DeliveryInstructionResp: &pb.DeliveryInstructionResp{
				PageNo:                  request.DeliveryInstructionReq.Pageno,
				Count:                   request.DeliveryInstructionReq.Count,
				Total:                   utils.NewUint32(0),
				DeliveryInstructionList: nil,
			},
		}, nil
	}
	total, availables, lcosErr := c.deliveryMethodService.GetDeliveryInstructionInfoByRegionInOnCache(lcosCtx, request.DeliveryInstructionReq.GetPageno(), request.DeliveryInstructionReq.GetCount(), strings.ToUpper(request.DeliveryInstructionReq.GetRegion()))
	if lcosErr != nil {
		return &pb.DeliveryInstructionByRegionResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcosErr.RetCode, lcosErr.Msg),
			DeliveryInstructionResp: &pb.DeliveryInstructionResp{
				PageNo:                  request.DeliveryInstructionReq.Pageno,
				Count:                   request.DeliveryInstructionReq.Count,
				Total:                   utils.NewUint32(0),
				DeliveryInstructionList: nil,
			},
		}, nil
	}
	result := &pb.DeliveryInstructionResp{
		PageNo:                  request.DeliveryInstructionReq.Pageno,
		Count:                   request.DeliveryInstructionReq.Count,
		Total:                   &total,
		DeliveryInstructionList: availables,
	}
	return &pb.DeliveryInstructionByRegionResponse{
		RespHeader:              http.GrpcSuccessRespHeader(),
		DeliveryInstructionResp: result,
	}, nil
}

//提供给wbc的方法-slstn
func (c *deliveryInstructionController) BatchGetDeliveryInstructionBySlsTn(ctx context.Context, request *pb.ListDeliveryInstructionBySlsTnRequest) (*pb.DeliveryInstructionBySlsTnResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	if len(request.DeliveryInstructionBySlstnReqList) > delivery_constant.MaxNum {
		return &pb.DeliveryInstructionBySlsTnResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.SchemaParamsErrorCode, "param over max"),
		}, nil
	}
	deliveryInstructionRespList := make([]*pb.SingleDeliveryInstructionBySlsTnResp, 0, len(request.GetDeliveryInstructionBySlstnReqList()))
	for _, requestInfo := range request.GetDeliveryInstructionBySlstnReqList() {
		availables, lcosErr := c.deliveryMethodService.GetDeliveryInstructionInfoByLineId(lcosCtx, requestInfo)
		if lcosErr != nil {
			deliveryInstructionRespList = append(deliveryInstructionRespList, &pb.SingleDeliveryInstructionBySlsTnResp{
				SlsTn:   utils.NewString(requestInfo.GetSlsTn()),
				LineId:  utils.NewString(requestInfo.GetLineId()),
				Retcode: utils.NewUint32(uint32(lcosErr.RetCode)),
				Message: utils.NewString(lcosErr.Msg),
			})
			continue
		}
		deliveryInstructionRespList = append(deliveryInstructionRespList, &pb.SingleDeliveryInstructionBySlsTnResp{
			SlsTn:                         utils.NewString(requestInfo.GetSlsTn()),
			LineId:                        utils.NewString(requestInfo.GetLineId()),
			AvailableDeliveryInstructions: availables,
			Retcode:                       utils.NewUint32(uint32(constant.SUCCESS)),
			Message:                       utils.NewString(""),
		})
	}

	return &pb.DeliveryInstructionBySlsTnResponse{
		RespHeader:                         http.GrpcSuccessRespHeader(),
		DeliveryInstructionBySlstnRespList: deliveryInstructionRespList,
	}, nil
}
