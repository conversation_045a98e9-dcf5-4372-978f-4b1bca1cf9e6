package branch

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/schema/address"
	station_protocol "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/gateway/router/admin_router/protocol/station"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/match"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/station"
	"github.com/gogo/protobuf/proto"
	"github.com/jinzhu/copier"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant/branch_constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/address_service"
	branchService "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/branch"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type branchController struct {
	pb.UnimplementedBranchServiceServer // branch和lcos共用远程pb库，branch新增接口在lcos不会实现

	branchService          branchService.BranchService
	stationService         station.StationService
	homeDeliveryServiceApi match.HomeDeliveryServiceApi
}

func NewBranchController(branchService branchService.BranchService, stationService station.StationService, homeDeliveryServiceApi match.HomeDeliveryServiceApi) *branchController {
	return &branchController{
		branchService:          branchService,
		stationService:         stationService,
		homeDeliveryServiceApi: homeDeliveryServiceApi,
	}
}

// @core
func (b *branchController) GetBranchListByBranchGroupID(ctx context.Context, in *pb.GetBranchListByBranchGroupIDRequest) (*pb.GetBranchListByBranchGroupIDResponse, error) {
	resp := &pb.GetBranchListByBranchGroupIDResponse{}
	lcosCtx := utils.NewCommonCtx(ctx)

	branchInfos, e := b.branchService.GetAllBranchesByBranchGroupID(lcosCtx, uint64(in.GetBranchGroupId()))
	if e != nil {
		logger.CtxLogErrorf(ctx, e.Msg)
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(e.RetCode, e.Msg)
		return resp, nil
	}
	resp.RespHeader = http.GrpcSuccessRespHeader()
	for _, branchInfo := range branchInfos {
		resp.BranchInfoList = append(resp.BranchInfoList, convertPbBranchInfo(branchInfo))
	}
	return resp, nil
}

// @core
func (b *branchController) GetSyncBranchSupplyType(ctx context.Context, in *pb.GetSyncBranchSupplyTypeRequest) (*pb.GetSyncBranchSupplyTypeResponse, error) {
	resp := &pb.GetSyncBranchSupplyTypeResponse{}
	lcosCtx := utils.NewCommonCtx(ctx)

	branchSupplyTypes, e := b.branchService.GetSyncBranchSupplyType(lcosCtx, in.GetRegion())
	if e != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(e.RetCode, e.Msg)
		return resp, nil
	}
	resp.RespHeader = http.GrpcSuccessRespHeader()
	resp.BranchSupplyType = branchSupplyTypes
	return resp, nil
}

// @core
func (b *branchController) SyncBranchInfo(ctx context.Context, in *pb.SyncBranchInfoRequest) (*pb.SyncBranchInfoResponse, error) {
	resp := &pb.SyncBranchInfoResponse{}
	lcosCtx := utils.NewCommonCtx(ctx)

	s3Config := config.GetConf(ctx).LCSS3Config
	if err := b.branchService.SyncBranchInfoFromChannel(lcosCtx, in.GetBranchSupplyType(), in.GetRegion(), in.GetRemoteFilePath(), in.ReqHeader.GetRequestId(), s3Config.Endpoint, s3Config.AccessKeyID, s3Config.BucketKey, false); err != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
		return resp, nil
	}
	resp.RespHeader = http.GrpcSuccessRespHeader()
	return resp, nil
}

// @core
func (b *branchController) GetBranchInfo(ctx context.Context, in *pb.GetBranchInfoRequest) (*pb.GetBranchInfoResponse, error) {
	resp := &pb.GetBranchInfoResponse{}
	lcosCtx := utils.NewCommonCtx(ctx)
	branchInfo, err := b.branchService.GetBranchFromCache(lcosCtx, in.GetBranchId(), in.GetBranchGroupId())
	if err != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
		return resp, nil
	}

	resp.BranchInfo = convertPbBranchInfo(branchInfo)
	resp.RespHeader = http.GrpcSuccessRespHeader()
	return resp, nil
}

// @core
func (b *branchController) BatchGetBranchInfoByBranchId(ctx context.Context, in *pb.BatchGetBranchInfoByBranchIdRequest) (*pb.BatchGetBranchInfoByBranchIdResponse, error) {
	resp := &pb.BatchGetBranchInfoByBranchIdResponse{}
	lcosCtx := utils.NewCommonCtx(ctx)
	out := b.branchService.BatchGetBranchInfoByBranchId(lcosCtx, in.GetBranchInfoList())
	resp.Data = out
	resp.RespHeader = http.GrpcSuccessRespHeader()
	return resp, nil
}

// @core
func (b *branchController) BatchGetBranchInfoByBranchRef(ctx context.Context, in *pb.BatchGetBranchInfoByBranchRefRequest) (*pb.BatchGetBranchInfoByBranchRefResponse, error) {
	resp := &pb.BatchGetBranchInfoByBranchRefResponse{}
	lcosCtx := utils.NewCommonCtx(ctx)
	out := b.branchService.BatchGetBranchInfoByBranchRef(lcosCtx, in.GetBranchInfoList())
	resp.Data = out
	resp.RespHeader = http.GrpcSuccessRespHeader()
	return resp, nil
}

// @core
func (b *branchController) GetBranchInfoByLocationID(ctx context.Context, in *pb.GetBranchInfoByLocationIDRequest) (*pb.GetBranchInfoByLocationIDResponse, error) {
	resp := &pb.GetBranchInfoByLocationIDResponse{}
	lcosCtx := utils.NewCommonCtx(ctx)
	logger.LogInfof("GetBranchInfoForSlsApi GetBranchGroupId==%d", in.GetBranchGroupId())
	branches, err := b.branchService.GetBranchByGroupIdAndLocationId(lcosCtx, in.GetBranchGroupId(), in.GetLocationId(), in.GetSubStatus())
	logger.LogInfof("GetBranchInfoForSlsApi branches cap==%d", len(branches))
	if err != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
		return resp, nil
	}
	branchList := make([]*pb.BranchInfo, 0, len(branches))
	for _, v := range branches {
		branchList = append(branchList, convertPbBranchInfo(v))
	}
	resp.Data = branchList
	resp.RespHeader = http.GrpcSuccessRespHeader()
	return resp, nil
}

// @core
func (b *branchController) GetBranchSubLocations(ctx context.Context, in *pb.GetBranchSubLocationsRequest) (*pb.GetBranchSubLocationsResponse, error) {
	resp := &pb.GetBranchSubLocationsResponse{}
	lcosCtx := utils.NewCommonCtx(ctx)
	locationIdMap, err := b.branchService.GetSubLocationsByGroupIdAndLocationId(lcosCtx, in.GetBranchGroupId(), in.GetLocationId())
	logger.LogInfof("GetSubLocationsByGroupIdAndLocationId==%s", locationIdMap)
	if err != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
		return resp, nil
	}
	resp.RespHeader = http.GrpcSuccessRespHeader()
	if locationIdMap != nil {
		stateLocationsId := locationIdMap["stateLocationsId"]
		cityLocationsId := locationIdMap["cityLocationsId"]
		districtLocationsId := locationIdMap["districtLocationsId"]
		streetLocationsId := locationIdMap["streetLocationsId"]
		resp.LocationsIdMap = &pb.LocationsIdMap{
			StateLocationsId:    stateLocationsId,
			CityLocationsId:     cityLocationsId,
			DistrictLocationsId: districtLocationsId,
			StreetLocationsId:   streetLocationsId,
		}
	}
	return resp, nil
}

// @core
func (b *branchController) SearchBranch(ctx context.Context, in *pb.SearchBranchRequest) (*pb.SearchBranchResponse, error) {
	resp := &pb.SearchBranchResponse{
		Distance: map[uint64]float64{},
	}
	lcosCtx := utils.NewCommonCtx(ctx)
	results, distance, err := b.branchService.FindBranch(lcosCtx, in)
	if err != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
		return resp, nil
	}
	for k, v := range results {
		resp.BranchInfo = append(resp.BranchInfo, convertPbBranchInfo(v))
		if len(distance) > 0 {
			resp.Distance[v.BranchID] = distance[k]
		}
	}
	resp.Count = utils.NewUint32(uint32(len(results)))
	resp.RespHeader = http.GrpcSuccessRespHeader()
	return resp, nil
}

// sort branch by distance, small distance go to the front, large distance go to the rear
func sortByDistance(branches []*pb.FullBranchInfo, distanceMap map[uint64]float64) {
	sort.SliceStable(branches, func(i, j int) bool {
		leftBranchID := branches[i].GetBranchId()
		rightBranchID := branches[j].GetBranchId()
		leftDistance, ok1 := distanceMap[leftBranchID]
		rightDistance, ok2 := distanceMap[rightBranchID]
		// branch with distance will go to front
		if ok1 && !ok2 {
			return true
		}
		if !ok1 && ok2 {
			return false
		}
		if !ok1 && !ok2 {
			return i < j
		}
		return leftDistance < rightDistance
	})
}

// @core
func (b *branchController) SearchBranchForGoogleMaps(ctx context.Context, request *pb.SearchBranchForGoogleMapsRequest) (*pb.SearchBranchForGoogleMapsResponse, error) {
	resp := &pb.SearchBranchForGoogleMapsResponse{
		Distance: map[uint64]float64{},
	}
	lcosCtx := utils.NewCommonCtx(ctx)

	var finalResults []*pb.FullBranchInfo
	var resultMap = make(map[uint64]bool) // used to deduplication
	var distanceMap = make(map[uint64]float64)

	maxSize := request.GetSize()
	if configedSize := config.GetMaxBranchSizeByRegion(ctx, request.GetRegion()); configedSize != 0 {
		if configedSize < maxSize {
			maxSize = configedSize
		}
	}

	for _, branchGroupID := range request.GetBranchGroupId() {

		// for循环遍历传入的branch group id
		in := &branchService.SearchBranchReq{
			BranchGroupID:    branchGroupID,
			Keyword:          request.GetKeyword(),
			FindType:         request.GetFindType(),
			Region:           request.GetRegion(),
			Size:             maxSize,
			LocationSortType: request.GetLocationSortType(),
			Longitude:        request.GetLongitude(),
			Latitude:         request.GetLatitude(),
			ZipCode:          request.GetZipcode(),
			Distance:         request.GetDistance(),
		}
		results, distance, err := b.branchService.FindBranchGeneral(lcosCtx, in)
		if err != nil {
			logger.CtxLogErrorf(ctx, err.Msg)
			resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
			return resp, nil
		}
		for k, v := range results {
			if _, ok := resultMap[v.BranchID]; !ok {
				if tmp := convertFullPbBranchInfo(ctx, v, branchGroupID); tmp != nil {
					finalResults = append(finalResults, tmp)
					if len(distance) > 0 {
						distanceMap[v.BranchID] = distance[k]
					}
					resultMap[v.BranchID] = true
				}
			}
		}
	}

	// sort by sort type, and set max return count to size
	if request.GetFindType() == branch_constant.SearchByDistance {
		sortByDistance(finalResults, distanceMap)
	}
	if int(maxSize) < len(finalResults) {
		finalResults = finalResults[:maxSize]
	}

	resp.RespHeader = http.GrpcSuccessRespHeader()
	resp.BranchInfo = finalResults
	resp.Distance = distanceMap
	resp.Count = utils.NewUint32(uint32(len(finalResults)))
	return resp, nil
}

func convertPbBranchInfo(branchInfo *branch_info.LogisticBranchInfoTab) *pb.BranchInfo {
	status := uint32(branchInfo.BranchStatus)
	b := &pb.BranchInfo{
		BranchId:              &branchInfo.BranchID,
		BranchName:            &branchInfo.BranchName,
		LocationId:            &branchInfo.LocationID,
		LocationDivisionId:    &branchInfo.LocationDivisionID,
		BranchType:            &branchInfo.BranchType,
		Postalcode:            &branchInfo.Postalcode,
		Longitude:             &branchInfo.Longitude,
		Latitude:              &branchInfo.Latitude,
		DetailAddress:         &branchInfo.DetailAddress,
		BranchRef:             &branchInfo.BranchRef,
		BranchStatus:          &status,
		SubDistrict:           &branchInfo.SubDistrict,
		MaxParcelStayDuration: &branchInfo.MaxParcelStayDuration,
		BranchPhone:           &branchInfo.BranchPhone,
		BranchInfoExtraData: &pb.BranchInfoExtraData{
			DCNAME: &branchInfo.ExtraData.DCNAME,
			DCCODE: &branchInfo.ExtraData.DCCODE,
		},
		BranchCode: &branchInfo.BranchCode,
		Region:     &branchInfo.Region,

		OpsType:             branchInfo.GetOpsType(),
		LocationDescription: &branchInfo.LocationDescription,
		SubStatus:           pb.SubStatusEnum(branchInfo.BranchSubStatus).Enum(),
	}

	openHours := make([]*pb.OpenHour, 0, len(branchInfo.OpeningHours))
	for _, v := range branchInfo.OpeningHours {
		dayOfWeek := make([]uint32, 0, len(v.DayOfWeek))
		for _, d := range v.DayOfWeek {
			dayOfWeek = append(dayOfWeek, uint32(d))
		}
		openHours = append(openHours, &pb.OpenHour{
			StartTime: &v.StartTime,
			EndTime:   &v.EndTime,
			TimeZone:  &v.TimeZone,
			DayOfWeek: dayOfWeek,
		})
	}
	b.OpenHour = openHours
	return b
}

func convertFullPbBranchInfo(ctx context.Context, branchInfo *branch_info.LogisticBranchInfoTab, branchGroupID uint32) *pb.FullBranchInfo {
	status := uint32(branchInfo.BranchStatus)
	b := &pb.FullBranchInfo{
		BranchId:              &branchInfo.BranchID,
		BranchName:            &branchInfo.BranchName,
		LocationId:            &branchInfo.LocationID,
		LocationDivisionId:    &branchInfo.LocationDivisionID,
		BranchType:            &branchInfo.BranchType,
		Postalcode:            &branchInfo.Postalcode,
		Longitude:             &branchInfo.Longitude,
		Latitude:              &branchInfo.Latitude,
		DetailAddress:         &branchInfo.DetailAddress,
		BranchRef:             &branchInfo.BranchRef,
		BranchStatus:          &status,
		SubDistrict:           &branchInfo.SubDistrict,
		MaxParcelStayDuration: &branchInfo.MaxParcelStayDuration,
		BranchPhone:           &branchInfo.BranchPhone,
		BranchInfoExtraData: &pb.BranchInfoExtraData{
			DCNAME: &branchInfo.ExtraData.DCNAME,
			DCCODE: &branchInfo.ExtraData.DCCODE,
		},
		BranchCode:    &branchInfo.BranchCode,
		BranchGroupId: utils.NewUint32(branchGroupID),
	}

	openHours := make([]*pb.OpenHour, 0, len(branchInfo.OpeningHours))
	for _, v := range branchInfo.OpeningHours {
		dayOfWeek := make([]uint32, 0, len(v.DayOfWeek))
		for _, d := range v.DayOfWeek {
			dayOfWeek = append(dayOfWeek, uint32(d))
		}
		openHours = append(openHours, &pb.OpenHour{
			StartTime: &v.StartTime,
			EndTime:   &v.EndTime,
			TimeZone:  &v.TimeZone,
			DayOfWeek: dayOfWeek,
		})
	}
	b.OpenHour = openHours

	// add location info
	locationInfo, lcosErr := address_service.LocationServer.GetLocationInfoById(ctx, int(branchInfo.LocationID))
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, lcosErr.Msg)
		return nil
	}
	b.State = utils.NewString(locationInfo.State)
	b.City = utils.NewString(locationInfo.City)
	b.District = utils.NewString(locationInfo.District)
	b.Street = utils.NewString(locationInfo.Street)
	return b
}

func (b *branchController) BatchSearchDropoffBranch(ctx context.Context, in *pb.BatchSearchDropoffBranchRequest) (*pb.BatchSearchDropoffBranchResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	batchResp := &pb.BatchSearchDropoffBranchResponse{
		RespHeader:      http.GrpcSuccessRespHeader(),
		BranchResultMap: map[string]*pb.SearchBranchResponse{},
	}

	for i := range in.SearchDropoffBranchRequest {
		request := in.SearchDropoffBranchRequest[i]
		resp := &pb.SearchBranchResponse{
			Distance: map[uint64]float64{},
		}
		results, distance, err := b.branchService.FindDropoffBranch(lcosCtx, request.SearchBranchRequest)
		if err != nil {
			resp.RespHeader = http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
			resp.RespHeader.RequestId = proto.String(request.GetUniqueId())
			batchResp.BranchResultMap[request.GetUniqueId()] = resp
			continue
		}
		for k, v := range results {
			resp.BranchInfo = append(resp.BranchInfo, convertPbBranchInfo(v))
			if len(distance) > 0 {
				resp.Distance[v.BranchID] = distance[k]
			}
		}
		resp.Count = utils.NewUint32(uint32(len(results)))
		resp.RespHeader = http.GrpcSuccessRespHeader()
		resp.RespHeader.RequestId = proto.String(request.GetUniqueId())
		batchResp.BranchResultMap[request.GetUniqueId()] = resp
	}
	return batchResp, nil
}

func (b *branchController) GetSpxStationIdInfoByOrderId(ctx context.Context, req *pb.GetSpxStationIdInfoByOrderIdRequest) (*pb.GetSpxStationIdInfoByOrderIdResponse, error) {
	if req == nil {
		return nil, errors.New("request is nil")
	}
	lcosCtx := utils.NewCommonCtx(ctx)
	getStationByOrderIdRequest := &address.GetStationByOrderIdRequest{
		OrderId:      req.GetOrderId(),
		AddressL1:    req.GetAddressL1(),
		AddressL2:    req.GetAddressL2(),
		Address:      req.GetAddress(),
		DeployRegion: utils.GetRegion(ctx),
	}
	rsp, lcosError := b.stationService.GetSpxStationInfoByOrderId(lcosCtx, getStationByOrderIdRequest)
	if lcosError != nil {
		rsp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcosError.RetCode, lcosError.Msg)
		return rsp, nil
	}
	return rsp, nil
}

func (b *branchController) CheckAddressListHomeDeliveryAbility(ctx context.Context, in *pb.CheckHDAbilityRequest) (*pb.CheckHDAbilityResponse, error) {
	if in == nil {
		return nil, errors.New("request is nil")
	}
	req := convertToCheckAbilityRequest(ctx, in)
	resp, err := b.stationService.CheckAddressListHomeDeliveryAbility(ctx, req)
	if err != nil {
		result := &pb.CheckHDAbilityResponse{}
		result.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.HDCheckAbilityErr, fmt.Sprintf("home delivery check ability err, err=%v", err))
		return result, nil
	}
	return convertToCheckAbilityResponse(ctx, resp), nil
}

func (b *branchController) GetHomeDeliveryStation(ctx context.Context, in *pb.GetHDStationRequest) (*pb.GetHDStationResponse, error) {
	if in == nil {
		return nil, errors.New("request is nil")
	}
	req := convertToGetHDStationRequest(ctx, in)
	resp, err := b.stationService.GetHomeDeliveryStation(ctx, req)
	if err != nil {
		result := &pb.GetHDStationResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.HDGetStationErr, fmt.Sprintf("home delivery get optimal station err, err=%v", err)),
		}
		return result, nil
	}
	return convertToGetHDStationResponse(ctx, resp), nil
}

func (b *branchController) GetDrivingDistanceWithHDStation(ctx context.Context, in *pb.GetDrivingDistanceWithHDStationRequest) (*pb.GetDrivingDistanceWithHDStationResponse, error) {
	var addressList []address.CommonAddress
	for _, addressInfo := range in.GetAddressList() {
		addressList = append(addressList, address.CommonAddress{
			Address:   addressInfo.GetAddress(),
			AddressL1: addressInfo.GetAddressL1(),
			AddressL2: addressInfo.GetAddressL2(),
			AddressL3: addressInfo.GetAddressL3(),
			AddressL4: addressInfo.GetAddressL4(),
			AddressL5: addressInfo.GetAddressL5(),
			AddressL6: addressInfo.GetAddressL6(),
			AddressL7: addressInfo.GetAddressL7(),
			AddressL8: addressInfo.GetAddressL8(),
			Zipcode:   addressInfo.GetZipcode(),
		})
	}
	resp, err := b.homeDeliveryServiceApi.GetDrivingDistanceWithHDStation(ctx, in.GetBuyerId(), addressList)
	if err != nil {
		result := &pb.GetDrivingDistanceWithHDStationResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.HDGetStationErr, fmt.Sprintf("home delivery get optimal station err, err=%v", err)),
		}
		return result, nil
	}
	var stationToAddressDrivingDistanceList []*pb.StationToAddressDrivingDistance
	for _, respItem := range resp {
		var stationToAddressDrivingDistanceItemList []*pb.StationToAddressDrivingDistanceItem
		for _, distanceItem := range respItem.StationList {
			stationToAddressDrivingDistanceItemList = append(stationToAddressDrivingDistanceItemList, &pb.StationToAddressDrivingDistanceItem{
				StationId:       proto.Int64(distanceItem.StationID),
				DrivingDistance: proto.Float32(float32(distanceItem.DrivingDistance)),
				Version:         proto.Uint64(distanceItem.Version),
			})
		}
		stationToAddressDrivingDistanceList = append(stationToAddressDrivingDistanceList, &pb.StationToAddressDrivingDistance{
			Address: &pb.CommonAddress{
				Address:   proto.String(respItem.Address.Address),
				AddressL1: proto.String(respItem.Address.AddressL1),
				AddressL2: proto.String(respItem.Address.AddressL2),
				AddressL3: proto.String(respItem.Address.AddressL3),
				AddressL4: proto.String(respItem.Address.AddressL4),
				AddressL5: proto.String(respItem.Address.AddressL5),
				AddressL6: proto.String(respItem.Address.AddressL6),
				AddressL7: proto.String(respItem.Address.AddressL7),
				AddressL8: proto.String(respItem.Address.AddressL8),
				Zipcode:   proto.String(respItem.Address.Zipcode),
			},
			Coordinate: &pb.CommonCoordinate{
				Lat: proto.Float32(float32(respItem.Coordinate.Lat)),
				Lng: proto.Float32(float32(respItem.Coordinate.Lng)),
			},
			IsAddressNotExist:                       proto.Bool(respItem.IsAddressNotExist),
			StationToAddressDrivingDistanceItemList: stationToAddressDrivingDistanceItemList,
		})
	}
	return &pb.GetDrivingDistanceWithHDStationResponse{
		RespHeader:                          http.GrpcSuccessRespHeader(),
		StationToAddressDrivingDistanceList: stationToAddressDrivingDistanceList,
	}, nil
}

func (b *branchController) GetBuyerAddressCoordinate(ctx context.Context, in *pb.GetBuyerAddressCoordinateRequest) (*pb.GetBuyerAddressCoordinateResponse, error) {
	var addressList []address.CommonAddress
	for _, addressInfo := range in.GetAddressList() {
		addressList = append(addressList, address.CommonAddress{
			Address:   addressInfo.GetAddress(),
			AddressL1: addressInfo.GetAddressL1(),
			AddressL2: addressInfo.GetAddressL2(),
			AddressL3: addressInfo.GetAddressL3(),
			AddressL4: addressInfo.GetAddressL4(),
			AddressL5: addressInfo.GetAddressL5(),
			AddressL6: addressInfo.GetAddressL6(),
			AddressL7: addressInfo.GetAddressL7(),
			AddressL8: addressInfo.GetAddressL8(),
			Zipcode:   addressInfo.GetZipcode(),
		})
	}
	resp, err := b.homeDeliveryServiceApi.GetBuyerAddressCoordinate(ctx, in.GetBuyerId(), addressList)
	if err != nil {
		result := &pb.GetBuyerAddressCoordinateResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.HDGetStationErr, fmt.Sprintf("home delivery get optimal station err, err=%v", err)),
		}
		return result, nil
	}
	var addressCoordinateList []*pb.AddressCoordinate
	for _, respItem := range resp {
		addressCoordinateList = append(addressCoordinateList, &pb.AddressCoordinate{
			Address: &pb.CommonAddress{
				Address:   proto.String(respItem.Address.Address),
				AddressL1: proto.String(respItem.Address.AddressL1),
				AddressL2: proto.String(respItem.Address.AddressL2),
				AddressL3: proto.String(respItem.Address.AddressL3),
				AddressL4: proto.String(respItem.Address.AddressL4),
				AddressL5: proto.String(respItem.Address.AddressL5),
				AddressL6: proto.String(respItem.Address.AddressL6),
				AddressL7: proto.String(respItem.Address.AddressL7),
				AddressL8: proto.String(respItem.Address.AddressL8),
				Zipcode:   proto.String(respItem.Address.Zipcode),
			},
			Coordinate: &pb.CommonCoordinate{
				Lat: proto.Float32(float32(respItem.Coordinate.Lat)),
				Lng: proto.Float32(float32(respItem.Coordinate.Lng)),
			},
			StandardAddress: proto.String(respItem.StandardAddress),
		})
	}
	return &pb.GetBuyerAddressCoordinateResponse{
		RespHeader:            http.GrpcSuccessRespHeader(),
		AddressCoordinateList: addressCoordinateList,
	}, nil
}

func (b *branchController) SearchNearbyStation(ctx context.Context, in *pb.SearchNearbyStationRequest) (*pb.SearchNearbyStationResponse, error) {
	var centerList []address.CenterPointWithRadius
	for _, centerPointWithRadius := range in.GetCenterPointWithRadiusList() {
		centerList = append(centerList, address.CenterPointWithRadius{
			CenterPoint: address.CommonCoordinate{
				Lat: float64(centerPointWithRadius.GetCenterPoint().GetLat()),
				Lng: float64(centerPointWithRadius.GetCenterPoint().GetLng()),
			},
			Radius: float64(centerPointWithRadius.GetRadius()),
			Address: address.CommonAddress{
				Address:   centerPointWithRadius.GetAddress().GetAddress(),
				AddressL1: centerPointWithRadius.GetAddress().GetAddressL1(),
				AddressL2: centerPointWithRadius.GetAddress().GetAddressL2(),
				AddressL3: centerPointWithRadius.GetAddress().GetAddressL3(),
				AddressL4: centerPointWithRadius.GetAddress().GetAddressL4(),
				AddressL5: centerPointWithRadius.GetAddress().GetAddressL5(),
				AddressL6: centerPointWithRadius.GetAddress().GetAddressL6(),
				AddressL7: centerPointWithRadius.GetAddress().GetAddressL7(),
				AddressL8: centerPointWithRadius.GetAddress().GetAddressL8(),
				Zipcode:   centerPointWithRadius.GetAddress().GetZipcode(),
			},
		})
	}
	resp, err := b.homeDeliveryServiceApi.SearchNearbyStation(ctx, centerList)
	if err != nil {
		result := &pb.SearchNearbyStationResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.HDGetStationErr, fmt.Sprintf("home delivery get optimal station err, err=%v", err)),
		}
		return result, nil
	}
	var nearbyStationList []*pb.NearbyStation
	for _, respItem := range resp {
		var stationList []*pb.StationCoordinate
		for _, stationCoordinate := range respItem.StationList {
			stationList = append(stationList, &pb.StationCoordinate{
				StationId: proto.Int64(stationCoordinate.StationID),
				Coordinate: &pb.CommonCoordinate{
					Lat: proto.Float32(float32(stationCoordinate.Coordinate.Lat)),
					Lng: proto.Float32(float32(stationCoordinate.Coordinate.Lng)),
				},
			})
		}
		nearbyStationList = append(nearbyStationList, &pb.NearbyStation{
			CenterPoint: &pb.CommonCoordinate{
				Lat: proto.Float32(float32(respItem.CenterPoint.Lat)),
				Lng: proto.Float32(float32(respItem.CenterPoint.Lng)),
			},
			Radius: proto.Float32(float32(respItem.Radius)),
			Address: &pb.CommonAddress{
				Address:   proto.String(respItem.Address.Address),
				AddressL1: proto.String(respItem.Address.AddressL1),
				AddressL2: proto.String(respItem.Address.AddressL2),
				AddressL3: proto.String(respItem.Address.AddressL3),
				AddressL4: proto.String(respItem.Address.AddressL4),
				AddressL5: proto.String(respItem.Address.AddressL5),
				AddressL6: proto.String(respItem.Address.AddressL6),
				AddressL7: proto.String(respItem.Address.AddressL7),
				AddressL8: proto.String(respItem.Address.AddressL8),
				Zipcode:   proto.String(respItem.Address.Zipcode),
			},
			StationList: stationList,
		})
	}
	return &pb.SearchNearbyStationResponse{
		RespHeader:        http.GrpcSuccessRespHeader(),
		NearbyStationList: nearbyStationList,
	}, nil
}

func (b *branchController) GetDrivingDistanceFromMatrix(ctx context.Context, in *pb.GetDrivingDistanceFromMatrixRequest) (*pb.GetDrivingDistanceFromMatrixResponse, error) {
	var startingPointList []address.CommonCoordinate
	var terminatePointList []address.CommonCoordinate
	for _, startPoint := range in.GetStartingPointList() {
		startingPointList = append(startingPointList, address.CommonCoordinate{
			Lat: float64(startPoint.GetLat()),
			Lng: float64(startPoint.GetLng()),
		})
	}
	for _, endPoint := range in.GetTerminatePointList() {
		terminatePointList = append(terminatePointList, address.CommonCoordinate{
			Lat: float64(endPoint.GetLat()),
			Lng: float64(endPoint.GetLng()),
		})
	}
	resp, err := b.homeDeliveryServiceApi.GetDrivingDistanceFromMatrix(ctx, startingPointList, terminatePointList)
	if err != nil {
		result := &pb.GetDrivingDistanceFromMatrixResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(lcos_error.HDGetStationErr, fmt.Sprintf("home delivery get optimal station err, err=%v", err)),
		}
		return result, nil
	}
	var commonMatrixDistanceList []*pb.CommonMatrixDistance
	for _, respItem := range resp {
		commonMatrixDistanceList = append(commonMatrixDistanceList, &pb.CommonMatrixDistance{
			StartPoint: &pb.CommonCoordinate{
				Lat: proto.Float32(float32(respItem.StartPoint.Lat)),
				Lng: proto.Float32(float32(respItem.StartPoint.Lng)),
			},
			EndPoint: &pb.CommonCoordinate{
				Lat: proto.Float32(float32(respItem.EndPoint.Lat)),
				Lng: proto.Float32(float32(respItem.EndPoint.Lng)),
			},
			IsNoResult: proto.Bool(respItem.IsNoResult),
			Distance:   proto.Float32(float32(respItem.Distance)),
		})
	}
	return &pb.GetDrivingDistanceFromMatrixResponse{
		RespHeader:               http.GrpcSuccessRespHeader(),
		CommonMatrixDistanceList: commonMatrixDistanceList,
	}, nil
}

func convertToCheckAbilityRequest(ctx context.Context, in *pb.CheckHDAbilityRequest) address.GetHdAbilityRequest {
	var addrList []address.GetHdAbilityAddressItem
	for _, addressItem := range in.GetCheckHdAbilityAddressItems() {
		addrList = append(addrList, address.GetHdAbilityAddressItem{
			Address:   addressItem.GetAddress(),
			AddressL1: addressItem.GetAddressL1(),
			AddressL2: addressItem.GetAddressL2(),
			UniqueKey: addressItem.GetUniqueKey(),
		})
	}
	req := address.GetHdAbilityRequest{
		BuyerId:  in.GetBuyerId(),
		AddrList: addrList,
	}
	return req
}

func convertToCheckAbilityResponse(ctx context.Context, resp address.GetHdAbilityResponse) *pb.CheckHDAbilityResponse {
	var resItemList []*pb.CheckHDAbilityAddressResItem
	for _, addr := range resp.AddrList {
		resItem := &pb.CheckHDAbilityAddressResItem{
			Address:     proto.String(addr.Address),
			AddressL1:   proto.String(addr.AddressL1),
			AddressL2:   proto.String(addr.AddressL2),
			UniqueKey:   proto.String(addr.UniqueKey),
			IsSupportHd: proto.Int32(int32(addr.IsSupportHd)),
			ReturnType:  proto.String(addr.ReturnType),
		}
		resItemList = append(resItemList, resItem)
	}
	result := &pb.CheckHDAbilityResponse{
		RespHeader:                   http.GrpcSuccessRespHeader(),
		BuyerId:                      proto.String(resp.BuyerId),
		CheckHdAbilityAddressResItem: resItemList,
	}
	return result
}

func convertToGetHDStationRequest(ctx context.Context, in *pb.GetHDStationRequest) address.GetHomeDeliveryStationRequest {
	req := address.GetHomeDeliveryStationRequest{}
	addressItem := in.GetGetHdStationAddressItem()
	if addressItem == nil {
		return req
	}
	req.AddressItem = address.AddressItem{
		AddressL1:  addressItem.GetAddressL1(),
		AddressL2:  addressItem.GetAddressL2(),
		Address:    addressItem.GetAddress(),
		ShipmentId: addressItem.GetShipmentId(),
	}
	return req
}

func convertToGetHDStationResponse(ctx context.Context, resp address.GetHomeDeliveryStationResponse) *pb.GetHDStationResponse {
	result := &pb.GetHDStationResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
		GetHdStationAddressResItem: &pb.GetHDStationAddressResItem{
			AddressL1:  proto.String(resp.AddressItem.AddressL1),
			AddressL2:  proto.String(resp.AddressItem.AddressL2),
			Address:    proto.String(resp.AddressItem.Address),
			StationId:  proto.Uint64(resp.AddressItem.StationId),
			AddressLng: proto.Float32(float32(resp.AddressItem.AddressLng)),
			AddressLat: proto.Float32(float32(resp.AddressItem.AddressLat)),
		},
	}
	return result
}

func (b *branchController) GetCacheVal(ctx context.Context, info *pb.QueryCacheValReq) (*pb.QueryCacheValResp, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	var req station_protocol.QueryCacheValReq
	var resp = pb.QueryCacheValResp{
		RespHeader: http.GrpcSuccessRespHeader(),
	}
	_ = copier.Copy(&req, info)
	data, lErr := b.stationService.GetCacheVal(lcosCtx, &req)
	if lErr != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(lErr.RetCode, lErr.Msg)
		return &resp, nil
	}
	_ = copier.Copy(&resp.List, data)
	return &resp, nil
}

func (b *branchController) GetCoordinateByAddress(ctx context.Context, req *pb.GetCoordinateByAddressRequest) (*pb.GetCoordinateByAddressResponse, error) {
	resp := &pb.GetCoordinateByAddressResponse{}
	if req == nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.GetCoordinateErr, "request is nil")
		return resp, nil
	}
	coordinateReq := address.GetCoordinateByAddressRequest{
		Address:   req.GetAddress(),
		AddressL1: req.GetAddressL1(),
		AddressL2: req.GetAddressL2(),
	}
	result, err := b.homeDeliveryServiceApi.GetCoordinateByAddress(ctx, coordinateReq)
	if err != nil {
		resp.RespHeader = http.GrpcErrorRespHeaderWithParam(lcos_error.GetCoordinateErr, fmt.Sprintf("get coordinate by address error, err:%v", err))
		return resp, nil
	}
	resp.RespHeader = http.GrpcSuccessRespHeader()
	resp.Longitude = proto.String(result.Longitude)
	resp.Latitude = proto.String(result.Latitude)

	return resp, nil
}
