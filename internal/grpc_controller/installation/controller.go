package installation

import (
	"context"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/installation"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type InstallationController struct {
	installationService installation.InstallationDateInterface
}

func (d InstallationController) BatchGetInstallationDate(ctx context.Context, request *pb.BatchGetInstallationDataRequest) (*pb.InstallationDateResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	res, err := d.installationService.BatchGetInstallationDate(lcosCtx, request)
	if err != nil {
		return &pb.InstallationDateResponse{RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)}, nil
	}
	return &pb.InstallationDateResponse{RespHeader: http.GrpcSuccessRespHeader(), DateItems: res.DateItems}, nil
}

var _ pb.LcosInstallationServiceServer = (*InstallationController)(nil)

func NewInstallationController(installation installation.InstallationDateInterface) *InstallationController {
	return &InstallationController{
		installationService: installation,
	}
}
