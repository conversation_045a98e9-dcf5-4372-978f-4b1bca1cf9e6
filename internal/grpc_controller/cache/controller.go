package cache_api

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	lcos_protobuf "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type cacheApiService struct{}

func CacheApiService() *cacheApiService {
	return &cacheApiService{}
}

func (h *cacheApiService) CleanCache(ctx context.Context, req *lcos_protobuf.CacheApiRequest) (*lcos_protobuf.SuccessBody, error) {
	namespace := req.Namespace
	logger.LogInfof("clean cache message:%s\n", &namespace)
	manager, err := localcache.GetLocalCacheManager()
	if err != nil {
		errMsg := err.Error()
		return &lcos_protobuf.SuccessBody{
			Result: &errMsg,
		}, nil
	}
	err = manager.CleanByNamespace(*namespace)
	msg := "success"
	if err != nil {
		msg = err.Error()
		return &lcos_protobuf.SuccessBody{
			Result: &msg,
		}, nil
	}
	return &lcos_protobuf.SuccessBody{
		Result: &msg,
	}, nil
}

func (h *cacheApiService) GetCache(ctx context.Context, req *lcos_protobuf.CacheApiRequest) (*lcos_protobuf.SuccessBody, error) {
	namespace := req.Namespace
	logger.LogInfof("clean cache message:%s\n", &namespace)
	manager, err := localcache.GetLocalCacheManager()
	if err != nil {
		errMsg := err.Error()
		return &lcos_protobuf.SuccessBody{
			Result: &errMsg,
		}, nil
	}
	res, err := manager.ListByNamespace(*namespace)
	if err != nil {
		errMsg := err.Error()
		return &lcos_protobuf.SuccessBody{
			Result: &errMsg,
		}, nil
	}
	return &lcos_protobuf.SuccessBody{
		Result: &res,
	}, nil
}

func (h *cacheApiService) RefreshLocalCache(ctx context.Context, req *lcos_protobuf.CacheApiRequest) (*lcos_protobuf.SuccessBody, error) {
	namespace := req.Namespace
	logger.LogInfof("refresh cache message:%s\n", &namespace)
	manager, err := localcache.GetLocalCacheManager()
	if err != nil {
		errMsg := err.Error()
		return &lcos_protobuf.SuccessBody{
			Result: &errMsg,
		}, nil
	}
	manager.SetLocalCacheVersion(*namespace, 0)

	res := "ok"
	return &lcos_protobuf.SuccessBody{
		Result: &res,
	}, nil
}
