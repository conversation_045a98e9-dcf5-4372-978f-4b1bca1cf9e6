package scene_serviceable_area

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/pathutil"
	cf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_postcode"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic"
	siteservice "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/startup"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"os"
	"path"
	"reflect"
	"strings"
	"testing"
)

func Test_grpcSceneServiceableAreaController_getLaneAreaServiceableV2(t *testing.T) {
	ctx := context.Background()
	_ = os.Setenv("GOLANG_PROTOBUF_REGISTRATION_CONFLICT", "warn ./main")
	_ = os.Setenv("ENV", "TEST")
	_ = os.Setenv("SSC_ENV", strings.ToLower(cf.GetEnv(ctx)))
	_ = os.Setenv("CHASSIS_CONF_DIR", path.Join(pathutil.GetProjectAbsolutePath(), "conf/chassis_conf/grpc"))

	err := chassis.Init()
	if err != nil {
		t.Fatalf(err.Error())
	}

	c, err := cf.InitConfig(ctx)
	if err != nil {
		t.Fatalf("getConfig %v", err)
	}

	_, err = cf.InitMutableConfig()
	if err != nil {
		t.Fatalf("Init Mutable config Error:%v", err)
	}

	if err := startup.InitLibs(c); err != nil {
		t.Fatalf("InitLibs Error: %v", err)
	}
	if err := cache.InitLocalCache(); err != nil {
		t.Fatalf("InitLocalcache Error: %v", err)
	}
	if err := localcache.LoadLocalCache(); err != nil {
		t.Fatalf("loadLocalcache Error: %v", err)
	}
	type fields struct {
		serviceableCheckerService  service.ServiceableCheckerServiceInterface
		siteServiceableAreaService siteservice.SiteServiceableAreaInterface
	}
	type args struct {
		ctx      utils.LCOSContext
		laneInfo *lfs_service.LaneCodeStruct
		req      *pb.LaneAreaServiceabilityReq
		header   *pb.ReqHeader
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *pb.LaneAreaServiceable
		want1  *lcos_error.LCOSError
	}{
		// TODO: Add test cases.
		{
			name: "check 1",
			fields: fields{
				serviceableCheckerService: service.NewServiceableCheckerService(basic_conf.NewLineBasicServiceableConfDAO(), basic_location.NewLineBasicServiceableLocationDAO(), basic_postcode.NewLineBasicServiceablePostcodeDAO(), scenario_conf.NewLineCommonServiceableScenarioConfDAO(), operation_conf.NewLogisticLineOperationServiceableConfTabDAO(), operation_location.NewLogisticLineOperationServiceableLocationTabDAO(), operation_postcode.NewLogisticLineOperationServiceablePostcodeTabDAO(), route.NewLogisticLineServiceableRouteTabDAO(), area_location_ref.NewLogisticLineServiceableAreaLocationRefTabDAO(), collect_deliver_group.NewLineCollectDeliverGroupConfDao(), collect_deliver_group_ref.NewLineBaseServiceableGroupRefDao(), cep_range.NewLineServiceableCepRangeDAO(), operation_route.NewLineOperationRouteTabDAO(), effective_rule.NewEffectiveRuleDAO()),
				siteServiceableAreaService: siteservice.NewSiteServiceableAreaService(site_serviceable_area_basic_conf.NewSiteServiceableAreaBasicConfDAO(), site_serviceable_area_location.NewSiteServiceableAreaLocationDAO(),
					site_serviceable_area_postcode.NewSiteServiceableAreaPostcodeDAO(), site_serviceable_area_cep_range.NewSiteServiceableAreaCepRangeDAO()),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				laneInfo: &lfs_service.LaneCodeStruct{
					LaneCode:       "L-BR-120",
					LaneFormatType: "6",
					Composes: []*lfs_service.SingleCompose{
						{
							ResourceID:   "SBR2",
							ResourceName: "BR Local Buyer",
							ResourceType: 1,
							MainType:     2,
							SubType:      7,
							Sequence:     5,
						},
						{
							ResourceID:   "LBR17",
							ResourceName: "D-BR-Total-Standard-LM",
							ResourceType: 2,
							MainType:     2,
							SubType:      128,
							Sequence:     4,
						},
						{
							ResourceID:   "LBR45",
							ResourceName: "D-BR-PUDO-Pegaki-Standard-FM",
							ResourceType: 2,
							MainType:     2,
							SubType:      64,
							Sequence:     2,
						},
						{
							ResourceID:   "SBR1",
							ResourceName: "BR Local Seller",
							ResourceType: 1,
							MainType:     1,
							SubType:      2,
							Sequence:     1,
						},
						{
							ResourceID:   "SBR25",
							ResourceName: "BR_SPX_XD_SOC",
							ResourceType: 1,
							MainType:     3,
							SubType:      8,
							Sequence:     3,
						},
					},
					ModelRules: []*lfs_service.SingleModelRule{
						{
							ResourceType:               1,
							MainType:                   2,
							SubType:                    7,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   5,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
						{
							ResourceType:               2,
							MainType:                   2,
							SubType:                    128,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   4,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
						{
							ResourceType:               1,
							MainType:                   3,
							SubType:                    8,
							SortingAsPreDeliverFlag:    1,
							SortingAsNextPickupFlag:    1,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    1,
							SortingFlag:                1,
							Sequence:                   3,
							SiteSortingPickupAddrFlag:  2,
							SiteSortingDeliverAddrFlag: 2,
						},
						{
							ResourceType:               2,
							MainType:                   2,
							SubType:                    64,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   2,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
						{
							ResourceType:               1,
							MainType:                   1,
							SubType:                    2,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   1,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
					},
				},
				req: &pb.LaneAreaServiceabilityReq{
					LaneCodes: []string{"L-BR-120"},
					Region:    utils.NewString("BR"),
					Basis: &pb.ServiceableBasis{
						CheckOperation:     utils.NewUint32(1),
						CheckBasic:         utils.NewUint32(1),
						CollectType:        pb.CollectTypeEnum_DROP_OFF_COLLECT.Enum(),
						DeliverType:        pb.DeliveryTypeEnum_TO_HOME.Enum(),
						CheckPostalCode:    utils.NewUint32(1),
						PaymentMethod:      pb.PaymentMethodEnum_STANDARD.Enum(),
						SenderCheckLevel:   pb.LocationCheckLevelEnum_DISTRICT.Enum(),
						ReceiverCheckLevel: pb.LocationCheckLevelEnum_DISTRICT.Enum(),
						CheckSender:        utils.NewUint32(1),
						CheckReceiver:      utils.NewUint32(1),
					},
					PickupAddr: &pb.ServiceableAddress{
						Region:     utils.NewString("BR"),
						PostalCode: utils.NewString("2225011"),
					},
					DeliverAddr: &pb.ServiceableAddress{
						Region:     utils.NewString("BR"),
						PostalCode: utils.NewString("2226005"),
					},
				},
			},
		},
		{
			name: "check 2",
			fields: fields{
				serviceableCheckerService: service.NewServiceableCheckerService(basic_conf.NewLineBasicServiceableConfDAO(), basic_location.NewLineBasicServiceableLocationDAO(), basic_postcode.NewLineBasicServiceablePostcodeDAO(), scenario_conf.NewLineCommonServiceableScenarioConfDAO(), operation_conf.NewLogisticLineOperationServiceableConfTabDAO(), operation_location.NewLogisticLineOperationServiceableLocationTabDAO(), operation_postcode.NewLogisticLineOperationServiceablePostcodeTabDAO(), route.NewLogisticLineServiceableRouteTabDAO(), area_location_ref.NewLogisticLineServiceableAreaLocationRefTabDAO(), collect_deliver_group.NewLineCollectDeliverGroupConfDao(), collect_deliver_group_ref.NewLineBaseServiceableGroupRefDao(), cep_range.NewLineServiceableCepRangeDAO(), operation_route.NewLineOperationRouteTabDAO(), effective_rule.NewEffectiveRuleDAO()),
				siteServiceableAreaService: siteservice.NewSiteServiceableAreaService(site_serviceable_area_basic_conf.NewSiteServiceableAreaBasicConfDAO(), site_serviceable_area_location.NewSiteServiceableAreaLocationDAO(),
					site_serviceable_area_postcode.NewSiteServiceableAreaPostcodeDAO(), site_serviceable_area_cep_range.NewSiteServiceableAreaCepRangeDAO()),
			},
			args: args{
				ctx: utils.NewCommonCtx(context.Background()),
				laneInfo: &lfs_service.LaneCodeStruct{
					LaneCode:       "I-CN-MY-252/CL-MY-44",
					LaneFormatType: "",
					Composes: []*lfs_service.SingleCompose{
						{
							ResourceID:   "SMY14",
							ResourceName: "MY_CB_SELF_COLLECTION_buyer",
							ResourceType: 1,
							MainType:     2,
							SubType:      6,
							Sequence:     7,
						},
						{
							ResourceID:   "LMY53",
							ResourceName: "SPX_BSC",
							ResourceType: 2,
							MainType:     1,
							SubType:      2,
							Sequence:     6,
						},
						{
							ResourceID:   "SMY5",
							ResourceName: "MY HO Group",
							ResourceType: 1,
							MainType:     3,
							SubType:      4,
							Sequence:     5,
						},
						{
							ResourceID:   "LMY28",
							ResourceName: "MY_CB_Vinflair",
							ResourceType: 2,
							MainType:     1,
							SubType:      1,
							Sequence:     4,
						},
						{
							ResourceID:   "SCN78",
							ResourceName: "TWS Group-HK",
							ResourceType: 1,
							MainType:     3,
							SubType:      3,
							Sequence:     3,
						},
						{
							ResourceID:   "FM01",
							ResourceName: "Dummy",
							ResourceType: 2,
							MainType:     1,
							SubType:      4,
							Sequence:     2,
						},
						{
							ResourceID:   "SCN2367",
							ResourceName: "测试CN发货点-LFS",
							ResourceType: 1,
							MainType:     1,
							SubType:      2,
							Sequence:     1,
						},
					},
					ModelRules: []*lfs_service.SingleModelRule{
						{
							ResourceType:               1,
							MainType:                   2,
							SubType:                    6,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   7,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
						{
							ResourceType:               2,
							MainType:                   1,
							SubType:                    2,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   6,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
						{
							ResourceType:               1,
							MainType:                   3,
							SubType:                    4,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    1,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                1,
							Sequence:                   5,
							SiteSortingPickupAddrFlag:  2,
							SiteSortingDeliverAddrFlag: 1,
						},
						{
							ResourceType:               2,
							MainType:                   1,
							SubType:                    1,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   4,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
						{
							ResourceType:               1,
							MainType:                   3,
							SubType:                    3,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   3,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
						{
							ResourceType:               2,
							MainType:                   1,
							SubType:                    4,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   2,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
						{
							ResourceType:               1,
							MainType:                   1,
							SubType:                    2,
							SortingAsPreDeliverFlag:    0,
							SortingAsNextPickupFlag:    0,
							PreResourceSACheckFlag:     0,
							NextResourceSACheckFlag:    0,
							SortingFlag:                0,
							Sequence:                   1,
							SiteSortingPickupAddrFlag:  0,
							SiteSortingDeliverAddrFlag: 0,
						},
					},
				},
				req: &pb.LaneAreaServiceabilityReq{
					LaneCodes: []string{"I-CN-MY-252/CL-MY-44"},
					Region:    utils.NewString("MY"),
					Basis: &pb.ServiceableBasis{
						CheckOperation:     utils.NewUint32(1),
						CheckBasic:         utils.NewUint32(1),
						CollectType:        pb.CollectTypeEnum_DROP_OFF_COLLECT.Enum(),
						DeliverType:        pb.DeliveryTypeEnum_TO_HOME.Enum(),
						CheckPostalCode:    utils.NewUint32(1),
						PaymentMethod:      pb.PaymentMethodEnum_STANDARD.Enum(),
						SenderCheckLevel:   pb.LocationCheckLevelEnum_DISTRICT.Enum(),
						ReceiverCheckLevel: pb.LocationCheckLevelEnum_DISTRICT.Enum(),
						CheckSender:        utils.NewUint32(1),
						CheckReceiver:      utils.NewUint32(1),
					},
					PickupAddr: &pb.ServiceableAddress{
						Region:          utils.NewString("MY"),
						StateLocationId: utils.NewUint32(1000113),
						PostalCode:      utils.NewString("2225011"),
					},
					DeliverAddr: &pb.ServiceableAddress{
						Region:          utils.NewString("MY"),
						StateLocationId: utils.NewUint32(1000001),
						PostalCode:      utils.NewString("2226005"),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &grpcSceneServiceableAreaController{
				serviceableCheckerService:  tt.fields.serviceableCheckerService,
				siteServiceableAreaService: tt.fields.siteServiceableAreaService,
			}
			got, got1 := c.getLaneAreaServiceableV2(tt.args.ctx, tt.args.laneInfo, tt.args.req, tt.args.header)
			fmt.Println(got)
			if reflect.DeepEqual(got, tt.want) {
				t.Errorf("grpcSceneServiceableAreaController.getLaneAreaServiceableV2() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("grpcSceneServiceableAreaController.getLaneAreaServiceableV2() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
