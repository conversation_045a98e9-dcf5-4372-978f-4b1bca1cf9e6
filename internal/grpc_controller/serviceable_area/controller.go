package serviceable_area

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/e_fence"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_conf"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

type grpcServiceableAreaController struct {
	pb.UnimplementedLcosServiceableServiceServer

	serviceableCheckerService service.ServiceableCheckerServiceInterface
	basicConfService          basic_conf.LineBasicServiceableConfServiceInterface
	eFenceService             e_fence.EFenceServiceInterface
	pb.LcosServiceableServiceServer
}

func NewGrpcServiceableAreaController(serviceableCheckerService service.ServiceableCheckerServiceInterface, basicConfService basic_conf.LineBasicServiceableConfServiceInterface, zoneService e_fence.EFenceServiceInterface) *grpcServiceableAreaController {
	return &grpcServiceableAreaController{
		serviceableCheckerService: serviceableCheckerService,
		basicConfService:          basicConfService,
		eFenceService:             zoneService,
	}
}

// Deprecated
// Please use GetLineServiceableArea2 instead of GetLineServiceableArea. cause scenario params will not exists
func (c *grpcServiceableAreaController) GetLineServiceableArea(ctx context.Context, req *pb.GetLineServiceableInfoRequest) (*pb.GetLineServiceableInfoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	serviceableInfo, err := c.serviceableCheckerService.GetServiceable(lcosCtx, req)
	if err != nil {
		return &pb.GetLineServiceableInfoResponse{
			RespHeader:      http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			ServiceableInfo: service.GenAllFailedServiceable(),
		}, nil
	}
	return &pb.GetLineServiceableInfoResponse{
		RespHeader:      http.GrpcSuccessRespHeader(),
		ServiceableInfo: serviceableInfo,
	}, nil
}

// @core
func (c *grpcServiceableAreaController) GetLineServiceableArea2(ctx context.Context, req *pb.GetLineServiceableInfoRequest2) (*pb.GetLineServiceableInfoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	serviceableInfo, _, err := c.serviceableCheckerService.GetServiceableWithCheckFlag(lcosCtx, req)
	if err != nil {
		_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(err.RetCode)), "line": req.BaseInfo.GetLineId()})
		return &pb.GetLineServiceableInfoResponse{
			RespHeader:      http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
			ServiceableInfo: service.GenAllFailedServiceable(),
		}, nil
	}
	_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(0), "line": req.BaseInfo.GetLineId()})
	return &pb.GetLineServiceableInfoResponse{
		RespHeader:      http.GrpcSuccessRespHeader(),
		ServiceableInfo: serviceableInfo,
	}, nil
}

// Deprecated
// 1. 当前接口任然使用的是场景配置的参数，2.不够规范的coding，业务逻辑应该尽量下沉到service层
// 请使用BatchGetLineServiceableArea2代替BatchGetLineServiceableArea
func (c *grpcServiceableAreaController) BatchGetLineServiceableArea(ctx context.Context, req *pb.BatchGetLineServiceableInfoRequest) (*pb.BatchGetLineServiceableInfoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	serviceableRespList := make([]*pb.SingleGetServiceableResponse, 0, len(req.ServiceableReqList))
	for _, singleRequest := range req.ServiceableReqList {
		serviceableInfo, err := c.serviceableCheckerService.GetServiceable(lcosCtx, &pb.GetLineServiceableInfoRequest{
			ReqHeader:   nil,
			BaseInfo:    singleRequest.BaseInfo,
			PickupInfo:  singleRequest.PickupInfo,
			DeliverInfo: singleRequest.DeliverInfo,
		})
		if err != nil {
			serviceableRespList = append(serviceableRespList, &pb.SingleGetServiceableResponse{
				LineId:          singleRequest.BaseInfo.LineId,
				ServiceableInfo: service.GenAllFailedServiceable(),
			})
		} else {
			serviceableRespList = append(serviceableRespList, &pb.SingleGetServiceableResponse{
				LineId:          singleRequest.BaseInfo.LineId,
				ServiceableInfo: serviceableInfo,
			})
		}
	}
	return &pb.BatchGetLineServiceableInfoResponse{
		RespHeader:          http.GrpcSuccessRespHeader(),
		ServiceableRespList: serviceableRespList,
	}, nil
}

// 批量获取线的服务范围
// @core
func (c *grpcServiceableAreaController) BatchGetLineServiceableArea2(ctx context.Context, req *pb.BatchGetLineServiceableInfoRequest2) (*pb.BatchGetLineServiceableInfoResponse2, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	batchServiceable, err := c.serviceableCheckerService.BatchGetServiceable(lcosCtx, req)
	if err != nil {
		return &pb.BatchGetLineServiceableInfoResponse2{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}
	return batchServiceable, nil
}

// @core
func (c *grpcServiceableAreaController) GetLineCollectDeliverAbility(ctx context.Context, req *pb.GetLineCollectDeliverAbilityRequest) (*pb.GetLineCollectDeliverAbilityResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	lineAbility, err := c.basicConfService.GetLineCollectDeliverAbility(lcosCtx, req)
	if err != nil {
		return &pb.GetLineCollectDeliverAbilityResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	return lineAbility, nil
}

// @core
func (c *grpcServiceableAreaController) BatchGetLineCollectDeliverAbility(ctx context.Context, req *pb.BatchGetLineCollectDeliverAbilityRequest) (*pb.BatchGetLineCollectDeliverAbilityResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	lineAbilities, err := c.basicConfService.BatchGetLineCollectDeliverAbility(lcosCtx, req)
	if err != nil {
		return &pb.BatchGetLineCollectDeliverAbilityResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	return lineAbilities, nil
}

// Deprecated
// 不在使用场景配置的参数，请使用CheckLineServiceableArea2接口
func (c *grpcServiceableAreaController) CheckLineServiceableArea(ctx context.Context, req *pb.CheckLineServiceableAreaRequest) (*pb.CheckLineServiceableAreaResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	res, _ := c.serviceableCheckerService.CheckServiceable(lcosCtx, req)
	return res, nil
}

// @core
func (c *grpcServiceableAreaController) CheckLineServiceableArea2(ctx context.Context, req *pb.CheckLineServiceableAreaRequest2) (*pb.CheckLineServiceableAreaResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	res, err := c.serviceableCheckerService.CheckServiceable2(lcosCtx, req)
	if err != nil {
		_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(err.RetCode)), "line": req.BaseInfo.GetLineId()})
		return &pb.CheckLineServiceableAreaResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}
	_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(*res.RespHeader.Retcode)), "line": req.BaseInfo.GetLineId()})
	return res, nil
}

// @core
func (c *grpcServiceableAreaController) BatchCheckLineServiceableArea(ctx context.Context, req *pb.BatchCheckLineServiceableAreaRequest) (*pb.BatchCheckLineServiceableAreaResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	res, err := c.serviceableCheckerService.BatchCheckServiceable(lcosCtx, req)
	if err != nil {
		return &pb.BatchCheckLineServiceableAreaResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}
	return res, nil
}

// @core
func (c *grpcServiceableAreaController) MultipleBatchCheckLineServiceableArea(ctx context.Context, req *pb.MultipleBatchCheckLineServiceableAreaRequest) (*pb.MultipleBatchCheckLineServiceableAreaResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	res, err := c.serviceableCheckerService.MultipleBatchCheckLineServiceableArea(lcosCtx, req)
	if err != nil {
		errorRespHeader := http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg)
		errorRespHeader.RequestId = req.ReqHeader.RequestId
		return &pb.MultipleBatchCheckLineServiceableAreaResponse{
			RespHeader: errorRespHeader,
		}, nil
	}
	return res, nil
}

// @core
func (c *grpcServiceableAreaController) CheckFmServiceableArea(ctx context.Context, req *pb.CheckFmServiceableAreaRequest) (*pb.CheckFmServiceableAreaResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	res, err := c.serviceableCheckerService.CheckFmServiceableArea(lcosCtx, req)
	if err != nil {
		_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(err.RetCode)), "line": req.GetLineId()})
		return &pb.CheckFmServiceableAreaResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}
	_ = metrics.CounterIncr(constant.MetricLineStatus, map[string]string{"url": lcosCtx.GetUrl(), "rsp_status": strconv.Itoa(int(*res.RespHeader.Retcode)), "line": req.GetLineId()})
	return res, nil
}

// @core
func (c *grpcServiceableAreaController) BatchCheckLineServiceableArea2(ctx context.Context, req *pb.BatchCheckLineServiceableAreaRequest2) (*pb.BatchCheckLineServiceableAreaResponse2, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	res, err := c.serviceableCheckerService.BatchCheckLineServiceableArea2(lcosCtx, req)
	if err != nil {
		return &pb.BatchCheckLineServiceableAreaResponse2{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	return res, nil
}

// @core
func (c *grpcServiceableAreaController) BatchGetLaneServiceableRule(ctx context.Context, req *pb.BatchGetLaneServiceableRuleRequest) (*pb.BatchGetLaneServiceableRuleResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	rules, err := c.serviceableCheckerService.GetLaneServiceableRuleList(lcosCtx, req.LaneCode)
	if err != nil {
		return &pb.BatchGetLaneServiceableRuleResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	return &pb.BatchGetLaneServiceableRuleResponse{
		RespHeader:          http.GrpcSuccessRespHeader(),
		LaneServiceableRule: rules,
	}, nil
}

func (c *grpcServiceableAreaController) NotifyZoneInfo(ctx context.Context, req *pb.NotifyZoneInfoRequest) (*pb.NotifyZoneInfoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	err := c.eFenceService.ReceiveZoneInfo(lcosCtx, req)
	if err != nil {
		return &pb.NotifyZoneInfoResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	return &pb.NotifyZoneInfoResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

func (c *grpcServiceableAreaController) RefreshZoneInfo(ctx context.Context, req *pb.RefreshZoneInfoRequest) (*pb.RefreshZoneInfoResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	err := c.eFenceService.RefreshSingleZoneInfo(lcosCtx, req)
	if err != nil {
		return &pb.RefreshZoneInfoResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	return &pb.RefreshZoneInfoResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}

func (c *grpcServiceableAreaController) RegenerateMesh(ctx context.Context, req *pb.RegenerateMeshRequest) (*pb.RegenerateMeshResponse, error) {
	lcosCtx := utils.NewCommonCtx(ctx)
	err := c.eFenceService.RegenerateMesh(lcosCtx, req.GetRegion(), req.GetZoneId(), req.GetVersion(), req.GetLayerId(), int(req.GetPipMode()), req.GetRefreshCache())
	if err != nil {
		return &pb.RegenerateMeshResponse{
			RespHeader: http.GrpcErrorRespHeaderWithParam(err.RetCode, err.Msg),
		}, nil
	}

	return &pb.RegenerateMeshResponse{
		RespHeader: http.GrpcSuccessRespHeader(),
	}, nil
}
