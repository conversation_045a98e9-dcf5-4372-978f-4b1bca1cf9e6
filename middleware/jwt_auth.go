package middleware

import (
	"context"
	"encoding/gob"
	"fmt"
	"net"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	chassisRestful "git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/http"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
	"github.com/dgrijalva/jwt-go"
	"github.com/emicklei/go-restful"
)

const (
	HttpHeaderRequestId = "request-id"
	HttpHeaderOperator  = "optr"
	HttpHeaderJwtToken  = "jwt-token"
	RequestIdMaxLength  = 64
	RequestIdMinLength  = 10
	HeaderRequestIP     = "X-FORWARDED-FOR"
)

const (
	JwtKey                    = "jwt_key"
	UrlPrefix                 = "/lcos/admin/"
	UrlPrefixForSha256WithRSA = "/lcos/admin/api/"
)

type CustomClaimsExample struct {
	Data Data `json:"data"`
	*jwt.StandardClaims
}

type Data struct {
	Info Info `json:"info"`
}

type Info struct {
	Entity Entity `json:"entity"`
	User   User   `json:"user"`
}

type Entity struct {
	Name     string `json:"name"`
	Country  string `json:"country"`
	Timezone int    `json:"timezone"`
}

type User struct {
	Name     string `json:"name"`
	Email    string `json:"email"`
	Level    int    `json:"level"`
	Category int    `json:"category"`
}

func RegisterJwt() {
	_ = handler.RegisterHandler(JwtKey, NewJwtHandle)
}

type JwtHandle struct {
}

func init() {
	gob.Register(map[constant.ContextKey]interface{}{})
}
func (j *JwtHandle) Handle(i *invocation.Invocation) {
	if strings.ToLower(utils.GetEnv(i.Ctx)) == "local" {
		i.Next()
		return
	}

	var (
		reqId      string
		ok         bool
		req        *restful.Request
		resp       *restful.Response
		restfulCtx *chassisRestful.Context
		ctx        context.Context
		valid      bool
		err        error
	)

	// check request id
	req, ok = i.Args.(*restful.Request)
	if !ok {
		i.Next()
		return
	}
	// 只适用于 /lcos/admin 开头的接口
	url := req.Request.URL.Path
	index := strings.Index(url, UrlPrefix)
	if index != 0 {
		i.Next()
		return
	}

	if resp, ok = i.Reply.(*restful.Response); !ok {
		i.Next()
		return
	}

	ctx = i.Ctx
	restfulCtx = chassisRestful.NewBaseServer(i.Ctx, req, resp)
	reqId = req.Request.Header.Get(HttpHeaderRequestId)
	logger.LogDebugf("reqid:%s", reqId)
	if reqId == "" {
		msg := "invalid request: no request id"
		http.GenerateErrorResponse(restfulCtx, lcos_error.NewRetcode(lcos_error.NotFoundRequestIdErrorCode, msg), nil)
		return
	}
	if len(reqId) < RequestIdMinLength {
		msg := "invalid request: request-id length should be great than or equal 10"
		http.GenerateErrorResponse(restfulCtx, lcos_error.NewRetcode(lcos_error.InvalidRequestIdErrorCode, msg), nil)
		return
	}

	// check jwt
	jwtToken := req.Request.Header.Get(HttpHeaderJwtToken)
	if jwtToken == "" {
		errMsg := fmt.Sprintf("receive request with empty token from %s", GetRequestIp(req))
		logger.LogErrorf(errMsg)
		http.GenerateErrorResponse(restfulCtx, lcos_error.NewRetcode(lcos_error.NotFoundJwtDataErrorCode, errMsg), nil)
		return
	}

	if index := strings.Index(url, UrlPrefixForSha256WithRSA); index == 0 {
		valid, err = jwtRsaCheck(restfulCtx, ctx, jwtToken)
	} else if index := strings.Index(url, UrlPrefix); index == 0 {
		valid, ctx, err = jwtCheck(restfulCtx, ctx, jwtToken)
	}

	i.Ctx = ctx
	if !valid {
		msg := fmt.Sprintf("invalid token: %v", err)
		logger.LogErrorf(msg)
		http.GenerateErrorResponse(restfulCtx, lcos_error.NewRetcode(lcos_error.InvalidAccountErrorCode, msg), nil)
		return
	}

	i.Next()
}

func GetRequestIp(req *restful.Request) string {
	requestIp := req.HeaderParameter(HeaderRequestIP)
	if requestIp == "" {
		ip, _, err := net.SplitHostPort(req.Request.RemoteAddr)
		if err == nil {
			requestIp = ip
		}
	}
	return requestIp
}

func (j *JwtHandle) Name() string {
	return RecoveryKey
}

func NewJwtHandle() handler.Handler {
	return &JwtHandle{}
}

func jwtCheck(restfulCtx *chassisRestful.Context, ctx context.Context, jwtToken string) (bool, context.Context, error) {
	var (
		optr    interface{}
		optrStr string
	)
	jwtParse := func(ctx context.Context, t string) (bool, map[constant.ContextKey]interface{}, error) {
		ctxKey := map[constant.ContextKey]interface{}{}
		token, err := jwt.Parse(jwtToken, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			var ok bool
			// check optr
			if optr, ok = token.Header[HttpHeaderOperator]; !ok {
				return nil, fmt.Errorf("token error: unexpected sender")
			}
			optrStr, ok = optr.(string)
			if !ok {
				return nil, fmt.Errorf("token error: optr should be string")
			}

			secret, ok := config.GetValidToken(ctx, optrStr)
			if !ok {
				return nil, fmt.Errorf("token error: unexpected sender: %v", optr)
			}
			// put data to context
			if item, ok := token.Claims.(jwt.MapClaims)["info"]; ok {
				ctxKey[constant.JwtInfoKey] = item
				if entity, ok := item.(map[string]interface{})["entity"]; ok {
					if region, ok := entity.(map[string]interface{})["country"]; ok {
						// xx stands for global
						ctxKey[constant.CountryKey] = region
					}
				}
				if user, ok := item.(map[string]interface{})["user"]; ok {
					ctxKey[constant.UserNameKey] = user.(map[string]interface{})["name"]
				}
				if entity, ok := item.(map[string]interface{})["user"]; ok {
					if email, ok := entity.(map[string]interface{})["email"]; ok {
						ctxKey[constant.UserEmailKey] = email
					}
				}
			}
			return []byte(secret), nil
		})
		if err != nil {
			http.GenerateErrorResponse(restfulCtx, lcos_error.NewRetcode(lcos_error.JwtDecodeErrorCode, err.Error()), nil)
			return false, ctxKey, err
		}
		return token.Valid, ctxKey, nil
	}
	var jwtParseWrapper = recorder.Wrap(jwtParse).(func(ctx context.Context, jwtToken string) (bool, map[constant.ContextKey]interface{}, error))
	valid, ctxKey, err := jwtParseWrapper(ctx, jwtToken)
	for k, v := range ctxKey {
		ctx = context.WithValue(ctx, k, v)
	}

	return valid, ctx, err
}

func jwtRsaCheck(restfulCtx *chassisRestful.Context, ctx context.Context, jwtToken string) (bool, error) {
	var (
		optr    interface{}
		optrStr string
	)

	jwtParse := func(ctx context.Context, t string) (bool, error) {
		claim := &CustomClaimsExample{StandardClaims: &jwt.StandardClaims{}}
		token, err := jwt.ParseWithClaims(jwtToken, claim, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			var ok bool
			// check optr
			if optr, ok = token.Header[HttpHeaderOperator]; !ok {
				return nil, fmt.Errorf("token error: unexpected sender")
			}
			optrStr, ok = optr.(string)
			if !ok {
				return nil, fmt.Errorf("token error: optr should be string")
			}
			secret, ok := config.GetValidToken(ctx, optrStr)
			if !ok {
				return nil, fmt.Errorf("token error: unexpected sender: %v", optr)
			}
			verifyKey, err := jwt.ParseRSAPublicKeyFromPEM([]byte(secret))
			if err != nil {
				return nil, fmt.Errorf("get verify key error: %v", verifyKey)
			}

			return verifyKey, nil
		})
		if err != nil {
			http.GenerateErrorResponse(restfulCtx, lcos_error.NewRetcode(lcos_error.JwtDecodeErrorCode, err.Error()), nil)
			return false, err
		}

		return token.Valid, nil
	}

	var jwtParseWrapper = recorder.Wrap(jwtParse).(func(ctx context.Context, jwtToken string) (bool, error))
	valid, err := jwtParseWrapper(ctx, jwtToken)

	return valid, err
}
