package middleware

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"github.com/emicklei/go-restful"
)

const (
	LogRequestKey = "log_request_key"
)

func RegisterLogRequest() {
	_ = handler.RegisterHandler(LogRequestKey, NewLogRequestHandle)
}

type LogRequestHandle struct {
}

func (j *LogRequestHandle) Name() string {
	return LogRequestKey
}

func NewLogRequestHandle() handler.Handler {
	return &LogRequestHandle{}
}

func (j *LogRequestHandle) Handle(i *invocation.Invocation) {
	var (
		ok   bool
		req  *restful.Request
		resp *restful.Response
	)
	req, ok = i.Args.(*restful.Request)
	if !ok {
		i.Next()
		return
	}
	if resp, ok = i.Reply.(*restful.Response); !ok {
		i.Next()
		return
	}

	start := utils.Now(i.Ctx)
	path := req.Request.URL.Path
	raw := req.Request.URL.RawQuery
	i.Next()

	end := utils.Now(i.Ctx)
	latency := end.Sub(start)
	clientIP := GetRequestIp(req)
	method := req.Request.Method
	statusCode := resp.StatusCode()
	responseBody := utils.GetResponse(i.Ctx)
	requestBody := utils.GetRequest(i.Ctx)
	requestId := utils.GetRequestId(i.Ctx)

	logger.LogInfof("[LogRequest]%v|%d|%s|%v|%s|%-7s|%s|%s|%+v|%s",
		end.Format("2006/01/02 - 15:04:05"),
		statusCode,
		requestId,
		latency,
		clientIP,
		method,
		path,
		raw,
		requestBody,
		responseBody,
	)
}
