package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"github.com/emicklei/go-restful"
	uuid "github.com/satori/go.uuid"
	"io/ioutil"
	"net/http"
)

const (
	LoggerKey = "logger_key"
)

func RegisterLogger() {
	_ = handler.RegisterHandler(LoggerKey, NewLoggerHandle)
}

type LoggerHandle struct {
}

func (j *LoggerHandle) Name() string {
	return LoggerKey
}

func NewLoggerHandle() handler.Handler {
	return &LoggerHandle{}
}

func (j *LoggerHandle) Handle(i *invocation.Invocation) {
	var (
		ok   bool
		req  *restful.Request
		resp *restful.Response
	)
	req, ok = i.Args.(*restful.Request)
	if !ok {
		i.Next()
		return
	}
	if resp, ok = i.Reply.(*restful.Response); !ok {
		i.Next()
		return
	}
	xRequestID := getRequestID(req)
	i.Ctx = logger.NewLogContext(i.Ctx, xRequestID)
	logger.SetLogId(xRequestID)
	defer logger.UnsetLogId()

	_, body, err := getRequestBody(req)
	request := compactBody(body)
	c := NewResponseCapture(resp.ResponseWriter)
	resp.ResponseWriter = c
	tmpCtx := i.Ctx
	tmpCtx = context.WithValue(tmpCtx, constant.RequestKey, request)
	tmpCtx = context.WithValue(tmpCtx, constant.RequestIdKey, xRequestID)
	tmpCtx = logger.NewLogContext(tmpCtx, xRequestID)
	i.Ctx = tmpCtx

	i.Next()

	response := compactBody(c.Bytes())
	if err != nil {
		logger.CtxLogErrorf(i.Ctx, "middleware logger, getRequestBody error: %v", err)
	}
	// 必须放在 i.Next() 之后
	tmpCtx = context.WithValue(tmpCtx, constant.ResponseKey, response)
	i.Ctx = tmpCtx
}

type ResponseCapture struct {
	http.ResponseWriter
	wroteHeader bool
	status      int
	body        *bytes.Buffer
}

func NewResponseCapture(w http.ResponseWriter) *ResponseCapture {
	return &ResponseCapture{
		ResponseWriter: w,
		wroteHeader:    false,
		body:           new(bytes.Buffer),
	}
}

func (c ResponseCapture) Header() http.Header {
	return c.ResponseWriter.Header()
}

func (c ResponseCapture) Write(data []byte) (int, error) {
	if !c.wroteHeader {
		c.WriteHeader(http.StatusOK)
	}
	c.body.Write(data)
	return c.ResponseWriter.Write(data)
}

func (c *ResponseCapture) WriteHeader(statusCode int) {
	c.status = statusCode
	c.wroteHeader = true
	c.ResponseWriter.WriteHeader(statusCode)
}

func (c ResponseCapture) Bytes() []byte {
	return c.body.Bytes()
}

func (c ResponseCapture) StatusCode() int {
	return c.status
}

func compactBody(body []byte) string {
	buf := new(bytes.Buffer)
	if err := json.Compact(buf, body); err != nil {
		return string(body)
	}
	return buf.String()
}

func getRequestBody(req *restful.Request) (*restful.Request, []byte, error) {
	r := req.Request
	body, bodyErr := ioutil.ReadAll(r.Body)
	if bodyErr != nil {
		return req, nil, bodyErr
	}

	dup := make([]byte, len(body))
	copy(dup, body)
	r.Body = ioutil.NopCloser(bytes.NewBuffer(body))
	return req, dup, nil
}

// 使用chassis Gin改造
func getRequestID(req *restful.Request) string {
	xRequestID := req.Request.Header.Get("X-Request-ID")
	if len(xRequestID) == 0 {
		xRequestID = uuid.NewV4().String() // nolint
	}
	return fmt.Sprintf("|%s", xRequestID)
}
