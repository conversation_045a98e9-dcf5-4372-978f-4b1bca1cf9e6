package middleware

import (
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"github.com/emicklei/go-restful"
	"time"
)

const (
	RecoveryKey = "recovery_key"
)

func RegisterRecovery() {
	_ = handler.RegisterHandler(RecoveryKey, NewRecoveryHandle)
}

type RecoveryHandle struct {
}

func (j *RecoveryHandle) Handle(i *invocation.Invocation) {
	defer func() {
		if err := recover(); err != nil {
			req, ok := i.Args.(*restful.Request)
			if !ok {
				return
			}
			_ = monitor.ReportEvent(constant.CatModuleURL, req.Request.URL.Path, constant.StatusPanic, fmt.Sprint(err))
			stack := utils.Stack(3)
			logger.LogErrorf("[Recovery] %s panic recovered:\n%s\n%s", timeFormat(utils.Now(i.Ctx)), err, stack)
			i.Abort(invocation.StatusAborted, errors.New("[Recovery] panic"))
			return
		}
	}()
	i.Next()
}

func (j *RecoveryHandle) Name() string {
	return RecoveryKey
}

func NewRecoveryHandle() handler.Handler {
	return &RecoveryHandle{}
}

func timeFormat(t time.Time) string {
	var timeString = t.Format("2006/01/02 - 15:04:05")
	return timeString
}
