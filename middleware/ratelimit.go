package middleware

import (
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"github.com/emicklei/go-restful"
	"golang.org/x/time/rate"
	"strings"
	"sync"
	"time"
)

// for concurrency safe
var rateMap = sync.Map{}
var defaultLimitConfig = rate.NewLimiter(rate.Every(10*time.Millisecond), 100)

const (
	RateLimitKey = "rate_limit_key"
)

func RegisterRateLimit() {
	_ = handler.RegisterHandler(RateLimitKey, NewRateLimitHandle)
}

type RateLimitHandle struct {
}

func (j *RateLimitHandle) Name() string {
	return RateLimitKey
}

func NewRateLimitHandle() handler.Handler {
	return &RateLimitHandle{}
}

func init() {
	// add your config here
	//rateMap.Store("/admin/logistic_line_extra_info/update/", rate.NewLimiter(rate.Every(10*time.Millisecond), 100))
}

func (j *RateLimitHandle) Handle(i *invocation.Invocation) {
	var (
		ok  bool
		req *restful.Request
	)
	if req, ok = i.Args.(*restful.Request); !ok {
		return
	}
	requestPath := req.Request.URL.Path
	// 只有 /locs/admin 做为前缀的域名需要开启限流
	if strings.Index(requestPath, UrlPrefix) != 0 {
		i.Next()
		return
	}
	v, _ := rateMap.LoadOrStore(requestPath, defaultLimitConfig)
	limiter := v.(*rate.Limiter)
	// reach maximum
	if !limiter.Allow() {
		msg := fmt.Sprintf("URI [%s] has already reached maximum frequency", requestPath)
		logger.LogInfof(msg)
		i.Abort(invocation.StatusAborted, errors.New("requests are too frequent. Please do it later"))
		return
	}
	i.Next()
}
