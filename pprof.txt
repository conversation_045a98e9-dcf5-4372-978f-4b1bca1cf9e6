File: grpc_server
Build ID: 459f638858f12437ec600b26bc8a454e66d55074
Type: cpu
Time: Aug 8, 2025 at 3:31pm (CST)
Duration: 30.19s, Total samples = 120.78s (400.10%)
Showing nodes accounting for 95.22s, 78.84% of 120.78s total
Dropped 892 nodes (cum <= 0.60s)
      flat  flat%   sum%        cum   cum%
     9.33s  7.72%  7.72%     10.98s  9.09%  runtime.findObject
     8.69s  7.19% 14.92%     22.62s 18.73%  runtime.mallocgc
     5.54s  4.59% 19.51%     30.02s 24.86%  runtime.scanobject
     4.78s  3.96% 23.46%      4.78s  3.96%  runtime.(*mspan).base (inline)
     3.51s  2.91% 26.37%      3.57s  2.96%  runtime.(*gcBits).bitp (inline)
     3.49s  2.89% 29.26%      3.49s  2.89%  runtime.futex
     3.09s  2.56% 31.82%      4.86s  4.02%  runtime.mapaccess2_faststr
     2.82s  2.33% 34.15%      2.82s  2.33%  runtime/internal/syscall.Syscall6
     2.74s  2.27% 36.42%      2.74s  2.27%  runtime.nextFreeFast (inline)
     2.69s  2.23% 38.65%      2.72s  2.25%  runtime.(*mspan).heapBitsSmallForAddr
     2.51s  2.08% 40.73%      3.41s  2.82%  runtime.step
     2.46s  2.04% 42.76%      6.69s  5.54%  runtime.pcvalue
     2.03s  1.68% 44.44%      3.05s  2.53%  runtime.(*mspan).writeHeapBitsSmall
     1.65s  1.37% 45.81%      1.65s  1.37%  runtime.memmove
     1.58s  1.31% 47.12%      1.58s  1.31%  runtime.arenaIndex (inline)
     1.36s  1.13% 48.24%      7.20s  5.96%  google.golang.org/protobuf/internal/impl.(*MessageInfo).sizePointerSlow
     1.28s  1.06% 49.30%      7.54s  6.24%  runtime.adjustframe
     1.28s  1.06% 50.36%      3.03s  2.51%  runtime.mapaccess2
     1.22s  1.01% 51.37%      1.22s  1.01%  aeshashbody
     1.22s  1.01% 52.38%     10.02s  8.30%  runtime.greyobject
     1.19s  0.99% 53.37%      1.19s  0.99%  runtime.memclrNoHeapPointers
     1.17s  0.97% 54.34%      1.17s  0.97%  memeqbody
     1.13s  0.94% 55.27%      1.13s  0.94%  runtime.acquirem (inline)
     1.05s  0.87% 56.14%      1.19s  0.99%  runtime.adjustpointers
     0.90s  0.75% 56.89%     16.22s 13.43%  runtime.newobject (partial-inline)
     0.90s  0.75% 57.63%      0.90s  0.75%  runtime.readvarint (inline)
     0.85s   0.7% 58.34%      0.85s   0.7%  runtime.releasem (inline)
     0.78s  0.65% 58.98%      0.78s  0.65%  google.golang.org/protobuf/encoding/protowire.SizeVarint (inline)
     0.75s  0.62% 59.60%      1.32s  1.09%  runtime.deductAssistCredit
     0.75s  0.62% 60.23%      0.81s  0.67%  runtime.findfunc
     0.73s   0.6% 60.83%      0.73s   0.6%  runtime.heapBitsSlice (inline)
     0.72s   0.6% 61.43%      5.51s  4.56%  google.golang.org/protobuf/internal/impl.(*MessageInfo).unmarshalPointer
     0.64s  0.53% 61.96%     30.54s 25.29%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).GetServiceableWithCheckFlag
     0.64s  0.53% 62.49%      0.64s  0.53%  runtime.(*moduledata).textAddr
     0.60s   0.5% 62.98%      1.30s  1.08%  runtime.mapaccess1_faststr
     0.58s  0.48% 63.46%      4.73s  3.92%  runtime.(*stkframe).getStackMap
     0.49s  0.41% 63.87%      0.63s  0.52%  context.value
     0.47s  0.39% 64.26%      0.64s  0.53%  runtime.spanOf (inline)
     0.45s  0.37% 64.63%      7.41s  6.14%  google.golang.org/protobuf/internal/impl.asMessage
     0.45s  0.37% 65.00%      2.97s  2.46%  runtime.growslice
     0.43s  0.36% 65.36%      0.97s   0.8%  strconv.formatBits
     0.37s  0.31% 65.66%      2.41s  2.00%  strings.(*Builder).WriteString (inline)
     0.35s  0.29% 65.95%      3.26s  2.70%  runtime.(*unwinder).resolveInternal
     0.35s  0.29% 66.24%     30.57s 25.31%  runtime.gcDrain
     0.34s  0.28% 66.53%      3.39s  2.81%  runtime.heapSetType
     0.34s  0.28% 66.81%      0.93s  0.77%  runtime.pageIndexOf (inline)
     0.34s  0.28% 67.09%      1.14s  0.94%  runtime.stealWork
     0.34s  0.28% 67.37%      2.92s  2.42%  sync.(*Map).Load
     0.33s  0.27% 67.64%      2.76s  2.29%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.convertEffectiveRuleToLaneRule
     0.31s  0.26% 67.90%     51.72s 42.82%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area.(*grpcSceneServiceableAreaController).BatchCheckProductServiceableForItemScene.func1
     0.31s  0.26% 68.16%      4.59s  3.80%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).checkLocationServiceableRouteV2
     0.30s  0.25% 68.41%      3.15s  2.61%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area.(*grpcSceneServiceableAreaController).BatchCheckProductServiceableForItemScene
     0.30s  0.25% 68.65%      7.05s  5.84%  google.golang.org/protobuf/proto.MarshalOptions.size
     0.30s  0.25% 68.90%      1.15s  0.95%  runtime.sweepone
     0.29s  0.24% 69.14%      1.33s  1.10%  google.golang.org/protobuf/internal/impl.(*messageReflectWrapper).Interface
     0.29s  0.24% 69.38%      0.76s  0.63%  reflect.valueInterface
     0.28s  0.23% 69.61%      6.61s  5.47%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache.(*Manager).Get
     0.28s  0.23% 69.85%     10.82s  8.96%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common.genNormalKey
     0.27s  0.22% 70.07%      5.11s  4.23%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache.(*Manager).get (inline)
     0.26s  0.22% 70.28%      1.70s  1.41%  reflect.(*rtype).ptrTo
     0.25s  0.21% 70.49%      9.03s  7.48%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area.(*grpcSceneServiceableAreaController).getResourcesWithCheckFlag
     0.25s  0.21% 70.70%      3.46s  2.86%  runtime.(*unwinder).next
     0.24s   0.2% 70.90%     10.42s  8.63%  google.golang.org/protobuf/proto.MarshalOptions.marshal
     0.23s  0.19% 71.09%      3.18s  2.63%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route.(*logisticLineServiceableRouteTabDAO).CheckLineServiceableRouteExistUseCache
     0.23s  0.19% 71.28%      7.20s  5.96%  google.golang.org/protobuf/internal/impl.(*MessageInfo).marshalAppendPointer
     0.23s  0.19% 71.47%      0.87s  0.72%  runtime.funcInfo.entry (inline)
     0.22s  0.18% 71.65%      5.04s  4.17%  runtime.findRunnable
     0.21s  0.17% 71.82%      1.23s  1.02%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common.GenKey
     0.21s  0.17% 72.00%      7.12s  5.90%  google.golang.org/protobuf/proto.MarshalOptions.Size
     0.21s  0.17% 72.17%      1.45s  1.20%  runtime.mapassign_faststr
     0.20s  0.17% 72.34%      1.36s  1.13%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).GetCollectDeliverGroupIdByCollectDeliverType
     0.19s  0.16% 72.50%      0.96s  0.79%  fmt.(*pp).doPrintf
     0.19s  0.16% 72.65%      0.72s   0.6%  fmt.(*pp).printArg
     0.19s  0.16% 72.81%      2.89s  2.39%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area.(*grpcSceneServiceableAreaController).generateLineServiceableAreaReq
     0.19s  0.16% 72.97%      2.98s  2.47%  google.golang.org/protobuf/internal/impl.(*MessageInfo).MessageOf
     0.18s  0.15% 73.12%     48.73s 40.35%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area.(*grpcSceneServiceableAreaController).getLaneAreaServiceableByRule
     0.18s  0.15% 73.27%      5.51s  4.56%  google.golang.org/protobuf/proto.UnmarshalOptions.unmarshal
     0.17s  0.14% 73.41%      6.70s  5.55%  runtime.schedule
     0.16s  0.13% 73.54%      0.97s   0.8%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area.(*grpcSceneServiceableAreaController).getLinesAreaServiceableByRule.func1
     0.15s  0.12% 73.66%     37.78s 31.28%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area.(*grpcSceneServiceableAreaController).getLinesAreaServiceableByRule
     0.15s  0.12% 73.79%      1.66s  1.37%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area_location_ref.(*logisticLineServiceableAreaLocationRefTabDAO).GetLogisticLineServiceableAreaLocationRefTabByLocationIdListUseCache
     0.15s  0.12% 73.91%      7.17s  5.94%  google.golang.org/protobuf/internal/impl.appendMessageSlice
     0.15s  0.12% 74.04%      0.92s  0.76%  runtime.(*mspan).heapBits
     0.14s  0.12% 74.15%      7.37s  6.10%  google.golang.org/protobuf/internal/impl.(*MessageInfo).size
     0.13s  0.11% 74.26%      1.04s  0.86%  google.golang.org/protobuf/internal/impl.(*messageReflectWrapper).protoUnwrap
     0.13s  0.11% 74.37%     13.78s 11.41%  runtime.newstack
     0.13s  0.11% 74.47%      1.09s   0.9%  runtime.slicebytetostring
     0.13s  0.11% 74.58%      0.79s  0.65%  runtime.wbBufFlush1
     0.12s 0.099% 74.68%     14.44s 11.96%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).getOriginServiceableDetail
     0.12s 0.099% 74.78%      2.25s  1.86%  google.golang.org/protobuf/internal/impl.(*MessageInfo).checkInitializedPointer
     0.12s 0.099% 74.88%      1.78s  1.47%  reflect.NewAt
     0.11s 0.091% 74.97%      1.33s  1.10%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area.serviceableBasis2LcosRuleBaseInfo
     0.11s 0.091% 75.06%      4.35s  3.60%  google.golang.org/protobuf/internal/impl.makeMessageFieldCoder.func1
     0.10s 0.083% 75.14%      1.94s  1.61%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).checkOperationServiceableRouteV2
     0.10s 0.083% 75.23%      5.14s  4.26%  google.golang.org/protobuf/internal/impl.consumeMessageSlice
     0.10s 0.083% 75.31%      5.41s  4.48%  google.golang.org/protobuf/internal/impl.legacyWrapMessage
     0.10s 0.083% 75.39%      0.83s  0.69%  runtime.(*unwinder).initAt
     0.10s 0.083% 75.48%      1.24s  1.03%  runtime.convTstring
     0.09s 0.075% 75.55%     31.62s 26.18%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).BatchGetServiceable
     0.09s 0.075% 75.63%      3.96s  3.28%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).getDestServiceableByRequest
     0.09s 0.075% 75.70%     15.13s 12.53%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).getOriginServiceableByRequest
     0.09s 0.075% 75.77%      7.27s  6.02%  google.golang.org/protobuf/internal/impl.(*MessageInfo).sizePointer
     0.09s 0.075% 75.85%      0.97s   0.8%  google.golang.org/protobuf/internal/impl.consumeStringPtr
     0.09s 0.075% 75.92%      5.17s  4.28%  google.golang.org/protobuf/internal/impl.makeMessageSliceFieldCoder.func3
     0.09s 0.075% 76.00%      0.77s  0.64%  runtime.chanrecv
     0.09s 0.075% 76.07%     12.71s 10.52%  runtime.copystack
     0.09s 0.075% 76.15%      1.19s  0.99%  runtime.makeslice
     0.09s 0.075% 76.22%      0.93s  0.77%  runtime.netpoll
     0.08s 0.066% 76.29%      0.74s  0.61%  gcWriteBarrier
     0.08s 0.066% 76.35%      6.71s  5.56%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache.(*queryExecutor).Find
     0.08s 0.066% 76.42%      6.15s  5.09%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location.(*lineBasicServiceableLocationDAO).SearchBasicServiceableLocationDetailUseCache
     0.08s 0.066% 76.49%      2.22s  1.84%  google.golang.org/protobuf/internal/impl.isInitMessageSlice
     0.08s 0.066% 76.55%      1.86s  1.54%  google.golang.org/protobuf/internal/impl.legacyLoadMessageInfo
     0.08s 0.066% 76.62%      0.75s  0.62%  google.golang.org/protobuf/internal/impl.pointer.AsIfaceOf
     0.08s 0.066% 76.68%     10.42s  8.63%  google.golang.org/protobuf/proto.MarshalOptions.MarshalAppend
     0.08s 0.066% 76.75%      1.77s  1.47%  runtime.(*mcache).refill
     0.07s 0.058% 76.81%     11.17s  9.25%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location.(*logisticLineOperationServiceableLocationTabDAO).SearchLineOperationServiceableLocationDetailUseCacheV2
     0.07s 0.058% 76.87%      4.78s  3.96%  google.golang.org/protobuf/internal/impl.sizeMessageSlice
     0.07s 0.058% 76.92%      2.27s  1.88%  google.golang.org/protobuf/proto.checkInitialized
     0.07s 0.058% 76.98%      1.20s  0.99%  runtime.(*mcentral).cacheSpan
     0.07s 0.058% 77.04%      2.80s  2.32%  runtime.(*mspan).typePointersOfUnchecked
     0.06s  0.05% 77.09%      4.10s  3.39%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).GetLaneServiceableRule
     0.06s  0.05% 77.14%      3.63s  3.01%  runtime.(*mspan).markBitsForIndex (inline)
     0.06s  0.05% 77.19%      0.68s  0.56%  runtime.makemap_small
     0.06s  0.05% 77.24%      0.88s  0.73%  runtime.spanOfUnchecked (inline)
     0.06s  0.05% 77.29%     33.68s 27.89%  runtime.systemstack
     0.05s 0.041% 77.33%      1.04s  0.86%  bufio.(*Reader).Read
     0.05s 0.041% 77.37%      1.43s  1.18%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route.GenerateRouteCacheKey
     0.05s 0.041% 77.41%     10.10s  8.36%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location.GenCacheKey
     0.05s 0.041% 77.45%      1.21s  1.00%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area_rule/effective_rule.(*effectiveRuleDAO).GetEffectiveRuleMapByLaneCodesUsingCache
     0.05s 0.041% 77.50%      4.64s  3.84%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).checkLocationServiceableRoute
     0.05s 0.041% 77.54%      3.10s  2.57%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).getDestServiceableDetail
     0.05s 0.041% 77.58%      7.20s  5.96%  google.golang.org/protobuf/internal/impl.(*MessageInfo).marshal
     0.05s 0.041% 77.62%      2.26s  1.87%  google.golang.org/protobuf/internal/impl.consumeMessage
     0.05s 0.041% 77.66%      0.77s  0.64%  runtime.gcmarknewobject
     0.05s 0.041% 77.70%      0.73s   0.6%  runtime.rawbyteslice
     0.05s 0.041% 77.74%      0.80s  0.66%  runtime.stringtoslicebyte
     0.04s 0.033% 77.78%      3.75s  3.10%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache.(*Manager).getFromLocal (inline)
     0.04s 0.033% 77.81%      1.87s  1.55%  golang.org/x/net/http2.(*Framer).ReadFrame
     0.04s 0.033% 77.84%      2.10s  1.74%  google.golang.org/grpc/internal/transport.(*loopyWriter).run
     0.04s 0.033% 77.88%      2.22s  1.84%  google.golang.org/protobuf/internal/impl.makeMessageSliceFieldCoder.func4
     0.04s 0.033% 77.91%      2.10s  1.74%  runtime.(*mcache).nextFree
     0.04s 0.033% 77.94%      1.90s  1.57%  runtime.notesleep
     0.04s 0.033% 77.98%      3.77s  3.12%  runtime.pcdatavalue
     0.03s 0.025% 78.00%      0.75s  0.62%  context.(*valueCtx).Value
     0.03s 0.025% 78.03%      1.69s  1.40%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/grpc_controller/scene_serviceable_area.successAreaServiceable (inline)
     0.03s 0.025% 78.05%     11.22s  9.29%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location.(*logisticLineOperationServiceableLocationTabDAO).SearchLineOperationServiceableLocationDetailUseCache
     0.03s 0.025% 78.08%      1.97s  1.63%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).checkOperationServiceableRoute
     0.03s 0.025% 78.10%      2.88s  2.38%  google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams
     0.03s 0.025% 78.13%      2.26s  1.87%  google.golang.org/protobuf/internal/impl.(*MessageInfo).checkInitialized
     0.03s 0.025% 78.15%      3.59s  2.97%  google.golang.org/protobuf/internal/impl.makeMessageFieldCoder.func3
     0.03s 0.025% 78.18%      2.39s  1.98%  google.golang.org/protobuf/internal/impl.sizeMessage
     0.03s 0.025% 78.20%      0.89s  0.74%  internal/poll.(*FD).Read
     0.03s 0.025% 78.22%      1.11s  0.92%  io.ReadAtLeast
     0.03s 0.025% 78.25%      0.79s  0.65%  reflect.Value.Interface (inline)
     0.03s 0.025% 78.27%      1.18s  0.98%  runtime.bgsweep
     0.03s 0.025% 78.30%      7.26s  6.01%  runtime.mcall
     0.03s 0.025% 78.32%      1.88s  1.56%  runtime.wakep
     0.02s 0.017% 78.34%      5.78s  4.79%  git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/grpc.(*ProtocolServerFactory).New.ChainUnaryServer.func2.1.1
     0.02s 0.017% 78.36%      0.69s  0.57%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route.(*lineOperationRouteTabDAO).CheckLineOperationRouteExistUseCache
     0.02s 0.017% 78.37%      0.79s  0.65%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location.GenLineBasicLocationCacheKey
     0.02s 0.017% 78.39%      2.03s  1.68%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/serviceable_core_logic.(*ServiceableCheckerService).getAreaByLocationId
     0.02s 0.017% 78.41%      0.63s  0.52%  github.com/go-playground/validator/v10.(*structCache).Get (inline)
     0.02s 0.017% 78.42%      0.70s  0.58%  github.com/go-playground/validator/v10.(*validate).traverseField
     0.02s 0.017% 78.44%      0.91s  0.75%  github.com/soheilhy/cmux.(*MuxConn).Read
     0.02s 0.017% 78.46%      0.67s  0.55%  golang.org/x/net/http2/hpack.(*Decoder).Write
     0.02s 0.017% 78.47%      1.43s  1.18%  google.golang.org/grpc/internal/transport.(*bufWriter).Flush
     0.02s 0.017% 78.49%      2.23s  1.85%  google.golang.org/protobuf/internal/impl.makeMessageFieldCoder.func2
     0.02s 0.017% 78.51%      4.80s  3.97%  google.golang.org/protobuf/internal/impl.makeMessageSliceFieldCoder.func1
     0.02s 0.017% 78.52%      1.80s  1.49%  google.golang.org/protobuf/internal/impl.pointer.AsValueOf (inline)
     0.02s 0.017% 78.54%      2.27s  1.88%  google.golang.org/protobuf/proto.CheckInitialized
     0.02s 0.017% 78.56%      7.12s  5.90%  google.golang.org/protobuf/proto.Size (inline)
     0.02s 0.017% 78.57%      1.41s  1.17%  net.(*netFD).Write
     0.02s 0.017% 78.59%      1.90s  1.57%  runtime.futexsleep
     0.02s 0.017% 78.61%      0.66s  0.55%  runtime.newproc.func1
     0.02s 0.017% 78.62%      1.62s  1.34%  runtime.notewakeup
     0.01s 0.0083% 78.63%      1.41s  1.17%  fmt.Sprintf
     0.01s 0.0083% 78.64%      4.53s  3.75%  git.garena.com/shopee/bg-logistics/go/chassis/handler.(*PrometheusMetricHandler).Handle
     0.01s 0.0083% 78.65%      4.72s  3.91%  git.garena.com/shopee/bg-logistics/go/chassis/handler.(*ReplayerProvider).Handle
     0.01s 0.0083% 78.66%      4.84s  4.01%  git.garena.com/shopee/bg-logistics/go/chassis/handler.(*ViewercontextProviderHandler).Handle
     0.01s 0.0083% 78.66%      4.18s  3.46%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/grpc_api.LogRequest
     0.01s 0.0083% 78.67%      4.15s  3.44%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/grpc_api.MonitorReport
     0.01s 0.0083% 78.68%      3.19s  2.64%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/traffic.Compare
     0.01s 0.0083% 78.69%      0.99s  0.82%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location.GetPartitionNum (inline)
     0.01s 0.0083% 78.70%     11.44s  9.47%  git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go._LcosSceneServiceable_BatchCheckProductServiceableForItemScene_Handler
     0.01s 0.0083% 78.71%      5.59s  4.63%  github.com/golang/protobuf/proto.UnmarshalMerge
     0.01s 0.0083% 78.71%     12.77s 10.57%  github.com/golang/protobuf/proto.marshalAppend
     0.01s 0.0083% 78.72%      0.89s  0.74%  github.com/soheilhy/cmux.(*bufferedReader).Read
     0.01s 0.0083% 78.73%     12.79s 10.59%  google.golang.org/grpc.encode
     0.01s 0.0083% 78.74%     12.78s 10.58%  google.golang.org/grpc/encoding/proto.codec.Marshal
     0.01s 0.0083% 78.75%      5.51s  4.56%  google.golang.org/protobuf/internal/impl.(*MessageInfo).unmarshal
     0.01s 0.0083% 78.75%      0.88s  0.73%  net.(*conn).Read
     0.01s 0.0083% 78.76%      0.83s  0.69%  runtime.(*mheap).alloc.func1
     0.01s 0.0083% 78.77%      0.81s  0.67%  runtime.(*mheap).allocSpan
     0.01s 0.0083% 78.78%      2.91s  2.41%  runtime.funcspdelta (inline)
     0.01s 0.0083% 78.79%     30.58s 25.32%  runtime.gcBgMarkWorker
     0.01s 0.0083% 78.80%      1.91s  1.58%  runtime.mPark (inline)
     0.01s 0.0083% 78.80%      0.70s  0.58%  runtime.newproc
     0.01s 0.0083% 78.81%      1.33s  1.10%  runtime.resetspinning
     0.01s 0.0083% 78.82%      1.63s  1.35%  runtime.startm
     0.01s 0.0083% 78.83%      1.98s  1.64%  runtime.stopm
     0.01s 0.0083% 78.84%      0.80s  0.66%  runtime/internal/syscall.EpollWait
         0     0% 78.84%      5.05s  4.18%  git.garena.com/shopee/bg-logistics/go/chassis/core/handler.(*Chain).Next
         0     0% 78.84%      5.05s  4.18%  git.garena.com/shopee/bg-logistics/go/chassis/core/invocation.(*Invocation).Next (inline)
         0     0% 78.84%      5.05s  4.18%  git.garena.com/shopee/bg-logistics/go/chassis/handler.(*CatProviderHandler).Handle
         0     0% 78.84%      4.60s  3.81%  git.garena.com/shopee/bg-logistics/go/chassis/handler.(*LogProviderHandler).Handle
         0     0% 78.84%      4.52s  3.74%  git.garena.com/shopee/bg-logistics/go/chassis/handler.(*PrometheusMetricHandler).handleProviderInvocation
         0     0% 78.84%      4.60s  3.81%  git.garena.com/shopee/bg-logistics/go/chassis/handler.(*RateLimitHandler).Handle
         0     0% 78.84%      4.35s  3.60%  git.garena.com/shopee/bg-logistics/go/chassis/handler.(*RecorderProviderHandler).Handle
         0     0% 78.84%      4.32s  3.58%  git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/grpc.(*Handler).Handle
         0     0% 78.84%      5.81s  4.81%  git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/grpc.(*ProtocolServerFactory).New.ChainUnaryServer.func2
         0     0% 78.84%      5.78s  4.79%  git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/grpc.(*ProtocolServerFactory).New.func1
         0     0% 78.84%      3.19s  2.64%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/grpc_api.BranchSwitch
         0     0% 78.84%      4.18s  3.46%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/grpc_api.Recovery
         0     0% 78.84%      3.94s  3.26%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/grpc_api.SecretValidate
         0     0% 78.84%      3.17s  2.62%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/grpc_api.SetContextKey
         0     0% 78.84%      3.94s  3.26%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/apps/grpc_api.Validate
         0     0% 78.84%     10.82s  8.96%  git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common.GenNormalKey (inline)
         0     0% 78.84%      3.15s  2.61%  git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go._LcosSceneServiceable_BatchCheckProductServiceableForItemScene_Handler.func1
         0     0% 78.84%      0.73s   0.6%  github.com/go-playground/validator/v10.(*Validate).Struct (inline)
         0     0% 78.84%      0.73s   0.6%  github.com/go-playground/validator/v10.(*Validate).StructCtx
         0     0% 78.84%      0.72s   0.6%  github.com/go-playground/validator/v10.(*validate).validateStruct
         0     0% 78.84%     12.77s 10.57%  github.com/golang/protobuf/proto.Marshal (inline)
         0     0% 78.84%      5.59s  4.63%  github.com/golang/protobuf/proto.Unmarshal
         0     0% 78.84%      2.27s  1.88%  github.com/golang/protobuf/proto.checkRequiredNotSet (inline)
         0     0% 78.84%      0.69s  0.57%  golang.org/x/net/http2.(*Framer).readMetaFrame
         0     0% 78.84%      0.87s  0.72%  golang.org/x/net/http2.readFrameHeader
         0     0% 78.84%      0.65s  0.54%  golang.org/x/net/http2/hpack.(*Decoder).parseHeaderFieldRepr
         0     0% 78.84%      2.88s  2.38%  google.golang.org/grpc.(*Server).handleRawConn.func1
         0     0% 78.84%     24.69s 20.44%  google.golang.org/grpc.(*Server).handleStream
         0     0% 78.84%     24.70s 20.45%  google.golang.org/grpc.(*Server).processUnaryRPC
         0     0% 78.84%      5.61s  4.64%  google.golang.org/grpc.(*Server).processUnaryRPC.func2
         0     0% 78.84%     12.93s 10.71%  google.golang.org/grpc.(*Server).sendResponse
         0     0% 78.84%      2.88s  2.38%  google.golang.org/grpc.(*Server).serveStreams
         0     0% 78.84%     24.70s 20.45%  google.golang.org/grpc.(*Server).serveStreams.func1.2
         0     0% 78.84%      5.59s  4.63%  google.golang.org/grpc/encoding/proto.codec.Unmarshal
         0     0% 78.84%      2.10s  1.74%  google.golang.org/grpc/internal/transport.NewServerTransport.func2
         0     0% 78.84%      1.78s  1.47%  google.golang.org/protobuf/internal/impl.appendMessage
         0     0% 78.84%      1.04s  0.86%  google.golang.org/protobuf/internal/impl.makeMessageFieldCoder.func4
         0     0% 78.84%      7.17s  5.94%  google.golang.org/protobuf/internal/impl.makeMessageSliceFieldCoder.func2
         0     0% 78.84%      5.51s  4.56%  google.golang.org/protobuf/proto.UnmarshalOptions.UnmarshalState
         0     0% 78.84%      1.39s  1.15%  internal/poll.(*FD).Write
         0     0% 78.84%      2.14s  1.77%  internal/poll.ignoringEINTRIO (inline)
         0     0% 78.84%      1.11s  0.92%  io.ReadFull (inline)
         0     0% 78.84%      1.41s  1.17%  net.(*conn).Write
         0     0% 78.84%      0.87s  0.72%  net.(*netFD).Read
         0     0% 78.84%      0.87s  0.72%  runtime.(*mcentral).grow
         0     0% 78.84%      0.83s  0.69%  runtime.(*mheap).alloc
         0     0% 78.84%      0.83s  0.69%  runtime.(*unwinder).init (inline)
         0     0% 78.84%      1.61s  1.33%  runtime.futexwakeup
         0     0% 78.84%     30.57s 25.31%  runtime.gcBgMarkWorker.func2
         0     0% 78.84%      9.70s  8.03%  runtime.gcDrainMarkWorkerDedicated (inline)
         0     0% 78.84%     20.87s 17.28%  runtime.gcDrainMarkWorkerIdle (inline)
         0     0% 78.84%      4.96s  4.11%  runtime.goexit0
         0     0% 78.84%      2.22s  1.84%  runtime.park_m
         0     0% 78.84%      0.79s  0.65%  runtime.wbBufFlush
         0     0% 78.84%      0.79s  0.65%  runtime.wbBufFlush.func1
         0     0% 78.84%      2.03s  1.68%  syscall.RawSyscall6
         0     0% 78.84%      0.72s   0.6%  syscall.Read (inline)
         0     0% 78.84%      2.11s  1.75%  syscall.Syscall
         0     0% 78.84%      1.37s  1.13%  syscall.Write (inline)
         0     0% 78.84%      0.72s   0.6%  syscall.read
         0     0% 78.84%      1.37s  1.13%  syscall.write
